"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { PlusOutlined } from "@ant-design/icons";
import { ProTable } from "@ant-design/pro-components";
import { Button, message, Modal, Space, Form, Input, InputNumber, Switch, Tree } from "antd";
import { useState, useEffect } from "react";
import {
  getCategories,
  getCategoriesRoot,
  postCategories,
  putCategoriesId,
  deleteCategoriesId
} from "@/services/ant-design-pro/fenleiguanli";
const CategoryManagement = () => {
  const [categories, setCategories] = useState([]);
  const [rootCategories, setRootCategories] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [categoryForm] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [currentCategory, setCurrentCategory] = useState(null);
  const [treeModalVisible, setTreeModalVisible] = useState(false);
  const [selectedParentId, setSelectedParentId] = useState(null);
  const fetchCategories = async () => {
    try {
      const response = await getCategories({});
      const safeData = (response || []).map((item) => ({
        id: item.id || 0,
        name: item.name || "",
        description: item.description,
        icon: item.icon,
        parent_id: item.parent_id,
        sort_order: item.sort_order,
        is_active: item.is_active || false,
        created_at: item.created_at || "",
        updated_at: item.updated_at || ""
      }));
      setCategories(safeData);
    } catch (error) {
      message.error("\u83B7\u53D6\u5206\u7C7B\u5217\u8868\u5931\u8D25");
    }
  };
  const fetchRootCategories = async () => {
    try {
      const response = await getCategoriesRoot({});
      const safeData = (response || []).map((item) => ({
        id: item.id || 0,
        name: item.name || "",
        description: item.description,
        icon: item.icon,
        parent_id: item.parent_id,
        sort_order: item.sort_order,
        is_active: item.is_active || false,
        created_at: item.created_at || "",
        updated_at: item.updated_at || ""
      }));
      setRootCategories(safeData);
    } catch (error) {
      message.error("\u83B7\u53D6\u6839\u5206\u7C7B\u5931\u8D25");
    }
  };
  useEffect(() => {
    fetchCategories();
    fetchRootCategories();
  }, []);
  const handleSaveCategory = async () => {
    try {
      const values = await categoryForm.validateFields();
      setConfirmLoading(true);
      if (currentCategory) {
        await putCategoriesId(
          { id: currentCategory.id },
          {
            ...values,
            parent_id: selectedParentId
          }
        );
        message.success("\u66F4\u65B0\u5206\u7C7B\u6210\u529F");
      } else {
        await postCategories({
          ...values,
          parent_id: selectedParentId
        });
        message.success("\u521B\u5EFA\u5206\u7C7B\u6210\u529F");
      }
      setModalVisible(false);
      fetchCategories();
      fetchRootCategories();
    } catch (error) {
      message.error("\u64CD\u4F5C\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u8F93\u5165");
    } finally {
      setConfirmLoading(false);
    }
  };
  const handleDeleteCategory = async (id) => {
    Modal.confirm({
      title: "\u786E\u8BA4\u5220\u9664",
      content: "\u5220\u9664\u5206\u7C7B\u5C06\u4F1A\u5F71\u54CD\u5173\u8054\u7684\u5E94\u7528\uFF0C\u786E\u5B9A\u8981\u5220\u9664\u5417\uFF1F",
      onOk: async () => {
        try {
          await deleteCategoriesId({ id });
          message.success("\u5220\u9664\u6210\u529F");
          fetchCategories();
          fetchRootCategories();
        } catch (error) {
          message.error("\u5220\u9664\u5931\u8D25");
        }
      }
    });
  };
  const openCategoryModal = (category) => {
    categoryForm.resetFields();
    if (category) {
      setCurrentCategory(category);
      setSelectedParentId(category.parent_id || null);
      categoryForm.setFieldsValue({
        name: category.name,
        description: category.description,
        icon: category.icon,
        sort_order: category.sort_order,
        is_active: category.is_active
      });
    } else {
      setCurrentCategory(null);
      setSelectedParentId(null);
    }
    setModalVisible(true);
  };
  const buildTreeData = (data) => {
    return data.map((item) => ({
      title: item.name,
      key: item.id,
      children: item.children ? buildTreeData(item.children) : void 0
    }));
  };
  const showParentSelectModal = () => {
    setTreeModalVisible(true);
  };
  const handleSelectParent = (selectedKeys) => {
    if (selectedKeys.length > 0) {
      setSelectedParentId(selectedKeys[0]);
    } else {
      setSelectedParentId(null);
    }
    setTreeModalVisible(false);
  };
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80
    },
    {
      title: "\u540D\u79F0",
      dataIndex: "name",
      key: "name"
    },
    {
      title: "\u63CF\u8FF0",
      dataIndex: "description",
      key: "description",
      ellipsis: true
    },
    {
      title: "\u6392\u5E8F",
      dataIndex: "sort_order",
      key: "sort_order",
      width: 100
    },
    {
      title: "\u72B6\u6001",
      dataIndex: "is_active",
      key: "is_active",
      width: 100,
      render: (_, record) => record.is_active ? "\u542F\u7528" : "\u7981\u7528"
    },
    {
      title: "\u521B\u5EFA\u65F6\u95F4",
      dataIndex: "created_at",
      key: "created_at",
      width: 180
    },
    {
      title: "\u64CD\u4F5C",
      key: "action",
      width: 180,
      render: (_, record) => /* @__PURE__ */ jsxs(Space, { size: "middle", children: [
        /* @__PURE__ */ jsx("a", { onClick: () => openCategoryModal(record), children: "\u7F16\u8F91" }),
        /* @__PURE__ */ jsx("a", { onClick: () => handleDeleteCategory(record.id), children: "\u5220\u9664" })
      ] })
    }
  ];
  const rootNode = {
    title: "\u6839\u5206\u7C7B",
    key: "root"
  };
  return /* @__PURE__ */ jsxs("div", { children: [
    /* @__PURE__ */ jsx(
      ProTable,
      {
        headerTitle: "\u5206\u7C7B\u7BA1\u7406",
        rowKey: "id",
        search: false,
        dataSource: categories,
        columns,
        pagination: {
          showQuickJumper: true
        },
        toolBarRender: () => [
          /* @__PURE__ */ jsx(
            Button,
            {
              icon: /* @__PURE__ */ jsx(PlusOutlined, {}),
              type: "primary",
              onClick: () => openCategoryModal(),
              children: "\u65B0\u5EFA\u5206\u7C7B"
            },
            "button"
          )
        ]
      }
    ),
    /* @__PURE__ */ jsx(
      Modal,
      {
        title: currentCategory ? "\u7F16\u8F91\u5206\u7C7B" : "\u65B0\u5EFA\u5206\u7C7B",
        open: modalVisible,
        onOk: handleSaveCategory,
        confirmLoading,
        onCancel: () => setModalVisible(false),
        children: /* @__PURE__ */ jsxs(
          Form,
          {
            form: categoryForm,
            layout: "vertical",
            children: [
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "name",
                  label: "\u5206\u7C7B\u540D\u79F0",
                  rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0" }],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "description",
                  label: "\u5206\u7C7B\u63CF\u8FF0",
                  children: /* @__PURE__ */ jsx(Input.TextArea, { rows: 4, placeholder: "\u8BF7\u8F93\u5165\u5206\u7C7B\u63CF\u8FF0" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "icon",
                  label: "\u56FE\u6807URL",
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u56FE\u6807URL" })
                }
              ),
              /* @__PURE__ */ jsx(Form.Item, { label: "\u7236\u5206\u7C7B", children: /* @__PURE__ */ jsx(Button, { onClick: showParentSelectModal, children: selectedParentId ? `\u5DF2\u9009\u62E9ID: ${selectedParentId}` : "\u9009\u62E9\u7236\u5206\u7C7B" }) }),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "sort_order",
                  label: "\u6392\u5E8F\u6743\u91CD",
                  initialValue: 0,
                  children: /* @__PURE__ */ jsx(InputNumber, { min: 0, style: { width: "100%" } })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "is_active",
                  label: "\u72B6\u6001",
                  valuePropName: "checked",
                  initialValue: true,
                  children: /* @__PURE__ */ jsx(Switch, {})
                }
              )
            ]
          }
        )
      }
    ),
    /* @__PURE__ */ jsx(
      Modal,
      {
        title: "\u9009\u62E9\u7236\u5206\u7C7B",
        open: treeModalVisible,
        onOk: () => setTreeModalVisible(false),
        onCancel: () => setTreeModalVisible(false),
        footer: [
          /* @__PURE__ */ jsx(Button, { onClick: () => setSelectedParentId(null), children: "\u6E05\u9664\u9009\u62E9" }, "clear"),
          /* @__PURE__ */ jsx(Button, { onClick: () => setTreeModalVisible(false), children: "\u53D6\u6D88" }, "cancel"),
          /* @__PURE__ */ jsx(Button, { type: "primary", onClick: () => setTreeModalVisible(false), children: "\u786E\u5B9A" }, "submit")
        ],
        children: /* @__PURE__ */ jsx(
          Tree,
          {
            treeData: [rootNode, ...buildTreeData(rootCategories)],
            defaultExpandAll: true,
            onSelect: handleSelectParent,
            selectedKeys: selectedParentId ? [selectedParentId] : []
          }
        )
      }
    )
  ] });
};
export default CategoryManagement;
