{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"CreateModuleInfo": 3630800, "ConfigureCmake": 653300, "PreCheckSyscap": 808600, "ProcessIntegratedHsp": 3607900, "BuildNativeWithCmake": 543500, "ProcessStartupConfig": 9295100, "BuildNativeWithNinja": 2492900, "CompileResource": 278390000, "BuildJS": 8382000, "CompileArkTS": 15242877700, "GeneratePkgModuleJson": 3311000, "PackageHap": 323288000, "SignHap": 381180300, "CollectDebugSymbol": 8518400, "assembleHap": 629200}}, "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true}, "BUILD_ID": "202506172340465400", "TOTAL_TIME": 19307365500}}