{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"CreateModuleInfo": 4243900, "ConfigureCmake": 507200, "PreCheckSyscap": 1227800, "ProcessIntegratedHsp": 5020000, "BuildNativeWithCmake": 587600, "ProcessStartupConfig": 11361000, "BuildNativeWithNinja": 4383900, "BuildJS": 16993400}}, "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true}, "TOTAL_TIME": 14847131900, "BUILD_ID": "202506180007298250", "ERROR_MESSAGE": [{"CODE": "10505001", "TIMESTAMP": "1750176464663"}, {"CODE": "10505001", "TIMESTAMP": "1750176464663"}, {"CODE": "10505001", "TIMESTAMP": "1750176464663"}, {"CODE": "10905204", "TIMESTAMP": "1750176464663"}]}}