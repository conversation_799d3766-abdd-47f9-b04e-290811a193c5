{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"CreateModuleInfo": 4494400, "ConfigureCmake": 1214900, "PreCheckSyscap": 1005000, "ProcessIntegratedHsp": 4846600, "BuildNativeWithCmake": 527600, "ProcessStartupConfig": 9388000, "BuildNativeWithNinja": 2278000, "BuildJS": 7345000, "CollectDebugSymbol": 10013700, "assembleHap": 390900}}, "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true}, "TOTAL_TIME": 830631400, "BUILD_ID": "202506180034227150"}}