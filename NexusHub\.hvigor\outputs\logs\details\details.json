{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"CreateModuleInfo": 7714100, "ConfigureCmake": 945300, "PreCheckSyscap": 1137100, "ProcessIntegratedHsp": 5951900, "BuildNativeWithCmake": 786900, "ProcessStartupConfig": 14152900, "BuildNativeWithNinja": 3967000, "BuildJS": 15701500, "CollectDebugSymbol": 13414600, "assembleHap": 882600}}, "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true}, "TOTAL_TIME": 1089585900, "BUILD_ID": "202506180103303570"}}