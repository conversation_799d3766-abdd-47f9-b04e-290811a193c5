{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"CreateModuleInfo": 1835000, "ConfigureCmake": 252700, "PreCheckSyscap": 442700, "ProcessIntegratedHsp": 2508700, "BuildNativeWithCmake": 470000, "ProcessStartupConfig": 5907100, "BuildNativeWithNinja": 2458300, "BuildJS": 8380500}}, "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true}, "TOTAL_TIME": 18138467300, "BUILD_ID": "202506180136055080", "ERROR_MESSAGE": [{"CODE": "10605066", "TIMESTAMP": "1750181783630"}, {"CODE": "10605029", "TIMESTAMP": "1750181783630"}, {"CODE": "10903329", "TIMESTAMP": "1750181783630"}]}}