{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"CreateModuleInfo": 3805600, "ConfigureCmake": 667400, "PreCheckSyscap": 758300, "ProcessIntegratedHsp": 4669500, "BuildNativeWithCmake": 695400, "ProcessStartupConfig": 13300400, "BuildNativeWithNinja": 3585200, "BuildJS": 12503400, "CollectDebugSymbol": 16416700, "assembleHap": 347800}}, "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true}, "TOTAL_TIME": 969072900, "BUILD_ID": "202506180119479450"}}