{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"CreateModuleInfo": 3516900, "ConfigureCmake": 321100, "PreCheckSyscap": 517400, "ProcessIntegratedHsp": 2890000, "BuildNativeWithCmake": 522300, "ProcessStartupConfig": 5828400, "BuildNativeWithNinja": 2437000, "BuildJS": 8834200, "CompileArkTS": 11252251900, "GeneratePkgModuleJson": 3752200, "PackageHap": 178051700, "SignHap": 319131900, "CollectDebugSymbol": 5391000, "assembleHap": 422000}}, "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true}, "TOTAL_TIME": 12223060700, "BUILD_ID": "202506180022087410"}}