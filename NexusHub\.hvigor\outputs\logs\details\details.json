{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreBuild": 226108100, "CreateModuleInfo": 2496600, "ConfigureCmake": 377600, "PreCheckSyscap": 708500, "ProcessIntegratedHsp": 2856800, "BuildNativeWithCmake": 393100, "ProcessStartupConfig": 7010300, "BuildNativeWithNinja": 2811200, "CompileResource": 239277800, "BuildJS": 8343800}}, "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true}, "TOTAL_TIME": 12139099700, "BUILD_ID": "202506172357380280", "ERROR_MESSAGE": [{"CODE": "10605029", "TIMESTAMP": "1750175870154"}, {"CODE": "10605029", "TIMESTAMP": "1750175870154"}, {"CODE": "10505001", "TIMESTAMP": "1750175870154"}, {"CODE": "10505001", "TIMESTAMP": "1750175870154"}, {"CODE": "10505001", "TIMESTAMP": "1750175870154"}, {"CODE": "10505001", "TIMESTAMP": "1750175870154"}, {"CODE": "10505001", "TIMESTAMP": "1750175870154"}, {"CODE": "10505001", "TIMESTAMP": "1750175870154"}, {"CODE": "10505001", "TIMESTAMP": "1750175870154"}]}}