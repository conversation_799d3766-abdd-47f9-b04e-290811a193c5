{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"CreateModuleInfo": 7152200, "ConfigureCmake": 651100, "PreCheckSyscap": 781900, "ProcessIntegratedHsp": 3411200, "BuildNativeWithCmake": 506500, "ProcessStartupConfig": 9767100, "BuildNativeWithNinja": 4001500, "CompileResource": 288180800, "BuildJS": 9599400}}, "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true}, "BUILD_ID": "202506172327335550", "ERROR_MESSAGE": [{"CODE": "10605099", "TIMESTAMP": "1750174072347"}, {"CODE": "10505001", "TIMESTAMP": "1750174072347"}, {"CODE": "10505001", "TIMESTAMP": "1750174072347"}, {"CODE": "10505001", "TIMESTAMP": "1750174072347"}, {"CODE": "10505001", "TIMESTAMP": "1750174072347"}], "TOTAL_TIME": 18802569100}}