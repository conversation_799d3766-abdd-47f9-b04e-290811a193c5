{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug"}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"CreateModuleInfo": 3724500, "ConfigureCmake": 679900, "PreCheckSyscap": 740400, "ProcessIntegratedHsp": 3243500, "BuildNativeWithCmake": 395900, "ProcessStartupConfig": 6910300, "BuildNativeWithNinja": 3128900, "BuildJS": 8090300, "CollectDebugSymbol": 5613800, "assembleHap": 415600}}, "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true}, "BUILD_ID": "202506172245162780", "TOTAL_TIME": 2845645500}}