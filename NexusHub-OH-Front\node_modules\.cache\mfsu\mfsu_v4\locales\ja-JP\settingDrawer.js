"use strict";
export default {
  "app.setting.pagestyle": "\u30DA\u30FC\u30B8\u30B9\u30BF\u30A4\u30EB\u8A2D\u5B9A",
  "app.setting.pagestyle.dark": "\u30C0\u30FC\u30AF\u30B9\u30BF\u30A4\u30EB",
  "app.setting.pagestyle.light": "\u30E9\u30A4\u30C8\u30B9\u30BF\u30A4\u30EB",
  "app.setting.content-width": "\u30B3\u30F3\u30C6\u30F3\u30C4\u306E\u5E45",
  "app.setting.content-width.fixed": "\u56FA\u5B9A",
  "app.setting.content-width.fluid": "\u6D41\u4F53",
  "app.setting.themecolor": "\u30C6\u30FC\u30DE\u30AB\u30E9\u30FC",
  "app.setting.themecolor.dust": "\u30C0\u30B9\u30C8\u30EC\u30C3\u30C9",
  "app.setting.themecolor.volcano": "\u30DC\u30EB\u30B1-\u30CE",
  "app.setting.themecolor.sunset": "\u30B5\u30F3\u30BB\u30C3\u30C8\u30AA\u30EC\u30F3\u30B8",
  "app.setting.themecolor.cyan": "\u30B7\u30A2\u30F3",
  "app.setting.themecolor.green": "\u30DD\u30FC\u30E9\u30FC\u30B0\u30EA\u30FC\u30F3",
  "app.setting.themecolor.daybreak": "\u591C\u660E\u3051\u306E\u9752\uFF08\u30C7\u30D5\u30A9\u30EB\u30C8\uFF09",
  "app.setting.themecolor.geekblue": "\u30AE\u30FC\u30AF \u30D6\u30EB\u30FC",
  "app.setting.themecolor.purple": "\u30B4\u30FC\u30EB\u30C7\u30F3\u30D1\u30FC\u30D7\u30EB",
  "app.setting.navigationmode": "\u30CA\u30D3\u30B2\u30FC\u30B7\u30E7\u30F3\u30E2\u30FC\u30C9",
  "app.setting.sidemenu": "\u30B5\u30A4\u30C9\u30E1\u30CB\u30E5\u30FC\u306E\u30EC\u30A4\u30A2\u30A6\u30C8",
  "app.setting.topmenu": "\u30C8\u30C3\u30D7\u30E1\u30CB\u30E5\u30FC\u306E\u30EC\u30A4\u30A2\u30A6\u30C8",
  "app.setting.fixedheader": "\u56FA\u5B9A\u30D8\u30C3\u30C0\u30FC",
  "app.setting.fixedsidebar": "\u56FA\u5B9A\u30B5\u30A4\u30C9\u30D0\u30FC",
  "app.setting.fixedsidebar.hint": "\u30B5\u30A4\u30C9\u30E1\u30CB\u30E5\u30FC\u306E\u30EC\u30A4\u30A2\u30A6\u30C8\u3067\u52D5\u4F5C\u3057\u307E\u3059",
  "app.setting.hideheader": "\u30B9\u30AF\u30ED\u30FC\u30EB\u6642\u306E\u975E\u8868\u793A\u30D8\u30C3\u30C0\u30FC",
  "app.setting.hideheader.hint": "\u975E\u8868\u793A\u30D8\u30C3\u30C0\u30FC\u304C\u6709\u52B9\u306B\u306A\u3063\u3066\u3044\u308B\u5834\u5408\u306B\u6A5F\u80FD\u3057\u307E\u3059",
  "app.setting.othersettings": "\u305D\u306E\u4ED6\u306E\u8A2D\u5B9A",
  "app.setting.weakmode": "\u30A6\u30A3\u30FC\u30AF\u30E2\u30FC\u30C9",
  "app.setting.copy": "\u30B3\u30D4\u30FC\u8A2D\u5B9A",
  "app.setting.copyinfo": "\u30B3\u30D4\u30FC\u304C\u6210\u529F\u3057\u307E\u3057\u305F\u3002src/models/setting.js\u306EdefaultSettings\u3092\u7F6E\u304D\u63DB\u3048\u3066\u304F\u3060\u3055\u3044",
  "app.setting.production.hint": "\u8A2D\u5B9A\u30D1\u30CD\u30EB\u306F\u958B\u767A\u74B0\u5883\u3067\u306E\u307F\u8868\u793A\u3055\u308C\u307E\u3059\u3002\u624B\u52D5\u3067\u5909\u66F4\u3057\u3066\u304F\u3060\u3055\u3044"
};
