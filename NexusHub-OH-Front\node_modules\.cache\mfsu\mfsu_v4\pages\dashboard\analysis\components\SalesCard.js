"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { Column } from "@ant-design/plots";
import { Card, Col, DatePicker, Row, Tabs } from "antd";
import numeral from "numeral";
import useStyles from "../style.style";
const { RangePicker } = DatePicker;
const topAppData = [
  { title: "NexusTube", category: "\u89C6\u9891", total: 123456 },
  { title: "NexusMusic", category: "\u97F3\u4E50", total: 98765 },
  { title: "NexusChat", category: "\u793E\u4EA4", total: 87654 },
  { title: "NexusReader", category: "\u9605\u8BFB", total: 76543 },
  { title: "NexusPhotos", category: "\u56FE\u7247", total: 65432 },
  { title: "NexusGames", category: "\u6E38\u620F", total: 54321 },
  { title: "NexusFit", category: "\u5065\u5EB7", total: 43210 }
];
const SalesCard = ({
  rangePickerValue,
  salesData,
  userData,
  isActive,
  handleRangePickerChange,
  loading,
  selectDate
}) => {
  const { styles } = useStyles();
  return /* @__PURE__ */ jsx(
    Card,
    {
      loading,
      bordered: false,
      bodyStyle: {
        padding: 0
      },
      children: /* @__PURE__ */ jsx("div", { className: styles.salesCard, children: /* @__PURE__ */ jsx(
        Tabs,
        {
          tabBarExtraContent: /* @__PURE__ */ jsxs("div", { className: styles.salesExtraWrap, children: [
            /* @__PURE__ */ jsxs("div", { className: styles.salesExtra, children: [
              /* @__PURE__ */ jsx("a", { className: isActive("today"), onClick: () => selectDate("today"), children: "\u4ECA\u65E5" }),
              /* @__PURE__ */ jsx("a", { className: isActive("week"), onClick: () => selectDate("week"), children: "\u672C\u5468" }),
              /* @__PURE__ */ jsx("a", { className: isActive("month"), onClick: () => selectDate("month"), children: "\u672C\u6708" }),
              /* @__PURE__ */ jsx("a", { className: isActive("year"), onClick: () => selectDate("year"), children: "\u672C\u5E74" })
            ] }),
            /* @__PURE__ */ jsx(
              RangePicker,
              {
                value: rangePickerValue,
                onChange: handleRangePickerChange,
                style: {
                  width: 256
                }
              }
            )
          ] }),
          size: "large",
          tabBarStyle: {
            marginBottom: 24
          },
          items: [
            {
              key: "downloads",
              label: "\u4E0B\u8F7D\u8D8B\u52BF",
              children: /* @__PURE__ */ jsxs(Row, { children: [
                /* @__PURE__ */ jsx(Col, { xl: 16, lg: 12, md: 12, sm: 24, xs: 24, children: /* @__PURE__ */ jsx("div", { className: styles.salesBar, children: /* @__PURE__ */ jsx(
                  Column,
                  {
                    height: 300,
                    data: salesData,
                    xField: "x",
                    yField: "y",
                    paddingBottom: 12,
                    axis: {
                      x: {
                        title: false
                      },
                      y: {
                        title: false,
                        gridLineDash: null,
                        gridStroke: "#ccc"
                      }
                    },
                    scale: {
                      x: { paddingInner: 0.4 }
                    },
                    tooltip: {
                      name: "\u4E0B\u8F7D\u91CF",
                      channel: "y"
                    }
                  }
                ) }) }),
                /* @__PURE__ */ jsx(Col, { xl: 8, lg: 12, md: 12, sm: 24, xs: 24, children: /* @__PURE__ */ jsxs("div", { className: styles.salesRank, children: [
                  /* @__PURE__ */ jsx("h4", { className: styles.rankingTitle, children: "\u70ED\u95E8\u5E94\u7528\u4E0B\u8F7D\u6392\u540D" }),
                  /* @__PURE__ */ jsx("ul", { className: styles.rankingList, children: topAppData.map((item, i) => /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx(
                      "span",
                      {
                        className: `${styles.rankingItemNumber} ${i < 3 ? styles.rankingItemNumberActive : ""}`,
                        children: i + 1
                      }
                    ),
                    /* @__PURE__ */ jsxs("span", { className: styles.rankingItemTitle, title: item.title, children: [
                      item.title,
                      /* @__PURE__ */ jsx("span", { className: styles.rankingItemCategory, children: item.category })
                    ] }),
                    /* @__PURE__ */ jsx("span", { children: numeral(item.total).format("0,0") })
                  ] }, item.title)) })
                ] }) })
              ] })
            },
            {
              key: "users",
              label: "\u7528\u6237\u589E\u957F",
              children: /* @__PURE__ */ jsxs(Row, { children: [
                /* @__PURE__ */ jsx(Col, { xl: 16, lg: 12, md: 12, sm: 24, xs: 24, children: /* @__PURE__ */ jsx("div", { className: styles.salesBar, children: /* @__PURE__ */ jsx(
                  Column,
                  {
                    height: 300,
                    data: userData || salesData,
                    xField: "x",
                    yField: "y",
                    paddingBottom: 12,
                    axis: {
                      x: {
                        title: false
                      },
                      y: {
                        title: false
                      }
                    },
                    scale: {
                      x: { paddingInner: 0.4 }
                    },
                    tooltip: {
                      name: "\u7528\u6237\u6570",
                      channel: "y"
                    }
                  }
                ) }) }),
                /* @__PURE__ */ jsx(Col, { xl: 8, lg: 12, md: 12, sm: 24, xs: 24, children: /* @__PURE__ */ jsxs("div", { className: styles.salesRank, children: [
                  /* @__PURE__ */ jsx("h4", { className: styles.rankingTitle, children: "\u7528\u6237\u6D3B\u8DC3\u5EA6\u6392\u540D" }),
                  /* @__PURE__ */ jsx("ul", { className: styles.rankingList, children: topAppData.map((item, i) => /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx(
                      "span",
                      {
                        className: `${i < 3 ? styles.rankingItemNumberActive : styles.rankingItemNumber}`,
                        children: i + 1
                      }
                    ),
                    /* @__PURE__ */ jsxs("span", { className: styles.rankingItemTitle, title: item.title, children: [
                      item.title,
                      /* @__PURE__ */ jsx("span", { className: styles.rankingItemCategory, children: item.category })
                    ] }),
                    /* @__PURE__ */ jsx("span", { children: numeral(Math.floor(item.total * 0.7)).format("0,0") })
                  ] }, item.title)) })
                ] }) })
              ] })
            }
          ]
        }
      ) })
    }
  );
};
export default SalesCard;
