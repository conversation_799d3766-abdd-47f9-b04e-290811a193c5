"use strict";
import { request } from "@umijs/max";
export async function getSummaryData() {
  return request("/dashboard/analytics/summary");
}
export async function getTrendData(days = 30) {
  return request("/dashboard/analytics/trend", {
    params: { days }
  });
}
export async function getCategoriesData() {
  return request("/dashboard/analytics/categories");
}
export async function getPopularApps(limit = 10) {
  return request("/dashboard/analytics/popular-apps", {
    params: { limit }
  });
}
