{":NexusHub:entry:default@PreBuild": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apiType\",\"_value\":\"stageMode\",\"_valueType\":\"string\",\"_hash\":\"699c7565645ea3ea8c88551a4926a3d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"codeType\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"d6dcf21a9f078b661283507536e57ce8\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":12,\"_valueType\":\"number\",\"_hash\":\"b8c1ba3ecddcd8b1be17325fb873c8bc\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":18,\"_valueType\":\"number\",\"_hash\":\"634461a1eedd122eef53cd10a734a672\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configuration\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"d12f0038691f8f34d654391bbcee2f8e\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configurationFile<PERSON><PERSON>\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"2b55287f40f7e8896b21bab4028e156b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"970a2695bffac1c5a4fa283dc36050e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceType\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"3f44547af15457ff8e1ec09282ca9922\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integrated_hsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"2538281751f182d9123d2ab28efaf9be\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSupportOhpmProj\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"f3a249d7e3f751316e931b8a08b074b4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"mockConfigSources\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"c489be8273867a50afbc86c53d938c92\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"jsonFilePath\\\":\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\NexusHub-OH\\\\\\\\NexusHub\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\module.json5\\\",\\\"profile\\\":{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"default\\\",\\\"tablet\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":false,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}],\\\"extensionAbilities\\\":[{\\\"name\\\":\\\"EntryBackupAbility\\\",\\\"srcEntry\\\":\\\"./ets/entrybackupability/EntryBackupAbility.ets\\\",\\\"type\\\":\\\"backup\\\",\\\"exported\\\":false,\\\"metadata\\\":[{\\\"name\\\":\\\"ohos.extension.backup\\\",\\\"resource\\\":\\\"$profile:backup_config\\\"}]}],\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\",\\\"reason\\\":\\\"$string:permission_internet_reason\\\",\\\"usedScene\\\":{\\\"abilities\\\":[\\\"EntryAbility\\\"],\\\"when\\\":\\\"inuse\\\"}},{\\\"name\\\":\\\"ohos.permission.GET_NETWORK_INFO\\\"}],\\\"metadata\\\":[{\\\"name\\\":\\\"network_security_config\\\",\\\"resource\\\":\\\"$profile:network_security_config\\\"}]}},\\\"deviceTypes\\\":[\\\"default\\\",\\\"tablet\\\"],\\\"deviceConfig\\\":\\\"deviceTypes\\\",\\\"configurationProfile\\\":\\\"module.json5\\\"}\",\"_valueType\":\"string\",\"_hash\":\"634e168355736e60f371a6704ec36cdb\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"profileModuleName\",\"_value\":\"entry\",\"_valueType\":\"string\",\"_hash\":\"b174ea6ff5824844dde5ad92f6b3ef2b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\",\"_hash\":\"0703cfc523a152c6195a8fd1935503e5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"5.1.0.107\",\"_valueType\":\"string\",\"_hash\":\"489260fcbe3456171ca6b26f38964d7f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sourceRoots\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a369c115d2c4122f3819759804ec9d35\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetStatusCode\",\"_value\":1,\"_valueType\":\"number\",\"_hash\":\"0ec6123a9c2b84b1bb8ff1aa490b637d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7d270d0ce7ae5c6e2e32760cb396ea5a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@PreBuild", "_key": ":NexusHub:entry:default@PreBuild", "_executionId": ":NexusHub:entry:default@PreBuild:1750162232534", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\app.json5", {"isDirectory": false, "fileSnapShotHashValue": "09cf34510243205467fa62132cc07dba"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\module.json5", {"isDirectory": false, "fileSnapShotHashValue": "9f48c5206c0e86fff9d623f27ba950c4"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build-profile.json5", {"fileSnapShotHashValue": "c2931e466be903987f78b705610b7587"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build-profile.json5", {"fileSnapShotHashValue": "95c14d6deea12d2f1db09af5f0b6f5d0"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", {"fileSnapShotHashValue": "657330566fc4b7186b695ded8c492e6b"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigor\\hvigor-config.json5", {"isDirectory": true, "fileSnapShotHashValue": "3af84185e7f9c76b4d195562737d325e"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "24bb98c100376cec4f0ec28cc0352e0d"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\oh-package.json5", {"fileSnapShotHashValue": "5ca88c8f0f2d86337e10135f0ac68a1a"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\oh-package.json5", {"fileSnapShotHashValue": "480d6fdb12effc9f7bf6b153e44bfd59"}]]}, "_outputFiles": {"dataType": "Map", "value": []}}, ":NexusHub:entry:default@CreateModuleInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"ohosUiTransformOptimization\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@CreateModuleInfo", "_key": ":NexusHub:entry:default@CreateModuleInfo", "_executionId": ":NexusHub:entry:default@CreateModuleInfo:1750174056958", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts", {"isDirectory": false, "fileSnapShotHashValue": ""}]]}}, ":NexusHub:entry:default@GenerateMetadata": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"artifactName\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7f42b9cbe0181813f8dc60881bf90988\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSigned\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"64af347b4db9779050781c231d4eec1b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"relatedEntryModules\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"85b4d1fa598cae3838191eb99a09f4dd\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"remoteHspMetaData\",\"_value\":[],\"_valueType\":\"object\",\"_hash\":\"fbb8220e0c8d7b6197c24bdb06eb568c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetDeviceType\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"29b5156d11ab91ded8ed5f27e60762f0\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@GenerateMetadata", "_key": ":NexusHub:entry:default@GenerateMetadata", "_executionId": ":NexusHub:entry:default@GenerateMetadata:1750161373939", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "9f48c5206c0e86fff9d623f27ba950c4"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json", {"fileSnapShotHashValue": "7f06823d1899a6d3473c48de0d47dbce"}]]}}, ":NexusHub:entry:default@MergeProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appJsonOpt\",\"_value\":\"{\\\"app\\\":{\\\"bundleName\\\":\\\"com.example.nexushub\\\",\\\"vendor\\\":\\\"example\\\",\\\"versionCode\\\":1000000,\\\"versionName\\\":\\\"1.0.0\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:app_name\\\"}}\",\"_valueType\":\"string\",\"_hash\":\"6806b4e337ace55b283b2032d72c1583\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"asanEnable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"bc2129ba20a21b7e5234139ede1b4d7b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileAbilities\",\"_valueType\":\"undefined\",\"_hash\":\"40d5093f345351dd6d67ce5d6a209345\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildRoot\",\"_value\":\"build\",\"_valueType\":\"string\",\"_hash\":\"4c2dd7bb7507f74001ec27f2936db7f9\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":12,\"_valueType\":\"number\",\"_hash\":\"3c48b1937556f5c7e1ff0b2e0af0184f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hwasan<PERSON><PERSON><PERSON>\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"d35c8440e915c3a94c482ddd6f7af075\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integratedHsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"e67900c25b1f9fb70cc779de77dc6912\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDebug\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"edbf05a2d2be2c385e75d9565a48d419\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarModule\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a15b7a3ed818faa99a4a10d67f52cb72\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"default\\\",\\\"tablet\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":false,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}],\\\"extensionAbilities\\\":[{\\\"name\\\":\\\"EntryBackupAbility\\\",\\\"srcEntry\\\":\\\"./ets/entrybackupability/EntryBackupAbility.ets\\\",\\\"type\\\":\\\"backup\\\",\\\"exported\\\":false,\\\"metadata\\\":[{\\\"name\\\":\\\"ohos.extension.backup\\\",\\\"resource\\\":\\\"$profile:backup_config\\\"}]}],\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\",\\\"reason\\\":\\\"$string:permission_internet_reason\\\",\\\"usedScene\\\":{\\\"abilities\\\":[\\\"EntryAbility\\\"],\\\"when\\\":\\\"inuse\\\"}},{\\\"name\\\":\\\"ohos.permission.GET_NETWORK_INFO\\\"}],\\\"metadata\\\":[{\\\"name\\\":\\\"network_security_config\\\",\\\"resource\\\":\\\"$profile:network_security_config\\\"}]}}\",\"_valueType\":\"string\",\"_hash\":\"61bbcf70b6667a0f9adab79d3847fb90\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"multiProjects\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"555604752defc243b4e4c55d1549fc06\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"packageName\",\"_value\":\"entry\",\"_valueType\":\"string\",\"_hash\":\"a754c30e6c7696d34a7f9aca5e67d70a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\",\"_hash\":\"b52997704fa206ed96a13a1f2e464a85\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\",\"_hash\":\"bbcabdda034e97584f8c36f85b3ec517\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\",\"_hash\":\"0703cfc523a152c6195a8fd1935503e5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetSdkVersion\",\"_value\":18,\"_valueType\":\"number\",\"_hash\":\"0511b3f223e59ffefcf972bb2a34c0bf\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"tsan<PERSON>nable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"086cac69f102cdd9ee25e54982ad7b76\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ubsanEnable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"3b15da42c5f4b695fbd1d0b43191764a\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@MergeProfile", "_key": ":NexusHub:entry:default@MergeProfile", "_executionId": ":NexusHub:entry:default@MergeProfile:1750162232631", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\app.json5", {"fileSnapShotHashValue": "09cf34510243205467fa62132cc07dba"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build-profile.json5", {"fileSnapShotHashValue": "c2931e466be903987f78b705610b7587"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "9f48c5206c0e86fff9d623f27ba950c4"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "f895f49569b172ccb9b858fdde711184"}]]}}, ":NexusHub:entry:default@CreateBuildProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildMode\",\"_value\":\"Debug\",\"_valueType\":\"string\",\"_hash\":\"8120d22ada0d6de22b101e1f4ea16e81\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildModeName\",\"_value\":\"debug\",\"_valueType\":\"string\",\"_hash\":\"3f0246ea410fd9efa9fc7196cca045e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileFields\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"21090e125326cef17357e44b789a1ab5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectOhosConfigAppOpt\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7613f1b2cb16d78bb723d12882b0d923\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@CreateBuildProfile", "_key": ":NexusHub:entry:default@CreateBuildProfile", "_executionId": ":NexusHub:entry:default@CreateBuildProfile:1750162232659", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\app.json5", {"fileSnapShotHashValue": "09cf34510243205467fa62132cc07dba"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build-profile.json5", {"fileSnapShotHashValue": "c2931e466be903987f78b705610b7587"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "f00c78eeb2f52e2f59c1428397911f3a"}]]}}, ":NexusHub:entry:default@GeneratePkgContextInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap\",\"_value\":\"{\\\"@ohos/hypium\\\":{\\\"packageName\\\":\\\"@ohos/hypium\\\",\\\"bundleName\\\":\\\"\\\",\\\"moduleName\\\":\\\"\\\",\\\"version\\\":\\\"1.0.21\\\",\\\"entryPath\\\":\\\"index.js\\\",\\\"isSO\\\":false,\\\"dependencyAlias\\\":\\\"\\\"},\\\"@ohos/hamock\\\":{\\\"packageName\\\":\\\"@ohos/hamock\\\",\\\"bundleName\\\":\\\"\\\",\\\"moduleName\\\":\\\"\\\",\\\"version\\\":\\\"1.0.0\\\",\\\"entryPath\\\":\\\"index.ets\\\",\\\"isSO\\\":false,\\\"dependencyAlias\\\":\\\"\\\"},\\\"entry\\\":{\\\"packageName\\\":\\\"entry\\\",\\\"bundleName\\\":\\\"\\\",\\\"moduleName\\\":\\\"\\\",\\\"version\\\":\\\"\\\",\\\"entryPath\\\":\\\"src/main/\\\",\\\"isSO\\\":false,\\\"dependencyAlias\\\":\\\"\\\"}}\",\"_valueType\":\"string\",\"_hash\":\"35afdacc65f4c84a98ad0cdbe6729d36\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@GeneratePkgContextInfo", "_key": ":NexusHub:entry:default@GeneratePkgContextInfo", "_executionId": ":NexusHub:entry:default@GeneratePkgContextInfo:1750161374044", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"isDirectory": false, "fileSnapShotHashValue": "5d9ca1a0d29252758a94a78ac5648f3b"}]]}}, ":NexusHub:entry:default@ProcessIntegratedHsp": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"bundleName\",\"_value\":\"com.example.nexushub\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"versionCode\",\"_value\":1000000,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@ProcessIntegratedHsp", "_key": ":NexusHub:entry:default@ProcessIntegratedHsp", "_executionId": ":NexusHub:entry:default@ProcessIntegratedHsp:1750174057040", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json", {"isDirectory": false, "fileSnapShotHashValue": ""}]]}}, ":NexusHub:entry:default@MakePackInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appResOpt\",\"_value\":\"{\\\"app\\\":{\\\"bundleName\\\":\\\"com.example.nexushub\\\",\\\"vendor\\\":\\\"example\\\",\\\"versionCode\\\":1000000,\\\"versionName\\\":\\\"1.0.0\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:app_name\\\"}}\",\"_valueType\":\"string\",\"_hash\":\"8691b746a0729b72a8e3757be24a948f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":12,\"_valueType\":\"number\",\"_hash\":\"3c48b1937556f5c7e1ff0b2e0af0184f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileSdkVersion\",\"_value\":18,\"_valueType\":\"number\",\"_hash\":\"152326878b5a1b780f9fcd754d3609e8\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"0342e19985c07130618d063695ce1f7c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integratedHsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"e67900c25b1f9fb70cc779de77dc6912\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"default\\\",\\\"tablet\\\"],\\\"deliveryWithInstall\\\":true,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}],\\\"extensionAbilities\\\":[{\\\"name\\\":\\\"EntryBackupAbility\\\",\\\"srcEntry\\\":\\\"./ets/entrybackupability/EntryBackupAbility.ets\\\",\\\"type\\\":\\\"backup\\\",\\\"exported\\\":false,\\\"metadata\\\":[{\\\"name\\\":\\\"ohos.extension.backup\\\",\\\"resource\\\":\\\"$profile:backup_config\\\"}]}],\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\",\\\"reason\\\":\\\"$string:permission_internet_reason\\\",\\\"usedScene\\\":{\\\"abilities\\\":[\\\"EntryAbility\\\"],\\\"when\\\":\\\"inuse\\\"}},{\\\"name\\\":\\\"ohos.permission.GET_NETWORK_INFO\\\"}],\\\"metadata\\\":[{\\\"name\\\":\\\"network_security_config\\\",\\\"resource\\\":\\\"$profile:network_security_config\\\"}]}}\",\"_valueType\":\"string\",\"_hash\":\"e8c91c037dc6ee156460d72785a76ffc\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\",\"_hash\":\"b52997704fa206ed96a13a1f2e464a85\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\",\"_hash\":\"bbcabdda034e97584f8c36f85b3ec517\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@MakePackInfo", "_key": ":NexusHub:entry:default@MakePackInfo", "_executionId": ":NexusHub:entry:default@MakePackInfo:1750162232760", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\app.json5", {"fileSnapShotHashValue": "09cf34510243205467fa62132cc07dba"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "9f48c5206c0e86fff9d623f27ba950c4"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build-profile.json5", {"fileSnapShotHashValue": "c2931e466be903987f78b705610b7587"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources\\base\\profile\\backup_config.json", {"fileSnapShotHashValue": "0a3a00f174fe65ea612ce945be0f13f5"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info", {"fileSnapShotHashValue": "5c976e82bcac4aecd7f21a93de9418c3"}]]}}, ":NexusHub:entry:default@SyscapTransform": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"0342e19985c07130618d063695ce1f7c\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@SyscapTransform", "_key": ":NexusHub:entry:default@SyscapTransform", "_executionId": ":NexusHub:entry:default@SyscapTransform:1750161484950", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\syscap_tool.exe", {"fileSnapShotHashValue": "f60257a9ed8ca2341f877f254d3be2ae"}], ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\device-define", {"fileSnapShotHashValue": "4d08092f8a8bc343b79b02be61bf178b"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc", {"fileSnapShotHashValue": "e0641beadef468f87f171e799c537c2b"}]]}}, ":NexusHub:entry:default@ProcessProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkEnable\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"750b4bda198545a67903dfb3f6a00a95\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"beta1\",\"_valueType\":\"string\",\"_hash\":\"e588408901fe47899eadeda64a5c41fb\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\",\"_hash\":\"dacafc8e0b77a950178a0b4d142dc32c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"dependency\",\"_value\":\"[]\",\"_valueType\":\"string\",\"_hash\":\"ac54f3d4ced2d4c1d666d40e4f7c454a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"0342e19985c07130618d063695ce1f7c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harExcludeHSPDependencies\",\"_valueType\":\"undefined\",\"_hash\":\"441c7ca15ef24eb05ff0b3f48a2c455e\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@ProcessProfile", "_key": ":NexusHub:entry:default@ProcessProfile", "_executionId": ":NexusHub:entry:default@ProcessProfile:1750162232805", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "f895f49569b172ccb9b858fdde711184"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", {"fileSnapShotHashValue": "bfbb6839ce9a7f7626993c84e01a2161"}]]}}, ":NexusHub:entry:default@ProcessRouterMap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"obfuscated\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a69c27b9cf01a6710d3662cfe180239f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@ProcessRouterMap", "_key": ":NexusHub:entry:default@ProcessRouterMap", "_executionId": ":NexusHub:entry:default@ProcessRouterMap:1750161374308", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\oh-package.json5", {"fileSnapShotHashValue": "5ca88c8f0f2d86337e10135f0ac68a1a"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\oh-package.json5", {"fileSnapShotHashValue": "480d6fdb12effc9f7bf6b153e44bfd59"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "9f48c5206c0e86fff9d623f27ba950c4"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "5d9ca1a0d29252758a94a78ac5648f3b"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\router_map\\default\\temp-router-map.json", {"fileSnapShotHashValue": "6e4fa50c935de1ae9b5317e67fe28654"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\router_map\\default\\loader-router-map.json", {"fileSnapShotHashValue": "2629a5fb9c3079e801b3b3ce6e40d4e8"}]]}}, ":NexusHub:entry:default@ProcessStartupConfig": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"appStartupFileName\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@ProcessStartupConfig", "_key": ":NexusHub:entry:default@ProcessStartupConfig", "_executionId": ":NexusHub:entry:default@ProcessStartupConfig:1750174057122", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "5d9ca1a0d29252758a94a78ac5648f3b"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json", {"fileSnapShotHashValue": ""}]]}}, ":NexusHub:entry:default@ProcessResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\opt-compression.json\",\"_value\":\"{\\\"context\\\":{\\\"extensionPath\\\":\\\"\\\"},\\\"compression\\\":{\\\"media\\\":{\\\"enable\\\":false},\\\"filters\\\":[]}}\",\"_valueType\":\"string\",\"_hash\":\"790082a1f2577a16f76c5c9a959a24e4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"resConfigJsonContent\",\"_value\":\"{\\\"configPath\\\":\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\NexusHub-OH\\\\\\\\NexusHub\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\process_profile\\\\\\\\default\\\\\\\\module.json\\\",\\\"packageName\\\":\\\"com.example.nexushub\\\",\\\"output\\\":\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\NexusHub-OH\\\\\\\\NexusHub\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\",\\\"moduleNames\\\":\\\"entry\\\",\\\"ResourceTable\\\":[\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\NexusHub-OH\\\\\\\\NexusHub\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\generated\\\\\\\\r\\\\\\\\default\\\\\\\\ResourceTable.h\\\"],\\\"applicationResource\\\":\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\NexusHub-OH\\\\\\\\NexusHub\\\\\\\\AppScope\\\\\\\\resources\\\",\\\"moduleResources\\\":[\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\NexusHub-OH\\\\\\\\NexusHub\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\resources\\\"],\\\"dependencies\\\":[],\\\"iconCheck\\\":true,\\\"ids\\\":\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\NexusHub-OH\\\\\\\\NexusHub\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ids_map\\\",\\\"definedIds\\\":\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\NexusHub-OH\\\\\\\\NexusHub\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ids_map\\\\\\\\id_defined.json\\\"}\",\"_valueType\":\"string\",\"_hash\":\"6efad0771d3cd5956278d23ff4c128ed\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"resource_str\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"c59ebe125e2aaa014a4454c9564cf3c6\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@ProcessResource", "_key": ":NexusHub:entry:default@ProcessResource", "_executionId": ":NexusHub:entry:default@ProcessResource:1750161374355", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "161a58e0d656869e46daf9462546e2fb"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\opt-compression.json", {"isDirectory": false, "fileSnapShotHashValue": "5be683a1da6ac7561dc1447901d7588e"}]]}}, ":NexusHub:entry:default@GenerateLoaderJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"anBuildMode\",\"_value\":\"type\",\"_valueType\":\"string\",\"_hash\":\"de241a1eec94a2a622ff1c89f32846a2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apPath\",\"_value\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\modules.ap\",\"_valueType\":\"string\",\"_hash\":\"1a50e3835c90da606be7c6cf84d23b5f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appStartupFileName\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"87d81fdb1c6249048cc0cae16c042f58\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":12,\"_valueType\":\"number\",\"_hash\":\"b8c1ba3ecddcd8b1be17325fb873c8bc\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":18,\"_valueType\":\"number\",\"_hash\":\"634461a1eedd122eef53cd10a734a672\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\",\"_hash\":\"dacafc8e0b77a950178a0b4d142dc32c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"fallbackAnBuild\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a63e98bb3f368ced6ed4d5579ea7ca39\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"0faf006bccbcdc2c7f04ff2d8c87894f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hspNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"0e2d87e0c1ed279c66bc3efb8683e5d1\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"c5f9b9e5cabee4253d52d0d01ca64ba2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isFullCompilationEnabled\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"98b683d049e4e7fc32ef1be5321fe0b6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarWithCoverage\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"7224a86bd9c5f83cb9a1a61584afcfb4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isOhosTest\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"d82a43074b4d7726f9a69dbce1ae80d2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"modulePathMap\",\"_value\":\"{\\\"entry\\\":\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\NexusHub-OH\\\\\\\\NexusHub\\\\\\\\entry\\\"}\",\"_valueType\":\"string\",\"_hash\":\"c5bb1439de87efcc947ca61e018f80c6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"module_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"974757f304b5bfd1c1454ea7a38cd0d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needSubmitArkTsWidget\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"dc1ab65720a503a3d9098eab280b7116\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"nodeModulesPath\",\"_value\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\node_modules\",\"_valueType\":\"string\",\"_hash\":\"bbc3ea1d207c4bb3d460705a84ca355b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"overrides\",\"_valueType\":\"undefined\",\"_hash\":\"0e8f66f8eb79c6f33fb153c3fc3942f4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"patchConfig\",\"_value\":\"{\\\"changedFileList\\\":\\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Documents\\\\\\\\NexusHub-OH\\\\\\\\NexusHub\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\patch\\\\\\\\default\\\\\\\\changedFileList.json\\\"}\",\"_valueType\":\"string\",\"_hash\":\"8654630dd4633d671d6ef3a16e7d23b8\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectRootPath\",\"_value\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\",\"_valueType\":\"string\",\"_hash\":\"3576cda104e24d966d76b30cdd64598c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"project_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"e15496bf1de2273597f444f07f1ca6d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"shouldTreatHarAsHap\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"24c9a48f68bd9cc73238825ca10c9629\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetConfig\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\",\"_hash\":\"44f0c01d44e2bbf4013c5bb1f232e1fe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@GenerateLoaderJson", "_key": ":NexusHub:entry:default@GenerateLoaderJson", "_executionId": ":NexusHub:entry:default@GenerateLoaderJson:1750164915611", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5", {"fileSnapShotHashValue": "24bb98c100376cec4f0ec28cc0352e0d"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "5d9ca1a0d29252758a94a78ac5648f3b"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\router_map\\default\\temp-router-map.json", {"fileSnapShotHashValue": "6e4fa50c935de1ae9b5317e67fe28654"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", {"isDirectory": false, "fileSnapShotHashValue": "6710476898a89ad01e7471a09bddc7d4"}]]}}, ":NexusHub:entry:default@ProcessLibs": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"excludeFromHar\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"c33cbd85982f2e758a878e3aa30a9f01\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"c5f9b9e5cabee4253d52d0d01ca64ba2\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@ProcessLibs", "_key": ":NexusHub:entry:default@ProcessLibs", "_executionId": ":NexusHub:entry:default@ProcessLibs:1750161374492", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build-profile.json5", {"fileSnapShotHashValue": "c2931e466be903987f78b705610b7587"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build-profile.json5", {"fileSnapShotHashValue": "95c14d6deea12d2f1db09af5f0b6f5d0"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":NexusHub:entry:default@CompileResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"TARGET_CONFIG\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\restool.exe,-l,C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resConfig.json\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\restool.exe\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@CompileResource", "_key": ":NexusHub:entry:default@CompileResource", "_executionId": ":NexusHub:entry:default@CompileResource:1750174057219", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources", {"fileSnapShotHashValue": "f27936c013ab6e0c28b1e98d850c39b6"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\resources", {"fileSnapShotHashValue": "d16fe496f8d9a7b3e7c01c3f284368d1"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "bfbb6839ce9a7f7626993c84e01a2161"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "161a58e0d656869e46daf9462546e2fb"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default", {"isDirectory": true, "fileSnapShotHashValue": "83d7113c5c28e75386f17950cff1fdef"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h", {"isDirectory": false, "fileSnapShotHashValue": "fdf50c9f654e1c348a127f0697222f56"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\r\\default", {"isDirectory": true, "fileSnapShotHashValue": "a64877bce436df7700b48f50fbd438ae"}]]}}, ":NexusHub:entry:default@DoNativeStrip": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"debugSymbol\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"055eeab6818b3f1a4978705cfba272fa\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@DoNativeStrip", "_key": ":NexusHub:entry:default@DoNativeStrip", "_executionId": ":NexusHub:entry:default@DoNativeStrip:1750161485583", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":NexusHub:entry:default@CompileArkTS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ark.tsImportSendable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"caseSensitive<PERSON><PERSON><PERSON>\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"beta1\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"autoLazyImport\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"allowEmptyBundleName\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"reExportCheckMode\",\"_value\":\"noCheck\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkTsWdiget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"OBFUSCATION_ENABLE\",\"_value\":\"undefined\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"OBFUSCATION_FILES_HASH\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"max<PERSON>low<PERSON>epth\",\"_value\":\"undefined\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"noExternalImportByPath\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceEnable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceExcludes\",\"_value\":[],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ohos.uiTransform.Optimization\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@CompileArkTS", "_key": ":NexusHub:entry:default@CompileArkTS", "_executionId": ":NexusHub:entry:default@CompileArkTS:1750174057528", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "932281e13d355b9d6b7d10ab4f5bc25b"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile", {"isDirectory": true, "fileSnapShotHashValue": ""}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "3842734f091f28faaff51ceab568e3d3"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json", {"fileSnapShotHashValue": "7c7d369176f1165d7b58d62a37da475b"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "7e9cfcf5a5b5662da1852d0ffa148987"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets", {"isDirectory": true, "fileSnapShotHashValue": "02b10eabd30f8fbbd68c2be1f7893dc6"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "5d9ca1a0d29252758a94a78ac5648f3b"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "f00c78eeb2f52e2f59c1428397911f3a"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "24bb98c100376cec4f0ec28cc0352e0d"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":NexusHub:entry:default@BuildJS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ark.tsImportSendable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"caseSensitive<PERSON><PERSON><PERSON>\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"beta1\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"autoLazyImport\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"allowEmptyBundleName\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"reExportCheckMode\",\"_value\":\"noCheck\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@BuildJS", "_key": ":NexusHub:entry:default@BuildJS", "_executionId": ":NexusHub:entry:default@BuildJS:1750174057574", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "932281e13d355b9d6b7d10ab4f5bc25b"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile", {"isDirectory": true, "fileSnapShotHashValue": ""}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "3842734f091f28faaff51ceab568e3d3"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json", {"fileSnapShotHashValue": "7c7d369176f1165d7b58d62a37da475b"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "7e9cfcf5a5b5662da1852d0ffa148987"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "5d9ca1a0d29252758a94a78ac5648f3b"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":NexusHub:entry:default@CacheNativeLibs": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"debugSymbol\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"055eeab6818b3f1a4978705cfba272fa\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@CacheNativeLibs", "_key": ":NexusHub:entry:default@CacheNativeLibs", "_executionId": ":NexusHub:entry:default@CacheNativeLibs:1750161485715", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\patch\\default\\base_native_libs.json", {"isDirectory": false, "fileSnapShotHashValue": "6ae72a3d96f8a6976be31d8429542241"}]]}}, ":NexusHub:entry:default@GeneratePkgModuleJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@GeneratePkgModuleJson", "_key": ":NexusHub:entry:default@GeneratePkgModuleJson", "_executionId": ":NexusHub:entry:default@GeneratePkgModuleJson:1750167713775", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json", {"fileSnapShotHashValue": "c7e515c3b1c5c0c352ee00979c99d339"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\package\\default\\module.json", {"fileSnapShotHashValue": "c7e515c3b1c5c0c352ee00979c99d339"}]]}}, ":NexusHub:entry:default@PackageHap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"java,-Dfile.encoding=GBK,-jar,C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar,--mode,hap,--force,true,--lib-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default,--json-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json,--resources-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources,--index-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index,--pack-info-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info,--out-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap,--rpcid-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc,--ets-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets,--pkg-context-path,C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"bundleType\",\"_value\":\"app\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hotReload\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integrated_hsp\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sourceMapDir\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@PackageHap", "_key": ":NexusHub:entry:default@PackageHap", "_executionId": ":NexusHub:entry:default@PackageHap:1750171502314", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", {"isDirectory": false, "fileSnapShotHashValue": ""}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "c7e515c3b1c5c0c352ee00979c99d339"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources", {"isDirectory": false, "fileSnapShotHashValue": "9a649b41d6c8c88724eace4aa45d262c"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index", {"isDirectory": false, "fileSnapShotHashValue": "36277df4ac9a6a10371678bb4aee869a"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info", {"isDirectory": false, "fileSnapShotHashValue": "5c976e82bcac4aecd7f21a93de9418c3"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc", {"isDirectory": false, "fileSnapShotHashValue": "e0641beadef468f87f171e799c537c2b"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": false, "fileSnapShotHashValue": "21ec48ed2142fc19a820b4b3e20adc41"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"isDirectory": false, "fileSnapShotHashValue": "5d9ca1a0d29252758a94a78ac5648f3b"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map", {"isDirectory": false, "fileSnapShotHashValue": "48c8c6a1c68128c344a04aed704c4cbe"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "f00c78eeb2f52e2f59c1428397911f3a"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap", {"isDirectory": false, "fileSnapShotHashValue": "086fa666ab8e0454aa31f98cc7561dc5"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map", {"fileSnapShotHashValue": "48c8c6a1c68128c344a04aed704c4cbe"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map", {"fileSnapShotHashValue": "48c8c6a1c68128c344a04aed704c4cbe"}]]}}, ":NexusHub:entry:default@SignHap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"enableSignTask\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"existMaterial\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"existSigningConfig\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"5.1.0.107\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_keyAlias\",\"_value\":\"debugKey\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_keyPassword\",\"_value\":\"0000001B76E7EEA9E0F10BCF4AE466647114561CAEECB200D97668F0B1768C5BB527E2D088F718A52AE36A\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_name\",\"_value\":\"default\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_signAlg\",\"_value\":\"SHA256withECDSA\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_storePassword\",\"_value\":\"0000001B65482E631CA34A96FE677B956FD58A421C333163E27DFD280D1DB889B5AC54CDFA86EAB0FA3A61\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_type\",\"_value\":\"HarmonyOS\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@SignHap", "_key": ":NexusHub:entry:default@SignHap", "_executionId": ":NexusHub:entry:default@SignHap:1750171502494", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer", {"isDirectory": false, "fileSnapShotHashValue": "9d95909730bdd836463aa5f65f96b18d"}], ["C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b", {"isDirectory": false, "fileSnapShotHashValue": "371b022ed3b01bb24de05fbc0018ac3b"}], ["C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12", {"isDirectory": false, "fileSnapShotHashValue": "bc240075d653a654954c07d8a6f2046f"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap", {"isDirectory": false, "fileSnapShotHashValue": "086fa666ab8e0454aa31f98cc7561dc5"}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap", {"isDirectory": false, "fileSnapShotHashValue": "d0ae4098ee945900f6c56dc5af9a80ca"}]]}}, ":NexusHub:entry:default@CollectDebugSymbol": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "NexusHub", "_moduleName": "entry", "_taskName": "default@CollectDebugSymbol", "_key": ":NexusHub:entry:default@CollectDebugSymbol", "_executionId": ":NexusHub:entry:default@CollectDebugSymbol:1750171519109", "_inputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map", {"fileSnapShotHashValue": "48c8c6a1c68128c344a04aed704c4cbe"}], ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}, "_outputFiles": {"dataType": "Map", "value": [["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}}