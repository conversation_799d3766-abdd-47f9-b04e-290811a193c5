"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { <PERSON>Container } from "@ant-design/pro-components";
import { Card, Form, Input, Button, Upload, message, Space, InputNumber, Select } from "antd";
const { Option } = Select;
import { UploadOutlined, SaveOutlined, ArrowLeftOutlined, PictureOutlined } from "@ant-design/icons";
import { useState } from "react";
import { useRequest, history, useParams, request } from "@umijs/max";
import { createAppVersion, updateAppVersion, getAppVersionDetail } from "@/services/version";
const { TextArea } = Input;
const CreateEditVersion = () => {
  const params = useParams();
  const appId = params.id;
  const versionId = params.versionId;
  const isEdit = !!versionId;
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [screenshotList, setScreenshotList] = useState([]);
  const [uploading, setUploading] = useState(false);
  const { data: versionDetail, loading: detailLoading } = useRequest(
    () => isEdit ? getAppVersionDetail(Number(versionId)) : Promise.resolve(null),
    {
      ready: isEdit,
      onSuccess: (data) => {
        if (data) {
          form.setFieldsValue({
            versionName: data.versionName,
            versionCode: data.versionCode,
            updateDescription: data.updateDescription
          });
          if (data.filePath) {
            setFileList([{
              uid: "1",
              name: data.fileName || "app.apk",
              status: "done",
              url: data.filePath
            }]);
          }
          if (data.screenshots && data.screenshots.length > 0) {
            const screenshots = data.screenshots.map((screenshot, index) => ({
              uid: `screenshot-${index}`,
              name: `screenshot-${index + 1}.jpg`,
              status: "done",
              url: screenshot
            }));
            setScreenshotList(screenshots);
          }
        }
      }
    }
  );
  const getUploadToken = async (fileType, fileName) => {
    const response = await request("/upload/token", {
      method: "GET",
      params: {
        file_type: fileType,
        file_name: fileName
      }
    });
    if (!response.data || !response.data.file_url) {
      throw new Error("\u83B7\u53D6\u4E0A\u4F20URL\u5931\u8D25");
    }
    return response.data.file_url;
  };
  const uploadFileToStorage = async (file, uploadUrl) => {
    if (!uploadUrl) {
      throw new Error("\u4E0A\u4F20URL\u4E0D\u80FD\u4E3A\u7A7A");
    }
    const response = await fetch(uploadUrl, {
      method: "PUT",
      body: file,
      headers: {
        "Content-Type": file.type
      }
    });
    if (!response.ok) {
      throw new Error("\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25");
    }
    return uploadUrl.split("?")[0];
  };
  const handleSubmit = async (values) => {
    if (!isEdit && fileList.length === 0) {
      message.error("\u8BF7\u4E0A\u4F20\u5E94\u7528\u6587\u4EF6\uFF08HAP\u3001HSP\u3001APP\u683C\u5F0F\uFF09");
      return;
    }
    try {
      setUploading(true);
      let packageUrl = "";
      let fileSize = 0;
      const screenshots = [];
      if (fileList.length > 0 && fileList[0].originFileObj) {
        const file = fileList[0].originFileObj;
        const uploadUrl = await getUploadToken("package", file.name);
        packageUrl = await uploadFileToStorage(file, uploadUrl);
        fileSize = file.size;
      }
      for (const screenshot of screenshotList) {
        if (screenshot.originFileObj) {
          const uploadUrl = await getUploadToken("screenshot", screenshot.originFileObj.name);
          const screenshotUrl = await uploadFileToStorage(screenshot.originFileObj, uploadUrl);
          screenshots.push(screenshotUrl);
        } else if (screenshot.url) {
          screenshots.push(screenshot.url);
        }
      }
      const requestData = {
        version_name: values.versionName,
        version_code: values.versionCode,
        change_log: values.updateDescription || "",
        min_open_harmony_os_ver: values.minHarmonyOSVer,
        ...packageUrl && { package_url: packageUrl },
        ...fileSize && { size: fileSize },
        ...screenshots.length > 0 && { screenshots }
      };
      if (isEdit) {
        await updateAppVersion(Number(appId), Number(versionId), requestData);
        message.success("\u7248\u672C\u66F4\u65B0\u6210\u529F");
      } else {
        await createAppVersion(Number(appId), requestData);
        message.success("\u7248\u672C\u521B\u5EFA\u6210\u529F");
      }
      history.push(`/app/list/versions/${appId}`);
    } catch (error) {
      console.error("\u64CD\u4F5C\u5931\u8D25:", error);
      message.error(isEdit ? "\u7248\u672C\u66F4\u65B0\u5931\u8D25" : "\u7248\u672C\u521B\u5EFA\u5931\u8D25");
    } finally {
      setUploading(false);
    }
  };
  const uploadProps = {
    beforeUpload: (file) => {
      const fileName = file.name.toLowerCase();
      const allowedExtensions = [".hap", ".hsp", ".app"];
      const isValidFile = allowedExtensions.some((ext) => fileName.endsWith(ext));
      if (!isValidFile) {
        message.error("\u53EA\u80FD\u4E0A\u4F20 HAP\u3001HSP\u3001APP \u683C\u5F0F\u7684\u6587\u4EF6");
        return false;
      }
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        message.error("\u6587\u4EF6\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC7 100MB");
        return false;
      }
      return false;
    },
    fileList,
    onChange: ({ fileList: newFileList }) => {
      setFileList(newFileList.slice(-1));
    },
    onRemove: () => {
      setFileList([]);
    }
  };
  const screenshotUploadProps = {
    beforeUpload: (file) => {
      const isImage = file.type.startsWith("image/");
      if (!isImage) {
        message.error("\u53EA\u80FD\u4E0A\u4F20\u56FE\u7247\u6587\u4EF6");
        return false;
      }
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error("\u56FE\u7247\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC7 5MB");
        return false;
      }
      return false;
    },
    fileList: screenshotList,
    onChange: ({ fileList: newFileList }) => {
      setScreenshotList(newFileList.slice(-5));
    },
    onRemove: (file) => {
      setScreenshotList(screenshotList.filter((item) => item.uid !== file.uid));
    },
    multiple: true,
    listType: "picture-card"
  };
  return /* @__PURE__ */ jsx(PageContainer, { children: /* @__PURE__ */ jsxs(Card, { bordered: false, children: [
    /* @__PURE__ */ jsx("div", { style: { marginBottom: 16 }, children: /* @__PURE__ */ jsx(
      Button,
      {
        icon: /* @__PURE__ */ jsx(ArrowLeftOutlined, {}),
        onClick: () => history.push(`/app/list/versions/${appId}`),
        children: "\u8FD4\u56DE\u7248\u672C\u5217\u8868"
      }
    ) }),
    /* @__PURE__ */ jsxs(
      Form,
      {
        form,
        layout: "vertical",
        onFinish: handleSubmit,
        loading: detailLoading,
        children: [
          /* @__PURE__ */ jsx(
            Form.Item,
            {
              name: "versionName",
              label: "\u7248\u672C\u53F7",
              rules: [
                { required: true, message: "\u8BF7\u8F93\u5165\u7248\u672C\u53F7" },
                { pattern: /^\d+\.\d+\.\d+$/, message: "\u7248\u672C\u53F7\u683C\u5F0F\u5E94\u4E3A x.y.z\uFF08\u5982\uFF1A1.0.0\uFF09" }
              ],
              children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u7248\u672C\u53F7\uFF0C\u5982\uFF1A1.0.0" })
            }
          ),
          /* @__PURE__ */ jsx(
            Form.Item,
            {
              name: "versionCode",
              label: "\u7248\u672C\u4EE3\u7801",
              rules: [
                { required: true, message: "\u8BF7\u8F93\u5165\u7248\u672C\u4EE3\u7801" },
                { type: "number", min: 1, message: "\u7248\u672C\u4EE3\u7801\u5FC5\u987B\u662F\u5927\u4E8E0\u7684\u6574\u6570" }
              ],
              children: /* @__PURE__ */ jsx(
                InputNumber,
                {
                  placeholder: "\u8BF7\u8F93\u5165\u7248\u672C\u4EE3\u7801\uFF08\u6574\u6570\uFF09",
                  style: { width: "100%" },
                  min: 1
                }
              )
            }
          ),
          /* @__PURE__ */ jsx(
            Form.Item,
            {
              name: "updateDescription",
              label: "\u66F4\u65B0\u8BF4\u660E",
              rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u66F4\u65B0\u8BF4\u660E" }],
              children: /* @__PURE__ */ jsx(
                TextArea,
                {
                  rows: 6,
                  placeholder: "\u8BF7\u8BE6\u7EC6\u63CF\u8FF0\u672C\u6B21\u66F4\u65B0\u7684\u5185\u5BB9...",
                  showCount: true,
                  maxLength: 1e3
                }
              )
            }
          ),
          /* @__PURE__ */ jsx(
            Form.Item,
            {
              name: "minHarmonyOSVer",
              label: "\u6700\u4F4EHarmonyOS\u7248\u672C",
              rules: [{ required: true, message: "\u8BF7\u9009\u62E9\u6700\u4F4EHarmonyOS\u7248\u672C" }],
              children: /* @__PURE__ */ jsxs(Select, { placeholder: "\u8BF7\u9009\u62E9\u6700\u4F4EHarmonyOS\u7248\u672C", children: [
                /* @__PURE__ */ jsx(Option, { value: "2.0", children: "HarmonyOS 2.0" }),
                /* @__PURE__ */ jsx(Option, { value: "3.0", children: "HarmonyOS 3.0" }),
                /* @__PURE__ */ jsx(Option, { value: "4.0", children: "HarmonyOS 4.0" }),
                /* @__PURE__ */ jsx(Option, { value: "5.0", children: "HarmonyOS 5.0" })
              ] })
            }
          ),
          /* @__PURE__ */ jsxs(
            Form.Item,
            {
              label: "\u5E94\u7528\u6587\u4EF6",
              required: !isEdit,
              children: [
                /* @__PURE__ */ jsx(Upload, { ...uploadProps, children: /* @__PURE__ */ jsx(Button, { icon: /* @__PURE__ */ jsx(UploadOutlined, {}), children: isEdit ? "\u91CD\u65B0\u4E0A\u4F20\u6587\u4EF6\uFF08\u53EF\u9009\uFF09" : "\u9009\u62E9\u5E94\u7528\u6587\u4EF6" }) }),
                /* @__PURE__ */ jsx("div", { style: { marginTop: 8, color: "#666", fontSize: "12px" }, children: "\u652F\u6301 HAP\u3001HSP\u3001APP \u683C\u5F0F\uFF0C\u6587\u4EF6\u5927\u5C0F\u4E0D\u8D85\u8FC7 100MB" })
              ]
            }
          ),
          /* @__PURE__ */ jsx(
            Form.Item,
            {
              label: "\u5E94\u7528\u622A\u56FE",
              extra: "\u4E0A\u4F20\u5E94\u7528\u622A\u56FE\uFF0C\u6700\u591A5\u5F20\uFF0C\u652F\u6301 JPG\u3001PNG \u683C\u5F0F\uFF0C\u5355\u5F20\u56FE\u7247\u4E0D\u8D85\u8FC7 5MB",
              children: /* @__PURE__ */ jsx(Upload, { ...screenshotUploadProps, children: screenshotList.length >= 5 ? null : /* @__PURE__ */ jsxs("div", { style: {
                width: 104,
                height: 104,
                border: "1px dashed #d9d9d9",
                borderRadius: 6,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
                transition: "border-color 0.3s"
              }, children: [
                /* @__PURE__ */ jsx(PictureOutlined, { style: { fontSize: 24, color: "#999" } }),
                /* @__PURE__ */ jsx("div", { style: { marginTop: 8, color: "#999", fontSize: 12 }, children: "\u4E0A\u4F20\u622A\u56FE" })
              ] }) })
            }
          ),
          /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsxs(Space, { children: [
            /* @__PURE__ */ jsx(
              Button,
              {
                type: "primary",
                htmlType: "submit",
                icon: /* @__PURE__ */ jsx(SaveOutlined, {}),
                loading: uploading,
                children: isEdit ? "\u66F4\u65B0\u7248\u672C" : "\u521B\u5EFA\u7248\u672C"
              }
            ),
            /* @__PURE__ */ jsx(
              Button,
              {
                onClick: () => history.push(`/app/list/versions/${appId}`),
                children: "\u53D6\u6D88"
              }
            )
          ] }) })
        ]
      }
    )
  ] }) });
};
export default CreateEditVersion;
