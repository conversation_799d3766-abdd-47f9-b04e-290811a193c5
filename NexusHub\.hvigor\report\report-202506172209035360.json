{"version": "2.0", "ppid": 33740, "events": [{"head": {"id": "3b33cf74-c885-47ce-b7ab-ef67e1d8cbf3", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144643205410500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69d7000b-a714-46cd-a4bb-f759582fb2f1", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144643212767700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc12690d-0d18-474a-bd07-965ca4aa63e2", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144643215147100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d431228-0ccc-4c28-8f27-cbc708048962", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144643215631000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78bbae92-eea9-45a5-bc0b-2ea56f9786af", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144643220982600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee64b479-9c1c-47c3-b988-b7f9511c02cc", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144643222920600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf03c703-7308-48fd-95e2-823861a8a657", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713753932800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d08e54a2-2e06-4fbc-818d-0018692c6a4d", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713759633200, "endTime": 144714151213300}, "additional": {"children": ["2dc08507-99ee-4fc2-9e3a-8dc6cbca73c9", "a3c4bcc6-f971-4897-be45-2b0f53c44d8b", "b2ade5bd-bd3c-431b-a105-ab536dd13423", "ab2dece4-9dd0-4300-9e65-2f9efa544ad4", "20501982-8644-4f97-ab92-ebdac55e0324", "ca42c41e-8a99-4048-91b1-cab35d815433", "0a97449c-db84-4eb8-b704-69bdaf4c9727"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "10130397-2b7e-4380-b60f-b951e201415e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2dc08507-99ee-4fc2-9e3a-8dc6cbca73c9", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713759640300, "endTime": 144713773233200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d08e54a2-2e06-4fbc-818d-0018692c6a4d", "logId": "c31be5a6-a683-47a2-aaf4-6beea1e0544c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3c4bcc6-f971-4897-be45-2b0f53c44d8b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713773262900, "endTime": 144714149302500}, "additional": {"children": ["db2d772a-4661-4738-87b5-d3853fa0d21f", "bf32f1b0-21e2-4c4a-9605-3822b3d72705", "577d1cd0-b55f-4532-8d88-9e60310b4a0f", "5c805c81-3196-40c6-a0d8-356e7c1aff59", "b37113ea-b90d-40b2-b06a-958b552fa94c", "8376db3d-365f-48a6-9720-cbd62d55d126", "932686fb-9cda-4900-956c-13c0e3acec9f", "953984a0-35a7-4c94-9582-14da8bcac115", "a5ffff54-7629-4ec0-9a4b-5defd70bf04f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d08e54a2-2e06-4fbc-818d-0018692c6a4d", "logId": "48d46b7c-9332-4e80-b6df-26ccc76d699a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2ade5bd-bd3c-431b-a105-ab536dd13423", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714149335600, "endTime": 144714151159800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d08e54a2-2e06-4fbc-818d-0018692c6a4d", "logId": "31fdf919-8cec-48f6-85ae-448cda5acb55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab2dece4-9dd0-4300-9e65-2f9efa544ad4", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714151171400, "endTime": 144714151205400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d08e54a2-2e06-4fbc-818d-0018692c6a4d", "logId": "ac85ee21-ba9d-466e-900b-d6b38ae935a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20501982-8644-4f97-ab92-ebdac55e0324", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713762751600, "endTime": 144713762798100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d08e54a2-2e06-4fbc-818d-0018692c6a4d", "logId": "f99a5196-b3e6-48b5-9eb7-1431ba9d977d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f99a5196-b3e6-48b5-9eb7-1431ba9d977d", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713762751600, "endTime": 144713762798100}, "additional": {"logType": "info", "children": [], "durationId": "20501982-8644-4f97-ab92-ebdac55e0324", "parent": "10130397-2b7e-4380-b60f-b951e201415e"}}, {"head": {"id": "ca42c41e-8a99-4048-91b1-cab35d815433", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713767752900, "endTime": 144713767770300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d08e54a2-2e06-4fbc-818d-0018692c6a4d", "logId": "2afcdc66-79ec-4802-8891-d2a380c9bb4d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2afcdc66-79ec-4802-8891-d2a380c9bb4d", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713767752900, "endTime": 144713767770300}, "additional": {"logType": "info", "children": [], "durationId": "ca42c41e-8a99-4048-91b1-cab35d815433", "parent": "10130397-2b7e-4380-b60f-b951e201415e"}}, {"head": {"id": "9c2e429f-6131-4029-99fa-0dc04d16a4d3", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713768235000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e25cc300-0577-4dca-b84d-acbc0a5ef45d", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713773046600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c31be5a6-a683-47a2-aaf4-6beea1e0544c", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713759640300, "endTime": 144713773233200}, "additional": {"logType": "info", "children": [], "durationId": "2dc08507-99ee-4fc2-9e3a-8dc6cbca73c9", "parent": "10130397-2b7e-4380-b60f-b951e201415e"}}, {"head": {"id": "db2d772a-4661-4738-87b5-d3853fa0d21f", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713780620400, "endTime": 144713780633000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c4bcc6-f971-4897-be45-2b0f53c44d8b", "logId": "e3322756-8ca9-44cc-99ac-7495b026e665"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf32f1b0-21e2-4c4a-9605-3822b3d72705", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713780657400, "endTime": 144713786527300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c4bcc6-f971-4897-be45-2b0f53c44d8b", "logId": "f190bfbb-ffb8-458f-8146-5d91c35e1487"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "577d1cd0-b55f-4532-8d88-9e60310b4a0f", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713786540700, "endTime": 144713936932400}, "additional": {"children": ["3971fca5-1565-45f2-9fc1-3037251e08be", "671c0cb5-0d7d-41f2-9005-39a39ab58658", "4c2945ac-17d7-484c-9ece-22b7768ffb74"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c4bcc6-f971-4897-be45-2b0f53c44d8b", "logId": "fb045239-4aa4-4c91-807a-cf3e3b98835f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c805c81-3196-40c6-a0d8-356e7c1aff59", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713936959300, "endTime": 144713990129600}, "additional": {"children": ["76729c88-3128-48aa-a690-7bdfe3809280"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c4bcc6-f971-4897-be45-2b0f53c44d8b", "logId": "8643b368-8763-49eb-8b03-dd7d6b7c7b52"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b37113ea-b90d-40b2-b06a-958b552fa94c", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713990157500, "endTime": 144714106425800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c4bcc6-f971-4897-be45-2b0f53c44d8b", "logId": "bac53aff-762b-471d-a0c2-7d2064dd69d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8376db3d-365f-48a6-9720-cbd62d55d126", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714108160400, "endTime": 144714124069600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c4bcc6-f971-4897-be45-2b0f53c44d8b", "logId": "d8c39729-2946-4ddb-ac0a-1c40e99e074b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "932686fb-9cda-4900-956c-13c0e3acec9f", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714124105100, "endTime": 144714149063700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c4bcc6-f971-4897-be45-2b0f53c44d8b", "logId": "675ef914-fbe3-4086-b50d-1bb96419a74f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "953984a0-35a7-4c94-9582-14da8bcac115", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714149095300, "endTime": 144714149286200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c4bcc6-f971-4897-be45-2b0f53c44d8b", "logId": "8e6409de-a1ad-412d-8449-3de7a38c63e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3322756-8ca9-44cc-99ac-7495b026e665", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713780620400, "endTime": 144713780633000}, "additional": {"logType": "info", "children": [], "durationId": "db2d772a-4661-4738-87b5-d3853fa0d21f", "parent": "48d46b7c-9332-4e80-b6df-26ccc76d699a"}}, {"head": {"id": "f190bfbb-ffb8-458f-8146-5d91c35e1487", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713780657400, "endTime": 144713786527300}, "additional": {"logType": "info", "children": [], "durationId": "bf32f1b0-21e2-4c4a-9605-3822b3d72705", "parent": "48d46b7c-9332-4e80-b6df-26ccc76d699a"}}, {"head": {"id": "3971fca5-1565-45f2-9fc1-3037251e08be", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713787251000, "endTime": 144713787274300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "577d1cd0-b55f-4532-8d88-9e60310b4a0f", "logId": "0dc288f6-4aa0-4a64-b64f-09c694125590"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dc288f6-4aa0-4a64-b64f-09c694125590", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713787251000, "endTime": 144713787274300}, "additional": {"logType": "info", "children": [], "durationId": "3971fca5-1565-45f2-9fc1-3037251e08be", "parent": "fb045239-4aa4-4c91-807a-cf3e3b98835f"}}, {"head": {"id": "671c0cb5-0d7d-41f2-9005-39a39ab58658", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713790319100, "endTime": 144713935107500}, "additional": {"children": ["90404406-3580-4a04-ad92-a63829d1a86e", "3bdafd8a-c85d-40dd-bfe2-a97edcabef61"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "577d1cd0-b55f-4532-8d88-9e60310b4a0f", "logId": "b4074032-eb76-4425-8186-ddbc6d67105e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90404406-3580-4a04-ad92-a63829d1a86e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713790320700, "endTime": 144713801111900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "671c0cb5-0d7d-41f2-9005-39a39ab58658", "logId": "da7c8443-2124-4b4b-898e-9de54d80416b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3bdafd8a-c85d-40dd-bfe2-a97edcabef61", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713801141300, "endTime": 144713935087100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "671c0cb5-0d7d-41f2-9005-39a39ab58658", "logId": "1a79ad94-5a4f-456f-8683-394d680def17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da9dc6c5-ce4f-43e4-a643-dc14a3d18868", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713790329400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceecffe1-3a86-4c4c-9fcb-204486983cf4", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713800867600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da7c8443-2124-4b4b-898e-9de54d80416b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713790320700, "endTime": 144713801111900}, "additional": {"logType": "info", "children": [], "durationId": "90404406-3580-4a04-ad92-a63829d1a86e", "parent": "b4074032-eb76-4425-8186-ddbc6d67105e"}}, {"head": {"id": "9e7a6a66-64f3-4afb-aae5-3c56a885a7a3", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713801164400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ebc551c-9baa-45b6-918d-de580120ae97", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713814956200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95621265-1b6a-441b-a545-09355b55c07e", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713815286200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a78bf65b-4f82-440c-9cfb-2bee3abfa3b4", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713815655400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4daf4ae-7f38-4e71-b70c-38499c78d2fa", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713816026800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc94a540-c679-4a56-8927-d2bd01c08cad", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713820119100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0daf30e-18ea-43a1-87b0-81f73625aced", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713844371900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1258a706-6624-4003-a30a-976711829853", "name": "Sdk init in 55 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713884694800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3bc26d5-4f3e-44bd-907d-ffe103d2a71d", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713884934600}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 9, "second": 3}, "markType": "other"}}, {"head": {"id": "33c56998-ab0d-42c5-ae38-b752ce4c165c", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713885001400}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 9, "second": 3}, "markType": "other"}}, {"head": {"id": "0b7b9474-a750-4b8a-9242-ad96fb274149", "name": "Project task initialization takes 47 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713934546400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0c6195f-ca88-42fd-8a7f-8666323d9254", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713934778800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78759799-d564-44fc-a173-5f487c6571aa", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713934901500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33a2266f-6acf-4507-90e1-f0dbccbf0fe8", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713934998600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a79ad94-5a4f-456f-8683-394d680def17", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713801141300, "endTime": 144713935087100}, "additional": {"logType": "info", "children": [], "durationId": "3bdafd8a-c85d-40dd-bfe2-a97edcabef61", "parent": "b4074032-eb76-4425-8186-ddbc6d67105e"}}, {"head": {"id": "b4074032-eb76-4425-8186-ddbc6d67105e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713790319100, "endTime": 144713935107500}, "additional": {"logType": "info", "children": ["da7c8443-2124-4b4b-898e-9de54d80416b", "1a79ad94-5a4f-456f-8683-394d680def17"], "durationId": "671c0cb5-0d7d-41f2-9005-39a39ab58658", "parent": "fb045239-4aa4-4c91-807a-cf3e3b98835f"}}, {"head": {"id": "4c2945ac-17d7-484c-9ece-22b7768ffb74", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713936856600, "endTime": 144713936893400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "577d1cd0-b55f-4532-8d88-9e60310b4a0f", "logId": "76e7cea5-0ad4-4abf-84ba-d9495af20d63"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76e7cea5-0ad4-4abf-84ba-d9495af20d63", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713936856600, "endTime": 144713936893400}, "additional": {"logType": "info", "children": [], "durationId": "4c2945ac-17d7-484c-9ece-22b7768ffb74", "parent": "fb045239-4aa4-4c91-807a-cf3e3b98835f"}}, {"head": {"id": "fb045239-4aa4-4c91-807a-cf3e3b98835f", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713786540700, "endTime": 144713936932400}, "additional": {"logType": "info", "children": ["0dc288f6-4aa0-4a64-b64f-09c694125590", "b4074032-eb76-4425-8186-ddbc6d67105e", "76e7cea5-0ad4-4abf-84ba-d9495af20d63"], "durationId": "577d1cd0-b55f-4532-8d88-9e60310b4a0f", "parent": "48d46b7c-9332-4e80-b6df-26ccc76d699a"}}, {"head": {"id": "76729c88-3128-48aa-a690-7bdfe3809280", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713938880600, "endTime": 144713990110200}, "additional": {"children": ["56e90335-b5b8-4220-af39-ad90e83780c1", "eb73eff6-fd24-43ef-ad1b-c29cdba72692", "05ade0f5-e353-4086-a641-cf0069a48147"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5c805c81-3196-40c6-a0d8-356e7c1aff59", "logId": "62016456-1bc9-4303-bf97-6c2eb6e6a51e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56e90335-b5b8-4220-af39-ad90e83780c1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713946409400, "endTime": 144713946452100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "76729c88-3128-48aa-a690-7bdfe3809280", "logId": "c1357d80-3c4c-46d8-a308-5b4ce503aa10"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1357d80-3c4c-46d8-a308-5b4ce503aa10", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713946409400, "endTime": 144713946452100}, "additional": {"logType": "info", "children": [], "durationId": "56e90335-b5b8-4220-af39-ad90e83780c1", "parent": "62016456-1bc9-4303-bf97-6c2eb6e6a51e"}}, {"head": {"id": "eb73eff6-fd24-43ef-ad1b-c29cdba72692", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713950993700, "endTime": 144713986994700}, "additional": {"children": ["f673d08c-f1ba-43cb-817e-4e20bafa7b1d", "5c24b131-ab64-4e66-8b12-0652cb2b6c73"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "76729c88-3128-48aa-a690-7bdfe3809280", "logId": "a3289544-4f8d-4ef8-8239-086a91187a43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f673d08c-f1ba-43cb-817e-4e20bafa7b1d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713950996300, "endTime": 144713958525400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "eb73eff6-fd24-43ef-ad1b-c29cdba72692", "logId": "0ef6bb4c-675c-4cc6-8a39-b5f82436ab31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c24b131-ab64-4e66-8b12-0652cb2b6c73", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713958551400, "endTime": 144713986978000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "eb73eff6-fd24-43ef-ad1b-c29cdba72692", "logId": "9f40da87-25a2-437c-acf8-60b00bb0f1ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59e0be74-2c27-40fc-bbdd-841ef31035ea", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713951007800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01f040dd-11d9-4bfc-a314-72916a6f7e0e", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713958298400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ef6bb4c-675c-4cc6-8a39-b5f82436ab31", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713950996300, "endTime": 144713958525400}, "additional": {"logType": "info", "children": [], "durationId": "f673d08c-f1ba-43cb-817e-4e20bafa7b1d", "parent": "a3289544-4f8d-4ef8-8239-086a91187a43"}}, {"head": {"id": "86420c3f-3e27-499e-a740-872a7361fab7", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713958577300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76145dd2-381e-4bc5-8efa-bcbe2d5cb8b1", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713973219000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1de87a3e-1f08-42af-8ca6-fe6c3f898af5", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713973437000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42db0251-713d-42b4-9f0f-3a50fd21cc26", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713974103200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d2286d1-3791-4d97-84e2-81caa483c8fe", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713974446400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfcd52ac-6e7a-4a1f-8f71-0e0637ecd327", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713974576300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1429e858-049e-4d3b-b648-efb5eed36b5e", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713974675900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1277db13-0156-44e9-b073-a360e3a25bd1", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713974827400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d76fe2-f1d9-434a-9ef4-7ba2bac300f2", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713974948900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75eaea8a-5fa2-435b-ac74-1bc50844d337", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713975824500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d469b5-5675-4216-b609-d1c05adad4cd", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713976173000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8382330c-4a12-4e79-9ede-e699f0786b3e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713976318700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca7de2af-702b-470e-a88a-550612239b40", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713976419500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "602fa02b-5e80-43bf-a45b-14c34e0a16a6", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713976585600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "735534f2-4369-40e7-ab00-327970c3d3fa", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713976775300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ffd013d-0c80-4758-a824-2c250ef176f8", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713977123500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "744a0755-9eb2-4fa8-bd71-9c88d62d5f74", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713977408200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ff0cc93-4c0d-46b0-9b41-910ccbf2dd13", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713977545900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66c70d88-9d96-424e-bbb6-14f5150b30f6", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713977673500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f94755b7-35ed-4f66-b58c-ddb19e5abc07", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713977836700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc3196cf-82ac-4e9c-be22-8be84daaee96", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713986355000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13269771-f6f4-4247-bd06-311161db6c3d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713986657100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaacb111-498b-4c20-96d6-9325fb4e0b2f", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713986795600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4e1d99a-6e3d-4877-bd6b-10989eeccb94", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713986888600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f40da87-25a2-437c-acf8-60b00bb0f1ea", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713958551400, "endTime": 144713986978000}, "additional": {"logType": "info", "children": [], "durationId": "5c24b131-ab64-4e66-8b12-0652cb2b6c73", "parent": "a3289544-4f8d-4ef8-8239-086a91187a43"}}, {"head": {"id": "a3289544-4f8d-4ef8-8239-086a91187a43", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713950993700, "endTime": 144713986994700}, "additional": {"logType": "info", "children": ["0ef6bb4c-675c-4cc6-8a39-b5f82436ab31", "9f40da87-25a2-437c-acf8-60b00bb0f1ea"], "durationId": "eb73eff6-fd24-43ef-ad1b-c29cdba72692", "parent": "62016456-1bc9-4303-bf97-6c2eb6e6a51e"}}, {"head": {"id": "05ade0f5-e353-4086-a641-cf0069a48147", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713990061700, "endTime": 144713990088500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "76729c88-3128-48aa-a690-7bdfe3809280", "logId": "c2a60439-293f-4f8b-b27d-42f91a75be11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2a60439-293f-4f8b-b27d-42f91a75be11", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713990061700, "endTime": 144713990088500}, "additional": {"logType": "info", "children": [], "durationId": "05ade0f5-e353-4086-a641-cf0069a48147", "parent": "62016456-1bc9-4303-bf97-6c2eb6e6a51e"}}, {"head": {"id": "62016456-1bc9-4303-bf97-6c2eb6e6a51e", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713938880600, "endTime": 144713990110200}, "additional": {"logType": "info", "children": ["c1357d80-3c4c-46d8-a308-5b4ce503aa10", "a3289544-4f8d-4ef8-8239-086a91187a43", "c2a60439-293f-4f8b-b27d-42f91a75be11"], "durationId": "76729c88-3128-48aa-a690-7bdfe3809280", "parent": "8643b368-8763-49eb-8b03-dd7d6b7c7b52"}}, {"head": {"id": "8643b368-8763-49eb-8b03-dd7d6b7c7b52", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713936959300, "endTime": 144713990129600}, "additional": {"logType": "info", "children": ["62016456-1bc9-4303-bf97-6c2eb6e6a51e"], "durationId": "5c805c81-3196-40c6-a0d8-356e7c1aff59", "parent": "48d46b7c-9332-4e80-b6df-26ccc76d699a"}}, {"head": {"id": "ecbb4e9c-efb9-4990-b4ba-46f5b206507a", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714039380400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "852dbc5a-3286-4d4e-9013-74182ef96467", "name": "hvigorfile, resolve hvigorfile dependencies in 117 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714106274100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bac53aff-762b-471d-a0c2-7d2064dd69d0", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713990157500, "endTime": 144714106425800}, "additional": {"logType": "info", "children": [], "durationId": "b37113ea-b90d-40b2-b06a-958b552fa94c", "parent": "48d46b7c-9332-4e80-b6df-26ccc76d699a"}}, {"head": {"id": "a5ffff54-7629-4ec0-9a4b-5defd70bf04f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714107645000, "endTime": 144714108134500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3c4bcc6-f971-4897-be45-2b0f53c44d8b", "logId": "283bc1ea-3e6c-4b9c-88df-957f8c341733"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6924dfa4-a247-4944-8d02-fcb043e381f6", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714107697500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "283bc1ea-3e6c-4b9c-88df-957f8c341733", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714107645000, "endTime": 144714108134500}, "additional": {"logType": "info", "children": [], "durationId": "a5ffff54-7629-4ec0-9a4b-5defd70bf04f", "parent": "48d46b7c-9332-4e80-b6df-26ccc76d699a"}}, {"head": {"id": "9f48405a-b8ca-4f92-bcec-2bbb58551a6d", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714110865700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8923969-505b-4c1a-995e-2183ef06b276", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714122432300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8c39729-2946-4ddb-ac0a-1c40e99e074b", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714108160400, "endTime": 144714124069600}, "additional": {"logType": "info", "children": [], "durationId": "8376db3d-365f-48a6-9720-cbd62d55d126", "parent": "48d46b7c-9332-4e80-b6df-26ccc76d699a"}}, {"head": {"id": "f669deb7-502a-4d1e-b822-7472ce2f6bb4", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714124131500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f52cc3e6-5a1f-4231-86df-35ed9bf4e84b", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714137946100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3f3fc31-055f-49f6-beb3-29357db891ba", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714138127300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "523b4bcc-3268-4fdb-b9d3-376fb9341d24", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714138386900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a65f63b5-c502-4311-a0be-e3dd138c0e19", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714143988700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd270651-7a96-45a0-926f-fc5c2c07885e", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714144158500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "675ef914-fbe3-4086-b50d-1bb96419a74f", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714124105100, "endTime": 144714149063700}, "additional": {"logType": "info", "children": [], "durationId": "932686fb-9cda-4900-956c-13c0e3acec9f", "parent": "48d46b7c-9332-4e80-b6df-26ccc76d699a"}}, {"head": {"id": "0f286778-c754-473b-a55e-369b2bf8d0e0", "name": "Configuration phase cost:369 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714149139900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e6409de-a1ad-412d-8449-3de7a38c63e3", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714149095300, "endTime": 144714149286200}, "additional": {"logType": "info", "children": [], "durationId": "953984a0-35a7-4c94-9582-14da8bcac115", "parent": "48d46b7c-9332-4e80-b6df-26ccc76d699a"}}, {"head": {"id": "48d46b7c-9332-4e80-b6df-26ccc76d699a", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713773262900, "endTime": 144714149302500}, "additional": {"logType": "info", "children": ["e3322756-8ca9-44cc-99ac-7495b026e665", "f190bfbb-ffb8-458f-8146-5d91c35e1487", "fb045239-4aa4-4c91-807a-cf3e3b98835f", "8643b368-8763-49eb-8b03-dd7d6b7c7b52", "bac53aff-762b-471d-a0c2-7d2064dd69d0", "d8c39729-2946-4ddb-ac0a-1c40e99e074b", "675ef914-fbe3-4086-b50d-1bb96419a74f", "8e6409de-a1ad-412d-8449-3de7a38c63e3", "283bc1ea-3e6c-4b9c-88df-957f8c341733"], "durationId": "a3c4bcc6-f971-4897-be45-2b0f53c44d8b", "parent": "10130397-2b7e-4380-b60f-b951e201415e"}}, {"head": {"id": "0a97449c-db84-4eb8-b704-69bdaf4c9727", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714151120200, "endTime": 144714151143600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d08e54a2-2e06-4fbc-818d-0018692c6a4d", "logId": "dd15d094-8965-4c10-8397-cb7a8763e86a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd15d094-8965-4c10-8397-cb7a8763e86a", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714151120200, "endTime": 144714151143600}, "additional": {"logType": "info", "children": [], "durationId": "0a97449c-db84-4eb8-b704-69bdaf4c9727", "parent": "10130397-2b7e-4380-b60f-b951e201415e"}}, {"head": {"id": "31fdf919-8cec-48f6-85ae-448cda5acb55", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714149335600, "endTime": 144714151159800}, "additional": {"logType": "info", "children": [], "durationId": "b2ade5bd-bd3c-431b-a105-ab536dd13423", "parent": "10130397-2b7e-4380-b60f-b951e201415e"}}, {"head": {"id": "ac85ee21-ba9d-466e-900b-d6b38ae935a0", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714151171400, "endTime": 144714151205400}, "additional": {"logType": "info", "children": [], "durationId": "ab2dece4-9dd0-4300-9e65-2f9efa544ad4", "parent": "10130397-2b7e-4380-b60f-b951e201415e"}}, {"head": {"id": "10130397-2b7e-4380-b60f-b951e201415e", "name": "init", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713759633200, "endTime": 144714151213300}, "additional": {"logType": "info", "children": ["c31be5a6-a683-47a2-aaf4-6beea1e0544c", "48d46b7c-9332-4e80-b6df-26ccc76d699a", "31fdf919-8cec-48f6-85ae-448cda5acb55", "ac85ee21-ba9d-466e-900b-d6b38ae935a0", "f99a5196-b3e6-48b5-9eb7-1431ba9d977d", "2afcdc66-79ec-4802-8891-d2a380c9bb4d", "dd15d094-8965-4c10-8397-cb7a8763e86a"], "durationId": "d08e54a2-2e06-4fbc-818d-0018692c6a4d"}}, {"head": {"id": "54425674-7243-4cb1-83a3-8c501ad53de7", "name": "Configuration task cost before running: 395 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714151629500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c093df19-d022-4fcb-8f08-1e81ec136360", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714167686400, "endTime": 144714180241900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d2c892e7-3700-4f4e-afa5-1c62854cd951", "logId": "aae4a8ab-3e27-4d0d-b6e9-7d85633c4caa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2c892e7-3700-4f4e-afa5-1c62854cd951", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714154389200}, "additional": {"logType": "detail", "children": [], "durationId": "c093df19-d022-4fcb-8f08-1e81ec136360"}}, {"head": {"id": "9158f756-129e-4d79-92b1-ec44b677f7d3", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714155687700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bea50a2-62c8-4481-91af-4ec45d14c9de", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714155905000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86118a78-167d-4766-a9ab-10bed290d6ea", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714157234200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc73f18f-8c19-4a96-9a8e-157a7d3da770", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714158362700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "094fcf60-7ad3-4e1a-a0a9-6dbee7da9c3b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714160035000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a7d637c-472a-44f6-8913-22e299aceff3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714160201900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e0aa46a-433e-4df4-ab65-b760577b53ad", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714167721800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ba4ab28-46e5-41f6-8749-170a123f1d69", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714179996400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d9c9df7-a78e-498a-a1d2-f9d26257c958", "name": "entry : default@PreBuild cost memory 0.3270721435546875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714180172900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aae4a8ab-3e27-4d0d-b6e9-7d85633c4caa", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714167686400, "endTime": 144714180241900}, "additional": {"logType": "info", "children": [], "durationId": "c093df19-d022-4fcb-8f08-1e81ec136360"}}, {"head": {"id": "8958c95b-59b4-429e-beeb-a51864921552", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714193064200, "endTime": 144714195507900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2418d940-33b1-4946-adf4-14ea8b5e2192", "logId": "c27f2999-7090-41b7-9efe-dbd30135ed20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2418d940-33b1-4946-adf4-14ea8b5e2192", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714190290400}, "additional": {"logType": "detail", "children": [], "durationId": "8958c95b-59b4-429e-beeb-a51864921552"}}, {"head": {"id": "597123d7-c0be-4ff2-8f83-ce64df3ee43a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714192306400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2679d58-6c94-4f6b-93ab-71c8dc55e399", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714192430100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d6dfb20-3976-4666-a149-8bb535421286", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714193077200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dcd85e4-ef4b-48d6-947e-ea7c49ea2289", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714194022400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7627b821-20ca-406b-bc8b-522802abf9eb", "name": "entry : default@CreateModuleInfo cost memory 0.0619659423828125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714195266800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d56fe820-2115-4b00-ba52-f2924ca32769", "name": "runTaskFromQueue task cost before running: 439 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714195437100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c27f2999-7090-41b7-9efe-dbd30135ed20", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714193064200, "endTime": 144714195507900, "totalTime": 2339100}, "additional": {"logType": "info", "children": [], "durationId": "8958c95b-59b4-429e-beeb-a51864921552"}}, {"head": {"id": "ee5751c5-7ce1-4eb0-a115-f2acdf5006c3", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714211875800, "endTime": 144714215499200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5f919083-4587-488f-93e3-cb451e1d585a", "logId": "4ddf57df-44a0-43a7-8134-d2d7c5d89011"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f919083-4587-488f-93e3-cb451e1d585a", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714200271200}, "additional": {"logType": "detail", "children": [], "durationId": "ee5751c5-7ce1-4eb0-a115-f2acdf5006c3"}}, {"head": {"id": "798e91e4-7779-428b-967a-68921080363b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714202428000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fabdbbb8-17b1-48bb-850b-565d9e94985f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714202622400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45f702f6-b263-417f-b904-cab2e09bbe7e", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714211903200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb9053ce-3094-4f3e-95c7-5b9705047b79", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714213516000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af6e603a-a4b7-4389-9235-17866fb4b5a8", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714215191700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26423eef-4fc4-4ee8-9f97-27410de7e77a", "name": "entry : default@GenerateMetadata cost memory 0.1040496826171875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714215387600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ddf57df-44a0-43a7-8134-d2d7c5d89011", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714211875800, "endTime": 144714215499200}, "additional": {"logType": "info", "children": [], "durationId": "ee5751c5-7ce1-4eb0-a115-f2acdf5006c3"}}, {"head": {"id": "825c8ca5-e45b-48e9-b0f0-40f24be9393d", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714223292100, "endTime": 144714224044500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "658caf46-69f8-48dd-80f3-2a2ad980667f", "logId": "d5ca8c9c-ec81-40a9-8c1a-6ed1b65985fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "658caf46-69f8-48dd-80f3-2a2ad980667f", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714220518700}, "additional": {"logType": "detail", "children": [], "durationId": "825c8ca5-e45b-48e9-b0f0-40f24be9393d"}}, {"head": {"id": "02ea2297-91a1-4366-89e6-e847717bb558", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714222855400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b56cb86-0e2e-48d2-9257-b83bf5a4e49b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714223051200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f49f9b6-ee1b-4c6a-9682-ab154e59f43b", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714223307300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "254552a6-6013-4822-b99a-a7498f263fca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714223501700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a681721-0532-4ee2-b73b-a3234df24060", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714223614300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dcdfdb0-e50b-4333-95a3-2521d1fd21be", "name": "entry : default@ConfigureCmake cost memory 0.0376434326171875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714223761200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61770d4e-d3ae-444d-9f34-017536c5dcfa", "name": "runTaskFromQueue task cost before running: 467 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714223911900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5ca8c9c-ec81-40a9-8c1a-6ed1b65985fd", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714223292100, "endTime": 144714224044500, "totalTime": 583900}, "additional": {"logType": "info", "children": [], "durationId": "825c8ca5-e45b-48e9-b0f0-40f24be9393d"}}, {"head": {"id": "5b1cd6a8-f3b6-4e35-85d3-2b22c082a697", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714230812000, "endTime": 144714234990300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "0732b551-7888-4fe4-88cb-b053679634fb", "logId": "d9245129-62ef-465b-a73c-8f1644a8781f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0732b551-7888-4fe4-88cb-b053679634fb", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714227171200}, "additional": {"logType": "detail", "children": [], "durationId": "5b1cd6a8-f3b6-4e35-85d3-2b22c082a697"}}, {"head": {"id": "e983046d-bb56-4ef0-b7d0-42b18623d055", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714229360400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96e3be47-9cb2-4ba3-ad1d-9f52557dbdb3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714229546100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "798e1e97-1b8e-4833-b087-2052fd732e66", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714230833100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d005e19-29ba-4a40-b818-03fdcdfd7977", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714234663800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c499abba-a98e-43bc-8caf-3cdfbdc1e9e2", "name": "entry : default@MergeProfile cost memory 0.118408203125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714234867000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9245129-62ef-465b-a73c-8f1644a8781f", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714230812000, "endTime": 144714234990300}, "additional": {"logType": "info", "children": [], "durationId": "5b1cd6a8-f3b6-4e35-85d3-2b22c082a697"}}, {"head": {"id": "9779f008-c0f9-4dab-a110-e0eb4f2605e5", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714241576900, "endTime": 144714246782100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b447100c-764a-4f44-8d8d-968cc83b24cd", "logId": "f5418257-7297-49a6-8a17-769531a6ed52"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b447100c-764a-4f44-8d8d-968cc83b24cd", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714238062000}, "additional": {"logType": "detail", "children": [], "durationId": "9779f008-c0f9-4dab-a110-e0eb4f2605e5"}}, {"head": {"id": "3298174a-292f-4635-9f14-29fed5b8448f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714240086500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ba3564f-41b1-4aa2-8890-444ab04ae2a8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714240240800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c010ed2f-fef8-4a72-bae4-d91920a7abd3", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714241590200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ee8ef50-6a6d-47c3-a30b-749701895660", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714243446400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb098c0f-6f31-44eb-8739-e2f6e1e3366b", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714246466000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d3b8e7d-0be1-4822-80f2-9e6294798892", "name": "entry : default@CreateBuildProfile cost memory 0.10929107666015625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714246654900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5418257-7297-49a6-8a17-769531a6ed52", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714241576900, "endTime": 144714246782100}, "additional": {"logType": "info", "children": [], "durationId": "9779f008-c0f9-4dab-a110-e0eb4f2605e5"}}, {"head": {"id": "73cc25de-98dd-4484-804e-58f1e49be1d9", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714253183000, "endTime": 144714254039000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "329e176f-5069-48e0-8693-b1cd914cd620", "logId": "6d6c6f4f-5fce-4526-86eb-c69024944327"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "329e176f-5069-48e0-8693-b1cd914cd620", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714249583900}, "additional": {"logType": "detail", "children": [], "durationId": "73cc25de-98dd-4484-804e-58f1e49be1d9"}}, {"head": {"id": "4e40e867-eeae-4c19-8364-690b89d4687e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714251726100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a024c82-1b0d-4778-9fbd-cecca07fdfdc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714251874200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27158ccd-f79c-47fb-a8d7-d52aac3c02c4", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714253197400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88ebd132-4aa2-47b9-915d-df145860af3a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714253381500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d42301bb-2b56-444d-8c7b-f524ff6dde84", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714253475900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5739bc2-8b92-4577-a020-1e671b5bd9ae", "name": "entry : default@PreCheckSyscap cost memory 0.04132080078125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714253783600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2fe4b89-26bc-4458-912b-831de7708f17", "name": "runTaskFromQueue task cost before running: 497 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714253933500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d6c6f4f-5fce-4526-86eb-c69024944327", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714253183000, "endTime": 144714254039000, "totalTime": 719800}, "additional": {"logType": "info", "children": [], "durationId": "73cc25de-98dd-4484-804e-58f1e49be1d9"}}, {"head": {"id": "086394db-342d-4525-9311-0f3209a19ef6", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714261237300, "endTime": 144714272326100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "33576731-8b93-4d3a-a52e-80a4f4191042", "logId": "aa7780d9-78a3-4774-8dc5-79e3561d7e86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33576731-8b93-4d3a-a52e-80a4f4191042", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714256782400}, "additional": {"logType": "detail", "children": [], "durationId": "086394db-342d-4525-9311-0f3209a19ef6"}}, {"head": {"id": "d1da9989-9dff-4c3c-b41e-573fa2c89cb6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714258629300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fe9b77d-6f88-4d19-9431-deeae621e41d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714258763100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30847acb-a3d5-4fd2-b840-aab8c7d7025a", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714261257700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "addf9e02-ee26-43ad-8d5c-38dc9a042232", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714270680300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "134fd1c6-9227-4bc0-88fb-1d825574800e", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714271990800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9da8e0f5-4dea-442e-9dac-f96c1f442e16", "name": "entry : default@GeneratePkgContextInfo cost memory 0.24063873291015625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714272189700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa7780d9-78a3-4774-8dc5-79e3561d7e86", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714261237300, "endTime": 144714272326100}, "additional": {"logType": "info", "children": [], "durationId": "086394db-342d-4525-9311-0f3209a19ef6"}}, {"head": {"id": "285c184a-4867-4eaa-ac7f-af20834783a2", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714287949900, "endTime": 144714291975100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "cbcf9cdb-a74a-478b-bc78-2798eac60e99", "logId": "255006fd-1ec0-497b-8b0c-b02707df06ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cbcf9cdb-a74a-478b-bc78-2798eac60e99", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714275418300}, "additional": {"logType": "detail", "children": [], "durationId": "285c184a-4867-4eaa-ac7f-af20834783a2"}}, {"head": {"id": "6e42a8ae-d189-40d3-b30a-e3ec55e8c41d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714277542000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5c3f3d1-75ee-4e89-9f83-dc1e1cdfcea6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714277720700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39489dbe-f125-47ee-898d-6f29090f4bed", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714287978600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21e48941-e610-4e45-a691-6416090ea0df", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714291272000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8ca8826-8bbf-4bdd-8125-a51195138b1e", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714291464500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cad915ae-faba-4195-88e1-868047ade6f4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714291597100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46756cb1-eb13-48df-a51b-ba543e46c51a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714291687600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "690a82e7-419c-49f9-9598-5be22c0f89b9", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1230316162109375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714291796000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86c407e6-11ee-490d-ac95-e110a4981c7d", "name": "runTaskFromQueue task cost before running: 535 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714291899600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "255006fd-1ec0-497b-8b0c-b02707df06ef", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714287949900, "endTime": 144714291975100, "totalTime": 3931300}, "additional": {"logType": "info", "children": [], "durationId": "285c184a-4867-4eaa-ac7f-af20834783a2"}}, {"head": {"id": "16fef530-9087-405f-b94f-adcfcd2a3473", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714301027100, "endTime": 144714301816300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "df5e5d6c-a308-4bd2-bdea-497e635a95dd", "logId": "cf29efd3-7c39-48e2-955c-7475e627893f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df5e5d6c-a308-4bd2-bdea-497e635a95dd", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714297428800}, "additional": {"logType": "detail", "children": [], "durationId": "16fef530-9087-405f-b94f-adcfcd2a3473"}}, {"head": {"id": "885c6398-24a4-455a-bace-76afcbada4de", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714299492200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f32b962a-ed4c-4338-8df0-ab6e9a288a55", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714299646800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82b0e27e-da1c-4128-8135-05d3d8c8dd12", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714301045300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3bf8c93-da2f-409a-85d4-3891268bf113", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714301262900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b7e1e01-69a2-465c-a5df-1ba76f19d02b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714301361500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25021684-920d-4679-99cb-25b700d410e0", "name": "entry : default@BuildNativeWithCmake cost memory 0.03900909423828125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714301489500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7b8f057-f159-438c-baff-6c6e519c2e2e", "name": "runTaskFromQueue task cost before running: 545 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714301650400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf29efd3-7c39-48e2-955c-7475e627893f", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714301027100, "endTime": 144714301816300, "totalTime": 590200}, "additional": {"logType": "info", "children": [], "durationId": "16fef530-9087-405f-b94f-adcfcd2a3473"}}, {"head": {"id": "fdd63bee-a2af-4c89-a1fc-f03591aa779e", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714309114300, "endTime": 144714318241800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1803921a-6e9e-4db8-809c-c4cf20100d7a", "logId": "f607202e-087b-4bd5-87a2-63e4aa05856a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1803921a-6e9e-4db8-809c-c4cf20100d7a", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714305056200}, "additional": {"logType": "detail", "children": [], "durationId": "fdd63bee-a2af-4c89-a1fc-f03591aa779e"}}, {"head": {"id": "9919f634-f525-4122-a801-dc95f744a689", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714307110600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b74329b-1cb1-4b8b-bec5-5b7a31abb9af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714307405300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2c9b260-c614-4bfb-bd55-5a56d5acbfd4", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714309137400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f275f52-2cd2-49ae-b081-a3bfea7a1bd4", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714317885300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebd5be02-e78f-4201-bcdb-20dda7e69a98", "name": "entry : default@MakePackInfo cost memory 0.40423583984375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714318103800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f607202e-087b-4bd5-87a2-63e4aa05856a", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714309114300, "endTime": 144714318241800}, "additional": {"logType": "info", "children": [], "durationId": "fdd63bee-a2af-4c89-a1fc-f03591aa779e"}}, {"head": {"id": "1d395ddd-9529-4250-bda0-cfc321ae520a", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714326651500, "endTime": 144714333362500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4b7080a3-1940-46f0-9519-5051806fd37e", "logId": "3b52a79e-2948-4f66-9e2d-52546188bd65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b7080a3-1940-46f0-9519-5051806fd37e", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714322313500}, "additional": {"logType": "detail", "children": [], "durationId": "1d395ddd-9529-4250-bda0-cfc321ae520a"}}, {"head": {"id": "6abc7546-71b2-45d5-8ba5-c68107338cb1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714324155300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c15e35fb-2b3d-46b8-b132-9ce548274d02", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714324342900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f4cda1f-9bcf-4d7c-b442-f4fb8e1f9e81", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714326667200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6c0d0c9-5db8-4918-8ad7-e77f72a1f917", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714326967900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee6ebc18-9732-4bfc-8188-301df2ccfda9", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714328528700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "502ef5a4-1057-47b5-abfe-1c65d70aa28d", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714333056700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d1f91f4-c827-4f5c-837c-c9373c663898", "name": "entry : default@SyscapTransform cost memory 0.15204620361328125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714333240200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b52a79e-2948-4f66-9e2d-52546188bd65", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714326651500, "endTime": 144714333362500}, "additional": {"logType": "info", "children": [], "durationId": "1d395ddd-9529-4250-bda0-cfc321ae520a"}}, {"head": {"id": "e89e2c3c-8a7e-4f3c-af69-3b6f4f62979d", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714341763600, "endTime": 144714345833800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "61c35b76-b76f-40d2-b0d3-a52db3ee9425", "logId": "b48a5874-e4ba-4649-8c03-eb2b048fb8f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61c35b76-b76f-40d2-b0d3-a52db3ee9425", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714336615000}, "additional": {"logType": "detail", "children": [], "durationId": "e89e2c3c-8a7e-4f3c-af69-3b6f4f62979d"}}, {"head": {"id": "52707dd9-2b14-42ab-ac72-1ab0ca346d2f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714339145300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99b6d4e2-f8b0-475d-8b1b-31cd7f9c06e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714339359900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f244bc5-9130-4486-9ab1-cb59743880bb", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714341781900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "569e89d4-88db-4ec4-ad5b-a12e8b66dce5", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714345539600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23ef28de-592e-4e24-a48c-da3d89e5760e", "name": "entry : default@ProcessProfile cost memory 0.12731170654296875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714345717000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b48a5874-e4ba-4649-8c03-eb2b048fb8f5", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714341763600, "endTime": 144714345833800}, "additional": {"logType": "info", "children": [], "durationId": "e89e2c3c-8a7e-4f3c-af69-3b6f4f62979d"}}, {"head": {"id": "dbc68051-b44b-483b-b5ab-a20004d0a361", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714354349000, "endTime": 144714366726500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fb6df1f0-7dcd-4366-9747-43de5233d7d7", "logId": "99d0dc67-7569-4a01-a46d-2ae9dfaa577f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb6df1f0-7dcd-4366-9747-43de5233d7d7", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714348794100}, "additional": {"logType": "detail", "children": [], "durationId": "dbc68051-b44b-483b-b5ab-a20004d0a361"}}, {"head": {"id": "ca3d46a9-f7e0-4dc6-91fe-e60ca567fb52", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714350812700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42fc46ea-fd7a-466c-9338-97e23a5426e5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714350968000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2587bafc-1ae9-4de5-9782-8982fcb70a01", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714354369200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aad78a65-35ea-4ac6-bbc2-a44761a8d43a", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714366409400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c6ae0d5-a22c-4232-b9a9-d235f58ba3c6", "name": "entry : default@ProcessRouterMap cost memory 0.23766326904296875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714366611000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99d0dc67-7569-4a01-a46d-2ae9dfaa577f", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714354349000, "endTime": 144714366726500}, "additional": {"logType": "info", "children": [], "durationId": "dbc68051-b44b-483b-b5ab-a20004d0a361"}}, {"head": {"id": "e0b6c969-9cd2-4637-ba40-a61b874df731", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714374060100, "endTime": 144714385408100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "5fdaaf1a-aa4b-4154-8aad-c310d4f46034", "logId": "395142e4-22c5-400d-898a-49638e021e9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5fdaaf1a-aa4b-4154-8aad-c310d4f46034", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714372153300}, "additional": {"logType": "detail", "children": [], "durationId": "e0b6c969-9cd2-4637-ba40-a61b874df731"}}, {"head": {"id": "d5e60cc5-73ca-42d4-9973-6736e0db6197", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714373717900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3d937bd-cea1-4d5a-ae3a-64a3734b2c65", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714373897800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "564c72e6-f78d-4fb2-85bd-9b5aaacbbb39", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714374073800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a01c402-080c-437e-bdff-69234f08a0c9", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714374257500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebccc372-ce61-49b4-b9dc-3374a40981f4", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714382654400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfdc26b9-70b7-4416-ba59-4704a3508991", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714382880900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08cc7176-1514-445c-8a53-744aed6e7840", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714383034800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93b251f9-06a9-4934-984b-2fcc79d26c76", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714383147800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f70b00b5-8fe8-4db1-a677-bab1c3f9224e", "name": "entry : default@ProcessStartupConfig cost memory 0.26493072509765625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714385105600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28f2249d-ceb9-4af4-9486-9e3cb9bab1ca", "name": "runTaskFromQueue task cost before running: 628 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714385301100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "395142e4-22c5-400d-898a-49638e021e9c", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714374060100, "endTime": 144714385408100, "totalTime": 11198600}, "additional": {"logType": "info", "children": [], "durationId": "e0b6c969-9cd2-4637-ba40-a61b874df731"}}, {"head": {"id": "8ecfe178-637d-49f6-9be6-0c3b21e3c798", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714394392100, "endTime": 144714396762700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "9e88c897-4339-4d3a-9f26-643ea47a8837", "logId": "328d446d-6fe5-45d0-8673-013b5e90f90a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e88c897-4339-4d3a-9f26-643ea47a8837", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714390728800}, "additional": {"logType": "detail", "children": [], "durationId": "8ecfe178-637d-49f6-9be6-0c3b21e3c798"}}, {"head": {"id": "94529bc9-3253-4383-92c1-ec0e1a98ccf6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714392850200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42dfe6ab-2abc-4de4-bfd3-c3917497788f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714393009800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3399de2-e915-495c-b786-8588deb4112b", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714394443100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b0ed8cf-b579-4863-b72e-a6c5cd4fe7ef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714394640100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87b00718-0f76-4fa3-993d-5976612c9789", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714394752100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fe0db9a-0b0f-4c89-8d43-9644916f7c8d", "name": "entry : default@BuildNativeWithNinja cost memory 0.059722900390625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714396459400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82af7074-bdbb-4294-badf-1da088b199c1", "name": "runTaskFromQueue task cost before running: 640 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714396652700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "328d446d-6fe5-45d0-8673-013b5e90f90a", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714394392100, "endTime": 144714396762700, "totalTime": 2225000}, "additional": {"logType": "info", "children": [], "durationId": "8ecfe178-637d-49f6-9be6-0c3b21e3c798"}}, {"head": {"id": "cc12ea55-b345-4468-a8f3-e065b61838d9", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714408600700, "endTime": 144714420412100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "a92552b2-d475-4e7c-8ee6-73dac33be48a", "logId": "e07343f6-cde0-4bb6-8292-bf25b250ec9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a92552b2-d475-4e7c-8ee6-73dac33be48a", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714399702900}, "additional": {"logType": "detail", "children": [], "durationId": "cc12ea55-b345-4468-a8f3-e065b61838d9"}}, {"head": {"id": "8a67ae7b-2932-4fbd-8dae-9b36d1188344", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714401777300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "539e37e4-747c-489a-a548-e9919a9b10f2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714401929600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5348193-13c4-4ec6-874a-51db50d02097", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714404075800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c421663-4078-431a-9767-5c41a6249c1f", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714411865300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfdc7892-58ab-4296-a98b-b9b49fb620f0", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714416452100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b531ec6e-03fe-4dad-8da5-88f55c77d5f1", "name": "entry : default@ProcessResource cost memory 0.16689300537109375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714416660500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e07343f6-cde0-4bb6-8292-bf25b250ec9e", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714408600700, "endTime": 144714420412100}, "additional": {"logType": "info", "children": [], "durationId": "cc12ea55-b345-4468-a8f3-e065b61838d9"}}, {"head": {"id": "dd120536-2b2e-4535-ad64-ac0c3f604936", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714447267000, "endTime": 144714492959000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e9c5c55a-4ede-4126-a14e-7bb4a4ec505c", "logId": "be24b8e3-dc51-4fb2-92f3-327c697d0299"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9c5c55a-4ede-4126-a14e-7bb4a4ec505c", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714428294800}, "additional": {"logType": "detail", "children": [], "durationId": "dd120536-2b2e-4535-ad64-ac0c3f604936"}}, {"head": {"id": "46c8ba4f-6097-4932-898f-e4ed0a219ca6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714430564500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3edc7680-1df7-4e8a-988a-8a0eea586965", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714430725000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c97d08b-a5d2-4a6d-9e25-f532ff364a21", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714447290900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26554a8f-4d2b-4426-bfed-401aba73ece1", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 24 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714492589300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16147cbb-2a5d-4483-ba75-30e6a3d2f118", "name": "entry : default@GenerateLoaderJson cost memory 1.0781707763671875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714492864300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be24b8e3-dc51-4fb2-92f3-327c697d0299", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714447267000, "endTime": 144714492959000}, "additional": {"logType": "info", "children": [], "durationId": "dd120536-2b2e-4535-ad64-ac0c3f604936"}}, {"head": {"id": "68410a48-043f-4906-9af4-206fac0470c1", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714510896700, "endTime": 144714521807400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "e2b05a0e-3fe0-4ce9-b545-8814e12e7538", "logId": "8c82a4e6-463f-4c13-b6a2-4e432a5180b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2b05a0e-3fe0-4ce9-b545-8814e12e7538", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714506148100}, "additional": {"logType": "detail", "children": [], "durationId": "68410a48-043f-4906-9af4-206fac0470c1"}}, {"head": {"id": "34c177fe-c091-43b0-9432-de84ebe404a5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714508945800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1139384b-1f7e-466d-849d-9b2c677cf93d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714509123700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18f80e8b-8da0-4a61-8e76-144ed7fc0f74", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714510915300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce9f6215-6545-4ebd-aff8-b53cc8041c33", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714521472100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23fff7cd-7b56-4493-8d00-ec4f69505efc", "name": "entry : default@ProcessLibs cost memory 0.144744873046875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714521685200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c82a4e6-463f-4c13-b6a2-4e432a5180b4", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714510896700, "endTime": 144714521807400}, "additional": {"logType": "info", "children": [], "durationId": "68410a48-043f-4906-9af4-206fac0470c1"}}, {"head": {"id": "b3e12f8f-3169-4129-8d11-65064d1e4b08", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714537519100, "endTime": 144714594990200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "6e5e58ca-12fe-4601-88f6-701130fa0ff9", "logId": "ca5171a8-5088-4473-a010-a737b891358a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e5e58ca-12fe-4601-88f6-701130fa0ff9", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714526755000}, "additional": {"logType": "detail", "children": [], "durationId": "b3e12f8f-3169-4129-8d11-65064d1e4b08"}}, {"head": {"id": "b939d88a-83ca-48ff-872e-2383f518ac5d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714529189000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dffa4ca-a54c-4a93-8b55-3ce3fb68b102", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714529422200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccc85e82-0a35-4748-8d40-26403fb87da1", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714530748100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee83973-52e5-460e-9fd5-df88f6f93474", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714537729100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbc596f3-eb6d-4895-a130-f89a3aa27d68", "name": "Incremental task entry:default@CompileResource pre-execution cost: 54 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714594565000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "923f4dc0-de1d-4941-90c5-15064701cc4a", "name": "entry : default@CompileResource cost memory 1.3332061767578125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714594753600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca5171a8-5088-4473-a010-a737b891358a", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714537519100, "endTime": 144714594990200}, "additional": {"logType": "info", "children": [], "durationId": "b3e12f8f-3169-4129-8d11-65064d1e4b08"}}, {"head": {"id": "41bed996-4cfb-4930-b640-ffdcb3d0a5e1", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714603704100, "endTime": 144714607005800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "921a9133-fb48-487f-9df4-9d22ef60afde", "logId": "5e06368b-7522-4dd2-8f16-3bd6c70909f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "921a9133-fb48-487f-9df4-9d22ef60afde", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714598701500}, "additional": {"logType": "detail", "children": [], "durationId": "41bed996-4cfb-4930-b640-ffdcb3d0a5e1"}}, {"head": {"id": "bf4ac735-974e-486e-a77d-3206e80dfd6b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714599992400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13ea0e58-4be8-4578-87ed-5b968634be30", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714600121700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e42cff66-ab4d-4a86-914a-b0475195627c", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714603725600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7206ae3f-ef79-49eb-8b30-bfe1e9debb8f", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714604412000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58ae6af5-645f-4bb8-80ce-487c769a67d3", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714606678500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5ffeb73-4d32-4cee-aacf-89589e7a987f", "name": "entry : default@DoNativeStrip cost memory 0.0817413330078125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714606886500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e06368b-7522-4dd2-8f16-3bd6c70909f0", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714603704100, "endTime": 144714607005800}, "additional": {"logType": "info", "children": [], "durationId": "41bed996-4cfb-4930-b640-ffdcb3d0a5e1"}}, {"head": {"id": "4a07e8ee-03db-4583-8e20-c4581a0a54cc", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714617825100, "endTime": 144724342662500}, "additional": {"children": ["f13b6c0f-a0a8-4417-958d-6c1de3584052", "63283f21-4468-4f03-ada3-782c937607ea"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "050123aa-7a20-4ab6-be42-a0bb61fa9d9b", "logId": "c4f02167-bfdd-4c5f-9bc8-68329bf9a70f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "050123aa-7a20-4ab6-be42-a0bb61fa9d9b", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714609540900}, "additional": {"logType": "detail", "children": [], "durationId": "4a07e8ee-03db-4583-8e20-c4581a0a54cc"}}, {"head": {"id": "311f0c0c-2670-474a-82bc-2f9a77299c44", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714610982700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "986ed4a3-23e1-4d87-8257-398312e2621b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714611146700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ff70774-8557-4508-91cf-28e3d73ffed1", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714617852600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1fd4cc4-da0a-4e4a-a599-4b20a18f984a", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714618189400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a906aa38-63a3-4674-852a-a72d0ff99de3", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714650309300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a123d3ad-2fda-4a91-9052-e15ded3a6c08", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714650567200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db59b200-5715-4b5b-a460-3ab7359d70a2", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714672932000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65a0dd2b-1337-457b-a9de-dd6dee6a5ba1", "name": "default@CompileArkTS work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714675420300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f13b6c0f-a0a8-4417-958d-6c1de3584052", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144717222510800, "endTime": 144724335852100}, "additional": {"children": ["082cbbf7-7d6a-4776-a05e-cc57aba6586e", "4a0f9538-1f80-48a7-ad15-dc5c1945fa21", "ce654729-c96e-417c-8dec-7d3e9c8b4355", "7a1e3dc1-1f08-42d7-974e-69a12f8336d2", "4cd1d37b-81b1-499c-8e98-04ecedfd19e5", "33b97e86-a134-4bd2-a806-6c7f28343027", "9f263fff-7f95-415d-b2a0-08b8833fa38f"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "4a07e8ee-03db-4583-8e20-c4581a0a54cc", "logId": "fee1762b-92c3-435c-9962-a5a0bb02e23b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e43cf9f-acbd-4071-a86d-454d0d284388", "name": "default@CompileArkTS work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714676773500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0001ea4f-7554-4890-ad3b-c20a207c3987", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677026700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1841c054-05a3-4611-be80-4b542d3694bb", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677094400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6a5f662-3693-4256-94b9-236c162a42dd", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677133900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6099321-2827-4195-8b02-fc07b789e90d", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677178100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c37a01fe-c57d-4a74-8602-e996f5b285a6", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677205000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf439d16-acdd-464a-a9fa-05dd8064cec1", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677231100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c0ae9a0-1648-4a5d-9bd6-b8360e9d169e", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677258000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10da52fa-0dd1-4e82-ad94-b89ac5b86ec5", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677283600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66ff3fa1-cd6d-43dd-b6a0-7c7580c7dc6f", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677310200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4308e8f7-4bc0-472a-94a1-2fbe824bd7cb", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677337500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aa6d4df-e74e-47e9-906d-261d2a61b5e6", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677363200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a603df04-4f4b-44f0-9bb6-fc88002e28fe", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677395500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66341aa6-4d61-46b5-a916-93f270a77442", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677422200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1da1636-3239-4b3d-a87d-e4dd5005ce06", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677447400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "543400a1-cc0a-463c-85e3-f102e1ce9bd3", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677472300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57f007c4-900d-4463-90af-02cc06e36323", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714677609300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6491c44c-af0f-45e0-b5cc-692a45567a15", "name": "default@CompileArkTS work[0] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714678724800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0386fdeb-b71e-4e28-9bc2-e3ca7e5a8bbd", "name": "default@CompileArkTS work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714678878700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64f2aa0f-634b-4ab1-8bb3-b3c0200880c7", "name": "CopyResources startTime: 144714679003500", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714679008100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33ac08b3-b6e4-4f25-a78c-32b310c1741e", "name": "default@CompileArkTS work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714679084900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63283f21-4468-4f03-ada3-782c937607ea", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker18", "startTime": 144715688748400, "endTime": 144715701279700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "4a07e8ee-03db-4583-8e20-c4581a0a54cc", "logId": "368bfec9-f892-414c-bd7c-b7738f537b4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7af2ae83-0021-4099-998d-33bb1ac7ff39", "name": "default@CompileArkTS work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714680362300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8cb0d7e-247d-4d64-a2a8-980873fca0f2", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714680476900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb6d26f3-126a-4173-9ba3-bf1b51ee17a3", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714680534200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f223cbc-3e35-40c4-8f07-947862aa29c9", "name": "default@CompileArkTS work[1] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714681275900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e7e7a23-631b-4781-912a-0624cb605f45", "name": "default@CompileArkTS work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714681362500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82e3a66c-86fe-451e-9714-8d7e458ed3be", "name": "entry : default@CompileArkTS cost memory 2.4376449584960938", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714681519000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8590d006-5dac-420f-a050-4673d23d07f1", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714689890000, "endTime": 144714699411400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "8d65f85a-26f9-418c-b0ed-e537841c81b3", "logId": "833892a2-d6ec-4106-b236-b8d0ee75e1d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d65f85a-26f9-418c-b0ed-e537841c81b3", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714683339600}, "additional": {"logType": "detail", "children": [], "durationId": "8590d006-5dac-420f-a050-4673d23d07f1"}}, {"head": {"id": "31d57037-5b7a-48ab-9814-d0c0790f204e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714684332200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02496ded-9c72-4ec6-a75b-46ad0066fcfd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714684493100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fec788f-706e-4827-99ef-79f98991e014", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714689904800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6548b25-de91-455d-aca2-a54c3b4beac4", "name": "entry : default@BuildJS cost memory 0.34876251220703125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714699182900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "568b2d4a-42fc-4325-9517-6b708b55144d", "name": "runTaskFromQueue task cost before running: 943 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714699349800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "833892a2-d6ec-4106-b236-b8d0ee75e1d2", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714689890000, "endTime": 144714699411400, "totalTime": 9431500}, "additional": {"logType": "info", "children": [], "durationId": "8590d006-5dac-420f-a050-4673d23d07f1"}}, {"head": {"id": "852f0158-2653-49d9-ba57-69c9d1ffffd6", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714707600100, "endTime": 144714710971800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e292d79e-8e4e-4c4c-8f22-c9f194f88be1", "logId": "2e80396b-d661-4ea0-a2cf-d84506695098"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e292d79e-8e4e-4c4c-8f22-c9f194f88be1", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714701435400}, "additional": {"logType": "detail", "children": [], "durationId": "852f0158-2653-49d9-ba57-69c9d1ffffd6"}}, {"head": {"id": "157a94ae-12ff-4cdc-9903-0509380d936e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714702808100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "879903fd-0a8b-43bd-b5cb-283c40153f58", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714702941600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ce1c268-9fbe-4e3b-b42e-eef78f221d5d", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714707615700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cd57180-1a09-4524-9d78-eea7975b68ed", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714708556400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "098b5098-842a-493a-9524-1ea46dbc8878", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714710762200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "381d2209-f2d6-4cb5-86f0-e0fa0e059dfa", "name": "entry : default@CacheNativeLibs cost memory 0.1002044677734375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714710898000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e80396b-d661-4ea0-a2cf-d84506695098", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714707600100, "endTime": 144714710971800}, "additional": {"logType": "info", "children": [], "durationId": "852f0158-2653-49d9-ba57-69c9d1ffffd6"}}, {"head": {"id": "8a9b00f9-3245-4157-b03c-342e70af860d", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144715702233500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d170320-1acb-4d83-a561-23426ab69496", "name": "CopyResources is end, endTime: 144715702801900", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144715702815700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01c98bff-46fa-4676-8fd5-381b107af4fb", "name": "default@CompileArkTS work[1] done.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144715703200400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "368bfec9-f892-414c-bd7c-b7738f537b4a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Worker18", "startTime": 144715688748400, "endTime": 144715701279700}, "additional": {"logType": "info", "children": [], "durationId": "63283f21-4468-4f03-ada3-782c937607ea", "parent": "c4f02167-bfdd-4c5f-9bc8-68329bf9a70f"}}, {"head": {"id": "55968e58-aa82-40dc-b27f-c30e560b8a40", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144715703480600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a1408fd-9001-4f85-a208-f33cf6cd0807", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724336162000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "082cbbf7-7d6a-4776-a05e-cc57aba6586e", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144717223420200, "endTime": 144718206151800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f13b6c0f-a0a8-4417-958d-6c1de3584052", "logId": "8c2c9c82-0f68-49d8-8787-c9220a46f929"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c2c9c82-0f68-49d8-8787-c9220a46f929", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144717223420200, "endTime": 144718206151800}, "additional": {"logType": "info", "children": [], "durationId": "082cbbf7-7d6a-4776-a05e-cc57aba6586e", "parent": "fee1762b-92c3-435c-9962-a5a0bb02e23b"}}, {"head": {"id": "4a0f9538-1f80-48a7-ad15-dc5c1945fa21", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144718208654500, "endTime": 144718269734000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f13b6c0f-a0a8-4417-958d-6c1de3584052", "logId": "a2840414-e941-4520-99eb-b90d71c3eb38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2840414-e941-4520-99eb-b90d71c3eb38", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144718208654500, "endTime": 144718269734000}, "additional": {"logType": "info", "children": [], "durationId": "4a0f9538-1f80-48a7-ad15-dc5c1945fa21", "parent": "fee1762b-92c3-435c-9962-a5a0bb02e23b"}}, {"head": {"id": "ce654729-c96e-417c-8dec-7d3e9c8b4355", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144718269853900, "endTime": 144718270128000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f13b6c0f-a0a8-4417-958d-6c1de3584052", "logId": "d52c8a73-9f38-42f3-b287-1b220e0ce177"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d52c8a73-9f38-42f3-b287-1b220e0ce177", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144718269853900, "endTime": 144718270128000}, "additional": {"logType": "info", "children": [], "durationId": "ce654729-c96e-417c-8dec-7d3e9c8b4355", "parent": "fee1762b-92c3-435c-9962-a5a0bb02e23b"}}, {"head": {"id": "7a1e3dc1-1f08-42d7-974e-69a12f8336d2", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144718270208200, "endTime": 144724125195300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f13b6c0f-a0a8-4417-958d-6c1de3584052", "logId": "179bb09e-6c73-43d7-978d-d7015df6dee0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "179bb09e-6c73-43d7-978d-d7015df6dee0", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144718270208200, "endTime": 144724125195300}, "additional": {"logType": "info", "children": [], "durationId": "7a1e3dc1-1f08-42d7-974e-69a12f8336d2", "parent": "fee1762b-92c3-435c-9962-a5a0bb02e23b"}}, {"head": {"id": "4cd1d37b-81b1-499c-8e98-04ecedfd19e5", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144724125492700, "endTime": 144724136431600}, "additional": {"children": ["470eeae6-323f-41c5-9d26-ec4929155037", "7b88d1a8-caed-4342-a98f-5dff00063b1d", "8d1674be-06dc-457d-8150-4213cf679880"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f13b6c0f-a0a8-4417-958d-6c1de3584052", "logId": "b416a7a0-5528-4bbc-9304-ddfe74b33ecf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b416a7a0-5528-4bbc-9304-ddfe74b33ecf", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724125492700, "endTime": 144724136431600}, "additional": {"logType": "info", "children": ["cc9f2fb5-33d5-4432-9415-043451ab8d90", "0e03d30e-9a79-42c1-a5a2-4739c650a7fb", "a7a85a28-c3e3-4205-b6e2-578022174c72"], "durationId": "4cd1d37b-81b1-499c-8e98-04ecedfd19e5", "parent": "fee1762b-92c3-435c-9962-a5a0bb02e23b"}}, {"head": {"id": "470eeae6-323f-41c5-9d26-ec4929155037", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144724125675700, "endTime": 144724125694600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4cd1d37b-81b1-499c-8e98-04ecedfd19e5", "logId": "cc9f2fb5-33d5-4432-9415-043451ab8d90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc9f2fb5-33d5-4432-9415-043451ab8d90", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724125675700, "endTime": 144724125694600}, "additional": {"logType": "info", "children": [], "durationId": "470eeae6-323f-41c5-9d26-ec4929155037", "parent": "b416a7a0-5528-4bbc-9304-ddfe74b33ecf"}}, {"head": {"id": "7b88d1a8-caed-4342-a98f-5dff00063b1d", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144724125705500, "endTime": 144724131467000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4cd1d37b-81b1-499c-8e98-04ecedfd19e5", "logId": "0e03d30e-9a79-42c1-a5a2-4739c650a7fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e03d30e-9a79-42c1-a5a2-4739c650a7fb", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724125705500, "endTime": 144724131467000}, "additional": {"logType": "info", "children": [], "durationId": "7b88d1a8-caed-4342-a98f-5dff00063b1d", "parent": "b416a7a0-5528-4bbc-9304-ddfe74b33ecf"}}, {"head": {"id": "8d1674be-06dc-457d-8150-4213cf679880", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144724131475300, "endTime": 144724136256200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4cd1d37b-81b1-499c-8e98-04ecedfd19e5", "logId": "a7a85a28-c3e3-4205-b6e2-578022174c72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7a85a28-c3e3-4205-b6e2-578022174c72", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724131475300, "endTime": 144724136256200}, "additional": {"logType": "info", "children": [], "durationId": "8d1674be-06dc-457d-8150-4213cf679880", "parent": "b416a7a0-5528-4bbc-9304-ddfe74b33ecf"}}, {"head": {"id": "33b97e86-a134-4bd2-a806-6c7f28343027", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144724136486000, "endTime": 144724333395300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f13b6c0f-a0a8-4417-958d-6c1de3584052", "logId": "2aa2f1ed-ddaa-4dd5-8525-de4bda69ea79"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2aa2f1ed-ddaa-4dd5-8525-de4bda69ea79", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724136486000, "endTime": 144724333395300}, "additional": {"logType": "info", "children": [], "durationId": "33b97e86-a134-4bd2-a806-6c7f28343027", "parent": "fee1762b-92c3-435c-9962-a5a0bb02e23b"}}, {"head": {"id": "9f263fff-7f95-415d-b2a0-08b8833fa38f", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144715635702500, "endTime": 144717221031000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f13b6c0f-a0a8-4417-958d-6c1de3584052", "logId": "03764caf-8cb9-409b-879b-51563a1b21a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03764caf-8cb9-409b-879b-51563a1b21a1", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144715635702500, "endTime": 144717221031000}, "additional": {"logType": "info", "children": [], "durationId": "9f263fff-7f95-415d-b2a0-08b8833fa38f", "parent": "fee1762b-92c3-435c-9962-a5a0bb02e23b"}}, {"head": {"id": "a6589568-829b-4f36-8a71-aaddf8a3eba5", "name": "default@CompileArkTS work[0] done.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724342198500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fee1762b-92c3-435c-9962-a5a0bb02e23b", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144717222510800, "endTime": 144724335852100}, "additional": {"logType": "info", "children": ["8c2c9c82-0f68-49d8-8787-c9220a46f929", "a2840414-e941-4520-99eb-b90d71c3eb38", "d52c8a73-9f38-42f3-b287-1b220e0ce177", "179bb09e-6c73-43d7-978d-d7015df6dee0", "b416a7a0-5528-4bbc-9304-ddfe74b33ecf", "2aa2f1ed-ddaa-4dd5-8525-de4bda69ea79", "03764caf-8cb9-409b-879b-51563a1b21a1"], "durationId": "f13b6c0f-a0a8-4417-958d-6c1de3584052", "parent": "c4f02167-bfdd-4c5f-9bc8-68329bf9a70f"}}, {"head": {"id": "a406abee-2758-48d3-99f7-f14fd08a3750", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724342516700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4f02167-bfdd-4c5f-9bc8-68329bf9a70f", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144714617825100, "endTime": 144724342662500, "totalTime": 7189627500}, "additional": {"logType": "info", "children": ["fee1762b-92c3-435c-9962-a5a0bb02e23b", "368bfec9-f892-414c-bd7c-b7738f537b4a"], "durationId": "4a07e8ee-03db-4583-8e20-c4581a0a54cc"}}, {"head": {"id": "0ee7fc2c-dbfd-4b49-8d7d-af2cf0079505", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724350117600, "endTime": 144724351705000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "93e01a3f-e9b4-4c55-834f-bcae76bc5124", "logId": "2f579c48-71c6-45c1-8687-d5beee395385"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93e01a3f-e9b4-4c55-834f-bcae76bc5124", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724348107400}, "additional": {"logType": "detail", "children": [], "durationId": "0ee7fc2c-dbfd-4b49-8d7d-af2cf0079505"}}, {"head": {"id": "ff3371b8-f6a7-4a33-85ff-3c4bf387699a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724349072900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efa902e9-51b1-49d5-a4d8-9ebcfa985b85", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724349182800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa114c4c-d81c-4350-a6b8-45aa7bcb18dc", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724350127500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cf94f23-f67e-4bcf-ae8e-d90101c193fa", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724350450900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45aae13f-0084-40ed-884a-5d1496a6dfe9", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724351519400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ebf940e-7e20-4331-8704-8c696930f17a", "name": "entry : default@GeneratePkgModuleJson cost memory 0.116058349609375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724351634400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f579c48-71c6-45c1-8687-d5beee395385", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724350117600, "endTime": 144724351705000}, "additional": {"logType": "info", "children": [], "durationId": "0ee7fc2c-dbfd-4b49-8d7d-af2cf0079505"}}, {"head": {"id": "bf2c21b2-d26e-41a8-8faf-0f75ebe2510c", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724362870100, "endTime": 144724503826500}, "additional": {"children": ["9e468dbb-8585-43dc-9d09-fea8bcdcdcb2", "f4885c38-f998-4652-aee9-5de5a627cd9e"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "c81c7e82-a083-4505-85f0-f358ac435386", "logId": "82a950bc-3641-4992-bdf2-c6014aa55525"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c81c7e82-a083-4505-85f0-f358ac435386", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724354089400}, "additional": {"logType": "detail", "children": [], "durationId": "bf2c21b2-d26e-41a8-8faf-0f75ebe2510c"}}, {"head": {"id": "578fb268-9c1b-4c58-9a06-c5d42bed7ddb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724355039200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1597bd00-f832-4fed-adf8-c18f1b3e4e6f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724355130300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7481eb90-cdbc-427a-901f-29a4b7dfb610", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724362885300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "976fd2b2-23e7-4539-9ec9-bff1115ee4dd", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724378734100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa8f5cd9-92d8-49cc-9fbf-e2380a06444f", "name": "Incremental task entry:default@PackageHap pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724378921100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afe1d497-981b-4454-851b-93a4646a7e6f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724379024500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "809a3ebb-87af-48ba-b0b8-a1dfc14eeda1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724379062300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e468dbb-8585-43dc-9d09-fea8bcdcdcb2", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724380538200, "endTime": 144724382656700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bf2c21b2-d26e-41a8-8faf-0f75ebe2510c", "logId": "4bd76b59-7eb2-446a-ad77-cb682c282637"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4feb30e1-29d3-499d-abe0-1db1500a0450", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724382490700}, "additional": {"logType": "debug", "children": [], "durationId": "bf2c21b2-d26e-41a8-8faf-0f75ebe2510c"}}, {"head": {"id": "4bd76b59-7eb2-446a-ad77-cb682c282637", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724380538200, "endTime": 144724382656700}, "additional": {"logType": "info", "children": [], "durationId": "9e468dbb-8585-43dc-9d09-fea8bcdcdcb2", "parent": "82a950bc-3641-4992-bdf2-c6014aa55525"}}, {"head": {"id": "f4885c38-f998-4652-aee9-5de5a627cd9e", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724383294500, "endTime": 144724498037800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bf2c21b2-d26e-41a8-8faf-0f75ebe2510c", "logId": "90ba83b0-74cf-409b-8c56-dca4d49d1a91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7a38b65-ff3f-42f5-993f-d2253c456d00", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724497229800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90ba83b0-74cf-409b-8c56-dca4d49d1a91", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724383294500, "endTime": 144724498029400}, "additional": {"logType": "info", "children": [], "durationId": "f4885c38-f998-4652-aee9-5de5a627cd9e", "parent": "82a950bc-3641-4992-bdf2-c6014aa55525"}}, {"head": {"id": "2bed71ef-931c-42b3-b270-bb5d8a836e24", "name": "entry : default@PackageHap cost memory 1.6158676147460938", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724503552200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c24743c6-800d-41a9-949b-916ad510176b", "name": "runTaskFromQueue task cost before running: 10 s 747 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724503755800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82a950bc-3641-4992-bdf2-c6014aa55525", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724362870100, "endTime": 144724503826500, "totalTime": 140847000}, "additional": {"logType": "info", "children": ["4bd76b59-7eb2-446a-ad77-cb682c282637", "90ba83b0-74cf-409b-8c56-dca4d49d1a91"], "durationId": "bf2c21b2-d26e-41a8-8faf-0f75ebe2510c"}}, {"head": {"id": "f6127c2c-0e49-477b-b647-901e3c6557bd", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724511395800, "endTime": 144724801526100}, "additional": {"children": ["422610b7-aa70-4754-aa20-59cff6e0c755", "950de4b1-4ea7-4bd8-91fd-3cc20bfcc058"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "c5b54b8e-0dee-4b41-9af2-754b9d81d0a4", "logId": "a4e3340d-5eb5-4063-a7e7-cd520d52211a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5b54b8e-0dee-4b41-9af2-754b9d81d0a4", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724507601200}, "additional": {"logType": "detail", "children": [], "durationId": "f6127c2c-0e49-477b-b647-901e3c6557bd"}}, {"head": {"id": "7d630d99-806f-4b5e-af2b-7f2dfafcee88", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724508662400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ccbd6ae-90d4-4a56-836c-dc7a7e940dd3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724508776100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "489df004-964e-43d0-82e9-d6daafb837e3", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724511410300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c268e665-642e-4e8d-a648-1cd62acc2052", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724513503000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dc73f99-6c9e-41fb-894b-984d3cacab04", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724513628600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4b73083-2786-475c-aae7-99eecd12f522", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724513704700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22f6bbea-4205-4806-b738-c78d3b603d1f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724513748900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "422610b7-aa70-4754-aa20-59cff6e0c755", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724515734100, "endTime": 144724593702000}, "additional": {"children": ["9fe85c7c-84cf-4781-abc7-3b391d2fd6b0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f6127c2c-0e49-477b-b647-901e3c6557bd", "logId": "f9016368-8795-4df5-b110-3f37601fc23d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fe85c7c-84cf-4781-abc7-3b391d2fd6b0", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724534039500, "endTime": 144724592509300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "422610b7-aa70-4754-aa20-59cff6e0c755", "logId": "14173ce3-e7e5-4061-a15b-ec6e28b06e47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b7879c4-cd6b-4574-bcaf-75b88314fd41", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724537438900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c0109fd-6471-47c1-924c-63035d2b80ef", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724592023300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14173ce3-e7e5-4061-a15b-ec6e28b06e47", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724534039500, "endTime": 144724592509300}, "additional": {"logType": "info", "children": [], "durationId": "9fe85c7c-84cf-4781-abc7-3b391d2fd6b0", "parent": "f9016368-8795-4df5-b110-3f37601fc23d"}}, {"head": {"id": "f9016368-8795-4df5-b110-3f37601fc23d", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724515734100, "endTime": 144724593702000}, "additional": {"logType": "info", "children": ["14173ce3-e7e5-4061-a15b-ec6e28b06e47"], "durationId": "422610b7-aa70-4754-aa20-59cff6e0c755", "parent": "a4e3340d-5eb5-4063-a7e7-cd520d52211a"}}, {"head": {"id": "950de4b1-4ea7-4bd8-91fd-3cc20bfcc058", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724594375400, "endTime": 144724800982700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f6127c2c-0e49-477b-b647-901e3c6557bd", "logId": "5792aceb-7135-48b6-87a9-2f1a8b903cf5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef45b297-1b2b-478e-a406-49abeed9f6c7", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724596239000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "497b27e2-50e7-4ee5-a012-916ab8bd6d55", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724800463300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5792aceb-7135-48b6-87a9-2f1a8b903cf5", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724594375400, "endTime": 144724800982700}, "additional": {"logType": "info", "children": [], "durationId": "950de4b1-4ea7-4bd8-91fd-3cc20bfcc058", "parent": "a4e3340d-5eb5-4063-a7e7-cd520d52211a"}}, {"head": {"id": "70a4f1d4-cdbb-4660-9cd9-207f6dba3aa0", "name": "entry : default@SignHap cost memory -9.488677978515625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724801306500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c83ec26-b06c-4a34-afc6-ef718291f8e1", "name": "runTaskFromQueue task cost before running: 11 s 45 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724801451600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4e3340d-5eb5-4063-a7e7-cd520d52211a", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724511395800, "endTime": 144724801526100, "totalTime": 290023300}, "additional": {"logType": "info", "children": ["f9016368-8795-4df5-b110-3f37601fc23d", "5792aceb-7135-48b6-87a9-2f1a8b903cf5"], "durationId": "f6127c2c-0e49-477b-b647-901e3c6557bd"}}, {"head": {"id": "0cd6429b-c779-4246-aa54-03154a5c6253", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724805931300, "endTime": 144724812262100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "dd5ddc84-3323-4b84-a9f1-04914c107423", "logId": "d488fcef-ac38-49b5-9de6-ba0e8104820f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd5ddc84-3323-4b84-a9f1-04914c107423", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724803825100}, "additional": {"logType": "detail", "children": [], "durationId": "0cd6429b-c779-4246-aa54-03154a5c6253"}}, {"head": {"id": "debb4617-e57d-496f-a5f2-8815f2ad0670", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724804915100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd3a53fa-1dea-4b9f-89ee-7ab4c23146b9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724805022800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5abdbcd-5339-4835-8304-837f3bbb3778", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724805944500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a8b985f-17e5-4c17-846d-46241cc5e174", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724811895800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a0fee92-5ce7-4813-95de-80de6de3efd2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724812029800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dae4bcbd-8103-4922-9787-79b861d899ac", "name": "entry : default@CollectDebugSymbol cost memory 0.25148773193359375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724812141500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d5a2212-667e-44df-8121-31575710d3d4", "name": "runTaskFromQueue task cost before running: 11 s 55 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724812221100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d488fcef-ac38-49b5-9de6-ba0e8104820f", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724805931300, "endTime": 144724812262100, "totalTime": 6268800}, "additional": {"logType": "info", "children": [], "durationId": "0cd6429b-c779-4246-aa54-03154a5c6253"}}, {"head": {"id": "66b9e051-b18f-43f9-aa3d-b9aa2ef50a9f", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724813951000, "endTime": 144724814327800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d4dcbbec-0ee0-4926-8628-f514d2fb2470", "logId": "86c568fe-58ed-4226-a414-eb374469d729"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4dcbbec-0ee0-4926-8628-f514d2fb2470", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724813903900}, "additional": {"logType": "detail", "children": [], "durationId": "66b9e051-b18f-43f9-aa3d-b9aa2ef50a9f"}}, {"head": {"id": "9d7fa76d-62b0-45f7-aaee-71bb941d4e75", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724813959800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbcaeeb6-d3b2-47a4-bfa2-2d5a4efb8a20", "name": "entry : assembleHap cost memory 0.0117950439453125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724814182000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d23a00ec-4d99-4d62-a3cb-6a5ec7294084", "name": "runTaskFromQueue task cost before running: 11 s 57 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724814275500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86c568fe-58ed-4226-a414-eb374469d729", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724813951000, "endTime": 144724814327800, "totalTime": 300900}, "additional": {"logType": "info", "children": [], "durationId": "66b9e051-b18f-43f9-aa3d-b9aa2ef50a9f"}}, {"head": {"id": "f84c8d16-61f6-4055-81cb-3d58ac0a7627", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724828264300, "endTime": 144724828304100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "711bf056-9898-4aeb-9aab-62b7480db939", "logId": "be77037b-6b4b-45de-9770-36dbe6dde6d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be77037b-6b4b-45de-9770-36dbe6dde6d1", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724828264300, "endTime": 144724828304100}, "additional": {"logType": "info", "children": [], "durationId": "f84c8d16-61f6-4055-81cb-3d58ac0a7627"}}, {"head": {"id": "a330806d-d3e3-412d-acf6-0032f699ad65", "name": "BUILD SUCCESSFUL in 11 s 72 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724828429900}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "272f1993-02f6-4ba7-a671-213b602fc4cd", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144713757312000, "endTime": 144724828983700}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 9, "second": 14}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "78350b3a-c84e-4eb1-a6da-01fab1034a0f", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724829105200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "684d28e9-460d-4226-8bda-e0fbbe553eba", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724829287100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d5d6311-b0cf-4001-b67a-adb8526c3a8e", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724829847700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac760b8a-4f3e-4759-b449-f3d9e951a035", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724829949000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "953e5cc3-95ff-499d-bdab-b7188e42aa45", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724830096000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de4f134d-02dd-486e-be90-9670471ff8c3", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724830161500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b987e97-ec26-4795-8b99-aca1f0806017", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724830197300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba36bcda-6def-469e-b8a0-fe6facfada9b", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724830947900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb8a9f4b-e847-4169-a9dd-cee1063425c3", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724831243100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdc18349-a143-4657-a989-751d82a5eeb4", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724831310500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0916d47-3fdc-4a04-806b-a29f7dace266", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724831351300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0be89ae-ea2f-413c-8ba5-c5df9b66e6ff", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724831383000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f23fc085-f5df-4810-a836-5852a3ca7fc8", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724831416900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f9009c4-1213-47e1-b599-a6caf4922b4f", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724832732900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6bf1f48-f483-4cbd-85c7-b23b25da1cac", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724833073400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4edb74f1-6d99-4cd8-a166-5defef3d5f84", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724833486800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7275bdd1-3c30-45f6-9db1-5000944c4ace", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724833566700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ca4f990-50ba-4b9e-8f91-f45e62dc75d6", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724833605400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae9eff79-203f-48f4-8ef4-77c76d8f8457", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724833642400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "203da301-64e2-4b23-8be7-0580ebd71575", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724833670300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df1de9d9-2aa5-4455-a919-079a51e8a99d", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724833698300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c24be852-7459-42a2-bd5e-b28ce1239aa2", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724837314900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ba0122d-3047-4027-81d3-61d403b23101", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724838177000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "234d3aa1-82ea-4d0d-8439-b6a320eea409", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724838688700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "267e5833-adcc-4885-9c46-5cb5ac3a87e7", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724838986200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9486914b-b02d-47ab-aa61-55b687bd90df", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724839241200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1845709a-229a-4c42-8a72-c100c4fb3b3b", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724840151100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5468d49-983d-45c1-b745-be392cb28c72", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724840238000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1ccf6f0-542b-44df-b6b7-a5f1bba47ae8", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724840527000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61e0bb52-46c8-422d-b3bd-53f02d28022f", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724840938200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5d26a89-0fa1-4432-918b-86650a01cbc8", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724842058100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6494690-154d-4731-8c53-67849a64eb7a", "name": "Incremental task entry:default@CompileArkTS post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724843117200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5684385c-7215-4737-a531-adf01096d09a", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724845720900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d9cc886-b2fc-49df-9fda-59acf37bf89a", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724846555400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7450b0ec-8abc-4553-9936-59e577531cc8", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724847032900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31054616-828b-4ceb-be4c-1e28eb9ba2f8", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724847344500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "364fd211-fcb4-412e-b40d-3e3bf110d317", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724847621400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4813a83b-a467-405c-9479-ae121a4b4329", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724848516300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cf8d189-a085-4adb-ad34-3aeaea53b8a7", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724849596700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d87de4cf-ea4e-4528-bec2-a6d95adc2571", "name": "Incremental task entry:default@BuildJS post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724849925300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1055ac2c-01a0-4859-83d6-beee31e7e72d", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724850010400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75d66800-aa6d-49a2-b8dc-80e818f2056a", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724850115800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb29385c-a7b5-471f-8e41-9871c80a6be1", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724852075700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9825bef-10b1-4b61-b9e2-b1a6859b60a5", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724852743000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d454fe3-99aa-43ad-8da2-5b2d6e24125a", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724853079900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef9ded20-a190-48a0-af3a-07b9dfdc8943", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724861398600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b53a93b-68c1-44fc-b361-69037777705f", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724861736300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4f6b6be-c9c6-45a0-bf79-03d92cef38ce", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724862031400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81595f55-724f-4173-a91d-8ddf3dd01ea4", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724862297400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72ac51ff-feda-4f22-b1a2-3c174ac0f185", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724862373500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03d095d7-dddf-4f57-a1be-e93dd110507d", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724862604500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd4731f7-128a-49ca-aa6e-2ea63693593d", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724862848500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9c6cd83-26ab-4e50-85f8-ef4fa9c6c483", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724863915400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66f03f78-6453-434b-b157-bd74c9b1a493", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724864212200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78d68a27-0809-49bc-99ab-55b059a56a45", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724864456000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7581e19b-4948-4267-9d53-32849482df40", "name": "Incremental task entry:default@PackageHap post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724864771700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fd167b8-2fcb-4daf-ad2e-5333797e4e86", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724865230500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbceb1c2-a116-4f56-a5de-f607de485a98", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724865508600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8461bd6-0d3b-434d-9b89-741f233f21f7", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724865772500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af8887b2-fd18-4098-9cef-d27c0683ba50", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724866028800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de96ccd0-7a53-4243-9d6e-086d0cd53d29", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724866097100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "048243f9-bc94-469e-a84d-3dab99f5a10f", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724866405500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c11e3a7f-1f04-43d6-ba26-01aaa09b8888", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724869452700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "459346f6-d607-4c9a-a87f-d0c093671184", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724869777200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e08b3049-eb9e-4a9b-a510-6ba545167b14", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724870388300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54c7e0b0-029d-4d8b-ab32-811a23c85925", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724870681400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}