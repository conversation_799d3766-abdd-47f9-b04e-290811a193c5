"use strict";
import component from "./ja-JP/component";
import globalHeader from "./ja-JP/globalHeader";
import menu from "./ja-JP/menu";
import pages from "./ja-JP/pages";
import pwa from "./ja-JP/pwa";
import settingDrawer from "./ja-<PERSON>/settingDrawer";
import settings from "./ja-JP/settings";
export default {
  "navBar.lang": "\u8A00\u8A9E",
  "layout.user.link.help": "\u30D8\u30EB\u30D7",
  "layout.user.link.privacy": "\u30D7\u30E9\u30A4\u30D0\u30B7\u30FC",
  "layout.user.link.terms": "\u5229\u7528\u898F\u7D04",
  "app.preview.down.block": "\u3053\u306E\u30DA\u30FC\u30B8\u3092\u30ED\u30FC\u30AB\u30EB\u30D7\u30ED\u30B8\u30A7\u30AF\u30C8\u306B\u30C0\u30A6\u30F3\u30ED\u30FC\u30C9\u3057\u3066\u304F\u3060\u3055\u3044",
  "app.welcome.link.fetch-blocks": "",
  "app.welcome.link.block-list": "",
  ...globalHeader,
  ...menu,
  ...settingDrawer,
  ...settings,
  ...pwa,
  ...component,
  ...pages
};
