"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { Avatar, version, Dropdown, Menu, Spin } from "antd";
import { LogoutOutlined } from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons";
import { SelectLang } from "@@/plugin-locale";
export function getRightRenderContent(opts) {
  if (opts.runtimeConfig.rightRender) {
    return opts.runtimeConfig.rightRender(
      opts.initialState,
      opts.setInitialState,
      opts.runtimeConfig
    );
  }
  const showAvatar = opts.initialState?.avatar || opts.initialState?.name || opts.runtimeConfig.logout;
  const disableAvatarImg = opts.initialState?.avatar === false;
  const nameClassName = disableAvatarImg ? "umi-plugin-layout-name umi-plugin-layout-hide-avatar-img" : "umi-plugin-layout-name";
  const avatar = showAvatar ? /* @__PURE__ */ jsxs("span", { className: "umi-plugin-layout-action", children: [
    !disableAvatarImg ? /* @__PURE__ */ jsx(
      Avatar,
      {
        size: "small",
        className: "umi-plugin-layout-avatar",
        src: opts.initialState?.avatar || "https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png",
        alt: "avatar"
      }
    ) : null,
    /* @__PURE__ */ jsx("span", { className: nameClassName, children: opts.initialState?.name })
  ] }) : null;
  if (opts.loading) {
    return /* @__PURE__ */ jsx("div", { className: "umi-plugin-layout-right", children: /* @__PURE__ */ jsx(Spin, { size: "small", style: { marginLeft: 8, marginRight: 8 } }) });
  }
  const langMenu = {
    className: "umi-plugin-layout-menu",
    selectedKeys: [],
    items: [
      {
        key: "logout",
        label: /* @__PURE__ */ jsxs(Fragment, { children: [
          /* @__PURE__ */ jsx(LogoutOutlined, {}),
          "\u9000\u51FA\u767B\u5F55"
        ] }),
        onClick: () => {
          opts?.runtimeConfig?.logout?.(opts.initialState);
        }
      }
    ]
  };
  let dropdownProps;
  if (version.startsWith("5.") || version.startsWith("4.24.")) {
    dropdownProps = { menu: langMenu };
  } else if (version.startsWith("3.")) {
    dropdownProps = {
      overlay: /* @__PURE__ */ jsx(Menu, { children: langMenu.items.map((item) => /* @__PURE__ */ jsx(Menu.Item, { onClick: item.onClick, children: item.label }, item.key)) })
    };
  } else {
    dropdownProps = { overlay: /* @__PURE__ */ jsx(Menu, { ...langMenu }) };
  }
  return /* @__PURE__ */ jsxs("div", { className: "umi-plugin-layout-right anticon", children: [
    opts.runtimeConfig.logout ? /* @__PURE__ */ jsx(Dropdown, { ...dropdownProps, overlayClassName: "umi-plugin-layout-container", children: avatar }) : avatar,
    /* @__PURE__ */ jsx(SelectLang, {})
  ] });
}
