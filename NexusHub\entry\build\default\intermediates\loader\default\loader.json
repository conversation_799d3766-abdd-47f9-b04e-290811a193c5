{"modulePathMap": {"entry": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry"}, "compileMode": "esmodule", "projectRootPath": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub", "nodeModulesPath": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\node_modules", "byteCodeHarInfo": {}, "declarationEntry": [], "moduleName": "entry", "hspNameOhmMap": {}, "harNameOhmMap": {}, "packageManagerType": "ohpm", "compileEntry": [], "otherCompileFiles": [], "dynamicImportLibInfo": {}, "routerMap": [], "hspResourcesMap": {}, "updateVersionInfo": {}, "anBuildOutPut": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\an\\arm64-v8a", "anBuildMode": "type", "patchConfig": {"changedFileList": "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\patch\\default\\changedFileList.json"}}