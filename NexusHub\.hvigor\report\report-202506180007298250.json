{"version": "2.0", "ppid": 29880, "events": [{"head": {"id": "ec50a07e-6a60-4b76-89b1-a450f1415a4e", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240474533800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee3f8942-8936-4c0f-853f-5c6ab02a443b", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240475103800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52bbd5c3-c141-4bad-bc58-ad6812281177", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240477158900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f83c635-e760-4b85-a323-03672eeafb46", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240477552100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "376950d5-36ab-4baf-97f7-2e7b6b55330a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240479619500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6984369-f33a-43fd-8fc3-6452458801a2", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240479961900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96db8cbf-f9a4-4178-96a8-b8d647c7c4ce", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240480375500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "863dae49-5d09-43a9-88ea-1bec69b14ed0", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240518662100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cca9d982-80f8-412b-8da8-228bdaaac7a0", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820038883900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5eb75217-2689-49ee-9155-8848cd760bd4", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820055054600, "endTime": 151820538008300}, "additional": {"children": ["ce86a68d-34bc-4074-98c6-b56c81d9e8f3", "0cd7769b-335c-4ea3-8ec4-cc7156223d62", "f35d527a-3330-4653-9067-8e5e7a75aaca", "82dcfdd6-f1d7-4703-baf2-e9b4d7e627c2", "96c21b01-1aa2-4e44-81ec-18f9f65a9f3e", "bf6018f1-75ed-4fee-8977-cfd47975eafb", "eddc5e15-9809-4e45-8bb5-e4183dd44309"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "2b296cd0-440e-4704-901f-5bb5811884ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce86a68d-34bc-4074-98c6-b56c81d9e8f3", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820055056700, "endTime": 151820075885100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5eb75217-2689-49ee-9155-8848cd760bd4", "logId": "1309e077-7ee3-44b7-aaf9-02347b9d4de7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0cd7769b-335c-4ea3-8ec4-cc7156223d62", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820075902800, "endTime": 151820535652100}, "additional": {"children": ["652b9fa3-4f41-44c7-a44d-85a16589fea1", "4e7fbe84-6a03-4eea-990a-50ef856c3c12", "ab4b3372-27ec-4a9c-b766-161f122b5b81", "6a18f8c8-c59e-401a-8152-ebf9f2790481", "47076a50-5804-4436-b1f6-1d51e4a90832", "19f4c90a-e194-4340-9431-c58733dd99de", "7ee89cc6-49f8-46ac-abcc-6cd142d47543", "0b9865b2-233d-477d-854c-f5c231f34c99", "1e1ccbbd-89b7-40f7-ae5a-9b7debb1a849"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5eb75217-2689-49ee-9155-8848cd760bd4", "logId": "b72037ca-8396-4f50-b363-573a06a5406e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f35d527a-3330-4653-9067-8e5e7a75aaca", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820535690300, "endTime": 151820537943700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5eb75217-2689-49ee-9155-8848cd760bd4", "logId": "35fadb49-f585-4645-9e91-9cb2ae79d12f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82dcfdd6-f1d7-4703-baf2-e9b4d7e627c2", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820537953900, "endTime": 151820537994400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5eb75217-2689-49ee-9155-8848cd760bd4", "logId": "931c44bd-dfb1-447b-a911-7c0395b84847"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96c21b01-1aa2-4e44-81ec-18f9f65a9f3e", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820060343000, "endTime": 151820060404900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5eb75217-2689-49ee-9155-8848cd760bd4", "logId": "3b748981-5f21-4351-9879-2cbef8fc7559"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b748981-5f21-4351-9879-2cbef8fc7559", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820060343000, "endTime": 151820060404900}, "additional": {"logType": "info", "children": [], "durationId": "96c21b01-1aa2-4e44-81ec-18f9f65a9f3e", "parent": "2b296cd0-440e-4704-901f-5bb5811884ce"}}, {"head": {"id": "bf6018f1-75ed-4fee-8977-cfd47975eafb", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820069551900, "endTime": 151820069579700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5eb75217-2689-49ee-9155-8848cd760bd4", "logId": "b8eb9684-5d6b-4a9a-8e55-13677c0c7752"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8eb9684-5d6b-4a9a-8e55-13677c0c7752", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820069551900, "endTime": 151820069579700}, "additional": {"logType": "info", "children": [], "durationId": "bf6018f1-75ed-4fee-8977-cfd47975eafb", "parent": "2b296cd0-440e-4704-901f-5bb5811884ce"}}, {"head": {"id": "c286db89-7977-40ea-9dab-15d79cd07f9d", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820069823500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9a6f32b-d9d5-4cd8-80d8-202c962e8ed7", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820075766400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1309e077-7ee3-44b7-aaf9-02347b9d4de7", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820055056700, "endTime": 151820075885100}, "additional": {"logType": "info", "children": [], "durationId": "ce86a68d-34bc-4074-98c6-b56c81d9e8f3", "parent": "2b296cd0-440e-4704-901f-5bb5811884ce"}}, {"head": {"id": "652b9fa3-4f41-44c7-a44d-85a16589fea1", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820083435100, "endTime": 151820083451700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0cd7769b-335c-4ea3-8ec4-cc7156223d62", "logId": "4e619be3-b20e-4709-951f-b9710f152f4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e7fbe84-6a03-4eea-990a-50ef856c3c12", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820083554500, "endTime": 151820090904900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0cd7769b-335c-4ea3-8ec4-cc7156223d62", "logId": "215d3dd6-f8ff-4192-a547-175b42fb2eba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab4b3372-27ec-4a9c-b766-161f122b5b81", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820090926200, "endTime": 151820348420500}, "additional": {"children": ["49d8ba07-8413-4c39-9329-39e48b3c27d1", "69805f7f-f758-415d-982b-41ef401c5905", "ef595c62-fd59-4288-9c53-0c90f296a16d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0cd7769b-335c-4ea3-8ec4-cc7156223d62", "logId": "38e2136e-2470-408c-a1f1-22274a11d7fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a18f8c8-c59e-401a-8152-ebf9f2790481", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820348443400, "endTime": 151820401753800}, "additional": {"children": ["8b3c6779-ea4d-4f2b-a733-206c82f41ae0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0cd7769b-335c-4ea3-8ec4-cc7156223d62", "logId": "c7c6a628-3907-4138-8652-9c539ca2dc24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47076a50-5804-4436-b1f6-1d51e4a90832", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820401819700, "endTime": 151820485296100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0cd7769b-335c-4ea3-8ec4-cc7156223d62", "logId": "f94bd15a-4b27-448c-839b-d179066bafba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19f4c90a-e194-4340-9431-c58733dd99de", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820487223100, "endTime": 151820502415600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0cd7769b-335c-4ea3-8ec4-cc7156223d62", "logId": "8a77eaa0-21a6-4f1e-831c-4f9a1f6b247b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ee89cc6-49f8-46ac-abcc-6cd142d47543", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820502438600, "endTime": 151820535390700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0cd7769b-335c-4ea3-8ec4-cc7156223d62", "logId": "a0d2b793-35b0-4a22-b2c2-a72e00d463c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b9865b2-233d-477d-854c-f5c231f34c99", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820535425900, "endTime": 151820535633700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0cd7769b-335c-4ea3-8ec4-cc7156223d62", "logId": "bf61eeea-feeb-4fc8-ac46-707f597ad84d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e619be3-b20e-4709-951f-b9710f152f4b", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820083435100, "endTime": 151820083451700}, "additional": {"logType": "info", "children": [], "durationId": "652b9fa3-4f41-44c7-a44d-85a16589fea1", "parent": "b72037ca-8396-4f50-b363-573a06a5406e"}}, {"head": {"id": "215d3dd6-f8ff-4192-a547-175b42fb2eba", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820083554500, "endTime": 151820090904900}, "additional": {"logType": "info", "children": [], "durationId": "4e7fbe84-6a03-4eea-990a-50ef856c3c12", "parent": "b72037ca-8396-4f50-b363-573a06a5406e"}}, {"head": {"id": "49d8ba07-8413-4c39-9329-39e48b3c27d1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820092072300, "endTime": 151820092095800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ab4b3372-27ec-4a9c-b766-161f122b5b81", "logId": "ffbf2aad-ed3b-4162-b144-40ed300e2b17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffbf2aad-ed3b-4162-b144-40ed300e2b17", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820092072300, "endTime": 151820092095800}, "additional": {"logType": "info", "children": [], "durationId": "49d8ba07-8413-4c39-9329-39e48b3c27d1", "parent": "38e2136e-2470-408c-a1f1-22274a11d7fe"}}, {"head": {"id": "69805f7f-f758-415d-982b-41ef401c5905", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820096851500, "endTime": 151820347238500}, "additional": {"children": ["36fc6adf-cfa3-480e-8ea0-f4813bd419b0", "dab69d8e-b600-433e-8583-20288495ed71"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ab4b3372-27ec-4a9c-b766-161f122b5b81", "logId": "fd4a5358-9439-41ba-a7a9-fe81b5c1d8d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36fc6adf-cfa3-480e-8ea0-f4813bd419b0", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820096855300, "endTime": 151820207886400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "69805f7f-f758-415d-982b-41ef401c5905", "logId": "758d7dd6-19ef-4342-9b8b-e23f5f9108a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dab69d8e-b600-433e-8583-20288495ed71", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820207905100, "endTime": 151820347220400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "69805f7f-f758-415d-982b-41ef401c5905", "logId": "bf1ca12a-a675-4507-9b89-1f70cea4bc6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35b9ceda-f508-493d-b1c5-6a13f75cab3b", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820096865900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3212bf4e-44b1-4d5a-8133-7ff63746d5e0", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820207748200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "758d7dd6-19ef-4342-9b8b-e23f5f9108a5", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820096855300, "endTime": 151820207886400}, "additional": {"logType": "info", "children": [], "durationId": "36fc6adf-cfa3-480e-8ea0-f4813bd419b0", "parent": "fd4a5358-9439-41ba-a7a9-fe81b5c1d8d1"}}, {"head": {"id": "22daf350-a2ab-4c95-9ab5-0798a3f6a256", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820207939900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b4cfae-1485-453e-912f-c663d0cf19f4", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820237404400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbe58fd5-3b9a-452c-874c-0241949fbcc6", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820237665000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49e12da1-91af-4f7c-afd3-15a00efdc7e6", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820237835000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21fa6c09-c668-49d1-81b9-f2f72fd55ec5", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820238043200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c54b2c0e-9291-4ea0-9c64-a1ef7998ce93", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820241311500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e163546-95bb-49de-ba7a-320461be9d98", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820270506000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f8c45e8-2a92-42af-877b-5b2593d1e74f", "name": "Sdk init in 59 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820311360700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b035195-b046-4750-9494-cfa4d5ba93dc", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820311596400}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 7, "second": 30}, "markType": "other"}}, {"head": {"id": "1c7a629f-72ec-477d-92cc-18f4322d2f8d", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820311627900}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 7, "second": 30}, "markType": "other"}}, {"head": {"id": "b8bd9504-64c0-4ddd-bea9-42adef6a69e5", "name": "Project task initialization takes 33 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820346781800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "822ea5d0-ee04-43ba-baad-b022493d410a", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820346941700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d971218-ec77-45c6-a0e0-466ddb773efc", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820347045300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd12fda0-212e-4797-8429-94c24d4dde66", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820347118100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf1ca12a-a675-4507-9b89-1f70cea4bc6b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820207905100, "endTime": 151820347220400}, "additional": {"logType": "info", "children": [], "durationId": "dab69d8e-b600-433e-8583-20288495ed71", "parent": "fd4a5358-9439-41ba-a7a9-fe81b5c1d8d1"}}, {"head": {"id": "fd4a5358-9439-41ba-a7a9-fe81b5c1d8d1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820096851500, "endTime": 151820347238500}, "additional": {"logType": "info", "children": ["758d7dd6-19ef-4342-9b8b-e23f5f9108a5", "bf1ca12a-a675-4507-9b89-1f70cea4bc6b"], "durationId": "69805f7f-f758-415d-982b-41ef401c5905", "parent": "38e2136e-2470-408c-a1f1-22274a11d7fe"}}, {"head": {"id": "ef595c62-fd59-4288-9c53-0c90f296a16d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820348363300, "endTime": 151820348399200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ab4b3372-27ec-4a9c-b766-161f122b5b81", "logId": "15d0eb87-ead8-4544-932e-f313730348c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15d0eb87-ead8-4544-932e-f313730348c3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820348363300, "endTime": 151820348399200}, "additional": {"logType": "info", "children": [], "durationId": "ef595c62-fd59-4288-9c53-0c90f296a16d", "parent": "38e2136e-2470-408c-a1f1-22274a11d7fe"}}, {"head": {"id": "38e2136e-2470-408c-a1f1-22274a11d7fe", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820090926200, "endTime": 151820348420500}, "additional": {"logType": "info", "children": ["ffbf2aad-ed3b-4162-b144-40ed300e2b17", "fd4a5358-9439-41ba-a7a9-fe81b5c1d8d1", "15d0eb87-ead8-4544-932e-f313730348c3"], "durationId": "ab4b3372-27ec-4a9c-b766-161f122b5b81", "parent": "b72037ca-8396-4f50-b363-573a06a5406e"}}, {"head": {"id": "8b3c6779-ea4d-4f2b-a733-206c82f41ae0", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820349671100, "endTime": 151820401737200}, "additional": {"children": ["773b6777-6745-43bd-9203-57cb180f86bb", "ee45bc1f-8c30-4e68-8ccd-7cb553ef571b", "c434ca93-2761-4cb5-bde9-feda7d328dc5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6a18f8c8-c59e-401a-8152-ebf9f2790481", "logId": "4c5df79f-2323-4a76-89ad-c8755cc9ee8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "773b6777-6745-43bd-9203-57cb180f86bb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820355358700, "endTime": 151820355385500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8b3c6779-ea4d-4f2b-a733-206c82f41ae0", "logId": "c7a25a3c-2916-4bfe-95f2-57fc3ad366dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7a25a3c-2916-4bfe-95f2-57fc3ad366dc", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820355358700, "endTime": 151820355385500}, "additional": {"logType": "info", "children": [], "durationId": "773b6777-6745-43bd-9203-57cb180f86bb", "parent": "4c5df79f-2323-4a76-89ad-c8755cc9ee8f"}}, {"head": {"id": "ee45bc1f-8c30-4e68-8ccd-7cb553ef571b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820358643600, "endTime": 151820399409400}, "additional": {"children": ["5fd6d590-b172-4950-a5b3-ff52b731df5a", "11a456f2-7b47-409f-be4c-638ba2ec8c2d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8b3c6779-ea4d-4f2b-a733-206c82f41ae0", "logId": "cb3011e0-74f7-4257-a35c-e74076252006"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5fd6d590-b172-4950-a5b3-ff52b731df5a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820358645300, "endTime": 151820363747300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ee45bc1f-8c30-4e68-8ccd-7cb553ef571b", "logId": "ba89a8ce-4e7f-48c4-923a-8a9589dd9960"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11a456f2-7b47-409f-be4c-638ba2ec8c2d", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820363772200, "endTime": 151820399391300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ee45bc1f-8c30-4e68-8ccd-7cb553ef571b", "logId": "35d74560-51d6-49fa-9665-ce92f2733a7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c7d5dc3-5f96-4053-ab35-718cc49bfac9", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820358653500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b75860f4-9392-404d-a98d-10c9ceff8df5", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820363557100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba89a8ce-4e7f-48c4-923a-8a9589dd9960", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820358645300, "endTime": 151820363747300}, "additional": {"logType": "info", "children": [], "durationId": "5fd6d590-b172-4950-a5b3-ff52b731df5a", "parent": "cb3011e0-74f7-4257-a35c-e74076252006"}}, {"head": {"id": "aa57dc7d-e5d7-4513-a5dc-f8d6e48d4543", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820363791900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7045539c-971d-4a3b-92d6-a25d52ad7c95", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820386739800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a8bf0fa-36dd-4b38-b68d-c6bf62e4741b", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820386968400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14d809fe-7cf0-45ac-83ad-9d6d8555d067", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820387412200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7373b247-aa10-444f-818d-9bc544f56292", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820387645900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dd6457e-784a-43a5-8429-f7bb40f05377", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820387753400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a82630c1-bb5e-46cc-a452-7cadef49d585", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820387897600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7a5c769-2923-4395-9cf6-6f0d4131e0da", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820388031000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5433b0c8-030d-4ea8-ac19-57d2901952df", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820388134400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7f4ea04-4980-4c96-9766-80643b574307", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820388530400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6250612c-f45b-4aec-9274-9dcfa0323391", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820388754200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9295a321-e9e4-4890-823d-83f0675d0447", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820388863100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b80893dc-8c48-49b0-8244-7c6758005152", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820388940200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8cd2c7b-0b74-48bf-a47d-31972cdcdb37", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820389060500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "506d69e8-6ae1-487b-a173-6d4c7b5685cd", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820389158800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf60b209-2839-4c18-9590-89693bfcc2e5", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820389366600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d2c636f-d08a-4b62-b07d-c58d17f36c4a", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820389732600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "293c2f01-3c4e-48d8-b5ca-08fa7b673696", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820389867800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49daea2b-cc7f-4f32-9fe2-70ed50abfc2d", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820389959700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b5975be-704a-4016-90b3-0fdcb5ad5ecb", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820390374400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96be044c-a8f2-425c-b66e-0093e22ad2d9", "name": "Module entry task initialization takes 5 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820398818800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78735341-3f0b-4319-a8c0-196ec17ee448", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820399124300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac852bd9-e11f-46e6-a74b-023bf5aa4b78", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820399223600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bf1dafb-7aa6-4c62-98f0-a059c24d9740", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820399295200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35d74560-51d6-49fa-9665-ce92f2733a7c", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820363772200, "endTime": 151820399391300}, "additional": {"logType": "info", "children": [], "durationId": "11a456f2-7b47-409f-be4c-638ba2ec8c2d", "parent": "cb3011e0-74f7-4257-a35c-e74076252006"}}, {"head": {"id": "cb3011e0-74f7-4257-a35c-e74076252006", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820358643600, "endTime": 151820399409400}, "additional": {"logType": "info", "children": ["ba89a8ce-4e7f-48c4-923a-8a9589dd9960", "35d74560-51d6-49fa-9665-ce92f2733a7c"], "durationId": "ee45bc1f-8c30-4e68-8ccd-7cb553ef571b", "parent": "4c5df79f-2323-4a76-89ad-c8755cc9ee8f"}}, {"head": {"id": "c434ca93-2761-4cb5-bde9-feda7d328dc5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820401690900, "endTime": 151820401708800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8b3c6779-ea4d-4f2b-a733-206c82f41ae0", "logId": "f7e521a8-f593-49f7-aca4-4926318175f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7e521a8-f593-49f7-aca4-4926318175f7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820401690900, "endTime": 151820401708800}, "additional": {"logType": "info", "children": [], "durationId": "c434ca93-2761-4cb5-bde9-feda7d328dc5", "parent": "4c5df79f-2323-4a76-89ad-c8755cc9ee8f"}}, {"head": {"id": "4c5df79f-2323-4a76-89ad-c8755cc9ee8f", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820349671100, "endTime": 151820401737200}, "additional": {"logType": "info", "children": ["c7a25a3c-2916-4bfe-95f2-57fc3ad366dc", "cb3011e0-74f7-4257-a35c-e74076252006", "f7e521a8-f593-49f7-aca4-4926318175f7"], "durationId": "8b3c6779-ea4d-4f2b-a733-206c82f41ae0", "parent": "c7c6a628-3907-4138-8652-9c539ca2dc24"}}, {"head": {"id": "c7c6a628-3907-4138-8652-9c539ca2dc24", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820348443400, "endTime": 151820401753800}, "additional": {"logType": "info", "children": ["4c5df79f-2323-4a76-89ad-c8755cc9ee8f"], "durationId": "6a18f8c8-c59e-401a-8152-ebf9f2790481", "parent": "b72037ca-8396-4f50-b363-573a06a5406e"}}, {"head": {"id": "6947d283-b538-4a51-b24e-af04468ef042", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1953 more items\n]", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820425837600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "affceeb5-4f24-4412-bba7-5af2c11e1284", "name": "hvigorfile, resolve hvigorfile dependencies in 84 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820485092800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f94bd15a-4b27-448c-839b-d179066bafba", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820401819700, "endTime": 151820485296100}, "additional": {"logType": "info", "children": [], "durationId": "47076a50-5804-4436-b1f6-1d51e4a90832", "parent": "b72037ca-8396-4f50-b363-573a06a5406e"}}, {"head": {"id": "1e1ccbbd-89b7-40f7-ae5a-9b7debb1a849", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820486620300, "endTime": 151820487202100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0cd7769b-335c-4ea3-8ec4-cc7156223d62", "logId": "07e680f7-3e36-4816-b405-4d7f7e76bec1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9dfaa875-d0cb-44af-9598-eecaefd10bfc", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820486786300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07e680f7-3e36-4816-b405-4d7f7e76bec1", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820486620300, "endTime": 151820487202100}, "additional": {"logType": "info", "children": [], "durationId": "1e1ccbbd-89b7-40f7-ae5a-9b7debb1a849", "parent": "b72037ca-8396-4f50-b363-573a06a5406e"}}, {"head": {"id": "d0b994ae-b0aa-49f5-baa6-0530f6565492", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820490520800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f0d7d45-b148-4d5a-9b7c-82e516a05342", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820501082700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a77eaa0-21a6-4f1e-831c-4f9a1f6b247b", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820487223100, "endTime": 151820502415600}, "additional": {"logType": "info", "children": [], "durationId": "19f4c90a-e194-4340-9431-c58733dd99de", "parent": "b72037ca-8396-4f50-b363-573a06a5406e"}}, {"head": {"id": "2fe8d7f2-6445-438d-9407-a4531cd7f169", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820502547400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90e682f9-e231-4661-8345-85b25f84247f", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820518277200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5a2fcb5-90ca-434d-9eb8-22dcc61ff4c3", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820518513700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01a1a0a5-bed0-4715-bce9-1d7974596957", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820519687400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19eef0b9-9cb3-449a-a895-5fe16c0172a3", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820526232000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0278528f-6fd4-4ccd-b1f5-9972423e80a9", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820526404100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0d2b793-35b0-4a22-b2c2-a72e00d463c9", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820502438600, "endTime": 151820535390700}, "additional": {"logType": "info", "children": [], "durationId": "7ee89cc6-49f8-46ac-abcc-6cd142d47543", "parent": "b72037ca-8396-4f50-b363-573a06a5406e"}}, {"head": {"id": "8eca1b35-a73d-4523-91f9-758e6a4c0f11", "name": "Configuration phase cost:452 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820535462900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf61eeea-feeb-4fc8-ac46-707f597ad84d", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820535425900, "endTime": 151820535633700}, "additional": {"logType": "info", "children": [], "durationId": "0b9865b2-233d-477d-854c-f5c231f34c99", "parent": "b72037ca-8396-4f50-b363-573a06a5406e"}}, {"head": {"id": "b72037ca-8396-4f50-b363-573a06a5406e", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820075902800, "endTime": 151820535652100}, "additional": {"logType": "info", "children": ["4e619be3-b20e-4709-951f-b9710f152f4b", "215d3dd6-f8ff-4192-a547-175b42fb2eba", "38e2136e-2470-408c-a1f1-22274a11d7fe", "c7c6a628-3907-4138-8652-9c539ca2dc24", "f94bd15a-4b27-448c-839b-d179066bafba", "8a77eaa0-21a6-4f1e-831c-4f9a1f6b247b", "a0d2b793-35b0-4a22-b2c2-a72e00d463c9", "bf61eeea-feeb-4fc8-ac46-707f597ad84d", "07e680f7-3e36-4816-b405-4d7f7e76bec1"], "durationId": "0cd7769b-335c-4ea3-8ec4-cc7156223d62", "parent": "2b296cd0-440e-4704-901f-5bb5811884ce"}}, {"head": {"id": "eddc5e15-9809-4e45-8bb5-e4183dd44309", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820537892100, "endTime": 151820537921300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5eb75217-2689-49ee-9155-8848cd760bd4", "logId": "6eee29a7-909a-4b09-984c-857e9174554b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6eee29a7-909a-4b09-984c-857e9174554b", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820537892100, "endTime": 151820537921300}, "additional": {"logType": "info", "children": [], "durationId": "eddc5e15-9809-4e45-8bb5-e4183dd44309", "parent": "2b296cd0-440e-4704-901f-5bb5811884ce"}}, {"head": {"id": "35fadb49-f585-4645-9e91-9cb2ae79d12f", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820535690300, "endTime": 151820537943700}, "additional": {"logType": "info", "children": [], "durationId": "f35d527a-3330-4653-9067-8e5e7a75aaca", "parent": "2b296cd0-440e-4704-901f-5bb5811884ce"}}, {"head": {"id": "931c44bd-dfb1-447b-a911-7c0395b84847", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820537953900, "endTime": 151820537994400}, "additional": {"logType": "info", "children": [], "durationId": "82dcfdd6-f1d7-4703-baf2-e9b4d7e627c2", "parent": "2b296cd0-440e-4704-901f-5bb5811884ce"}}, {"head": {"id": "2b296cd0-440e-4704-901f-5bb5811884ce", "name": "init", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820055054600, "endTime": 151820538008300}, "additional": {"logType": "info", "children": ["1309e077-7ee3-44b7-aaf9-02347b9d4de7", "b72037ca-8396-4f50-b363-573a06a5406e", "35fadb49-f585-4645-9e91-9cb2ae79d12f", "931c44bd-dfb1-447b-a911-7c0395b84847", "3b748981-5f21-4351-9879-2cbef8fc7559", "b8eb9684-5d6b-4a9a-8e55-13677c0c7752", "6eee29a7-909a-4b09-984c-857e9174554b"], "durationId": "5eb75217-2689-49ee-9155-8848cd760bd4"}}, {"head": {"id": "9bd377b8-7b72-4de6-9817-5dd22054224d", "name": "Configuration task cost before running: 492 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820538727300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ef28025-e8c8-41cf-9395-619e25f7934f", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820555287000, "endTime": 151820575919700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e2a71fee-7cdd-45b7-8267-26616b0d1b77", "logId": "2daf61cd-0d16-490c-8831-700dc249c938"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2a71fee-7cdd-45b7-8267-26616b0d1b77", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820541606500}, "additional": {"logType": "detail", "children": [], "durationId": "6ef28025-e8c8-41cf-9395-619e25f7934f"}}, {"head": {"id": "66d8158a-5d22-4b34-baa2-dc5c0770ec81", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820542923300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "371cdcc6-bcd9-456c-9ee4-25c9f647d7d9", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820543303500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6908707e-9d12-4c89-b634-1db2faa8c865", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820545494600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0c3f1b6-19b0-4629-b530-256268b00369", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820546819700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8b232fb-e8ff-47e6-b8e8-92ef15310b2f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820548931000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b60ac58-a3d7-4084-8a4e-54cef7f68afc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820549096700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81b9bfc0-f005-44d6-9461-d16aa8484a39", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820555303000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a630496-8297-4e9e-82e5-6689abc267f0", "name": "Incremental task entry:default@PreBuild pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820575706300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8273a70a-1736-4ea5-90fd-5bac28b6669f", "name": "entry : default@PreBuild cost memory 0.**********", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820575856800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2daf61cd-0d16-490c-8831-700dc249c938", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820555287000, "endTime": 151820575919700}, "additional": {"logType": "info", "children": [], "durationId": "6ef28025-e8c8-41cf-9395-619e25f7934f"}}, {"head": {"id": "3d371ff2-160d-48ef-a94c-189f1b044cb8", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820589361600, "endTime": 151820592490800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "76f7eb22-1595-433c-9906-9f19a60fea64", "logId": "eac1a41e-7233-4640-a8a3-88b5400674eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76f7eb22-1595-433c-9906-9f19a60fea64", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820585837700}, "additional": {"logType": "detail", "children": [], "durationId": "3d371ff2-160d-48ef-a94c-189f1b044cb8"}}, {"head": {"id": "a7ec7559-9879-47b9-bd00-a35e7a30ad29", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820587679100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67cfe947-05d1-4d80-b29d-365bfc8ab0e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820587865700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9ecc8fc-1303-4130-a282-4d3ccd30bb97", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820589383600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9cfcc28-daf9-4654-88de-7fcf2c770827", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820590509700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dba4a4d-25b4-4f52-ae9e-d03fdcadf66b", "name": "entry : default@CreateModuleInfo cost memory 0.06109619140625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820592157800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8ddaa6a-2aaa-48fb-9a42-cb59d457051a", "name": "runTaskFromQueue task cost before running: 545 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820592373600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eac1a41e-7233-4640-a8a3-88b5400674eb", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820589361600, "endTime": 151820592490800, "totalTime": 2973100}, "additional": {"logType": "info", "children": [], "durationId": "3d371ff2-160d-48ef-a94c-189f1b044cb8"}}, {"head": {"id": "e2691498-f1bf-4b13-9f19-a98ce4b8daaa", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820608248400, "endTime": 151820612655800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f0126b59-ad9d-4197-916f-a9e44eb6887d", "logId": "e2c4e451-d441-42e3-bbd7-b3bf0c9897a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0126b59-ad9d-4197-916f-a9e44eb6887d", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820596941400}, "additional": {"logType": "detail", "children": [], "durationId": "e2691498-f1bf-4b13-9f19-a98ce4b8daaa"}}, {"head": {"id": "f69bfa03-1094-4714-b026-2fed5bcbf011", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820598684000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe736d83-c774-43e2-b583-12e98b491ed3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820598830500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd27489f-b9bd-46df-b656-4dc877cd9f16", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820608274800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e4fa049-90c0-4f3c-ac06-214493ace36c", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820610232800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a14e0a91-fc27-4396-9ae3-e4537d0a8917", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820612349900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f7c5bc1-bdb3-4971-af78-6e35ea077065", "name": "entry : default@GenerateMetadata cost memory 0.103057861328125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820612531000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2c4e451-d441-42e3-bbd7-b3bf0c9897a8", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820608248400, "endTime": 151820612655800}, "additional": {"logType": "info", "children": [], "durationId": "e2691498-f1bf-4b13-9f19-a98ce4b8daaa"}}, {"head": {"id": "c43618dd-89ed-459f-8f21-c3d5aeab07fb", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820620596000, "endTime": 151820621092200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "33e6de2f-71cd-4f89-be3a-f9da593af22c", "logId": "85718378-29d3-4666-89b4-943c92c929bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33e6de2f-71cd-4f89-be3a-f9da593af22c", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820617988500}, "additional": {"logType": "detail", "children": [], "durationId": "c43618dd-89ed-459f-8f21-c3d5aeab07fb"}}, {"head": {"id": "2479d9b7-6a5d-4873-afd3-dc7f272c97d1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820620071300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1fdfd57-ddea-4f40-9f9f-7612f3bf33c0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820620252800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad9f3c8d-d72f-46f1-b550-1092c9d6d1c0", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820620608600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "804dea0d-1c26-44ae-a1de-3da7fdf3604f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820620745000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97c41f14-d95b-426a-a4f0-757478d39041", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820620801700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dff896e-490e-453b-b033-7ba66cfe3620", "name": "entry : default@ConfigureCmake cost memory 0.03753662109375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820620884000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33337fc4-662e-474f-9c86-29ce553b9f81", "name": "runTaskFromQueue task cost before running: 574 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820620976900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85718378-29d3-4666-89b4-943c92c929bd", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820620596000, "endTime": 151820621092200, "totalTime": 356700}, "additional": {"logType": "info", "children": [], "durationId": "c43618dd-89ed-459f-8f21-c3d5aeab07fb"}}, {"head": {"id": "ff995192-1dca-4b8d-9062-6b392f048280", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820626012900, "endTime": 151820630337200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "933786aa-191c-492d-bf54-ca04b29a1e2a", "logId": "1a9791bf-1b63-4911-8690-26de2ceaf4c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "933786aa-191c-492d-bf54-ca04b29a1e2a", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820623404400}, "additional": {"logType": "detail", "children": [], "durationId": "ff995192-1dca-4b8d-9062-6b392f048280"}}, {"head": {"id": "367be09f-8759-4740-a5a1-bca8e8490402", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820624762000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3da57e95-af68-4009-8696-7264aa487649", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820625063900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adf14ca6-fe82-4afa-9df9-5f09668c7efb", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820626030400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e9e746a-bcbd-49e7-a578-2127266d1b78", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820630009200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4453ff0e-baee-47dd-936c-b90b6ba36837", "name": "entry : default@MergeProfile cost memory 0.11888885498046875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820630214200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a9791bf-1b63-4911-8690-26de2ceaf4c9", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820626012900, "endTime": 151820630337200}, "additional": {"logType": "info", "children": [], "durationId": "ff995192-1dca-4b8d-9062-6b392f048280"}}, {"head": {"id": "7681ae8a-13db-4884-b704-34dbe700ce7c", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820634929700, "endTime": 151820641027900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f071eae3-afab-452d-aba3-0ab6828fca31", "logId": "92bef580-415e-4ce8-bd6c-487dbe19bba5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f071eae3-afab-452d-aba3-0ab6828fca31", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820632874600}, "additional": {"logType": "detail", "children": [], "durationId": "7681ae8a-13db-4884-b704-34dbe700ce7c"}}, {"head": {"id": "43939753-3456-415a-b70c-0b5a1c01decd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820633820600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "676d21a5-613c-4574-a677-576e329f51f6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820633934700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c6d0717-21f1-4fb1-92c6-a48590c81e7b", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820634949600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61296ebf-cfd2-45e6-bfeb-f4446b7eb874", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820637265900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f019e34-f2e5-4d6b-acce-e9396b916df1", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820640700000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "952def58-b413-45b9-87c0-5721428295d3", "name": "entry : default@CreateBuildProfile cost memory 0.108673095703125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820640903800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92bef580-415e-4ce8-bd6c-487dbe19bba5", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820634929700, "endTime": 151820641027900}, "additional": {"logType": "info", "children": [], "durationId": "7681ae8a-13db-4884-b704-34dbe700ce7c"}}, {"head": {"id": "0a83d252-e43d-4130-83ed-1d9ba5a6b359", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820655824100, "endTime": 151820657030600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "94e6cca6-44d9-44ee-b34b-a86d805a3ff3", "logId": "2dad94f0-7d83-4594-90f9-e522870e48f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94e6cca6-44d9-44ee-b34b-a86d805a3ff3", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820651981400}, "additional": {"logType": "detail", "children": [], "durationId": "0a83d252-e43d-4130-83ed-1d9ba5a6b359"}}, {"head": {"id": "f9789dfc-0428-4b8f-b442-844e5e176c27", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820654246500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80f56bf5-112b-42b0-8c48-2e67f3573a3c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820654420700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd2eca60-362e-4548-9b60-10337033c97a", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820655841700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c922ab07-d209-4c34-9426-ddf8dae8f5c7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820656050900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4bc4f2c-e450-4bc0-92d1-c985af0c916a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820656152400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ec0ac65-a038-460d-9d8b-5b99726733ac", "name": "entry : default@PreCheckSyscap cost memory 0.0467071533203125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820656708100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83315156-4d28-4fca-a1e3-36d146d30ae3", "name": "runTaskFromQueue task cost before running: 610 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820656903400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dad94f0-7d83-4594-90f9-e522870e48f3", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820655824100, "endTime": 151820657030600, "totalTime": 1040000}, "additional": {"logType": "info", "children": [], "durationId": "0a83d252-e43d-4130-83ed-1d9ba5a6b359"}}, {"head": {"id": "103bf903-69bf-4d75-a020-50373c04249b", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820664765300, "endTime": 151820677762900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "613fde61-064a-4b84-a511-832d6a7839cf", "logId": "97ac57c5-6359-4851-b80e-d7b4f93d166b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "613fde61-064a-4b84-a511-832d6a7839cf", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820659999300}, "additional": {"logType": "detail", "children": [], "durationId": "103bf903-69bf-4d75-a020-50373c04249b"}}, {"head": {"id": "ea272b6b-a94a-4fd5-b4e9-a08b06337a9b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820661985900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d44241ad-4fc6-4c8a-ac81-3ada425b3bb1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820662147000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9257b9ea-8374-4039-9213-86c4bca7ee7d", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820664792000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d373895f-f60f-4941-b90d-c36ce058fd0b", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820676131600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8c2e64c-9e0e-43f8-87f9-73d6bb4ec63e", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820677463400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1ac5b6d-9d00-44b8-8172-b7fcd75b5790", "name": "entry : default@GeneratePkgContextInfo cost memory 0.5051040649414062", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820677642800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97ac57c5-6359-4851-b80e-d7b4f93d166b", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820664765300, "endTime": 151820677762900}, "additional": {"logType": "info", "children": [], "durationId": "103bf903-69bf-4d75-a020-50373c04249b"}}, {"head": {"id": "b9e26093-385a-47f7-8f43-7dfc652861e7", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820693234000, "endTime": 151820697341800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "64f8f44e-ff71-41db-8af5-a10a62f99306", "logId": "c51bdc05-77d0-4b62-99dd-146d135f9e9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64f8f44e-ff71-41db-8af5-a10a62f99306", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820680826500}, "additional": {"logType": "detail", "children": [], "durationId": "b9e26093-385a-47f7-8f43-7dfc652861e7"}}, {"head": {"id": "aa4d1b26-e8c1-4278-b16d-b3491d08d48a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820683078400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a49c2ef-517c-4d05-922d-76e5dee5035e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820683267900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0f4402b-7927-4fb3-9888-7ed96c48b761", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820693260800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a4e5318-7caf-4c74-9a0e-7b02f4678641", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820696625800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea42cfb8-f6a6-4ff7-87c3-a071b4a23878", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820696842400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99cb4d1d-48f7-49c8-93b6-c85967a7696e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820696972800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "372144d7-2d4d-41ea-bf36-f2e1290df60a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820697052700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "539b4985-84a4-4af8-af15-dd85c5fe70eb", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1217193603515625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820697160500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e90a3c34-0aa5-432c-a4ec-9d3de424ec92", "name": "runTaskFromQueue task cost before running: 650 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820697259200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c51bdc05-77d0-4b62-99dd-146d135f9e9f", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820693234000, "endTime": 151820697341800, "totalTime": 4007800}, "additional": {"logType": "info", "children": [], "durationId": "b9e26093-385a-47f7-8f43-7dfc652861e7"}}, {"head": {"id": "c51a3c00-8a32-4616-abad-9e0edcf1c08d", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820703708700, "endTime": 151820704284700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "aa777ec9-1557-442c-99f9-3012d2cb4a27", "logId": "ecdce510-786d-4778-afae-bbaf9989da2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa777ec9-1557-442c-99f9-3012d2cb4a27", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820700644100}, "additional": {"logType": "detail", "children": [], "durationId": "c51a3c00-8a32-4616-abad-9e0edcf1c08d"}}, {"head": {"id": "86687703-5a1c-4d9c-b9d8-01af06ded557", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820702410300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08b092cb-251a-4812-87bb-f34247f6d319", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820702549000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae294dc8-4643-4284-a5c2-0e51edcf5d7c", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820703723500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "061cade5-0684-42e6-a4eb-3ccbaa6f17d4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820703889700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "828ef603-f7c4-4b64-8e7d-0248d78ad4f2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820703970400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceddf15c-ff7f-4fd3-9cf7-2099fe9ef300", "name": "entry : default@BuildNativeWithCmake cost memory 0.03858184814453125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820704106900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81b8a38e-67f1-4ef2-bc6b-07c333ea3b96", "name": "runTaskFromQueue task cost before running: 657 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820704213800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecdce510-786d-4778-afae-bbaf9989da2e", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820703708700, "endTime": 151820704284700, "totalTime": 481800}, "additional": {"logType": "info", "children": [], "durationId": "c51a3c00-8a32-4616-abad-9e0edcf1c08d"}}, {"head": {"id": "f4a360e5-fca7-4010-83dc-9763a32b93a9", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820709565300, "endTime": 151820716624300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0aab2bd9-ad79-4103-b057-5eb48b03a81a", "logId": "0178b5bb-d948-4bec-b83b-d4ffa4154e29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0aab2bd9-ad79-4103-b057-5eb48b03a81a", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820706578100}, "additional": {"logType": "detail", "children": [], "durationId": "f4a360e5-fca7-4010-83dc-9763a32b93a9"}}, {"head": {"id": "816eec35-9f9b-4c8f-afe6-21a1a838f7b9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820708267200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "294589fe-5e20-4821-af81-8b3d5a588a82", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820708433100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14ffec02-a1b2-4416-a035-9051f825bafd", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820709579000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd92d297-4102-49c3-b5aa-12d848ecc860", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820716351700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8fe29cb-ead0-4c5b-b768-8064eb12d369", "name": "entry : default@MakePackInfo cost memory 0.16484832763671875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820716497200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0178b5bb-d948-4bec-b83b-d4ffa4154e29", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820709565300, "endTime": 151820716624300}, "additional": {"logType": "info", "children": [], "durationId": "f4a360e5-fca7-4010-83dc-9763a32b93a9"}}, {"head": {"id": "d4daca4d-e5b4-4083-9623-b2e0388bbb8e", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820720794500, "endTime": 151820723712600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b8fc86c8-a67c-4cca-8d7b-ae6fb1334d5d", "logId": "4548482a-61a5-4a14-b008-0af7c046206c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8fc86c8-a67c-4cca-8d7b-ae6fb1334d5d", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820718809100}, "additional": {"logType": "detail", "children": [], "durationId": "d4daca4d-e5b4-4083-9623-b2e0388bbb8e"}}, {"head": {"id": "1e1b777e-1d68-4c4f-8603-cda850108c76", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820719656500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "781f6517-bd79-4bfd-babc-bf6e083a0f01", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820719769900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5731858-4fe9-443b-86f1-3465c384e148", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820720806500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81025540-0429-4b1e-a6e9-ee88c94514a3", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820720972800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8eb03d42-cbc2-402e-90cb-cb77da4b0c07", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820721788300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "478beb78-56b7-4148-848b-a36726114ee1", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820723542300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f2979f8-b134-4ac1-9cb8-f383d149fae6", "name": "entry : default@SyscapTransform cost memory 0.15381622314453125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820723661500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4548482a-61a5-4a14-b008-0af7c046206c", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820720794500, "endTime": 151820723712600}, "additional": {"logType": "info", "children": [], "durationId": "d4daca4d-e5b4-4083-9623-b2e0388bbb8e"}}, {"head": {"id": "98ac1d84-1317-4a58-8413-afa9bad85b10", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820728284500, "endTime": 151820731638000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "691a9d2d-731e-47bb-95e0-10d3bb423999", "logId": "8dc8cd3f-973d-4550-abe6-95faa0010aa2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "691a9d2d-731e-47bb-95e0-10d3bb423999", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820725615600}, "additional": {"logType": "detail", "children": [], "durationId": "98ac1d84-1317-4a58-8413-afa9bad85b10"}}, {"head": {"id": "b7a33215-acde-4a1f-a193-fba50607ac4b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820726832500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f3a9e2f-e525-4293-8a72-ea29d92b27a5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820726937900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50590c80-c2a0-47c5-891e-039ae64b3e14", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820728296000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46855dd7-8ea4-417b-b998-d6fe70477a32", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820731437000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41205fce-a321-4f59-b134-d23a7defba9d", "name": "entry : default@ProcessProfile cost memory 0.125457763671875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820731576900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dc8cd3f-973d-4550-abe6-95faa0010aa2", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820728284500, "endTime": 151820731638000}, "additional": {"logType": "info", "children": [], "durationId": "98ac1d84-1317-4a58-8413-afa9bad85b10"}}, {"head": {"id": "18441417-78cb-4ccf-adc2-0ad70773f190", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820737689700, "endTime": 151820748512100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "30e72dcb-5caf-49ec-89b7-21a11670c72e", "logId": "88463910-99bd-40d3-aa16-2a683ddc68cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30e72dcb-5caf-49ec-89b7-21a11670c72e", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820733396200}, "additional": {"logType": "detail", "children": [], "durationId": "18441417-78cb-4ccf-adc2-0ad70773f190"}}, {"head": {"id": "437ca8e5-2fc3-4f88-8222-868bf872cfef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820734927800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec0ea014-b02e-4492-b49d-c2dc7eaef887", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820735064900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3609c846-3155-4c13-b3e8-08cdfab4ade4", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820737710500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b24e0b81-69e5-426c-840f-c3c6ae6bab70", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820748227300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "656b0a0f-f27b-4324-8b4a-baa250095940", "name": "entry : default@ProcessRouterMap cost memory 0.2352294921875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820748410900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88463910-99bd-40d3-aa16-2a683ddc68cc", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820737689700, "endTime": 151820748512100}, "additional": {"logType": "info", "children": [], "durationId": "18441417-78cb-4ccf-adc2-0ad70773f190"}}, {"head": {"id": "1d9da12c-47ec-488b-beb7-8b6454634ccd", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820755604200, "endTime": 151820765272000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "e3b0ab00-f14c-42a3-a7af-61dbddba4515", "logId": "17ea4178-0e5e-448d-8912-3500ef8db98b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3b0ab00-f14c-42a3-a7af-61dbddba4515", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820753159900}, "additional": {"logType": "detail", "children": [], "durationId": "1d9da12c-47ec-488b-beb7-8b6454634ccd"}}, {"head": {"id": "0a9737c6-aea6-4df4-ade6-730223b00300", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820755306400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c732f767-919a-479f-bb73-50b7fe3deb8c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820755451900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0e709f6-dfb7-4679-83a9-fc50324c5574", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820755615900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3acc8af4-f20a-4215-a325-8c6a9c8ad6e3", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820755839600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c25867fd-2792-45d1-8696-b75d1c1539dd", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820762029800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8fad801-001e-4c33-8bc4-38b3b399e411", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820762258600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "637c560c-7fec-4389-af7c-dfe2775742bf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820762401200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c03af152-e465-4d9a-9f39-53a07b60c778", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820762501000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72c0305b-e724-4953-a433-3e3e00d076d8", "name": "entry : default@ProcessStartupConfig cost memory 0.262451171875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820764973200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ce4a2aa-df52-4645-bfac-f3df2a53b3db", "name": "runTaskFromQueue task cost before running: 718 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820765178300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17ea4178-0e5e-448d-8912-3500ef8db98b", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820755604200, "endTime": 151820765272000, "totalTime": 9531100}, "additional": {"logType": "info", "children": [], "durationId": "1d9da12c-47ec-488b-beb7-8b6454634ccd"}}, {"head": {"id": "c64a5252-2b5a-48ac-a421-2f20fbebba47", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820773353900, "endTime": 151820776093800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0e7ead00-45e5-4ad9-9a2a-9ab3176880a7", "logId": "cfbf73cc-3537-4ef5-a815-793ba13449ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e7ead00-45e5-4ad9-9a2a-9ab3176880a7", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820769298100}, "additional": {"logType": "detail", "children": [], "durationId": "c64a5252-2b5a-48ac-a421-2f20fbebba47"}}, {"head": {"id": "2a856cbd-0014-47f7-890b-a077db529bef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820771497600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3353e43b-606a-4053-9f89-3a425e61c943", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820771694300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a6356a8-2576-4f9a-bfc9-8ed411b8d342", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820773384700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a10e105-66b2-49a0-b235-dff4ee29f53d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820773590100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30e04f16-942f-4353-b45f-7bf443ce4525", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820773676800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e822a2a-f2cf-4fe9-b80c-c37760242ab0", "name": "entry : default@BuildNativeWithNinja cost memory 0.0586090087890625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820775611600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f6dc13c-88f4-47cd-963b-fb579c4e312c", "name": "runTaskFromQueue task cost before running: 729 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820775969800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfbf73cc-3537-4ef5-a815-793ba13449ed", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820773353900, "endTime": 151820776093800, "totalTime": 2596800}, "additional": {"logType": "info", "children": [], "durationId": "c64a5252-2b5a-48ac-a421-2f20fbebba47"}}, {"head": {"id": "b11f699c-1b13-42e1-a0b1-ca3f42e5f5c3", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820788448700, "endTime": 151820801763000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "30fb559f-47e8-4728-819d-c6e5f05ced68", "logId": "183c0fa7-8854-481d-9641-2b2b70b51bc1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30fb559f-47e8-4728-819d-c6e5f05ced68", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820780787000}, "additional": {"logType": "detail", "children": [], "durationId": "b11f699c-1b13-42e1-a0b1-ca3f42e5f5c3"}}, {"head": {"id": "fd14dc00-f31f-474f-8a2c-df5df9c75148", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820782890500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d465653-237c-47b5-b000-b509de34506d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820783052700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45fdf52e-4cac-468f-aac2-cc119b314780", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820785691700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6da61da-abf8-434a-be69-76aaac57f052", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820793105000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcaa2ba3-d329-44d8-a7c4-68e2702918a1", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820797844200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa749d72-fb40-4019-bd5f-ae921437311d", "name": "entry : default@ProcessResource cost memory 0.27890777587890625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820798084100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "183c0fa7-8854-481d-9641-2b2b70b51bc1", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820788448700, "endTime": 151820801763000}, "additional": {"logType": "info", "children": [], "durationId": "b11f699c-1b13-42e1-a0b1-ca3f42e5f5c3"}}, {"head": {"id": "c822b79b-cfa1-458e-81af-25ef18ad1f8b", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820820774000, "endTime": 151820858936000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "62f879b9-935e-40e7-a5a8-9e0802e3d595", "logId": "f3c873fc-d84f-449c-a3d3-84292cfa46d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62f879b9-935e-40e7-a5a8-9e0802e3d595", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820810452600}, "additional": {"logType": "detail", "children": [], "durationId": "c822b79b-cfa1-458e-81af-25ef18ad1f8b"}}, {"head": {"id": "8e0616ad-c172-49ea-98cd-0b644ce1b790", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820812992800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ce4a284-5315-4a14-bb46-037084360425", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820813167700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b164419a-6ac3-427b-9d63-f45e5c69b5f0", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820820795300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91f38f03-b383-49b2-86ea-f4bceb249a08", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820858592800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "124774bc-0a5e-4008-bc21-bf50e62a3e85", "name": "entry : default@GenerateLoaderJson cost memory 0.890472412109375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820858824900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3c873fc-d84f-449c-a3d3-84292cfa46d3", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820820774000, "endTime": 151820858936000}, "additional": {"logType": "info", "children": [], "durationId": "c822b79b-cfa1-458e-81af-25ef18ad1f8b"}}, {"head": {"id": "0e48270e-ec80-481e-a16d-5a92b30a2f8b", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820877525100, "endTime": 151820887487800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "052b851a-5238-4860-948a-7b3311974d40", "logId": "267416c1-a65a-4c2a-8c30-c81e5dba8c7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "052b851a-5238-4860-948a-7b3311974d40", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820874470200}, "additional": {"logType": "detail", "children": [], "durationId": "0e48270e-ec80-481e-a16d-5a92b30a2f8b"}}, {"head": {"id": "71a5f7aa-ee3e-4b69-90e2-447fa6d86fd8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820876238100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0204df56-7e65-43b9-80ed-79e83a04d085", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820876372300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a596375-110e-4668-9db0-e604116891f1", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820877538400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9be203d-2ddd-4bcb-bdea-8c5688cb8ea4", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820887169600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eed5f27-bfc6-4eca-80d1-f31fd7b86ddb", "name": "entry : default@ProcessLibs cost memory 0.144622802734375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820887369900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "267416c1-a65a-4c2a-8c30-c81e5dba8c7f", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820877525100, "endTime": 151820887487800}, "additional": {"logType": "info", "children": [], "durationId": "0e48270e-ec80-481e-a16d-5a92b30a2f8b"}}, {"head": {"id": "f9f7053b-a82f-4329-9b50-4d55e4cd29fb", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820900362100, "endTime": 151820939687300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "b0937368-d602-412d-b5ec-e9eca449e55b", "logId": "8c79fa59-ec57-43cc-839a-f9c2c96272bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b0937368-d602-412d-b5ec-e9eca449e55b", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820891936800}, "additional": {"logType": "detail", "children": [], "durationId": "f9f7053b-a82f-4329-9b50-4d55e4cd29fb"}}, {"head": {"id": "e20ab2cf-1c0a-4574-9b2b-e675505305e1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820894102500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8fa06b4-f402-41c3-8940-e99afdbffb90", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820894256100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49bebcc4-ead6-49f5-8703-52858a812355", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820896738100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "283b91c7-e7ca-4a12-b930-7145409544a5", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820900466300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd72fef2-eb38-4752-8424-bf20911f5686", "name": "Incremental task entry:default@CompileResource pre-execution cost: 38 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820939456400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da0403f7-c258-4eb2-9e88-0548ff461700", "name": "entry : default@CompileResource cost memory -5.9727020263671875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820939607300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c79fa59-ec57-43cc-839a-f9c2c96272bf", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820900362100, "endTime": 151820939687300}, "additional": {"logType": "info", "children": [], "durationId": "f9f7053b-a82f-4329-9b50-4d55e4cd29fb"}}, {"head": {"id": "81dba53c-f67d-47d7-b715-8f3616f75b69", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820951091700, "endTime": 151820953546500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "42eb8a25-d7f2-4d36-8bf1-70a2991a526f", "logId": "e4983ec2-1af1-4e8a-839c-c53551c23b91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42eb8a25-d7f2-4d36-8bf1-70a2991a526f", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820945490800}, "additional": {"logType": "detail", "children": [], "durationId": "81dba53c-f67d-47d7-b715-8f3616f75b69"}}, {"head": {"id": "9a968a64-5df7-425d-9354-db9bb889db76", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820948024700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92b8a342-4828-4307-bcbe-58b9aafa4fe0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820948173800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a718fd18-7e7a-4df1-b943-2df75b80788a", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820951106600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac29e8eb-6f8a-4f38-832b-98c1f8d6bcad", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820951704100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78924490-13fd-48e3-8193-6f89706ed5d2", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820953372200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34d35eef-d66d-4b62-8ed8-01f31c06d1f0", "name": "entry : default@DoNativeStrip cost memory 0.08173370361328125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820953486100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4983ec2-1af1-4e8a-839c-c53551c23b91", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820951091700, "endTime": 151820953546500}, "additional": {"logType": "info", "children": [], "durationId": "81dba53c-f67d-47d7-b715-8f3616f75b69"}}, {"head": {"id": "5aa5411a-b206-4736-8b28-ae434e3040ba", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820963012300, "endTime": 151834882916100}, "additional": {"children": ["ebb38231-beae-40df-97f2-9cf3c7ff72dc", "3756e061-c5ef-4c0b-b040-297201eb700f"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "17507e70-b525-4ed4-961f-bcd24df9251d", "logId": "622f86da-11a6-47a7-8778-79fec882a661"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17507e70-b525-4ed4-961f-bcd24df9251d", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820955624700}, "additional": {"logType": "detail", "children": [], "durationId": "5aa5411a-b206-4736-8b28-ae434e3040ba"}}, {"head": {"id": "b6057d9b-4a05-4128-9039-ba55d46b0ef4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820956679200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aebb6d0-92dd-49dc-a36e-a4533afe4580", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820956787700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6cff4e1-5bc9-40da-8fc2-5c25b34fa125", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820963032400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea18b442-9fd0-4568-9d0e-e7fc6b614af6", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820963286500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4931318-94ce-4ec6-b906-bf4101c05769", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821015411500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b08943f-725d-483a-81e3-13377009622f", "name": "default@CompileArkTS work[4] is submitted.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821019661400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebb38231-beae-40df-97f2-9cf3c7ff72dc", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 151821024812800, "endTime": 151834882659300}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "5aa5411a-b206-4736-8b28-ae434e3040ba", "logId": "96e4eb83-7b31-4141-b48f-c334bc7df36f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47663267-8027-4495-b0f8-fea176676797", "name": "default@CompileArkTS work[4] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821021472800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45d66018-83c5-41aa-814b-e8aae1cb7ac3", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821021754000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e46d8362-e8fd-429d-857f-7ff6488554f3", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821021885400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c3bfec0-a45b-4d95-8d72-1dfae18cdd5a", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821021994800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f67b4ea4-3532-4e26-be1b-8eb51a57b47b", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821022081300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c915c1b5-129d-4de1-b7fa-78343af19a86", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821022174500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6185f2bc-baa6-43d6-9cc8-e45ca39a9ea8", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821022259100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e294dab-dc43-4b66-9afd-4b0e8b6ff2dd", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821022340200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73c2d3ab-03c2-4867-8a39-3b7b3ebec2a1", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821022444100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "664d9b22-aaf6-462a-9f45-a25278883c10", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821022508600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04287ec4-a310-44a9-bc77-fa998a48682d", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821022579800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce419052-ac47-45e2-870f-a2a33b4acdd0", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821022652300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6db9ba6e-ba14-4a35-8929-5422b4473723", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821022788400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a95dc381-88e4-4e18-a96e-b73d5a88cd32", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821022836200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4e31782-7354-4575-a18f-0919317b241c", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821022875600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e50da9c-3b46-4bab-85e5-8aae55ffe63d", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821022906300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aef95d32-9099-471f-b46b-8f71fd9f584b", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821023090000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14b81c64-6063-4f6c-a275-3ebb625b2693", "name": "default@CompileArkTS work[4] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821024884200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb8e06cd-b8f5-4ffe-a699-1a86b67ea31a", "name": "default@CompileArkTS work[4] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821025167600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbb09add-afb1-4ece-902f-fa038ff8d7b6", "name": "CopyResources startTime: 151821025323800", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821025332100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f516ac3c-fae1-4bff-9fe1-548e78025d27", "name": "default@CompileArkTS work[5] is submitted.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821025490300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3756e061-c5ef-4c0b-b040-297201eb700f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker18", "startTime": 151822519001000, "endTime": 151822530745200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "5aa5411a-b206-4736-8b28-ae434e3040ba", "logId": "ef3085fd-8a5a-4075-8356-1b5b2feceb9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24a6ec38-9633-41e4-af18-911728433780", "name": "default@CompileArkTS work[5] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821027187600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c028278-b87b-4024-9a92-71315c1e79f5", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821027356300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "606e2e24-dbed-43e8-9af8-e2f74e401bd5", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821027481500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f62ac8f-03ca-41f8-ad88-33f20eeb0109", "name": "default@CompileArkTS work[5] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821028938000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6bd31c6-fe90-4b51-85b5-ed06af4e18a1", "name": "default@CompileArkTS work[5] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821029106600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e075b7df-ca19-4082-8ba0-56b4097d8f84", "name": "entry : default@CompileArkTS cost memory 1.901641845703125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821029402800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3020bfe0-d53c-493d-8c8b-8b2ee409579d", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821039405400, "endTime": 151821056380100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "ed4b708f-d226-481b-964a-b13bccc96595", "logId": "d6b041ed-d147-4a99-a7bf-c016ffa057b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed4b708f-d226-481b-964a-b13bccc96595", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821031936200}, "additional": {"logType": "detail", "children": [], "durationId": "3020bfe0-d53c-493d-8c8b-8b2ee409579d"}}, {"head": {"id": "44fa859b-1309-4860-8bef-682e697d0661", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821032961800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53b05a1f-7420-41fa-9671-f292208e1dcf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821033072800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cab01bd5-32d2-4d57-b1fc-1b725e7e6242", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821039431700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed50c12a-c2c2-42a8-87d2-d295f8158cdb", "name": "entry : default@BuildJS cost memory 0.35480499267578125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821055985700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "150ff074-972c-4426-bd45-3bb4e6a0861f", "name": "runTaskFromQueue task cost before running: 1 s 9 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821056247600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6b041ed-d147-4a99-a7bf-c016ffa057b7", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821039405400, "endTime": 151821056380100, "totalTime": 16792700}, "additional": {"logType": "info", "children": [], "durationId": "3020bfe0-d53c-493d-8c8b-8b2ee409579d"}}, {"head": {"id": "0987d758-afe8-4241-9d5a-4fb25f1214b6", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821066171500, "endTime": 151821069834400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6d5ac111-c233-4a63-bae0-fbaf4205965d", "logId": "80db080e-cb48-4789-a7f1-495a94704578"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d5ac111-c233-4a63-bae0-fbaf4205965d", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821059795300}, "additional": {"logType": "detail", "children": [], "durationId": "0987d758-afe8-4241-9d5a-4fb25f1214b6"}}, {"head": {"id": "03c20278-03bd-40a1-a71c-e3f3a90ffe94", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821062254600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7efed47a-f783-4b65-b0cd-078148797ae6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821062429500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aab20fe5-8d6a-4d4e-b06b-42690c95ce56", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821066187800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a06be73-3f09-4b24-b646-49761c120fb6", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821067116000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d92c44b6-3028-4afe-abcd-ab8c262567f1", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821069619000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e29a4c98-b380-4179-8ba2-fbc63806ccb8", "name": "entry : default@CacheNativeLibs cost memory 0.09880828857421875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821069764600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80db080e-cb48-4789-a7f1-495a94704578", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151821066171500, "endTime": 151821069834400}, "additional": {"logType": "info", "children": [], "durationId": "0987d758-afe8-4241-9d5a-4fb25f1214b6"}}, {"head": {"id": "63dd5b3c-b167-4ae9-9865-6780a7d6a866", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151822531203500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa3d948e-367d-497b-a8a1-a781c1c19fa7", "name": "CopyResources is end, endTime: 151822531495600", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151822531514100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f309afd8-707d-4c0b-994a-42b87bac678e", "name": "default@CompileArkTS work[5] done.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151822531892300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef3085fd-8a5a-4075-8356-1b5b2feceb9a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Worker18", "startTime": 151822519001000, "endTime": 151822530745200}, "additional": {"logType": "info", "children": [], "durationId": "3756e061-c5ef-4c0b-b040-297201eb700f", "parent": "622f86da-11a6-47a7-8778-79fec882a661"}}, {"head": {"id": "883b94d4-b27f-42b7-bea2-dc0a53fd7cfa", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151822532050000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4a69ad4-aa15-487b-b9a4-3b21e5b2f5fb", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834882488200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc908919-15c1-4f77-864b-d5a3b9b1f424", "name": "default@CompileArkTS work[4] failed.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834882781600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96e4eb83-7b31-4141-b48f-c334bc7df36f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 151821024812800, "endTime": 151834882659300}, "additional": {"logType": "error", "children": [], "durationId": "ebb38231-beae-40df-97f2-9cf3c7ff72dc", "parent": "622f86da-11a6-47a7-8778-79fec882a661"}}, {"head": {"id": "622f86da-11a6-47a7-8778-79fec882a661", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820963012300, "endTime": 151834882916100}, "additional": {"logType": "error", "children": ["96e4eb83-7b31-4141-b48f-c334bc7df36f", "ef3085fd-8a5a-4075-8356-1b5b2feceb9a"], "durationId": "5aa5411a-b206-4736-8b28-ae434e3040ba"}}, {"head": {"id": "395941b7-4e5d-4c4e-a341-8e74293aa5e9", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834883023600}, "additional": {"logType": "debug", "children": [], "durationId": "5aa5411a-b206-4736-8b28-ae434e3040ba"}}, {"head": {"id": "95240364-bf1c-47d9-b6cc-c405bdaad2d1", "name": "ERROR: stacktrace = Error: ArkTS Compiler Error\r\n\u001b[31m1 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Module '\"./FeaturedPage\"' declares 'FeaturedPage' locally, but it is not exported. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/Index.ets:3:10\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m2 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'margin' does not exist on type 'void'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedCollectionPage.ets:292:20\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m3 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'margin' does not exist on type 'void'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedCollectionDetailPage.ets:392:20\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m4 ERROR: \u001b[31m10905204 ArkTS Compiler Error\r\nError Message: 'FeaturedPage()' does not meet UI component syntax. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/Index.ets:76:7\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:5}\u001b[39m\n    at runArkPack (C:\\Program Files\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5479)", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834883480800}, "additional": {"logType": "debug", "children": [], "durationId": "5aa5411a-b206-4736-8b28-ae434e3040ba"}}, {"head": {"id": "c2a45474-edd4-4995-9f08-5023d4566a1b", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834894349900, "endTime": 151834894461100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6a8f52c1-70ea-4755-8834-816227f1ea2b", "logId": "885e26c5-5c1b-45c0-9548-7303bbe80cdf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "885e26c5-5c1b-45c0-9548-7303bbe80cdf", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834894349900, "endTime": 151834894461100}, "additional": {"logType": "info", "children": [], "durationId": "c2a45474-edd4-4995-9f08-5023d4566a1b"}}, {"head": {"id": "9f759b68-27c6-40a7-bea4-f51d53a01a1c", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151820047372500, "endTime": 151834894611600}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 7, "second": 44}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "86f2a739-df54-4ac4-b719-3148710e7ea1", "name": "BUILD FAILED in 14 s 848 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834894639600}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "002a8f6a-c93e-4f26-897b-5403b0c83652", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834894809000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b96b56c6-9a85-48ea-995d-2beda46bce77", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834894865400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfd8a921-6caf-4299-ad76-6594d4f62b04", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834895296600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11d4a14c-2a57-4574-91d9-b279044c458e", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834895366800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28345c99-8825-4df5-bc75-da31a6314bd2", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834895406800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1483bfff-696e-48de-b7b6-c4265af7d9f7", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834895439900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81b2ff2b-8646-4587-a1c2-69616eb73109", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834895468000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd748625-fe6c-45b2-85f0-14017cc46d63", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834896006500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1350b013-3ad6-4c36-8cf4-9218d2c072bc", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834896209600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09fd1d06-3746-486b-9ad2-82b88e16ef4d", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834896258400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "691d09cc-c9c1-47ba-b1c2-a8629bb3533a", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834896287700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d52d795f-a6f3-4dd4-a684-31fb74fb19be", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834896316900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faffc785-349e-43e3-9fc3-d427eca7aebf", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834896344800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be632ad6-bb71-433f-b9e2-914a9c1b63b1", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834897374800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb509676-a090-49ef-8fec-28325e04ace7", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834897780700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10a5fe0f-3632-468c-bd2e-960e9cc9d5e4", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834898014500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68df355f-39bf-4e72-b39e-459b7b5176cd", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834898082400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "077da509-6373-44b8-b5f5-4bf2171397c3", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834898120700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "547ba2f9-5016-4b0e-99c0-10c96855cb38", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834898158300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2faf165d-7f3e-4607-8df8-9311bba6bb9e", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834898188500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b12c5bef-fc63-4bd2-9318-a56946e8f9b2", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834898218200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b61cba7a-5fed-4d43-8e93-eb008dbd73c4", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834901042200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dc15731-0547-4123-b452-beaca41ac27f", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834901767800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "920f9040-a669-4f83-8730-eb645dab6fc5", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834902168800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79944fb9-5481-428c-a065-068e3c96ab36", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834902409200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b48f7cea-a7fe-4c33-b498-d5b01602935c", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834902612200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "604eb541-c989-4738-897f-3157cd15babb", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834903305700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cd2efbc-dcec-4db1-951e-514e65e97f2c", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834910005900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ada4c017-dc64-4e5f-9c89-c4d43411b959", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834910273400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3cd0b3d-ffc7-428c-9283-c8f24e610e1f", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834910649600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bcca323-1829-4e88-8224-c37438c1f444", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834911632000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae6b6ada-b4b1-414b-8a73-f53fbe290b35", "name": "Incremental task entry:default@CompileArkTS post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834912205200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "865eb0a2-fa3d-4de8-b859-b6fadc809da6", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834913952400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8c13f5f-1703-46ad-9e2d-b34e84cfd0c7", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834914682800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d57b7fe1-c4b2-42f4-8daa-e494a77e8fd6", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834915058700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd90ca63-ed97-4352-bdd3-418469980e87", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834915401200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "183c51cf-8e62-41b9-9ba7-30836d658025", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834915588800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b83acf3c-a772-4221-9263-0d3b395d874f", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834916199100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d93cea0e-1cc7-43a2-9e88-ee3ce3465fab", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834916996100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8848ff0-893d-4088-a04f-09a103dde01c", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834917311600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8551c20-be54-4c85-8018-d45cb8dd27b3", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151834917375800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}