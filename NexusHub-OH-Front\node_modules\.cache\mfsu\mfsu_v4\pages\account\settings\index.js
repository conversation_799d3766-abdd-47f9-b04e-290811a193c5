"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { GridContent } from "@ant-design/pro-components";
import { Menu } from "antd";
import { useLayoutEffect, useRef, useState } from "react";
import BaseView from "./components/base";
import NotificationView from "./components/notification";
import SecurityView from "./components/security";
import useStyles from "./style.style";
const Settings = () => {
  const { styles } = useStyles();
  const menuMap = {
    base: "\u57FA\u672C\u8BBE\u7F6E",
    security: "\u5B89\u5168\u8BBE\u7F6E",
    notification: "\u65B0\u6D88\u606F\u901A\u77E5"
  };
  const [initConfig, setInitConfig] = useState({
    mode: "inline",
    selectKey: "base"
  });
  const dom = useRef();
  const resize = () => {
    requestAnimationFrame(() => {
      if (!dom.current) {
        return;
      }
      let mode = "inline";
      const { offsetWidth } = dom.current;
      if (dom.current.offsetWidth < 641 && offsetWidth > 400) {
        mode = "horizontal";
      }
      if (window.innerWidth < 768 && offsetWidth > 400) {
        mode = "horizontal";
      }
      setInitConfig({
        ...initConfig,
        mode
      });
    });
  };
  useLayoutEffect(() => {
    if (dom.current) {
      window.addEventListener("resize", resize);
      resize();
    }
    return () => {
      window.removeEventListener("resize", resize);
    };
  }, [dom.current]);
  const getMenu = () => {
    return Object.keys(menuMap).map((item) => ({ key: item, label: menuMap[item] }));
  };
  const renderChildren = () => {
    const { selectKey } = initConfig;
    switch (selectKey) {
      case "base":
        return /* @__PURE__ */ jsx(BaseView, {});
      case "security":
        return /* @__PURE__ */ jsx(SecurityView, {});
      case "notification":
        return /* @__PURE__ */ jsx(NotificationView, {});
      default:
        return null;
    }
  };
  return /* @__PURE__ */ jsx(GridContent, { children: /* @__PURE__ */ jsxs(
    "div",
    {
      className: styles.main,
      ref: (ref) => {
        if (ref) {
          dom.current = ref;
        }
      },
      children: [
        /* @__PURE__ */ jsx("div", { className: styles.leftMenu, children: /* @__PURE__ */ jsx(
          Menu,
          {
            mode: initConfig.mode,
            selectedKeys: [initConfig.selectKey],
            onClick: ({ key }) => {
              setInitConfig({
                ...initConfig,
                selectKey: key
              });
            },
            items: getMenu()
          }
        ) }),
        /* @__PURE__ */ jsxs("div", { className: styles.right, children: [
          /* @__PURE__ */ jsx("div", { className: styles.title, children: menuMap[initConfig.selectKey] }),
          renderChildren()
        ] })
      ]
    }
  ) });
};
export default Settings;
