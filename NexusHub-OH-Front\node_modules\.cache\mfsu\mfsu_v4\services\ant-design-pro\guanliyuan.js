"use strict";
import { request } from "@umijs/max";
export async function getAdminUsers(params, options) {
  return request("/admin/users", {
    method: "GET",
    params: {
      ...params
    },
    ...options || {}
  });
}
export async function postAdminUsers(body, options) {
  return request("/admin/users", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    data: body,
    ...options || {}
  });
}
export async function putAdminUsersIdRole(params, body, options) {
  const { id: param0, ...queryParams } = params;
  return request(`/admin/users/${param0}/role`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json"
    },
    params: { ...queryParams },
    data: body,
    ...options || {}
  });
}
export async function putAdminUsersIdStatus(params, body, options) {
  const { id: param0, ...queryParams } = params;
  return request(`/admin/users/${param0}/status`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json"
    },
    params: { ...queryParams },
    data: body,
    ...options || {}
  });
}
