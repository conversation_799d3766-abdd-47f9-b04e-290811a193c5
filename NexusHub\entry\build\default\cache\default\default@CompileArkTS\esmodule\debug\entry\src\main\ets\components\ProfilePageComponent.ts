if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ProfilePageComponent_Params {
    isLoading?: boolean;
    isLoggedIn?: boolean;
    deviceUtils?;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import hilog from "@ohos:hilog";
export class ProfilePageComponent extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__isLoggedIn = new ObservedPropertySimplePU(false, this, "isLoggedIn");
        this.deviceUtils = DeviceUtils.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ProfilePageComponent_Params) {
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.isLoggedIn !== undefined) {
            this.isLoggedIn = params.isLoggedIn;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
    }
    updateStateVars(params: ProfilePageComponent_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoggedIn.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__isLoading.aboutToBeDeleted();
        this.__isLoggedIn.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __isLoggedIn: ObservedPropertySimplePU<boolean>;
    get isLoggedIn() {
        return this.__isLoggedIn.get();
    }
    set isLoggedIn(newValue: boolean) {
        this.__isLoggedIn.set(newValue);
    }
    private deviceUtils;
    aboutToAppear() {
        hilog.info(0x0000, 'ProfilePageComponent', '个人中心组件初始化');
    }
    /**
     * 导航到登录页面
     */
    private navigateToLogin(): void {
        this.getUIContext().getRouter().pushUrl({
            url: 'pages/LoginPage'
        }).catch((error: Error) => {
            hilog.error(0x0000, 'ProfilePageComponent', '导航到登录页失败: %{public}s', error.message);
        });
    }
    /**
     * 导航到设置页面
     */
    private navigateToSettings(): void {
        this.getUIContext().getRouter().pushUrl({
            url: 'pages/SettingsPage'
        }).catch((error: Error) => {
            hilog.error(0x0000, 'ProfilePageComponent', '导航到设置页失败: %{public}s', error.message);
        });
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.width('100%');
            Scroll.height('100%');
            Scroll.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Scroll.scrollBar(BarState.Off);
            Scroll.edgeEffect(EdgeEffect.Spring);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 用户信息区域
            Row.create({ space: 16 });
            // 用户信息区域
            Row.width('100%');
            // 用户信息区域
            Row.padding(20);
            // 用户信息区域
            Row.backgroundColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 用户信息区域
            Row.borderRadius(12);
            // 用户信息区域
            Row.margin({ left: 16, right: 16, top: 16 });
            // 用户信息区域
            Row.onClick(() => this.navigateToLogin());
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 头像
            Image.create({ "id": 16777268, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 头像
            Image.width(64);
            // 头像
            Image.height(64);
            // 头像
            Image.borderRadius(32);
            // 头像
            Image.backgroundColor({ "id": 125829510, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 头像
            Image.fillColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 用户信息
            Column.create({ space: 4 });
            // 用户信息
            Column.alignItems(HorizontalAlign.Start);
            // 用户信息
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('未登录');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('点击登录账号');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Text);
        Text.pop();
        // 用户信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 箭头图标
            Image.create({ "id": 16777245, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 箭头图标
            Image.width(20);
            // 箭头图标
            Image.height(20);
            // 箭头图标
            Image.fillColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        // 用户信息区域
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 功能菜单
            Column.create({ space: 0 });
            // 功能菜单
            Column.borderRadius(12);
            // 功能菜单
            Column.clip(true);
            // 功能菜单
            Column.margin({ left: 16, right: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 我的应用
            Row.create({ space: 16 });
            // 我的应用
            Row.width('100%');
            // 我的应用
            Row.height(56);
            // 我的应用
            Row.padding({ left: 20, right: 20 });
            // 我的应用
            Row.backgroundColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777252, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的应用');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM));
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777245, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(20);
            Image.height(20);
            Image.fillColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        // 我的应用
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.color({ "id": 125829159, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Divider.margin({ left: 60 });
        }, Divider);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 我的收藏
            Row.create({ space: 16 });
            // 我的收藏
            Row.width('100%');
            // 我的收藏
            Row.height(56);
            // 我的收藏
            Row.padding({ left: 20, right: 20 });
            // 我的收藏
            Row.backgroundColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777274, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的收藏');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM));
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777245, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(20);
            Image.height(20);
            Image.fillColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        // 我的收藏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.color({ "id": 125829159, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Divider.margin({ left: 60 });
        }, Divider);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 浏览历史
            Row.create({ space: 16 });
            // 浏览历史
            Row.width('100%');
            // 浏览历史
            Row.height(56);
            // 浏览历史
            Row.padding({ left: 20, right: 20 });
            // 浏览历史
            Row.backgroundColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777259, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('浏览历史');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM));
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777245, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(20);
            Image.height(20);
            Image.fillColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        // 浏览历史
        Row.pop();
        // 功能菜单
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 设置菜单
            Column.create();
            // 设置菜单
            Column.borderRadius(12);
            // 设置菜单
            Column.clip(true);
            // 设置菜单
            Column.margin({ left: 16, right: 16, bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 16 });
            Row.width('100%');
            Row.height(56);
            Row.padding({ left: 20, right: 20 });
            Row.backgroundColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Row.onClick(() => this.navigateToSettings());
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777271, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('设置');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM));
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777245, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(20);
            Image.height(20);
            Image.fillColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        Row.pop();
        // 设置菜单
        Column.pop();
        Column.pop();
        Scroll.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
