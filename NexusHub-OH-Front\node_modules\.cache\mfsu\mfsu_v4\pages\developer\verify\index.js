"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { useState, useEffect } from "react";
import {
  Card,
  Form,
  Input,
  Button,
  Upload,
  message,
  Steps,
  Row,
  Col,
  Alert,
  Space,
  Typography,
  Divider,
  Spin,
  Radio
} from "antd";
import { UploadOutlined, CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined, ReloadOutlined } from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-components";
import { postDevelopersVerify, getDevelopersVerifyStatus, getApiV1UploadToken } from "@/services/ant-design-pro/kaifazhe";
import DeveloperVerifyStatus from "@/components/DeveloperVerifyStatus";
import styles from "./index.less";
const { Step } = Steps;
const { Title, Paragraph, Text } = Typography;
const { TextArea } = Input;
const DeveloperVerify = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [statusLoading, setStatusLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  const [verifyStatus, setVerifyStatus] = useState(null);
  const [avatarFileList, setAvatarFileList] = useState([]);
  const [licenseFileList, setLicenseFileList] = useState([]);
  const [idCardFileList, setIdCardFileList] = useState([]);
  const [developerType, setDeveloperType] = useState("individual");
  const fetchVerifyStatus = async () => {
    setStatusLoading(true);
    try {
      const response = await getDevelopersVerifyStatus();
      if (response.code === 200) {
        if (response.data && response.data.verify_status && response.data.verify_status.trim() !== "") {
          setVerifyStatus(response.data);
          if (response.data.verify_status === "pending") {
            setCurrentStep(1);
          } else if (response.data.verify_status === "approved") {
            setCurrentStep(2);
          } else if (response.data.verify_status === "rejected") {
            setCurrentStep(0);
          }
        } else {
          setVerifyStatus(null);
          setCurrentStep(0);
        }
      } else {
        setVerifyStatus(null);
        setCurrentStep(0);
      }
    } catch (error) {
      console.error("\u83B7\u53D6\u8BA4\u8BC1\u72B6\u6001\u5931\u8D25:", error);
      setVerifyStatus(null);
      setCurrentStep(0);
    } finally {
      setStatusLoading(false);
    }
  };
  useEffect(() => {
    fetchVerifyStatus();
  }, []);
  const handleResubmit = () => {
    setCurrentStep(0);
    form.resetFields();
    setAvatarFileList([]);
    setLicenseFileList([]);
    setIdCardFileList([]);
    setDeveloperType("individual");
    message.info("\u8BF7\u91CD\u65B0\u586B\u5199\u8BA4\u8BC1\u4FE1\u606F");
  };
  const handleRefreshStatus = () => {
    fetchVerifyStatus();
    message.info("\u72B6\u6001\u5DF2\u5237\u65B0");
  };
  const customUpload = async (options, fileType) => {
    const { file, onSuccess, onError } = options;
    try {
      const tokenResponse = await getApiV1UploadToken({
        file_type: fileType,
        file_name: file.name
      });
      if (tokenResponse.code === 200 && tokenResponse.data) {
        onSuccess({
          url: tokenResponse.data.file_url
        });
        message.success("\u6587\u4EF6\u4E0A\u4F20\u6210\u529F");
      } else {
        onError(new Error("\u83B7\u53D6\u4E0A\u4F20\u51ED\u8BC1\u5931\u8D25"));
      }
    } catch (error) {
      onError(error);
      message.error("\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25");
    }
  };
  const handleUploadChange = (info, type) => {
    const { fileList } = info;
    if (type === "avatar") {
      setAvatarFileList(fileList);
    } else if (type === "license") {
      setLicenseFileList(fileList);
    } else if (type === "identity") {
      setIdCardFileList(fileList);
    }
  };
  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      if (!idCardFileList.length || !idCardFileList[0]?.response?.url) {
        message.error("\u8BF7\u4E0A\u4F20\u8EAB\u4EFD\u8BC1\u7167\u7247");
        setLoading(false);
        return;
      }
      if (values.developer_type === "enterprise" && (!licenseFileList.length || !licenseFileList[0]?.response?.url)) {
        message.error("\u4F01\u4E1A\u5F00\u53D1\u8005\u8BF7\u4E0A\u4F20\u8425\u4E1A\u6267\u7167");
        setLoading(false);
        return;
      }
      const avatarUrl = avatarFileList[0]?.response?.url || "";
      const licenseUrl = licenseFileList[0]?.response?.url || "";
      const idCardUrl = idCardFileList[0]?.response?.url || "";
      const submitData = {
        ...values,
        developer_avatar: avatarUrl,
        business_license: licenseUrl,
        identity_card: idCardUrl
      };
      const response = await postDevelopersVerify(submitData);
      if (response.code === 200) {
        message.success("\u5F00\u53D1\u8005\u8BA4\u8BC1\u7533\u8BF7\u63D0\u4EA4\u6210\u529F\uFF0C\u8BF7\u7B49\u5F85\u5BA1\u6838");
        setCurrentStep(1);
        await fetchVerifyStatus();
        form.resetFields();
        setAvatarFileList([]);
        setLicenseFileList([]);
        setIdCardFileList([]);
      } else {
        message.error(response.message || "\u63D0\u4EA4\u5931\u8D25");
      }
    } catch (error) {
      console.error("\u63D0\u4EA4\u8BA4\u8BC1\u7533\u8BF7\u5931\u8D25:", error);
      message.error("\u63D0\u4EA4\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5");
    } finally {
      setLoading(false);
    }
  };
  const renderStatusIcon = (status) => {
    switch (status) {
      case "pending":
        return /* @__PURE__ */ jsx(ClockCircleOutlined, { style: { color: "#faad14" } });
      case "approved":
        return /* @__PURE__ */ jsx(CheckCircleOutlined, { style: { color: "#52c41a" } });
      case "rejected":
        return /* @__PURE__ */ jsx(CloseCircleOutlined, { style: { color: "#ff4d4f" } });
      default:
        return null;
    }
  };
  const renderVerifyForm = () => /* @__PURE__ */ jsx(Card, { title: "\u5F00\u53D1\u8005\u8BA4\u8BC1\u7533\u8BF7", className: styles.verifyCard, children: /* @__PURE__ */ jsxs(
    Form,
    {
      form,
      layout: "vertical",
      onFinish: handleSubmit,
      initialValues: {
        developer_type: "individual",
        developer_name: "",
        company_name: "",
        contact_email: "",
        contact_phone: "",
        website: "",
        description: "",
        developer_address: ""
      },
      onValuesChange: (changedValues) => {
        if (changedValues.developer_type) {
          setDeveloperType(changedValues.developer_type);
        }
      },
      children: [
        /* @__PURE__ */ jsxs(Card, { size: "small", style: { marginBottom: 24, backgroundColor: "#fafafa" }, children: [
          /* @__PURE__ */ jsx(
            Form.Item,
            {
              name: "developer_type",
              label: "\u5F00\u53D1\u8005\u7C7B\u578B",
              rules: [{ required: true, message: "\u8BF7\u9009\u62E9\u5F00\u53D1\u8005\u7C7B\u578B" }],
              children: /* @__PURE__ */ jsxs(Radio.Group, { children: [
                /* @__PURE__ */ jsx(Radio, { value: "individual", children: "\u4E2A\u4EBA\u5F00\u53D1\u8005" }),
                /* @__PURE__ */ jsx(Radio, { value: "enterprise", children: "\u4F01\u4E1A\u5F00\u53D1\u8005" })
              ] })
            }
          ),
          /* @__PURE__ */ jsx(
            Alert,
            {
              message: developerType === "individual" ? "\u4E2A\u4EBA\u5F00\u53D1\u8005\u8BF4\u660E" : "\u4F01\u4E1A\u5F00\u53D1\u8005\u8BF4\u660E",
              description: developerType === "individual" ? "\u4E2A\u4EBA\u5F00\u53D1\u8005\u9002\u7528\u4E8E\u4E2A\u4EBA\u72EC\u7ACB\u5F00\u53D1\u8005\uFF0C\u9700\u8981\u63D0\u4F9B\u4E2A\u4EBA\u8EAB\u4EFD\u8BC1\u660E\u548C\u76F8\u5173\u8D44\u6599\u3002" : "\u4F01\u4E1A\u5F00\u53D1\u8005\u9002\u7528\u4E8E\u516C\u53F8\u6216\u56E2\u961F\uFF0C\u9700\u8981\u63D0\u4F9B\u8425\u4E1A\u6267\u7167\u3001\u4F01\u4E1A\u4FE1\u606F\u7B49\u76F8\u5173\u8D44\u6599\u3002",
              type: "info",
              showIcon: true,
              style: { marginTop: 12 }
            }
          )
        ] }),
        /* @__PURE__ */ jsxs(Row, { gutter: 24, children: [
          /* @__PURE__ */ jsx(Col, { span: 12, children: /* @__PURE__ */ jsx(
            Form.Item,
            {
              name: "developer_name",
              label: developerType === "individual" ? "\u5F00\u53D1\u8005\u59D3\u540D" : "\u8054\u7CFB\u4EBA\u59D3\u540D",
              rules: [
                { required: true, message: `\u8BF7\u8F93\u5165${developerType === "individual" ? "\u5F00\u53D1\u8005\u59D3\u540D" : "\u8054\u7CFB\u4EBA\u59D3\u540D"}` },
                { min: 2, max: 100, message: "\u59D3\u540D\u957F\u5EA6\u4E3A2-100\u4E2A\u5B57\u7B26" }
              ],
              children: /* @__PURE__ */ jsx(Input, { placeholder: `\u8BF7\u8F93\u5165${developerType === "individual" ? "\u5F00\u53D1\u8005\u59D3\u540D" : "\u8054\u7CFB\u4EBA\u59D3\u540D"}` })
            }
          ) }),
          /* @__PURE__ */ jsx(Col, { span: 12, children: /* @__PURE__ */ jsx(
            Form.Item,
            {
              name: "company_name",
              label: developerType === "individual" ? "\u5DE5\u4F5C\u5355\u4F4D" : "\u516C\u53F8\u540D\u79F0",
              rules: developerType === "enterprise" ? [
                { required: true, message: "\u8BF7\u8F93\u5165\u516C\u53F8\u540D\u79F0" },
                { min: 2, max: 200, message: "\u516C\u53F8\u540D\u79F0\u957F\u5EA6\u4E3A2-200\u4E2A\u5B57\u7B26" }
              ] : [],
              children: /* @__PURE__ */ jsx(Input, { placeholder: developerType === "individual" ? "\u8BF7\u8F93\u5165\u5DE5\u4F5C\u5355\u4F4D\uFF08\u53EF\u9009\uFF09" : "\u8BF7\u8F93\u5165\u516C\u53F8\u540D\u79F0" })
            }
          ) })
        ] }),
        /* @__PURE__ */ jsxs(Row, { gutter: 24, children: [
          /* @__PURE__ */ jsx(Col, { span: 12, children: /* @__PURE__ */ jsx(
            Form.Item,
            {
              name: "contact_email",
              label: "\u8054\u7CFB\u90AE\u7BB1",
              rules: [
                { required: true, message: "\u8BF7\u8F93\u5165\u8054\u7CFB\u90AE\u7BB1" },
                { type: "email", message: "\u8BF7\u8F93\u5165\u6709\u6548\u7684\u90AE\u7BB1\u5730\u5740" }
              ],
              children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u8054\u7CFB\u90AE\u7BB1" })
            }
          ) }),
          /* @__PURE__ */ jsx(Col, { span: 12, children: /* @__PURE__ */ jsx(
            Form.Item,
            {
              name: "contact_phone",
              label: "\u8054\u7CFB\u7535\u8BDD",
              rules: [
                { required: true, message: "\u8BF7\u8F93\u5165\u8054\u7CFB\u7535\u8BDD" },
                { pattern: /^1[3-9]\d{9}$/, message: "\u8BF7\u8F93\u5165\u6709\u6548\u7684\u624B\u673A\u53F7\u7801" }
              ],
              children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u8054\u7CFB\u7535\u8BDD" })
            }
          ) })
        ] }),
        /* @__PURE__ */ jsxs(Row, { gutter: 24, children: [
          /* @__PURE__ */ jsx(Col, { span: 12, children: /* @__PURE__ */ jsx(
            Form.Item,
            {
              name: "website",
              label: "\u4E2A\u4EBA\u7F51\u7AD9",
              rules: [
                { type: "url", message: "\u8BF7\u8F93\u5165\u6709\u6548\u7684\u7F51\u7AD9\u5730\u5740" }
              ],
              children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u4E2A\u4EBA\u7F51\u7AD9\uFF08\u53EF\u9009\uFF09" })
            }
          ) }),
          /* @__PURE__ */ jsx(Col, { span: 12, children: /* @__PURE__ */ jsx(
            Form.Item,
            {
              name: "developer_address",
              label: "\u8054\u7CFB\u5730\u5740",
              rules: [
                { required: true, message: "\u8BF7\u8F93\u5165\u8054\u7CFB\u5730\u5740" },
                { min: 5, max: 255, message: "\u8054\u7CFB\u5730\u5740\u957F\u5EA6\u4E3A5-255\u4E2A\u5B57\u7B26" }
              ],
              children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u8054\u7CFB\u5730\u5740" })
            }
          ) })
        ] }),
        /* @__PURE__ */ jsx(
          Form.Item,
          {
            name: "description",
            label: developerType === "individual" ? "\u4E2A\u4EBA\u7B80\u4ECB" : "\u4F01\u4E1A\u7B80\u4ECB",
            rules: [
              { required: true, message: `\u8BF7\u8F93\u5165${developerType === "individual" ? "\u4E2A\u4EBA\u7B80\u4ECB" : "\u4F01\u4E1A\u7B80\u4ECB"}` },
              { min: 10, max: 1e3, message: "\u7B80\u4ECB\u957F\u5EA6\u4E3A10-1000\u4E2A\u5B57\u7B26" }
            ],
            children: /* @__PURE__ */ jsx(
              TextArea,
              {
                rows: 4,
                placeholder: developerType === "individual" ? "\u8BF7\u8BE6\u7EC6\u63CF\u8FF0\u60A8\u7684\u5F00\u53D1\u7ECF\u9A8C\u3001\u6280\u80FD\u4E13\u957F\u3001\u9879\u76EE\u7ECF\u5386\u7B49\uFF0810-1000\u5B57\u7B26\uFF09" : "\u8BF7\u8BE6\u7EC6\u63CF\u8FF0\u4F01\u4E1A\u7684\u4E1A\u52A1\u8303\u56F4\u3001\u6280\u672F\u5B9E\u529B\u3001\u56E2\u961F\u89C4\u6A21\u7B49\uFF0810-1000\u5B57\u7B26\uFF09"
              }
            )
          }
        ),
        /* @__PURE__ */ jsx(Divider, { children: "\u4E0A\u4F20\u8BA4\u8BC1\u6750\u6599" }),
        /* @__PURE__ */ jsx(
          Alert,
          {
            message: "\u4E0A\u4F20\u8981\u6C42",
            description: developerType === "individual" ? "\u4E2A\u4EBA\u5F00\u53D1\u8005\u9700\u8981\u4E0A\u4F20\uFF1A\u4E2A\u4EBA\u5934\u50CF\uFF08\u53EF\u9009\uFF09\u3001\u8EAB\u4EFD\u8BC1\u6B63\u9762\u7167\u7247\uFF08\u5FC5\u9700\uFF09" : "\u4F01\u4E1A\u5F00\u53D1\u8005\u9700\u8981\u4E0A\u4F20\uFF1A\u4F01\u4E1ALogo\uFF08\u53EF\u9009\uFF09\u3001\u8425\u4E1A\u6267\u7167\uFF08\u5FC5\u9700\uFF09\u3001\u8EAB\u4EFD\u8BC1\u6B63\u9762\u7167\u7247\uFF08\u5FC5\u9700\uFF09",
            type: "warning",
            showIcon: true,
            style: { marginBottom: 16 }
          }
        ),
        /* @__PURE__ */ jsxs(Row, { gutter: 24, children: [
          /* @__PURE__ */ jsx(Col, { span: 8, children: /* @__PURE__ */ jsxs(Form.Item, { label: developerType === "individual" ? "\u4E2A\u4EBA\u5934\u50CF" : "\u4F01\u4E1ALogo", children: [
            /* @__PURE__ */ jsx(
              Upload,
              {
                customRequest: (options) => customUpload(options, "avatar"),
                fileList: avatarFileList,
                onChange: (info) => handleUploadChange(info, "avatar"),
                maxCount: 1,
                accept: "image/*",
                listType: "picture-card",
                children: avatarFileList.length === 0 && /* @__PURE__ */ jsxs("div", { children: [
                  /* @__PURE__ */ jsx(UploadOutlined, {}),
                  /* @__PURE__ */ jsx("div", { style: { marginTop: 8 }, children: developerType === "individual" ? "\u4E0A\u4F20\u5934\u50CF" : "\u4E0A\u4F20Logo" })
                ] })
              }
            ),
            /* @__PURE__ */ jsx(Text, { type: "secondary", style: { fontSize: 12 }, children: "\u652F\u6301 JPG\u3001PNG \u683C\u5F0F\uFF0C\u6587\u4EF6\u5927\u5C0F\u4E0D\u8D85\u8FC7 2MB\uFF08\u53EF\u9009\uFF09" })
          ] }) }),
          developerType === "enterprise" && /* @__PURE__ */ jsx(Col, { span: 8, children: /* @__PURE__ */ jsxs(Form.Item, { label: "\u8425\u4E1A\u6267\u7167", required: true, children: [
            /* @__PURE__ */ jsx(
              Upload,
              {
                customRequest: (options) => customUpload(options, "license"),
                fileList: licenseFileList,
                onChange: (info) => handleUploadChange(info, "license"),
                maxCount: 1,
                accept: "image/*",
                listType: "picture-card",
                children: licenseFileList.length === 0 && /* @__PURE__ */ jsxs("div", { children: [
                  /* @__PURE__ */ jsx(UploadOutlined, {}),
                  /* @__PURE__ */ jsx("div", { style: { marginTop: 8 }, children: "\u4E0A\u4F20\u6267\u7167" })
                ] })
              }
            ),
            /* @__PURE__ */ jsx(Text, { type: "secondary", style: { fontSize: 12 }, children: "\u8BF7\u4E0A\u4F20\u6E05\u6670\u7684\u8425\u4E1A\u6267\u7167\u7167\u7247" })
          ] }) }),
          /* @__PURE__ */ jsx(Col, { span: 8, children: /* @__PURE__ */ jsxs(Form.Item, { label: "\u8EAB\u4EFD\u8BC1\u660E", required: true, children: [
            /* @__PURE__ */ jsx(
              Upload,
              {
                customRequest: (options) => customUpload(options, "identity"),
                fileList: idCardFileList,
                onChange: (info) => handleUploadChange(info, "identity"),
                maxCount: 1,
                accept: "image/*",
                listType: "picture-card",
                children: idCardFileList.length === 0 && /* @__PURE__ */ jsxs("div", { children: [
                  /* @__PURE__ */ jsx(UploadOutlined, {}),
                  /* @__PURE__ */ jsx("div", { style: { marginTop: 8 }, children: "\u4E0A\u4F20\u8EAB\u4EFD\u8BC1" })
                ] })
              }
            ),
            /* @__PURE__ */ jsx(Text, { type: "secondary", style: { fontSize: 12 }, children: "\u8BF7\u4E0A\u4F20\u8EAB\u4EFD\u8BC1\u6B63\u9762\u7167\u7247" })
          ] }) })
        ] }),
        /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsxs(Space, { children: [
          /* @__PURE__ */ jsx(Button, { type: "primary", htmlType: "submit", loading, size: "large", children: "\u63D0\u4EA4\u8BA4\u8BC1\u7533\u8BF7" }),
          /* @__PURE__ */ jsx(Button, { onClick: () => form.resetFields(), size: "large", children: "\u91CD\u7F6E\u8868\u5355" })
        ] }) })
      ]
    }
  ) });
  const renderVerifyStatus = () => {
    if (!verifyStatus) return null;
    const { verify_status, verify_reason, submitted_at, verified_at } = verifyStatus;
    return /* @__PURE__ */ jsx(Card, { title: "\u8BA4\u8BC1\u72B6\u6001", className: styles.statusCard, children: /* @__PURE__ */ jsxs("div", { className: styles.statusContent, children: [
      renderStatusIcon(verify_status),
      /* @__PURE__ */ jsxs("div", { className: styles.statusInfo, children: [
        /* @__PURE__ */ jsxs(Title, { level: 4, children: [
          verify_status === "pending" && "\u8BA4\u8BC1\u5BA1\u6838\u4E2D",
          verify_status === "approved" && "\u8BA4\u8BC1\u5DF2\u901A\u8FC7",
          verify_status === "rejected" && "\u8BA4\u8BC1\u88AB\u62D2\u7EDD"
        ] }),
        verify_status === "pending" && /* @__PURE__ */ jsxs(Paragraph, { children: [
          "\u60A8\u7684\u5F00\u53D1\u8005\u8BA4\u8BC1\u7533\u8BF7\u5DF2\u63D0\u4EA4\uFF0C\u6211\u4EEC\u5C06\u5728 1-3 \u4E2A\u5DE5\u4F5C\u65E5\u5185\u5B8C\u6210\u5BA1\u6838\u3002",
          /* @__PURE__ */ jsx("br", {}),
          "\u63D0\u4EA4\u65F6\u95F4\uFF1A",
          submitted_at
        ] }),
        verify_status === "approved" && /* @__PURE__ */ jsxs(Paragraph, { children: [
          "\u606D\u559C\uFF01\u60A8\u7684\u5F00\u53D1\u8005\u8BA4\u8BC1\u5DF2\u901A\u8FC7\u5BA1\u6838\uFF0C\u73B0\u5728\u53EF\u4EE5\u53D1\u5E03\u5E94\u7528\u4E86\u3002",
          /* @__PURE__ */ jsx("br", {}),
          "\u5BA1\u6838\u901A\u8FC7\u65F6\u95F4\uFF1A",
          verified_at
        ] }),
        verify_status === "rejected" && /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsxs(Paragraph, { children: [
            "\u5F88\u62B1\u6B49\uFF0C\u60A8\u7684\u5F00\u53D1\u8005\u8BA4\u8BC1\u7533\u8BF7\u88AB\u62D2\u7EDD\u3002",
            /* @__PURE__ */ jsx("br", {}),
            "\u62D2\u7EDD\u65F6\u95F4\uFF1A",
            verified_at
          ] }),
          verify_reason && /* @__PURE__ */ jsx(
            Alert,
            {
              message: "\u62D2\u7EDD\u539F\u56E0",
              description: verify_reason,
              type: "error",
              showIcon: true,
              style: { marginBottom: 16 }
            }
          ),
          /* @__PURE__ */ jsx(Button, { type: "primary", onClick: () => setCurrentStep(0), children: "\u91CD\u65B0\u7533\u8BF7" })
        ] })
      ] })
    ] }) });
  };
  if (statusLoading) {
    return /* @__PURE__ */ jsx(
      PageContainer,
      {
        title: "\u5F00\u53D1\u8005\u8BA4\u8BC1",
        subTitle: "\u6210\u4E3A\u8BA4\u8BC1\u5F00\u53D1\u8005\uFF0C\u53D1\u5E03\u60A8\u7684\u5E94\u7528\u5230 NexusHub-OH \u5E94\u7528\u5546\u5E97",
        children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs("div", { style: { textAlign: "center", padding: "50px 0" }, children: [
          /* @__PURE__ */ jsx(Spin, { size: "large" }),
          /* @__PURE__ */ jsx("div", { style: { marginTop: 16 }, children: "\u6B63\u5728\u52A0\u8F7D\u8BA4\u8BC1\u72B6\u6001..." })
        ] }) })
      }
    );
  }
  return /* @__PURE__ */ jsx(
    PageContainer,
    {
      title: "\u5F00\u53D1\u8005\u8BA4\u8BC1",
      subTitle: "\u6210\u4E3A\u8BA4\u8BC1\u5F00\u53D1\u8005\uFF0C\u53D1\u5E03\u60A8\u7684\u5E94\u7528\u5230 NexusHub-OH \u5E94\u7528\u5546\u5E97",
      extra: [
        /* @__PURE__ */ jsx(
          Button,
          {
            icon: /* @__PURE__ */ jsx(ReloadOutlined, {}),
            onClick: handleRefreshStatus,
            children: "\u5237\u65B0\u72B6\u6001"
          },
          "refresh"
        )
      ],
      children: /* @__PURE__ */ jsxs("div", { className: styles.container, children: [
        /* @__PURE__ */ jsx(Card, { className: styles.stepsCard, children: /* @__PURE__ */ jsxs(Steps, { current: currentStep, className: styles.steps, children: [
          /* @__PURE__ */ jsx(Step, { title: "\u63D0\u4EA4\u7533\u8BF7", description: "\u586B\u5199\u8BA4\u8BC1\u4FE1\u606F" }),
          /* @__PURE__ */ jsx(Step, { title: "\u5BA1\u6838\u4E2D", description: "\u7B49\u5F85\u5E73\u53F0\u5BA1\u6838" }),
          /* @__PURE__ */ jsx(Step, { title: "\u8BA4\u8BC1\u5B8C\u6210", description: "\u5F00\u59CB\u53D1\u5E03\u5E94\u7528" })
        ] }) }),
        verifyStatus && /* @__PURE__ */ jsx(
          DeveloperVerifyStatus,
          {
            data: verifyStatus,
            onResubmit: handleResubmit,
            showResubmitButton: verifyStatus.verify_status === "rejected"
          }
        ),
        (!verifyStatus || !verifyStatus.verify_status || verifyStatus.verify_status === "rejected") && renderVerifyForm()
      ] })
    }
  );
};
export default DeveloperVerify;
