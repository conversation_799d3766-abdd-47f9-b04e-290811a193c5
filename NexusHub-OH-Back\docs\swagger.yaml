basePath: /api/v1
definitions:
  api.AddAppToCollectionRequest:
    properties:
      application_id:
        type: integer
      sort_order:
        type: integer
    required:
    - application_id
    type: object
  api.AdminCreateUserRequest:
    properties:
      email:
        type: string
      password:
        maxLength: 50
        minLength: 6
        type: string
      phone:
        type: string
      role:
        enum:
        - user
        - developer
        - operator
        - reviewer
        - admin
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - email
    - password
    - role
    - username
    type: object
  api.AdminVerifyRequest:
    properties:
      verify_reason:
        maxLength: 255
        type: string
      verify_status:
        enum:
        - pending
        - approved
        - rejected
        type: string
    required:
    - verify_status
    type: object
  api.AnonymousDownloadRequest:
    properties:
      device_model:
        type: string
      device_os:
        type: string
      device_type:
        type: string
    type: object
  api.AppDetailsResponse:
    properties:
      average_rating:
        type: number
      banner_image:
        type: string
      category:
        type: string
      company_name:
        type: string
      created_at:
        type: string
      current_version:
        type: string
      description:
        type: string
      developer_id:
        type: integer
      developer_name:
        type: string
      download_count:
        type: integer
      icon:
        type: string
      id:
        type: integer
      is_editor:
        type: boolean
      is_featured:
        type: boolean
      is_top:
        type: boolean
      is_verified:
        type: boolean
      min_open_harmony_os_ver:
        type: string
      name:
        type: string
      package:
        type: string
      package_url:
        type: string
      privacy_url:
        type: string
      rating_count:
        type: integer
      reject_reason:
        type: string
      release_date:
        type: string
      short_desc:
        type: string
      size:
        type: integer
      status:
        type: string
      tags:
        type: string
      updated_at:
        type: string
      website:
        type: string
      website_url:
        type: string
    type: object
  api.AppDownloadStatsResponse:
    properties:
      daily_stats:
        items:
          $ref: '#/definitions/models.AppDownloadStats'
        type: array
      device_stats:
        items:
          $ref: '#/definitions/models.DeviceDownloadStats'
        type: array
      total_downloads:
        type: integer
    type: object
  api.AppOverallStatsResponse:
    properties:
      average_rating:
        type: number
      category_stats:
        items:
          $ref: '#/definitions/api.CategoryStats'
        type: array
      download_trends:
        items:
          $ref: '#/definitions/api.DownloadTrendStats'
        type: array
      status_stats:
        items:
          $ref: '#/definitions/api.StatusStats'
        type: array
      top_apps:
        items:
          $ref: '#/definitions/api.TopAppStats'
        type: array
      total_apps:
        type: integer
      total_downloads:
        type: integer
    type: object
  api.AppResponse:
    description: 应用响应
    properties:
      average_rating:
        type: number
      category_id:
        type: integer
      created_at:
        type: string
      current_versions:
        type: string
      deleted_at:
        type: string
      description:
        type: string
      developer_id:
        type: integer
      download_count:
        type: integer
      icon:
        type: string
      id:
        type: integer
      min_android_sdk:
        type: integer
      name:
        type: string
      package_name:
        type: string
      review_count:
        type: integer
      status:
        type: string
      updated_at:
        type: string
    type: object
  api.AppReviewRequest:
    properties:
      reason:
        type: string
      status:
        enum:
        - approved
        - rejected
        type: string
    required:
    - status
    type: object
  api.AppTagRequest:
    properties:
      tag_ids:
        items:
          type: integer
        type: array
    required:
    - tag_ids
    type: object
  api.AppVersionRequest:
    properties:
      change_log:
        type: string
      incremental_update:
        type: boolean
      min_open_harmony_os_ver:
        type: string
      package_url:
        type: string
      size:
        minimum: 1
        type: integer
      version_code:
        minimum: 0.1
        type: number
      version_name:
        maxLength: 50
        minLength: 1
        type: string
    required:
    - min_open_harmony_os_ver
    - package_url
    - size
    - version_code
    - version_name
    type: object
  api.CategoryDetailResponse:
    description: 分类详情与子分类
    properties:
      category:
        allOf:
        - $ref: '#/definitions/api.CategoryResponse'
        description: 分类信息
      subcategories:
        description: 子分类列表
        items:
          $ref: '#/definitions/api.CategoryResponse'
        type: array
    type: object
  api.CategoryResponse:
    description: 分类信息响应
    properties:
      created_at:
        description: 创建时间
        type: string
      deleted_at:
        description: 删除时间
        type: string
      description:
        description: 分类描述
        type: string
      icon:
        description: 分类图标URL
        type: string
      id:
        description: 分类ID
        type: integer
      is_active:
        description: 是否启用
        type: boolean
      name:
        description: 分类名称
        type: string
      parent_id:
        description: 父分类ID
        type: integer
      sort_order:
        description: 排序权重
        type: integer
      updated_at:
        description: 更新时间
        type: string
    type: object
  api.CategoryStats:
    properties:
      category:
        type: string
      count:
        type: integer
    type: object
  api.CityResponse:
    properties:
      key:
        type: string
      label:
        type: string
    type: object
  api.CountryResponse:
    properties:
      key:
        type: string
      label:
        type: string
    type: object
  api.CreateAppRequest:
    properties:
      banner_image:
        type: string
      category:
        type: string
      description:
        type: string
      icon:
        type: string
      min_open_harmony_os_ver:
        type: string
      name:
        maxLength: 100
        minLength: 2
        type: string
      package:
        maxLength: 100
        minLength: 3
        type: string
      privacy_url:
        type: string
      short_desc:
        maxLength: 200
        type: string
      tags:
        maxLength: 255
        type: string
      website_url:
        type: string
    required:
    - category
    - icon
    - min_open_harmony_os_ver
    - name
    - package
    type: object
  api.CreateCategoryRequest:
    properties:
      description:
        maxLength: 1000
        type: string
      icon:
        maxLength: 255
        type: string
      name:
        maxLength: 50
        type: string
      parent_id:
        type: integer
      sort_order:
        type: integer
    required:
    - name
    type: object
  api.CreateFeaturedCollectionRequest:
    properties:
      cover_image:
        type: string
      description:
        type: string
      icon:
        type: string
      is_active:
        type: boolean
      is_public:
        type: boolean
      sort_order:
        type: integer
      title:
        maxLength: 100
        minLength: 1
        type: string
    required:
    - title
    type: object
  api.CreateReviewRequest:
    properties:
      app_version:
        maxLength: 50
        type: string
      content:
        maxLength: 1000
        minLength: 1
        type: string
      rating:
        maximum: 5
        minimum: 1
        type: integer
      title:
        maxLength: 100
        type: string
    required:
    - app_version
    - content
    - rating
    - title
    type: object
  api.CreateTagRequest:
    properties:
      color:
        maxLength: 20
        type: string
      description:
        maxLength: 1000
        type: string
      name:
        maxLength: 50
        type: string
    required:
    - name
    type: object
  api.DevResponseRequest:
    properties:
      content:
        maxLength: 1000
        minLength: 1
        type: string
    required:
    - content
    type: object
  api.DeveloperVerifyRequest:
    properties:
      business_license:
        maxLength: 500
        type: string
      company_name:
        maxLength: 100
        type: string
      contact_email:
        type: string
      contact_phone:
        type: string
      description:
        maxLength: 1000
        minLength: 10
        type: string
      developer_address:
        maxLength: 255
        minLength: 5
        type: string
      developer_avatar:
        maxLength: 500
        type: string
      developer_name:
        maxLength: 100
        minLength: 2
        type: string
      identity_card:
        maxLength: 500
        type: string
      website:
        maxLength: 255
        type: string
    required:
    - contact_email
    - contact_phone
    - description
    - developer_address
    - developer_name
    - identity_card
    type: object
  api.DeveloperVerifyResponse:
    properties:
      business_license:
        type: string
      company_name:
        type: string
      contact_email:
        type: string
      contact_phone:
        type: string
      description:
        type: string
      developer_address:
        type: string
      developer_avatar:
        type: string
      developer_name:
        type: string
      id:
        type: integer
      identity_card:
        type: string
      submitted_at:
        type: string
      username:
        type: string
      verified_at:
        type: string
      verify_reason:
        type: string
      verify_status:
        type: string
      website:
        type: string
    type: object
  api.DistrictResponse:
    properties:
      key:
        type: string
      label:
        type: string
    type: object
  api.DownloadRecordResponse:
    properties:
      app_icon:
        type: string
      app_name:
        type: string
      application_id:
        type: integer
      created_at:
        type: string
      device_model:
        type: string
      device_os:
        type: string
      device_type:
        type: string
      id:
        type: integer
      status:
        type: string
      user_id:
        type: integer
      version_name:
        type: string
    type: object
  api.DownloadTrendStats:
    properties:
      date:
        type: string
      downloads:
        type: integer
    type: object
  api.ErrorResponse:
    description: 错误响应
    properties:
      code:
        example: 400
        type: integer
      message:
        example: 请求参数错误
        type: string
    type: object
  api.FeaturedCollectionResponse:
    properties:
      apps:
        items:
          $ref: '#/definitions/models.Application'
        type: array
      cover_image:
        type: string
      created_at:
        type: string
      creator:
        $ref: '#/definitions/models.User'
      creator_id:
        type: integer
      description:
        type: string
      icon:
        type: string
      id:
        type: integer
      is_active:
        type: boolean
      is_public:
        type: boolean
      sort_order:
        type: integer
      title:
        type: string
      updated_at:
        type: string
    type: object
  api.GeographicResponse:
    properties:
      key:
        type: string
      label:
        type: string
    type: object
  api.GetNotificationsResponse:
    properties:
      notifications:
        items:
          $ref: '#/definitions/models.Notification'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total:
        type: integer
      unread_count:
        type: integer
    type: object
  api.LoginRequest:
    properties:
      password:
        type: string
      username_or_email:
        type: string
    required:
    - password
    - username_or_email
    type: object
  api.MarkAsReadRequest:
    properties:
      notification_id:
        type: integer
    required:
    - notification_id
    type: object
  api.MonitoringData:
    properties:
      summary:
        $ref: '#/definitions/models.MonitoringSummary'
      system_status:
        additionalProperties: true
        type: object
    type: object
  api.PageResponse:
    properties:
      code:
        example: 200
        type: integer
      data: {}
      message:
        example: 操作成功
        type: string
      page:
        example: 1
        type: integer
      page_size:
        example: 20
        type: integer
      total:
        example: 100
        type: integer
    type: object
  api.PagedResponse:
    properties:
      data:
        description: 数据
      page:
        description: 当前页码
        type: integer
      page_size:
        description: 每页数量
        type: integer
      total:
        description: 总记录数
        type: integer
    type: object
  api.PaginatedData:
    properties:
      list: {}
      page:
        type: integer
      page_size:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  api.ProvinceResponse:
    properties:
      key:
        type: string
      label:
        type: string
    type: object
  api.RegisterRequest:
    properties:
      email:
        type: string
      password:
        maxLength: 50
        minLength: 6
        type: string
      phone:
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - email
    - password
    - username
    type: object
  api.Response:
    properties:
      code:
        example: 200
        type: integer
      data: {}
      message:
        example: 操作成功
        type: string
    type: object
  api.ReviewResponse:
    properties:
      app_name:
        type: string
      app_version:
        type: string
      application_id:
        type: integer
      avatar:
        type: string
      content:
        type: string
      created_at:
        type: string
      dev_response:
        type: string
      id:
        type: integer
      like_count:
        type: integer
      rating:
        type: integer
      status:
        type: string
      title:
        type: string
      updated_at:
        type: string
      user_id:
        type: integer
      username:
        type: string
    type: object
  api.SendEmailRequest:
    properties:
      body:
        type: string
      is_html:
        type: boolean
      subject:
        type: string
      to:
        type: string
    required:
    - body
    - subject
    - to
    type: object
  api.SendNotificationRequest:
    properties:
      content:
        type: string
      title:
        type: string
      type:
        enum:
        - info
        - warning
        - error
        - success
        type: string
      user_id:
        type: integer
    required:
    - content
    - title
    - type
    - user_id
    type: object
  api.StatusStats:
    properties:
      count:
        type: integer
      status:
        type: string
    type: object
  api.StreetResponse:
    properties:
      key:
        type: string
      label:
        type: string
    type: object
  api.SuccessResponse:
    description: 成功响应
    properties:
      code:
        example: 200
        type: integer
      data: {}
      message:
        example: 操作成功
        type: string
    type: object
  api.TagAppCountResponse:
    description: 标签及应用数量响应
    properties:
      app_count:
        type: integer
      tag:
        $ref: '#/definitions/api.TagResponse'
    type: object
  api.TagResponse:
    description: 标签响应
    properties:
      color:
        type: string
      created_at:
        type: string
      deleted_at:
        type: string
      description:
        type: string
      id:
        type: integer
      is_active:
        type: boolean
      name:
        type: string
      updated_at:
        type: string
    type: object
  api.TopAppStats:
    properties:
      category:
        type: string
      download_count:
        type: integer
      icon:
        type: string
      id:
        type: integer
      name:
        type: string
      rating:
        type: number
    type: object
  api.UpdateAppRequest:
    properties:
      banner_image:
        type: string
      category:
        type: string
      description:
        type: string
      icon:
        type: string
      min_open_harmony_os_ver:
        type: string
      name:
        maxLength: 100
        minLength: 2
        type: string
      package_url:
        type: string
      privacy_url:
        type: string
      screenshots:
        items:
          type: string
        type: array
      short_desc:
        maxLength: 200
        type: string
      tags:
        maxLength: 255
        type: string
      website_url:
        type: string
    type: object
  api.UpdateCategoryRequest:
    properties:
      description:
        maxLength: 1000
        type: string
      icon:
        maxLength: 255
        type: string
      is_active:
        type: boolean
      name:
        maxLength: 50
        type: string
      parent_id:
        type: integer
      sort_order:
        type: integer
    type: object
  api.UpdateFeaturedCollectionRequest:
    properties:
      cover_image:
        type: string
      description:
        type: string
      icon:
        type: string
      is_active:
        type: boolean
      is_public:
        type: boolean
      sort_order:
        type: integer
      title:
        maxLength: 100
        minLength: 1
        type: string
    type: object
  api.UpdateNotificationSettingsRequest:
    properties:
      app_review_email_enabled:
        type: boolean
      app_review_web_enabled:
        type: boolean
      developer_verify_email_enabled:
        type: boolean
      developer_verify_web_enabled:
        type: boolean
      email_notification_enabled:
        type: boolean
      system_email_enabled:
        type: boolean
      system_web_enabled:
        type: boolean
      version_update_email_enabled:
        type: boolean
      version_update_web_enabled:
        type: boolean
      web_notification_enabled:
        type: boolean
    type: object
  api.UpdateProfileRequest:
    properties:
      address:
        type: string
      avatar:
        type: string
      city:
        type: string
      company_name:
        type: string
      description:
        description: 基本信息字段
        type: string
      developer_info:
        properties:
          company_name:
            type: string
          description:
            type: string
          developer_name:
            type: string
          website:
            type: string
        type: object
      district:
        type: string
      email:
        type: string
      is_developer:
        type: boolean
      new_password:
        maxLength: 50
        minLength: 6
        type: string
      old_password:
        type: string
      phone:
        type: string
      province:
        type: string
      street:
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    type: object
  api.UpdateTagRequest:
    properties:
      color:
        maxLength: 20
        type: string
      description:
        maxLength: 1000
        type: string
      is_active:
        type: boolean
      name:
        maxLength: 50
        type: string
    type: object
  api.UpdateUserRoleRequest:
    properties:
      role:
        enum:
        - user
        - developer
        - operator
        - reviewer
        - admin
        type: string
    required:
    - role
    type: object
  api.UpdateUserStatusRequest:
    properties:
      status:
        enum:
        - active
        - suspended
        - banned
        type: string
    required:
    - status
    type: object
  api.UpdateVersionRequest:
    properties:
      change_log:
        type: string
      incremental_update:
        type: boolean
      min_open_harmony_os_ver:
        type: string
      package_url:
        type: string
      size:
        minimum: 1
        type: integer
      version_name:
        maxLength: 50
        minLength: 1
        type: string
    type: object
  api.UploadResult:
    properties:
      file_url:
        type: string
    type: object
  api.UserActivityRequest:
    properties:
      action:
        type: string
      resource:
        type: string
      resource_id:
        type: integer
      user_id:
        type: integer
    required:
    - action
    - resource
    - user_id
    type: object
  api.UserResponse:
    properties:
      address:
        description: 地址信息字段
        type: string
      avatar:
        type: string
      city:
        type: string
      company_name:
        type: string
      created_at:
        type: string
      description:
        type: string
      developer_name:
        type: string
      district:
        type: string
      email:
        type: string
      id:
        type: integer
      is_developer:
        type: boolean
      last_login_at:
        type: string
      phone:
        type: string
      province:
        type: string
      role:
        type: string
      status:
        type: string
      street:
        type: string
      username:
        type: string
      verify_status:
        type: string
      website:
        type: string
    type: object
  api.VersionReviewRequest:
    properties:
      reason:
        type: string
      status:
        enum:
        - approved
        - rejected
        type: string
    required:
    - status
    type: object
  models.AlertEvent:
    properties:
      created_at:
        description: 创建时间
        type: string
      description:
        description: 告警描述
        type: string
      id:
        type: integer
      resolved_at:
        description: 解决时间
        type: string
      severity:
        description: 严重程度：low, medium, high, critical
        type: string
      status:
        description: 状态：active, resolved
        type: string
      type:
        description: 告警类型
        type: string
    type: object
  models.AnalyticsSummary:
    properties:
      new_apps_today:
        description: 今日新增应用
        type: integer
      new_downloads_today:
        description: 今日新增下载
        type: integer
      new_users_today:
        description: 今日新增用户
        type: integer
      pending_apps_count:
        description: 待审核应用数
        type: integer
      pending_reviews_count:
        description: 待审核评论数
        type: integer
      total_apps:
        description: 总应用数
        type: integer
      total_developers:
        description: 总开发者数
        type: integer
      total_downloads:
        description: 总下载量
        type: integer
      total_reviews:
        description: 总评论数
        type: integer
      total_users:
        description: 总用户数
        type: integer
    type: object
  models.AnalyticsTrend:
    properties:
      app_trend:
        description: 应用增长趋势
        items:
          $ref: '#/definitions/models.DateValue'
        type: array
      developer_trend:
        description: 开发者增长趋势
        items:
          $ref: '#/definitions/models.DateValue'
        type: array
      download_trend:
        description: 下载增长趋势
        items:
          $ref: '#/definitions/models.DateValue'
        type: array
      user_trend:
        description: 用户增长趋势
        items:
          $ref: '#/definitions/models.DateValue'
        type: array
    type: object
  models.AppDownloadStats:
    properties:
      date:
        type: string
      downloads:
        type: integer
    type: object
  models.Application:
    properties:
      approved_at:
        description: 审核通过时间
        type: string
      average_rating:
        type: number
      banner_image:
        type: string
      category:
        type: string
      created_at:
        type: string
      current_version:
        type: string
      deleted_at:
        type: string
      description:
        type: string
      developer:
        $ref: '#/definitions/models.User'
      developer_id:
        type: integer
      download_count:
        type: integer
      icon:
        type: string
      id:
        type: integer
      is_editor:
        description: 编辑推荐
        type: boolean
      is_featured:
        type: boolean
      is_top:
        description: 置顶应用
        type: boolean
      is_verified:
        type: boolean
      min_open_harmony_os_ver:
        type: string
      name:
        type: string
      package:
        type: string
      packageURL:
        type: string
      privacy_url:
        type: string
      rating_count:
        type: integer
      reject_reason:
        description: 拒绝原因
        type: string
      rejected_at:
        description: 拒绝时间
        type: string
      release_date:
        type: string
      reviewer:
        $ref: '#/definitions/models.User'
      reviewer_id:
        description: 审核相关字段
        type: integer
      short_desc:
        type: string
      size:
        description: 单位：字节
        type: integer
      status:
        $ref: '#/definitions/models.ApplicationStatus'
      tags:
        description: 以逗号分隔的标签
        type: string
      updated_at:
        type: string
      website_url:
        type: string
    type: object
  models.ApplicationStatus:
    enum:
    - pending
    - approved
    - rejected
    - removed
    - draft
    type: string
    x-enum-varnames:
    - ApplicationStatusPending
    - ApplicationStatusApproved
    - ApplicationStatusRejected
    - ApplicationStatusRemoved
    - ApplicationStatusDraft
  models.CategoryStats:
    properties:
      app_count:
        description: 应用数量
        type: integer
      category_id:
        description: 分类ID
        type: integer
      category_name:
        description: 分类名称
        type: string
      download_count:
        description: 下载数量
        type: integer
    type: object
  models.DateValue:
    properties:
      date:
        description: 日期，格式 YYYY-MM-DD
        type: string
      value:
        description: 值
        type: integer
    type: object
  models.DeviceDownloadStats:
    properties:
      device_type:
        type: string
      downloads:
        type: integer
    type: object
  models.MonitoringSummary:
    properties:
      average_response_time:
        description: 平均响应时间(ms)
        type: number
      cpu_usage:
        description: CPU使用率
        type: number
      database_connections:
        description: 数据库连接数
        type: integer
      disk_usage:
        description: 磁盘使用率
        type: number
      error_rate:
        description: 错误率
        type: number
      memory_usage:
        description: 内存使用率
        type: number
      requests_per_minute:
        description: 每分钟请求数
        type: integer
      server_status:
        description: 服务器状态
        type: string
      uptime_hours:
        description: 运行时间(小时)
        type: number
    type: object
  models.Notification:
    properties:
      category:
        description: app_review, developer_verify, version_update
        type: string
      content:
        type: string
      created_at:
        type: string
      deleted_at:
        type: string
      email_sent:
        type: boolean
      email_sent_at:
        type: string
      expires_at:
        description: 通知过期时间
        type: string
      id:
        type: integer
      is_read:
        type: boolean
      metadata:
        description: 额外的元数据，JSON格式
        type: string
      priority:
        $ref: '#/definitions/models.Priority'
      read_at:
        type: string
      related_id:
        description: 关联的ID（应用ID、版本ID等）
        type: integer
      related_type:
        description: 关联类型（application, version等）
        type: string
      title:
        type: string
      type:
        $ref: '#/definitions/models.NotificationType'
      updated_at:
        type: string
      user:
        $ref: '#/definitions/models.User'
      user_id:
        type: integer
    type: object
  models.NotificationSettings:
    properties:
      app_review_email_enabled:
        type: boolean
      app_review_web_enabled:
        description: 分类通知设置
        type: boolean
      created_at:
        type: string
      deleted_at:
        type: string
      developer_verify_email_enabled:
        type: boolean
      developer_verify_web_enabled:
        type: boolean
      email_notification_enabled:
        description: 邮件通知
        type: boolean
      id:
        type: integer
      system_email_enabled:
        type: boolean
      system_web_enabled:
        type: boolean
      updated_at:
        type: string
      user:
        $ref: '#/definitions/models.User'
      user_id:
        type: integer
      version_update_email_enabled:
        type: boolean
      version_update_web_enabled:
        type: boolean
      web_notification_enabled:
        description: 网页通知
        type: boolean
    type: object
  models.NotificationType:
    enum:
    - info
    - success
    - warning
    - error
    type: string
    x-enum-varnames:
    - NotificationTypeInfo
    - NotificationTypeSuccess
    - NotificationTypeWarning
    - NotificationTypeError
  models.PopularApp:
    properties:
      app_icon:
        type: string
      app_id:
        type: integer
      app_name:
        type: string
      category_name:
        type: string
      developer_name:
        type: string
      download_count:
        type: integer
      rating:
        type: number
    type: object
  models.Priority:
    enum:
    - low
    - normal
    - high
    - urgent
    type: string
    x-enum-varnames:
    - PriorityLow
    - PriorityNormal
    - PriorityHigh
    - PriorityUrgent
  models.RecentActivity:
    properties:
      content:
        description: 活动内容
        type: string
      created_at:
        description: 创建时间
        type: string
      id:
        type: integer
      type:
        description: 活动类型：app_create, app_update, review, download, etc.
        type: string
      user_id:
        description: 活动所属用户ID
        type: integer
    type: object
  models.SystemLog:
    properties:
      created_at:
        description: 创建时间
        type: string
      id:
        type: integer
      level:
        description: 日志级别：info, warning, error, critical
        type: string
      message:
        description: 日志消息
        type: string
      source:
        description: 日志来源
        type: string
    type: object
  models.TaskItem:
    properties:
      completed_at:
        description: 完成时间
        type: string
      created_at:
        description: 创建时间
        type: string
      description:
        description: 任务描述
        type: string
      due_date:
        description: 截止日期
        type: string
      id:
        type: integer
      priority:
        description: 优先级：low, medium, high
        type: string
      status:
        description: 任务状态：pending, in_progress, completed
        type: string
      title:
        description: 任务标题
        type: string
      updated_at:
        description: 更新时间
        type: string
      user_id:
        description: 任务所属用户ID
        type: integer
    type: object
  models.User:
    properties:
      address:
        description: 地址信息字段
        type: string
      avatar:
        type: string
      business_license:
        description: 营业执照URL
        type: string
      city:
        description: 城市
        type: string
      company_name:
        type: string
      contact_email:
        description: 联系邮箱
        type: string
      contact_phone:
        description: 联系电话
        type: string
      created_at:
        type: string
      deleted_at:
        type: string
      description:
        type: string
      developer_address:
        description: 开发者地址
        type: string
      developer_avatar:
        description: 开发者头像URL
        type: string
      developer_name:
        type: string
      district:
        description: 区/县
        type: string
      email:
        type: string
      id:
        type: integer
      identity_card:
        description: 身份证URL
        type: string
      is_developer:
        description: 开发者相关字段
        type: boolean
      last_login_at:
        type: string
      login_count:
        type: integer
      phone:
        type: string
      province:
        description: 省份
        type: string
      role:
        description: user, developer, admin
        type: string
      status:
        $ref: '#/definitions/models.UserStatus'
      street:
        description: 街道
        type: string
      submitted_at:
        description: 提交认证时间
        type: string
      updated_at:
        type: string
      username:
        type: string
      verified_at:
        description: 认证通过时间
        type: string
      verify_reason:
        description: 认证结果说明
        type: string
      verify_status:
        allOf:
        - $ref: '#/definitions/models.VerifyStatus'
        description: 审核状态
      website:
        type: string
    type: object
  models.UserStatus:
    enum:
    - active
    - inactive
    - suspended
    type: string
    x-enum-varnames:
    - UserStatusActive
    - UserStatusInactive
    - UserStatusSuspended
  models.VerifyStatus:
    enum:
    - pending
    - approved
    - rejected
    type: string
    x-enum-varnames:
    - VerifyStatusPending
    - VerifyStatusApproved
    - VerifyStatusRejected
  models.WorkbenchSummary:
    properties:
      average_rating:
        description: 我的应用平均评分
        type: number
      completed_task_count:
        description: 已完成任务数量
        type: integer
      my_app_count:
        description: 我的应用数量
        type: integer
      new_reviews_count:
        description: 我的应用新评论数量
        type: integer
      pending_apps_count:
        description: 我的待审核应用数量
        type: integer
      total_downloads:
        description: 我的应用总下载量
        type: integer
      total_task_count:
        description: 总任务数量
        type: integer
    type: object
  services.AppDocument:
    properties:
      average_rating:
        type: number
      category:
        type: string
      current_version:
        type: string
      description:
        type: string
      developer_id:
        type: integer
      developer_name:
        type: string
      download_count:
        type: integer
      id:
        type: integer
      is_editor:
        type: boolean
      is_featured:
        type: boolean
      is_top:
        type: boolean
      is_verified:
        type: boolean
      keywords:
        description: 用于搜索的关键词
        items:
          type: string
        type: array
      min_open_harmony_os_ver:
        type: string
      name:
        type: string
      package:
        type: string
      rating_count:
        type: integer
      short_desc:
        type: string
      status:
        type: string
      tags:
        type: string
    type: object
  services.ReviewDocument:
    properties:
      app_name:
        type: string
      app_package:
        type: string
      app_version:
        type: string
      application_id:
        type: integer
      content:
        type: string
      created_at:
        type: string
      dev_response:
        type: string
      id:
        type: integer
      keywords:
        description: 用于搜索的关键词
        items:
          type: string
        type: array
      like_count:
        type: integer
      rating:
        type: integer
      status:
        type: string
      title:
        type: string
      updated_at:
        type: string
      user_id:
        type: integer
      username:
        type: string
    type: object
  services.ReviewSearchResponse:
    properties:
      page:
        type: integer
      page_size:
        type: integer
      reviews:
        items:
          $ref: '#/definitions/services.ReviewDocument'
        type: array
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  services.SearchResponse:
    properties:
      apps:
        items:
          $ref: '#/definitions/services.AppDocument'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  services.TagDocument:
    properties:
      app_count:
        description: 使用该标签的应用数量
        type: integer
      color:
        type: string
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      is_active:
        type: boolean
      keywords:
        description: 用于搜索的关键词
        items:
          type: string
        type: array
      name:
        type: string
      updated_at:
        type: string
    type: object
  services.TagSearchResponse:
    properties:
      page:
        type: integer
      page_size:
        type: integer
      tags:
        items:
          $ref: '#/definitions/services.TagDocument'
        type: array
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  services.TagStatsDocument:
    properties:
      app_count:
        type: integer
      tag_id:
        type: integer
      tag_name:
        type: string
      total_apps:
        type: integer
      usage_rate:
        description: 使用率
        type: number
    type: object
  services.UserDocument:
    properties:
      company_name:
        type: string
      contact_email:
        type: string
      contact_phone:
        type: string
      created_at:
        type: string
      description:
        type: string
      developer_address:
        type: string
      developer_name:
        type: string
      email:
        type: string
      id:
        type: integer
      is_developer:
        type: boolean
      keywords:
        description: 用于搜索的关键词
        items:
          type: string
        type: array
      last_login_at:
        type: string
      login_count:
        type: integer
      phone:
        type: string
      role:
        type: string
      status:
        type: string
      updated_at:
        type: string
      username:
        type: string
      verify_status:
        type: string
      website:
        type: string
    type: object
  services.UserSearchResponse:
    properties:
      page:
        type: integer
      page_size:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
      users:
        items:
          $ref: '#/definitions/services.UserDocument'
        type: array
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.nexushub-oh.top/support
  description: NexusHub-OH 应用商店后端 API，为 OpenHarmony 系统应用商店提供完整的 API
  termsOfService: http://swagger.io/terms/
  title: NexusHub-OH API
  version: "1.0"
paths:
  /admin/developers/{id}/verify:
    post:
      consumes:
      - application/json
      description: 管理员审核开发者认证申请
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 审核结果
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.AdminVerifyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 审核成功，返回更新后的认证信息
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.DeveloperVerifyResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 非管理员，无权操作
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 审核开发者
      tags:
      - 开发者
  /admin/developers/verify:
    get:
      consumes:
      - application/json
      description: 管理员获取待审核的开发者列表
      parameters:
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      - default: '"pending"'
        description: 认证状态(pending/approved/rejected)
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回待审核的开发者列表
          schema:
            allOf:
            - $ref: '#/definitions/api.PageResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/api.DeveloperVerifyResponse'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 非管理员，无权访问
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取待审核的开发者
      tags:
      - 开发者
  /admin/users:
    get:
      consumes:
      - application/json
      description: 管理员获取所有用户列表
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认10
        in: query
        name: pageSize
        type: integer
      - description: 搜索关键词（用户名/邮箱/手机）
        in: query
        name: keyword
        type: string
      - description: 用户角色
        in: query
        name: role
        type: string
      - description: 用户状态
        in: query
        name: status
        type: string
      - description: 开发者状态
        in: query
        name: developerStatus
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 用户列表
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/api.UserResponse'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 无权限
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取用户列表
      tags:
      - 管理员
    post:
      consumes:
      - application/json
      description: 管理员创建新用户并指定角色
      parameters:
      - description: 用户信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.AdminCreateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 管理员创建用户
      tags:
      - 管理员
  /admin/users/{id}/role:
    put:
      consumes:
      - application/json
      description: 管理员更新用户角色
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 角色信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.UpdateUserRoleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 更新用户角色
      tags:
      - 管理员
  /admin/users/{id}/status:
    put:
      consumes:
      - application/json
      description: 管理员更新用户状态（禁用/启用）
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 状态信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.UpdateUserStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 更新用户状态
      tags:
      - 管理员
  /admin/versions/{version_id}/review:
    post:
      consumes:
      - application/json
      description: 管理员审核应用版本
      parameters:
      - description: 版本ID
        in: path
        name: version_id
        required: true
        type: integer
      - description: 审核信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.VersionReviewRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 审核成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 版本不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 审核应用版本
      tags:
      - 版本审核
  /admin/versions/pending:
    get:
      consumes:
      - application/json
      description: 管理员获取待审核的版本列表
      parameters:
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取待审核版本列表
      tags:
      - 版本审核
  /api/v1/admin/featured-collections:
    get:
      consumes:
      - application/json
      description: 获取所有精选集列表，包括非公开和非活跃的（管理员权限）
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - default: false
        description: 是否包含非活跃精选集
        in: query
        name: include_inactive
        type: boolean
      - default: false
        description: 是否包含非公开精选集
        in: query
        name: include_private
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/api.PaginatedData'
                  - properties:
                      items:
                        items:
                          $ref: '#/definitions/api.FeaturedCollectionResponse'
                        type: array
                    type: object
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取所有精选集列表
      tags:
      - 精选集管理
    post:
      consumes:
      - application/json
      description: 创建新的精选集（管理员权限）
      parameters:
      - description: 精选集信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.CreateFeaturedCollectionRequest'
      produces:
      - application/json
      responses:
        "201":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.FeaturedCollectionResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - ApiKeyAuth: []
      summary: 创建精选集
      tags:
      - 精选集管理
  /api/v1/admin/featured-collections/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定精选集（管理员权限）
      parameters:
      - description: 精选集ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 精选集不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - ApiKeyAuth: []
      summary: 删除精选集
      tags:
      - 精选集管理
    put:
      consumes:
      - application/json
      description: 更新精选集信息（管理员权限）
      parameters:
      - description: 精选集ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.UpdateFeaturedCollectionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.FeaturedCollectionResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 精选集不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - ApiKeyAuth: []
      summary: 更新精选集
      tags:
      - 精选集管理
  /api/v1/admin/featured-collections/{id}/apps:
    post:
      consumes:
      - application/json
      description: 向指定精选集添加应用（管理员权限）
      parameters:
      - description: 精选集ID
        in: path
        name: id
        required: true
        type: integer
      - description: 应用信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.AddAppToCollectionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 添加成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 精选集或应用不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - ApiKeyAuth: []
      summary: 向精选集添加应用
      tags:
      - 精选集管理
  /api/v1/admin/featured-collections/{id}/apps/{app_id}:
    delete:
      consumes:
      - application/json
      description: 从指定精选集移除应用（管理员权限）
      parameters:
      - description: 精选集ID
        in: path
        name: id
        required: true
        type: integer
      - description: 应用ID
        in: path
        name: app_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 移除成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 精选集或应用不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - ApiKeyAuth: []
      summary: 从精选集移除应用
      tags:
      - 精选集管理
  /api/v1/geographic/city/{province}:
    get:
      consumes:
      - application/json
      description: 根据省份ID获取该省份下的所有城市
      parameters:
      - description: 省份ID
        in: path
        name: province
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 城市列表
          schema:
            items:
              $ref: '#/definitions/api.CityResponse'
            type: array
      summary: 获取城市列表
      tags:
      - 地理位置
  /api/v1/geographic/country:
    get:
      consumes:
      - application/json
      description: 获取所有国家数据
      produces:
      - application/json
      responses:
        "200":
          description: 国家列表
          schema:
            items:
              $ref: '#/definitions/api.CountryResponse'
            type: array
      summary: 获取国家列表
      tags:
      - 地理位置
  /api/v1/geographic/district/{city}:
    get:
      consumes:
      - application/json
      description: 根据城市ID获取该城市下的所有区/镇
      parameters:
      - description: 城市ID
        in: path
        name: city
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 区/镇列表
          schema:
            items:
              $ref: '#/definitions/api.DistrictResponse'
            type: array
      summary: 获取区/镇列表
      tags:
      - 地理位置
  /api/v1/geographic/level:
    get:
      consumes:
      - application/json
      description: 根据级别(0-省份,1-城市,2-区镇,3-街道)和父ID获取地理位置数据
      parameters:
      - description: 级别(0-省份,1-城市,2-区镇,3-街道)
        in: query
        name: level
        required: true
        type: integer
      - description: 父级ID(获取省份时可不传)
        in: query
        name: parent_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 地理位置列表
          schema:
            items:
              $ref: '#/definitions/api.GeographicResponse'
            type: array
      summary: 根据级别获取地理位置数据
      tags:
      - 地理位置
  /api/v1/geographic/province:
    get:
      consumes:
      - application/json
      description: 获取所有省份数据
      produces:
      - application/json
      responses:
        "200":
          description: 省份列表
          schema:
            items:
              $ref: '#/definitions/api.ProvinceResponse'
            type: array
      summary: 获取省份列表
      tags:
      - 地理位置
  /api/v1/geographic/street/{district}:
    get:
      consumes:
      - application/json
      description: 根据区/镇ID获取该区/镇下的所有街道
      parameters:
      - description: 区/镇ID
        in: path
        name: district
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 街道列表
          schema:
            items:
              $ref: '#/definitions/api.StreetResponse'
            type: array
      summary: 获取街道列表
      tags:
      - 地理位置
  /api/v1/messages/activity:
    post:
      consumes:
      - application/json
      description: 记录用户活动到消息队列
      parameters:
      - description: 用户活动请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.UserActivityRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.Response'
      summary: 记录用户活动
      tags:
      - 消息队列
  /api/v1/messages/app-review/{app_id}:
    post:
      consumes:
      - application/json
      description: 触发应用审核流程
      parameters:
      - description: 应用ID
        in: path
        name: app_id
        required: true
        type: integer
      - description: 操作类型
        enum:
        - submit
        - approve
        - reject
        in: query
        name: action
        required: true
        type: string
      - description: 原因（拒绝时必填）
        in: query
        name: reason
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.Response'
      summary: 触发应用审核
      tags:
      - 消息队列
  /api/v1/messages/email:
    post:
      consumes:
      - application/json
      description: 发送邮件到消息队列
      parameters:
      - description: 邮件请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.SendEmailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.Response'
      summary: 发送邮件消息
      tags:
      - 消息队列
  /api/v1/messages/notification:
    post:
      consumes:
      - application/json
      description: 向指定用户发送通知消息
      parameters:
      - description: 通知请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.SendNotificationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.Response'
      summary: 发送通知消息
      tags:
      - 消息队列
  /api/v1/messages/status:
    get:
      description: 获取消息队列状态信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取队列状态
      tags:
      - 消息队列
  /api/v1/notifications:
    get:
      consumes:
      - application/json
      description: 获取当前用户的通知列表，支持分页和筛选未读通知
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - default: false
        description: 仅显示未读
        in: query
        name: unread_only
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.GetNotificationsResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取用户通知列表
      tags:
      - 通知管理
  /api/v1/notifications/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定的通知
      parameters:
      - description: 通知ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 通知不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 删除通知
      tags:
      - 通知管理
  /api/v1/notifications/read:
    post:
      consumes:
      - application/json
      description: 标记指定通知为已读状态
      parameters:
      - description: 标记已读请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.MarkAsReadRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 标记成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 标记通知为已读
      tags:
      - 通知管理
  /api/v1/notifications/read-all:
    post:
      consumes:
      - application/json
      description: 标记当前用户的所有未读通知为已读状态
      produces:
      - application/json
      responses:
        "200":
          description: 标记成功
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 标记所有通知为已读
      tags:
      - 通知管理
  /api/v1/notifications/settings:
    get:
      consumes:
      - application/json
      description: 获取当前用户的通知设置
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.NotificationSettings'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取通知设置
      tags:
      - 通知管理
    put:
      consumes:
      - application/json
      description: 更新当前用户的通知设置
      parameters:
      - description: 通知设置
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/api.UpdateNotificationSettingsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 更新通知设置
      tags:
      - 通知管理
  /api/v1/notifications/unread-count:
    get:
      consumes:
      - application/json
      description: 获取当前用户的未读通知数量
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  additionalProperties:
                    type: integer
                  type: object
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取未读通知数量
      tags:
      - 通知管理
  /api/v1/public/featured-collections:
    get:
      consumes:
      - application/json
      description: 获取精选集列表（公开接口）
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/api.PaginatedData'
                  - properties:
                      items:
                        items:
                          $ref: '#/definitions/api.FeaturedCollectionResponse'
                        type: array
                    type: object
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取精选集列表
      tags:
      - 精选集
  /api/v1/public/featured-collections/{id}:
    get:
      consumes:
      - application/json
      description: 获取指定精选集的详细信息（公开接口）
      parameters:
      - description: 精选集ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.FeaturedCollectionResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 精选集不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取精选集详情
      tags:
      - 精选集
  /api/v1/public/featured-collections/{id}/apps:
    get:
      consumes:
      - application/json
      description: 获取指定精选集中的应用列表（公开接口）
      parameters:
      - description: 精选集ID
        in: path
        name: id
        required: true
        type: integer
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/api.PaginatedData'
                  - properties:
                      items:
                        items:
                          $ref: '#/definitions/models.Application'
                        type: array
                    type: object
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 精选集不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取精选集中的应用列表
      tags:
      - 精选集
  /api/v1/upload/token:
    get:
      consumes:
      - application/json
      description: 获取上传到对象存储的预签名URL
      parameters:
      - description: 文件类型(avatar/license/identity/screenshot/package)
        in: query
        name: file_type
        required: true
        type: string
      - description: 文件名称
        in: query
        name: file_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回上传凭证
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.UploadResult'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取文件上传凭证
      tags:
      - 开发者
  /apps:
    get:
      consumes:
      - application/json
      description: 获取应用列表，支持分页和筛选。普通用户只能看到已审核通过的应用，管理员可以看到所有状态的应用
      parameters:
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      - description: 分类筛选
        in: query
        name: category
        type: string
      - description: 搜索关键字
        in: query
        name: keyword
        type: string
      - description: 状态筛选（仅管理员可用）
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回应用列表
          schema:
            allOf:
            - $ref: '#/definitions/api.PageResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/api.AppDetailsResponse'
                  type: array
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取应用列表
      tags:
      - 应用
    post:
      consumes:
      - application/json
      description: 开发者创建新应用
      parameters:
      - description: 应用信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.CreateAppRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功，返回应用详情
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.AppDetailsResponse'
              type: object
        "400":
          description: 参数错误/包名已被使用
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 非开发者或开发者未通过审核
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 创建应用
      tags:
      - 应用
  /apps/{id}:
    get:
      consumes:
      - application/json
      description: 获取应用的详细信息，包括截图和版本
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回应用详情，包括应用基本信息、截图和版本
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
              type: object
        "404":
          description: 应用不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取应用详情
      tags:
      - 应用
    put:
      consumes:
      - application/json
      description: 开发者更新应用信息
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新的应用信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.UpdateAppRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功，返回应用详情
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.AppDetailsResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 非应用的开发者
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 应用不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 更新应用
      tags:
      - 应用
  /apps/{id}/reviews:
    get:
      consumes:
      - application/json
      description: 获取应用的评论和评分
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回评论列表和分页信息
          schema:
            allOf:
            - $ref: '#/definitions/api.PageResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/api.ReviewResponse'
                  type: array
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 应用不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取应用评论
      tags:
      - 评论
    post:
      consumes:
      - application/json
      description: 用户为应用发表评价和评分
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 评论内容
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.CreateReviewRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功，返回评论详情
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.ReviewResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 应用不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 发表应用评论
      tags:
      - 评论
  /apps/{id}/reviews/{review_id}/like:
    post:
      consumes:
      - application/json
      description: 给应用评论点赞
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 评论ID
        in: path
        name: review_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 点赞成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 评论不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 点赞评论
      tags:
      - 评论
  /apps/{id}/reviews/{review_id}/respond:
    post:
      consumes:
      - application/json
      description: 应用开发者回复用户评论
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 评论ID
        in: path
        name: review_id
        required: true
        type: integer
      - description: 回复内容
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.DevResponseRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 回复成功，返回更新后的评论
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.ReviewResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 非应用开发者，无权回复
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 评论不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 开发者回复评论
      tags:
      - 评论
  /apps/{id}/reviews/{review_id}/unlike:
    post:
      consumes:
      - application/json
      description: 取消对应用评论的点赞
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 评论ID
        in: path
        name: review_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 取消点赞成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 评论不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 取消点赞评论
      tags:
      - 评论
  /apps/{id}/submit:
    post:
      consumes:
      - application/json
      description: 开发者提交应用进行审核（从草稿状态变为待审核状态）
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 提交成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 应用不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 提交应用审核
      tags:
      - 应用管理
  /apps/{id}/tags:
    get:
      consumes:
      - application/json
      description: 获取应用的所有标签
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 标签列表
          schema:
            items:
              $ref: '#/definitions/api.TagResponse'
            type: array
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "404":
          description: 应用不存在
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 获取应用的标签
      tags:
      - 标签管理
    post:
      consumes:
      - application/json
      description: 为应用添加多个标签
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        required: true
        type: string
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 标签ID列表
        in: body
        name: tags
        required: true
        schema:
          $ref: '#/definitions/api.AppTagRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 添加成功
          schema:
            $ref: '#/definitions/api.SuccessResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "404":
          description: 应用或标签不存在
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 为应用添加标签
      tags:
      - 标签管理
  /apps/{id}/tags/{tag_id}:
    delete:
      consumes:
      - application/json
      description: 删除应用的指定标签
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        required: true
        type: string
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 标签ID
        in: path
        name: tag_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/api.SuccessResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "404":
          description: 应用或标签不存在
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 删除应用的标签
      tags:
      - 标签管理
  /apps/{id}/versions:
    get:
      consumes:
      - application/json
      description: 获取指定应用的版本列表
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 版本状态筛选
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 应用不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取应用版本列表
      tags:
      - 应用版本
    post:
      consumes:
      - application/json
      description: 开发者创建新的应用版本
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 版本信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.AppVersionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 应用不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 创建应用版本
      tags:
      - 应用版本
  /apps/{id}/versions/{version_id}:
    delete:
      consumes:
      - application/json
      description: 开发者删除应用版本
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 版本ID
        in: path
        name: version_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 版本不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 删除应用版本
      tags:
      - 应用版本
    get:
      consumes:
      - application/json
      description: 获取指定应用版本的详细信息
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 版本ID
        in: path
        name: version_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 版本不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取应用版本详情
      tags:
      - 应用版本
    put:
      consumes:
      - application/json
      description: 开发者更新应用版本信息
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 版本ID
        in: path
        name: version_id
        required: true
        type: integer
      - description: 版本信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.UpdateVersionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 版本不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 更新应用版本
      tags:
      - 应用版本
  /apps/{id}/versions/{version_id}/download:
    post:
      consumes:
      - application/json
      description: 记录用户下载应用的行为，更新下载统计
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 版本ID
        in: path
        name: version_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 记录成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 应用或版本不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 记录应用下载
      tags:
      - 统计
  /apps/{id}/versions/{version_id}/publish:
    post:
      consumes:
      - application/json
      description: 开发者发布应用版本（提交审核）
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 版本ID
        in: path
        name: version_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 提交成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 版本不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 发布应用版本
      tags:
      - 应用版本
  /categories:
    get:
      consumes:
      - application/json
      description: 获取所有应用分类
      parameters:
      - default: false
        description: 是否包含未激活的分类
        in: query
        name: include_inactive
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: 分类列表
          schema:
            items:
              $ref: '#/definitions/api.CategoryResponse'
            type: array
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 获取应用分类列表
      tags:
      - 分类管理
    post:
      consumes:
      - application/json
      description: 创建一个新的应用分类
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        required: true
        type: string
      - description: 分类信息
        in: body
        name: category
        required: true
        schema:
          $ref: '#/definitions/api.CreateCategoryRequest'
      produces:
      - application/json
      responses:
        "201":
          description: 创建成功
          schema:
            $ref: '#/definitions/api.CategoryResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 创建应用分类
      tags:
      - 分类管理
  /categories/{id}:
    delete:
      consumes:
      - application/json
      description: 删除应用分类
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        required: true
        type: string
      - description: 分类ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/api.SuccessResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "404":
          description: 分类不存在
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 删除应用分类
      tags:
      - 分类管理
    get:
      consumes:
      - application/json
      description: 获取应用分类详情
      parameters:
      - description: 分类ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 分类详情
          schema:
            $ref: '#/definitions/api.CategoryDetailResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "404":
          description: 分类不存在
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 获取应用分类
      tags:
      - 分类管理
    put:
      consumes:
      - application/json
      description: 更新应用分类信息
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        required: true
        type: string
      - description: 分类ID
        in: path
        name: id
        required: true
        type: integer
      - description: 分类信息
        in: body
        name: category
        required: true
        schema:
          $ref: '#/definitions/api.UpdateCategoryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/api.CategoryResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "404":
          description: 分类不存在
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 更新应用分类
      tags:
      - 分类管理
  /categories/{id}/apps:
    get:
      consumes:
      - application/json
      description: 获取指定分类下的所有已审核通过的应用
      parameters:
      - description: 分类ID
        in: path
        name: id
        required: true
        type: integer
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      - default: latest
        description: 排序方式：latest(最新)、popular(热门)、rating(评分)
        in: query
        name: sort
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回应用列表
          schema:
            allOf:
            - $ref: '#/definitions/api.PageResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/api.AppDetailsResponse'
                  type: array
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "404":
          description: 分类不存在
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 获取分类下的应用
      tags:
      - 分类管理
  /categories/root:
    get:
      consumes:
      - application/json
      description: 获取所有根应用分类（没有父分类的分类）
      parameters:
      - default: false
        description: 是否包含未激活的分类
        in: query
        name: include_inactive
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: 根分类列表
          schema:
            items:
              $ref: '#/definitions/api.CategoryResponse'
            type: array
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 获取根应用分类列表
      tags:
      - 分类管理
  /dashboard/analytics/categories:
    get:
      consumes:
      - application/json
      description: 获取各应用分类的应用数量和下载统计
      produces:
      - application/json
      responses:
        "200":
          description: 返回分类统计数据
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.CategoryStats'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取分类统计
      tags:
      - 仪表盘
  /dashboard/analytics/popular-apps:
    get:
      consumes:
      - application/json
      description: 获取平台上下载量和评分最高的应用
      parameters:
      - default: 10
        description: 返回数量，默认10个
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回热门应用列表
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.PopularApp'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取热门应用
      tags:
      - 仪表盘
  /dashboard/analytics/summary:
    get:
      consumes:
      - application/json
      description: 获取平台总体数据摘要，包括用户、应用、下载、评论等统计
      produces:
      - application/json
      responses:
        "200":
          description: 返回分析页摘要数据
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.AnalyticsSummary'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取分析页摘要数据
      tags:
      - 仪表盘
  /dashboard/analytics/trend:
    get:
      consumes:
      - application/json
      description: 获取用户、应用、下载、开发者等趋势数据
      parameters:
      - default: 30
        description: 统计天数，默认30天
        in: query
        name: days
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回趋势分析数据
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.AnalyticsTrend'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取趋势分析数据
      tags:
      - 仪表盘
  /dashboard/monitoring/alerts:
    get:
      consumes:
      - application/json
      description: 获取系统告警事件
      parameters:
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      - description: 状态过滤(active/resolved)
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回告警事件和分页信息
          schema:
            allOf:
            - $ref: '#/definitions/api.PageResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.AlertEvent'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取告警事件
      tags:
      - 仪表盘
  /dashboard/monitoring/data:
    get:
      consumes:
      - application/json
      description: 获取系统运行状态监控数据
      produces:
      - application/json
      responses:
        "200":
          description: 返回监控数据
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.MonitoringData'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取监控数据
      tags:
      - 仪表盘
  /dashboard/monitoring/logs:
    get:
      consumes:
      - application/json
      description: 获取系统运行日志记录
      parameters:
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      - description: 日志级别过滤(info/warning/error/critical)
        in: query
        name: level
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回系统日志和分页信息
          schema:
            allOf:
            - $ref: '#/definitions/api.PageResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.SystemLog'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取系统日志
      tags:
      - 仪表盘
  /dashboard/workbench/activities:
    get:
      consumes:
      - application/json
      description: 获取当前用户的最近活动记录
      parameters:
      - default: 10
        description: 返回数量，默认10条
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回最近活动列表
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.RecentActivity'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取最近活动
      tags:
      - 仪表盘
  /dashboard/workbench/summary:
    get:
      consumes:
      - application/json
      description: 获取当前用户的工作台摘要数据
      produces:
      - application/json
      responses:
        "200":
          description: 返回工作台摘要数据
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.WorkbenchSummary'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取工作台摘要
      tags:
      - 仪表盘
  /dashboard/workbench/tasks:
    get:
      consumes:
      - application/json
      description: 获取当前用户的任务列表
      parameters:
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      - description: 任务状态过滤(pending/in_progress/completed)
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回任务列表和分页信息
          schema:
            allOf:
            - $ref: '#/definitions/api.PageResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.TaskItem'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取任务列表
      tags:
      - 仪表盘
    post:
      consumes:
      - application/json
      description: 创建新的任务
      parameters:
      - description: 任务信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/models.TaskItem'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功，返回任务详情
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.TaskItem'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 创建任务
      tags:
      - 仪表盘
  /dashboard/workbench/tasks/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定的任务
      parameters:
      - description: 任务ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 无权限操作此任务
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 任务不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 删除任务
      tags:
      - 仪表盘
    put:
      consumes:
      - application/json
      description: 更新任务信息
      parameters:
      - description: 任务ID
        in: path
        name: id
        required: true
        type: integer
      - description: 任务信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/models.TaskItem'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功，返回任务详情
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.TaskItem'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 无权限操作此任务
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 任务不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 更新任务
      tags:
      - 仪表盘
  /developer/apps:
    get:
      consumes:
      - application/json
      description: 开发者获取自己提交的所有应用（包括各种状态）
      parameters:
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      - description: 应用状态筛选
        in: query
        name: status
        type: string
      - description: 搜索关键字
        in: query
        name: keyword
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回开发者应用列表
          schema:
            allOf:
            - $ref: '#/definitions/api.PageResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/api.AppDetailsResponse'
                  type: array
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取开发者应用列表
      tags:
      - 应用
  /developers/verify:
    post:
      consumes:
      - application/json
      description: 用户提交开发者认证申请
      parameters:
      - description: 开发者认证信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.DeveloperVerifyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 提交成功，返回认证信息
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.DeveloperVerifyResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 已是开发者或认证正在处理中
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 提交开发者认证
      tags:
      - 开发者
  /developers/verify/status:
    get:
      consumes:
      - application/json
      description: 获取当前用户的开发者认证状态
      produces:
      - application/json
      responses:
        "200":
          description: 返回认证状态
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.DeveloperVerifyResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取开发者认证状态
      tags:
      - 开发者
  /health:
    get:
      consumes:
      - application/json
      description: 检查API服务是否正常运行
      produces:
      - application/json
      responses:
        "200":
          description: 返回服务状态和时间
          schema:
            additionalProperties: true
            type: object
      summary: 健康检查接口
      tags:
      - 系统
  /public/apps:
    get:
      consumes:
      - application/json
      description: 获取已审核通过的应用列表，支持分页和筛选，无需认证
      parameters:
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      - description: 分类筛选
        in: query
        name: category
        type: string
      - description: 搜索关键字
        in: query
        name: keyword
        type: string
      - default: latest
        description: 排序方式：latest(最新)、popular(热门)、rating(评分)
        in: query
        name: sort
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 返回应用列表
          schema:
            allOf:
            - $ref: '#/definitions/api.PageResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/api.AppDetailsResponse'
                  type: array
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取公开应用列表
      tags:
      - 应用
  /public/apps/{id}:
    get:
      consumes:
      - application/json
      description: 匿名用户获取应用的详细信息，包括截图和版本，只返回已审核通过的应用
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回应用详情，包括应用基本信息、截图和版本
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
              type: object
        "404":
          description: 应用不存在或未审核通过
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 匿名获取应用详情
      tags:
      - 应用
  /public/apps/{id}/versions/{version_id}/download:
    post:
      consumes:
      - application/json
      description: 匿名用户下载应用时记录下载信息
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 版本ID
        in: path
        name: version_id
        required: true
        type: integer
      - description: 设备信息
        in: body
        name: data
        schema:
          $ref: '#/definitions/api.AnonymousDownloadRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 记录成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 应用或版本不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 匿名下载记录
      tags:
      - 应用
  /public/apps/latest:
    get:
      consumes:
      - application/json
      description: 获取最新发布的应用列表，无需认证
      parameters:
      - default: 10
        description: 返回数量，默认10个
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回最新应用列表
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/api.AppDetailsResponse'
                  type: array
              type: object
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取最新应用
      tags:
      - 应用
  /public/apps/popular:
    get:
      consumes:
      - application/json
      description: 获取下载量和评分最高的应用列表，无需认证
      parameters:
      - default: 10
        description: 返回数量，默认10个
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回热门应用列表
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/api.AppDetailsResponse'
                  type: array
              type: object
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取热门应用
      tags:
      - 应用
  /public/apps/recommended:
    get:
      consumes:
      - application/json
      description: 获取编辑推荐的应用列表，无需认证
      parameters:
      - default: 10
        description: 返回数量，默认10个
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回推荐应用列表
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/api.AppDetailsResponse'
                  type: array
              type: object
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取推荐应用
      tags:
      - 应用
  /reviewer/apps/{id}/review:
    post:
      consumes:
      - application/json
      description: 审核员审核应用
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - description: 审核信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.AppReviewRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 审核成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 应用不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 审核应用
      tags:
      - 审核员
  /reviewer/apps/pending:
    get:
      consumes:
      - application/json
      description: 获取所有待审核的应用
      parameters:
      - description: 页码，默认1
        in: query
        name: page
        type: integer
      - description: 每页数量，默认20
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回待审核应用列表
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.PagedResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取待审核应用列表
      tags:
      - 审核员
  /search/apps:
    get:
      consumes:
      - application/json
      description: 使用Elasticsearch搜索应用，支持关键词、分类、标签等多种过滤条件
      parameters:
      - description: 搜索关键词
        in: query
        name: keyword
        type: string
      - description: 应用分类
        in: query
        name: category
        type: string
      - collectionFormat: csv
        description: 标签列表
        in: query
        items:
          type: string
        name: tags
        type: array
      - description: 最低评分
        in: query
        name: min_rating
        type: number
      - description: 是否认证
        in: query
        name: is_verified
        type: boolean
      - description: 是否推荐
        in: query
        name: is_featured
        type: boolean
      - description: 排序字段(name/download_count/rating)
        enum:
        - name
        - download_count
        - rating
        in: query
        name: sort_by
        type: string
      - description: 排序方向(asc/desc)
        enum:
        - asc
        - desc
        in: query
        name: sort_order
        type: string
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 搜索结果
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/services.SearchResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 搜索应用
      tags:
      - 搜索
  /search/initialize:
    post:
      consumes:
      - application/json
      description: 创建Elasticsearch索引并同步所有应用数据（管理员功能）
      produces:
      - application/json
      responses:
        "200":
          description: 初始化成功
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 初始化搜索索引
      tags:
      - 搜索
  /search/initialize-all:
    post:
      consumes:
      - application/json
      description: 初始化应用、用户、评论、标签的Elasticsearch索引（管理员功能）
      produces:
      - application/json
      responses:
        "200":
          description: 初始化成功
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 初始化所有搜索索引
      tags:
      - 搜索
  /search/reviews:
    get:
      consumes:
      - application/json
      description: 使用Elasticsearch搜索评论，支持评论内容、用户、应用等搜索
      parameters:
      - description: 搜索关键词
        in: query
        name: keyword
        type: string
      - description: 用户ID
        in: query
        name: user_id
        type: integer
      - description: 应用ID
        in: query
        name: application_id
        type: integer
      - description: 最低评分
        in: query
        name: min_rating
        type: integer
      - description: 最高评分
        in: query
        name: max_rating
        type: integer
      - description: 评论状态
        in: query
        name: status
        type: string
      - description: 是否有开发者回复
        in: query
        name: has_dev_reply
        type: boolean
      - description: 排序字段
        enum:
        - created_at
        - rating
        - like_count
        in: query
        name: sort_by
        type: string
      - description: 排序方向
        enum:
        - asc
        - desc
        in: query
        name: sort_order
        type: string
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 搜索结果
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/services.ReviewSearchResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 搜索评论
      tags:
      - 搜索
  /search/suggestions:
    get:
      consumes:
      - application/json
      description: 根据输入的关键词获取搜索建议
      parameters:
      - description: 搜索关键词
        in: query
        name: q
        required: true
        type: string
      - default: 10
        description: 建议数量限制，默认10
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 搜索建议列表
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  items:
                    type: string
                  type: array
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 获取搜索建议
      tags:
      - 搜索
  /search/sync:
    post:
      consumes:
      - application/json
      description: 重新同步所有应用数据到Elasticsearch（管理员功能）
      produces:
      - application/json
      responses:
        "200":
          description: 同步成功
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 同步搜索索引
      tags:
      - 搜索
  /search/tags:
    get:
      consumes:
      - application/json
      description: 使用Elasticsearch搜索标签，支持标签名称、描述等搜索
      parameters:
      - description: 搜索关键词
        in: query
        name: keyword
        type: string
      - description: 是否活跃
        in: query
        name: is_active
        type: boolean
      - description: 排序字段
        enum:
        - name
        - app_count
        - created_at
        in: query
        name: sort_by
        type: string
      - description: 排序方向
        enum:
        - asc
        - desc
        in: query
        name: sort_order
        type: string
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 搜索结果
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/services.TagSearchResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 搜索标签
      tags:
      - 搜索
  /search/tags/stats:
    get:
      consumes:
      - application/json
      description: 获取标签使用统计信息（管理员功能）
      produces:
      - application/json
      responses:
        "200":
          description: 统计结果
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/services.TagStatsDocument'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取标签统计
      tags:
      - 搜索
  /search/tags/suggest:
    get:
      consumes:
      - application/json
      description: 根据输入前缀提供标签建议
      parameters:
      - description: 搜索前缀
        in: query
        name: prefix
        required: true
        type: string
      - default: 10
        description: 建议数量限制，默认10
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 建议列表
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  items:
                    type: string
                  type: array
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 标签自动补全
      tags:
      - 搜索
  /search/users:
    get:
      consumes:
      - application/json
      description: 使用Elasticsearch搜索用户，支持用户名、邮箱、开发者信息等搜索（管理员功能）
      parameters:
      - description: 搜索关键词
        in: query
        name: keyword
        type: string
      - description: 用户角色
        in: query
        name: role
        type: string
      - description: 用户状态
        in: query
        name: status
        type: string
      - description: 是否为开发者
        in: query
        name: is_developer
        type: boolean
      - description: 认证状态
        in: query
        name: verify_status
        type: string
      - description: 排序字段
        enum:
        - username
        - created_at
        - last_login_at
        - login_count
        in: query
        name: sort_by
        type: string
      - description: 排序方向
        enum:
        - asc
        - desc
        in: query
        name: sort_order
        type: string
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 搜索结果
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/services.UserSearchResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 搜索用户
      tags:
      - 搜索
  /stats/apps/{id}/downloads:
    get:
      consumes:
      - application/json
      description: 获取应用的下载统计数据，包括总数、每日统计和设备类型统计
      parameters:
      - description: 应用ID
        in: path
        name: id
        required: true
        type: integer
      - default: 30
        description: 统计天数，默认30天
        in: query
        name: days
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回下载统计数据
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.AppDownloadStatsResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 无权限查看此应用统计
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 应用不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取应用下载统计
      tags:
      - 统计
  /stats/apps/overall:
    get:
      consumes:
      - application/json
      description: 获取应用商店的整体统计数据，包括应用总数、下载总数、分类统计等
      parameters:
      - default: month
        description: 时间范围：week, month, year
        in: query
        name: time_range
        type: string
      - description: 开发者ID，用于筛选特定开发者的应用统计
        in: query
        name: developer_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回应用整体统计数据
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.AppOverallStatsResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取应用整体统计
      tags:
      - 统计
  /stats/downloads:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的应用下载记录
      parameters:
      - default: 1
        description: 页码，默认1
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量，默认20
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 返回下载记录和分页信息
          schema:
            allOf:
            - $ref: '#/definitions/api.PageResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/api.DownloadRecordResponse'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取用户下载记录
      tags:
      - 统计
  /tags:
    get:
      consumes:
      - application/json
      description: 获取所有应用标签
      parameters:
      - default: false
        description: 是否包含未激活的标签
        in: query
        name: include_inactive
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: 标签列表
          schema:
            items:
              $ref: '#/definitions/api.TagResponse'
            type: array
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 获取应用标签列表
      tags:
      - 标签管理
    post:
      consumes:
      - application/json
      description: 创建一个新的应用标签
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        required: true
        type: string
      - description: 标签信息
        in: body
        name: tag
        required: true
        schema:
          $ref: '#/definitions/api.CreateTagRequest'
      produces:
      - application/json
      responses:
        "201":
          description: 创建成功
          schema:
            $ref: '#/definitions/api.TagResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 创建应用标签
      tags:
      - 标签管理
  /tags/{id}:
    delete:
      consumes:
      - application/json
      description: 删除应用标签
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        required: true
        type: string
      - description: 标签ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/api.SuccessResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "404":
          description: 标签不存在
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 删除应用标签
      tags:
      - 标签管理
    get:
      consumes:
      - application/json
      description: 获取应用标签详情
      parameters:
      - description: 标签ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 标签详情
          schema:
            $ref: '#/definitions/api.TagAppCountResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "404":
          description: 标签不存在
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 获取应用标签
      tags:
      - 标签管理
    put:
      consumes:
      - application/json
      description: 更新应用标签信息
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        required: true
        type: string
      - description: 标签ID
        in: path
        name: id
        required: true
        type: integer
      - description: 标签信息
        in: body
        name: tag
        required: true
        schema:
          $ref: '#/definitions/api.UpdateTagRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/api.TagResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "404":
          description: 标签不存在
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 更新应用标签
      tags:
      - 标签管理
  /tags/{id}/apps:
    get:
      consumes:
      - application/json
      description: 获取包含指定标签的所有应用
      parameters:
      - description: 标签ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 应用列表
          schema:
            items:
              $ref: '#/definitions/api.AppResponse'
            type: array
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "404":
          description: 标签不存在
          schema:
            $ref: '#/definitions/api.ErrorResponse'
        "500":
          description: 内部服务器错误
          schema:
            $ref: '#/definitions/api.ErrorResponse'
      summary: 获取指定标签的应用
      tags:
      - 标签管理
  /users/login:
    post:
      consumes:
      - application/json
      description: 登录获取认证令牌
      parameters:
      - description: 登录信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功，返回token和用户信息
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
              type: object
        "400":
          description: 参数错误/用户名或密码错误
          schema:
            $ref: '#/definitions/api.Response'
        "403":
          description: 账号已被禁用
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 用户登录
      tags:
      - 用户
  /users/logout:
    post:
      consumes:
      - application/json
      description: 使当前用户的JWT令牌失效
      produces:
      - application/json
      responses:
        "200":
          description: 登出成功
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 用户退出登录
      tags:
      - 用户
  /users/profile:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的详细资料
      produces:
      - application/json
      responses:
        "200":
          description: 返回用户资料
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.UserResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 获取用户资料
      tags:
      - 用户
    put:
      consumes:
      - application/json
      description: 更新当前登录用户的资料信息
      parameters:
      - description: 更新的用户资料
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.UpdateProfileRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功，返回更新后的用户资料
          schema:
            allOf:
            - $ref: '#/definitions/api.Response'
            - properties:
                data:
                  $ref: '#/definitions/api.UserResponse'
              type: object
        "400":
          description: 参数错误/用户名或邮箱已存在/密码错误
          schema:
            $ref: '#/definitions/api.Response'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/api.Response'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      security:
      - Bearer: []
      summary: 更新用户资料
      tags:
      - 用户
  /users/register:
    post:
      consumes:
      - application/json
      description: 创建新用户账号
      parameters:
      - description: 用户注册信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/api.RegisterRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 注册成功
          schema:
            $ref: '#/definitions/api.Response'
        "400":
          description: 参数错误/用户名或邮箱已存在
          schema:
            $ref: '#/definitions/api.Response'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/api.Response'
      summary: 用户注册
      tags:
      - 用户
schemes:
- http
- https
securityDefinitions:
  Bearer:
    description: 请在值前加上 "Bearer " 前缀, 例如 "Bearer abcde12345". 所有需要认证的API都需要在请求头中带上此令牌。
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
