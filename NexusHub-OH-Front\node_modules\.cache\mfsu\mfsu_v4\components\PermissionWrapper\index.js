"use strict";
import { Fragment, jsx } from "react/jsx-runtime";
import { useAccess } from "@umijs/max";
import { Result, Button } from "antd";
import { history } from "@umijs/max";
const PermissionWrapper = ({
  children,
  permission,
  fallback,
  requireAll = false
}) => {
  const access = useAccess();
  if (!permission) {
    return /* @__PURE__ */ jsx(Fragment, { children });
  }
  const checkPermission = () => {
    if (typeof permission === "string") {
      return access[permission] || false;
    }
    if (Array.isArray(permission)) {
      if (requireAll) {
        return permission.every((p) => access[p] || false);
      } else {
        return permission.some((p) => access[p] || false);
      }
    }
    return false;
  };
  const hasPermission = checkPermission();
  if (hasPermission) {
    return /* @__PURE__ */ jsx(Fragment, { children });
  }
  if (fallback) {
    return /* @__PURE__ */ jsx(Fragment, { children: fallback });
  }
  return /* @__PURE__ */ jsx(
    Result,
    {
      status: "403",
      title: "403",
      subTitle: "\u62B1\u6B49\uFF0C\u60A8\u6CA1\u6709\u6743\u9650\u8BBF\u95EE\u6B64\u9875\u9762\u3002",
      extra: /* @__PURE__ */ jsx(Button, { type: "primary", onClick: () => history.back(), children: "\u8FD4\u56DE\u4E0A\u4E00\u9875" })
    }
  );
};
export default PermissionWrapper;
export const usePermission = () => {
  const access = useAccess();
  const checkPermission = (permission, requireAll = false) => {
    if (typeof permission === "string") {
      return access[permission] || false;
    }
    if (Array.isArray(permission)) {
      if (requireAll) {
        return permission.every((p) => access[p] || false);
      } else {
        return permission.some((p) => access[p] || false);
      }
    }
    return false;
  };
  return {
    checkPermission,
    access
  };
};
export const PermissionButton = ({
  permission,
  requireAll = false,
  children,
  fallback = null
}) => {
  const { checkPermission } = usePermission();
  const hasPermission = checkPermission(permission, requireAll);
  if (hasPermission) {
    return /* @__PURE__ */ jsx(Fragment, { children });
  }
  return /* @__PURE__ */ jsx(Fragment, { children: fallback });
};
