"use strict";
import { request } from "@umijs/max";
export const getAppVersions = async (appId, params) => {
  return request(`/apps/${appId}/versions`, {
    method: "GET",
    params
  });
};
export const getAppVersionDetail = async (appId, versionId) => {
  return request(`/apps/${appId}/versions/${versionId}`, {
    method: "GET"
  });
};
export const createAppVersion = async (appId, data) => {
  return request(`/apps/${appId}/versions`, {
    method: "POST",
    data
  });
};
export const updateAppVersion = async (appId, versionId, data) => {
  return request(`/apps/${appId}/versions/${versionId}`, {
    method: "PUT",
    data
  });
};
export const deleteAppVersion = async (appId, versionId) => {
  return request(`/apps/${appId}/versions/${versionId}`, {
    method: "DELETE"
  });
};
export const getPendingVersions = async (params) => {
  return request("/admin/versions/pending", {
    method: "GET",
    params
  });
};
export const reviewAppVersion = async (versionId, data) => {
  return request(`/admin/versions/${versionId}/review`, {
    method: "POST",
    data
  });
};
export const publishAppVersion = async (appId, versionId) => {
  return request(`/apps/${appId}/versions/${versionId}/publish`, {
    method: "POST"
  });
};
export const unpublishAppVersion = async (appId, versionId) => {
  return request(`/apps/${appId}/versions/${versionId}/unpublish`, {
    method: "POST"
  });
};
