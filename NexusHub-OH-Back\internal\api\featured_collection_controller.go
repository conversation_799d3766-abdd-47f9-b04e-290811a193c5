package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"nexushub-oh-back/internal/models"
)

// PaginatedData 分页数据结构体
type PaginatedData struct {
	Items      interface{} `json:"list"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// FeaturedCollectionController 精选集控制器
type FeaturedCollectionController struct {
	db *gorm.DB
}

// NewFeaturedCollectionController 创建精选集控制器
func NewFeaturedCollectionController(db *gorm.DB) *FeaturedCollectionController {
	return &FeaturedCollectionController{
		db: db,
	}
}

// FeaturedCollectionResponse 精选集响应结构
type FeaturedCollectionResponse struct {
	ID          uint                 `json:"id"`
	Title       string               `json:"title"`
	Description string               `json:"description"`
	Icon        string               `json:"icon"`
	CoverImage  string               `json:"cover_image"`
	SortOrder   int                  `json:"sort_order"`
	IsActive    bool                 `json:"is_active"`
	IsPublic    bool                 `json:"is_public"`
	CreatorID   uint                 `json:"creator_id"`
	Creator     *models.User         `json:"creator,omitempty"`
	Apps        []models.Application `json:"apps,omitempty"`
	CreatedAt   string               `json:"created_at"`
	UpdatedAt   string               `json:"updated_at"`
}

// CreateFeaturedCollectionRequest 创建精选集请求结构
type CreateFeaturedCollectionRequest struct {
	Title       string `json:"title" binding:"required,min=1,max=100"`
	Description string `json:"description"`
	Icon        string `json:"icon"`
	CoverImage  string `json:"cover_image"`
	SortOrder   int    `json:"sort_order"`
	IsActive    *bool  `json:"is_active"`
	IsPublic    *bool  `json:"is_public"`
}

// UpdateFeaturedCollectionRequest 更新精选集请求结构
type UpdateFeaturedCollectionRequest struct {
	Title       string `json:"title" binding:"omitempty,min=1,max=100"`
	Description string `json:"description"`
	Icon        string `json:"icon"`
	CoverImage  string `json:"cover_image"`
	SortOrder   *int   `json:"sort_order"`
	IsActive    *bool  `json:"is_active"`
	IsPublic    *bool  `json:"is_public"`
}

// AddAppToCollectionRequest 向精选集添加应用请求结构
type AddAppToCollectionRequest struct {
	ApplicationID uint `json:"application_id" binding:"required"`
	SortOrder     int  `json:"sort_order"`
}

// CreateFeaturedCollection 创建精选集
// @Summary 创建精选集
// @Description 创建新的精选集（管理员权限）
// @Tags 精选集管理
// @Accept json
// @Produce json
// @Param request body CreateFeaturedCollectionRequest true "精选集信息"
// @Success 201 {object} Response{data=FeaturedCollectionResponse} "创建成功"
// @Failure 400 {object} Response "请求参数错误"
// @Failure 401 {object} Response "未授权"
// @Failure 403 {object} Response "权限不足"
// @Failure 500 {object} Response "服务器内部错误"
// @Router /api/v1/admin/featured-collections [post]
// @Security ApiKeyAuth
func (fc *FeaturedCollectionController) CreateFeaturedCollection(c *gin.Context) {
	var req CreateFeaturedCollectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    http.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Code:    http.StatusUnauthorized,
			Message: "未授权",
		})
		return
	}

	// 检查标题是否已存在
	existingCollection, err := models.GetFeaturedCollectionByTitle(fc.db, req.Title)
	if err == nil && existingCollection.ID > 0 {
		c.JSON(http.StatusBadRequest, Response{
			Code:    http.StatusBadRequest,
			Message: "精选集标题已存在",
		})
		return
	}

	// 设置默认值
	isActive := true
	if req.IsActive != nil {
		isActive = *req.IsActive
	}
	isPublic := true
	if req.IsPublic != nil {
		isPublic = *req.IsPublic
	}

	// 创建精选集
	collection := &models.FeaturedCollection{
		Title:       req.Title,
		Description: req.Description,
		Icon:        req.Icon,
		CoverImage:  req.CoverImage,
		SortOrder:   req.SortOrder,
		IsActive:    isActive,
		IsPublic:    isPublic,
		CreatorID:   userID.(uint),
	}

	if err := models.CreateFeaturedCollection(fc.db, collection); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    http.StatusInternalServerError,
			Message: "创建精选集失败: " + err.Error(),
		})
		return
	}

	// 获取完整的精选集信息
	createdCollection, err := models.GetFeaturedCollectionByID(fc.db, collection.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    http.StatusInternalServerError,
			Message: "获取精选集信息失败: " + err.Error(),
		})
		return
	}

	response := &FeaturedCollectionResponse{
		ID:          createdCollection.ID,
		Title:       createdCollection.Title,
		Description: createdCollection.Description,
		Icon:        createdCollection.Icon,
		CoverImage:  createdCollection.CoverImage,
		SortOrder:   createdCollection.SortOrder,
		IsActive:    createdCollection.IsActive,
		IsPublic:    createdCollection.IsPublic,
		CreatorID:   createdCollection.CreatorID,
		Creator:     &createdCollection.Creator,
		CreatedAt:   createdCollection.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   createdCollection.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	c.JSON(http.StatusCreated, Response{
		Code:    http.StatusCreated,
		Message: "精选集创建成功",
		Data:    response,
	})
}

// GetFeaturedCollection 获取精选集详情
// @Summary 获取精选集详情
// @Description 获取指定精选集的详细信息（公开接口）
// @Tags 精选集
// @Accept json
// @Produce json
// @Param id path int true "精选集ID"
// @Success 200 {object} Response{data=FeaturedCollectionResponse} "获取成功"
// @Failure 400 {object} Response "请求参数错误"
// @Failure 404 {object} Response "精选集不存在"
// @Failure 500 {object} Response "服务器内部错误"
// @Router /api/v1/public/featured-collections/{id} [get]
func (fc *FeaturedCollectionController) GetFeaturedCollection(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    http.StatusBadRequest,
			Message: "无效的精选集ID",
		})
		return
	}

	collection, err := models.GetFeaturedCollectionByID(fc.db, uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, Response{
				Code:    http.StatusNotFound,
				Message: "精选集不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, Response{
				Code:    http.StatusInternalServerError,
				Message: "获取精选集失败: " + err.Error(),
			})
		}
		return
	}

	// 检查是否为公开精选集
	if !collection.IsPublic {
		c.JSON(http.StatusNotFound, Response{
			Code:    http.StatusNotFound,
			Message: "精选集不存在",
		})
		return
	}

	response := &FeaturedCollectionResponse{
		ID:          collection.ID,
		Title:       collection.Title,
		Description: collection.Description,
		Icon:        collection.Icon,
		CoverImage:  collection.CoverImage,
		SortOrder:   collection.SortOrder,
		IsActive:    collection.IsActive,
		IsPublic:    collection.IsPublic,
		CreatorID:   collection.CreatorID,
		Creator:     &collection.Creator,
		Apps:        collection.Apps,
		CreatedAt:   collection.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   collection.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	SuccessWithMessage(c, "获取精选集成功", response)
}

// ListFeaturedCollections 获取精选集列表
// @Summary 获取精选集列表
// @Description 获取精选集列表（公开接口）
// @Tags 精选集
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} Response{data=PaginatedData{list=[]FeaturedCollectionResponse}} "获取成功"
// @Failure 400 {object} Response "请求参数错误"
// @Failure 500 {object} Response "服务器内部错误"
// @Router /api/v1/public/featured-collections [get]
func (fc *FeaturedCollectionController) ListFeaturedCollections(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 只获取公开且活跃的精选集
	collections, total, err := models.ListFeaturedCollections(fc.db, false, false, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    http.StatusInternalServerError,
			Message: "获取精选集列表失败: " + err.Error(),
		})
		return
	}

	var items []FeaturedCollectionResponse
	for _, collection := range collections {
		item := FeaturedCollectionResponse{
			ID:          collection.ID,
			Title:       collection.Title,
			Description: collection.Description,
			Icon:        collection.Icon,
			CoverImage:  collection.CoverImage,
			SortOrder:   collection.SortOrder,
			IsActive:    collection.IsActive,
			IsPublic:    collection.IsPublic,
			CreatorID:   collection.CreatorID,
			Creator:     &collection.Creator,
			CreatedAt:   collection.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   collection.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		items = append(items, item)
	}

	response := PaginatedData{
		Items:      items,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
	}

	SuccessWithMessage(c, "获取精选集列表成功", response)
}

// UpdateFeaturedCollection 更新精选集
// @Summary 更新精选集
// @Description 更新精选集信息（管理员权限）
// @Tags 精选集管理
// @Accept json
// @Produce json
// @Param id path int true "精选集ID"
// @Param request body UpdateFeaturedCollectionRequest true "更新信息"
// @Success 200 {object} Response{data=FeaturedCollectionResponse} "更新成功"
// @Failure 400 {object} Response "请求参数错误"
// @Failure 401 {object} Response "未授权"
// @Failure 403 {object} Response "权限不足"
// @Failure 404 {object} Response "精选集不存在"
// @Failure 500 {object} Response "服务器内部错误"
// @Router /api/v1/admin/featured-collections/{id} [put]
// @Security ApiKeyAuth
func (fc *FeaturedCollectionController) UpdateFeaturedCollection(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    http.StatusBadRequest,
			Message: "无效的精选集ID",
		})
		return
	}

	var req UpdateFeaturedCollectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    http.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取现有精选集
	collection, err := models.GetFeaturedCollectionByID(fc.db, uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, Response{
				Code:    http.StatusNotFound,
				Message: "精选集不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, Response{
				Code:    http.StatusInternalServerError,
				Message: "获取精选集失败: " + err.Error(),
			})
		}
		return
	}

	// 检查标题是否与其他精选集冲突
	if req.Title != "" && req.Title != collection.Title {
		existingCollection, err := models.GetFeaturedCollectionByTitle(fc.db, req.Title)
		if err == nil && existingCollection.ID != collection.ID {
			c.JSON(http.StatusBadRequest, Response{
				Code:    http.StatusBadRequest,
				Message: "精选集标题已存在",
			})
			return
		}
	}

	// 更新字段
	if req.Title != "" {
		collection.Title = req.Title
	}
	if req.Description != "" {
		collection.Description = req.Description
	}
	if req.Icon != "" {
		collection.Icon = req.Icon
	}
	if req.CoverImage != "" {
		collection.CoverImage = req.CoverImage
	}
	if req.SortOrder != nil {
		collection.SortOrder = *req.SortOrder
	}
	if req.IsActive != nil {
		collection.IsActive = *req.IsActive
	}
	if req.IsPublic != nil {
		collection.IsPublic = *req.IsPublic
	}

	if err := models.UpdateFeaturedCollection(fc.db, collection); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    http.StatusInternalServerError,
			Message: "更新精选集失败: " + err.Error(),
		})
		return
	}

	// 获取更新后的完整信息
	updatedCollection, err := models.GetFeaturedCollectionByID(fc.db, collection.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    http.StatusInternalServerError,
			Message: "获取更新后的精选集信息失败: " + err.Error(),
		})
		return
	}

	response := &FeaturedCollectionResponse{
		ID:          updatedCollection.ID,
		Title:       updatedCollection.Title,
		Description: updatedCollection.Description,
		Icon:        updatedCollection.Icon,
		CoverImage:  updatedCollection.CoverImage,
		SortOrder:   updatedCollection.SortOrder,
		IsActive:    updatedCollection.IsActive,
		IsPublic:    updatedCollection.IsPublic,
		CreatorID:   updatedCollection.CreatorID,
		Creator:     &updatedCollection.Creator,
		CreatedAt:   updatedCollection.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   updatedCollection.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	SuccessWithMessage(c, "精选集更新成功", response)
}

// DeleteFeaturedCollection 删除精选集
// @Summary 删除精选集
// @Description 删除指定精选集（管理员权限）
// @Tags 精选集管理
// @Accept json
// @Produce json
// @Param id path int true "精选集ID"
// @Success 200 {object} Response "删除成功"
// @Failure 400 {object} Response "请求参数错误"
// @Failure 401 {object} Response "未授权"
// @Failure 403 {object} Response "权限不足"
// @Failure 404 {object} Response "精选集不存在"
// @Failure 500 {object} Response "服务器内部错误"
// @Router /api/v1/admin/featured-collections/{id} [delete]
// @Security ApiKeyAuth
func (fc *FeaturedCollectionController) DeleteFeaturedCollection(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    http.StatusBadRequest,
			Message: "无效的精选集ID",
		})
		return
	}

	// 检查精选集是否存在
	_, err = models.GetFeaturedCollectionByID(fc.db, uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, Response{
				Code:    http.StatusNotFound,
				Message: "精选集不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, Response{
				Code:    http.StatusInternalServerError,
				Message: "获取精选集失败: " + err.Error(),
			})
		}
		return
	}

	if err := models.DeleteFeaturedCollection(fc.db, uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    http.StatusInternalServerError,
			Message: "删除精选集失败: " + err.Error(),
		})
		return
	}

	SuccessWithMessage(c, "精选集删除成功", nil)
}

// AddAppToFeaturedCollection 向精选集添加应用
// @Summary 向精选集添加应用
// @Description 向指定精选集添加应用（管理员权限）
// @Tags 精选集管理
// @Accept json
// @Produce json
// @Param id path int true "精选集ID"
// @Param request body AddAppToCollectionRequest true "应用信息"
// @Success 200 {object} Response "添加成功"
// @Failure 400 {object} Response "请求参数错误"
// @Failure 401 {object} Response "未授权"
// @Failure 403 {object} Response "权限不足"
// @Failure 404 {object} Response "精选集或应用不存在"
// @Failure 500 {object} Response "服务器内部错误"
// @Router /api/v1/admin/featured-collections/{id}/apps [post]
// @Security ApiKeyAuth
func (fc *FeaturedCollectionController) AddAppToFeaturedCollection(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    http.StatusBadRequest,
			Message: "无效的精选集ID",
		})
		return
	}

	var req AddAppToCollectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    http.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 检查精选集是否存在
	_, err = models.GetFeaturedCollectionByID(fc.db, uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, Response{
				Code:    http.StatusNotFound,
				Message: "精选集不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, Response{
				Code:    http.StatusInternalServerError,
				Message: "获取精选集失败: " + err.Error(),
			})
		}
		return
	}

	// 检查应用是否存在
	_, err = models.GetApplicationByID(fc.db, req.ApplicationID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, Response{
				Code:    http.StatusNotFound,
				Message: "应用不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, Response{
				Code:    http.StatusInternalServerError,
				Message: "获取应用失败: " + err.Error(),
			})
		}
		return
	}

	if err := models.AddAppToFeaturedCollection(fc.db, uint(id), req.ApplicationID, req.SortOrder); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    http.StatusInternalServerError,
			Message: "添加应用到精选集失败: " + err.Error(),
		})
		return
	}

	SuccessWithMessage(c, "应用添加到精选集成功", nil)
}

// RemoveAppFromFeaturedCollection 从精选集移除应用
// @Summary 从精选集移除应用
// @Description 从指定精选集移除应用（管理员权限）
// @Tags 精选集管理
// @Accept json
// @Produce json
// @Param id path int true "精选集ID"
// @Param app_id path int true "应用ID"
// @Success 200 {object} Response "移除成功"
// @Failure 400 {object} Response "请求参数错误"
// @Failure 401 {object} Response "未授权"
// @Failure 403 {object} Response "权限不足"
// @Failure 404 {object} Response "精选集或应用不存在"
// @Failure 500 {object} Response "服务器内部错误"
// @Router /api/v1/admin/featured-collections/{id}/apps/{app_id} [delete]
// @Security ApiKeyAuth
func (fc *FeaturedCollectionController) RemoveAppFromFeaturedCollection(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    http.StatusBadRequest,
			Message: "无效的精选集ID",
		})
		return
	}

	appIDStr := c.Param("app_id")
	appID, err := strconv.ParseUint(appIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    http.StatusBadRequest,
			Message: "无效的应用ID",
		})
		return
	}

	// 检查精选集是否存在
	_, err = models.GetFeaturedCollectionByID(fc.db, uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, Response{
				Code:    http.StatusNotFound,
				Message: "精选集不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, Response{
				Code:    http.StatusInternalServerError,
				Message: "获取精选集失败: " + err.Error(),
			})
		}
		return
	}

	if err := models.RemoveAppFromFeaturedCollection(fc.db, uint(id), uint(appID)); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    http.StatusInternalServerError,
			Message: "从精选集移除应用失败: " + err.Error(),
		})
		return
	}

	SuccessWithMessage(c, "应用从精选集移除成功", nil)
}

// GetFeaturedCollectionApps 获取精选集中的应用列表
// @Summary 获取精选集中的应用列表
// @Description 获取指定精选集中的应用列表（公开接口）
// @Tags 精选集
// @Accept json
// @Produce json
// @Param id path int true "精选集ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} Response{data=PaginatedData{list=[]models.Application}} "获取成功"
// @Failure 400 {object} Response "请求参数错误"
// @Failure 404 {object} Response "精选集不存在"
// @Failure 500 {object} Response "服务器内部错误"
// @Router /api/v1/public/featured-collections/{id}/apps [get]
func (fc *FeaturedCollectionController) GetFeaturedCollectionApps(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Code:    http.StatusBadRequest,
			Message: "无效的精选集ID",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 检查精选集是否存在且为公开
	collection, err := models.GetFeaturedCollectionByID(fc.db, uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, Response{
				Code:    http.StatusNotFound,
				Message: "精选集不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, Response{
				Code:    http.StatusInternalServerError,
				Message: "获取精选集失败: " + err.Error(),
			})
		}
		return
	}

	if !collection.IsPublic {
		c.JSON(http.StatusNotFound, Response{
			Code:    http.StatusNotFound,
			Message: "精选集不存在",
		})
		return
	}

	apps, total, err := models.GetFeaturedCollectionApps(fc.db, uint(id), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    http.StatusInternalServerError,
			Message: "获取精选集应用列表失败: " + err.Error(),
		})
		return
	}

	response := PaginatedData{
		Items:      apps,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
	}

	SuccessWithMessage(c, "获取精选集应用列表成功", response)
}

// ListAllFeaturedCollections 获取所有精选集列表（管理员）
// @Summary 获取所有精选集列表
// @Description 获取所有精选集列表，包括非公开和非活跃的（管理员权限）
// @Tags 精选集管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param include_inactive query bool false "是否包含非活跃精选集" default(false)
// @Param include_private query bool false "是否包含非公开精选集" default(false)
// @Success 200 {object} Response{data=PaginatedData{list=[]FeaturedCollectionResponse}} "获取成功"
// @Failure 400 {object} Response "请求参数错误"
// @Failure 401 {object} Response "未授权"
// @Failure 403 {object} Response "权限不足"
// @Failure 500 {object} Response "服务器内部错误"
// @Router /api/v1/admin/featured-collections [get]
// @Security ApiKeyAuth
func (fc *FeaturedCollectionController) ListAllFeaturedCollections(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	includeInactive, _ := strconv.ParseBool(c.DefaultQuery("include_inactive", "false"))
	includePrivate, _ := strconv.ParseBool(c.DefaultQuery("include_private", "false"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	collections, total, err := models.ListFeaturedCollections(fc.db, includeInactive, includePrivate, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Code:    http.StatusInternalServerError,
			Message: "获取精选集列表失败: " + err.Error(),
		})
		return
	}

	var items []FeaturedCollectionResponse
	for _, collection := range collections {
		item := FeaturedCollectionResponse{
			ID:          collection.ID,
			Title:       collection.Title,
			Description: collection.Description,
			Icon:        collection.Icon,
			CoverImage:  collection.CoverImage,
			SortOrder:   collection.SortOrder,
			IsActive:    collection.IsActive,
			IsPublic:    collection.IsPublic,
			CreatorID:   collection.CreatorID,
			Creator:     &collection.Creator,
			CreatedAt:   collection.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   collection.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		items = append(items, item)
	}

	response := PaginatedData{
		Items:      items,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
	}

	SuccessWithMessage(c, "获取精选集列表成功", response)
}
