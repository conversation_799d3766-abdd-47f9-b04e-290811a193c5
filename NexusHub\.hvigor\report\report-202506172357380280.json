{"version": "2.0", "ppid": 29880, "events": [{"head": {"id": "675890ae-2da0-40a5-b7fe-13ca75bee80f", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236162454500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84682add-18a0-4fb2-8921-f34ac1e3a221", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236162978300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00080260-dbb0-40a0-845b-c9046b8da483", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236165241100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92831903-f1c8-4ac3-9b99-26a50d9006f5", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236165535800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99d03ac3-864b-4fda-b07b-fe8fbe8673f8", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236169870900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88525ce3-0359-4f54-8404-5f7e647bd395", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236170355900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "462a6cd5-f761-4faf-bd37-35ab7152ff3b", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236170569900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de627f64-b325-423d-9d0e-22d33e7989a7", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236174636400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d62c79a6-ac0b-431a-ac67-1f0c3b3bf110", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236232340900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6aa33c2d-d073-4445-bc04-6d9e57b63a21", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228224619200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "876d3bf3-76e4-4090-91ee-f68fcc29246c", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228258841500, "endTime": 151228650745900}, "additional": {"children": ["19fd22bf-ad37-40c0-8631-55349ad06ae7", "5661447a-309c-47ea-bee3-a616ca06373b", "3b5a0b5e-f713-4aee-96d3-0b63aac5773a", "bdc582bc-ad19-4878-a78b-3f9824b77224", "cd542ef5-6c80-48f3-a6b8-b864256c6b02", "4bda1b66-469e-42e3-9593-f1754f7fce74", "2785048a-9bc1-4ad6-a883-eaba6b60093f"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "e8f75ccd-0440-422a-900b-dacf92dc9273"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19fd22bf-ad37-40c0-8631-55349ad06ae7", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228258849400, "endTime": 151228293537100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "876d3bf3-76e4-4090-91ee-f68fcc29246c", "logId": "9a0f6c58-7f54-4c81-838b-26a7ad41d17f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5661447a-309c-47ea-bee3-a616ca06373b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228293569600, "endTime": 151228649373500}, "additional": {"children": ["6cae86d8-847f-4f97-a90a-4b1d304d619d", "95296d8b-aae7-4f47-bbb4-b47d958ce87a", "0e762708-6efd-45de-85d7-17407106a45f", "5c469ca4-9948-4a4a-99a8-729374d20342", "02dcb800-d930-4cb3-a2ab-06fa7552df84", "31afc63b-18fd-48c3-a36e-621940099fc7", "d50d61da-31e6-4868-bd20-0ba58fcb6476", "e855dd59-ef98-4082-80cd-61af31c88beb", "3d8cc8bb-8e6a-4337-b053-869ae0233f90"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "876d3bf3-76e4-4090-91ee-f68fcc29246c", "logId": "3eb511b6-c24e-404a-bb7e-8f7e26edec81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b5a0b5e-f713-4aee-96d3-0b63aac5773a", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228649400700, "endTime": 151228650709000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "876d3bf3-76e4-4090-91ee-f68fcc29246c", "logId": "b51285c5-3e62-4ea7-a87f-eb0f55ac6fdb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdc582bc-ad19-4878-a78b-3f9824b77224", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228650714300, "endTime": 151228650736100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "876d3bf3-76e4-4090-91ee-f68fcc29246c", "logId": "1b5fdc18-0d93-49c5-bed2-b803f1e89623"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd542ef5-6c80-48f3-a6b8-b864256c6b02", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228267578600, "endTime": 151228267836700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "876d3bf3-76e4-4090-91ee-f68fcc29246c", "logId": "912fd964-29b3-4119-8248-886f2e3d5d9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "912fd964-29b3-4119-8248-886f2e3d5d9f", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228267578600, "endTime": 151228267836700}, "additional": {"logType": "info", "children": [], "durationId": "cd542ef5-6c80-48f3-a6b8-b864256c6b02", "parent": "e8f75ccd-0440-422a-900b-dacf92dc9273"}}, {"head": {"id": "4bda1b66-469e-42e3-9593-f1754f7fce74", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228277916600, "endTime": 151228277993400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "876d3bf3-76e4-4090-91ee-f68fcc29246c", "logId": "8273de50-a6c0-490d-9b53-062f2e784502"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8273de50-a6c0-490d-9b53-062f2e784502", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228277916600, "endTime": 151228277993400}, "additional": {"logType": "info", "children": [], "durationId": "4bda1b66-469e-42e3-9593-f1754f7fce74", "parent": "e8f75ccd-0440-422a-900b-dacf92dc9273"}}, {"head": {"id": "89e69ba3-2db8-4c7f-be76-ae7d2b7e9e8e", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228279061700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58912796-b4aa-424c-bb78-fab5a3f8d157", "name": "Cache service initialization finished in 14 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228293203100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a0f6c58-7f54-4c81-838b-26a7ad41d17f", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228258849400, "endTime": 151228293537100}, "additional": {"logType": "info", "children": [], "durationId": "19fd22bf-ad37-40c0-8631-55349ad06ae7", "parent": "e8f75ccd-0440-422a-900b-dacf92dc9273"}}, {"head": {"id": "6cae86d8-847f-4f97-a90a-4b1d304d619d", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228304706800, "endTime": 151228304727800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5661447a-309c-47ea-bee3-a616ca06373b", "logId": "df686fed-3b3a-4377-9a0d-c7e23f0138b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95296d8b-aae7-4f47-bbb4-b47d958ce87a", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228304747900, "endTime": 151228316175000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5661447a-309c-47ea-bee3-a616ca06373b", "logId": "77236da0-6bcc-44c0-a4e9-35a176f8a95c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e762708-6efd-45de-85d7-17407106a45f", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228316193100, "endTime": 151228483483800}, "additional": {"children": ["48011a18-3e24-4886-bfcc-c23babb73346", "25000ed6-a536-4e12-ac2f-0583e5dd3a06", "0a5b7e78-6acf-462d-9de0-8380294e992d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5661447a-309c-47ea-bee3-a616ca06373b", "logId": "02a7be5b-a5c0-459a-b6ee-2063dd4d4485"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c469ca4-9948-4a4a-99a8-729374d20342", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228483495100, "endTime": 151228520510000}, "additional": {"children": ["ed73accd-7b44-450d-bd13-efed38e0470d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5661447a-309c-47ea-bee3-a616ca06373b", "logId": "5f168d11-9b94-4fbb-92ed-1afa44bb8234"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02dcb800-d930-4cb3-a2ab-06fa7552df84", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228520522700, "endTime": 151228604879800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5661447a-309c-47ea-bee3-a616ca06373b", "logId": "b6c235a2-0fbc-46c1-aead-363af0492d72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31afc63b-18fd-48c3-a36e-621940099fc7", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228607251200, "endTime": 151228626112300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5661447a-309c-47ea-bee3-a616ca06373b", "logId": "deb921df-f4fe-4898-8519-d28c70f5ce69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d50d61da-31e6-4868-bd20-0ba58fcb6476", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228626148100, "endTime": 151228649193600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5661447a-309c-47ea-bee3-a616ca06373b", "logId": "8a171033-0dba-4343-adf4-2cc191c83d11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e855dd59-ef98-4082-80cd-61af31c88beb", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228649217100, "endTime": 151228649359600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5661447a-309c-47ea-bee3-a616ca06373b", "logId": "6c664c3a-cbf2-473f-8193-1f785774093e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df686fed-3b3a-4377-9a0d-c7e23f0138b4", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228304706800, "endTime": 151228304727800}, "additional": {"logType": "info", "children": [], "durationId": "6cae86d8-847f-4f97-a90a-4b1d304d619d", "parent": "3eb511b6-c24e-404a-bb7e-8f7e26edec81"}}, {"head": {"id": "77236da0-6bcc-44c0-a4e9-35a176f8a95c", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228304747900, "endTime": 151228316175000}, "additional": {"logType": "info", "children": [], "durationId": "95296d8b-aae7-4f47-bbb4-b47d958ce87a", "parent": "3eb511b6-c24e-404a-bb7e-8f7e26edec81"}}, {"head": {"id": "48011a18-3e24-4886-bfcc-c23babb73346", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228317378200, "endTime": 151228317400400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0e762708-6efd-45de-85d7-17407106a45f", "logId": "1ea920d6-ab2c-4459-a4c5-38e56c7a9403"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ea920d6-ab2c-4459-a4c5-38e56c7a9403", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228317378200, "endTime": 151228317400400}, "additional": {"logType": "info", "children": [], "durationId": "48011a18-3e24-4886-bfcc-c23babb73346", "parent": "02a7be5b-a5c0-459a-b6ee-2063dd4d4485"}}, {"head": {"id": "25000ed6-a536-4e12-ac2f-0583e5dd3a06", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228321728800, "endTime": 151228482925600}, "additional": {"children": ["d34c6278-132a-4153-b395-a8067cf5b35b", "5a39f7ed-1fbf-4630-a632-571942b1b5e0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0e762708-6efd-45de-85d7-17407106a45f", "logId": "2899e04e-490b-4ea4-9f94-b82882f13e16"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d34c6278-132a-4153-b395-a8067cf5b35b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228321730500, "endTime": 151228340887300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "25000ed6-a536-4e12-ac2f-0583e5dd3a06", "logId": "33ea4c50-8ae1-45ec-988c-4b66d7d7f883"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a39f7ed-1fbf-4630-a632-571942b1b5e0", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228340916500, "endTime": 151228482916500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "25000ed6-a536-4e12-ac2f-0583e5dd3a06", "logId": "6cf822bb-833b-40c1-8a85-fc51747a805a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff31f1cf-70f2-4346-bed7-4cc2f28e4fdb", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228321741600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d20db9af-9b17-496a-89de-540784b805c7", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228340596900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33ea4c50-8ae1-45ec-988c-4b66d7d7f883", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228321730500, "endTime": 151228340887300}, "additional": {"logType": "info", "children": [], "durationId": "d34c6278-132a-4153-b395-a8067cf5b35b", "parent": "2899e04e-490b-4ea4-9f94-b82882f13e16"}}, {"head": {"id": "a2edf611-c015-4ab8-b712-66a68aacc737", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228341035800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe6edf27-744e-4e1d-92f4-0842b69b6c92", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228368435500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7255b764-4099-4d43-b54c-b32c5295479e", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228368732100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d9d1cd9-595e-4bdd-ae46-ce804cb9e0c3", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228369016000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4375793-f0ab-4fa1-978e-0b6ccaf33521", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228369510500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d95b0796-71dc-49cc-844d-8c1a02d04cb3", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228374905700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0710fd72-fca9-4929-b229-0564552bbf02", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228405459200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4280f448-58d4-4568-98bf-4ebe3ec6a326", "name": "Sdk init in 54 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228441839800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8b75c7d-4862-4f50-a561-46a3912bce5b", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228442161900}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 23, "minute": 57, "second": 38}, "markType": "other"}}, {"head": {"id": "2b2ed469-13a6-48a7-b9ef-b94429ea654a", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228442256300}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 23, "minute": 57, "second": 38}, "markType": "other"}}, {"head": {"id": "d9ec93a1-7e4f-4473-8bb3-e0e9c60152bb", "name": "Project task initialization takes 38 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228482645200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99514303-2273-46bf-9d7b-34e8840a1fdf", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228482763600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b9a31a9-9752-4f9b-a7fd-cf9a0f9694e4", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228482843900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b9abc1-a392-41fc-aa17-8058b6e47edf", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228482880500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cf822bb-833b-40c1-8a85-fc51747a805a", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228340916500, "endTime": 151228482916500}, "additional": {"logType": "info", "children": [], "durationId": "5a39f7ed-1fbf-4630-a632-571942b1b5e0", "parent": "2899e04e-490b-4ea4-9f94-b82882f13e16"}}, {"head": {"id": "2899e04e-490b-4ea4-9f94-b82882f13e16", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228321728800, "endTime": 151228482925600}, "additional": {"logType": "info", "children": ["33ea4c50-8ae1-45ec-988c-4b66d7d7f883", "6cf822bb-833b-40c1-8a85-fc51747a805a"], "durationId": "25000ed6-a536-4e12-ac2f-0583e5dd3a06", "parent": "02a7be5b-a5c0-459a-b6ee-2063dd4d4485"}}, {"head": {"id": "0a5b7e78-6acf-462d-9de0-8380294e992d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228483447600, "endTime": 151228483465000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0e762708-6efd-45de-85d7-17407106a45f", "logId": "95707a75-db85-4648-92c1-4265e266f710"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95707a75-db85-4648-92c1-4265e266f710", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228483447600, "endTime": 151228483465000}, "additional": {"logType": "info", "children": [], "durationId": "0a5b7e78-6acf-462d-9de0-8380294e992d", "parent": "02a7be5b-a5c0-459a-b6ee-2063dd4d4485"}}, {"head": {"id": "02a7be5b-a5c0-459a-b6ee-2063dd4d4485", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228316193100, "endTime": 151228483483800}, "additional": {"logType": "info", "children": ["1ea920d6-ab2c-4459-a4c5-38e56c7a9403", "2899e04e-490b-4ea4-9f94-b82882f13e16", "95707a75-db85-4648-92c1-4265e266f710"], "durationId": "0e762708-6efd-45de-85d7-17407106a45f", "parent": "3eb511b6-c24e-404a-bb7e-8f7e26edec81"}}, {"head": {"id": "ed73accd-7b44-450d-bd13-efed38e0470d", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228483934700, "endTime": 151228520490500}, "additional": {"children": ["1e2e0870-26e4-4026-8f6e-e88f9d564c60", "99af7ead-c5be-411c-bb82-e8d3413c678d", "85fcba4e-e174-468b-942b-146c94f4dde9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5c469ca4-9948-4a4a-99a8-729374d20342", "logId": "e93dc83b-8fc5-44cc-88ec-b886241dbfc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e2e0870-26e4-4026-8f6e-e88f9d564c60", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228487401300, "endTime": 151228487420400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed73accd-7b44-450d-bd13-efed38e0470d", "logId": "0fed192f-6f7c-4675-b469-edbe31f18db1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0fed192f-6f7c-4675-b469-edbe31f18db1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228487401300, "endTime": 151228487420400}, "additional": {"logType": "info", "children": [], "durationId": "1e2e0870-26e4-4026-8f6e-e88f9d564c60", "parent": "e93dc83b-8fc5-44cc-88ec-b886241dbfc8"}}, {"head": {"id": "99af7ead-c5be-411c-bb82-e8d3413c678d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228490046500, "endTime": 151228517656600}, "additional": {"children": ["a9f0bcb0-fb60-4c7f-89ce-077da2667ba6", "d5228563-b5af-4d56-9c68-dbcc54e359a6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed73accd-7b44-450d-bd13-efed38e0470d", "logId": "15322ea6-2526-4ef1-9527-0fc22e57bbd3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9f0bcb0-fb60-4c7f-89ce-077da2667ba6", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228490047700, "endTime": 151228495277800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "99af7ead-c5be-411c-bb82-e8d3413c678d", "logId": "337aa626-e0b1-468a-b737-9d10f9648120"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5228563-b5af-4d56-9c68-dbcc54e359a6", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228495295700, "endTime": 151228517631000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "99af7ead-c5be-411c-bb82-e8d3413c678d", "logId": "89e2247f-277c-4cf9-811d-bb7b0e5752a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e308591-3c98-4300-9672-c3cd7b28eb5b", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228490053500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0508a406-0028-4dce-a685-35125be6b6b4", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228494584900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "337aa626-e0b1-468a-b737-9d10f9648120", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228490047700, "endTime": 151228495277800}, "additional": {"logType": "info", "children": [], "durationId": "a9f0bcb0-fb60-4c7f-89ce-077da2667ba6", "parent": "15322ea6-2526-4ef1-9527-0fc22e57bbd3"}}, {"head": {"id": "412a6583-0782-44ac-9896-a14491c42bc5", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228495314000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d67c01c-8174-47b6-ae58-aa06295c3ef2", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228505726000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdff4b4f-2134-43f0-bd79-79f381924733", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228505943100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecb4635e-3b44-4eab-98bb-737896fc74fa", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228506445100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "738a1714-a9f5-4701-8f0f-3feed2eb8fd6", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228506667300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0706533e-f48b-417c-a3da-e05644781c80", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228506779400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca5bbe73-9ff8-4629-bd23-9f1ce955eb8a", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228506870900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8a5140c-abd3-4433-9625-84a06854c1f1", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228506979200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbecb3d0-37dc-4b30-aed2-f0144d529c33", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228507069400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01b06743-d8cc-475c-92b5-dea78fc9addc", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228507796600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11e2946d-f1cb-4003-afad-40f26771dbee", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228508057700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b89086c-1ff3-4c56-aa9f-1457cacb868f", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228508197900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f27d9cd3-45f3-4025-8545-51f58012f457", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228508281500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b563ca83-e134-4f6f-9864-eb175cae701d", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228508410400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c6ff6f7-2f19-4984-b48f-c3e57b347bdb", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228508533100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79c4cc7c-b0f8-42fd-af3f-1f4c2f304d28", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228508733100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dc74450-4120-47e5-906f-ebd942b4bed8", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228508909200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f24db36-622e-46da-ba4a-ae4c63fbfef7", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228508989900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "219dfbf3-8634-4211-8833-9115e9b0b324", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228509059200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceb51cfd-cc58-4756-b6f0-fad76bb6cda0", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228509187800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "135f3831-d471-4b27-9c0a-e6807b192e17", "name": "Module entry task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228516929600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c275e968-1173-40ae-a659-a5c7e021e690", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228517200600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9008689-5f29-4dac-a25a-fb172cfa1d5b", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228517330300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64e94e3d-038f-4f91-b170-07401eb3e6b0", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228517428100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89e2247f-277c-4cf9-811d-bb7b0e5752a2", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228495295700, "endTime": 151228517631000}, "additional": {"logType": "info", "children": [], "durationId": "d5228563-b5af-4d56-9c68-dbcc54e359a6", "parent": "15322ea6-2526-4ef1-9527-0fc22e57bbd3"}}, {"head": {"id": "15322ea6-2526-4ef1-9527-0fc22e57bbd3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228490046500, "endTime": 151228517656600}, "additional": {"logType": "info", "children": ["337aa626-e0b1-468a-b737-9d10f9648120", "89e2247f-277c-4cf9-811d-bb7b0e5752a2"], "durationId": "99af7ead-c5be-411c-bb82-e8d3413c678d", "parent": "e93dc83b-8fc5-44cc-88ec-b886241dbfc8"}}, {"head": {"id": "85fcba4e-e174-468b-942b-146c94f4dde9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228520438700, "endTime": 151228520467300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed73accd-7b44-450d-bd13-efed38e0470d", "logId": "db207975-2495-4d3e-bdb4-fce0d34c93fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db207975-2495-4d3e-bdb4-fce0d34c93fb", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228520438700, "endTime": 151228520467300}, "additional": {"logType": "info", "children": [], "durationId": "85fcba4e-e174-468b-942b-146c94f4dde9", "parent": "e93dc83b-8fc5-44cc-88ec-b886241dbfc8"}}, {"head": {"id": "e93dc83b-8fc5-44cc-88ec-b886241dbfc8", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228483934700, "endTime": 151228520490500}, "additional": {"logType": "info", "children": ["0fed192f-6f7c-4675-b469-edbe31f18db1", "15322ea6-2526-4ef1-9527-0fc22e57bbd3", "db207975-2495-4d3e-bdb4-fce0d34c93fb"], "durationId": "ed73accd-7b44-450d-bd13-efed38e0470d", "parent": "5f168d11-9b94-4fbb-92ed-1afa44bb8234"}}, {"head": {"id": "5f168d11-9b94-4fbb-92ed-1afa44bb8234", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228483495100, "endTime": 151228520510000}, "additional": {"logType": "info", "children": ["e93dc83b-8fc5-44cc-88ec-b886241dbfc8"], "durationId": "5c469ca4-9948-4a4a-99a8-729374d20342", "parent": "3eb511b6-c24e-404a-bb7e-8f7e26edec81"}}, {"head": {"id": "e53a223e-7161-4028-95a8-59f70f602d36", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1953 more items\n]", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228554534000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69b192a9-f936-4223-8324-ac153cd13be6", "name": "hvigorfile, resolve hvigorfile dependencies in 85 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228604694600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6c235a2-0fbc-46c1-aead-363af0492d72", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228520522700, "endTime": 151228604879800}, "additional": {"logType": "info", "children": [], "durationId": "02dcb800-d930-4cb3-a2ab-06fa7552df84", "parent": "3eb511b6-c24e-404a-bb7e-8f7e26edec81"}}, {"head": {"id": "3d8cc8bb-8e6a-4337-b053-869ae0233f90", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228606671700, "endTime": 151228607228900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5661447a-309c-47ea-bee3-a616ca06373b", "logId": "5c77d74a-9641-43f4-9bb0-24c38abd3338"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4dbf89c9-7301-454a-bc05-a4ce1483cafa", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228606727900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c77d74a-9641-43f4-9bb0-24c38abd3338", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228606671700, "endTime": 151228607228900}, "additional": {"logType": "info", "children": [], "durationId": "3d8cc8bb-8e6a-4337-b053-869ae0233f90", "parent": "3eb511b6-c24e-404a-bb7e-8f7e26edec81"}}, {"head": {"id": "9dd42cb8-0c9a-42c0-81a7-f18696d35261", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228610262600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e4e6fb0-7052-4af3-9abe-23b2dce71c45", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228624085100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deb921df-f4fe-4898-8519-d28c70f5ce69", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228607251200, "endTime": 151228626112300}, "additional": {"logType": "info", "children": [], "durationId": "31afc63b-18fd-48c3-a36e-621940099fc7", "parent": "3eb511b6-c24e-404a-bb7e-8f7e26edec81"}}, {"head": {"id": "b351d1dc-7272-4bd0-9814-8158d2cbefb6", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228626176500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25afa821-d377-43a6-bab5-a27ce5f96dff", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228637091600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00264506-1d67-42de-a0fc-a41acbf8224f", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228637296600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9569995b-812e-4a4d-ac15-d6323ef6e5d5", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228637587900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5036088e-bf0e-4457-bd2a-52c3e0743275", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228644088700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb32c976-40fe-4e26-b070-a996572d9f24", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228644232800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a171033-0dba-4343-adf4-2cc191c83d11", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228626148100, "endTime": 151228649193600}, "additional": {"logType": "info", "children": [], "durationId": "d50d61da-31e6-4868-bd20-0ba58fcb6476", "parent": "3eb511b6-c24e-404a-bb7e-8f7e26edec81"}}, {"head": {"id": "4e3762c1-dc96-43ad-a1b7-46fd1ce6c2b0", "name": "Configuration phase cost:345 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228649246700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c664c3a-cbf2-473f-8193-1f785774093e", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228649217100, "endTime": 151228649359600}, "additional": {"logType": "info", "children": [], "durationId": "e855dd59-ef98-4082-80cd-61af31c88beb", "parent": "3eb511b6-c24e-404a-bb7e-8f7e26edec81"}}, {"head": {"id": "3eb511b6-c24e-404a-bb7e-8f7e26edec81", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228293569600, "endTime": 151228649373500}, "additional": {"logType": "info", "children": ["df686fed-3b3a-4377-9a0d-c7e23f0138b4", "77236da0-6bcc-44c0-a4e9-35a176f8a95c", "02a7be5b-a5c0-459a-b6ee-2063dd4d4485", "5f168d11-9b94-4fbb-92ed-1afa44bb8234", "b6c235a2-0fbc-46c1-aead-363af0492d72", "deb921df-f4fe-4898-8519-d28c70f5ce69", "8a171033-0dba-4343-adf4-2cc191c83d11", "6c664c3a-cbf2-473f-8193-1f785774093e", "5c77d74a-9641-43f4-9bb0-24c38abd3338"], "durationId": "5661447a-309c-47ea-bee3-a616ca06373b", "parent": "e8f75ccd-0440-422a-900b-dacf92dc9273"}}, {"head": {"id": "2785048a-9bc1-4ad6-a883-eaba6b60093f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228650674100, "endTime": 151228650693700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "876d3bf3-76e4-4090-91ee-f68fcc29246c", "logId": "b1bccba9-5cba-4555-9eac-9fca559cc90c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b1bccba9-5cba-4555-9eac-9fca559cc90c", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228650674100, "endTime": 151228650693700}, "additional": {"logType": "info", "children": [], "durationId": "2785048a-9bc1-4ad6-a883-eaba6b60093f", "parent": "e8f75ccd-0440-422a-900b-dacf92dc9273"}}, {"head": {"id": "b51285c5-3e62-4ea7-a87f-eb0f55ac6fdb", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228649400700, "endTime": 151228650709000}, "additional": {"logType": "info", "children": [], "durationId": "3b5a0b5e-f713-4aee-96d3-0b63aac5773a", "parent": "e8f75ccd-0440-422a-900b-dacf92dc9273"}}, {"head": {"id": "1b5fdc18-0d93-49c5-bed2-b803f1e89623", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228650714300, "endTime": 151228650736100}, "additional": {"logType": "info", "children": [], "durationId": "bdc582bc-ad19-4878-a78b-3f9824b77224", "parent": "e8f75ccd-0440-422a-900b-dacf92dc9273"}}, {"head": {"id": "e8f75ccd-0440-422a-900b-dacf92dc9273", "name": "init", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228258841500, "endTime": 151228650745900}, "additional": {"logType": "info", "children": ["9a0f6c58-7f54-4c81-838b-26a7ad41d17f", "3eb511b6-c24e-404a-bb7e-8f7e26edec81", "b51285c5-3e62-4ea7-a87f-eb0f55ac6fdb", "1b5fdc18-0d93-49c5-bed2-b803f1e89623", "912fd964-29b3-4119-8248-886f2e3d5d9f", "8273de50-a6c0-490d-9b53-062f2e784502", "b1bccba9-5cba-4555-9eac-9fca559cc90c"], "durationId": "876d3bf3-76e4-4090-91ee-f68fcc29246c"}}, {"head": {"id": "1bb349d5-99e4-4704-bf68-da07e59e4af5", "name": "Configuration task cost before running: 401 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228650924200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "337e4ee7-c2c7-4846-b8b7-5a6bb9a4b1f4", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228661826300, "endTime": 151228884791800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources\\base\\profile\\main_pages.json' has been changed."], "detailId": "e132a9c0-c422-40e8-af9d-e13ac2c47c0e", "logId": "82b39c45-9813-4a5d-89dd-a85c52e0fac0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e132a9c0-c422-40e8-af9d-e13ac2c47c0e", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228652801700}, "additional": {"logType": "detail", "children": [], "durationId": "337e4ee7-c2c7-4846-b8b7-5a6bb9a4b1f4"}}, {"head": {"id": "7ac3af48-341e-41df-a8e6-1ccece0befa2", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228653652900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ff47615-add1-4e5e-9f05-7b6137373caa", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228653878400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f13daa2-2f5b-41a3-ab85-72116630f2c6", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228655143000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4dedc5d-5cc5-4c19-9707-1ca91ded2511", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228656698600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5272979-2821-4c96-9632-a97ba82ea4ec", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228657764800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a82b97d-38dd-417e-911e-b5ce1561de6d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228657913900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b1b32e8-e350-4b52-a70a-6c67654efe36", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228661843300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "578663f6-cfe9-47f9-b911-fb42a8c6d741", "name": "entry:default@PreBuild is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources\\base\\profile\\main_pages.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228674681500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5738c876-7bd6-4a31-8d8a-6314e460fc0e", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228674954900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00e7274b-bff4-4c0d-ad6b-768bb9e677d7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228675271700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79fded8b-3c93-4997-bd4c-4cf8ca15d92e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228675358800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bfcee94-835c-4d98-9f7b-a2c6e9437ae7", "name": "current product is not Atomic service.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228882964000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8014c5b-b723-43d6-9580-93d52d45ea4b", "name": "Use tool [win32: JAVA_HOME, CLASSPATH]\n [\n  { JAVA_HOME: 'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\jbr' },\n  { CLASSPATH: undefined }\n]", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228883801100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3179ea6-142b-4b31-9949-064232bad5d8", "name": "Use tool [win32: NODE_HOME]\n [\n  {\n    NODE_HOME: 'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\node'\n  }\n]", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228883958000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "540e7e75-3293-4f33-9076-3f4f72ddc882", "name": "entry : default@PreBuild cost memory 6.726844787597656", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228884578200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aca41856-a060-4b17-9826-63fd7d66bdef", "name": "runTaskFromQueue task cost before running: 634 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228884702200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82b39c45-9813-4a5d-89dd-a85c52e0fac0", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228661826300, "endTime": 151228884791800, "totalTime": 222856500}, "additional": {"logType": "info", "children": [], "durationId": "337e4ee7-c2c7-4846-b8b7-5a6bb9a4b1f4"}}, {"head": {"id": "d11aa8ea-96e1-424b-9486-03ebe5e2dcbb", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228892138000, "endTime": 151228894107900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c5c2c76b-72e4-4be1-a07d-2e91d71960ec", "logId": "546d735c-12af-4f1a-9ad5-96cd6a78bc2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5c2c76b-72e4-4be1-a07d-2e91d71960ec", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228890061700}, "additional": {"logType": "detail", "children": [], "durationId": "d11aa8ea-96e1-424b-9486-03ebe5e2dcbb"}}, {"head": {"id": "9d7d7de1-f2a0-42f2-86f4-341f722679b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228891321800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32d9ba1c-79bd-4274-9626-c37214344ff7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228891421300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0f217d4-a206-403a-95b8-9d5876810733", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228892151300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "195f8c0a-b0fb-4622-96a2-60ca1cb412b2", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228892917700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a20c3d2b-6b14-4a65-85d2-12ac4e3155ff", "name": "entry : default@CreateModuleInfo cost memory 0.06292724609375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228893928900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dfb6da4-670e-4637-8f15-394e54e8d258", "name": "runTaskFromQueue task cost before running: 644 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228894046400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "546d735c-12af-4f1a-9ad5-96cd6a78bc2f", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228892138000, "endTime": 151228894107900, "totalTime": 1890700}, "additional": {"logType": "info", "children": [], "durationId": "d11aa8ea-96e1-424b-9486-03ebe5e2dcbb"}}, {"head": {"id": "e63be3e4-9cdf-433f-b67f-ef254ffc0990", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228902814500, "endTime": 151228906477500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "27a43762-8ed5-42ea-86dc-3b42eeb9a28d", "logId": "08ed7d3a-0ca3-458f-abb9-c9ccaa987722"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27a43762-8ed5-42ea-86dc-3b42eeb9a28d", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228896405300}, "additional": {"logType": "detail", "children": [], "durationId": "e63be3e4-9cdf-433f-b67f-ef254ffc0990"}}, {"head": {"id": "92e8c6c0-56a1-43d5-899b-30d8a082decc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228897550300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "378c50c4-31d1-4efa-8303-5b481a263449", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228897672500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43dc49c3-731c-4f9b-99dd-f0045187d5d6", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228902835000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae4a4541-e8aa-4dfc-ad50-9a930a9e5ab9", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228904707500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54f9cd3a-053c-49de-91db-ddad2e495302", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228906255200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6565b95-2d53-430b-a8c3-2af7517ddc7b", "name": "entry : default@GenerateMetadata cost memory 0.23830413818359375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228906399300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08ed7d3a-0ca3-458f-abb9-c9ccaa987722", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228902814500, "endTime": 151228906477500}, "additional": {"logType": "info", "children": [], "durationId": "e63be3e4-9cdf-433f-b67f-ef254ffc0990"}}, {"head": {"id": "a0488aa5-c081-40f9-8ad6-eb872172022e", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228909717000, "endTime": 151228910086900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "314e64ed-e211-4c2f-bf09-917eecdd506a", "logId": "7d7ab089-d621-4dbd-95b8-62763e57e14c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "314e64ed-e211-4c2f-bf09-917eecdd506a", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228908258800}, "additional": {"logType": "detail", "children": [], "durationId": "a0488aa5-c081-40f9-8ad6-eb872172022e"}}, {"head": {"id": "ee1176ae-6e9b-4a0a-8256-8e075371c61d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228909263200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d15d8191-f1d2-4a86-a5e3-0933f844f331", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228909385100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cac8633a-f122-4229-bf69-6616f1ebf2b0", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228909727800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86a46cdd-76fd-4d9d-a85c-e1513c0eb267", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228909854600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c563e61-ff10-4529-8fcf-d76516f240ef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228909906400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13108a81-d4a3-4373-a5ed-327f3411ff18", "name": "entry : default@ConfigureCmake cost memory 0.045196533203125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228909989000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3758dde6-23e9-4ec2-bb11-d0b381171deb", "name": "runTaskFromQueue task cost before running: 660 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228910051900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d7ab089-d621-4dbd-95b8-62763e57e14c", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228909717000, "endTime": 151228910086900, "totalTime": 318000}, "additional": {"logType": "info", "children": [], "durationId": "a0488aa5-c081-40f9-8ad6-eb872172022e"}}, {"head": {"id": "e0f2eafa-ca50-4e10-9047-d00534901645", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228914113400, "endTime": 151228917627400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1822903d-0f0b-4aec-ab35-2ba2f97652b3", "logId": "70b9d52e-7fdb-4ccd-aacc-1ab4791547ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1822903d-0f0b-4aec-ab35-2ba2f97652b3", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228911848700}, "additional": {"logType": "detail", "children": [], "durationId": "e0f2eafa-ca50-4e10-9047-d00534901645"}}, {"head": {"id": "85c33011-a6fe-435f-bc01-9effbed4a76e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228913025200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d35911ed-ebcf-41bb-ad2e-9e1fb04aa1d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228913146900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dd94d3b-c373-4d9d-bbde-45fd786d3bdf", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228914129600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b876f070-2eca-4198-9edc-5398f349ba1b", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228917368400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "911dee5a-eddb-4ba2-92a3-fed6786c28f4", "name": "entry : default@MergeProfile cost memory 0.13033294677734375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228917534500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70b9d52e-7fdb-4ccd-aacc-1ab4791547ae", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228914113400, "endTime": 151228917627400}, "additional": {"logType": "info", "children": [], "durationId": "e0f2eafa-ca50-4e10-9047-d00534901645"}}, {"head": {"id": "6943a9bd-1a33-4b6e-a15c-582d6b472b00", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228922162300, "endTime": 151228925131900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7494e865-c9ab-4c00-bd30-7db89c670af0", "logId": "5ef40134-ebaa-40df-a335-8509612f683f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7494e865-c9ab-4c00-bd30-7db89c670af0", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228919807700}, "additional": {"logType": "detail", "children": [], "durationId": "6943a9bd-1a33-4b6e-a15c-582d6b472b00"}}, {"head": {"id": "de01b62f-05ff-4dd3-a38a-0bcc5cb28958", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228921046900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d6baab2-a0f3-43e4-baee-e23a9498a305", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228921169300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c624cf56-0bc3-46be-b293-24c2fea659a4", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228922173000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fff08ec-59c0-42c1-8483-3708b788800c", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228923261100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "646c537b-fea7-4ec6-bb2f-776f65dcb241", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228924882700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aecb93b7-12aa-4a66-acc1-e15361ccda4d", "name": "entry : default@CreateBuildProfile cost memory 0.1137847900390625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228925024900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ef40134-ebaa-40df-a335-8509612f683f", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228922162300, "endTime": 151228925131900}, "additional": {"logType": "info", "children": [], "durationId": "6943a9bd-1a33-4b6e-a15c-582d6b472b00"}}, {"head": {"id": "819f090d-c5b9-4511-8314-368dc39a0a2b", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228928889900, "endTime": 151228929586000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "8d1d25ca-29bd-4506-bd4b-96a345d632e8", "logId": "55a22077-8334-4f84-945e-ac53cd882752"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d1d25ca-29bd-4506-bd4b-96a345d632e8", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228927051300}, "additional": {"logType": "detail", "children": [], "durationId": "819f090d-c5b9-4511-8314-368dc39a0a2b"}}, {"head": {"id": "fe5fb46e-02f0-47f0-b3fd-2391aab27e69", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228928055000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df8802ae-f160-4318-8f8b-0fa482b3cfe8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228928161300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "863bcf1e-6f27-42dd-9a83-09b06bea06c3", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228928900700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b21935cf-2022-400d-b210-3252d1336c3b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228929033100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e578394c-1544-4a7b-8599-894e41d2256e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228929080700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25b190eb-661e-4ceb-b2ab-e26e00ac0bb2", "name": "entry : default@PreCheckSyscap cost memory 0.0418548583984375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228929401400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a22c0e01-8475-43b6-8afb-49fbbcaaa211", "name": "runTaskFromQueue task cost before running: 679 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228929530000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55a22077-8334-4f84-945e-ac53cd882752", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228928889900, "endTime": 151228929586000, "totalTime": 617000}, "additional": {"logType": "info", "children": [], "durationId": "819f090d-c5b9-4511-8314-368dc39a0a2b"}}, {"head": {"id": "1a730b9f-c56d-4dd8-ad5f-f8af94d5680d", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228936279100, "endTime": 151228945872900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2af3600c-2df0-4d90-9060-568886da7c63", "logId": "25d218fa-218e-41c4-af15-867100d2c689"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2af3600c-2df0-4d90-9060-568886da7c63", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228931795100}, "additional": {"logType": "detail", "children": [], "durationId": "1a730b9f-c56d-4dd8-ad5f-f8af94d5680d"}}, {"head": {"id": "355dd2ef-09ac-42d9-a4aa-c63f3442241e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228932808500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb05d21e-a3b8-4cd9-8dd0-92a6e3162797", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228932924300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a31e2fc0-cedc-4235-be15-6dd580eca126", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228936295400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03245bbf-3c8b-4921-b4a0-735f0996a21b", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228944689300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af742d54-8bea-4140-b4bf-b098c0190aef", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228945647000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82a776fe-7f11-4ac7-8118-7f6de8e74e4d", "name": "entry : default@GeneratePkgContextInfo cost memory 0.3183135986328125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228945797300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25d218fa-218e-41c4-af15-867100d2c689", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228936279100, "endTime": 151228945872900}, "additional": {"logType": "info", "children": [], "durationId": "1a730b9f-c56d-4dd8-ad5f-f8af94d5680d"}}, {"head": {"id": "4c1c3a42-9101-4c34-b465-63dcc4c5cc29", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228954030600, "endTime": 151228956250600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "868e49f7-e99c-4426-9658-84b9c0eada25", "logId": "1b2cbbaa-1056-4177-94fb-37ba441bff75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "868e49f7-e99c-4426-9658-84b9c0eada25", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228948562300}, "additional": {"logType": "detail", "children": [], "durationId": "4c1c3a42-9101-4c34-b465-63dcc4c5cc29"}}, {"head": {"id": "1601a1b7-c08f-4108-8748-22d59c864f33", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228949541400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac659964-f45f-40ef-a6c1-8e2c3e6d99ed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228949653600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdc7fc12-5b30-4fa3-b494-5e78780453d4", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228954047600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a7dbfe1-aa4c-462d-ae53-040d58b13dce", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228955840500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9b91f13-416e-4f00-acc7-50e0f0d3bed3", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228955975900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3674b6c4-c6cb-4d4e-8a9f-888683877a6b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228956057500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32bf83f5-7df7-469f-a175-1ba26a1b1110", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228956094900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6db13e8c-967b-475e-980f-6b707c3e17bc", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12483978271484375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228956157100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c43e4e26-b5ef-4b57-ba30-4b2f0e0b42d3", "name": "runTaskFromQueue task cost before running: 706 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228956212300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b2cbbaa-1056-4177-94fb-37ba441bff75", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228954030600, "endTime": 151228956250600, "totalTime": 2172000}, "additional": {"logType": "info", "children": [], "durationId": "4c1c3a42-9101-4c34-b465-63dcc4c5cc29"}}, {"head": {"id": "904b4c2c-a202-4617-9cc7-8f40fb7b511c", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228960590500, "endTime": 151228960976000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "10219b02-f830-41af-9975-189a6e941c03", "logId": "e209e263-dc40-4e2c-802e-8c88e3d11b88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10219b02-f830-41af-9975-189a6e941c03", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228958655700}, "additional": {"logType": "detail", "children": [], "durationId": "904b4c2c-a202-4617-9cc7-8f40fb7b511c"}}, {"head": {"id": "1dd31e9a-3171-4d9f-814a-081811ca55e3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228959676100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea169245-a5aa-4f6d-8259-9554fc608ccb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228959774900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0bb15ce-277c-4471-bdea-6c8a508dad00", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228960600200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "600c4f70-6ade-4e71-a2d4-befa11667820", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228960737700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f569852-ddb0-4a05-8782-9552b72d69ad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228960787400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f46c072-6a40-4165-ab3a-faa783ac48bc", "name": "entry : default@BuildNativeWithCmake cost memory 0.06121826171875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228960874300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80ef06ca-ffe5-47de-8819-a59a832384c8", "name": "runTaskFromQueue task cost before running: 711 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228960941300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e209e263-dc40-4e2c-802e-8c88e3d11b88", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228960590500, "endTime": 151228960976000, "totalTime": 334800}, "additional": {"logType": "info", "children": [], "durationId": "904b4c2c-a202-4617-9cc7-8f40fb7b511c"}}, {"head": {"id": "f50391f7-f5f4-496f-9c85-678e194ef9e5", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228964348900, "endTime": 151228968210400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3380e166-865d-4d21-ae0f-31e6c55daafa", "logId": "c0c4cecc-29da-46cb-89d2-0f268dcdb7de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3380e166-865d-4d21-ae0f-31e6c55daafa", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228962447500}, "additional": {"logType": "detail", "children": [], "durationId": "f50391f7-f5f4-496f-9c85-678e194ef9e5"}}, {"head": {"id": "c067a36f-3713-46f3-9571-714311ad720c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228963464900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13596f23-8d84-4682-9516-6d24d8293821", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228963562500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0f65abd-8a51-4286-a048-6d9b030a3f8d", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228964359200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb89dd9d-04ed-4fc8-9aec-b310eaa0e1b8", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228968031600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7f0bc78-ae58-4a52-b677-d1ed18f685be", "name": "entry : default@MakePackInfo cost memory 0.17285919189453125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228968147900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0c4cecc-29da-46cb-89d2-0f268dcdb7de", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228964348900, "endTime": 151228968210400}, "additional": {"logType": "info", "children": [], "durationId": "f50391f7-f5f4-496f-9c85-678e194ef9e5"}}, {"head": {"id": "f5a6a4c5-ed60-46a6-9809-9fc84157bb9c", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228972657600, "endTime": 151228976265000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1c3351c7-22ea-49a1-b0c0-835c95e25f5c", "logId": "c39fab0c-51b4-4933-a342-40bc61064882"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c3351c7-22ea-49a1-b0c0-835c95e25f5c", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228970354800}, "additional": {"logType": "detail", "children": [], "durationId": "f5a6a4c5-ed60-46a6-9809-9fc84157bb9c"}}, {"head": {"id": "09fea47a-0556-4461-9305-4cadb970798c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228971408100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01135a7e-3bf4-4fd1-b274-a706df1e7388", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228971497200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05e05bc4-89aa-4a7f-9a41-e9850d007b18", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228972667400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a99c542-47c4-4ceb-9a2a-c33d1abf35f2", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228972849400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfedc7b1-867c-418e-bb79-702f3bf21ce4", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228973814600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa41cd42-00ba-4a1a-a243-f4e85319f8b7", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228976099000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dc9dbdd-1cd3-4cef-839f-2c5601bd4b70", "name": "entry : default@SyscapTransform cost memory 0.151458740234375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228976203800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c39fab0c-51b4-4933-a342-40bc61064882", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228972657600, "endTime": 151228976265000}, "additional": {"logType": "info", "children": [], "durationId": "f5a6a4c5-ed60-46a6-9809-9fc84157bb9c"}}, {"head": {"id": "94a770cb-27b0-48f7-9cc0-ef251843e33c", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228980907200, "endTime": 151228983236400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "62068349-e44c-4274-a78a-2b61c1674258", "logId": "3fcc202e-0c84-44af-bb82-954dc292b044"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62068349-e44c-4274-a78a-2b61c1674258", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228978094600}, "additional": {"logType": "detail", "children": [], "durationId": "94a770cb-27b0-48f7-9cc0-ef251843e33c"}}, {"head": {"id": "f26ed2fd-6a23-40b8-b9f9-8d0da617a60a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228979232000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b05e12-0881-4758-b18a-291e12d4a6e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228979334300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b055878-0f61-4b5c-8ef5-c4aa05aae0bf", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228980920600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7051be91-242c-4da4-8463-fbb30491818f", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228982921600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5a576b0-4c2f-452a-8424-e7863334bbaa", "name": "entry : default@ProcessProfile cost memory 0.13080596923828125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228983159900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fcc202e-0c84-44af-bb82-954dc292b044", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228980907200, "endTime": 151228983236400}, "additional": {"logType": "info", "children": [], "durationId": "94a770cb-27b0-48f7-9cc0-ef251843e33c"}}, {"head": {"id": "476e8698-80f7-468b-b933-53d14a6788c7", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228987874200, "endTime": 151228995915700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8316aa63-2236-4011-b1c9-5b3180821f07", "logId": "fabf5d86-2e09-4678-aa08-1f171644221a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8316aa63-2236-4011-b1c9-5b3180821f07", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228984999100}, "additional": {"logType": "detail", "children": [], "durationId": "476e8698-80f7-468b-b933-53d14a6788c7"}}, {"head": {"id": "afc31314-3ed5-40a9-ad56-66cc6a8dd4f6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228985955400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4448752-3979-40ea-8d1f-02bd9d38a80b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228986059500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1506a83-5692-40a7-826f-979292db6b1b", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228987894600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6314debf-fcbc-475f-9be9-a2e7e3f6b824", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228995612700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc4a04f7-b10e-484d-8112-48204aca8e60", "name": "entry : default@ProcessRouterMap cost memory 0.2374267578125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228995823600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fabf5d86-2e09-4678-aa08-1f171644221a", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228987874200, "endTime": 151228995915700}, "additional": {"logType": "info", "children": [], "durationId": "476e8698-80f7-468b-b933-53d14a6788c7"}}, {"head": {"id": "aea323c9-d183-4f23-8cd9-47de41e8d863", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229001429100, "endTime": 151229007207700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "952fadb1-a4f5-4a7e-8d54-708b9217801d", "logId": "a5a2608c-68e3-4d35-bbf4-cc6f178946b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "952fadb1-a4f5-4a7e-8d54-708b9217801d", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228999994300}, "additional": {"logType": "detail", "children": [], "durationId": "aea323c9-d183-4f23-8cd9-47de41e8d863"}}, {"head": {"id": "d96ca8cc-a2ae-45e9-bdec-073447ecf33e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229001221200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e656b64-54f1-4153-913f-0bcb820e5b8a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229001337400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50d04123-341d-45e8-b64d-abbe99b9d53d", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229001437500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd417bb3-fc31-4113-9522-06f553592d2a", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229001576400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1ea6d0b-1fb4-48a4-8888-3da1c9342aa7", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229005597400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ed75934-8d1d-4a6b-99c9-845ddbe3f9de", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229005735700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "174962b7-358c-4f0d-b9cd-8c713595b185", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229005813300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c90a1870-3591-41a2-a0d0-f09e82e1b38a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229005850900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b60272a2-1f70-496b-9c14-c9250bfa06ca", "name": "entry : default@ProcessStartupConfig cost memory 0.26270294189453125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229007024300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71a8cf41-5215-49cc-a6ea-0b10622b5f3e", "name": "runTaskFromQueue task cost before running: 757 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229007153500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5a2608c-68e3-4d35-bbf4-cc6f178946b4", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229001429100, "endTime": 151229007207700, "totalTime": 5699400}, "additional": {"logType": "info", "children": [], "durationId": "aea323c9-d183-4f23-8cd9-47de41e8d863"}}, {"head": {"id": "9984072e-f79d-4419-9b5d-e960e092a160", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229012336500, "endTime": 151229014083400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a58b7fd5-87cb-4680-adb5-523de4cef16f", "logId": "64aad41f-da8c-4cfa-ad8e-36ca649eb8e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a58b7fd5-87cb-4680-adb5-523de4cef16f", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229010424500}, "additional": {"logType": "detail", "children": [], "durationId": "9984072e-f79d-4419-9b5d-e960e092a160"}}, {"head": {"id": "fb221a3a-4d84-406e-89d0-5bae3403e46e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229011435700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b00172d5-437f-40fa-b110-62998005fc8d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229011541100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f41a33ed-5efc-4eb9-b46f-7005692d6369", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229012349300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cffbcf51-cc3d-4e91-ad42-c9fc8252b85d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229012474900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52af6d68-2bd5-4d34-83cb-2c33bd697c25", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229012520300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "281db086-5bee-41e0-83be-709afc2ca650", "name": "entry : default@BuildNativeWithNinja cost memory 0.06334686279296875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229013681100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b98f1779-9a96-4d74-9a6e-caee554816e2", "name": "runTaskFromQueue task cost before running: 764 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229013939100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64aad41f-da8c-4cfa-ad8e-36ca649eb8e2", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229012336500, "endTime": 151229014083400, "totalTime": 1554500}, "additional": {"logType": "info", "children": [], "durationId": "9984072e-f79d-4419-9b5d-e960e092a160"}}, {"head": {"id": "f2dc0b1e-c0b3-4d0e-b5b3-edc04b54c222", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229029893500, "endTime": 151229036434700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "7cf941d1-62b0-464f-8e49-f2496e01b1a4", "logId": "af09d8f0-19db-4e9a-945e-ec204359173d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cf941d1-62b0-464f-8e49-f2496e01b1a4", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229017177500}, "additional": {"logType": "detail", "children": [], "durationId": "f2dc0b1e-c0b3-4d0e-b5b3-edc04b54c222"}}, {"head": {"id": "97fa9321-7a99-40f3-bea0-f750ff9e57d6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229026423400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c4a5937-b955-4bf9-b4e7-f0f0847ce829", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229026587900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f4649c5-abac-4adb-b94b-715076960361", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229028358500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0708c80c-27cc-4cfd-ac0d-b42ea3b89c1a", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229031997200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37f5cb47-dfbe-4edb-81d1-41537f65b122", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229034582900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "504ea573-30dd-437f-b9a6-41c5885870f9", "name": "entry : default@ProcessResource cost memory 0.17832183837890625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229034736000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af09d8f0-19db-4e9a-945e-ec204359173d", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229029893500, "endTime": 151229036434700}, "additional": {"logType": "info", "children": [], "durationId": "f2dc0b1e-c0b3-4d0e-b5b3-edc04b54c222"}}, {"head": {"id": "537abcd9-487a-442b-9c8b-339cee8c90af", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229044954300, "endTime": 151229063977700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1d6c37ac-9025-4116-9d00-62f2b3ff4382", "logId": "d2ad94b3-3aa1-4d9e-8380-c2262b0e3f98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d6c37ac-9025-4116-9d00-62f2b3ff4382", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229040132800}, "additional": {"logType": "detail", "children": [], "durationId": "537abcd9-487a-442b-9c8b-339cee8c90af"}}, {"head": {"id": "a1d86619-5741-42d7-889a-067b89be6ad1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229041268600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43ed26d7-9a23-445d-9ed3-85c446e24253", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229041370800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46919465-742c-4c46-91e0-3f94da9cab98", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229044965600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41e275e9-7043-4fc9-aa25-d0b3cb79a4dd", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229063761000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "195f293b-c407-4554-95fd-db2531048af6", "name": "entry : default@GenerateLoaderJson cost memory 0.9237518310546875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229063913900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2ad94b3-3aa1-4d9e-8380-c2262b0e3f98", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229044954300, "endTime": 151229063977700}, "additional": {"logType": "info", "children": [], "durationId": "537abcd9-487a-442b-9c8b-339cee8c90af"}}, {"head": {"id": "8d856922-e97e-4037-9cdd-07e1634144eb", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229072451300, "endTime": 151229076518400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "a0f20a7c-2929-44a8-a551-0dd06ba343b9", "logId": "8f6f5dab-38e3-48c7-916d-54948758ad08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0f20a7c-2929-44a8-a551-0dd06ba343b9", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229070688700}, "additional": {"logType": "detail", "children": [], "durationId": "8d856922-e97e-4037-9cdd-07e1634144eb"}}, {"head": {"id": "075766cc-802f-4456-8b7d-2995fd57aac9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229071564000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08d38ecf-bb37-452a-8ef0-b5c3d5d200a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229071652900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5602e3d8-efbb-489b-93bb-3b2b6b2f7f88", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229072461600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6486fa05-760d-4235-a6d3-d4d5c1517bdd", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229076367300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7654a7e3-499d-4574-a813-79dd972ab3b0", "name": "entry : default@ProcessLibs cost memory 0.1542205810546875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229076467000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f6f5dab-38e3-48c7-916d-54948758ad08", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229072451300, "endTime": 151229076518400}, "additional": {"logType": "info", "children": [], "durationId": "8d856922-e97e-4037-9cdd-07e1634144eb"}}, {"head": {"id": "d5d393d6-7cad-4b0e-b808-8aa7c69c41a6", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229082318300, "endTime": 151229320385100}, "additional": {"children": ["ae33fdc1-0ed7-4a6b-9163-c9aa07192378", "a5943a6a-6a2a-43f5-bfb6-1e16926ec223"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources' has been changed."], "detailId": "5f0dc9bf-39f4-4ba5-bd45-086704024ee6", "logId": "025be9df-1bb6-4406-88fe-9ffdff5f00da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f0dc9bf-39f4-4ba5-bd45-086704024ee6", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229078312300}, "additional": {"logType": "detail", "children": [], "durationId": "d5d393d6-7cad-4b0e-b808-8aa7c69c41a6"}}, {"head": {"id": "505be91a-0915-4050-ad88-5c0637f5e0de", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229079150800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "692dab75-6172-4f2c-8743-2fca4d00ed87", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229079235400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8664297d-1123-4414-b677-f709b983d346", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229080039800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c89e758-3816-405e-974a-bd078eb0ade7", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229082430500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbdb0672-d4a7-4ff0-bdb2-344519bd9830", "name": "entry:default@CompileResource is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229094888900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfb9cf72-4fde-470a-80fd-e2e902b261b7", "name": "Incremental task entry:default@CompileResource pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229095088700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae33fdc1-0ed7-4a6b-9163-c9aa07192378", "name": "create intermediate resource category", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229096281900, "endTime": 151229164084400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5d393d6-7cad-4b0e-b808-8aa7c69c41a6", "logId": "44f43499-b048-4dc2-ae82-151b1cb5a60e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44f43499-b048-4dc2-ae82-151b1cb5a60e", "name": "create intermediate resource category", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229096281900, "endTime": 151229164084400}, "additional": {"logType": "info", "children": [], "durationId": "ae33fdc1-0ed7-4a6b-9163-c9aa07192378", "parent": "025be9df-1bb6-4406-88fe-9ffdff5f00da"}}, {"head": {"id": "a58070fa-5907-4ade-8dea-1ad16a576997", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\restool.exe]\n [\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\restool.exe',\n  '-l',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resConfig.json'\n]", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229165011400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5943a6a-6a2a-43f5-bfb6-1e16926ec223", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229166851600, "endTime": 151229317969500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5d393d6-7cad-4b0e-b808-8aa7c69c41a6", "logId": "c5cc3104-4980-4349-a94a-b8d5268a1752"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c725a64b-842f-495f-86be-926c873a4c5d", "name": "current process  memoryUsage: {\n  rss: 177524736,\n  heapTotal: 166076416,\n  heapUsed: 127198344,\n  external: 3348181,\n  arrayBuffers: 135698\n} os memoryUsage :10.678764343261719", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229169537900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88587cf5-f1de-4b14-96ef-2be755e1af05", "name": "Info: Pack: normal pack mode\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229215589400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d52dcd03-b1d7-4fd3-9fa5-09cfdac1161c", "name": "Warning: 'extensionPath' value cannot be empty.\r\nat C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\opt-compression.json\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229215919600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f316cc15-5699-4fdd-b98d-85bd1375e30f", "name": "Info: hardware concurrency count is : 20\r\nInfo: thread count is : 20\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229273154300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1b91327-3e1b-4220-87d5-d0548d2bd22e", "name": "Info: thread pool is started\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229273481700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04751e94-3780-4f92-9574-f2d9870c0d32", "name": "Info: GenericCompiler::CompileFiles\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229276076700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9105327e-3939-46d1-8040-0ac1e04d301b", "name": "Info: GenericCompiler::CompileFiles\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229280669600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e84424e1-4fb2-4b80-b75a-86471a1aea1c", "name": "Info: GenericCompiler::CompileFiles\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229287789000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "508ed6e4-75ee-45da-871c-09b22d58256a", "name": "Warning: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources\\base\\media\\background.png' is defined repeatedly.\r\nWarning: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources\\base\\media\\foreground.png' is defined repeatedly.\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229288116300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51519bd5-46d7-4353-8ae5-db373999f8d3", "name": "Warning: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources\\base\\media\\layered_image.json' is defined repeatedly.\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229299227200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7da52dd-3496-4c63-a28f-012328ff5eb1", "name": "Info: GenericCompiler::CompileFiles\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229307689000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d03cb90-3a60-4206-8a2b-3c0c21d1cae3", "name": "Info: scale icon is not enable.\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229312580900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44760272-eb22-483f-9999-0b12ed505a1d", "name": "Warning: The width or height of the png file referenced by the icon exceeds the limit (41 pixels)\r\nat C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\resources\\base\\media\\background.png\r\nWarning: The width or height of the png file referenced by the icon exceeds the limit (41 pixels)\r\nat C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\resources\\base\\media\\foreground.png\r\nWarning: C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\resources\\base\\media\\layered_image.json is not png format\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229312880600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b1a4d41-0c8a-40b7-9479-d85d39f94e0d", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229313836800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e19f0bde-a928-4b95-b039-00d384429d3b", "name": "Info: thread pool is stopped\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229314458300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5cc3104-4980-4349-a94a-b8d5268a1752", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229166851600, "endTime": 151229317969500}, "additional": {"logType": "info", "children": [], "durationId": "a5943a6a-6a2a-43f5-bfb6-1e16926ec223", "parent": "025be9df-1bb6-4406-88fe-9ffdff5f00da"}}, {"head": {"id": "1f4cfb2f-b5aa-435c-a447-9943e3f97fa8", "name": "entry : default@CompileResource cost memory -4.0235595703125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229320137800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2878d520-b9bc-486a-bb27-63816f230de7", "name": "runTaskFromQueue task cost before running: 1 s 70 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229320317000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "025be9df-1bb6-4406-88fe-9ffdff5f00da", "name": "Finished :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229082318300, "endTime": 151229320385100, "totalTime": 237946700}, "additional": {"logType": "info", "children": ["44f43499-b048-4dc2-ae82-151b1cb5a60e", "c5cc3104-4980-4349-a94a-b8d5268a1752"], "durationId": "d5d393d6-7cad-4b0e-b808-8aa7c69c41a6"}}, {"head": {"id": "bb2ea83c-1ac9-4f79-a638-df0eead798f2", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229326460500, "endTime": 151229328581800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f6e1d034-659f-44cf-8e4f-d9ed2ec9e786", "logId": "f2c72429-2d71-4634-bbd3-42d42b4f8559"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6e1d034-659f-44cf-8e4f-d9ed2ec9e786", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229323258200}, "additional": {"logType": "detail", "children": [], "durationId": "bb2ea83c-1ac9-4f79-a638-df0eead798f2"}}, {"head": {"id": "9c21c4dd-01b6-4a25-bbbe-df7565e57f22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229324123600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e84860c-1a44-443d-bb48-e744735a7ec5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229324210600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9fbf396-2de1-4068-957f-d70d8501e864", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229326475000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ec18f54-8fcd-465f-ace5-4cb4a3e52707", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229327023500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e75382ba-7999-4ad6-8d29-40ea7fc9dbdf", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229328371100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a68ac1af-cbbc-4658-bde3-e48e597c256c", "name": "entry : default@DoNativeStrip cost memory 0.08191680908203125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229328470700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2c72429-2d71-4634-bbd3-42d42b4f8559", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229326460500, "endTime": 151229328581800}, "additional": {"logType": "info", "children": [], "durationId": "bb2ea83c-1ac9-4f79-a638-df0eead798f2"}}, {"head": {"id": "321aafe1-e114-43f3-88b2-9f2aed80f0a9", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229335170100, "endTime": 151240372760600}, "additional": {"children": ["6132f703-fa33-4662-b54a-8cc8c1be7aea", "0e4019c9-8144-4ad4-af52-66c27be47008"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "7b2b1793-5765-4e53-af1d-8bdf4b36a271", "logId": "44cbe3c7-547e-4cc1-8078-acc23a7d4a42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b2b1793-5765-4e53-af1d-8bdf4b36a271", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229330303500}, "additional": {"logType": "detail", "children": [], "durationId": "321aafe1-e114-43f3-88b2-9f2aed80f0a9"}}, {"head": {"id": "d57c6bab-1971-43c8-828f-1c768992f63a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229331309600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c6dd941-26d2-4e60-b2e2-2bf9afa3cee8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229331416500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5ef3af6-ed18-4806-9f24-2113d5cfedc0", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229335184700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8878c46-c414-4042-a8b6-5bacb5a43b79", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229335368800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05e8d1a4-b090-4130-9661-32c758b8b443", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229348888300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0883076d-3149-4c5f-bf13-938b1bbfd1da", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229349059600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b45ec1c-6aa9-4c14-8f7a-0074dadcee45", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229366774000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64aea54f-8cb2-407b-b3c4-7c83f20c79d3", "name": "default@CompileArkTS work[2] is submitted.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229368873600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6132f703-fa33-4662-b54a-8cc8c1be7aea", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 151229371681800, "endTime": 151240372457900}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "321aafe1-e114-43f3-88b2-9f2aed80f0a9", "logId": "32d24414-167c-4444-bc0d-6fe4ce3f5eac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d58ef91a-d337-43f5-867f-b955ce5e0854", "name": "default@CompileArkTS work[2] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370033300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "992e3baf-63b9-479b-b8aa-47804b4b7513", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370133100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6119d29d-ec05-401e-a3fa-8a1bb1d2182e", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370246200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22f50744-cd1f-4754-bd08-f5b2e5c3dd0b", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370283100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caf0a361-14b3-4f1c-a25a-795a24a70b5b", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370310000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1ef5809-d02e-49d8-a1ab-1ce9c6894717", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370334700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6788427b-7c96-49ae-a1f3-457d721698a3", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370358100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e417dbd9-8017-4adb-9b76-f937cc8d7953", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370386600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b3bd500-e47f-4f3d-9d85-c1f3f33c6f38", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370414800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d592cd0-7048-4578-9644-cac66bf19a6e", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370440200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97c0b8f5-3538-4842-90cc-ea0aa07d22f8", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370468700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25e68438-2922-42f5-b407-cf1de899b69f", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370492300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b55c230-ffaf-48db-b0ff-31bed711bbda", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370523900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9e6ec06-ccbb-4fdc-a021-dec8ac029303", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370548300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e1608d1-fc6b-43dd-8045-b07fcf83aa31", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370573400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c490f80-f012-4274-bca2-e7a3a0db0d9b", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370596700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da1714cb-f6c8-4286-8e4f-7c172cbb3498", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229370678700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbcb9c19-8be5-45c3-982a-beba44615411", "name": "default@CompileArkTS work[2] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229371710000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64981279-d0e1-4630-8b6a-d1105545dbe6", "name": "default@CompileArkTS work[2] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229371974600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "417c87ee-c9ed-4cca-b05c-7c69d081b7f8", "name": "CopyResources startTime: 151229372051300", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229372054900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77b7a3e4-120f-409c-9c51-91ea72985cf6", "name": "default@CompileArkTS work[3] is submitted.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229372128000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e4019c9-8144-4ad4-af52-66c27be47008", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker18", "startTime": 151230489265900, "endTime": 151230503660300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "321aafe1-e114-43f3-88b2-9f2aed80f0a9", "logId": "cfa0d2e8-689d-41d6-a5ae-a839c2e6d21a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ce9a981-3ae7-4906-b54b-f51f218ce0f3", "name": "default@CompileArkTS work[3] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229372947200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38d541f4-d066-4961-83c4-712f53600164", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229373034000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73f2b926-b198-4d55-a4a2-412468a0d2a1", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229373085300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2e90792-b5c1-40e6-9e18-4c4a3cc17b28", "name": "default@CompileArkTS work[3] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229373754100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa59da07-6fea-4c07-b8e0-3f3a8b4be38b", "name": "default@CompileArkTS work[3] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229373826000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d575359f-a3ea-4b5d-ad69-a0d146cb5ac1", "name": "entry : default@CompileArkTS cost memory 2.1509857177734375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229373952300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b632c30-3956-404f-895e-e4f4e160d322", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229381323800, "endTime": 151229389655100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "d0f5758f-c490-4158-b46f-81ad79d7fa14", "logId": "e8985fb6-f8d5-410e-9f20-dfdbdd139d56"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0f5758f-c490-4158-b46f-81ad79d7fa14", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229375603900}, "additional": {"logType": "detail", "children": [], "durationId": "1b632c30-3956-404f-895e-e4f4e160d322"}}, {"head": {"id": "2d7b077b-4ca2-4b35-b2bd-93a26fb64998", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229376545800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36230021-33fe-4e79-ba69-00196f5f1c10", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229376638900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05bfe1fb-73a6-4e0a-8437-ce0c8b9bb53d", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229381340300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3794c181-4490-4852-beb0-b91a58ee968d", "name": "entry : default@BuildJS cost memory 0.36209869384765625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229389419500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5397e55-05f4-4563-a91b-1dbae5acd332", "name": "runTaskFromQueue task cost before running: 1 s 139 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229389570100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8985fb6-f8d5-410e-9f20-dfdbdd139d56", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229381323800, "endTime": 151229389655100, "totalTime": 8217500}, "additional": {"logType": "info", "children": [], "durationId": "1b632c30-3956-404f-895e-e4f4e160d322"}}, {"head": {"id": "4f8fc943-8ffb-40b7-9041-82b700fc4bff", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229397494600, "endTime": 151229401885500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c32e7c8b-2c15-463e-9095-413f4f8fb806", "logId": "80b560df-0d6c-4597-a8f8-b986cffc337f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c32e7c8b-2c15-463e-9095-413f4f8fb806", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229391731500}, "additional": {"logType": "detail", "children": [], "durationId": "4f8fc943-8ffb-40b7-9041-82b700fc4bff"}}, {"head": {"id": "5f72e953-b53a-46b9-ba30-d2ab470bb5c7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229393135100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c271eb1e-ea77-49fc-a72d-40979aeec038", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229393253000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "793a99ff-3144-41fc-bd6f-530c4b569a2c", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229397509300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4d12f33-dc46-45b8-b28c-ce39e6651525", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229398671300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32ffc0d2-1ea5-4761-a2b4-1ca2f8316636", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229401638400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01a33e93-85c7-426f-87dc-eccba125c32c", "name": "entry : default@CacheNativeLibs cost memory 0.09795379638671875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229401798800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80b560df-0d6c-4597-a8f8-b986cffc337f", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229397494600, "endTime": 151229401885500}, "additional": {"logType": "info", "children": [], "durationId": "4f8fc943-8ffb-40b7-9041-82b700fc4bff"}}, {"head": {"id": "30695e13-287f-41ff-a6a7-ae12187b4e8a", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151230503902400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89a3fb54-b24f-4d6e-86fa-fa355a825f87", "name": "CopyResources is end, endTime: 151230504135600", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151230504145700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7901393a-bb6f-4820-8349-e321ee17ea8e", "name": "default@CompileArkTS work[3] done.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151230504348000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfa0d2e8-689d-41d6-a5ae-a839c2e6d21a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Worker18", "startTime": 151230489265900, "endTime": 151230503660300}, "additional": {"logType": "info", "children": [], "durationId": "0e4019c9-8144-4ad4-af52-66c27be47008", "parent": "44cbe3c7-547e-4cc1-8078-acc23a7d4a42"}}, {"head": {"id": "6693589b-62eb-43b6-b6bd-ac57688dda77", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151230504456700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19fe5623-f9d0-4aec-827b-2875b578f00b", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240372167500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51c4fbca-cba6-4c23-8bd3-0dd199c8a35d", "name": "default@CompileArkTS work[2] failed.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240372577900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32d24414-167c-4444-bc0d-6fe4ce3f5eac", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 151229371681800, "endTime": 151240372457900}, "additional": {"logType": "error", "children": [], "durationId": "6132f703-fa33-4662-b54a-8cc8c1be7aea", "parent": "44cbe3c7-547e-4cc1-8078-acc23a7d4a42"}}, {"head": {"id": "44cbe3c7-547e-4cc1-8078-acc23a7d4a42", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151229335170100, "endTime": 151240372760600}, "additional": {"logType": "error", "children": ["32d24414-167c-4444-bc0d-6fe4ce3f5eac", "cfa0d2e8-689d-41d6-a5ae-a839c2e6d21a"], "durationId": "321aafe1-e114-43f3-88b2-9f2aed80f0a9"}}, {"head": {"id": "66694a01-88a4-45f0-ad57-8ab1db0c9257", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240372939200}, "additional": {"logType": "debug", "children": [], "durationId": "321aafe1-e114-43f3-88b2-9f2aed80f0a9"}}, {"head": {"id": "174d5fcf-b361-463b-9194-8dbaad172f59", "name": "ERROR: stacktrace = Error: ArkTS Compiler Error\r\n\u001b[33m1 WARN: \u001b[33m\u001b[33mArkTS:WARN: For details about ArkTS syntax errors, see FAQs\u001b[39m\u001b[39m\r\n\u001b[33m2 WARN: \u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedCollectionDetailPage.ets:23:1\n It's not a recommended way to export struct with '@Entry' decorator, which may cause ACE Engine error in component preview mode.\u001b[39m\u001b[39m\r\n\u001b[33m3 WARN: \u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedCollectionPage.ets:17:1\n It's not a recommended way to export struct with '@Entry' decorator, which may cause ACE Engine error in component preview mode.\u001b[39m\u001b[39m\r\n\u001b[33m4 WARN: \u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedPage.ets:17:1\n It's not a recommended way to export struct with '@Entry' decorator, which may cause ACE Engine error in component preview mode.\u001b[39m\u001b[39m\r\n\u001b[31m1 ERROR: \u001b[31m10605029 ArkTS Compiler Error\r\nError Message: Indexed access is not supported for fields (arkts-no-props-by-index) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedCollectionDetailPage.ets:47:27\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m2 ERROR: \u001b[31m10605029 ArkTS Compiler Error\r\nError Message: Indexed access is not supported for fields (arkts-no-props-by-index) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedCollectionDetailPage.ets:48:25\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m3 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'collections' does not exist on type 'FeaturedCollectionListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedCollectionPage.ets:87:42\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m4 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'collections' does not exist on type 'FeaturedCollectionListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedCollectionPage.ets:122:73\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m5 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'TEXT_TERTIARY' does not exist on type 'ColorConfig'. Did you mean 'TEXT_PRIMARY'? At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedCollectionPage.ets:203:41\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m6 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'margin' does not exist on type 'void'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedCollectionPage.ets:292:20\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m7 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'TEXT_TERTIARY' does not exist on type 'ColorConfig'. Did you mean 'TEXT_PRIMARY'? At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedCollectionDetailPage.ets:205:41\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m8 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'RATING' does not exist on type 'ColorConfig'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedCollectionDetailPage.ets:259:43\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m9 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'margin' does not exist on type 'void'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedCollectionDetailPage.ets:392:20\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:10 WARN:4}\u001b[39m\n    at runArkPack (C:\\Program Files\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5479)", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240373919000}, "additional": {"logType": "debug", "children": [], "durationId": "321aafe1-e114-43f3-88b2-9f2aed80f0a9"}}, {"head": {"id": "18a8cbfc-1543-43e7-ba08-50d9c8fd9433", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240389459600, "endTime": 151240389734800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7f1a5b68-a7bd-49ca-8c76-f7a091aaae98", "logId": "c8aab77b-4789-4572-85e6-b0d78c9f4bab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8aab77b-4789-4572-85e6-b0d78c9f4bab", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240389459600, "endTime": 151240389734800}, "additional": {"logType": "info", "children": [], "durationId": "18a8cbfc-1543-43e7-ba08-50d9c8fd9433"}}, {"head": {"id": "35b14f63-aa19-4e60-aa35-f9e7694b66d5", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151228250710400, "endTime": 151240390174400}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 23, "minute": 57, "second": 50}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "1f7f19ae-99fc-466a-9b96-de5fc05186f6", "name": "BUILD FAILED in 12 s 140 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240390222200}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "bf839138-c842-4482-8093-9623d8ab81b1", "name": "Update task entry:default@PreBuild input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240391586700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db285477-456d-4481-b2da-3c7623776f17", "name": "Update task entry:default@PreBuild input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240391998000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb08d007-a07b-485d-a1fb-660468e6cab0", "name": "Update task entry:default@PreBuild input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240392256400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b17a85e9-c6a3-4b4d-a555-065553e7f2f8", "name": "Update task entry:default@PreBuild input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240392469500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59805bc7-bdcb-4a8b-8ddb-0fcb031a3ca4", "name": "Update task entry:default@PreBuild input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources\\base\\profile\\main_pages.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240392721700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efa5d15c-3ba3-4130-a495-016b6e16b8e1", "name": "Update task entry:default@PreBuild input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigor\\hvigor-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240392781600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c442e1c2-1211-49ca-977c-606984b940ad", "name": "Update task entry:default@PreBuild input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240392985600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "570fb736-6733-4de2-9419-451076e40b4f", "name": "Update task entry:default@PreBuild input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240393175700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c273c3d-22e9-4b0d-a1e0-b43a41e6b059", "name": "Update task entry:default@PreBuild input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240393388500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd1b8d20-4d80-4561-b7a6-521d2381a31d", "name": "Incremental task entry:default@PreBuild post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240393725700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55a70553-6570-4e77-815c-c52df0a4b8c9", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240393812000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1929ef2-4d9b-433a-b3ab-30ba7b8db43e", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240394219500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cb5891e-d95d-4a99-ab8c-7e7bf3c3929f", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240394302800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "334e1cc1-9060-4def-9c38-10c5ba01657e", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240394356300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0558c7a-32b8-490d-8512-c50f55300464", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240394391400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3e73e76-f746-4215-905e-9e4deea4af1e", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240394428500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71781d9c-fc42-4ea1-ae8e-42bb770717b2", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240395093900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed839b5d-6a59-4326-97d3-0f36a9cecf31", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240395311000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd7b05f0-538a-4bb1-8a5f-bc175e6293d9", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240395378800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b42f3d0c-ba25-4a95-ab0d-3e984d18c34f", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240395418000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1e0e752-b88f-4817-89e9-4766b14cac96", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240395456800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3330ca97-019c-4fd6-ae49-2ad6c80f039f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240395493100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a1def75-14e1-4b81-89ce-26c0147f1aef", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240397485500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "776847ad-fa12-4fbe-8328-8774036311da", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240397925600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "872d4299-fa91-4702-a2bd-bf9f05042cc3", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240398211200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "befe6bd6-4e8f-4af4-b20e-349bbbc56b6f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240398293000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19155e1c-acbe-4768-9245-66f086e27992", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240398348800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b8f1039-caf2-4194-974e-1cf65d1dfc23", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240398390200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87ff13bf-9d60-4204-aea6-6ad1442a743d", "name": "Update task entry:default@CompileResource input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240399573100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a53d308-5d21-446c-993e-80a046f0f425", "name": "Update task entry:default@CompileResource input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240399698300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75ff95b1-84d6-4e76-9d88-ba9b2d404806", "name": "Update task entry:default@CompileResource input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240401700800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b58906f-0c4d-4c95-9039-b9cbb0b67bdc", "name": "Update task entry:default@CompileResource input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240402002700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "490e0ed3-df28-420d-8b25-88aad07dfd25", "name": "Update task entry:default@CompileResource output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240402293200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de8e2cda-9002-4e50-939b-2abd7256268d", "name": "Update task entry:default@CompileResource output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240412584700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5db8cbb1-9a67-4f15-bdbf-7500bad18ceb", "name": "Update task entry:default@CompileResource output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240413212700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e74177f2-0aa7-48b9-8d10-1bb9ef44ba93", "name": "Incremental task entry:default@CompileResource post-execution cost:16 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240414439100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "986a88ca-db6c-47a1-82d0-98854561def1", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240414604400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c5390aa-06f0-4abc-9238-78a0dbb80251", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240418104700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e7e1e6a-a9f7-4853-b8f7-40db9554672e", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240418894800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cd6e3f3-b739-41cf-8df7-261c071b2cad", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240419369900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "250c3328-f864-43b3-9111-98bd2f65c722", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240419463900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4801a2c-c52f-40b3-b43a-ce69fc881ecd", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240419741000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7569ff03-11e5-4c86-88d7-d1e3695eb7f3", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240420636000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8baeede-2e8f-4258-ab16-6865e1aba483", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240428312200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bd863ad-8290-4669-ada5-a3b950d0008f", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240428711500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f20d973d-10e2-4b03-80dc-ce6732c36ff1", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240429158500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8baf3a4-6bec-4e75-b62a-318dbcd58cd0", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240430352300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "094e8ed0-9429-4f46-88e6-6b4feb11dc53", "name": "Incremental task entry:default@CompileArkTS post-execution cost:17 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240431121700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbde9009-1877-42a3-bed2-4b4eaeebc5bb", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240433598200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a5470ba-85c9-4856-89e5-11c7d7f8a0c5", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240434967500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72907fac-6623-4cd7-9a0b-d4226a8e0a0d", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240435576100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5283149-f04d-49e6-ba60-429785c78030", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240435935900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02396090-f263-42af-aff1-e6c9fdadbbe8", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240436202000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "867716ef-64d5-4d73-a521-28a15f7b43a0", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240437147500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9368c76f-341b-4c43-949c-e0e1d219e87c", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240438221800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "074de7cd-e702-472c-9d7d-068ef37de0a6", "name": "Incremental task entry:default@BuildJS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240438650800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30b684ca-a44b-4645-b6f9-e2c7c9677d5a", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 151240438772200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}