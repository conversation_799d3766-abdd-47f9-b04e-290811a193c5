"use strict";
import { jsx } from "react/jsx-runtime";
import { createContext, useContext, useEffect, useState } from "react";
import { LogtoProvider as LogtoReactProvider } from "@logto/react";
import { logtoConfig } from "@/config/logto";
import { message } from "antd";
const AuthContext = createContext(null);
const config = {
  endpoint: logtoConfig.endpoint,
  appId: logtoConfig.appId,
  resources: logtoConfig.resources,
  scopes: logtoConfig.scopes
};
export const LogtoProvider = ({ children }) => {
  return /* @__PURE__ */ jsx(LogtoReactProvider, { config, children: /* @__PURE__ */ jsx(AuthContextProvider, { children }) });
};
const AuthContextProvider = ({ children }) => {
  const [authState, setAuthState] = useState({
    isAuthenticated: false,
    isLoading: true
  });
  useEffect(() => {
    const initAuth = async () => {
      try {
        setAuthState((prev) => ({ ...prev, isLoading: false }));
      } catch (error) {
        console.error("\u8BA4\u8BC1\u521D\u59CB\u5316\u5931\u8D25:", error);
        setAuthState({
          isAuthenticated: false,
          isLoading: false,
          error
        });
        message.error("\u8BA4\u8BC1\u521D\u59CB\u5316\u5931\u8D25");
      }
    };
    initAuth();
  }, []);
  return /* @__PURE__ */ jsx(AuthContext.Provider, { value: authState, children });
};
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within a LogtoProvider");
  }
  return context;
};
export default LogtoProvider;
