{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "de2169bd-6ae1-4e17-bbeb-c69332dba45b", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181730155000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85851896-7060-4d93-8e29-38f5bf2af908", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181730393600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "750fc98d-3e7c-4e1a-a7a6-e61e0ac575a3", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181767803700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35be1b50-d038-447a-87f8-a5c1e5020c52", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181768143700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f976550-e89c-4ac8-a27b-e8ccafa051e8", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181771139400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7371fc5-552c-4740-bdc7-b94fe256ea4a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181771497000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f59ef36-77d8-404e-b1da-90270bf68d2b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012146985200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2749e6b-187f-4d09-8965-a2e68731645a", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012152756200, "endTime": 156012344734300}, "additional": {"children": ["3b50afc1-d541-4da7-bd8a-a1ecb70e44c7", "da1f1fa6-cc5b-485e-b005-6b08f5463263", "65fa1ef7-0c73-4459-9075-99451afc57ab", "9379b17f-3a8f-4caa-8c71-92a28d4cb0da", "21fde15d-49fb-496b-8443-a60e7666a4f3", "51dca106-4e17-4209-b349-2c75f69dc34f", "68a81b30-beab-49ae-8667-2a0f60d96275"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "6bb7d77a-c332-421c-bcfb-c2b550290888"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b50afc1-d541-4da7-bd8a-a1ecb70e44c7", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012152761700, "endTime": 156012165747500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a2749e6b-187f-4d09-8965-a2e68731645a", "logId": "96e7584a-48c6-47db-8c39-83c80abe0f84"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da1f1fa6-cc5b-485e-b005-6b08f5463263", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012165763100, "endTime": 156012343387900}, "additional": {"children": ["c43f2085-b848-4bcb-be4e-42700ab70867", "4bfd2660-b590-458a-93e4-d383366ab45d", "87cd6e2a-7c7f-4873-bc79-a0894fdea232", "f340ac8f-85a3-4f48-9049-dd2122fd1ee7", "002e2411-f01c-4b0c-b63c-4e683f429a50", "f7dcfcc8-b4f8-4e0a-957a-c7d0965ad40e", "1e3d6c55-e350-472a-9201-2b0279984a69", "327fbd03-f9d1-4b3a-bb91-b5e16c63a437", "55a24b6d-daf4-446b-b11e-e04d5745bd58"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a2749e6b-187f-4d09-8965-a2e68731645a", "logId": "a1befd2a-41af-4aaf-b261-c90dca96ae96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65fa1ef7-0c73-4459-9075-99451afc57ab", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012343412300, "endTime": 156012344718900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a2749e6b-187f-4d09-8965-a2e68731645a", "logId": "63d8f8f5-06ee-4d09-8427-2de97ce3ff7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9379b17f-3a8f-4caa-8c71-92a28d4cb0da", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012344723400, "endTime": 156012344730700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a2749e6b-187f-4d09-8965-a2e68731645a", "logId": "35cf6460-172e-44a9-8732-22ef4392f6f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21fde15d-49fb-496b-8443-a60e7666a4f3", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012156123400, "endTime": 156012156584200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a2749e6b-187f-4d09-8965-a2e68731645a", "logId": "44068bd0-e138-48db-95d0-993a9be23b6f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44068bd0-e138-48db-95d0-993a9be23b6f", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012156123400, "endTime": 156012156584200}, "additional": {"logType": "info", "children": [], "durationId": "21fde15d-49fb-496b-8443-a60e7666a4f3", "parent": "6bb7d77a-c332-421c-bcfb-c2b550290888"}}, {"head": {"id": "51dca106-4e17-4209-b349-2c75f69dc34f", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012160422300, "endTime": 156012160434900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a2749e6b-187f-4d09-8965-a2e68731645a", "logId": "6177c643-f0ee-4a10-bdf8-7081e9812c01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6177c643-f0ee-4a10-bdf8-7081e9812c01", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012160422300, "endTime": 156012160434900}, "additional": {"logType": "info", "children": [], "durationId": "51dca106-4e17-4209-b349-2c75f69dc34f", "parent": "6bb7d77a-c332-421c-bcfb-c2b550290888"}}, {"head": {"id": "42f14fa4-a67f-400e-b170-095f085cca11", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012160489800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0570a35f-b1b8-4332-8679-28582fe1c037", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012165617300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96e7584a-48c6-47db-8c39-83c80abe0f84", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012152761700, "endTime": 156012165747500}, "additional": {"logType": "info", "children": [], "durationId": "3b50afc1-d541-4da7-bd8a-a1ecb70e44c7", "parent": "6bb7d77a-c332-421c-bcfb-c2b550290888"}}, {"head": {"id": "c43f2085-b848-4bcb-be4e-42700ab70867", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012170874400, "endTime": 156012170881200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da1f1fa6-cc5b-485e-b005-6b08f5463263", "logId": "85f9f0b1-6255-4e9a-a9d4-812cb41d5dc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bfd2660-b590-458a-93e4-d383366ab45d", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012170899300, "endTime": 156012175562800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da1f1fa6-cc5b-485e-b005-6b08f5463263", "logId": "ef0a84ac-567c-4d8c-bd53-01330fa53e7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87cd6e2a-7c7f-4873-bc79-a0894fdea232", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012175574000, "endTime": 156012242078600}, "additional": {"children": ["0fdd98ab-90d2-46f9-95e7-27f96a4d0055", "77cc5894-31ff-4911-851b-2283cb3d2fb9", "0dde34e7-2e90-484b-b822-da3d4d8c1b1c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da1f1fa6-cc5b-485e-b005-6b08f5463263", "logId": "2048e458-5ec6-4fc3-82d4-78e33c0a994e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f340ac8f-85a3-4f48-9049-dd2122fd1ee7", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012242090600, "endTime": 156012262018700}, "additional": {"children": ["a85b6d51-9f29-4092-80f9-e3729dbdcea2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da1f1fa6-cc5b-485e-b005-6b08f5463263", "logId": "230c6191-a12b-4449-a841-f2fd1eaac3a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "002e2411-f01c-4b0c-b63c-4e683f429a50", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012262033300, "endTime": 156012309908100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da1f1fa6-cc5b-485e-b005-6b08f5463263", "logId": "75ee9732-a668-486f-b4a0-1b0d0370a699"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7dcfcc8-b4f8-4e0a-957a-c7d0965ad40e", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012311031700, "endTime": 156012326898100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da1f1fa6-cc5b-485e-b005-6b08f5463263", "logId": "c0569a85-e964-43a2-a180-2616155e8f4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e3d6c55-e350-472a-9201-2b0279984a69", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012326921500, "endTime": 156012343207500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da1f1fa6-cc5b-485e-b005-6b08f5463263", "logId": "09e10935-d49b-4cc2-8bce-984576b23c05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "327fbd03-f9d1-4b3a-bb91-b5e16c63a437", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012343226500, "endTime": 156012343376000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da1f1fa6-cc5b-485e-b005-6b08f5463263", "logId": "5d8b28f1-4897-4cda-8e52-2c0c9b4eab3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85f9f0b1-6255-4e9a-a9d4-812cb41d5dc2", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012170874400, "endTime": 156012170881200}, "additional": {"logType": "info", "children": [], "durationId": "c43f2085-b848-4bcb-be4e-42700ab70867", "parent": "a1befd2a-41af-4aaf-b261-c90dca96ae96"}}, {"head": {"id": "ef0a84ac-567c-4d8c-bd53-01330fa53e7f", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012170899300, "endTime": 156012175562800}, "additional": {"logType": "info", "children": [], "durationId": "4bfd2660-b590-458a-93e4-d383366ab45d", "parent": "a1befd2a-41af-4aaf-b261-c90dca96ae96"}}, {"head": {"id": "0fdd98ab-90d2-46f9-95e7-27f96a4d0055", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012176162100, "endTime": 156012176181700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87cd6e2a-7c7f-4873-bc79-a0894fdea232", "logId": "15425bae-94da-4110-be81-1cfed1a93e19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15425bae-94da-4110-be81-1cfed1a93e19", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012176162100, "endTime": 156012176181700}, "additional": {"logType": "info", "children": [], "durationId": "0fdd98ab-90d2-46f9-95e7-27f96a4d0055", "parent": "2048e458-5ec6-4fc3-82d4-78e33c0a994e"}}, {"head": {"id": "77cc5894-31ff-4911-851b-2283cb3d2fb9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012177928900, "endTime": 156012241530400}, "additional": {"children": ["1cd47ca7-6f45-457d-9e17-a6d485b24182", "75686645-7766-434e-ac2b-9e913f39f064"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87cd6e2a-7c7f-4873-bc79-a0894fdea232", "logId": "3369d209-214e-4e98-8135-87f46247a7fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1cd47ca7-6f45-457d-9e17-a6d485b24182", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012177929900, "endTime": 156012183117900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "77cc5894-31ff-4911-851b-2283cb3d2fb9", "logId": "e237b5f7-b3a9-4567-b55b-da9fc4dacc44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75686645-7766-434e-ac2b-9e913f39f064", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012183129500, "endTime": 156012241507900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "77cc5894-31ff-4911-851b-2283cb3d2fb9", "logId": "46e3071f-e14e-41e2-8b67-82e337c412c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c15c95a-88fa-45f1-b9cd-e7b888d7edfa", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012177933000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c02eef2-378c-4fdd-b658-5f95bb01f265", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012183004200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e237b5f7-b3a9-4567-b55b-da9fc4dacc44", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012177929900, "endTime": 156012183117900}, "additional": {"logType": "info", "children": [], "durationId": "1cd47ca7-6f45-457d-9e17-a6d485b24182", "parent": "3369d209-214e-4e98-8135-87f46247a7fb"}}, {"head": {"id": "2c734b92-d1e7-467c-8f9a-bd472ba7b7b3", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012183139800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0caded9d-c136-4b24-a80d-712c6907c824", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012190118900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f19019fa-e014-4507-b3fd-7fe536e16f8c", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012190254800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "804c4ba8-e1d7-47d3-b8a7-1437e3ede2a5", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012190358300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "648fab87-b966-4667-985a-bcff045564ab", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012190421500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1287bcc-18e0-456c-9649-75f29c58230c", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012192621500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0f8208c-6e15-433e-bc83-038d23dd15e9", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012203251600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b77c14bd-f020-47a5-93f7-b6b2df85ddc0", "name": "Sdk init in 27 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012223307800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbf74cfb-1094-4862-9cc3-ad3e2284c34c", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012223467800}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 17, "second": 22}, "markType": "other"}}, {"head": {"id": "a10dd4cd-dbf5-4891-89b6-5e1a7e3d3e97", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012223526300}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 17, "second": 22}, "markType": "other"}}, {"head": {"id": "ecbab7d5-0242-40c8-b970-c2012cefed88", "name": "Project task initialization takes 17 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012241280800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13c16495-3592-41a2-8aa7-81897e11c791", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012241398100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87bd8f54-20ee-4702-a3f1-8cb8ed7d1ef7", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012241445000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7046cbef-f0f6-4a4b-b48e-52815ff27b29", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012241478500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46e3071f-e14e-41e2-8b67-82e337c412c1", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012183129500, "endTime": 156012241507900}, "additional": {"logType": "info", "children": [], "durationId": "75686645-7766-434e-ac2b-9e913f39f064", "parent": "3369d209-214e-4e98-8135-87f46247a7fb"}}, {"head": {"id": "3369d209-214e-4e98-8135-87f46247a7fb", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012177928900, "endTime": 156012241530400}, "additional": {"logType": "info", "children": ["e237b5f7-b3a9-4567-b55b-da9fc4dacc44", "46e3071f-e14e-41e2-8b67-82e337c412c1"], "durationId": "77cc5894-31ff-4911-851b-2283cb3d2fb9", "parent": "2048e458-5ec6-4fc3-82d4-78e33c0a994e"}}, {"head": {"id": "0dde34e7-2e90-484b-b822-da3d4d8c1b1c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012242056300, "endTime": 156012242069600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87cd6e2a-7c7f-4873-bc79-a0894fdea232", "logId": "438fc4d0-1d75-4e04-87a6-c6d0584e0384"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "438fc4d0-1d75-4e04-87a6-c6d0584e0384", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012242056300, "endTime": 156012242069600}, "additional": {"logType": "info", "children": [], "durationId": "0dde34e7-2e90-484b-b822-da3d4d8c1b1c", "parent": "2048e458-5ec6-4fc3-82d4-78e33c0a994e"}}, {"head": {"id": "2048e458-5ec6-4fc3-82d4-78e33c0a994e", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012175574000, "endTime": 156012242078600}, "additional": {"logType": "info", "children": ["15425bae-94da-4110-be81-1cfed1a93e19", "3369d209-214e-4e98-8135-87f46247a7fb", "438fc4d0-1d75-4e04-87a6-c6d0584e0384"], "durationId": "87cd6e2a-7c7f-4873-bc79-a0894fdea232", "parent": "a1befd2a-41af-4aaf-b261-c90dca96ae96"}}, {"head": {"id": "a85b6d51-9f29-4092-80f9-e3729dbdcea2", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012242551000, "endTime": 156012262009300}, "additional": {"children": ["dfb85311-15d2-48eb-9578-93388c7a3d0c", "d4893158-fdde-4270-9b47-9517d74edbf6", "6c054698-a0bd-499e-8219-5606f13cabb7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f340ac8f-85a3-4f48-9049-dd2122fd1ee7", "logId": "774ff9f4-9b94-4be5-919b-08f3099971c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfb85311-15d2-48eb-9578-93388c7a3d0c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012244870700, "endTime": 156012244884100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a85b6d51-9f29-4092-80f9-e3729dbdcea2", "logId": "d8f606ba-e8b7-4b34-8ac4-5cfb8f8652c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8f606ba-e8b7-4b34-8ac4-5cfb8f8652c5", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012244870700, "endTime": 156012244884100}, "additional": {"logType": "info", "children": [], "durationId": "dfb85311-15d2-48eb-9578-93388c7a3d0c", "parent": "774ff9f4-9b94-4be5-919b-08f3099971c4"}}, {"head": {"id": "d4893158-fdde-4270-9b47-9517d74edbf6", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012247076100, "endTime": 156012260525700}, "additional": {"children": ["07c89616-b7f0-41d8-9dea-6b7b20358c65", "0f76a239-d4dd-40ae-8838-8a5e6365dfab"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a85b6d51-9f29-4092-80f9-e3729dbdcea2", "logId": "5eea971d-d508-4816-bb27-3fa95748672e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07c89616-b7f0-41d8-9dea-6b7b20358c65", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012247077200, "endTime": 156012249524500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d4893158-fdde-4270-9b47-9517d74edbf6", "logId": "bc50a4ec-fd1c-4e49-acb0-4d8d2b313bcb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f76a239-d4dd-40ae-8838-8a5e6365dfab", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012249535300, "endTime": 156012260470700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d4893158-fdde-4270-9b47-9517d74edbf6", "logId": "7c57495d-9c4d-4450-afc9-84b3af620f91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08e7eb0b-8066-4875-972a-12e36f4f8a44", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012247080300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ab1a5e1-1edc-4989-ba52-4f1b65294802", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012249431200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc50a4ec-fd1c-4e49-acb0-4d8d2b313bcb", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012247077200, "endTime": 156012249524500}, "additional": {"logType": "info", "children": [], "durationId": "07c89616-b7f0-41d8-9dea-6b7b20358c65", "parent": "5eea971d-d508-4816-bb27-3fa95748672e"}}, {"head": {"id": "8a4dc6e9-7711-4011-bd29-7089c58e6542", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012249541500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa014753-5f36-4999-b4ba-b8c4aac664fb", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012255423800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8f04db8-82c9-4281-ab24-c819297da753", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012255528200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11d98d87-91a8-4045-b3d6-cd780217763e", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012255680700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4f830af-348b-48e5-840d-71b3adb19abb", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012255763300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e921bf7e-e2ca-412b-b75b-da37dc255bb1", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012255799100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e88d1cc0-a60b-49da-998f-5caf441cc33c", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012255828300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "060b1f40-8b76-4fea-a458-d3e7e044d712", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012255864800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6256e900-8a2b-48ce-8b17-c1204a22df2a", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012255894500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08051b3d-80c9-48d9-a139-aaaa4c15e227", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012256026900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8c5e53c-b672-4d9b-a2f4-1cedae707496", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012256102400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02ac8f25-f301-4274-8261-267f440794c8", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012256137500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9691bbc-d7ae-445a-b5fa-388a00bcafae", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012256175200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bf0c859-bc29-466c-a13a-c841f5952de5", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012256216000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61b88f01-3b38-44a5-9cb8-7b2857d60684", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012256285000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83481998-b7be-4b95-aff2-408eec05437f", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012256435400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2be7cb79-b531-419c-a9a4-0d40f5e693ab", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012256550900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67a20f36-433a-41f8-884f-08824cf5e931", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012256617500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ca93bf8-a012-4351-b7a8-4ac71ccf6cb7", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012256723800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c11e5405-65b5-4c07-9e3e-a4c34ff1a16d", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012256787600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06c315b0-962e-4430-a33b-b0d38170161a", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012260249100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "508e35d4-2b17-465d-8851-679db09955e2", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012260367900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bc7f409-36f4-4bb2-acde-3f3fd2cee9bd", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012260411400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "884ece59-0d12-4875-8238-7b20c82a2403", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012260442400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c57495d-9c4d-4450-afc9-84b3af620f91", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012249535300, "endTime": 156012260470700}, "additional": {"logType": "info", "children": [], "durationId": "0f76a239-d4dd-40ae-8838-8a5e6365dfab", "parent": "5eea971d-d508-4816-bb27-3fa95748672e"}}, {"head": {"id": "5eea971d-d508-4816-bb27-3fa95748672e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012247076100, "endTime": 156012260525700}, "additional": {"logType": "info", "children": ["bc50a4ec-fd1c-4e49-acb0-4d8d2b313bcb", "7c57495d-9c4d-4450-afc9-84b3af620f91"], "durationId": "d4893158-fdde-4270-9b47-9517d74edbf6", "parent": "774ff9f4-9b94-4be5-919b-08f3099971c4"}}, {"head": {"id": "6c054698-a0bd-499e-8219-5606f13cabb7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012261983700, "endTime": 156012261998000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a85b6d51-9f29-4092-80f9-e3729dbdcea2", "logId": "19892674-9bf0-43d7-bd55-87e2ac554ad5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19892674-9bf0-43d7-bd55-87e2ac554ad5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012261983700, "endTime": 156012261998000}, "additional": {"logType": "info", "children": [], "durationId": "6c054698-a0bd-499e-8219-5606f13cabb7", "parent": "774ff9f4-9b94-4be5-919b-08f3099971c4"}}, {"head": {"id": "774ff9f4-9b94-4be5-919b-08f3099971c4", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012242551000, "endTime": 156012262009300}, "additional": {"logType": "info", "children": ["d8f606ba-e8b7-4b34-8ac4-5cfb8f8652c5", "5eea971d-d508-4816-bb27-3fa95748672e", "19892674-9bf0-43d7-bd55-87e2ac554ad5"], "durationId": "a85b6d51-9f29-4092-80f9-e3729dbdcea2", "parent": "230c6191-a12b-4449-a841-f2fd1eaac3a2"}}, {"head": {"id": "230c6191-a12b-4449-a841-f2fd1eaac3a2", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012242090600, "endTime": 156012262018700}, "additional": {"logType": "info", "children": ["774ff9f4-9b94-4be5-919b-08f3099971c4"], "durationId": "f340ac8f-85a3-4f48-9049-dd2122fd1ee7", "parent": "a1befd2a-41af-4aaf-b261-c90dca96ae96"}}, {"head": {"id": "e2f3d8ed-fc69-413a-bb81-3d6663e89b81", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012274594800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "577014c8-fb03-4d67-8c67-77a1f0158e17", "name": "hvigorfile, resolve hvigorfile dependencies in 48 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012309760600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75ee9732-a668-486f-b4a0-1b0d0370a699", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012262033300, "endTime": 156012309908100}, "additional": {"logType": "info", "children": [], "durationId": "002e2411-f01c-4b0c-b63c-4e683f429a50", "parent": "a1befd2a-41af-4aaf-b261-c90dca96ae96"}}, {"head": {"id": "55a24b6d-daf4-446b-b11e-e04d5745bd58", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012310803100, "endTime": 156012311018700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da1f1fa6-cc5b-485e-b005-6b08f5463263", "logId": "155382f3-46c5-4019-a9cc-278cf68b9356"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9c924d4-2cde-4f62-8168-53e4089ea399", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012310834600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "155382f3-46c5-4019-a9cc-278cf68b9356", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012310803100, "endTime": 156012311018700}, "additional": {"logType": "info", "children": [], "durationId": "55a24b6d-daf4-446b-b11e-e04d5745bd58", "parent": "a1befd2a-41af-4aaf-b261-c90dca96ae96"}}, {"head": {"id": "9ea55ea7-83b2-4e72-b4f7-3217c7b66dab", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012312434000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50da9d2c-cf93-4b6a-bf79-85eba1d8f1db", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012325922200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0569a85-e964-43a2-a180-2616155e8f4f", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012311031700, "endTime": 156012326898100}, "additional": {"logType": "info", "children": [], "durationId": "f7dcfcc8-b4f8-4e0a-957a-c7d0965ad40e", "parent": "a1befd2a-41af-4aaf-b261-c90dca96ae96"}}, {"head": {"id": "4f4e6a6c-2ac4-4604-a464-07ac58ff0269", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012326939700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ca32f13-7660-4e0c-9c05-f800a0588185", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012334248300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4d9e480-51fe-4763-9c94-53af216c7d49", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012334385700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb797bb5-1bfa-4ea9-99de-fe7f9eaae11e", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012334543900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9dd2856-e2e5-453b-a938-ff3587cbba3c", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012337823300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34f27058-9a73-4294-bbd4-ef57ece9c3ce", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012337951400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09e10935-d49b-4cc2-8bce-984576b23c05", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012326921500, "endTime": 156012343207500}, "additional": {"logType": "info", "children": [], "durationId": "1e3d6c55-e350-472a-9201-2b0279984a69", "parent": "a1befd2a-41af-4aaf-b261-c90dca96ae96"}}, {"head": {"id": "c70c1b1f-81bf-4657-85d8-a5cc5e7055df", "name": "Configuration phase cost:173 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012343251500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d8b28f1-4897-4cda-8e52-2c0c9b4eab3e", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012343226500, "endTime": 156012343376000}, "additional": {"logType": "info", "children": [], "durationId": "327fbd03-f9d1-4b3a-bb91-b5e16c63a437", "parent": "a1befd2a-41af-4aaf-b261-c90dca96ae96"}}, {"head": {"id": "a1befd2a-41af-4aaf-b261-c90dca96ae96", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012165763100, "endTime": 156012343387900}, "additional": {"logType": "info", "children": ["85f9f0b1-6255-4e9a-a9d4-812cb41d5dc2", "ef0a84ac-567c-4d8c-bd53-01330fa53e7f", "2048e458-5ec6-4fc3-82d4-78e33c0a994e", "230c6191-a12b-4449-a841-f2fd1eaac3a2", "75ee9732-a668-486f-b4a0-1b0d0370a699", "c0569a85-e964-43a2-a180-2616155e8f4f", "09e10935-d49b-4cc2-8bce-984576b23c05", "5d8b28f1-4897-4cda-8e52-2c0c9b4eab3e", "155382f3-46c5-4019-a9cc-278cf68b9356"], "durationId": "da1f1fa6-cc5b-485e-b005-6b08f5463263", "parent": "6bb7d77a-c332-421c-bcfb-c2b550290888"}}, {"head": {"id": "68a81b30-beab-49ae-8667-2a0f60d96275", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012344694700, "endTime": 156012344710100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a2749e6b-187f-4d09-8965-a2e68731645a", "logId": "9f5ed28a-c68b-4e93-9aca-d15c79079e8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f5ed28a-c68b-4e93-9aca-d15c79079e8f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012344694700, "endTime": 156012344710100}, "additional": {"logType": "info", "children": [], "durationId": "68a81b30-beab-49ae-8667-2a0f60d96275", "parent": "6bb7d77a-c332-421c-bcfb-c2b550290888"}}, {"head": {"id": "63d8f8f5-06ee-4d09-8427-2de97ce3ff7f", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012343412300, "endTime": 156012344718900}, "additional": {"logType": "info", "children": [], "durationId": "65fa1ef7-0c73-4459-9075-99451afc57ab", "parent": "6bb7d77a-c332-421c-bcfb-c2b550290888"}}, {"head": {"id": "35cf6460-172e-44a9-8732-22ef4392f6f2", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012344723400, "endTime": 156012344730700}, "additional": {"logType": "info", "children": [], "durationId": "9379b17f-3a8f-4caa-8c71-92a28d4cb0da", "parent": "6bb7d77a-c332-421c-bcfb-c2b550290888"}}, {"head": {"id": "6bb7d77a-c332-421c-bcfb-c2b550290888", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012152756200, "endTime": 156012344734300}, "additional": {"logType": "info", "children": ["96e7584a-48c6-47db-8c39-83c80abe0f84", "a1befd2a-41af-4aaf-b261-c90dca96ae96", "63d8f8f5-06ee-4d09-8427-2de97ce3ff7f", "35cf6460-172e-44a9-8732-22ef4392f6f2", "44068bd0-e138-48db-95d0-993a9be23b6f", "6177c643-f0ee-4a10-bdf8-7081e9812c01", "9f5ed28a-c68b-4e93-9aca-d15c79079e8f"], "durationId": "a2749e6b-187f-4d09-8965-a2e68731645a"}}, {"head": {"id": "23c14f3f-57da-4b15-ac15-e9865233051a", "name": "Configuration task cost before running: 195 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012345076500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86fb9047-8c34-4290-b5c1-26d48e6a7d57", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012354397400, "endTime": 156012369256900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e9cd0bf0-b6eb-4af8-838a-ec4ce90801af", "logId": "e3190b76-9c5e-4ac7-a3c8-8ce43d8bfeb6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9cd0bf0-b6eb-4af8-838a-ec4ce90801af", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012346509700}, "additional": {"logType": "detail", "children": [], "durationId": "86fb9047-8c34-4290-b5c1-26d48e6a7d57"}}, {"head": {"id": "734478f5-76ad-46ae-b47a-8744dfdfa309", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012347248300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9424b594-25ab-4b1d-9beb-299ddb96bcc3", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012347360900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73e100ca-415f-408f-983b-4f555cab921c", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012348229100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35b740c5-2539-4c95-bc39-28eb3997a4e0", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012349096000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b55d7e4-4b7c-4ea8-9cc7-36c69dc39750", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012350151600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47d51421-c971-403c-8a1e-186a4c84a10e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012350247000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3520fb53-e3fc-4945-8a4a-5d54c7d3bb2e", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012354412100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b69c21a-b213-440e-ab93-cfdadb512169", "name": "Incremental task entry:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012369011900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76f5c29f-3bb5-4099-8b62-8b61d1a2348a", "name": "entry : default@PreBuild cost memory 0.32342529296875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012369162600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3190b76-9c5e-4ac7-a3c8-8ce43d8bfeb6", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012354397400, "endTime": 156012369256900}, "additional": {"logType": "info", "children": [], "durationId": "86fb9047-8c34-4290-b5c1-26d48e6a7d57"}}, {"head": {"id": "a4f94c1f-fbf1-451e-9288-589968cb5460", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012377542100, "endTime": 156012379262000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9c0e187c-3a3a-4dd9-8f6e-2a29bab553d2", "logId": "b60e900b-ddd7-4dea-a437-212aa58f9882"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c0e187c-3a3a-4dd9-8f6e-2a29bab553d2", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012375670000}, "additional": {"logType": "detail", "children": [], "durationId": "a4f94c1f-fbf1-451e-9288-589968cb5460"}}, {"head": {"id": "d23734e9-1aab-42af-8fe0-d3f4ad0dfc94", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012376755700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbf9de07-3646-441c-8e6b-5adec606fa8c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012376869600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdb68cf9-5c3e-49de-b4cb-a19a6ecaaf13", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012377552300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88cd39b6-bc35-41cf-9b8b-2f511f660bee", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012378201200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c40aeb8-989f-4dae-812b-ce628c57d154", "name": "entry : default@CreateModuleInfo cost memory 0.0599517822265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012379070600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8511f3d4-699d-49aa-abf0-d382d9049215", "name": "runTaskFromQueue task cost before running: 229 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012379206100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b60e900b-ddd7-4dea-a437-212aa58f9882", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012377542100, "endTime": 156012379262000, "totalTime": 1644200}, "additional": {"logType": "info", "children": [], "durationId": "a4f94c1f-fbf1-451e-9288-589968cb5460"}}, {"head": {"id": "fafd1ee6-11eb-44e3-b456-ed5a024b521e", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012388554900, "endTime": 156012393284700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3ec43932-3343-4179-8a96-e8816978c03c", "logId": "bde4e395-39f2-4804-9cdf-8d0e60c9c819"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ec43932-3343-4179-8a96-e8816978c03c", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012381539400}, "additional": {"logType": "detail", "children": [], "durationId": "fafd1ee6-11eb-44e3-b456-ed5a024b521e"}}, {"head": {"id": "b8b1250f-9cdd-446d-8244-828e334c883a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012383068200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66084e91-c044-413d-ac9d-67db003ded98", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012383282300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a05e6a1b-04e5-4913-bb05-76ec27569e57", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012388572100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e6ca5d0-51ed-49c9-a5c9-8da362cc21bc", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012390498500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94ac30e7-c6ad-41e5-a2ff-3398e0330900", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012392842700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12aad341-688c-4a8a-811b-333bbf3b81f4", "name": "entry : default@GenerateMetadata cost memory 0.10221099853515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012393049100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bde4e395-39f2-4804-9cdf-8d0e60c9c819", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012388554900, "endTime": 156012393284700}, "additional": {"logType": "info", "children": [], "durationId": "fafd1ee6-11eb-44e3-b456-ed5a024b521e"}}, {"head": {"id": "47b789f8-ff9a-47eb-b754-fae8292b60df", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012399500500, "endTime": 156012400171300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0fba84a2-3428-4854-b976-80ca4b5e562a", "logId": "078ab8a8-e3b8-4e1f-ae10-a62509ce3cf1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0fba84a2-3428-4854-b976-80ca4b5e562a", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012396539100}, "additional": {"logType": "detail", "children": [], "durationId": "47b789f8-ff9a-47eb-b754-fae8292b60df"}}, {"head": {"id": "fd8b5f4f-6431-414d-b14e-8b0edeb368b9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012399016400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c31df06-f967-4132-9579-bd6b740f80ed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012399232500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db588f8f-bb66-4f72-b7fa-13e01e5ce87c", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012399515000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de7f9986-2c1a-492a-9263-87c322acb8e7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012399685500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "823861c0-4822-4bba-97f8-8262b8deb3a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012399797300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cd51543-8221-4d60-8c96-48442519f0e1", "name": "entry : default@ConfigureCmake cost memory 0.037200927734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012399924400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf39ef02-8d27-4f51-afb1-ec169fbcb373", "name": "runTaskFromQueue task cost before running: 250 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012400080500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "078ab8a8-e3b8-4e1f-ae10-a62509ce3cf1", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012399500500, "endTime": 156012400171300, "totalTime": 554800}, "additional": {"logType": "info", "children": [], "durationId": "47b789f8-ff9a-47eb-b754-fae8292b60df"}}, {"head": {"id": "e8327a40-9666-4340-b449-fa67c191f43c", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012405137500, "endTime": 156012407572200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "40c4b84c-5432-489b-b54e-52ae252aaf48", "logId": "1fb0ca6e-311f-40d3-b35f-6d7a986afa3c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40c4b84c-5432-489b-b54e-52ae252aaf48", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012403022500}, "additional": {"logType": "detail", "children": [], "durationId": "e8327a40-9666-4340-b449-fa67c191f43c"}}, {"head": {"id": "d50cc1be-edac-4570-bd68-a012d6e2371a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012404256800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf369512-84e0-4403-9775-1802bb59bbdc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012404387100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c46e314d-2d1d-4d00-a9be-5e50bc0422cd", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012405150400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11200132-0a11-470b-8375-d30a822a34da", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012407337100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60224bc2-8fd1-4757-9d22-7ffa5cba0d48", "name": "entry : default@MergeProfile cost memory 0.1180877685546875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012407497800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fb0ca6e-311f-40d3-b35f-6d7a986afa3c", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012405137500, "endTime": 156012407572200}, "additional": {"logType": "info", "children": [], "durationId": "e8327a40-9666-4340-b449-fa67c191f43c"}}, {"head": {"id": "c6354036-4247-4249-8449-0be4be693e80", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012411563200, "endTime": 156012414578300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "70cd20cd-46c0-4815-9082-709ff35fd7eb", "logId": "b8379646-28a3-4203-bfa8-7aa35d79155d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70cd20cd-46c0-4815-9082-709ff35fd7eb", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012409493700}, "additional": {"logType": "detail", "children": [], "durationId": "c6354036-4247-4249-8449-0be4be693e80"}}, {"head": {"id": "8a415e5c-2d65-4387-a0e2-70d8df86ced9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012410558500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb568c16-1bc5-46ef-a25f-d2a0517fb189", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012410690300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adb44022-4cf1-4992-bf84-7277cdb2d666", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012411571800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a849bdc0-e167-433b-a879-c4cd067bfe2e", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012412598000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ca846cf-8ede-4cb2-a684-c0cec1efbab6", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012414320600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59d98ba1-ef6b-4145-9625-27d80dee7931", "name": "entry : default@CreateBuildProfile cost memory 0.106597900390625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012414447300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8379646-28a3-4203-bfa8-7aa35d79155d", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012411563200, "endTime": 156012414578300}, "additional": {"logType": "info", "children": [], "durationId": "c6354036-4247-4249-8449-0be4be693e80"}}, {"head": {"id": "9a7b9396-e503-4899-8f2e-feda1fcff194", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012418507500, "endTime": 156012419029200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "c8e63e23-735b-4a5e-b669-56485284e92d", "logId": "5068fd2d-d5cb-4227-a37b-6003a9ab7886"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8e63e23-735b-4a5e-b669-56485284e92d", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012416178200}, "additional": {"logType": "detail", "children": [], "durationId": "9a7b9396-e503-4899-8f2e-feda1fcff194"}}, {"head": {"id": "9d8c23bd-12a3-4f29-a4d9-870f16df8f55", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012417516200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a09ecfee-8552-4bbe-8826-ad58422759b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012417638900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d436af0-5bb4-4d37-bc76-de15c5975866", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012418516400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e17fa621-56df-4f45-9b83-dd723ac48a3f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012418643100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d557dde5-96ca-48b9-8736-1bb344f5890e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012418691700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58ba4f84-cd00-4953-a0b6-0f28ab565da9", "name": "entry : default@PreCheckSyscap cost memory 0.04083251953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012418889100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "982dbcd1-45e3-4b87-bd3f-d397c9950263", "name": "runTaskFromQueue task cost before running: 269 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012418981500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5068fd2d-d5cb-4227-a37b-6003a9ab7886", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012418507500, "endTime": 156012419029200, "totalTime": 456700}, "additional": {"logType": "info", "children": [], "durationId": "9a7b9396-e503-4899-8f2e-feda1fcff194"}}, {"head": {"id": "75457080-9cd9-4b1e-beba-59061e07bb46", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012425026300, "endTime": 156012433517800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5cf7c7ca-0272-4e39-ac90-bb28410234ea", "logId": "8096ea33-5acd-447a-b500-b68687097aec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cf7c7ca-0272-4e39-ac90-bb28410234ea", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012421443100}, "additional": {"logType": "detail", "children": [], "durationId": "75457080-9cd9-4b1e-beba-59061e07bb46"}}, {"head": {"id": "1a1a4eed-e776-44aa-b2c0-b59cfb12d88d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012422777600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5651ed2c-75ab-48ba-af8c-1bc93364f6a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012422950600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4d94c85-b039-433e-8aa8-cc5b40c13c91", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012425038600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b42ef478-52fa-482f-817d-9c56e339c278", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012430587900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a064db47-4ae6-497a-9dfc-310cb89f62a1", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012433325300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7a33e06-c416-4178-a24c-2eca338ae1b0", "name": "entry : default@GeneratePkgContextInfo cost memory -4.754119873046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012433451900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8096ea33-5acd-447a-b500-b68687097aec", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012425026300, "endTime": 156012433517800}, "additional": {"logType": "info", "children": [], "durationId": "75457080-9cd9-4b1e-beba-59061e07bb46"}}, {"head": {"id": "540c4fa1-ab2b-46f3-b519-f890ca5d600c", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012441985600, "endTime": 156012444631100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "037eb744-358e-4305-9b9a-676449eecaf0", "logId": "47c13ae8-b5a1-46e1-8f5d-e79596d00d12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "037eb744-358e-4305-9b9a-676449eecaf0", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012435251800}, "additional": {"logType": "detail", "children": [], "durationId": "540c4fa1-ab2b-46f3-b519-f890ca5d600c"}}, {"head": {"id": "3f944d27-86f8-40d1-b9a8-effc7c0b4814", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012436433000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "704123cb-cd25-499a-aca1-4007a3b7bd03", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012436565000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a25d8704-f697-4df0-bcd4-9a7986570096", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012442000200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d5bf7d6-782a-47df-9f40-9cf0faa79e16", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012444196900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e76c362-62f1-4454-b2ca-d00dc962912a", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012444350300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97802327-3964-40b5-81ba-794bee1b9652", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012444431900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bea0e7f6-c856-44e9-bb89-3113dfb2126a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012444472300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c71a728-9911-4fc7-af0d-e226853ad7c1", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11993408203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012444536100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f92b579-ff48-4665-942b-c814cb07ea0f", "name": "runTaskFromQueue task cost before running: 295 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012444594200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47c13ae8-b5a1-46e1-8f5d-e79596d00d12", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012441985600, "endTime": 156012444631100, "totalTime": 2600400}, "additional": {"logType": "info", "children": [], "durationId": "540c4fa1-ab2b-46f3-b519-f890ca5d600c"}}, {"head": {"id": "be1d4fba-b645-4d4f-84e5-49ae2d4e6b98", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012448865200, "endTime": 156012449207200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "72fe0546-77d3-4256-9fa8-eac1f0b910aa", "logId": "0b15e20c-f8ca-42ad-8918-2de3f49c5830"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72fe0546-77d3-4256-9fa8-eac1f0b910aa", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012446866000}, "additional": {"logType": "detail", "children": [], "durationId": "be1d4fba-b645-4d4f-84e5-49ae2d4e6b98"}}, {"head": {"id": "871d8879-b5e1-4050-a770-7047778a8c53", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012447915600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "798c2c6d-f801-4d70-9cd2-6f6443a1c799", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012448028900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4a6eec6-b206-4245-a8dd-2b347ae88faf", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012448873800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a2e023b-ea21-4b55-b7cb-a289383e98bf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012449006100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3eef13cd-2463-44eb-beed-9b320ec419e2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012449050700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cafc6b5c-4cb2-4490-821d-1716b530479e", "name": "entry : default@BuildNativeWithCmake cost memory 0.03824615478515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012449106100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e72527e1-8e20-4492-b55a-1c9a8e18e118", "name": "runTaskFromQueue task cost before running: 299 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012449168500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b15e20c-f8ca-42ad-8918-2de3f49c5830", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012448865200, "endTime": 156012449207200, "totalTime": 288100}, "additional": {"logType": "info", "children": [], "durationId": "be1d4fba-b645-4d4f-84e5-49ae2d4e6b98"}}, {"head": {"id": "dfeaba9a-2871-4584-8d79-3d96981ab754", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012452984000, "endTime": 156012458000300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9827aa1e-b5b7-43d9-acc8-bc81b807c396", "logId": "1ffc6125-cc69-40a1-aa20-c6d62c607fa7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9827aa1e-b5b7-43d9-acc8-bc81b807c396", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012450769200}, "additional": {"logType": "detail", "children": [], "durationId": "dfeaba9a-2871-4584-8d79-3d96981ab754"}}, {"head": {"id": "96cf6e64-f72f-4418-b7f9-5a627c6df53e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012452104300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e823dc21-207f-405e-8f52-d9d29a9e58d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012452224300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74587aca-3679-42c8-98f0-b65057db9d79", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012452993300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f352a6e0-68c8-4080-ad90-b22b15e8fd79", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012457768300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "029741e0-1948-481f-8e42-02478bfd86d1", "name": "entry : default@MakePackInfo cost memory 0.1628875732421875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012457929300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ffc6125-cc69-40a1-aa20-c6d62c607fa7", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012452984000, "endTime": 156012458000300}, "additional": {"logType": "info", "children": [], "durationId": "dfeaba9a-2871-4584-8d79-3d96981ab754"}}, {"head": {"id": "9335482e-de4d-4c2d-a291-6d87a85902ec", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012463001600, "endTime": 156012469210500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1704621d-27b9-4676-b791-2705f0891904", "logId": "15fd8d4d-3831-4a79-ac26-7df0b70289b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1704621d-27b9-4676-b791-2705f0891904", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012460307000}, "additional": {"logType": "detail", "children": [], "durationId": "9335482e-de4d-4c2d-a291-6d87a85902ec"}}, {"head": {"id": "d3864add-8b0f-4aee-80c8-3d87460e2101", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012461394800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cab648b-1b66-4c56-9361-cfbae01baa81", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012461508200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8e63bb6-a4ea-4863-b7a6-77b4a7b3d03b", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012463013300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbf7e3ca-bc23-42d2-90ca-ffcbb3efc102", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012463245500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de271758-b8f5-47b9-9217-8dc01eeef517", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012464113900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45697108-5eee-4762-8250-4629ccff1777", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012468835900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a2bbee1-5db9-476b-9756-ccd024869085", "name": "entry : default@SyscapTransform cost memory 0.1497039794921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012469064600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15fd8d4d-3831-4a79-ac26-7df0b70289b3", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012463001600, "endTime": 156012469210500}, "additional": {"logType": "info", "children": [], "durationId": "9335482e-de4d-4c2d-a291-6d87a85902ec"}}, {"head": {"id": "47a3a9ef-db9d-4bd2-a04d-f95453e1b42a", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012475447100, "endTime": 156012477453500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d6f57b6b-64c9-4400-8174-7287e27698ef", "logId": "c576ed4e-d87f-4405-8fea-907fdebf89f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6f57b6b-64c9-4400-8174-7287e27698ef", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012472777200}, "additional": {"logType": "detail", "children": [], "durationId": "47a3a9ef-db9d-4bd2-a04d-f95453e1b42a"}}, {"head": {"id": "e755e55e-c21b-4547-9dfc-cf11b161c640", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012474072900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41c0fe32-0a9b-4d6e-8839-35c84663dcba", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012474202700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08f3537c-913d-41e3-a412-a3f9ab546af9", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012475456600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0595d051-3931-4e71-9a7f-e1d1f3953c63", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012477270300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43cc6d3c-be0d-43a2-8535-f343e54862e4", "name": "entry : default@ProcessProfile cost memory 0.12361907958984375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012477383400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c576ed4e-d87f-4405-8fea-907fdebf89f2", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012475447100, "endTime": 156012477453500}, "additional": {"logType": "info", "children": [], "durationId": "47a3a9ef-db9d-4bd2-a04d-f95453e1b42a"}}, {"head": {"id": "1496a2ae-aaa8-412c-bbc8-6ea7f5c42399", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012482481400, "endTime": 156012493425300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "79149058-b638-405b-a45f-1a891600d5ae", "logId": "965ec0e7-b324-4b43-9b0a-3bac3c124508"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79149058-b638-405b-a45f-1a891600d5ae", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012479042700}, "additional": {"logType": "detail", "children": [], "durationId": "1496a2ae-aaa8-412c-bbc8-6ea7f5c42399"}}, {"head": {"id": "49b78a9f-7f21-4dd8-995b-822ae0ed9bf0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012480181200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "777f10e5-6d29-446d-bb54-5ef47ed5ae48", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012480293400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "078249c9-2f50-4cb2-8e55-9155cce8a7af", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012482494500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6e7994e-6684-414b-997e-33a8c7e0f8d6", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012493160800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86471e35-42ee-42bc-9544-15b45b82d0ec", "name": "entry : default@ProcessRouterMap cost memory 0.232177734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012493341300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "965ec0e7-b324-4b43-9b0a-3bac3c124508", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012482481400, "endTime": 156012493425300}, "additional": {"logType": "info", "children": [], "durationId": "1496a2ae-aaa8-412c-bbc8-6ea7f5c42399"}}, {"head": {"id": "8bae55f3-b0b8-4716-b505-c0e7955146d8", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012498812600, "endTime": 156012504626600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "69d39c3c-d46f-4b2f-a658-eca18ac8779a", "logId": "59c6dcfd-d57c-43ee-8396-19596364376f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69d39c3c-d46f-4b2f-a658-eca18ac8779a", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012497005400}, "additional": {"logType": "detail", "children": [], "durationId": "8bae55f3-b0b8-4716-b505-c0e7955146d8"}}, {"head": {"id": "ecdd66c7-8516-4034-81a2-a5dc7a0574b4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012498582600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2dacddc-1e37-45a3-a40e-341e6657f23f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012498707300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4e0df2e-2b02-418b-bf7e-284459b9afa7", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012498821400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0aa5529-073c-49f2-86af-809233d83811", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012498917400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adfc0dcd-c948-4100-9a4f-1d861115dbb6", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012502917400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e98ce96-c7a9-4359-9eb7-d3fdc14876c3", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012503088100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dea85a37-27bc-49a4-9e21-ea8c50d0233d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012503171800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "898ded37-768a-455a-b6f2-f706639bc95f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012503210400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e294042f-0112-4c8f-92fb-cab15a19a6c8", "name": "entry : default@ProcessStartupConfig cost memory 0.25699615478515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012504419900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "815b8683-e881-44ae-a3a7-bc1c3b5c865a", "name": "runTaskFromQueue task cost before running: 355 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012504569800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59c6dcfd-d57c-43ee-8396-19596364376f", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012498812600, "endTime": 156012504626600, "totalTime": 5731800}, "additional": {"logType": "info", "children": [], "durationId": "8bae55f3-b0b8-4716-b505-c0e7955146d8"}}, {"head": {"id": "1459509c-2491-47ff-b226-ed359c223745", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012510335900, "endTime": 156012511619400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c9538259-ba06-4f75-b63f-61566b56b930", "logId": "0c7e9f85-3a5f-4675-b0e3-1cfd5b315fa9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9538259-ba06-4f75-b63f-61566b56b930", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012508010200}, "additional": {"logType": "detail", "children": [], "durationId": "1459509c-2491-47ff-b226-ed359c223745"}}, {"head": {"id": "f3a66c23-910a-46c4-a95c-b583ad9e40d3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012509385200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0ab17fc-b0c5-4e9c-b75f-3f45f26ed170", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012509554100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6aa5c9e0-62cf-4969-863a-a50edf1085b4", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012510344200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50945880-6e05-426b-99c8-6adfecc7e4d4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012510470000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5d10a49-2f2e-47ae-8f76-81eeca37af92", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012510517100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f859dc91-246d-4e0b-974f-59b9d1e9a3fb", "name": "entry : default@BuildNativeWithNinja cost memory 0.0578155517578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012511429400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0b23171-501f-4ffb-b2d8-cd69d6c44ada", "name": "runTaskFromQueue task cost before running: 362 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012511561600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c7e9f85-3a5f-4675-b0e3-1cfd5b315fa9", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012510335900, "endTime": 156012511619400, "totalTime": 1204800}, "additional": {"logType": "info", "children": [], "durationId": "1459509c-2491-47ff-b226-ed359c223745"}}, {"head": {"id": "5a3c0702-96dc-4101-babf-e23b903aae11", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012517957000, "endTime": 156012524442600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ed3b16fc-fbb7-4aff-95ad-c3a3496a054f", "logId": "f415c27a-641f-4116-aae8-14e626bd41db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed3b16fc-fbb7-4aff-95ad-c3a3496a054f", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012514279000}, "additional": {"logType": "detail", "children": [], "durationId": "5a3c0702-96dc-4101-babf-e23b903aae11"}}, {"head": {"id": "0ec89eb7-e0e6-4a38-97b0-7026729f49b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012515320400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1934f6db-52dd-4c57-a017-d123fdde92a1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012515432400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cc7c23e-b1b2-44d8-abaa-586b07441dc7", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012516586800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08e3ccfc-d6e0-42f4-b5df-47543b94b2bd", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012519690500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf40e9b7-2be3-43d0-a546-1d852515d349", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012522447100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70ba5ba0-ef5e-49e9-b4e8-86bfea99475f", "name": "entry : default@ProcessResource cost memory 0.16095733642578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012522599700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f415c27a-641f-4116-aae8-14e626bd41db", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012517957000, "endTime": 156012524442600}, "additional": {"logType": "info", "children": [], "durationId": "5a3c0702-96dc-4101-babf-e23b903aae11"}}, {"head": {"id": "d478d6d8-6d64-491c-80ff-144fffcde7af", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012534187100, "endTime": 156012560234500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "efb3a545-c5e2-44d6-80d9-8d0262b90475", "logId": "d0efbfec-14c7-41bf-b020-a10b0881a7b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "efb3a545-c5e2-44d6-80d9-8d0262b90475", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012529305400}, "additional": {"logType": "detail", "children": [], "durationId": "d478d6d8-6d64-491c-80ff-144fffcde7af"}}, {"head": {"id": "3a5423e5-20c4-4b2f-9e8e-4fbfb0ad0b56", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012530652500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac350557-c5ce-45b2-b041-7e7dce1401c7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012530771400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e449d01-db76-47b1-857c-0bc9282b19fe", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012534202300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6feb8e4f-9691-4005-9d60-8ba2ae5d8529", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012559997000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82497760-1b3f-41e3-9a26-0d8c9df6b6f5", "name": "entry : default@GenerateLoaderJson cost memory 0.8739547729492188", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012560161000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0efbfec-14c7-41bf-b020-a10b0881a7b6", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012534187100, "endTime": 156012560234500}, "additional": {"logType": "info", "children": [], "durationId": "d478d6d8-6d64-491c-80ff-144fffcde7af"}}, {"head": {"id": "9ecd6b50-c6a7-4400-945d-9e6e8a93405b", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012573418200, "endTime": 156012578500500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "1405baf8-27aa-4181-891b-5d0c9fa07ba4", "logId": "fa884ebe-70d4-4a72-a210-3690b66364dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1405baf8-27aa-4181-891b-5d0c9fa07ba4", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012571299500}, "additional": {"logType": "detail", "children": [], "durationId": "9ecd6b50-c6a7-4400-945d-9e6e8a93405b"}}, {"head": {"id": "9810ac40-8b47-4a3e-945e-118c0b20eaf9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012572466600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "397af62d-713b-4064-8f14-76d6c0035948", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012572603600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "665a2f08-798e-4b77-a255-5c029cdd74d6", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012573430000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cf68a41-99ce-4458-a02d-a90d331120f2", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012578262000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60e9ebeb-8385-4990-a43f-6a751c74ba02", "name": "entry : default@ProcessLibs cost memory 0.1414337158203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012578418100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa884ebe-70d4-4a72-a210-3690b66364dd", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012573418200, "endTime": 156012578500500}, "additional": {"logType": "info", "children": [], "durationId": "9ecd6b50-c6a7-4400-945d-9e6e8a93405b"}}, {"head": {"id": "c4ff199d-c368-46b9-900f-a70c0e9805d0", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012587748900, "endTime": 156012615194000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4640a800-0c9f-4f80-a838-f554dae69c61", "logId": "21a1b65e-dca8-413c-b270-c08e9b4f0658"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4640a800-0c9f-4f80-a838-f554dae69c61", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012582324000}, "additional": {"logType": "detail", "children": [], "durationId": "c4ff199d-c368-46b9-900f-a70c0e9805d0"}}, {"head": {"id": "0feb0e63-4f35-45a0-94ef-bc4e61e970dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012583608000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00d2fab3-7c85-43b4-9567-dae778024b2a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012583744700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76ab669a-2b4a-426d-bc75-1882dc6f9693", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012584782100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e2c86c3-624e-4f47-b5cc-229ff9cba3a4", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012587780000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec752764-5f25-42cc-a939-21a1c603d4ae", "name": "Incremental task entry:default@CompileResource pre-execution cost: 26 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012614926100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af0de5f8-0456-4b25-969b-0648926c4dbc", "name": "entry : default@CompileResource cost memory 1.31463623046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012615111600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a1b65e-dca8-413c-b270-c08e9b4f0658", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012587748900, "endTime": 156012615194000}, "additional": {"logType": "info", "children": [], "durationId": "c4ff199d-c368-46b9-900f-a70c0e9805d0"}}, {"head": {"id": "e025fd63-dd60-4282-a09e-4bb38ac6827f", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012626093700, "endTime": 156012630994100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2b5f1ca0-277a-4f49-83e3-ea086dd52893", "logId": "120592f7-8e11-4090-a1ba-3361322725c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b5f1ca0-277a-4f49-83e3-ea086dd52893", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012618204500}, "additional": {"logType": "detail", "children": [], "durationId": "e025fd63-dd60-4282-a09e-4bb38ac6827f"}}, {"head": {"id": "c5a0761b-2859-48a4-92d6-d1d613749703", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012619989900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58c7af18-ee85-4e1b-85a1-8f9fce70c0db", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012620152600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26cda421-2a9b-4986-b3ea-f098368c56a7", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012626111200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7636bfd9-f4a1-44cc-a7dd-f9f6208de8c4", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012626940000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0524f635-7b85-4f3b-9f58-a5a9d0a7426a", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012630627000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bc320d0-704f-422e-99f5-071311c68500", "name": "entry : default@DoNativeStrip cost memory 0.07921600341796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012630858600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "120592f7-8e11-4090-a1ba-3361322725c1", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012626093700, "endTime": 156012630994100}, "additional": {"logType": "info", "children": [], "durationId": "e025fd63-dd60-4282-a09e-4bb38ac6827f"}}, {"head": {"id": "f434d6a6-ea63-4b04-9236-0ffad948df68", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012645282900, "endTime": 156023143566200}, "additional": {"children": ["133a73d7-4861-43cb-8bdf-d24e9f1646e1", "fa5cfbb7-65ec-42c7-a9f1-aefd2a42b7eb"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "6a5ba9f7-dec7-4c0f-9875-2bb1e1c85cd1", "logId": "18d459df-e58c-4ece-9b30-ad1ad37053dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a5ba9f7-dec7-4c0f-9875-2bb1e1c85cd1", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012633901500}, "additional": {"logType": "detail", "children": [], "durationId": "f434d6a6-ea63-4b04-9236-0ffad948df68"}}, {"head": {"id": "a70b8530-7b1f-47a8-95a4-36723289d930", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012636445800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0771823c-35bd-4ab5-ac42-928fe3024dcc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012636652100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31497d40-3186-400c-aaa7-7b6c6c4e8e4a", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012645301100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8678ffc3-d807-4a34-9c57-e6dc15c5ce2d", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012645548400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bacc0a4a-30d2-42bd-a6ea-a9b29f9700d5", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012687734700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51bc0d24-c291-4c7d-af38-990b6b4c4fd8", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 30 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012688138400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3f57f05-dc14-4754-ba13-b1d190472123", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012710825800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e41a1aa-45fc-4e48-b505-4b986f0ed512", "name": "default@CompileArkTS work[20] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012712898000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "133a73d7-4861-43cb-8bdf-d24e9f1646e1", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 156012715745700, "endTime": 156023143329100}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f434d6a6-ea63-4b04-9236-0ffad948df68", "logId": "656424e9-572f-41ac-aadb-78e30d5a4026"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f78d32fe-0097-4a94-b141-833d507f54e4", "name": "default@CompileArkTS work[20] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714074300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "375750a6-9546-4cec-ac0e-b5cb225f35e2", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714176600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b29f8c0a-cdc4-48e5-aee1-ff74d9c07fc1", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714217600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1748d51b-48a0-4773-880f-ab1d867e9cce", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714247400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86476ddd-faf3-4717-9ae3-81e6cd10f599", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714272500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcdcb778-15fb-4ace-8b3c-2f9780bc1ab5", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714295500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ad98daa-3bfb-4438-8185-b5fd07a4c30a", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714321500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8dbb1e9-712d-4a19-beac-377fe05e703b", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714345500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25f75dd6-ff4b-4d4f-a989-b867c43cf008", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714369100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d02d647b-9569-489a-acc9-5d078f1dd58f", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714394200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35074ba3-0bbc-4527-9132-e7038a51d454", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714415800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c9a999e-ef88-4c30-846d-c8a87f2f70c0", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714438900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0adbeb07-ee43-4850-ab19-4fb1f9a3456c", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714461600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a00bba8-e25b-492f-a450-7d2bc2aadd99", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714485300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e01fb35-95bc-4b02-9f18-c3162a2ad7e4", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714507900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3557d680-12f8-46d7-9d95-84efc4a76cdb", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714530300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be97ec24-f4fa-403d-ac28-160493846022", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012714637500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed61ae27-8583-4e92-9723-1a3faaa87062", "name": "default@CompileArkTS work[20] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012715781500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "300eb864-8367-4a9f-9cbc-28d884aa282d", "name": "default@CompileArkTS work[20] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012715949100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41479323-5dbe-48ec-806c-dab56b6bc7d8", "name": "CopyResources startTime: 156012716013000", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012716015600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0152cf73-217d-4cc5-87c8-fd3b19f2326a", "name": "default@CompileArkTS work[21] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012716118500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa5cfbb7-65ec-42c7-a9f1-aefd2a42b7eb", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 156013843646900, "endTime": 156013860452100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f434d6a6-ea63-4b04-9236-0ffad948df68", "logId": "06e50085-4f25-4edb-8e32-a7cea9786a15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5ac816d-b52c-4a64-b37e-4dc3fe29216d", "name": "default@CompileArkTS work[21] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012717213900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "747031bb-6148-46d8-9152-ab84cfd8eaa9", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012717339200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93797f7b-2f47-4244-bc7c-b611cea414ec", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012717405800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0a293e8-d1f8-483e-96c2-e66eca482039", "name": "default@CompileArkTS work[21] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012718424900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "335c5a46-c586-487c-8c68-5f773c706484", "name": "default@CompileArkTS work[21] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012718528800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd3aabf6-31db-4741-b093-83f566dbe883", "name": "entry : default@CompileArkTS cost memory 2.5143814086914062", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012718873900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96a36026-130f-4843-af21-b315782b91c3", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012727435700, "endTime": 156012735769100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "1cc0b703-6f8a-4cd6-b462-f1ad0ef23ad0", "logId": "3d73681e-c665-4a25-b756-bf4b43a6b3a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1cc0b703-6f8a-4cd6-b462-f1ad0ef23ad0", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012721314300}, "additional": {"logType": "detail", "children": [], "durationId": "96a36026-130f-4843-af21-b315782b91c3"}}, {"head": {"id": "0fc6b7c3-5939-4f3e-800e-89e750b74d3c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012722801700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a6edef6-5cde-41b3-a851-b4a245c7d78d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012722962300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aec1a87c-2570-4f2a-9a3f-193a755ab280", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012727447300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "750e8e0d-1af5-4096-bb07-be20340d51fc", "name": "entry : default@BuildJS cost memory 0.342132568359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012735431100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74203c71-e5b7-4251-a1e4-a46e81979f2e", "name": "runTaskFromQueue task cost before running: 586 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012735659700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d73681e-c665-4a25-b756-bf4b43a6b3a3", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012727435700, "endTime": 156012735769100, "totalTime": 8185600}, "additional": {"logType": "info", "children": [], "durationId": "96a36026-130f-4843-af21-b315782b91c3"}}, {"head": {"id": "66faceb9-45dc-4d40-ad69-38dba1560343", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012742041600, "endTime": 156012746539600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5ee24b8b-3c31-4038-84f5-f48a54a3c32b", "logId": "f4f51910-bdca-42ac-afa1-ae19dcbe0a32"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ee24b8b-3c31-4038-84f5-f48a54a3c32b", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012738155000}, "additional": {"logType": "detail", "children": [], "durationId": "66faceb9-45dc-4d40-ad69-38dba1560343"}}, {"head": {"id": "51d4acf6-65d6-4faf-a5f8-634f57865949", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012739358000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dc901e4-b08d-46a5-98c3-a6c8dd5cf288", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012739479100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bed4b5b-c784-41f5-8633-544c083ced0c", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012742053100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2304d4a1-9bc1-4a9a-b8d1-3fc58ba7a247", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012742968700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89e321f7-f32d-4c23-b26e-e6ce93cc4134", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012746184500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55672c3a-3495-4c57-9ab4-5912c5983d0d", "name": "entry : default@CacheNativeLibs cost memory 0.0944366455078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012746407700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4f51910-bdca-42ac-afa1-ae19dcbe0a32", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012742041600, "endTime": 156012746539600}, "additional": {"logType": "info", "children": [], "durationId": "66faceb9-45dc-4d40-ad69-38dba1560343"}}, {"head": {"id": "2e518c90-6cb3-4c67-b719-addb90f55132", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156013860915100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73e1c6b4-3f1d-49c8-be1f-c061e934224c", "name": "CopyResources is end, endTime: 156013861137500", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156013861142600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03315d70-ed33-466b-93eb-da4851bdcc8c", "name": "default@CompileArkTS work[21] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156013861326600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06e50085-4f25-4edb-8e32-a7cea9786a15", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 156013843646900, "endTime": 156013860452100}, "additional": {"logType": "info", "children": [], "durationId": "fa5cfbb7-65ec-42c7-a9f1-aefd2a42b7eb", "parent": "18d459df-e58c-4ece-9b30-ad1ad37053dc"}}, {"head": {"id": "fe8eca91-4fd7-4b45-9027-bfcec9ba29f4", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156013861417100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f9bd95b-599c-4c0c-8d84-c4b14a23f13a", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023143113200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c293aa45-f268-494c-b485-c19b8f3f7445", "name": "default@CompileArkTS work[20] failed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023143426100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "656424e9-572f-41ac-aadb-78e30d5a4026", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 156012715745700, "endTime": 156023143329100}, "additional": {"logType": "error", "children": [], "durationId": "133a73d7-4861-43cb-8bdf-d24e9f1646e1", "parent": "18d459df-e58c-4ece-9b30-ad1ad37053dc"}}, {"head": {"id": "18d459df-e58c-4ece-9b30-ad1ad37053dc", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012645282900, "endTime": 156023143566200}, "additional": {"logType": "error", "children": ["656424e9-572f-41ac-aadb-78e30d5a4026", "06e50085-4f25-4edb-8e32-a7cea9786a15"], "durationId": "f434d6a6-ea63-4b04-9236-0ffad948df68"}}, {"head": {"id": "6e81a6c8-6fd2-4bfd-80a6-fa0f97f898e1", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023143711500}, "additional": {"logType": "debug", "children": [], "durationId": "f434d6a6-ea63-4b04-9236-0ffad948df68"}}, {"head": {"id": "2181d3a2-0c7f-4244-bb2c-eb5d39e923c8", "name": "ERROR: stacktrace = Error: ArkTS Compiler Error\r\n\u001b[33m1 WARN: \u001b[33m\u001b[33mArkTS:WARN: For details about ArkTS syntax errors, see FAQs\u001b[39m\u001b[39m\r\n\u001b[31m1 ERROR: \u001b[31m10605087 ArkTS Compiler Error\r\nError Message: \"throw\" statements cannot accept values of arbitrary types (arkts-limited-throw) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/services/FirstLaunchService.ets:40:7\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m2 ERROR: \u001b[31m10605087 ArkTS Compiler Error\r\nError Message: \"throw\" statements cannot accept values of arbitrary types (arkts-limited-throw) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/services/FirstLaunchService.ets:95:7\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m3 ERROR: \u001b[31m10605087 ArkTS Compiler Error\r\nError Message: \"throw\" statements cannot accept values of arbitrary types (arkts-limited-throw) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/services/FirstLaunchService.ets:121:7\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m4 ERROR: \u001b[31m10605040 ArkTS Compiler Error\r\nError Message: Object literals cannot be used as type declarations (arkts-no-obj-literals-as-types) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/services/FirstLaunchService.ets:128:46\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m5 ERROR: \u001b[31m10605038 ArkTS Compiler Error\r\nError Message: Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/services/FirstLaunchService.ets:136:14\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m6 ERROR: \u001b[31m10605038 ArkTS Compiler Error\r\nError Message: Object literal must correspond to some explicitly declared class or interface (arkts-no-untyped-obj-literals) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/services/FirstLaunchService.ets:143:14\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:7 WARN:1}\u001b[39m\n    at runArkPack (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5479)", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023144137200}, "additional": {"logType": "debug", "children": [], "durationId": "f434d6a6-ea63-4b04-9236-0ffad948df68"}}, {"head": {"id": "a92fdcb2-9890-4bd6-b7c1-86afaacb3e57", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023157076300, "endTime": 156023157297800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "39d5fc2a-2930-4703-b4ef-dfbcf8b5a711", "logId": "d625c719-fa96-4d5b-8eae-5f4c66e34d94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d625c719-fa96-4d5b-8eae-5f4c66e34d94", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023157076300, "endTime": 156023157297800}, "additional": {"logType": "info", "children": [], "durationId": "a92fdcb2-9890-4bd6-b7c1-86afaacb3e57"}}, {"head": {"id": "5d6ddc86-495d-470c-9e8a-b22fc95af64d", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156012150272300, "endTime": 156023157702900}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 17, "second": 32}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "fd9ac203-2930-454f-acea-c9ac03fdaea4", "name": "BUILD FAILED in 11 s 8 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023157748200}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "a99a6252-1a0e-4582-9717-8ecbf426d6ca", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023158006400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa10670c-f893-415e-ba98-1f53d23c482c", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023158072100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "478fb0c7-2225-4af5-b5da-e3d7d54e436e", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023158477300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34fc7e88-0059-48fd-817e-3affa2216936", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023158541500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2333ae2a-ec5c-4273-8dbe-af589afbe0ff", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023158577700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8723ddce-c082-4be4-a1e1-cd8bcda9d650", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023158606500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd95b2c1-4f90-4a86-b4e2-1d70a9e084ce", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023158651800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5522e29-472e-42c9-b280-fc2f0f0d0aa0", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023159214200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4013dec3-ec01-4a65-9ce6-c62923900457", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023159460400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f33f9d39-fae6-4bc1-8f82-db5adba7d762", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023159523000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d9bb3f0-8120-4819-bc59-381dcf5fa333", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023159564400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f9b5882-e10d-4b04-aad6-3c486dcb0d9f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023159597600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f33b7b8e-d788-425f-815c-91f88555db96", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023159630700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81b49e03-fdc7-407b-a529-d405e4fdbef4", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023160718000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d948e868-f848-49f5-8435-704842e97bf4", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023161134700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daf2a8cd-497e-46f9-8319-1fa5fc3bceb9", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023161395400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b116190-123a-4e43-8b12-bb508e1aa74d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023161466000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c146849-c6fd-4e57-8a33-db875dcb1393", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023161518500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "768d1de8-d9cd-46f9-b176-79d10589f49d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023161557800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f3c1d55-1cb9-4135-85c8-931903f8a036", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023161592300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72321860-0def-4000-a162-937a2dc41046", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023161625200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d90874e4-1d4e-49c1-8f2b-6c4e1040c958", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023165022500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaa3aa9f-a9ae-4c1d-9a76-f241a6cb071e", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023165829100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3416aff2-8d48-4db0-bfef-435efcd3631f", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023166233500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2d41438-fc0e-4340-8dd0-e4fb6ea0dcb1", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023166475300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3463176a-a671-430a-a2f3-cfde26eb84be", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023166697100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae07b5ff-6bf9-4d88-a7d4-b8debe33326f", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023167487400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b4e7e53-dd1e-4013-b4ca-cee56d2e870c", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023167558600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efbb3cee-a5ad-4acc-83c3-24feafa38f33", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023167781700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2dde73a-b526-4bd7-baaf-d5a5b0e5e859", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023168133700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d747ee31-03eb-4bc1-b21e-fbe9b9306035", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023168974000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16c861f9-d7e0-4d3f-a47a-d550defab36c", "name": "Incremental task entry:default@CompileArkTS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023169596100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70c2da48-4432-4df8-9f9d-aa77288d654d", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023171602900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d9ea45a-1127-49e6-a6d9-f0f27420439c", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023172278700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8022a79c-413b-47c1-b415-8fd32b397163", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023172670000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7e9cbcb-0c6b-43aa-9103-14d9db0752ab", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023172917200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb95b320-ce13-4664-9ef9-33a549196360", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023173139100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d60c7e28-7de5-4cf6-9bfc-b0bbe9e5d010", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023174071000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea8a34a9-c726-4269-a83a-dc88ef983211", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023174940000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "730d71ad-2316-42ca-b816-f8bbe2a684d4", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023175299200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6166780-8fa0-4b8e-8b02-6e428273171c", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023175370400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}