{"version": "2.0", "ppid": 29880, "events": [{"head": {"id": "16e2f13c-e93b-4219-aff0-67712a6b87fd", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216756336600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b614122-d8f3-492c-b1e2-2200fd53b017", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216766187300, "endTime": 150219430589500}, "additional": {"children": ["ea84f72a-a148-40bc-9133-606b6cf2e050", "895fbe7e-81dd-4ea0-86be-4dde6f5c7607", "fca2b25b-ef90-46e5-bc83-9e2b09c83d63", "992b2339-ca43-4e2d-91e6-c5fed3cddbf0", "162a30fa-c298-464b-8b71-76f7d8560260", "0b824888-5959-4c47-bb80-d0a10ed26537", "6777c22f-5d8d-4419-b4cf-a2c74a1bb77a"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "4a016922-0927-4d77-9c13-2b7cac0c16d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea84f72a-a148-40bc-9133-606b6cf2e050", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216766191100, "endTime": 150216787468400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b614122-d8f3-492c-b1e2-2200fd53b017", "logId": "bb05273d-776c-4171-b015-90e4ef119d76"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "895fbe7e-81dd-4ea0-86be-4dde6f5c7607", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216787490500, "endTime": 150219428341200}, "additional": {"children": ["08c72df4-0f8a-4c10-a017-b630aebb63f7", "032065f1-819b-449a-885c-33036c2a23f1", "f14de174-a613-4e7b-912c-3274b2b1f85c", "14feea81-4122-4d23-adaa-d9aabe75453b", "f8ee5780-2c2f-4a58-a358-23881b75ac44", "9d072c30-8cfd-4c1f-9178-57ce16cf0350", "79a10b9b-cea5-42bc-828b-0a38c63eb67e", "cf3505b4-54c3-433b-8b93-91dd8e3b8ce1", "cf009c64-411e-428a-b916-20632fc47a48"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b614122-d8f3-492c-b1e2-2200fd53b017", "logId": "050d605f-827e-43e5-beba-a32b6e562242"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fca2b25b-ef90-46e5-bc83-9e2b09c83d63", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219428369900, "endTime": 150219430558300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b614122-d8f3-492c-b1e2-2200fd53b017", "logId": "1af31f7c-b361-42bf-9059-a6e3e3bc50de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "992b2339-ca43-4e2d-91e6-c5fed3cddbf0", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219430564000, "endTime": 150219430581900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b614122-d8f3-492c-b1e2-2200fd53b017", "logId": "9ba0151e-225e-4e76-98c6-a3799e19f155"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "162a30fa-c298-464b-8b71-76f7d8560260", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216772750300, "endTime": 150216773057000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b614122-d8f3-492c-b1e2-2200fd53b017", "logId": "7e50f7d8-e7c6-4178-bdc2-e4122e454a5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e50f7d8-e7c6-4178-bdc2-e4122e454a5a", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216772750300, "endTime": 150216773057000}, "additional": {"logType": "info", "children": [], "durationId": "162a30fa-c298-464b-8b71-76f7d8560260", "parent": "4a016922-0927-4d77-9c13-2b7cac0c16d9"}}, {"head": {"id": "0b824888-5959-4c47-bb80-d0a10ed26537", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216780634300, "endTime": 150216780692200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b614122-d8f3-492c-b1e2-2200fd53b017", "logId": "7494a6f4-edb8-4d77-acbc-f39dc5e21062"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7494a6f4-edb8-4d77-acbc-f39dc5e21062", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216780634300, "endTime": 150216780692200}, "additional": {"logType": "info", "children": [], "durationId": "0b824888-5959-4c47-bb80-d0a10ed26537", "parent": "4a016922-0927-4d77-9c13-2b7cac0c16d9"}}, {"head": {"id": "78661b5c-498d-42a4-ba5f-0ddb815e4a31", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216781232700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c4528bd-7a5b-4d79-9ae7-0ccfa84076dd", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216787315200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb05273d-776c-4171-b015-90e4ef119d76", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216766191100, "endTime": 150216787468400}, "additional": {"logType": "info", "children": [], "durationId": "ea84f72a-a148-40bc-9133-606b6cf2e050", "parent": "4a016922-0927-4d77-9c13-2b7cac0c16d9"}}, {"head": {"id": "08c72df4-0f8a-4c10-a017-b630aebb63f7", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216792893900, "endTime": 150216792944500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "895fbe7e-81dd-4ea0-86be-4dde6f5c7607", "logId": "f900009b-9d3c-4468-ad3e-7cdf08a648ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "032065f1-819b-449a-885c-33036c2a23f1", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216792988500, "endTime": 150216797500200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "895fbe7e-81dd-4ea0-86be-4dde6f5c7607", "logId": "bdf91910-7eb1-4fe9-b53b-3489035d2db4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f14de174-a613-4e7b-912c-3274b2b1f85c", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216797620000, "endTime": 150219273830400}, "additional": {"children": ["2b95a017-8932-4737-a447-4ba80c9c4b36", "2db027a8-e0f3-4b11-bb67-b254d4347f6b", "18cddc8e-8cb8-412f-87bd-591107d7cbf7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "895fbe7e-81dd-4ea0-86be-4dde6f5c7607", "logId": "1fede141-39c1-442f-902a-cc963edcddb5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14feea81-4122-4d23-adaa-d9aabe75453b", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219273966200, "endTime": 150219315157000}, "additional": {"children": ["ea48014c-8fd3-4aad-a1dc-46b3461905d6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "895fbe7e-81dd-4ea0-86be-4dde6f5c7607", "logId": "6f203a02-df73-4c8a-a552-c7680bf6097b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8ee5780-2c2f-4a58-a358-23881b75ac44", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219315218700, "endTime": 150219382105400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "895fbe7e-81dd-4ea0-86be-4dde6f5c7607", "logId": "8257bf66-529e-41bd-943a-72e7b81419e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d072c30-8cfd-4c1f-9178-57ce16cf0350", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219383756200, "endTime": 150219398869500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "895fbe7e-81dd-4ea0-86be-4dde6f5c7607", "logId": "8b5d14ee-3748-4d94-8b97-01f07484125c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79a10b9b-cea5-42bc-828b-0a38c63eb67e", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219398898700, "endTime": 150219427967800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "895fbe7e-81dd-4ea0-86be-4dde6f5c7607", "logId": "d0c845b8-0a7d-4237-9720-7b6821761a26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf3505b4-54c3-433b-8b93-91dd8e3b8ce1", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219427994500, "endTime": 150219428326100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "895fbe7e-81dd-4ea0-86be-4dde6f5c7607", "logId": "49ce8260-e03e-45d8-a1d3-93f79fce95e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f900009b-9d3c-4468-ad3e-7cdf08a648ed", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216792893900, "endTime": 150216792944500}, "additional": {"logType": "info", "children": [], "durationId": "08c72df4-0f8a-4c10-a017-b630aebb63f7", "parent": "050d605f-827e-43e5-beba-a32b6e562242"}}, {"head": {"id": "bdf91910-7eb1-4fe9-b53b-3489035d2db4", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216792988500, "endTime": 150216797500200}, "additional": {"logType": "info", "children": [], "durationId": "032065f1-819b-449a-885c-33036c2a23f1", "parent": "050d605f-827e-43e5-beba-a32b6e562242"}}, {"head": {"id": "2b95a017-8932-4737-a447-4ba80c9c4b36", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216798412700, "endTime": 150216798465500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f14de174-a613-4e7b-912c-3274b2b1f85c", "logId": "95059025-3cd3-41c7-b73e-747e40be3859"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95059025-3cd3-41c7-b73e-747e40be3859", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216798412700, "endTime": 150216798465500}, "additional": {"logType": "info", "children": [], "durationId": "2b95a017-8932-4737-a447-4ba80c9c4b36", "parent": "1fede141-39c1-442f-902a-cc963edcddb5"}}, {"head": {"id": "2db027a8-e0f3-4b11-bb67-b254d4347f6b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216800750500, "endTime": 150219272920200}, "additional": {"children": ["16315b7a-3392-4689-a5ce-856629f9ff85", "88ecf826-75cd-4e24-bcf2-6a8d0a27dd4e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f14de174-a613-4e7b-912c-3274b2b1f85c", "logId": "11a17974-d10c-4638-ab25-474ebf4516e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16315b7a-3392-4689-a5ce-856629f9ff85", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216800752000, "endTime": 150219006108500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2db027a8-e0f3-4b11-bb67-b254d4347f6b", "logId": "f06413bd-f860-471a-86fb-e4f50901fef3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88ecf826-75cd-4e24-bcf2-6a8d0a27dd4e", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219006133600, "endTime": 150219272904500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2db027a8-e0f3-4b11-bb67-b254d4347f6b", "logId": "befadf39-dbe9-4a47-a22e-1e8f6f8dbaaf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c98473d2-c476-49f2-8b1a-651a10a04866", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216800759200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fec1ceac-9f39-4c2a-bf4b-b356edb46611", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219005947900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f06413bd-f860-471a-86fb-e4f50901fef3", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216800752000, "endTime": 150219006108500}, "additional": {"logType": "info", "children": [], "durationId": "16315b7a-3392-4689-a5ce-856629f9ff85", "parent": "11a17974-d10c-4638-ab25-474ebf4516e8"}}, {"head": {"id": "26242e00-2321-4256-9f41-8164187b5566", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219006257500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f0fdad5-659c-4016-a4ce-b6cccb2e58d8", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219188865800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4603bee-afee-4a43-91a1-00627543ea38", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219189112200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9638b06e-7513-4a4b-8b69-b8298b017cc6", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219189705800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d18f3ea2-9afb-4850-8d0f-c72cdebf250d", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219189839300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3407c90a-5cdd-4422-8270-efbb6c59e34e", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219194146800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72d5e8a3-759b-4702-a528-daa75628a846", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219217649700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aef5ee11-9c75-4285-917b-4deaf165947f", "name": "Sdk init in 35 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219241108600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35ba93dd-3ecf-4825-8321-880719933dab", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219241422100}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 23, "minute": 40, "second": 49}, "markType": "other"}}, {"head": {"id": "cc435abf-567c-44f3-8525-b90854ffccb5", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219241563600}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 23, "minute": 40, "second": 49}, "markType": "other"}}, {"head": {"id": "38d96e93-4e6a-4669-8402-09059f47a9b1", "name": "Project task initialization takes 24 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219272322500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a0e8954-7855-4e21-816f-eb4795649631", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219272702800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3045d7dd-7a0c-4052-93a0-9a283a8a584e", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219272792400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5de51448-c208-43db-9af3-34ec0a864499", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219272840400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "befadf39-dbe9-4a47-a22e-1e8f6f8dbaaf", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219006133600, "endTime": 150219272904500}, "additional": {"logType": "info", "children": [], "durationId": "88ecf826-75cd-4e24-bcf2-6a8d0a27dd4e", "parent": "11a17974-d10c-4638-ab25-474ebf4516e8"}}, {"head": {"id": "11a17974-d10c-4638-ab25-474ebf4516e8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216800750500, "endTime": 150219272920200}, "additional": {"logType": "info", "children": ["f06413bd-f860-471a-86fb-e4f50901fef3", "befadf39-dbe9-4a47-a22e-1e8f6f8dbaaf"], "durationId": "2db027a8-e0f3-4b11-bb67-b254d4347f6b", "parent": "1fede141-39c1-442f-902a-cc963edcddb5"}}, {"head": {"id": "18cddc8e-8cb8-412f-87bd-591107d7cbf7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219273756200, "endTime": 150219273806600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f14de174-a613-4e7b-912c-3274b2b1f85c", "logId": "8428603a-0500-495a-b8fa-a1d664ee8738"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8428603a-0500-495a-b8fa-a1d664ee8738", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219273756200, "endTime": 150219273806600}, "additional": {"logType": "info", "children": [], "durationId": "18cddc8e-8cb8-412f-87bd-591107d7cbf7", "parent": "1fede141-39c1-442f-902a-cc963edcddb5"}}, {"head": {"id": "1fede141-39c1-442f-902a-cc963edcddb5", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216797620000, "endTime": 150219273830400}, "additional": {"logType": "info", "children": ["95059025-3cd3-41c7-b73e-747e40be3859", "11a17974-d10c-4638-ab25-474ebf4516e8", "8428603a-0500-495a-b8fa-a1d664ee8738"], "durationId": "f14de174-a613-4e7b-912c-3274b2b1f85c", "parent": "050d605f-827e-43e5-beba-a32b6e562242"}}, {"head": {"id": "ea48014c-8fd3-4aad-a1dc-46b3461905d6", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219274724900, "endTime": 150219315127300}, "additional": {"children": ["0c9db3a0-cd6b-43c4-a932-7d4ac58a329b", "56247e14-e911-404b-ba19-14de403789d6", "f08ca90f-488d-426a-8b55-875d1b601004"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "14feea81-4122-4d23-adaa-d9aabe75453b", "logId": "10da9c8c-e651-448d-a1aa-003b7ad270f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c9db3a0-cd6b-43c4-a932-7d4ac58a329b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219277587700, "endTime": 150219277610000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea48014c-8fd3-4aad-a1dc-46b3461905d6", "logId": "dc3a7060-53ac-4432-9060-fc25921a540b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc3a7060-53ac-4432-9060-fc25921a540b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219277587700, "endTime": 150219277610000}, "additional": {"logType": "info", "children": [], "durationId": "0c9db3a0-cd6b-43c4-a932-7d4ac58a329b", "parent": "10da9c8c-e651-448d-a1aa-003b7ad270f0"}}, {"head": {"id": "56247e14-e911-404b-ba19-14de403789d6", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219279543800, "endTime": 150219313359000}, "additional": {"children": ["666b6c4f-ce6f-488f-a198-fe51dd889742", "5b90c391-bcae-45df-9b0c-92a2f9eccbd8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea48014c-8fd3-4aad-a1dc-46b3461905d6", "logId": "3bff13b7-8058-4af8-aea5-31906908ca71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "666b6c4f-ce6f-488f-a198-fe51dd889742", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219279544800, "endTime": 150219286407500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "56247e14-e911-404b-ba19-14de403789d6", "logId": "c845dcf4-6967-4162-94fc-8de78188b97a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b90c391-bcae-45df-9b0c-92a2f9eccbd8", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219286429800, "endTime": 150219313348900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "56247e14-e911-404b-ba19-14de403789d6", "logId": "0db090a3-52c6-4fb2-8f84-b01afa6b8830"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e8b89e6-658f-4eb2-a732-d7fcf5193632", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219279551600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "939e2951-981f-49c6-b5be-097a9879ae53", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219286263700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c845dcf4-6967-4162-94fc-8de78188b97a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219279544800, "endTime": 150219286407500}, "additional": {"logType": "info", "children": [], "durationId": "666b6c4f-ce6f-488f-a198-fe51dd889742", "parent": "3bff13b7-8058-4af8-aea5-31906908ca71"}}, {"head": {"id": "262f4aca-e388-4eb4-8470-175b89d1dc3c", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219286551500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ec76e88-c969-41b1-926f-15dc4eb65d22", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219301728200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48d255fa-ad57-4940-b032-90cbac5b2d92", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219302407900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a60ecc3-6daa-4b6e-9cd0-210b05651ef5", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219303229300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7aa2c56-6bf6-494c-9fde-e3480b49639d", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219303580000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89e1caad-27a3-4242-a502-1c2a512fdcb3", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219303676200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73298c7d-6820-4213-b770-d846ac9bb464", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219303741200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c5fd544-cc51-4c60-9fb2-4dcb919bd48a", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219303850800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5cc5007-5b66-4c2e-bc0c-0a63f1906381", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219303941800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98653fa2-d40a-42b0-aad6-6615f7650219", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219304304900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3e454b7-d5eb-47df-8c3c-c8f99f96bcbf", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219304449400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de9ced5b-2e71-4f54-af4b-ff87dbc09b83", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219304507700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88d9af85-d054-483a-babe-7011c666ba07", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219304543600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51f64ff1-f2c6-4421-9e6e-a3c06e5ce329", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219304714600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ad87950-2c80-4dd5-9848-a3dbe5b40fcb", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219304798500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5363f702-f40f-4307-9a3a-fd346e0270d2", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219305029400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89b562fa-bef9-4448-957b-ab544eab70dc", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219305135000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "974df784-0d98-40eb-a1fc-cd1a246bead2", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219305172900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9a94fb9-5ece-4efb-9ede-248e83a4c9e6", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219305343400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3b9b86f-9105-4386-986e-40e3522d138e", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219305620200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5682d747-b293-4aef-91b0-98f18820916d", "name": "Module entry task initialization takes 5 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219312780000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "521f42f7-f778-47d2-a8a4-caeea8c6b269", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219313090200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06958311-c228-476c-9d09-59737a7fbd11", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219313234600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9532d0b9-d71c-44cb-9209-4e5a653205f9", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219313309900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0db090a3-52c6-4fb2-8f84-b01afa6b8830", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219286429800, "endTime": 150219313348900}, "additional": {"logType": "info", "children": [], "durationId": "5b90c391-bcae-45df-9b0c-92a2f9eccbd8", "parent": "3bff13b7-8058-4af8-aea5-31906908ca71"}}, {"head": {"id": "3bff13b7-8058-4af8-aea5-31906908ca71", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219279543800, "endTime": 150219313359000}, "additional": {"logType": "info", "children": ["c845dcf4-6967-4162-94fc-8de78188b97a", "0db090a3-52c6-4fb2-8f84-b01afa6b8830"], "durationId": "56247e14-e911-404b-ba19-14de403789d6", "parent": "10da9c8c-e651-448d-a1aa-003b7ad270f0"}}, {"head": {"id": "f08ca90f-488d-426a-8b55-875d1b601004", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219315093600, "endTime": 150219315110300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea48014c-8fd3-4aad-a1dc-46b3461905d6", "logId": "07c6b9d0-8190-4775-a263-cf9aac9fa8ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07c6b9d0-8190-4775-a263-cf9aac9fa8ac", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219315093600, "endTime": 150219315110300}, "additional": {"logType": "info", "children": [], "durationId": "f08ca90f-488d-426a-8b55-875d1b601004", "parent": "10da9c8c-e651-448d-a1aa-003b7ad270f0"}}, {"head": {"id": "10da9c8c-e651-448d-a1aa-003b7ad270f0", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219274724900, "endTime": 150219315127300}, "additional": {"logType": "info", "children": ["dc3a7060-53ac-4432-9060-fc25921a540b", "3bff13b7-8058-4af8-aea5-31906908ca71", "07c6b9d0-8190-4775-a263-cf9aac9fa8ac"], "durationId": "ea48014c-8fd3-4aad-a1dc-46b3461905d6", "parent": "6f203a02-df73-4c8a-a552-c7680bf6097b"}}, {"head": {"id": "6f203a02-df73-4c8a-a552-c7680bf6097b", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219273966200, "endTime": 150219315157000}, "additional": {"logType": "info", "children": ["10da9c8c-e651-448d-a1aa-003b7ad270f0"], "durationId": "14feea81-4122-4d23-adaa-d9aabe75453b", "parent": "050d605f-827e-43e5-beba-a32b6e562242"}}, {"head": {"id": "a2d0cf32-54c4-4f1d-b4d2-18aa366523fa", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219334416800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d728fc24-8106-4e68-85d1-3a7a189745aa", "name": "hvigorfile, resolve hvigorfile dependencies in 67 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219381956100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8257bf66-529e-41bd-943a-72e7b81419e4", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219315218700, "endTime": 150219382105400}, "additional": {"logType": "info", "children": [], "durationId": "f8ee5780-2c2f-4a58-a358-23881b75ac44", "parent": "050d605f-827e-43e5-beba-a32b6e562242"}}, {"head": {"id": "cf009c64-411e-428a-b916-20632fc47a48", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219383098700, "endTime": 150219383703100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "895fbe7e-81dd-4ea0-86be-4dde6f5c7607", "logId": "165fd537-68a6-4084-b53e-1af76332ee7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48740322-1b4b-4311-ab18-624e4f5e4742", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219383247700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "165fd537-68a6-4084-b53e-1af76332ee7a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219383098700, "endTime": 150219383703100}, "additional": {"logType": "info", "children": [], "durationId": "cf009c64-411e-428a-b916-20632fc47a48", "parent": "050d605f-827e-43e5-beba-a32b6e562242"}}, {"head": {"id": "b789b9a0-ac08-4459-bf8d-7c25a0149f88", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219385962500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be9aee92-6762-4069-8b93-2b7964b23a5f", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219396804500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b5d14ee-3748-4d94-8b97-01f07484125c", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219383756200, "endTime": 150219398869500}, "additional": {"logType": "info", "children": [], "durationId": "9d072c30-8cfd-4c1f-9178-57ce16cf0350", "parent": "050d605f-827e-43e5-beba-a32b6e562242"}}, {"head": {"id": "9d723725-a7b5-444a-9802-907c415c21c5", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219399030600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cea9e44-62a6-443d-8ce2-f07c7c1bf2c1", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219413914800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78a260ed-be0a-403a-b044-f2cf1bf7dbc4", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219414059300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a241416c-58e9-4ecc-a6db-4dada8110c47", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219414758300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7873eb0-f25b-4250-968c-234c2d5f4493", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219421021300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9d3a782-84e2-48fd-9541-e25ab0606430", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219421167900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0c845b8-0a7d-4237-9720-7b6821761a26", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219398898700, "endTime": 150219427967800}, "additional": {"logType": "info", "children": [], "durationId": "79a10b9b-cea5-42bc-828b-0a38c63eb67e", "parent": "050d605f-827e-43e5-beba-a32b6e562242"}}, {"head": {"id": "8207dba7-aceb-4dfc-b77f-5d52dbf7d345", "name": "Configuration phase cost:2 s 636 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219428199500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49ce8260-e03e-45d8-a1d3-93f79fce95e4", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219427994500, "endTime": 150219428326100}, "additional": {"logType": "info", "children": [], "durationId": "cf3505b4-54c3-433b-8b93-91dd8e3b8ce1", "parent": "050d605f-827e-43e5-beba-a32b6e562242"}}, {"head": {"id": "050d605f-827e-43e5-beba-a32b6e562242", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216787490500, "endTime": 150219428341200}, "additional": {"logType": "info", "children": ["f900009b-9d3c-4468-ad3e-7cdf08a648ed", "bdf91910-7eb1-4fe9-b53b-3489035d2db4", "1fede141-39c1-442f-902a-cc963edcddb5", "6f203a02-df73-4c8a-a552-c7680bf6097b", "8257bf66-529e-41bd-943a-72e7b81419e4", "8b5d14ee-3748-4d94-8b97-01f07484125c", "d0c845b8-0a7d-4237-9720-7b6821761a26", "49ce8260-e03e-45d8-a1d3-93f79fce95e4", "165fd537-68a6-4084-b53e-1af76332ee7a"], "durationId": "895fbe7e-81dd-4ea0-86be-4dde6f5c7607", "parent": "4a016922-0927-4d77-9c13-2b7cac0c16d9"}}, {"head": {"id": "6777c22f-5d8d-4419-b4cf-a2c74a1bb77a", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219430489800, "endTime": 150219430544700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b614122-d8f3-492c-b1e2-2200fd53b017", "logId": "4e7691c7-d186-4e4c-8e93-fa361cf46026"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e7691c7-d186-4e4c-8e93-fa361cf46026", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219430489800, "endTime": 150219430544700}, "additional": {"logType": "info", "children": [], "durationId": "6777c22f-5d8d-4419-b4cf-a2c74a1bb77a", "parent": "4a016922-0927-4d77-9c13-2b7cac0c16d9"}}, {"head": {"id": "1af31f7c-b361-42bf-9059-a6e3e3bc50de", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219428369900, "endTime": 150219430558300}, "additional": {"logType": "info", "children": [], "durationId": "fca2b25b-ef90-46e5-bc83-9e2b09c83d63", "parent": "4a016922-0927-4d77-9c13-2b7cac0c16d9"}}, {"head": {"id": "9ba0151e-225e-4e76-98c6-a3799e19f155", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219430564000, "endTime": 150219430581900}, "additional": {"logType": "info", "children": [], "durationId": "992b2339-ca43-4e2d-91e6-c5fed3cddbf0", "parent": "4a016922-0927-4d77-9c13-2b7cac0c16d9"}}, {"head": {"id": "4a016922-0927-4d77-9c13-2b7cac0c16d9", "name": "init", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216766187300, "endTime": 150219430589500}, "additional": {"logType": "info", "children": ["bb05273d-776c-4171-b015-90e4ef119d76", "050d605f-827e-43e5-beba-a32b6e562242", "1af31f7c-b361-42bf-9059-a6e3e3bc50de", "9ba0151e-225e-4e76-98c6-a3799e19f155", "7e50f7d8-e7c6-4178-bdc2-e4122e454a5a", "7494a6f4-edb8-4d77-acbc-f39dc5e21062", "4e7691c7-d186-4e4c-8e93-fa361cf46026"], "durationId": "4b614122-d8f3-492c-b1e2-2200fd53b017"}}, {"head": {"id": "713f0efa-c1e7-4ac6-8e2b-4e80a575dec5", "name": "Configuration task cost before running: 2 s 669 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219431215300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c819ded5-133f-4942-9af2-17e0d4402b83", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219450183900, "endTime": 150219469410900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d53b6b71-fc2c-404a-84c8-d4499a528616", "logId": "e450c9af-96f0-471d-a4ed-2cc34d682c72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d53b6b71-fc2c-404a-84c8-d4499a528616", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219433425200}, "additional": {"logType": "detail", "children": [], "durationId": "c819ded5-133f-4942-9af2-17e0d4402b83"}}, {"head": {"id": "1831b4d3-106e-4ae4-b517-4525fae4f292", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219434862400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc9954e5-7c4f-45d7-bc07-d68829892934", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219435073800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6edcd93-10de-4ad2-a418-b7a65b463f41", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219436617600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "549d6623-f99b-4edc-b9d7-3e7b7d56c2a6", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219439171300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad66ac55-655f-4b45-9ef8-1c5be7919a1f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219441747200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06a16124-e47f-4597-bddb-0865ec479736", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219441863500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b68344ce-7764-4103-a371-4e4d2d8fa014", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219450225400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b2611cf-d995-4b8a-b4fa-67464acc9636", "name": "Incremental task entry:default@PreBuild pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219468970400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25aa6311-0f6a-496c-9a0d-03cd821e4bcb", "name": "entry : default@PreBuild cost memory 0.6689605712890625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219469217700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e450c9af-96f0-471d-a4ed-2cc34d682c72", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219450183900, "endTime": 150219469410900}, "additional": {"logType": "info", "children": [], "durationId": "c819ded5-133f-4942-9af2-17e0d4402b83"}}, {"head": {"id": "89ff358c-251f-464d-aeff-5bda421afacf", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219477310300, "endTime": 150219480301000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5cfae0de-a2b5-4c99-bfef-61b77926b70f", "logId": "8d72030b-aa8b-440b-8a85-d3373fdf39f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cfae0de-a2b5-4c99-bfef-61b77926b70f", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219474908900}, "additional": {"logType": "detail", "children": [], "durationId": "89ff358c-251f-464d-aeff-5bda421afacf"}}, {"head": {"id": "7d1bb4a1-1df9-41ef-abdf-c51652972558", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219476362200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e05281e-9199-461e-b5ea-0e546642bdf6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219476487400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3142851-cc10-495e-b6fd-cd905ceb8a90", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219477329800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "230e8785-8300-4fb8-bae8-28cc2cbb92ef", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219478616000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93bbc3ef-65f8-4ec1-b9cd-dba1d38f5ebf", "name": "entry : default@CreateModuleInfo cost memory 0.0648193359375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219479952200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a2a8596-262e-47d5-8195-74ca1dc3958f", "name": "runTaskFromQueue task cost before running: 2 s 718 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219480119900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d72030b-aa8b-440b-8a85-d3373fdf39f2", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219477310300, "endTime": 150219480301000, "totalTime": 2787800}, "additional": {"logType": "info", "children": [], "durationId": "89ff358c-251f-464d-aeff-5bda421afacf"}}, {"head": {"id": "65139766-2567-45a8-82ff-2582fe2d876d", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219491126100, "endTime": 150219494986300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2b575858-2820-41a2-81f5-36eb80e0917e", "logId": "318e3122-4a8b-498c-affd-2ec5bc6b8ba0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b575858-2820-41a2-81f5-36eb80e0917e", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219484236800}, "additional": {"logType": "detail", "children": [], "durationId": "65139766-2567-45a8-82ff-2582fe2d876d"}}, {"head": {"id": "f07b5c81-9e48-4aa5-9f2e-d8b5a8d34e70", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219485716500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d839e4fc-d5e6-4ba2-9cb9-75146630aab8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219485848300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6190a9d6-4ffa-46f8-9c6c-adbedcc3af6f", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219491150800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9249b0b9-37e9-4945-a793-15372689f634", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219492905000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ba7fa78-10e1-47d3-bb39-28cf3a9c59f0", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219494748600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88814d84-9245-4e73-bcbf-dcc0e41ceb43", "name": "entry : default@GenerateMetadata cost memory 0.10823822021484375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219494908400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "318e3122-4a8b-498c-affd-2ec5bc6b8ba0", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219491126100, "endTime": 150219494986300}, "additional": {"logType": "info", "children": [], "durationId": "65139766-2567-45a8-82ff-2582fe2d876d"}}, {"head": {"id": "ca173f0a-5733-4bd7-a868-58ab8afcf6b8", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219498716200, "endTime": 150219499359200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "696649f0-0d80-4cdd-9d76-99efd460b8ad", "logId": "a794d215-a72c-44a7-b932-dc121e02b9ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "696649f0-0d80-4cdd-9d76-99efd460b8ad", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219497011500}, "additional": {"logType": "detail", "children": [], "durationId": "ca173f0a-5733-4bd7-a868-58ab8afcf6b8"}}, {"head": {"id": "e9e3a059-1406-46bd-b8e9-ea582421ca81", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219498211400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "266664c0-e54e-461a-ab84-736a0ce7afce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219498348800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aceb1a3-5490-42e8-9373-4fe0b37fe8b1", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219498732700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "716a1515-e9b6-48e2-9411-b7847929abfe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219499056200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8762bf3f-d073-4747-b5c5-af54e3177a9f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219499134500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6410ee3d-d83f-4af9-b830-04e800963c22", "name": "entry : default@ConfigureCmake cost memory 0.037750244140625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219499231500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46184bd3-edb2-46e6-924c-b19aba8341a0", "name": "runTaskFromQueue task cost before running: 2 s 737 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219499314800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a794d215-a72c-44a7-b932-dc121e02b9ce", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219498716200, "endTime": 150219499359200, "totalTime": 571100}, "additional": {"logType": "info", "children": [], "durationId": "ca173f0a-5733-4bd7-a868-58ab8afcf6b8"}}, {"head": {"id": "ca3dff84-a1e2-4ce0-8a40-2c60a051fc3d", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219505109800, "endTime": 150219508654000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "43d42094-a0e5-4054-b509-9fd1822ce67f", "logId": "99359589-1fa8-48d5-a47c-4255f9d518aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43d42094-a0e5-4054-b509-9fd1822ce67f", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219501302700}, "additional": {"logType": "detail", "children": [], "durationId": "ca3dff84-a1e2-4ce0-8a40-2c60a051fc3d"}}, {"head": {"id": "e3bbdb0c-4d9b-4dc9-b633-129040437570", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219503954800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8d0667b-15e4-41bd-8bc6-14726b2bcfb6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219504094800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cdbac9b-4b85-4f04-aa0b-8a2c5785cf10", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219505128400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f781196b-d89d-4437-8c3e-565bb1a91aae", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219508420000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e69d5511-83c3-4522-bd55-a5772d72b137", "name": "entry : default@MergeProfile cost memory 0.12445831298828125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219508581900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99359589-1fa8-48d5-a47c-4255f9d518aa", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219505109800, "endTime": 150219508654000}, "additional": {"logType": "info", "children": [], "durationId": "ca3dff84-a1e2-4ce0-8a40-2c60a051fc3d"}}, {"head": {"id": "cc7f51d1-0df9-4d81-b361-d5c128771ded", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219513070000, "endTime": 150219516887200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c5486539-776e-4d95-bf27-37d8028fa6a7", "logId": "3a33bb96-a51f-476e-839f-c9e44ca71539"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5486539-776e-4d95-bf27-37d8028fa6a7", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219510620400}, "additional": {"logType": "detail", "children": [], "durationId": "cc7f51d1-0df9-4d81-b361-d5c128771ded"}}, {"head": {"id": "0c032610-d1bb-411a-811d-240a20c3bbd1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219511862400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a9d764d-f858-4d12-94f2-ff28e24a98f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219511974000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e25c2fec-3f07-43b9-9754-0e61662e9bca", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219513085800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6827aa19-03cf-4d43-a430-84f77c752d96", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219514650600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23f9f374-6a0e-4a6e-b62a-fc17a511f17e", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219516672900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20f4149f-1791-4439-b273-ba0b84d4ea48", "name": "entry : default@CreateBuildProfile cost memory 0.1145477294921875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219516815100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a33bb96-a51f-476e-839f-c9e44ca71539", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219513070000, "endTime": 150219516887200}, "additional": {"logType": "info", "children": [], "durationId": "cc7f51d1-0df9-4d81-b361-d5c128771ded"}}, {"head": {"id": "8827d74e-5b82-4483-bc54-50869a6ed07c", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219522706800, "endTime": 150219523495400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "4a59bea5-9184-4d8c-8830-ccbcb576ea25", "logId": "bbd49d9b-7274-4324-8ab1-15739426a453"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a59bea5-9184-4d8c-8830-ccbcb576ea25", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219519539100}, "additional": {"logType": "detail", "children": [], "durationId": "8827d74e-5b82-4483-bc54-50869a6ed07c"}}, {"head": {"id": "62e9514b-1b6c-442c-8327-0a01c48055fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219521583800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04162ab7-d1aa-4c56-adf2-b4eb7585496a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219521731100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc2a75c7-a286-42d6-9b82-1407ff71e34c", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219522724700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a716e011-a3c5-4890-a41d-b1b02c7a5e2f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219522866200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d20c66ea-ab7b-4e66-ab81-f9cd48827957", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219522914300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5333133d-16a8-40a3-bb74-43592faae6c2", "name": "entry : default@PreCheckSyscap cost memory 0.0416107177734375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219523303800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9470a88-774e-4fdd-b1b9-0ae594e6512c", "name": "runTaskFromQueue task cost before running: 2 s 762 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219523439400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbd49d9b-7274-4324-8ab1-15739426a453", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219522706800, "endTime": 150219523495400, "totalTime": 704800}, "additional": {"logType": "info", "children": [], "durationId": "8827d74e-5b82-4483-bc54-50869a6ed07c"}}, {"head": {"id": "4140e5a9-5db4-4ced-be39-f5bac585d153", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219528863500, "endTime": 150219543900000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e6254b8f-ead1-4dcf-b7d8-bdeda1832e37", "logId": "dbbf3057-fc4e-439e-9893-8c7ac99df702"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6254b8f-ead1-4dcf-b7d8-bdeda1832e37", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219525599500}, "additional": {"logType": "detail", "children": [], "durationId": "4140e5a9-5db4-4ced-be39-f5bac585d153"}}, {"head": {"id": "ac6916ce-fdf1-40e7-8b87-5da7dc383f84", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219526849700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d265389-a7a4-44e4-8059-ad75d5a54180", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219526975600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0232d80-12a3-4546-9a8c-4c21d9205c1f", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219528879700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "437d989e-d2d5-4710-a1a5-7f83b9a8205f", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219542309300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a415d9-8739-4e8a-9e25-3e141870749f", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219543652900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fea20b1b-1251-4723-99be-d8b95ff8a1e9", "name": "entry : default@GeneratePkgContextInfo cost memory 0.48960113525390625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219543825200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbbf3057-fc4e-439e-9893-8c7ac99df702", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219528863500, "endTime": 150219543900000}, "additional": {"logType": "info", "children": [], "durationId": "4140e5a9-5db4-4ced-be39-f5bac585d153"}}, {"head": {"id": "ad411c76-874e-435e-acf2-b9c3991c4bb3", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219555139800, "endTime": 150219557905900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "5b66e5fc-71c3-4dac-916a-6635b7382af4", "logId": "decc55ad-d0b5-4458-b0ce-c3cc653c0619"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b66e5fc-71c3-4dac-916a-6635b7382af4", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219546202100}, "additional": {"logType": "detail", "children": [], "durationId": "ad411c76-874e-435e-acf2-b9c3991c4bb3"}}, {"head": {"id": "a5dd1df0-b13e-4ef3-b567-b8abda645a71", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219547623400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d11d0d3-9041-4c98-a1aa-8f2cbcd32c1c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219547747800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e3fae05-2a3f-45f1-92f0-58b71b9c3d10", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219555168400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d93d8c9a-8a1e-4bd3-bb57-19d2de1578c6", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219557250600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9588d77e-5546-47f4-9b70-a3cadef8ee38", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219557441400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5feebf1d-6386-457c-b1cf-ae7475fc0b66", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219557550800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f18149ee-db51-4b2f-84ff-bd91d903c570", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219557597900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6f40b67-4330-4ec6-867f-95e131425a4f", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12332916259765625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219557752400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4530552d-8e58-4850-87c6-7e6c92e86780", "name": "runTaskFromQueue task cost before running: 2 s 796 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219557852300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "decc55ad-d0b5-4458-b0ce-c3cc653c0619", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219555139800, "endTime": 150219557905900, "totalTime": 2689100}, "additional": {"logType": "info", "children": [], "durationId": "ad411c76-874e-435e-acf2-b9c3991c4bb3"}}, {"head": {"id": "eedbb67f-0add-4e71-8bbf-02a8eb4acd49", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219563372300, "endTime": 150219563904700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6286b1f1-969f-4887-b25f-52b623c1ee01", "logId": "227441e9-60b0-4c59-93c5-a67a8ba10365"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6286b1f1-969f-4887-b25f-52b623c1ee01", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219560964300}, "additional": {"logType": "detail", "children": [], "durationId": "eedbb67f-0add-4e71-8bbf-02a8eb4acd49"}}, {"head": {"id": "a178a2c8-f0e4-47c6-a25f-c3e031c9d717", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219562268500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bf6d43c-a55e-4aa5-8e30-d0ac86c2f58c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219562377900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc43fb7e-fd8c-4c48-9fe9-6c9e494e388e", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219563388000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78f80f20-6d88-4cc5-bd4e-00948e47e3c8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219563553500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcbf9fc6-7282-4df3-a107-5c1527711d24", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219563637900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d33f614-d142-489e-8836-5eea09c86382", "name": "entry : default@BuildNativeWithCmake cost memory 0.03899383544921875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219563763100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12df2dd3-da9b-4e5a-954f-cb20b62404e3", "name": "runTaskFromQueue task cost before running: 2 s 802 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219563858300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "227441e9-60b0-4c59-93c5-a67a8ba10365", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219563372300, "endTime": 150219563904700, "totalTime": 460100}, "additional": {"logType": "info", "children": [], "durationId": "eedbb67f-0add-4e71-8bbf-02a8eb4acd49"}}, {"head": {"id": "ef08c182-1bf3-4eae-8d37-18168f3b350e", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219568195200, "endTime": 150219573138600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "67c6fa36-a522-479c-9cde-a0f3e839ab48", "logId": "204d1527-b30c-478f-a18d-09daaf06a940"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67c6fa36-a522-479c-9cde-a0f3e839ab48", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219565836500}, "additional": {"logType": "detail", "children": [], "durationId": "ef08c182-1bf3-4eae-8d37-18168f3b350e"}}, {"head": {"id": "2b9d9796-cdb4-48f0-a3d2-d8eb6a5495a1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219567187800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0f48c0e-4cb3-4b84-bccd-d0b70480bac9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219567312100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8eaf3ba-4025-4b03-9b48-967dc85ec2f0", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219568209500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34196bd3-1006-4a03-865e-1edefe64488f", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219572734000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7af11cb2-0c0a-4e8a-a24b-0de934232091", "name": "entry : default@MakePackInfo cost memory 0.17138671875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219572895500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "204d1527-b30c-478f-a18d-09daaf06a940", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219568195200, "endTime": 150219573138600}, "additional": {"logType": "info", "children": [], "durationId": "ef08c182-1bf3-4eae-8d37-18168f3b350e"}}, {"head": {"id": "8631df9b-1ea9-4944-924f-c1f70681f6de", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219581412400, "endTime": 150219624811600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "86de8f56-6a20-4b56-bd4b-fee865d26f0c", "logId": "e75ef154-f10c-40c8-bdf4-4bc1982518f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86de8f56-6a20-4b56-bd4b-fee865d26f0c", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219575811400}, "additional": {"logType": "detail", "children": [], "durationId": "8631df9b-1ea9-4944-924f-c1f70681f6de"}}, {"head": {"id": "e5497430-1637-4923-8a63-e2fd9b915c5d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219578190500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fad67049-8eb6-4e21-8730-02123cd48949", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219578423600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e83bdc7f-7f49-4905-b14b-32a47970e165", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219581446700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7af5192d-8fae-49d5-8569-9d2dd4313016", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219581911300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7577f9b-7f30-4c25-a569-f72b6f03aeb7", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219583502300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18ccc5f5-1ec8-41cf-b6e4-dcc5c51ab7c2", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 41 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219624445300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "816e4ef6-94bb-416b-8181-2f4c33b33a9a", "name": "entry : default@SyscapTransform cost memory 0.1600189208984375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219624679300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e75ef154-f10c-40c8-bdf4-4bc1982518f5", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219581412400, "endTime": 150219624811600}, "additional": {"logType": "info", "children": [], "durationId": "8631df9b-1ea9-4944-924f-c1f70681f6de"}}, {"head": {"id": "d7b015fd-2fc0-49f3-bdbc-c116c8face55", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219632069900, "endTime": 150219634626600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "fb043ff8-fe4e-4557-8654-15ecbed465e6", "logId": "b1a6005a-b7b8-48f3-b13c-d932fa233903"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb043ff8-fe4e-4557-8654-15ecbed465e6", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219628556300}, "additional": {"logType": "detail", "children": [], "durationId": "d7b015fd-2fc0-49f3-bdbc-c116c8face55"}}, {"head": {"id": "fc0c06d3-2495-4174-8567-7bc5feec141e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219629995100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b59641a5-7d5b-462a-aeea-a37de938425a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219630126100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df8f2f4c-7b2f-47ab-9fc4-d92e3204c65f", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219632088000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f50e832f-5cf1-4bac-8264-2fe55867727f", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219634415300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5db537f2-4e11-4774-8fd3-a8f3b7718645", "name": "entry : default@ProcessProfile cost memory 0.12857818603515625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219634562300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1a6005a-b7b8-48f3-b13c-d932fa233903", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219632069900, "endTime": 150219634626600}, "additional": {"logType": "info", "children": [], "durationId": "d7b015fd-2fc0-49f3-bdbc-c116c8face55"}}, {"head": {"id": "88138cf9-47a6-43cb-a38f-33f0a3fbfce9", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219641825700, "endTime": 150219649294700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9283da41-128b-41a8-b518-5d6a29e88162", "logId": "cdbbd201-c087-46c7-9335-9c79a9c3063f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9283da41-128b-41a8-b518-5d6a29e88162", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219637476600}, "additional": {"logType": "detail", "children": [], "durationId": "88138cf9-47a6-43cb-a38f-33f0a3fbfce9"}}, {"head": {"id": "83832f5b-d32f-4057-817a-5229cfc6869a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219639375800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57e2533b-ba0f-48c3-b971-fc8981e5fb5c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219639507700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05f3423f-f0a6-4e0f-aa6b-3a07fcd7a86b", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219641843400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9af6a46-caaa-40ea-89f6-d788a851dd21", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219649066800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d56816d-0de9-4272-99b1-fb2feb157538", "name": "entry : default@ProcessRouterMap cost memory 0.24488067626953125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219649226600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdbbd201-c087-46c7-9335-9c79a9c3063f", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219641825700, "endTime": 150219649294700}, "additional": {"logType": "info", "children": [], "durationId": "88138cf9-47a6-43cb-a38f-33f0a3fbfce9"}}, {"head": {"id": "b1703b90-1af2-4ca7-bd3f-6e8e1285328d", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219658138500, "endTime": 150219665380300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "81a67deb-a915-460a-9210-4f9911f3ed29", "logId": "8fc65b3e-5184-487d-ae38-b208007cfebb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81a67deb-a915-460a-9210-4f9911f3ed29", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219656705300}, "additional": {"logType": "detail", "children": [], "durationId": "b1703b90-1af2-4ca7-bd3f-6e8e1285328d"}}, {"head": {"id": "4e898f9d-de57-4bd5-b729-375a778e1a49", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219657905300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "078bbe37-b542-4d9e-8288-b2330d7df05b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219658030200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bf672f0-3b95-4f22-9888-2432db296f7c", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219658148800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dda1176c-b746-4677-b3f6-2f76eba068a8", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219658307300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f5c2023-c96a-4a32-a21f-d7cd482450df", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219663221600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bf32e23-36a1-4048-8dee-492b0fbc391e", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219663401300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62fcd853-0480-4704-bccd-ba3fe65e5d03", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219663528800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2df3aa74-f588-4dee-b334-c21f94fb61a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219663570900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37993e8f-184a-4f07-a4fe-8a90e5fc7b81", "name": "entry : default@ProcessStartupConfig cost memory 0.26705169677734375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219665153200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8efff554-67b1-48c6-b9dc-214db5ba13e7", "name": "runTaskFromQueue task cost before running: 2 s 903 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219665320300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fc65b3e-5184-487d-ae38-b208007cfebb", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219658138500, "endTime": 150219665380300, "totalTime": 7147500}, "additional": {"logType": "info", "children": [], "durationId": "b1703b90-1af2-4ca7-bd3f-6e8e1285328d"}}, {"head": {"id": "e3829c44-8dfb-4895-a18f-decba3fda7b7", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219672700400, "endTime": 150219674285800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "9ddf47f6-0f52-44ba-9ff6-db99fccd5341", "logId": "c7813cdf-24e5-475e-b728-c29b02207136"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ddf47f6-0f52-44ba-9ff6-db99fccd5341", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219670422600}, "additional": {"logType": "detail", "children": [], "durationId": "e3829c44-8dfb-4895-a18f-decba3fda7b7"}}, {"head": {"id": "35f9226e-953e-4586-853c-05288f1cec89", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219671682700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81e87619-a58f-44fa-a9ec-4cf76831cd86", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219671802700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6593b886-cc1e-4ccc-8d4c-f31fdb76fe19", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219672715900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58f549a9-309e-4a10-a2b1-a4f38e9f1d41", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219672864800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c97eac4-6b89-40c0-bae5-9c9f413f5206", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219672915600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61216671-b082-4d26-8d48-421619d07221", "name": "entry : default@BuildNativeWithNinja cost memory 0.05895233154296875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219674084300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ebb1bd4-7e66-41df-be5b-577d68803dcd", "name": "runTaskFromQueue task cost before running: 2 s 912 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219674227900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7813cdf-24e5-475e-b728-c29b02207136", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219672700400, "endTime": 150219674285800, "totalTime": 1501200}, "additional": {"logType": "info", "children": [], "durationId": "e3829c44-8dfb-4895-a18f-decba3fda7b7"}}, {"head": {"id": "b1b26aaa-8558-4325-8b42-a9b32c798e08", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219682978600, "endTime": 150219692997700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "f30fcf65-e604-44d5-991e-dbaa4327afc9", "logId": "ffe7a23b-4f36-49f7-a9eb-371d82997950"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f30fcf65-e604-44d5-991e-dbaa4327afc9", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219677753700}, "additional": {"logType": "detail", "children": [], "durationId": "b1b26aaa-8558-4325-8b42-a9b32c798e08"}}, {"head": {"id": "4d617207-e9e4-4bfe-b8fd-10348cc94307", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219679216900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6968f3e9-4e22-4de2-9fcf-afa231880ea2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219679345700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "647d46b6-de54-4d87-a2d3-b2baf71509cd", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219681143900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03226b69-6e33-4d27-869b-dab32444dedc", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219685578600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9449391-82a3-4ebb-a0e4-630d69199bda", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219689957400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "480fbf75-82ff-4a60-833e-f95c5b1c0787", "name": "entry : default@ProcessResource cost memory 0.2068634033203125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219690178800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffe7a23b-4f36-49f7-a9eb-371d82997950", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219682978600, "endTime": 150219692997700}, "additional": {"logType": "info", "children": [], "durationId": "b1b26aaa-8558-4325-8b42-a9b32c798e08"}}, {"head": {"id": "f824dd60-9cb2-4ff9-baee-e59a1e0d1979", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219706041800, "endTime": 150219733153800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "286595e1-469c-4f4f-bfff-243b2a9c6415", "logId": "d40553b3-5295-44d8-9936-bdcd2d64515d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "286595e1-469c-4f4f-bfff-243b2a9c6415", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219697667800}, "additional": {"logType": "detail", "children": [], "durationId": "f824dd60-9cb2-4ff9-baee-e59a1e0d1979"}}, {"head": {"id": "c9919e5e-2f4d-43a0-a6bd-d1d7b54ec802", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219698958500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "124dd8a3-0405-4cc6-a2c2-47bc077724cf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219699107500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9fe1a35-c111-43a5-a98c-7988f676c37f", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219706059800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a6ad371-2762-4c2a-bd6f-73d0fcf479a3", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219732900400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7517be05-226d-418a-8780-aa510f06a482", "name": "entry : default@GenerateLoaderJson cost memory 1.0141220092773438", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219733083500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d40553b3-5295-44d8-9936-bdcd2d64515d", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219706041800, "endTime": 150219733153800}, "additional": {"logType": "info", "children": [], "durationId": "f824dd60-9cb2-4ff9-baee-e59a1e0d1979"}}, {"head": {"id": "16afcc6e-d548-4984-a055-633e6bf065b1", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219746555600, "endTime": 150219752979100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d6d7d00b-70c0-4af2-8c83-a80ed8e1e60e", "logId": "626ac76b-4ffb-4c3d-8530-389322bce3e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6d7d00b-70c0-4af2-8c83-a80ed8e1e60e", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219744281600}, "additional": {"logType": "detail", "children": [], "durationId": "16afcc6e-d548-4984-a055-633e6bf065b1"}}, {"head": {"id": "6e07593e-7db7-4f37-a649-dcabbd3bb090", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219745465100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15b81b6c-7a98-4395-bc6d-0b430dfd2e0c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219745606000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bbb4470-2ee2-40ce-82d3-58b5b8951fcc", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219746569100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ea2833e-0c5e-4717-b4bc-b56adcc38ea3", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219752567200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1813298b-5b17-4605-9b03-4f94da78ae50", "name": "entry : default@ProcessLibs cost memory 0.148681640625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219752849800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "626ac76b-4ffb-4c3d-8530-389322bce3e1", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219746555600, "endTime": 150219752979100}, "additional": {"logType": "info", "children": [], "durationId": "16afcc6e-d548-4984-a055-633e6bf065b1"}}, {"head": {"id": "c406685c-88c2-4e96-aad2-f611358e668b", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219761352100, "endTime": 150220038353100}, "additional": {"children": ["07e2c97b-60f1-4f86-9028-83be89165dbd", "6239fae9-0fb7-41c7-a28c-b051a9cdb579"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources' has been changed."], "detailId": "2cae4280-05b0-4652-bee1-d430de8228e5", "logId": "c99c5703-a6a6-4ea0-b7ec-62868a31c997"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2cae4280-05b0-4652-bee1-d430de8228e5", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219755681600}, "additional": {"logType": "detail", "children": [], "durationId": "c406685c-88c2-4e96-aad2-f611358e668b"}}, {"head": {"id": "cb987724-492c-4120-9838-c0bd105178c2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219756988200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a6a2f53-4131-414b-9b64-893e4e7ef17e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219757112400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80139041-2f1e-476d-9838-ae074e7f4eec", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219758227000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11bfcc94-3083-4d4a-b7d4-d2a0c8f0824a", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219761453700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9725023d-30c5-4b6b-b464-2406f3cfb543", "name": "entry:default@CompileResource is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219776992200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02d28972-248d-4218-ad88-4bac06345436", "name": "Incremental task entry:default@CompileResource pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219777223400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07e2c97b-60f1-4f86-9028-83be89165dbd", "name": "create intermediate resource category", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219779140100, "endTime": 150219892367400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c406685c-88c2-4e96-aad2-f611358e668b", "logId": "b609c6d6-f3bd-4bbc-ac2a-aa73a620ec95"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b609c6d6-f3bd-4bbc-ac2a-aa73a620ec95", "name": "create intermediate resource category", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219779140100, "endTime": 150219892367400}, "additional": {"logType": "info", "children": [], "durationId": "07e2c97b-60f1-4f86-9028-83be89165dbd", "parent": "c99c5703-a6a6-4ea0-b7ec-62868a31c997"}}, {"head": {"id": "6e958b74-5a80-4d9f-a3f3-a1c96bab38da", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\restool.exe]\n [\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\restool.exe',\n  '-l',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resConfig.json'\n]", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219893183000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6239fae9-0fb7-41c7-a28c-b051a9cdb579", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219894328400, "endTime": 150220034845200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c406685c-88c2-4e96-aad2-f611358e668b", "logId": "e490291e-5bf8-4edf-8741-8a862146d771"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20fdd16f-9642-4f81-9642-8d90b1040ae0", "name": "current process  memoryUsage: {\n  rss: 256540672,\n  heapTotal: 167219200,\n  heapUsed: 142744968,\n  external: 3100817,\n  arrayBuffers: 90024\n} os memoryUsage :13.417545318603516", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219896250900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ce49138-15ad-406c-b8ad-355b329ee3dc", "name": "Info: Pack: normal pack mode\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219932036600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "922c5fbc-9e1c-43c1-bafc-b9ab2d26455f", "name": "Warning: 'extensionPath' value cannot be empty.\r\nat C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\opt-compression.json\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219932622000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9d11846-b64d-4535-a272-c2301cb0f5e9", "name": "Info: hardware concurrency count is : 20\r\nInfo: thread count is : 20\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219981533400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c96b3448-3b7f-4ef5-984f-8aeb0974e6f6", "name": "Info: thread pool is started\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219981852100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8734844-8773-484b-bef3-b7578b6896c2", "name": "Info: GenericCompiler::CompileFiles\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219984515300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d4c34f9-acc5-41c3-a0bb-21e433a3d0e7", "name": "Info: GenericCompiler::CompileFiles\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219991194700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceea7a15-226a-4916-8790-c9047c3e6e80", "name": "Info: GenericCompiler::CompileFiles\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219999557900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58e38e2b-6a0b-4c74-b169-32228b68eff7", "name": "Warning: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources\\base\\media\\background.png' is defined repeatedly.\r\nWarning: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources\\base\\media\\foreground.png' is defined repeatedly.\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219999858000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b10a5a8-9bfd-4ea8-b220-8a284107ba73", "name": "Warning: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources\\base\\media\\layered_image.json' is defined repeatedly.\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220011556100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c72fa936-a724-481c-a1cd-73da3d22f46e", "name": "Info: GenericCompiler::CompileFiles\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220023315900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b077e9ba-065b-4344-b43c-39d8d162b75b", "name": "Info: scale icon is not enable.\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220028786000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f796c4fe-228c-41a3-86ee-34315e95b10a", "name": "Warning: The width or height of the png file referenced by the icon exceeds the limit (41 pixels)\r\nat C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\resources\\base\\media\\background.png\r\nWarning: The width or height of the png file referenced by the icon exceeds the limit (41 pixels)\r\nat C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\resources\\base\\media\\foreground.png\r\nWarning: C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\resources\\base\\media\\layered_image.json is not png format\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220029145100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1b16fab-c3b0-41d0-8c23-9b67437b683e", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220030288800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08324a1c-63f3-4591-86db-d8b3e40b98a2", "name": "Info: thread pool is stopped\r\n", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220030844300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e490291e-5bf8-4edf-8741-8a862146d771", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219894328400, "endTime": 150220034845200}, "additional": {"logType": "info", "children": [], "durationId": "6239fae9-0fb7-41c7-a28c-b051a9cdb579", "parent": "c99c5703-a6a6-4ea0-b7ec-62868a31c997"}}, {"head": {"id": "95da66e3-3411-4d8a-a3ca-9e29943e27e3", "name": "entry : default@CompileResource cost memory 11.100006103515625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220037990000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f93af1f-6bc0-47ca-b785-8b81c2d7f988", "name": "runTaskFromQueue task cost before running: 3 s 276 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220038254700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c99c5703-a6a6-4ea0-b7ec-62868a31c997", "name": "Finished :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150219761352100, "endTime": 150220038353100, "totalTime": 276820600}, "additional": {"logType": "info", "children": ["b609c6d6-f3bd-4bbc-ac2a-aa73a620ec95", "e490291e-5bf8-4edf-8741-8a862146d771"], "durationId": "c406685c-88c2-4e96-aad2-f611358e668b"}}, {"head": {"id": "2a864191-227f-44ec-8d82-992cc778ddee", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220046589200, "endTime": 150220049484000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7aeb108d-cc9d-4a7b-a428-c778591abed6", "logId": "1617b49f-28b1-4890-ab7e-ba4a9ee7aa55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7aeb108d-cc9d-4a7b-a428-c778591abed6", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220041855800}, "additional": {"logType": "detail", "children": [], "durationId": "2a864191-227f-44ec-8d82-992cc778ddee"}}, {"head": {"id": "4d2404bd-79ae-4100-82ee-085255c5f433", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220043231400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4af9257d-936f-4856-89f6-176d965496a3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220043366900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b01d053-dad8-4e01-b4fd-77b22e39fc57", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220046603800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ddde0ef-37b4-42f3-8dd1-a00d66d738e8", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220047297700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04d7cff4-1f30-4148-9b04-391d42ca9cd3", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220049238500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a80872a7-236d-4c4d-a9aa-6ff2e389b4d3", "name": "entry : default@DoNativeStrip cost memory 0.08217620849609375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220049382800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1617b49f-28b1-4890-ab7e-ba4a9ee7aa55", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220046589200, "endTime": 150220049484000}, "additional": {"logType": "info", "children": [], "durationId": "2a864191-227f-44ec-8d82-992cc778ddee"}}, {"head": {"id": "0ce1e0ba-37d0-40cf-985e-c26f7608cf4d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220059468200, "endTime": 150235298053900}, "additional": {"children": ["60c0c48a-cf39-4dd0-8567-5dd00faf8cd5", "ca5d9e34-eeb7-4ed5-8577-59a8630cdfc6"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "14715999-3e44-4305-9209-c56e716ae2f2", "logId": "3e576569-508f-4fd3-9e97-bfc8d91b4080"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14715999-3e44-4305-9209-c56e716ae2f2", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220051893600}, "additional": {"logType": "detail", "children": [], "durationId": "0ce1e0ba-37d0-40cf-985e-c26f7608cf4d"}}, {"head": {"id": "38cb4f99-d553-4393-9914-9595abe71512", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220053525400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b470701-f73e-4607-b332-053575c11937", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220053845900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87e0694c-808b-443f-a93a-6d296ae2af71", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220059483400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d7fe604-98fb-4a0d-9912-bd8bae010b35", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220059970300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ba55bf6-62a0-492d-b4f7-3fe2d3db5c19", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220102527800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3083dc-e6c9-47c2-942e-67d689e74b54", "name": "default@CompileArkTS work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220105313900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60c0c48a-cf39-4dd0-8567-5dd00faf8cd5", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 150223000413000, "endTime": 150235289938800}, "additional": {"children": ["778aa874-0bd1-44f4-8766-8c183abdf68d", "3c9a7468-11da-47e1-a28b-a2152279e756", "809354c9-7fe6-4b4e-8660-22501fb4b6c6", "1360873b-e0f0-4581-a71a-8cb64da564cb", "f51185aa-be8c-4612-bf93-6d86e2c49a04", "24ba56e9-9f28-405e-ac49-60561d30720d", "4a822450-3011-4511-8a3b-f0f0ba110d1a"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "0ce1e0ba-37d0-40cf-985e-c26f7608cf4d", "logId": "8c8ae6b3-502d-4541-bd48-a0a29960fe85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff27357e-64d9-4d3f-be5c-8a9f2bb4f3ef", "name": "default@CompileArkTS work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220106825600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31b034e2-7875-4782-8031-4f46c48cabd2", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107116700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a5ccdb2-e1f0-4f4c-b01e-d7ab908a408f", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107191000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50967ac9-4e66-4885-8aac-7f2a6989d11c", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107229700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "481d7716-202f-424e-8caf-d4b63f255b81", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107260900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c103f30-f39a-4b02-8b9d-01b7ee531dda", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107290500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54eade90-d661-4ea0-89d7-26321ab37eec", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107321800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1435fe50-da57-4cbf-98a2-3d337514958d", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107382200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "380400f8-eec9-41e5-b6bb-94a6fd9b4259", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107414100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77df206e-a1aa-44a6-b92c-53ac8da1553d", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107445400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62b1e314-4a8d-407e-821e-bdf09ffb951f", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107480900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43b61214-db3e-40ad-8d11-68848c50e1cc", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107512400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cbd8ed9-26d5-46f2-b0c7-b401e6421210", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107543900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "148044b3-a97b-499e-9de4-755efb952f30", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107575400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7eb13cf-24de-4f0a-a1ba-87f7339cf9c7", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107604600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "484fc054-bad6-4f4a-9930-c25e5b58fc94", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107634000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1301770-3195-4b72-87ee-716dde7219f4", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220107772000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0128e37d-2a1c-4c80-b209-f64041ea77fc", "name": "default@CompileArkTS work[0] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220108937500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32b5d28f-332e-44de-a034-1e658317781c", "name": "default@CompileArkTS work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220109095900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b69c2ac5-0d35-42a5-9958-76c3981f905b", "name": "CopyResources startTime: 150220109223500", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220109227700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69e138bc-7364-435f-8a9e-43d1ab380239", "name": "default@CompileArkTS work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220109299600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca5d9e34-eeb7-4ed5-8577-59a8630cdfc6", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker18", "startTime": 150221285936000, "endTime": 150221300011900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "0ce1e0ba-37d0-40cf-985e-c26f7608cf4d", "logId": "c6ebed31-41d2-436a-9b70-6b1661078227"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ac3dca3-9d9f-4563-a9c9-5e629f4099f0", "name": "default@CompileArkTS work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220110201800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5f5b6d7-2ac9-4bc9-9bc8-eabc7320e27d", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220110305200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f61a5dce-b75a-4552-9f02-9fbc0debd73e", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220110361400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f937a2e6-5cad-4583-8626-d3ddfdb2c95b", "name": "default@CompileArkTS work[1] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220111172300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b9afca5-913d-416f-ada9-caa868dc1f25", "name": "default@CompileArkTS work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220111260500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce085361-4248-46dc-8702-0f91c6fb12ac", "name": "entry : default@CompileArkTS cost memory 1.8011703491210938", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220111395700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53980016-b43c-41ad-a8f6-a5a6d83d1ee2", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220118207100, "endTime": 150220126574900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "4dacee5b-3015-425f-902c-0b37a526b091", "logId": "1163f4ae-559b-4ada-8ae2-31ef14150fdf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4dacee5b-3015-425f-902c-0b37a526b091", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220113120300}, "additional": {"logType": "detail", "children": [], "durationId": "53980016-b43c-41ad-a8f6-a5a6d83d1ee2"}}, {"head": {"id": "e64101e8-8376-426f-a1ac-c76e8bda9232", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220114232800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7643a59-29f1-4c1c-bf94-2bb6cf87815a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220114343000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4107fed7-c642-4ea9-9218-ff450c999155", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220118221600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2521c9d-1c89-4259-b17a-0c2c70f2d1cc", "name": "entry : default@BuildJS cost memory 0.35720062255859375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220126257100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7f57369-bbfc-43be-a8dc-4a6805bfb4db", "name": "runTaskFromQueue task cost before running: 3 s 365 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220126484300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1163f4ae-559b-4ada-8ae2-31ef14150fdf", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220118207100, "endTime": 150220126574900, "totalTime": 8219100}, "additional": {"logType": "info", "children": [], "durationId": "53980016-b43c-41ad-a8f6-a5a6d83d1ee2"}}, {"head": {"id": "71fc7700-843f-478b-8f01-288a270e1ad4", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220134556400, "endTime": 150220137791700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "795fc314-36dc-4338-934d-993b7949bf4b", "logId": "91de7971-8ce6-43fe-a472-c8a1dcd65beb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "795fc314-36dc-4338-934d-993b7949bf4b", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220130773900}, "additional": {"logType": "detail", "children": [], "durationId": "71fc7700-843f-478b-8f01-288a270e1ad4"}}, {"head": {"id": "96f658ee-7e6b-4176-9b25-f6d553adbde2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220131967800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80d08a24-57da-4e4a-b480-b96f67f5a8e3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220132096700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3de5aefe-bc1f-4b30-bc99-268801e9a1b5", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220134570600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed54df69-3f2f-4880-9e0b-2455d97247df", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220135425900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fe84a92-95d2-4c90-be1a-7255aa18dee0", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220137579900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47e72e04-9daa-4ef0-aa27-2f35be256231", "name": "entry : default@CacheNativeLibs cost memory 0.09719085693359375", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220137725100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91de7971-8ce6-43fe-a472-c8a1dcd65beb", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220134556400, "endTime": 150220137791700}, "additional": {"logType": "info", "children": [], "durationId": "71fc7700-843f-478b-8f01-288a270e1ad4"}}, {"head": {"id": "ba4b7a53-0e51-4ea8-ad6e-d55c4a9b3bfb", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150221300487100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a44b5b6-5b0e-4e21-b4e3-6b880b7851de", "name": "CopyResources is end, endTime: 150221300689200", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150221300697900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d67efea7-3a7b-4b34-9469-c61c2f0eb84e", "name": "default@CompileArkTS work[1] done.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150221300904000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6ebed31-41d2-436a-9b70-6b1661078227", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Worker18", "startTime": 150221285936000, "endTime": 150221300011900}, "additional": {"logType": "info", "children": [], "durationId": "ca5d9e34-eeb7-4ed5-8577-59a8630cdfc6", "parent": "3e576569-508f-4fd3-9e97-bfc8d91b4080"}}, {"head": {"id": "820e4712-0da6-4706-b04b-25d952253ce7", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150221301107300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69073ff3-9b84-4876-a42f-ab51b859babe", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235290298600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "778aa874-0bd1-44f4-8766-8c183abdf68d", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 150223001575900, "endTime": 150224071784000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "60c0c48a-cf39-4dd0-8567-5dd00faf8cd5", "logId": "af6a3ed5-f748-497e-90f2-b43a13c73714"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af6a3ed5-f748-497e-90f2-b43a13c73714", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150223001575900, "endTime": 150224071784000}, "additional": {"logType": "info", "children": [], "durationId": "778aa874-0bd1-44f4-8766-8c183abdf68d", "parent": "8c8ae6b3-502d-4541-bd48-a0a29960fe85"}}, {"head": {"id": "3c9a7468-11da-47e1-a28b-a2152279e756", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 150224073278200, "endTime": 150224106521100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "60c0c48a-cf39-4dd0-8567-5dd00faf8cd5", "logId": "edcd4f27-7001-487d-800b-dd2479158408"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "edcd4f27-7001-487d-800b-dd2479158408", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150224073278200, "endTime": 150224106521100}, "additional": {"logType": "info", "children": [], "durationId": "3c9a7468-11da-47e1-a28b-a2152279e756", "parent": "8c8ae6b3-502d-4541-bd48-a0a29960fe85"}}, {"head": {"id": "809354c9-7fe6-4b4e-8660-22501fb4b6c6", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 150224106637000, "endTime": 150224106856700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "60c0c48a-cf39-4dd0-8567-5dd00faf8cd5", "logId": "ecee5082-0b71-4aa9-80ae-ac73e08cf8c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ecee5082-0b71-4aa9-80ae-ac73e08cf8c6", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150224106637000, "endTime": 150224106856700}, "additional": {"logType": "info", "children": [], "durationId": "809354c9-7fe6-4b4e-8660-22501fb4b6c6", "parent": "8c8ae6b3-502d-4541-bd48-a0a29960fe85"}}, {"head": {"id": "1360873b-e0f0-4581-a71a-8cb64da564cb", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 150224106926600, "endTime": 150235041110600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "60c0c48a-cf39-4dd0-8567-5dd00faf8cd5", "logId": "0402de32-ae9c-41da-984a-656c15a3811e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0402de32-ae9c-41da-984a-656c15a3811e", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150224106926600, "endTime": 150235041110600}, "additional": {"logType": "info", "children": [], "durationId": "1360873b-e0f0-4581-a71a-8cb64da564cb", "parent": "8c8ae6b3-502d-4541-bd48-a0a29960fe85"}}, {"head": {"id": "f51185aa-be8c-4612-bf93-6d86e2c49a04", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 150235041355800, "endTime": 150235090850200}, "additional": {"children": ["9a649f59-dbe7-4b58-8916-19606417a325", "0c52d547-0f62-496b-8010-2de0eaecce8c", "e5dbe6aa-15f1-48b7-8c03-9d08be3a0d5e"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "60c0c48a-cf39-4dd0-8567-5dd00faf8cd5", "logId": "f7068d0f-72d2-489c-b6b2-6e86347ce4ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7068d0f-72d2-489c-b6b2-6e86347ce4ea", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235041355800, "endTime": 150235090850200}, "additional": {"logType": "info", "children": ["1f6cf2b3-c806-4c19-909d-4e508872972a", "f2874ba8-09ea-4c8f-bd20-23fb0499d7cb", "53ecfd07-24d4-439d-acb3-630d415e5430"], "durationId": "f51185aa-be8c-4612-bf93-6d86e2c49a04", "parent": "8c8ae6b3-502d-4541-bd48-a0a29960fe85"}}, {"head": {"id": "9a649f59-dbe7-4b58-8916-19606417a325", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 150235041494600, "endTime": 150235041509900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f51185aa-be8c-4612-bf93-6d86e2c49a04", "logId": "1f6cf2b3-c806-4c19-909d-4e508872972a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f6cf2b3-c806-4c19-909d-4e508872972a", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235041494600, "endTime": 150235041509900}, "additional": {"logType": "info", "children": [], "durationId": "9a649f59-dbe7-4b58-8916-19606417a325", "parent": "f7068d0f-72d2-489c-b6b2-6e86347ce4ea"}}, {"head": {"id": "0c52d547-0f62-496b-8010-2de0eaecce8c", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 150235041519000, "endTime": 150235065225100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f51185aa-be8c-4612-bf93-6d86e2c49a04", "logId": "f2874ba8-09ea-4c8f-bd20-23fb0499d7cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2874ba8-09ea-4c8f-bd20-23fb0499d7cb", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235041519000, "endTime": 150235065225100}, "additional": {"logType": "info", "children": [], "durationId": "0c52d547-0f62-496b-8010-2de0eaecce8c", "parent": "f7068d0f-72d2-489c-b6b2-6e86347ce4ea"}}, {"head": {"id": "e5dbe6aa-15f1-48b7-8c03-9d08be3a0d5e", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 150235065230800, "endTime": 150235090799400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f51185aa-be8c-4612-bf93-6d86e2c49a04", "logId": "53ecfd07-24d4-439d-acb3-630d415e5430"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53ecfd07-24d4-439d-acb3-630d415e5430", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235065230800, "endTime": 150235090799400}, "additional": {"logType": "info", "children": [], "durationId": "e5dbe6aa-15f1-48b7-8c03-9d08be3a0d5e", "parent": "f7068d0f-72d2-489c-b6b2-6e86347ce4ea"}}, {"head": {"id": "24ba56e9-9f28-405e-ac49-60561d30720d", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 150235090867100, "endTime": 150235278697700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "60c0c48a-cf39-4dd0-8567-5dd00faf8cd5", "logId": "c3d1094e-ebc5-4d60-ae66-0dd7e825bf22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3d1094e-ebc5-4d60-ae66-0dd7e825bf22", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235090867100, "endTime": 150235278697700}, "additional": {"logType": "info", "children": [], "durationId": "24ba56e9-9f28-405e-ac49-60561d30720d", "parent": "8c8ae6b3-502d-4541-bd48-a0a29960fe85"}}, {"head": {"id": "4a822450-3011-4511-8a3b-f0f0ba110d1a", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 150221200056100, "endTime": 150222998354900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "60c0c48a-cf39-4dd0-8567-5dd00faf8cd5", "logId": "3c19e1e6-ec57-4e04-9f6c-530964e320ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c19e1e6-ec57-4e04-9f6c-530964e320ba", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150221200056100, "endTime": 150222998354900}, "additional": {"logType": "info", "children": [], "durationId": "4a822450-3011-4511-8a3b-f0f0ba110d1a", "parent": "8c8ae6b3-502d-4541-bd48-a0a29960fe85"}}, {"head": {"id": "6b10f76f-f479-4861-873f-f67ff90804af", "name": "default@CompileArkTS work[0] done.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235297547800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c8ae6b3-502d-4541-bd48-a0a29960fe85", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Worker4", "startTime": 150223000413000, "endTime": 150235289938800}, "additional": {"logType": "info", "children": ["af6a3ed5-f748-497e-90f2-b43a13c73714", "edcd4f27-7001-487d-800b-dd2479158408", "ecee5082-0b71-4aa9-80ae-ac73e08cf8c6", "0402de32-ae9c-41da-984a-656c15a3811e", "f7068d0f-72d2-489c-b6b2-6e86347ce4ea", "c3d1094e-ebc5-4d60-ae66-0dd7e825bf22", "3c19e1e6-ec57-4e04-9f6c-530964e320ba"], "durationId": "60c0c48a-cf39-4dd0-8567-5dd00faf8cd5", "parent": "3e576569-508f-4fd3-9e97-bfc8d91b4080"}}, {"head": {"id": "ba047b71-f097-4a24-b5f2-9d086c216d5e", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235297916600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e576569-508f-4fd3-9e97-bfc8d91b4080", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150220059468200, "endTime": 150235298053900, "totalTime": 12355590700}, "additional": {"logType": "info", "children": ["8c8ae6b3-502d-4541-bd48-a0a29960fe85", "c6ebed31-41d2-436a-9b70-6b1661078227"], "durationId": "0ce1e0ba-37d0-40cf-985e-c26f7608cf4d"}}, {"head": {"id": "4164f709-0a4d-43a5-bbf5-a46560b9cf1c", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235308199000, "endTime": 150235311490700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "42f3b0c2-1af2-4ad0-92ee-0aa1b4e901d2", "logId": "5fb23189-99d1-43a8-ae23-6151489277de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42f3b0c2-1af2-4ad0-92ee-0aa1b4e901d2", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235304698200}, "additional": {"logType": "detail", "children": [], "durationId": "4164f709-0a4d-43a5-bbf5-a46560b9cf1c"}}, {"head": {"id": "34fae88a-8565-4a26-9491-852dee196b69", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235305884000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "341d6578-650e-4f17-8913-d31a073a4506", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235306037200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "119cec91-ee14-43fe-9018-b24f70b2e2bb", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235308212300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00bf84b7-4fd4-4f5b-94a6-569829f32067", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235308561300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "745a5a87-78a9-40ed-ac0b-4857c1484eda", "name": "entry:default@GeneratePkgModuleJson is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235309037800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe55886b-b3f9-4ca3-a0d1-1b11a4f8fdc4", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235309144600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdb608c8-9427-4914-a561-6cff6b249e64", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235309216100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f43e479b-a8a4-494b-8cd4-4560af8d22b9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235309252700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b23a0989-6581-4b75-87fd-3c02730939c4", "name": "entry : default@GeneratePkgModuleJson cost memory 0.12592315673828125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235311288700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0398f04-cf2d-4545-b7c6-29c2106299c6", "name": "runTaskFromQueue task cost before running: 18 s 550 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235311430100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fb23189-99d1-43a8-ae23-6151489277de", "name": "Finished :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235308199000, "endTime": 150235311490700, "totalTime": 3205000}, "additional": {"logType": "info", "children": [], "durationId": "4164f709-0a4d-43a5-bbf5-a46560b9cf1c"}}, {"head": {"id": "a3903066-ea75-4dd5-bbf8-55ce8bccd5dd", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235324379300, "endTime": 150235645835500}, "additional": {"children": ["b9399426-b057-40a9-8522-d403a9e4932a", "99d8cedb-4f8f-4895-afae-530a3a7975f5"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "105afa75-7d84-4146-a700-3dca2cb5c211", "logId": "7016038a-a6a4-4ec4-a2a1-e3f25529bed2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "105afa75-7d84-4146-a700-3dca2cb5c211", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235314053300}, "additional": {"logType": "detail", "children": [], "durationId": "a3903066-ea75-4dd5-bbf8-55ce8bccd5dd"}}, {"head": {"id": "e384f882-72cb-4441-ac1a-1b86b6edce2c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235315073700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2acfc194-5d8a-44ad-98c9-ed24baf3d0ff", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235315177300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d18e5146-0cee-470b-a062-1ff6e11da88e", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235324395700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abf24816-aeff-40c8-9c50-0b043f078c48", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235335590600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c75ee4a7-84d4-44bf-b3f3-d8fd2ff602a6", "name": "Incremental task entry:default@PackageHap pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235335778400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb603da3-f8e2-46a2-8da8-3a77509f2861", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235335880800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e12a8a82-6bc9-4d2b-9e07-854a4ea0e51a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235335928800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9399426-b057-40a9-8522-d403a9e4932a", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235337640400, "endTime": 150235340220600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3903066-ea75-4dd5-bbf8-55ce8bccd5dd", "logId": "dbaf8195-2cc7-4c3d-ba8e-57c7cc19cf5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82399cdf-7f99-4e0f-94ec-8c1915283f32", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235340016700}, "additional": {"logType": "debug", "children": [], "durationId": "a3903066-ea75-4dd5-bbf8-55ce8bccd5dd"}}, {"head": {"id": "dbaf8195-2cc7-4c3d-ba8e-57c7cc19cf5f", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235337640400, "endTime": 150235340220600}, "additional": {"logType": "info", "children": [], "durationId": "b9399426-b057-40a9-8522-d403a9e4932a", "parent": "7016038a-a6a4-4ec4-a2a1-e3f25529bed2"}}, {"head": {"id": "99d8cedb-4f8f-4895-afae-530a3a7975f5", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235341072400, "endTime": 150235640755700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3903066-ea75-4dd5-bbf8-55ce8bccd5dd", "logId": "5e069c0d-6943-491d-a468-7b63505e067d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca796b93-229a-441a-8e64-f8293c176d7a", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235640033200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e069c0d-6943-491d-a468-7b63505e067d", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235341072400, "endTime": 150235640748600}, "additional": {"logType": "info", "children": [], "durationId": "99d8cedb-4f8f-4895-afae-530a3a7975f5", "parent": "7016038a-a6a4-4ec4-a2a1-e3f25529bed2"}}, {"head": {"id": "480b16dc-3bdf-458e-b7b1-1135549ae2a0", "name": "entry : default@PackageHap cost memory 0.1727752685546875", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235645569200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b5585cf-a292-45dd-a4e4-36d2fbed5800", "name": "runTaskFromQueue task cost before running: 18 s 884 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235645761300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7016038a-a6a4-4ec4-a2a1-e3f25529bed2", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235324379300, "endTime": 150235645835500, "totalTime": 321354400}, "additional": {"logType": "info", "children": ["dbaf8195-2cc7-4c3d-ba8e-57c7cc19cf5f", "5e069c0d-6943-491d-a468-7b63505e067d"], "durationId": "a3903066-ea75-4dd5-bbf8-55ce8bccd5dd"}}, {"head": {"id": "ccb89b6e-72f4-4b32-b1af-a6db6f2cea2b", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235656039400, "endTime": 150236036894000}, "additional": {"children": ["8d9a5a8c-bd2f-4155-9c44-b87503444bdd", "d6b9676c-0569-4ad6-b08a-48bc9c2ef3ff"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "4e957512-c630-49a9-9a81-3c97a9fc1a79", "logId": "c7d24820-ae05-4ea7-a2b1-6221b66f8160"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e957512-c630-49a9-9a81-3c97a9fc1a79", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235649524400}, "additional": {"logType": "detail", "children": [], "durationId": "ccb89b6e-72f4-4b32-b1af-a6db6f2cea2b"}}, {"head": {"id": "b22695b2-1340-4adc-b035-1e81edb1ca09", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235650721300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4577fea1-7c90-4513-8d5c-dd1b482bdf8a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235650835100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40babf53-8791-4831-ab45-6f0eaaa66836", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235656057900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e458f7ea-6e04-4661-a1d4-c56ef990a09c", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235662870800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f4c4c88-37d3-48b6-8a90-379c1450a228", "name": "Incremental task entry:default@SignHap pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235663075500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83a90554-e1ff-434c-868e-4213b95e0bdb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235663192900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bb22a56-55c9-438e-8d39-9d5ded6fa735", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235663240100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d9a5a8c-bd2f-4155-9c44-b87503444bdd", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235665383400, "endTime": 150235786343700}, "additional": {"children": ["33eb20db-a9e3-4be1-8160-865c9fd6e4c7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccb89b6e-72f4-4b32-b1af-a6db6f2cea2b", "logId": "759aa12c-4306-4ae9-8039-a09a159d8d3c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33eb20db-a9e3-4be1-8160-865c9fd6e4c7", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235690466400, "endTime": 150235785008700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d9a5a8c-bd2f-4155-9c44-b87503444bdd", "logId": "74e7bb93-8fd9-4303-9583-d6050dc01853"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87627fde-3584-410b-aae3-4c2b19989a28", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235701587600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5c456af-58a0-4c34-81ed-45520747dad7", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235784416700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74e7bb93-8fd9-4303-9583-d6050dc01853", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235690466400, "endTime": 150235785008700}, "additional": {"logType": "info", "children": [], "durationId": "33eb20db-a9e3-4be1-8160-865c9fd6e4c7", "parent": "759aa12c-4306-4ae9-8039-a09a159d8d3c"}}, {"head": {"id": "759aa12c-4306-4ae9-8039-a09a159d8d3c", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235665383400, "endTime": 150235786343700}, "additional": {"logType": "info", "children": ["74e7bb93-8fd9-4303-9583-d6050dc01853"], "durationId": "8d9a5a8c-bd2f-4155-9c44-b87503444bdd", "parent": "c7d24820-ae05-4ea7-a2b1-6221b66f8160"}}, {"head": {"id": "d6b9676c-0569-4ad6-b08a-48bc9c2ef3ff", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235787073500, "endTime": 150236036365400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccb89b6e-72f4-4b32-b1af-a6db6f2cea2b", "logId": "dca58aeb-91a0-4ef2-a380-2153f5c37c48"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f079da0-0d76-4280-88c9-8bc7aad7d754", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235789089800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d743c2c-3634-4129-9b3a-8ef033be3464", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236035905800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dca58aeb-91a0-4ef2-a380-2153f5c37c48", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235787073500, "endTime": 150236036365400}, "additional": {"logType": "info", "children": [], "durationId": "d6b9676c-0569-4ad6-b08a-48bc9c2ef3ff", "parent": "c7d24820-ae05-4ea7-a2b1-6221b66f8160"}}, {"head": {"id": "f6f4be36-496e-418b-bfc2-7681e492fc66", "name": "entry : default@SignHap cost memory 0.4363555908203125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236036688800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c7efc87-f091-4d61-b29f-6ee044b33d39", "name": "runTaskFromQueue task cost before running: 19 s 275 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236036828000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7d24820-ae05-4ea7-a2b1-6221b66f8160", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150235656039400, "endTime": 150236036894000, "totalTime": 380760900}, "additional": {"logType": "info", "children": ["759aa12c-4306-4ae9-8039-a09a159d8d3c", "dca58aeb-91a0-4ef2-a380-2153f5c37c48"], "durationId": "ccb89b6e-72f4-4b32-b1af-a6db6f2cea2b"}}, {"head": {"id": "13cf6049-608f-4aac-aab6-2da831dba6f1", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236041333400, "endTime": 150236049840200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "94e7254e-393e-4169-b373-29fee77de88a", "logId": "344016f9-919c-4740-995b-6a94780c9101"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94e7254e-393e-4169-b373-29fee77de88a", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236038985100}, "additional": {"logType": "detail", "children": [], "durationId": "13cf6049-608f-4aac-aab6-2da831dba6f1"}}, {"head": {"id": "5edf78d9-4dad-4dc6-8654-897d5952154c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236040376000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7aef819-c4b2-44e7-a85a-eb0a914fc3b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236040497800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0f366ff-6b83-4d4c-9356-bbf90ef3b6f6", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236041344200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6943e987-4f2b-4089-bb49-815d875a4658", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236049134300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f16feaf3-86fc-4569-a4b0-568a9c3c1a73", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236049332900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14d332b1-11bd-4dd1-8cab-ef1351391e69", "name": "entry : default@CollectDebugSymbol cost memory 0.24930572509765625", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236049544400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41be55f5-e473-4b1c-b510-7fcf33220c66", "name": "runTaskFromQueue task cost before running: 19 s 288 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236049755800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "344016f9-919c-4740-995b-6a94780c9101", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236041333400, "endTime": 150236049840200, "totalTime": 8380800}, "additional": {"logType": "info", "children": [], "durationId": "13cf6049-608f-4aac-aab6-2da831dba6f1"}}, {"head": {"id": "7c94d595-785c-4b0f-943d-b9ba901cac12", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236052844900, "endTime": 150236053434700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "de53ef0c-369c-4a0d-938e-16f30514e3d2", "logId": "3252da69-1edf-4389-bd81-a175b6038cc0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de53ef0c-369c-4a0d-938e-16f30514e3d2", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236052753100}, "additional": {"logType": "detail", "children": [], "durationId": "7c94d595-785c-4b0f-943d-b9ba901cac12"}}, {"head": {"id": "fd643f4b-75fd-4e32-bf7f-912208f421e2", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236052861400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b26ae382-7391-4edb-8852-565086baf903", "name": "entry : assembleHap cost memory 0.0118408203125", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236053174700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7397c056-4788-478e-a461-22886bf9cfb0", "name": "runTaskFromQueue task cost before running: 19 s 291 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236053338800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3252da69-1edf-4389-bd81-a175b6038cc0", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236052844900, "endTime": 150236053434700, "totalTime": 441500}, "additional": {"logType": "info", "children": [], "durationId": "7c94d595-785c-4b0f-943d-b9ba901cac12"}}, {"head": {"id": "80132edd-ed94-48fa-9e2e-9af79bd2da19", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236069471500, "endTime": 150236069704300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "29e27316-3a68-4da1-9b0a-b21b12be6f20", "logId": "37efd1f5-2e5e-46a1-9e42-8b6c17084aad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37efd1f5-2e5e-46a1-9e42-8b6c17084aad", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236069471500, "endTime": 150236069704300}, "additional": {"logType": "info", "children": [], "durationId": "80132edd-ed94-48fa-9e2e-9af79bd2da19"}}, {"head": {"id": "d241d189-bda0-48e1-9819-009d6c2660f8", "name": "BUILD SUCCESSFUL in 19 s 308 ms ", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236069818000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "454a8be7-c44c-452c-8787-bccd09725296", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150216762376800, "endTime": 150236070528200}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 23, "minute": 41, "second": 5}, "completeCommand": "{\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"mode\":\"module\",\"parallel\":true,\"incremental\":true,\"daemon\":true,\"_\":[\"assembleHap\"],\"analyze\":\"normal\"};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "dfbf0c8a-79e3-41b8-8802-b9f0a3b34426", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236070692100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0369ef81-9e92-4a16-8aa3-c4cc1a2cb6b2", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236070978800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6b9e49e-3b61-42eb-8e00-2d71d529ea0d", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236071840600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a9bd533-8701-4bad-a012-72062831f704", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236071948000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "466529bd-022e-4f38-86ee-ec9a1113abd3", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236072001100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19112316-4c24-49e1-82ed-b35f731d58a4", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236072083900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82cbbbc0-dce5-4be9-baba-a7021db37c26", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236072122900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8347afd-7331-4b85-a06d-a1fd65ae1709", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236072798400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db01eca1-eac2-4228-ade3-6c46b943e0ca", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236073543400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc7c79e1-bdd4-4373-b14a-396acbc3ed0a", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236073684700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f0edbb9-fc66-414b-b6c3-bb44c2b7d6b9", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236073812700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2af70273-5c14-4dac-a91e-1ce6d89e31ab", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236073887400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11db8076-bee5-4c73-874d-c6a602a2ab48", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236073941200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69cc8d50-8293-4924-b976-beb0fa009f43", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236075334200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb3254b9-1f4d-4c96-bde3-d4488d1e1538", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236075744000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb273ff4-9564-41f1-b476-2adaf411ea59", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236076084000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81f093b6-71ff-4b84-9a88-ac77208ed5fe", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236076165800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b1fe683-757b-4f00-bb38-48cdddd5f9ab", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236076217900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f523f60-fcbd-4276-9020-1bcb1de4564b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236076263800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c92ffbff-af28-4010-86fa-6e8c0abbc970", "name": "Update task entry:default@CompileResource input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236077347900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b406817f-bcfa-4bcb-80a0-ccaf8ce4d5dc", "name": "Update task entry:default@CompileResource input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236077461800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec63cbb3-e1d3-4072-b6c1-bad203b7fa30", "name": "Update task entry:default@CompileResource input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236079545600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c91f2fa7-f433-4ad5-a2b7-d34484144895", "name": "Update task entry:default@CompileResource input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236079910300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1050588-4652-4ccc-881a-fca7a0e5f3ae", "name": "Update task entry:default@CompileResource output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236080218100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3bc2ec8-cf12-4431-8647-7081fad27ed4", "name": "Update task entry:default@CompileResource output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236090588400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1969e46-77cc-4067-a01f-178e15bae10c", "name": "Update task entry:default@CompileResource output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236090955500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "543260e8-c0f8-44d7-956b-c7f7fa5b744c", "name": "Incremental task entry:default@CompileResource post-execution cost:16 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236091582700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "940f9865-0786-4ef1-9651-758e2b197b96", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236091682100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "631645ac-883e-4bc3-8cc4-6c49808648b7", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236095145500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a43141a-ae78-4e83-a71f-48b6ee2faecc", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236095941700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c46afa1-49de-4fcc-bba7-66401b127a55", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236096383200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10d5de3b-8ef3-496e-8610-765f25f93593", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236096682200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efdd6584-1a99-4348-8be8-627546fa7739", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236096942900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70f70daa-e207-408b-88c9-2b25482572ac", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236097744800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01d3eb07-f296-4c05-97e9-a2e5a23622a4", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236105378100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "754f2901-a063-4c1d-8431-6635a8c231d2", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236105731500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae479bed-7bf7-4f04-befa-7b0ef45ec3bc", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236106189200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "122bf256-d9a4-4053-9463-6d345a322bf5", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236107257400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9874c336-9a7c-4c60-98ac-4f9e1ab62cde", "name": "Incremental task entry:default@CompileArkTS post-execution cost:17 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236108659300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e047d583-9ef8-41f5-a8a6-9631932e665b", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236110847800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "163775ce-ab19-48ba-9430-f23bbbb438a2", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236111681600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0631fa47-deca-4865-b5ce-68c19dfe1589", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236112165500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a3266cf-7d84-40a3-8522-79e63e6d7dcb", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236112475000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8586b1d-0888-4363-911b-9fa6d87c59f7", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236112700900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f76c3f69-8bb0-412a-8956-e3dea5684baf", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236113459900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbbf6cd2-3850-4a9b-8286-7a13ef0fb90b", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236114556300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1ce0b82-669e-42f1-9979-33d6e8c3b276", "name": "Incremental task entry:default@BuildJS post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236114874400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99957520-e75e-49c3-a004-8b760ba49f2a", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236114956700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26581c09-a5b5-499c-846e-38b2cf4df60d", "name": "Update task entry:default@GeneratePkgModuleJson input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236115009500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02ef0f57-a6d8-4bdf-adcd-66f8b8d8928a", "name": "Update task entry:default@GeneratePkgModuleJson output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\package\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236115063900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f20f6c5-80b9-4136-a5fb-0a0c4e08e43b", "name": "Incremental task entry:default@GeneratePkgModuleJson post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236115355300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff446b47-8d31-4374-99c6-a63fbf97eb5d", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236116939200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "045b3aea-865a-4def-baf7-2fabb4a3962d", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236117485400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff3f3ab6-71c0-4a7a-998b-4f321f893169", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236117578600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3a64832-87d9-4962-bced-0101995b5154", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236125168100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "732d3501-68fb-43ea-809c-d67d845e5780", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236125474800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d2d7897-a0e2-49cd-bf25-ba5a504d738f", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236125720600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24726319-aa5b-488a-be38-7b447dd0a249", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236125931100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "797ede95-4fa0-42ef-9770-3fe5c305ec24", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236126436800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8856784e-fac1-498f-9866-a1c362190cc9", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236126626000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7b60ab4-5744-41a8-abd7-1938cdfe8365", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236126822800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aa8f0a1-c6e2-4f8f-b7a4-2cf34ea51189", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236127699300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ca53a08-3ae8-4ea5-b901-13170b6db26a", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236127972800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3ac7023-b322-4d84-93f0-665f032b151e", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236128197000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d77cae1-3ff0-440b-84d6-559b7276fe24", "name": "Incremental task entry:default@PackageHap post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236128445600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd54abeb-de93-4eef-af42-51872dd3c3b8", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236128721200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "492f1924-f8c2-4096-b3e1-8c5e274e4919", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236128956500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a7e4328-e116-4e88-a53a-d9465c7ef5c9", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236129144500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e37e5f9c-0bc7-4165-bf32-3a72bd60d0ab", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236129321000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34843d80-4a04-47ff-a1da-d4409639a31b", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236129396200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec849035-bef1-4ca6-8e64-a6ce6b5ddbc6", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236129617300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4d3f115-73e5-40ae-b00d-9dfeafd516bc", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236131965700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18d53d76-a51b-4f9e-8d73-37809e4dd039", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236132209400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79ce5789-1bdf-43b4-93b9-166671a4f754", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236132680500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d89c749f-1a2a-4c85-ab72-bd2031bea3b4", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 11592, "tid": "Main Thread", "startTime": 150236132927500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}