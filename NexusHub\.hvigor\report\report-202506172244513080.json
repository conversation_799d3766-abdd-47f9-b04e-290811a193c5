{"version": "2.0", "ppid": 30328, "events": [{"head": {"id": "83aefaa5-1c00-4b62-8261-669ec41e052b", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778617966100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c93a6c0-041c-4928-9c54-59a123f560f3", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778619641100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55675cd3-231b-4cde-8b77-65e64270e192", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778619982800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "220c64e2-9a42-49dd-a364-26e2b71567c3", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778620637400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f516ca37-ead9-48f1-8856-95c2efc1aa28", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778621216300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "331ac120-1321-48c4-95cb-44bc176b93bd", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778621421200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7882b7f-65de-46f0-aac8-714b4e75e3da", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778621856700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d74087c-945c-43b0-8005-35c691b118a0", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778657037900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46fc66b1-6917-403b-abf3-842f91c1a9cd", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861525851500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86af6960-8e5e-4419-9eca-0c6cc46ddc3d", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861531594200, "endTime": 146861732571500}, "additional": {"children": ["702a410b-7c4b-4f08-96b0-0de6c903daa8", "ca562822-7750-48b9-aed1-5c3714037ed6", "7c0fc0f5-2055-4327-b6c5-ef404470dc85", "ed238bce-8ca7-413c-9a3f-7ed816965f7d", "5c94c92d-ed98-4290-ab31-5134940a20af", "0bf7d516-f4c5-4ab0-8bfb-d726e26a0cce", "434a74ef-7058-4c58-a031-df2f7b916985"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "28fa4acd-9183-4793-a715-c0acbaef1bcd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "702a410b-7c4b-4f08-96b0-0de6c903daa8", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861531595300, "endTime": 146861543754600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86af6960-8e5e-4419-9eca-0c6cc46ddc3d", "logId": "29899468-7874-41f2-abea-65754a7fe5a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca562822-7750-48b9-aed1-5c3714037ed6", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861543767800, "endTime": 146861731245600}, "additional": {"children": ["c94d7468-d215-4c16-8f57-dfcd6c15433b", "9e80a986-4a24-4b50-85b7-b21a5d5d6b4e", "c99fb5ea-f7b4-4ce9-8b0b-38d6549e2a1f", "979c6159-06c8-4e48-9433-097a900944f9", "1184a4ff-1645-4c54-8adf-cc88c73481f9", "7622156b-03e0-47ca-9799-3aaa7de1a585", "d0940e5e-af29-4da5-adfb-8c115a3d2c61", "95680964-31f3-4a71-ba4e-9d09c161aa9b", "baef3461-90e4-4b39-84f3-a61a7d354e1a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86af6960-8e5e-4419-9eca-0c6cc46ddc3d", "logId": "e823ce87-0194-40d0-a597-e55879cd13fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c0fc0f5-2055-4327-b6c5-ef404470dc85", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861731269600, "endTime": 146861732545800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86af6960-8e5e-4419-9eca-0c6cc46ddc3d", "logId": "54296445-5917-4112-a0f5-fa22ec1daadd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed238bce-8ca7-413c-9a3f-7ed816965f7d", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861732550500, "endTime": 146861732568000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86af6960-8e5e-4419-9eca-0c6cc46ddc3d", "logId": "223782e3-9982-48e1-a7f6-fe42626f2502"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c94c92d-ed98-4290-ab31-5134940a20af", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861534998900, "endTime": 146861535033500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86af6960-8e5e-4419-9eca-0c6cc46ddc3d", "logId": "750f6951-a2d8-4591-954f-ef0128c1b263"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "750f6951-a2d8-4591-954f-ef0128c1b263", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861534998900, "endTime": 146861535033500}, "additional": {"logType": "info", "children": [], "durationId": "5c94c92d-ed98-4290-ab31-5134940a20af", "parent": "28fa4acd-9183-4793-a715-c0acbaef1bcd"}}, {"head": {"id": "0bf7d516-f4c5-4ab0-8bfb-d726e26a0cce", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861539461200, "endTime": 146861539475500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86af6960-8e5e-4419-9eca-0c6cc46ddc3d", "logId": "2be2c722-e037-4997-982d-9d3f8b4e24d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2be2c722-e037-4997-982d-9d3f8b4e24d6", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861539461200, "endTime": 146861539475500}, "additional": {"logType": "info", "children": [], "durationId": "0bf7d516-f4c5-4ab0-8bfb-d726e26a0cce", "parent": "28fa4acd-9183-4793-a715-c0acbaef1bcd"}}, {"head": {"id": "50925f71-057a-4876-b9cd-0973f008aad4", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861539531600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29cc1870-b713-4c65-89cd-a0f95ae1fde6", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861543648200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29899468-7874-41f2-abea-65754a7fe5a4", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861531595300, "endTime": 146861543754600}, "additional": {"logType": "info", "children": [], "durationId": "702a410b-7c4b-4f08-96b0-0de6c903daa8", "parent": "28fa4acd-9183-4793-a715-c0acbaef1bcd"}}, {"head": {"id": "c94d7468-d215-4c16-8f57-dfcd6c15433b", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861547794300, "endTime": 146861547803200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca562822-7750-48b9-aed1-5c3714037ed6", "logId": "3f5e6912-6204-4487-98bc-321fc643e1d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e80a986-4a24-4b50-85b7-b21a5d5d6b4e", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861547816400, "endTime": 146861551580300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca562822-7750-48b9-aed1-5c3714037ed6", "logId": "07af89bf-0dfb-4154-b1f3-00446947d79f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c99fb5ea-f7b4-4ce9-8b0b-38d6549e2a1f", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861551624600, "endTime": 146861624427500}, "additional": {"children": ["b9773323-9dac-4b85-9526-2f9bcafa96d9", "b30bf873-2c08-4300-9f87-caae77c6eadd", "a6be5158-7c89-4a72-90da-906d3fe570dc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca562822-7750-48b9-aed1-5c3714037ed6", "logId": "65e49b0c-c76a-4d6a-8e34-845f67a1d140"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "979c6159-06c8-4e48-9433-097a900944f9", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861624440600, "endTime": 146861645958000}, "additional": {"children": ["0ee776ea-3e2a-4a32-a0d1-96e09903539e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca562822-7750-48b9-aed1-5c3714037ed6", "logId": "b877a470-778f-4996-92c9-e7c4ce096fbc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1184a4ff-1645-4c54-8adf-cc88c73481f9", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861645964300, "endTime": 146861698785700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca562822-7750-48b9-aed1-5c3714037ed6", "logId": "bd31ced7-33d9-497c-958f-1958fa2e56f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7622156b-03e0-47ca-9799-3aaa7de1a585", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861700050600, "endTime": 146861717063400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca562822-7750-48b9-aed1-5c3714037ed6", "logId": "b6530aba-fd53-447b-a5f5-78335b86f957"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0940e5e-af29-4da5-adfb-8c115a3d2c61", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861717089500, "endTime": 146861731095900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca562822-7750-48b9-aed1-5c3714037ed6", "logId": "7c3aa9f5-9542-4167-99b2-5609d3de6241"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95680964-31f3-4a71-ba4e-9d09c161aa9b", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861731115200, "endTime": 146861731233500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca562822-7750-48b9-aed1-5c3714037ed6", "logId": "08da9541-1ee5-4457-9680-1c7873055f53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f5e6912-6204-4487-98bc-321fc643e1d1", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861547794300, "endTime": 146861547803200}, "additional": {"logType": "info", "children": [], "durationId": "c94d7468-d215-4c16-8f57-dfcd6c15433b", "parent": "e823ce87-0194-40d0-a597-e55879cd13fa"}}, {"head": {"id": "07af89bf-0dfb-4154-b1f3-00446947d79f", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861547816400, "endTime": 146861551580300}, "additional": {"logType": "info", "children": [], "durationId": "9e80a986-4a24-4b50-85b7-b21a5d5d6b4e", "parent": "e823ce87-0194-40d0-a597-e55879cd13fa"}}, {"head": {"id": "b9773323-9dac-4b85-9526-2f9bcafa96d9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861552165100, "endTime": 146861552182300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c99fb5ea-f7b4-4ce9-8b0b-38d6549e2a1f", "logId": "eb105d82-0cb8-4b11-88f4-321ca11de21d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb105d82-0cb8-4b11-88f4-321ca11de21d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861552165100, "endTime": 146861552182300}, "additional": {"logType": "info", "children": [], "durationId": "b9773323-9dac-4b85-9526-2f9bcafa96d9", "parent": "65e49b0c-c76a-4d6a-8e34-845f67a1d140"}}, {"head": {"id": "b30bf873-2c08-4300-9f87-caae77c6eadd", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861553674500, "endTime": 146861623805700}, "additional": {"children": ["09d69f15-35f8-404a-92fe-2eab74cbb0f7", "798a5f0f-e893-4b9c-8625-3eedf9b076d8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c99fb5ea-f7b4-4ce9-8b0b-38d6549e2a1f", "logId": "6d1d7b4f-e381-41fe-b9cb-ae7f0826b803"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09d69f15-35f8-404a-92fe-2eab74cbb0f7", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861553675200, "endTime": 146861559801600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b30bf873-2c08-4300-9f87-caae77c6eadd", "logId": "bffcb531-e0ef-4b62-b393-c706f2b9a1b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "798a5f0f-e893-4b9c-8625-3eedf9b076d8", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861559819200, "endTime": 146861623790900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b30bf873-2c08-4300-9f87-caae77c6eadd", "logId": "ab7c4829-1f50-4376-96dd-82145668df33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b742a44-1237-4551-9297-01bba8088c4c", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861553680000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2422882c-fd5f-4587-acde-96a87a2cac98", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861559659700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bffcb531-e0ef-4b62-b393-c706f2b9a1b8", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861553675200, "endTime": 146861559801600}, "additional": {"logType": "info", "children": [], "durationId": "09d69f15-35f8-404a-92fe-2eab74cbb0f7", "parent": "6d1d7b4f-e381-41fe-b9cb-ae7f0826b803"}}, {"head": {"id": "4abb26f0-4caa-4b59-a164-61256df25bd9", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861559841000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aa7dea2-aa7b-4c41-8509-a38702041b58", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861567065100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f81ff26-42a2-42c1-97e2-cab1124f0627", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861567237100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "463bbeb4-f5ab-4f64-9d88-bb585dcae7a6", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861567378000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93072147-6a4d-4d5a-816d-265a1cdd386c", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861567450200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da6ff67d-71e5-4d87-a3db-ef93dcd22cc0", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861568922300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f815d09-e5af-4df9-9882-3720344ef810", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861581085600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59786bcc-2c2d-4aca-8d9a-8014d9ff5e25", "name": "Sdk init in 29 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861602208300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47e11dcb-fb3a-436b-9012-0b006ebe4dae", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861602398700}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 44, "second": 51}, "markType": "other"}}, {"head": {"id": "7719db84-50a1-42e0-84e1-3aeec72ede98", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861602444800}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 44, "second": 51}, "markType": "other"}}, {"head": {"id": "59ac9888-0bd2-482d-ad1c-5b4dfe6d5ef0", "name": "Project task initialization takes 20 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861623544200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c23b123-a9ea-42c5-9cd0-cba16a0d2ecd", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861623666000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51b7e2b5-64f8-4e32-913f-4b3a5b87a43a", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861623713100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de5098fc-6138-4a05-a3de-582d3ded94c4", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861623756600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab7c4829-1f50-4376-96dd-82145668df33", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861559819200, "endTime": 146861623790900}, "additional": {"logType": "info", "children": [], "durationId": "798a5f0f-e893-4b9c-8625-3eedf9b076d8", "parent": "6d1d7b4f-e381-41fe-b9cb-ae7f0826b803"}}, {"head": {"id": "6d1d7b4f-e381-41fe-b9cb-ae7f0826b803", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861553674500, "endTime": 146861623805700}, "additional": {"logType": "info", "children": ["bffcb531-e0ef-4b62-b393-c706f2b9a1b8", "ab7c4829-1f50-4376-96dd-82145668df33"], "durationId": "b30bf873-2c08-4300-9f87-caae77c6eadd", "parent": "65e49b0c-c76a-4d6a-8e34-845f67a1d140"}}, {"head": {"id": "a6be5158-7c89-4a72-90da-906d3fe570dc", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861624385300, "endTime": 146861624411000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c99fb5ea-f7b4-4ce9-8b0b-38d6549e2a1f", "logId": "ef6c3c2b-012e-42ee-9482-952581c4ed4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef6c3c2b-012e-42ee-9482-952581c4ed4f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861624385300, "endTime": 146861624411000}, "additional": {"logType": "info", "children": [], "durationId": "a6be5158-7c89-4a72-90da-906d3fe570dc", "parent": "65e49b0c-c76a-4d6a-8e34-845f67a1d140"}}, {"head": {"id": "65e49b0c-c76a-4d6a-8e34-845f67a1d140", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861551624600, "endTime": 146861624427500}, "additional": {"logType": "info", "children": ["eb105d82-0cb8-4b11-88f4-321ca11de21d", "6d1d7b4f-e381-41fe-b9cb-ae7f0826b803", "ef6c3c2b-012e-42ee-9482-952581c4ed4f"], "durationId": "c99fb5ea-f7b4-4ce9-8b0b-38d6549e2a1f", "parent": "e823ce87-0194-40d0-a597-e55879cd13fa"}}, {"head": {"id": "0ee776ea-3e2a-4a32-a0d1-96e09903539e", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861625019500, "endTime": 146861645948200}, "additional": {"children": ["167f8d7b-1717-461c-aaec-b226f478e57c", "bdb3b486-cf88-4de3-b004-55f9fba313b9", "cc8910e6-18f4-4dd1-ae7b-8697255be522"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "979c6159-06c8-4e48-9433-097a900944f9", "logId": "0c3d8f24-74c1-46a0-a8b8-e75f29e71748"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "167f8d7b-1717-461c-aaec-b226f478e57c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861627604500, "endTime": 146861627620600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0ee776ea-3e2a-4a32-a0d1-96e09903539e", "logId": "1d1fc18d-6fbe-402d-b591-bb368c38d99f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d1fc18d-6fbe-402d-b591-bb368c38d99f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861627604500, "endTime": 146861627620600}, "additional": {"logType": "info", "children": [], "durationId": "167f8d7b-1717-461c-aaec-b226f478e57c", "parent": "0c3d8f24-74c1-46a0-a8b8-e75f29e71748"}}, {"head": {"id": "bdb3b486-cf88-4de3-b004-55f9fba313b9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861629242200, "endTime": 146861644798800}, "additional": {"children": ["d31cb900-d9f6-4b12-9103-082a0fd21874", "7c180943-618c-448f-8272-d6aa230dfd30"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0ee776ea-3e2a-4a32-a0d1-96e09903539e", "logId": "1ccbf021-e326-4468-9cec-a811a474059c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d31cb900-d9f6-4b12-9103-082a0fd21874", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861629243100, "endTime": 146861633300800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bdb3b486-cf88-4de3-b004-55f9fba313b9", "logId": "1e900f0a-dd33-44a0-88f2-95c9fcd1e352"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c180943-618c-448f-8272-d6aa230dfd30", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861633312500, "endTime": 146861644789400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bdb3b486-cf88-4de3-b004-55f9fba313b9", "logId": "a319ea4f-8951-45c0-95fa-144d91783c7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27363013-c26d-42ab-bb72-21f9b9a88f48", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861629248000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11c3c182-1a63-4d8c-9c53-862eca02c854", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861633193400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e900f0a-dd33-44a0-88f2-95c9fcd1e352", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861629243100, "endTime": 146861633300800}, "additional": {"logType": "info", "children": [], "durationId": "d31cb900-d9f6-4b12-9103-082a0fd21874", "parent": "1ccbf021-e326-4468-9cec-a811a474059c"}}, {"head": {"id": "6290aa9a-c728-4603-9a87-d40dc946fe2c", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861633323200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2eaa17d-033b-4cb1-b153-320184398070", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861640493300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "831b62ba-b1f0-4d94-a84b-23a1a4b241b4", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861640636000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f342875-e750-4065-b6af-3ad89ab418ae", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861640862600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5c1f26b-b62c-4f46-b6a4-1e6fa2e20965", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861640971800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36c1a51b-16a5-4dd1-9ae1-fa3b730f2187", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861641012400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4155956a-0d2f-4f62-8ce2-10faf02b0350", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861641047200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d57e957-8b65-44e9-9a7a-3ee337187662", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861641096000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "882956dc-ea18-4192-829a-d2d5817a9bb6", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861641136400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13d4c762-e3c9-4bfe-ab97-b3d1014f533a", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861641508200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc80aaad-6237-42c3-a6c7-f7b7f7a2144e", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861641649500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05ea007e-6fc8-4b09-a98d-653c8d843335", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861641703300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b608983-6b7a-4956-bad3-ebbb2373a07a", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861641737400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fec74763-3912-4ae8-9cff-2063a5020ad1", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861641785600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44d81fb9-be29-448b-97ca-bb3a102d130c", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861641825300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "483c9f22-5659-4d27-a39b-718dc1f3df0f", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861641908000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9875018a-8131-424f-af0d-e87a7b22c2e5", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861641991500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d40cb89a-1035-4933-b841-3271d62e705b", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861642026700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a3a9278-09ad-4dbd-a100-9efaed0f69c0", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861642054500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8a4f1a5-3fd7-47a8-8f61-cdfff5aafde8", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861642096800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58a21262-007c-4615-8bf6-4916d9a6d513", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861644581900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33493970-e3b2-4a66-9319-eda25daac66d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861644683100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ce336b4-4c78-4118-ac08-4f5867759651", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861644725000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e65515e-3aaf-4c40-be35-0a884b47fa04", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861644754900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a319ea4f-8951-45c0-95fa-144d91783c7b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861633312500, "endTime": 146861644789400}, "additional": {"logType": "info", "children": [], "durationId": "7c180943-618c-448f-8272-d6aa230dfd30", "parent": "1ccbf021-e326-4468-9cec-a811a474059c"}}, {"head": {"id": "1ccbf021-e326-4468-9cec-a811a474059c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861629242200, "endTime": 146861644798800}, "additional": {"logType": "info", "children": ["1e900f0a-dd33-44a0-88f2-95c9fcd1e352", "a319ea4f-8951-45c0-95fa-144d91783c7b"], "durationId": "bdb3b486-cf88-4de3-b004-55f9fba313b9", "parent": "0c3d8f24-74c1-46a0-a8b8-e75f29e71748"}}, {"head": {"id": "cc8910e6-18f4-4dd1-ae7b-8697255be522", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861645916000, "endTime": 146861645929600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0ee776ea-3e2a-4a32-a0d1-96e09903539e", "logId": "c242d8b6-aa3b-454c-b02c-1272b8b36f96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c242d8b6-aa3b-454c-b02c-1272b8b36f96", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861645916000, "endTime": 146861645929600}, "additional": {"logType": "info", "children": [], "durationId": "cc8910e6-18f4-4dd1-ae7b-8697255be522", "parent": "0c3d8f24-74c1-46a0-a8b8-e75f29e71748"}}, {"head": {"id": "0c3d8f24-74c1-46a0-a8b8-e75f29e71748", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861625019500, "endTime": 146861645948200}, "additional": {"logType": "info", "children": ["1d1fc18d-6fbe-402d-b591-bb368c38d99f", "1ccbf021-e326-4468-9cec-a811a474059c", "c242d8b6-aa3b-454c-b02c-1272b8b36f96"], "durationId": "0ee776ea-3e2a-4a32-a0d1-96e09903539e", "parent": "b877a470-778f-4996-92c9-e7c4ce096fbc"}}, {"head": {"id": "b877a470-778f-4996-92c9-e7c4ce096fbc", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861624440600, "endTime": 146861645958000}, "additional": {"logType": "info", "children": ["0c3d8f24-74c1-46a0-a8b8-e75f29e71748"], "durationId": "979c6159-06c8-4e48-9433-097a900944f9", "parent": "e823ce87-0194-40d0-a597-e55879cd13fa"}}, {"head": {"id": "db208b30-6391-4640-8c60-eb6d2400531a", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861661162100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a14d923b-3760-4b15-8361-0a21d0b64c4e", "name": "hvigorfile, resolve hvigorfile dependencies in 53 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861698633600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd31ced7-33d9-497c-958f-1958fa2e56f5", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861645964300, "endTime": 146861698785700}, "additional": {"logType": "info", "children": [], "durationId": "1184a4ff-1645-4c54-8adf-cc88c73481f9", "parent": "e823ce87-0194-40d0-a597-e55879cd13fa"}}, {"head": {"id": "baef3461-90e4-4b39-84f3-a61a7d354e1a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861699694700, "endTime": 146861700034400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca562822-7750-48b9-aed1-5c3714037ed6", "logId": "44eb3426-7482-4f4c-822f-d8842645c99c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09dc25db-30d2-4146-8661-dc641fd0d4c1", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861699733900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44eb3426-7482-4f4c-822f-d8842645c99c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861699694700, "endTime": 146861700034400}, "additional": {"logType": "info", "children": [], "durationId": "baef3461-90e4-4b39-84f3-a61a7d354e1a", "parent": "e823ce87-0194-40d0-a597-e55879cd13fa"}}, {"head": {"id": "1f45facc-ad27-4a9f-8dfa-818e996e277f", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861701929200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68715df2-f8a4-4a1e-b8b0-dc20a3272e08", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861716002200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6530aba-fd53-447b-a5f5-78335b86f957", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861700050600, "endTime": 146861717063400}, "additional": {"logType": "info", "children": [], "durationId": "7622156b-03e0-47ca-9799-3aaa7de1a585", "parent": "e823ce87-0194-40d0-a597-e55879cd13fa"}}, {"head": {"id": "d24a6b8a-1b41-494a-bfd5-0c07ce248b0e", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861717110400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78e4ebd1-e48f-44d4-8ecd-b5b7744798ff", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861724441800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01e0478b-2a76-4ce5-a365-1a8c0a1241a6", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861724573000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "237b8a5a-4c13-47ae-b4ee-22ce31836148", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861724728100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fbf2ed1-dc29-42c2-b5fe-ca3ac702ad63", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861727562600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1385b5fc-226d-48e0-a9f9-0031a1b53db0", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861727661100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c3aa9f5-9542-4167-99b2-5609d3de6241", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861717089500, "endTime": 146861731095900}, "additional": {"logType": "info", "children": [], "durationId": "d0940e5e-af29-4da5-adfb-8c115a3d2c61", "parent": "e823ce87-0194-40d0-a597-e55879cd13fa"}}, {"head": {"id": "feafc297-e444-43c1-82ff-c94697ef053d", "name": "Configuration phase cost:184 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861731137700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08da9541-1ee5-4457-9680-1c7873055f53", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861731115200, "endTime": 146861731233500}, "additional": {"logType": "info", "children": [], "durationId": "95680964-31f3-4a71-ba4e-9d09c161aa9b", "parent": "e823ce87-0194-40d0-a597-e55879cd13fa"}}, {"head": {"id": "e823ce87-0194-40d0-a597-e55879cd13fa", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861543767800, "endTime": 146861731245600}, "additional": {"logType": "info", "children": ["3f5e6912-6204-4487-98bc-321fc643e1d1", "07af89bf-0dfb-4154-b1f3-00446947d79f", "65e49b0c-c76a-4d6a-8e34-845f67a1d140", "b877a470-778f-4996-92c9-e7c4ce096fbc", "bd31ced7-33d9-497c-958f-1958fa2e56f5", "b6530aba-fd53-447b-a5f5-78335b86f957", "7c3aa9f5-9542-4167-99b2-5609d3de6241", "08da9541-1ee5-4457-9680-1c7873055f53", "44eb3426-7482-4f4c-822f-d8842645c99c"], "durationId": "ca562822-7750-48b9-aed1-5c3714037ed6", "parent": "28fa4acd-9183-4793-a715-c0acbaef1bcd"}}, {"head": {"id": "434a74ef-7058-4c58-a031-df2f7b916985", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861732510000, "endTime": 146861732528500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86af6960-8e5e-4419-9eca-0c6cc46ddc3d", "logId": "91339fb9-bfd6-446d-8be3-2794bebdaae3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91339fb9-bfd6-446d-8be3-2794bebdaae3", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861732510000, "endTime": 146861732528500}, "additional": {"logType": "info", "children": [], "durationId": "434a74ef-7058-4c58-a031-df2f7b916985", "parent": "28fa4acd-9183-4793-a715-c0acbaef1bcd"}}, {"head": {"id": "54296445-5917-4112-a0f5-fa22ec1daadd", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861731269600, "endTime": 146861732545800}, "additional": {"logType": "info", "children": [], "durationId": "7c0fc0f5-2055-4327-b6c5-ef404470dc85", "parent": "28fa4acd-9183-4793-a715-c0acbaef1bcd"}}, {"head": {"id": "223782e3-9982-48e1-a7f6-fe42626f2502", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861732550500, "endTime": 146861732568000}, "additional": {"logType": "info", "children": [], "durationId": "ed238bce-8ca7-413c-9a3f-7ed816965f7d", "parent": "28fa4acd-9183-4793-a715-c0acbaef1bcd"}}, {"head": {"id": "28fa4acd-9183-4793-a715-c0acbaef1bcd", "name": "init", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861531594200, "endTime": 146861732571500}, "additional": {"logType": "info", "children": ["29899468-7874-41f2-abea-65754a7fe5a4", "e823ce87-0194-40d0-a597-e55879cd13fa", "54296445-5917-4112-a0f5-fa22ec1daadd", "223782e3-9982-48e1-a7f6-fe42626f2502", "750f6951-a2d8-4591-954f-ef0128c1b263", "2be2c722-e037-4997-982d-9d3f8b4e24d6", "91339fb9-bfd6-446d-8be3-2794bebdaae3"], "durationId": "86af6960-8e5e-4419-9eca-0c6cc46ddc3d"}}, {"head": {"id": "be963e9d-c0b1-4c7c-b014-6fcad0df8efe", "name": "Configuration task cost before running: 204 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861732737100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c65eb80-f697-4572-ac00-1ee5aaad7784", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861742058200, "endTime": 146861754465600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "ed0d1281-0586-4bdf-be4d-5bc2566aa4cd", "logId": "48d60687-d947-4e4c-9f6b-7a06f733951d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed0d1281-0586-4bdf-be4d-5bc2566aa4cd", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861734205900}, "additional": {"logType": "detail", "children": [], "durationId": "3c65eb80-f697-4572-ac00-1ee5aaad7784"}}, {"head": {"id": "c5602211-391b-465b-b7a6-c4e718437476", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861734978000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fa2e6c9-b810-4150-a663-58e69c3b1cf0", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861735128800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7241442-7ead-4133-abcc-2cd4368e0d98", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861735937300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d778c8cc-73fe-45f1-860a-c896e299ed16", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861736928600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9278c576-4f6a-4e39-9ba3-0451b2b92c32", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861738176900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32438888-e91e-4988-97d9-1f2b7a94c58d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861738297300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "befb1475-20dc-4407-ae14-acd18b64da20", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861742098800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f69ad87-62f2-4d93-bca6-525c4076b41f", "name": "Incremental task entry:default@PreBuild pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861754234900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f514e3fe-045e-4c72-81b5-c506b011d182", "name": "entry : default@PreBuild cost memory 0.3246307373046875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861754396500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48d60687-d947-4e4c-9f6b-7a06f733951d", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861742058200, "endTime": 146861754465600}, "additional": {"logType": "info", "children": [], "durationId": "3c65eb80-f697-4572-ac00-1ee5aaad7784"}}, {"head": {"id": "2c6cb89d-23e0-4f65-b09c-a23ca2ef4249", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861762523800, "endTime": 146861764799200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2a6669b2-fc97-4a2e-bb80-aba35af60ba7", "logId": "c37b1db5-462f-4f4c-8411-252ad15c63fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a6669b2-fc97-4a2e-bb80-aba35af60ba7", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861759570900}, "additional": {"logType": "detail", "children": [], "durationId": "2c6cb89d-23e0-4f65-b09c-a23ca2ef4249"}}, {"head": {"id": "dbbe4096-e0df-4e97-9382-d49d62fab88e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861761655700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2cd4f19-0ceb-4901-801a-cb4d40bea90c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861761777300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ef26216-b855-43bd-a79d-23c1ead0a7f4", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861762536300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bf84b0e-a4fc-46ba-9a03-fc9df6e86eff", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861763380600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a857fdd8-31a9-4be6-9f74-d52e4f0830a3", "name": "entry : default@CreateModuleInfo cost memory 0.06063079833984375", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861764608200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65a46c17-d0c8-4b4b-9076-59fb3ae6bac2", "name": "runTaskFromQueue task cost before running: 236 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861764740900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c37b1db5-462f-4f4c-8411-252ad15c63fb", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861762523800, "endTime": 146861764799200, "totalTime": 2192400}, "additional": {"logType": "info", "children": [], "durationId": "2c6cb89d-23e0-4f65-b09c-a23ca2ef4249"}}, {"head": {"id": "8174e05e-63d1-4081-8d4a-08225f4de6d5", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861773049900, "endTime": 146861775066400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "abdd3bc6-2c44-48bc-8c91-9507dde9dbb9", "logId": "b4e1f76d-64df-4fd6-b986-5ba78ab72fa6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "abdd3bc6-2c44-48bc-8c91-9507dde9dbb9", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861767404500}, "additional": {"logType": "detail", "children": [], "durationId": "8174e05e-63d1-4081-8d4a-08225f4de6d5"}}, {"head": {"id": "9c2514da-44bc-4373-9ad8-701e595de30e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861768870000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4b24ce2-0efd-4eab-900d-753bb994fad0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861769006000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a48196a6-204a-4f98-82d7-32dab4bc2566", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861773066700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8589cf45-48e8-45d8-815d-08bddad787c7", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861774004300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6be0e751-f3b4-4764-baa5-2706edf0aec9", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861774886800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03d89fd1-24db-4985-89d9-01b1d757deff", "name": "entry : default@GenerateMetadata cost memory 0.10131072998046875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861775002800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4e1f76d-64df-4fd6-b986-5ba78ab72fa6", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861773049900, "endTime": 146861775066400}, "additional": {"logType": "info", "children": [], "durationId": "8174e05e-63d1-4081-8d4a-08225f4de6d5"}}, {"head": {"id": "70f7f58a-4b76-4c4b-a485-49f3d44d5d14", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861779851100, "endTime": 146861780188400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3cf642ac-8449-4798-bbd6-1ed7bdc7c855", "logId": "2dce67c7-612b-4123-84e2-5c27cb84e236"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cf642ac-8449-4798-bbd6-1ed7bdc7c855", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861778270900}, "additional": {"logType": "detail", "children": [], "durationId": "70f7f58a-4b76-4c4b-a485-49f3d44d5d14"}}, {"head": {"id": "28132c2b-63ab-45ef-9eed-4ef95114cca3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861779596800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cc5e2ec-9519-48fe-86cc-c968f24c3b8b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861779715100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f8607fc-d9c7-4c0f-bb47-b4f853cce080", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861779858400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f17273d-91b9-4b7c-afa4-07959bd6dfc4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861779947800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be294c10-5f49-4476-aec1-50592fedcf2e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861780019200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f41ebddf-8453-4817-8f6f-4ece8d832b79", "name": "entry : default@ConfigureCmake cost memory 0.03772735595703125", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861780082100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb79ca6a-2ce6-4895-833c-7711b9316252", "name": "runTaskFromQueue task cost before running: 252 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861780146000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dce67c7-612b-4123-84e2-5c27cb84e236", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861779851100, "endTime": 146861780188400, "totalTime": 275700}, "additional": {"logType": "info", "children": [], "durationId": "70f7f58a-4b76-4c4b-a485-49f3d44d5d14"}}, {"head": {"id": "8add6eb4-5152-4665-8c82-6a14caed5c96", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861783617400, "endTime": 146861785754400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "ac521604-630d-4339-9804-63d21515ad7a", "logId": "0c8eafd8-1784-4807-932a-79d7f683beb7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac521604-630d-4339-9804-63d21515ad7a", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861781897900}, "additional": {"logType": "detail", "children": [], "durationId": "8add6eb4-5152-4665-8c82-6a14caed5c96"}}, {"head": {"id": "d6032cf8-ae39-441d-a73d-7967239a8427", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861782870500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96bac1e2-9b43-41a1-9c7d-646efa15805f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861782974900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fe34328-a513-4384-ad08-b564fe79bc9e", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861783631000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7280449-50de-448d-aab9-57ec2be35faf", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861785577600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f85ec575-f6a2-4ad3-8cbe-f5028321af08", "name": "entry : default@MergeProfile cost memory 0.11850738525390625", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861785697100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c8eafd8-1784-4807-932a-79d7f683beb7", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861783617400, "endTime": 146861785754400}, "additional": {"logType": "info", "children": [], "durationId": "8add6eb4-5152-4665-8c82-6a14caed5c96"}}, {"head": {"id": "90f5d34d-eca6-4a53-9580-40a3bfec670f", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861789183100, "endTime": 146861791873400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "39813565-e1c8-4142-9bb7-c7802b86e1c3", "logId": "9640e73c-f9e7-4978-a5be-8649736c46f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39813565-e1c8-4142-9bb7-c7802b86e1c3", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861787288100}, "additional": {"logType": "detail", "children": [], "durationId": "90f5d34d-eca6-4a53-9580-40a3bfec670f"}}, {"head": {"id": "9cb65ffe-d32e-49ee-85de-98ea935e0d3b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861788324100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc637028-7b0a-4673-96f7-d9d676eb2aca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861788436100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31391507-ee41-4148-a924-aaae354865ff", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861789193100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f84a19a2-8697-4945-9145-442b6d219ae8", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861790180200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eda3d77-1ddc-424d-8d35-c49b74bdc4e8", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861791683200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "200090fa-73c9-476d-bc62-39983c739126", "name": "entry : default@CreateBuildProfile cost memory 0.108306884765625", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861791808000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9640e73c-f9e7-4978-a5be-8649736c46f1", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861789183100, "endTime": 146861791873400}, "additional": {"logType": "info", "children": [], "durationId": "90f5d34d-eca6-4a53-9580-40a3bfec670f"}}, {"head": {"id": "3450a017-3c50-4b0a-8324-f780dd081c53", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861795399700, "endTime": 146861795929700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "8e0c9483-2827-4331-b6bc-f78ddeb7c403", "logId": "7d978483-ef7a-48ea-9086-b0259448e857"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e0c9483-2827-4331-b6bc-f78ddeb7c403", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861793430400}, "additional": {"logType": "detail", "children": [], "durationId": "3450a017-3c50-4b0a-8324-f780dd081c53"}}, {"head": {"id": "abdaf661-cbd0-44af-bb5a-a7d563d532bd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861794419200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fdb6c50-da55-4328-82c1-77ba6e2834c2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861794589900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7658682-21be-4b23-8984-266b3e74123c", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861795410700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fcc9298-b275-4c36-8692-1ce42345733b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861795544700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fdd4900-e3d5-4dc7-8bd7-6ceec8f9ac9e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861795592800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67726059-e9c8-420f-a851-b49192e7746b", "name": "entry : default@PreCheckSyscap cost memory 0.04126739501953125", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861795794200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cd0c1d8-87a6-4190-848f-4b56ce63dd66", "name": "runTaskFromQueue task cost before running: 267 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861795885000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d978483-ef7a-48ea-9086-b0259448e857", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861795399700, "endTime": 146861795929700, "totalTime": 464400}, "additional": {"logType": "info", "children": [], "durationId": "3450a017-3c50-4b0a-8324-f780dd081c53"}}, {"head": {"id": "42ac7272-cb82-4896-bd02-142cb633631c", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861800563900, "endTime": 146861806909400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7268adb7-1fe6-45ed-a7df-b8786032376d", "logId": "3f125200-8c6b-48e9-85c8-a2ff798e7ca5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7268adb7-1fe6-45ed-a7df-b8786032376d", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861797863000}, "additional": {"logType": "detail", "children": [], "durationId": "42ac7272-cb82-4896-bd02-142cb633631c"}}, {"head": {"id": "bd71de40-5ce7-4514-9956-b6270f000d69", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861799037400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "974d7ec9-1f9b-475c-8bf0-2fac95c84d1b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861799155500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5640e48e-86ce-4b10-a5f7-f7160f2d27b6", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861800578300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd6f8e11-e7e5-4724-90b9-3938742890b0", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861805968800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b86a69ff-d98a-466b-bb21-35b72dabf556", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861806701100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3d8033b-e3e7-47ca-b456-835a36fbd6d0", "name": "entry : default@GeneratePkgContextInfo cost memory 0.2396697998046875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861806843600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f125200-8c6b-48e9-85c8-a2ff798e7ca5", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861800563900, "endTime": 146861806909400}, "additional": {"logType": "info", "children": [], "durationId": "42ac7272-cb82-4896-bd02-142cb633631c"}}, {"head": {"id": "473b7ccd-8077-4559-a509-c64c215184a6", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861815349800, "endTime": 146861817619900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "48000ffc-ce26-40f3-9852-e6cb8812c0dc", "logId": "6334c5ca-caa2-4861-a4a6-e9722cb58183"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48000ffc-ce26-40f3-9852-e6cb8812c0dc", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861808559200}, "additional": {"logType": "detail", "children": [], "durationId": "473b7ccd-8077-4559-a509-c64c215184a6"}}, {"head": {"id": "ae3c5a2b-cf0e-463c-9acb-b682abc67223", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861810119000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83d2d98f-6767-4807-823d-275915d02415", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861810240600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "562b77bd-b38f-4c01-abc7-7f38ba008f4f", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861815368200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1847d74-868a-4f1b-8464-a69e57c58359", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861817121000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "640215c3-0c2e-4745-9687-98e8bee758c1", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861817263300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b27ed776-6b58-46ec-9c03-a1638f3660c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861817368500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dc84012-f569-4b26-bac8-b5c51c0cfce6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861817427200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4a003a9-592f-4fcc-a731-ba8f3cd88c51", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12244415283203125", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861817502100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c749223a-52ba-4784-a34e-c17a17203446", "name": "runTaskFromQueue task cost before running: 289 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861817568000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6334c5ca-caa2-4861-a4a6-e9722cb58183", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861815349800, "endTime": 146861817619900, "totalTime": 2203300}, "additional": {"logType": "info", "children": [], "durationId": "473b7ccd-8077-4559-a509-c64c215184a6"}}, {"head": {"id": "7f9eba05-bd86-42f4-97ab-f7998a831c93", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861821677200, "endTime": 146861822049100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c3a90622-08af-4eb0-bde7-e0d62d753285", "logId": "a58fe33f-1c5d-4ef0-b1a6-49318ec30e5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3a90622-08af-4eb0-bde7-e0d62d753285", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861819852300}, "additional": {"logType": "detail", "children": [], "durationId": "7f9eba05-bd86-42f4-97ab-f7998a831c93"}}, {"head": {"id": "3fb02dba-e086-47f9-8647-ae09fe679b01", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861820818500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a25f54e-e237-468c-b698-a90a5ec1e9cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861820925700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55212d5b-a0a0-450b-af77-a221deb114f0", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861821687900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a0a9794-1d36-4fe1-80d9-7f203b0d696a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861821823000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99102473-65b7-49c8-b0b5-ccd0283c6725", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861821867100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "579bb853-3303-4b23-beee-b0f7a8645fbd", "name": "entry : default@BuildNativeWithCmake cost memory 0.03887939453125", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861821936000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81abdcf1-d152-4f8e-886d-8e9f181f188e", "name": "runTaskFromQueue task cost before running: 294 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861822008300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a58fe33f-1c5d-4ef0-b1a6-49318ec30e5f", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861821677200, "endTime": 146861822049100, "totalTime": 312800}, "additional": {"logType": "info", "children": [], "durationId": "7f9eba05-bd86-42f4-97ab-f7998a831c93"}}, {"head": {"id": "8b3a35a9-8809-4b6a-9d0c-4c708296e932", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861825260000, "endTime": 146861828814300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "70fc2a49-24b0-4fda-8515-bd6ca08a6c69", "logId": "aaa786c0-10fa-4249-9913-842ef129327a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70fc2a49-24b0-4fda-8515-bd6ca08a6c69", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861823499200}, "additional": {"logType": "detail", "children": [], "durationId": "8b3a35a9-8809-4b6a-9d0c-4c708296e932"}}, {"head": {"id": "fdb11709-5769-4a77-b51d-a3456adb6072", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861824426500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a5ea8b9-382d-4c34-8877-8140e117e18f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861824549200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7889b67c-4bfb-41b4-a7db-1ea04e4c247e", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861825268800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a193edb6-eba1-4c37-8ba0-0a549cdec622", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861828626500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcea58ef-8840-4b0e-b477-d7a83d4842ca", "name": "entry : default@MakePackInfo cost memory 0.16545867919921875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861828753200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaa786c0-10fa-4249-9913-842ef129327a", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861825260000, "endTime": 146861828814300}, "additional": {"logType": "info", "children": [], "durationId": "8b3a35a9-8809-4b6a-9d0c-4c708296e932"}}, {"head": {"id": "1f2529bb-2000-4cb0-ae13-0d50673eb285", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861833203200, "endTime": 146861837568300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c85322fa-56fa-4859-b0d6-06556520c401", "logId": "503a2f10-0e18-4056-a78b-b905f7aee525"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c85322fa-56fa-4859-b0d6-06556520c401", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861830811600}, "additional": {"logType": "detail", "children": [], "durationId": "1f2529bb-2000-4cb0-ae13-0d50673eb285"}}, {"head": {"id": "6dd7580e-920f-4ba5-badf-d514c3b02bc3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861831895500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79e70bb9-c49e-4ab8-9b49-5e671e7fee70", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861831997200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d39360a6-cf3e-4a5a-9feb-73543858745a", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861833214100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ba526af-1a51-42d3-9387-f1af602cf573", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861833411100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "641bc4a2-78dd-4355-947a-9a5c21979a99", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861834145100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d9b69d3-9976-46fe-b47e-c9f001f6f5b9", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861837343400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e9a5638-1f52-4478-9f2f-e9676b613052", "name": "entry : default@SyscapTransform cost memory 0.15012359619140625", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861837490600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "503a2f10-0e18-4056-a78b-b905f7aee525", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861833203200, "endTime": 146861837568300}, "additional": {"logType": "info", "children": [], "durationId": "1f2529bb-2000-4cb0-ae13-0d50673eb285"}}, {"head": {"id": "15522f5a-171c-4432-a5f3-3508946255cc", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861842763400, "endTime": 146861845083400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "8202d875-d78d-48b3-8f24-e5ebcbd6928f", "logId": "9805fa4b-a7df-4da3-8335-907926930b8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8202d875-d78d-48b3-8f24-e5ebcbd6928f", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861839480300}, "additional": {"logType": "detail", "children": [], "durationId": "15522f5a-171c-4432-a5f3-3508946255cc"}}, {"head": {"id": "02de8357-ddfc-40ca-85dd-6f0128f7443f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861841103200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c357d33-f55b-4d74-b8d5-269d86cddbb6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861841266700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d71ef8bf-89bb-4a0c-bfa8-e6e9a1bfa350", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861842777000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4d5aad4-6441-42fb-a650-148040848911", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861844858800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6306583d-2091-44c9-a0d3-24f7b099258e", "name": "entry : default@ProcessProfile cost memory 0.12520599365234375", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861845016800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9805fa4b-a7df-4da3-8335-907926930b8b", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861842763400, "endTime": 146861845083400}, "additional": {"logType": "info", "children": [], "durationId": "15522f5a-171c-4432-a5f3-3508946255cc"}}, {"head": {"id": "c3230b29-6b27-44c9-90c3-2ee6d9966289", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861849917200, "endTime": 146861856536200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fb652500-240d-4aab-a805-f65b6cd86844", "logId": "de57801a-1001-47bc-aab9-8af0206cecc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb652500-240d-4aab-a805-f65b6cd86844", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861846797200}, "additional": {"logType": "detail", "children": [], "durationId": "c3230b29-6b27-44c9-90c3-2ee6d9966289"}}, {"head": {"id": "d17a9fd5-a819-42c9-a625-4ee09c7fab02", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861848043000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c5a8882-fbe6-4064-beea-614d290d0b3d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861848158700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "373f798f-c988-4b2e-ab17-018003ffebd9", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861849936500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3479459-144d-4e6b-8885-e7942a00921c", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861856301200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "482012a1-d54d-41cf-8167-084277c552a5", "name": "entry : default@ProcessRouterMap cost memory 0.23564910888671875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861856456900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de57801a-1001-47bc-aab9-8af0206cecc9", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861849917200, "endTime": 146861856536200}, "additional": {"logType": "info", "children": [], "durationId": "c3230b29-6b27-44c9-90c3-2ee6d9966289"}}, {"head": {"id": "9d47b662-9a92-4698-b7bf-aad29c579a72", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861860707000, "endTime": 146861866380000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "dae3ee71-4a75-44f4-9725-97756606ee3e", "logId": "91cf1d8e-d033-44c8-a2ae-1d2e5186f0fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dae3ee71-4a75-44f4-9725-97756606ee3e", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861859476200}, "additional": {"logType": "detail", "children": [], "durationId": "9d47b662-9a92-4698-b7bf-aad29c579a72"}}, {"head": {"id": "7c6ece5d-cb0a-4828-925d-349e4e3fd6b9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861860511500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03d7592b-91ca-4a5c-8b02-29d9aae390e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861860621200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81317c6b-fe9d-4bba-87f0-b0305299e998", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861860742200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aae9f1b-e70e-4be5-8f8d-0bda975c3c85", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861860853000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25a5121f-e819-4e67-a4e4-ddbe3371570d", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861864899700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1df69b4-400e-45de-8dc5-595a666d629b", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861865046500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f545a72-9914-4fa3-be3d-7819f666cee3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861865138400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01c72edc-fa81-4d51-87a8-ac028ff6a381", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861865176100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37dd6f59-4cba-4265-ac61-de01e7e15464", "name": "entry : default@ProcessStartupConfig cost memory 0.2639007568359375", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861866200800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d0b8787-e281-417a-bcf4-3b975cd476f7", "name": "runTaskFromQueue task cost before running: 338 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861866322500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91cf1d8e-d033-44c8-a2ae-1d2e5186f0fc", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861860707000, "endTime": 146861866380000, "totalTime": 5591700}, "additional": {"logType": "info", "children": [], "durationId": "9d47b662-9a92-4698-b7bf-aad29c579a72"}}, {"head": {"id": "84ae6d66-28bd-49a8-8f13-859cef1964d4", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861872376000, "endTime": 146861874300300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "883dc9e5-c52d-46c1-b70d-d665ef2ca3c0", "logId": "36522ea2-2165-421b-994b-02d14566a913"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "883dc9e5-c52d-46c1-b70d-d665ef2ca3c0", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861870164600}, "additional": {"logType": "detail", "children": [], "durationId": "84ae6d66-28bd-49a8-8f13-859cef1964d4"}}, {"head": {"id": "4ff03261-9b77-4b07-8756-4f1f9707565d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861871417900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d9a921c-8772-4f71-b173-065f368c9025", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861871542000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91b793ca-2cd9-4584-9082-1a878457bb6d", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861872399500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "813b84f1-deac-4df4-985a-8c531cd5d3a1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861872888400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09d3d953-5fc7-49ec-9509-f08bc43b955d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861873047800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f4a5e0d-ee37-44e0-8f3c-d750771196d7", "name": "entry : default@BuildNativeWithNinja cost memory 0.05956268310546875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861874087900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbc80f38-a955-400e-a4fe-96957a298f03", "name": "runTaskFromQueue task cost before running: 346 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861874239900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36522ea2-2165-421b-994b-02d14566a913", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861872376000, "endTime": 146861874300300, "totalTime": 1846800}, "additional": {"logType": "info", "children": [], "durationId": "84ae6d66-28bd-49a8-8f13-859cef1964d4"}}, {"head": {"id": "d0623c4e-bebb-40d9-8ec0-f3685ceb1630", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861883563000, "endTime": 146861891120100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4badc4ad-4a0e-4336-a4c6-c798d0bd156b", "logId": "487b8721-8bcf-4634-a52f-75cb6517a95e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4badc4ad-4a0e-4336-a4c6-c798d0bd156b", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861876874000}, "additional": {"logType": "detail", "children": [], "durationId": "d0623c4e-bebb-40d9-8ec0-f3685ceb1630"}}, {"head": {"id": "4eaab61c-9427-454f-8ade-14e7e139a158", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861879803000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5967f786-1b58-47bc-8ee4-eb734066ee5f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861879941800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbd94a6e-2b02-4788-9def-44899ad39313", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861881993500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bd742c7-cc4a-4917-afad-f87c65c25afd", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861886076800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "056fcd61-e58d-4af2-9e49-4d11a6fbd3dd", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861889006400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "361c7f0a-dce7-4f21-9921-78a082e0e738", "name": "entry : default@ProcessResource cost memory 0.165252685546875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861889186500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "487b8721-8bcf-4634-a52f-75cb6517a95e", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861883563000, "endTime": 146861891120100}, "additional": {"logType": "info", "children": [], "durationId": "d0623c4e-bebb-40d9-8ec0-f3685ceb1630"}}, {"head": {"id": "c12a59ac-9e45-48c1-a83a-8494cdf20fc9", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861900874600, "endTime": 146861935899200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f0fa404e-0186-4f80-9599-7c38544d79cd", "logId": "9ee9aa0e-6809-49a0-857c-a2f0de84e3d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0fa404e-0186-4f80-9599-7c38544d79cd", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861895706900}, "additional": {"logType": "detail", "children": [], "durationId": "c12a59ac-9e45-48c1-a83a-8494cdf20fc9"}}, {"head": {"id": "96275777-1072-4b92-a76a-d9022b7baf2c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861896925300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "541847d8-42b5-4fd7-a0d1-79ad508d66b9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861897054200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2876a9c5-fb82-45ea-bd16-1f671b20b01e", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861900916300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f3d8cf2-1a63-433d-a8ae-2467a01e0591", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861935506400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0b70a05-9873-4146-bace-27d09edd3e2b", "name": "entry : default@GenerateLoaderJson cost memory -8.556106567382812", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861935693100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ee9aa0e-6809-49a0-857c-a2f0de84e3d8", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861900874600, "endTime": 146861935899200}, "additional": {"logType": "info", "children": [], "durationId": "c12a59ac-9e45-48c1-a83a-8494cdf20fc9"}}, {"head": {"id": "0778321c-d686-4f3f-ad4a-8be227265aa4", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861946861200, "endTime": 146861952149400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "26ec4928-dddd-40d9-bbbe-9c7703b2e60c", "logId": "1fcb3e10-dc51-4069-b8e8-88282c025f5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26ec4928-dddd-40d9-bbbe-9c7703b2e60c", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861945108700}, "additional": {"logType": "detail", "children": [], "durationId": "0778321c-d686-4f3f-ad4a-8be227265aa4"}}, {"head": {"id": "edd473a1-1c5e-4696-a3d5-9f69b926674d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861946070700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63bfa589-ce5c-4fdb-a036-0d55ec437dd5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861946182800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53ed6284-c444-4997-8322-8c9960e753c3", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861946872500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e1f5219-e9bd-4d38-95f5-c25941eab1bc", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861951872800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27acc709-5f8e-48ff-b976-ca8c17cf302a", "name": "entry : default@ProcessLibs cost memory 0.2692413330078125", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861952073600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fcb3e10-dc51-4069-b8e8-88282c025f5d", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861946861200, "endTime": 146861952149400}, "additional": {"logType": "info", "children": [], "durationId": "0778321c-d686-4f3f-ad4a-8be227265aa4"}}, {"head": {"id": "347db3ef-be24-408f-830b-8fa9c7a76a49", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861961012500, "endTime": 146861995833500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4ccec47c-ac9d-4783-bb81-a387f50d9ed6", "logId": "e26a584d-e1c5-4e50-9cc0-9c0d08fe9a7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ccec47c-ac9d-4783-bb81-a387f50d9ed6", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861954808200}, "additional": {"logType": "detail", "children": [], "durationId": "347db3ef-be24-408f-830b-8fa9c7a76a49"}}, {"head": {"id": "ef6744da-41c1-4f3e-be34-c0c344a39990", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861956037000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1dc9cf7-48eb-4bd7-a1d9-3c12247b5dbd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861956190600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "701dc9b4-87c4-4976-bdeb-9cdda1523c55", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861957764200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06bdbda9-893d-4014-a238-49c8d8de9c37", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861961099500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1b79e6e-75f3-48e2-b95d-946ce4c61c85", "name": "Incremental task entry:default@CompileResource pre-execution cost: 33 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861995375000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90258c74-6ec0-4b06-9788-40e20d882583", "name": "entry : default@CompileResource cost memory 1.3251800537109375", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861995639200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e26a584d-e1c5-4e50-9cc0-9c0d08fe9a7f", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861961012500, "endTime": 146861995833500}, "additional": {"logType": "info", "children": [], "durationId": "347db3ef-be24-408f-830b-8fa9c7a76a49"}}, {"head": {"id": "9da95b8f-4a0e-4880-bcdd-2fb8b5431a1f", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862006666100, "endTime": 146862009024000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3216b36a-f8a6-4e0d-90c9-edb556a26634", "logId": "20ddc9e6-437e-49a8-8fc1-f880450a2451"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3216b36a-f8a6-4e0d-90c9-edb556a26634", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862000019400}, "additional": {"logType": "detail", "children": [], "durationId": "9da95b8f-4a0e-4880-bcdd-2fb8b5431a1f"}}, {"head": {"id": "86c0475e-02a4-47c4-bb74-fedeff163dc5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862001864800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c0a4417-26ea-41bc-84bb-8b46ee46784b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862002037900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd54aed7-83e0-4ea0-8283-2a2d0a9e9fc4", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862006680400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b840922-a54a-43ca-8a73-d0d57636b97d", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862007274300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33d69285-eb1b-4634-adaf-f71293fc4322", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862008844300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "695ee819-d5f3-4855-97a9-244270feecd0", "name": "entry : default@DoNativeStrip cost memory 0.08397674560546875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862008960400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20ddc9e6-437e-49a8-8fc1-f880450a2451", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862006666100, "endTime": 146862009024000}, "additional": {"logType": "info", "children": [], "durationId": "9da95b8f-4a0e-4880-bcdd-2fb8b5431a1f"}}, {"head": {"id": "e9b8f15d-a766-4c7d-ae4f-3019c6db5a76", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862017257600, "endTime": 146872506689800}, "additional": {"children": ["eebdb8f8-b7d6-46fa-a9d6-3026a8ab0ef5", "573751cf-a692-4e34-a912-fe53e5c0a4d3"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "611ce975-ca4a-418e-a5b6-93799594b1aa", "logId": "3435882e-f5ae-4d66-8b51-50c0f4248d80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "611ce975-ca4a-418e-a5b6-93799594b1aa", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862011373200}, "additional": {"logType": "detail", "children": [], "durationId": "e9b8f15d-a766-4c7d-ae4f-3019c6db5a76"}}, {"head": {"id": "ef2ccb98-7942-4306-9bbe-09dbbb4f890a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862012394700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a3c3bf2-28ec-48d9-93c1-2409b3e268fe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862012500700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa6325fc-7ec0-4775-ba8b-a6640ed7352c", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862017273700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "505940f7-e030-4d94-944c-0b02006d546f", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862017498800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdd32b89-13fd-4a49-b94a-82403263e2ee", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862052527300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89ee5a9d-31bd-4b60-96fb-0acff527fa87", "name": "default@CompileArkTS work[2] is submitted.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862055189000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eebdb8f8-b7d6-46fa-a9d6-3026a8ab0ef5", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Worker4", "startTime": 146864444145600, "endTime": 146872492447800}, "additional": {"children": ["1d13801e-a4fc-45a1-b391-a7135f29fc31", "8de4e6a5-5a03-4bba-8b63-69b3d7fb00f6", "e6435c40-4d9e-42fd-bfff-0594d52cf378", "38d6e2d5-8147-447e-8275-150dbe476219", "5763a400-c480-4af4-9614-b9c652b8abab", "68aadfdb-820c-45ec-826f-200e6d48125b", "b4047217-0e25-4cb3-86c9-42120f829760"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "e9b8f15d-a766-4c7d-ae4f-3019c6db5a76", "logId": "b9501a78-a2fb-4a4e-bbe4-1ee62e02a6a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0969e5b4-0d8d-4606-89cb-9c3033c2ff47", "name": "default@CompileArkTS work[2] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862056407000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2103b226-cf20-4ab2-a130-bfe2567858ae", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862056671500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e9a9a77-bce1-41c2-85ad-6a9f521fb16d", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862056753000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ded68c61-a0b4-4dba-a6e9-4a72aef8a734", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862056791200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3648c894-14b1-431b-a194-bc42f7700803", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862056820700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0d1c0a6-3e99-499e-9373-48ca6b40163c", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862056847700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d91fdf9-110d-4290-8bc1-6ba8d3850396", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862056875000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "986f0082-c669-4288-b949-0308e6e7e44c", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862056903300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "191e0360-06e9-4bc4-8ba4-0ac9eeb919a5", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862056928800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "431f6ead-f092-4d9d-abf0-c0ccba504756", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862056955200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9199a43-03b7-4c0d-8a41-dad5ab9337b1", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862056981900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6242dceb-8da8-4911-bd59-e78ac222a9ba", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862057007700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53106675-a988-4ed1-bd64-ea177c2d6b57", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862057035100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f12eea0-ac6b-46d6-8457-ff416293facf", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862057060600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90746bc7-77e0-4c61-b9a6-039490dd074c", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862057086000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa29ea68-561e-44d4-905e-c9c32e5ceb25", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862057111800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ada2b075-fbb9-48ce-9d34-ffebce8df0f1", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862057235500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b26e4af-f1d6-4ff3-8c71-c1fbcb116363", "name": "default@CompileArkTS work[2] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862058267900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a366531-2d60-4edb-9faa-7382dd302317", "name": "default@CompileArkTS work[2] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862058415800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae23c744-a502-41df-895d-ada61a2502c4", "name": "CopyResources startTime: 146862058488100", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862058491200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc42910b-7b57-424b-9c6f-abb5425a070a", "name": "default@CompileArkTS work[3] is submitted.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862058573800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "573751cf-a692-4e34-a912-fe53e5c0a4d3", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Worker18", "startTime": 146863004694600, "endTime": 146863016256500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "e9b8f15d-a766-4c7d-ae4f-3019c6db5a76", "logId": "8cd1b2c1-bdde-4060-909b-77e0850391c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a01bf8e6-83a3-4bf4-bcfd-99613826146f", "name": "default@CompileArkTS work[3] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862059471400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "242058e9-818f-49c5-8003-788879da7687", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862059561400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c50a7c9-aa97-43c3-afff-e12173a7110c", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862059610000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63f4eb73-0aea-4eb2-a459-34259b7e60bd", "name": "default@CompileArkTS work[3] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862060284500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66c1c24a-6f3e-4aab-9135-70769321e426", "name": "default@CompileArkTS work[3] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862060370100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "677d8e4b-ce70-421f-a9fb-296a8298adec", "name": "entry : default@CompileArkTS cost memory 1.8803253173828125", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862060495600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c19790b-c866-4707-8713-9df24e66e4d7", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862068283300, "endTime": 146862084371100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "80e25e16-c74e-4dd5-8ffd-e4198245d25b", "logId": "2685ea2c-28d2-4a43-88b0-790fe2b13173"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80e25e16-c74e-4dd5-8ffd-e4198245d25b", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862062436200}, "additional": {"logType": "detail", "children": [], "durationId": "8c19790b-c866-4707-8713-9df24e66e4d7"}}, {"head": {"id": "4078aeaa-e888-4fc7-ad9f-cd88b02706cd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862063478500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aebacc72-77a8-40d7-ae5a-fd8f48e920e0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862063589400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d97bab08-5f1d-40c2-a3a1-e50d59d050db", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862068310200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92d60600-4505-450a-9cfa-1fdbcd1fb41c", "name": "entry : default@BuildJS cost memory 0.38785552978515625", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862084029900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5eb5a27-b772-4740-954b-6391ccf604c5", "name": "runTaskFromQueue task cost before running: 556 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862084257400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2685ea2c-28d2-4a43-88b0-790fe2b13173", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862068283300, "endTime": 146862084371100, "totalTime": 15940600}, "additional": {"logType": "info", "children": [], "durationId": "8c19790b-c866-4707-8713-9df24e66e4d7"}}, {"head": {"id": "72baa48d-38ea-454d-8b8d-e04306e9bfe5", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862091114500, "endTime": 146862094450500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "bf3dc1f8-5a0a-453a-8da5-bea5d240f978", "logId": "f25f13f0-e0a4-4e66-861a-cc2af3172cc0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf3dc1f8-5a0a-453a-8da5-bea5d240f978", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862087420900}, "additional": {"logType": "detail", "children": [], "durationId": "72baa48d-38ea-454d-8b8d-e04306e9bfe5"}}, {"head": {"id": "bebc78bd-1145-462b-bc32-d2c0086cc9da", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862088469900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1255da95-361e-42d8-809b-5f3c9239f1f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862088630300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a229d2a6-5f40-42fe-b566-fbfee39de07d", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862091127400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f7ae2ab-ade5-4327-bc03-f2d72f02105e", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862092079400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6002a27-20c9-4043-b7d5-3bdf1296309f", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862094272800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55969dd5-ca94-42bc-912d-d956e5eb7dd6", "name": "entry : default@CacheNativeLibs cost memory 0.09619903564453125", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862094384900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f25f13f0-e0a4-4e66-861a-cc2af3172cc0", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862091114500, "endTime": 146862094450500}, "additional": {"logType": "info", "children": [], "durationId": "72baa48d-38ea-454d-8b8d-e04306e9bfe5"}}, {"head": {"id": "c523b412-3679-4e92-84e6-60935da306dc", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146863016731500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ff0c8b8-372a-4a15-99b6-90dfd4b34311", "name": "CopyResources is end, endTime: 146863016891800", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146863016897100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aff3626c-1890-4ff3-b4d7-39851e944ce2", "name": "default@CompileArkTS work[3] done.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146863017051000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cd1b2c1-bdde-4060-909b-77e0850391c9", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Worker18", "startTime": 146863004694600, "endTime": 146863016256500}, "additional": {"logType": "info", "children": [], "durationId": "573751cf-a692-4e34-a912-fe53e5c0a4d3", "parent": "3435882e-f5ae-4d66-8b51-50c0f4248d80"}}, {"head": {"id": "4bde4c96-57f9-48f2-8aaf-66631d233b35", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146863017224000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d467812e-0e08-4c8c-b6a1-aaa92eaad21f", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872492907100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d13801e-a4fc-45a1-b391-a7135f29fc31", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Worker4", "startTime": 146864445173800, "endTime": 146865455326200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "eebdb8f8-b7d6-46fa-a9d6-3026a8ab0ef5", "logId": "65d4a48a-3c8a-478e-ac46-40ed9305f16b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65d4a48a-3c8a-478e-ac46-40ed9305f16b", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146864445173800, "endTime": 146865455326200}, "additional": {"logType": "info", "children": [], "durationId": "1d13801e-a4fc-45a1-b391-a7135f29fc31", "parent": "b9501a78-a2fb-4a4e-bbe4-1ee62e02a6a4"}}, {"head": {"id": "8de4e6a5-5a03-4bba-8b63-69b3d7fb00f6", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Worker4", "startTime": 146865456780000, "endTime": 146865501277800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "eebdb8f8-b7d6-46fa-a9d6-3026a8ab0ef5", "logId": "f11e2ac5-d73b-4e68-9a07-8ecfc46ac134"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f11e2ac5-d73b-4e68-9a07-8ecfc46ac134", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146865456780000, "endTime": 146865501277800}, "additional": {"logType": "info", "children": [], "durationId": "8de4e6a5-5a03-4bba-8b63-69b3d7fb00f6", "parent": "b9501a78-a2fb-4a4e-bbe4-1ee62e02a6a4"}}, {"head": {"id": "e6435c40-4d9e-42fd-bfff-0594d52cf378", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Worker4", "startTime": 146865501680400, "endTime": 146865501835800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "eebdb8f8-b7d6-46fa-a9d6-3026a8ab0ef5", "logId": "a4a262aa-359b-492e-b854-24cc93b9cbc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4a262aa-359b-492e-b854-24cc93b9cbc7", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146865501680400, "endTime": 146865501835800}, "additional": {"logType": "info", "children": [], "durationId": "e6435c40-4d9e-42fd-bfff-0594d52cf378", "parent": "b9501a78-a2fb-4a4e-bbe4-1ee62e02a6a4"}}, {"head": {"id": "38d6e2d5-8147-447e-8275-150dbe476219", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Worker4", "startTime": 146865501902000, "endTime": 146872193803900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "eebdb8f8-b7d6-46fa-a9d6-3026a8ab0ef5", "logId": "8712bbfa-dad8-46db-b112-f396350b20e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8712bbfa-dad8-46db-b112-f396350b20e7", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146865501902000, "endTime": 146872193803900}, "additional": {"logType": "info", "children": [], "durationId": "38d6e2d5-8147-447e-8275-150dbe476219", "parent": "b9501a78-a2fb-4a4e-bbe4-1ee62e02a6a4"}}, {"head": {"id": "5763a400-c480-4af4-9614-b9c652b8abab", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Worker4", "startTime": 146872194061400, "endTime": 146872228208300}, "additional": {"children": ["d3bf1014-bb94-44f7-a81e-4a03a420809f", "18b853b0-38c8-408a-ba18-17f38df56e72", "263db637-23a0-48c2-9d9f-68df901d4ed5"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "eebdb8f8-b7d6-46fa-a9d6-3026a8ab0ef5", "logId": "80719876-95f7-40ee-b522-370fe78d46b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80719876-95f7-40ee-b522-370fe78d46b1", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872194061400, "endTime": 146872228208300}, "additional": {"logType": "info", "children": ["67ed2308-cda2-4a99-99fb-37cd07250bdc", "1085472c-5def-47e1-87ce-9b66d54456a3", "9ba6ce31-149c-4cf2-97e7-482bcb2a8778"], "durationId": "5763a400-c480-4af4-9614-b9c652b8abab", "parent": "b9501a78-a2fb-4a4e-bbe4-1ee62e02a6a4"}}, {"head": {"id": "d3bf1014-bb94-44f7-a81e-4a03a420809f", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Worker4", "startTime": 146872194224300, "endTime": 146872194242700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5763a400-c480-4af4-9614-b9c652b8abab", "logId": "67ed2308-cda2-4a99-99fb-37cd07250bdc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67ed2308-cda2-4a99-99fb-37cd07250bdc", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872194224300, "endTime": 146872194242700}, "additional": {"logType": "info", "children": [], "durationId": "d3bf1014-bb94-44f7-a81e-4a03a420809f", "parent": "80719876-95f7-40ee-b522-370fe78d46b1"}}, {"head": {"id": "18b853b0-38c8-408a-ba18-17f38df56e72", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Worker4", "startTime": 146872194252400, "endTime": 146872211692600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5763a400-c480-4af4-9614-b9c652b8abab", "logId": "1085472c-5def-47e1-87ce-9b66d54456a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1085472c-5def-47e1-87ce-9b66d54456a3", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872194252400, "endTime": 146872211692600}, "additional": {"logType": "info", "children": [], "durationId": "18b853b0-38c8-408a-ba18-17f38df56e72", "parent": "80719876-95f7-40ee-b522-370fe78d46b1"}}, {"head": {"id": "263db637-23a0-48c2-9d9f-68df901d4ed5", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Worker4", "startTime": 146872211698700, "endTime": 146872228105000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5763a400-c480-4af4-9614-b9c652b8abab", "logId": "9ba6ce31-149c-4cf2-97e7-482bcb2a8778"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ba6ce31-149c-4cf2-97e7-482bcb2a8778", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872211698700, "endTime": 146872228105000}, "additional": {"logType": "info", "children": [], "durationId": "263db637-23a0-48c2-9d9f-68df901d4ed5", "parent": "80719876-95f7-40ee-b522-370fe78d46b1"}}, {"head": {"id": "68aadfdb-820c-45ec-826f-200e6d48125b", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Worker4", "startTime": 146872228234900, "endTime": 146872481639300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "eebdb8f8-b7d6-46fa-a9d6-3026a8ab0ef5", "logId": "ed25d6dd-7ba0-4417-8801-a0dc7d1c5899"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed25d6dd-7ba0-4417-8801-a0dc7d1c5899", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872228234900, "endTime": 146872481639300}, "additional": {"logType": "info", "children": [], "durationId": "68aadfdb-820c-45ec-826f-200e6d48125b", "parent": "b9501a78-a2fb-4a4e-bbe4-1ee62e02a6a4"}}, {"head": {"id": "b4047217-0e25-4cb3-86c9-42120f829760", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Worker4", "startTime": 146862925155700, "endTime": 146864442482800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "eebdb8f8-b7d6-46fa-a9d6-3026a8ab0ef5", "logId": "2cfa1b59-71a6-49b5-9c82-c29206c5ef88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2cfa1b59-71a6-49b5-9c82-c29206c5ef88", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862925155700, "endTime": 146864442482800}, "additional": {"logType": "info", "children": [], "durationId": "b4047217-0e25-4cb3-86c9-42120f829760", "parent": "b9501a78-a2fb-4a4e-bbe4-1ee62e02a6a4"}}, {"head": {"id": "746f8492-2e79-494f-a2dd-cbe522075952", "name": "default@CompileArkTS work[2] done.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872506140600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9501a78-a2fb-4a4e-bbe4-1ee62e02a6a4", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Worker4", "startTime": 146864444145600, "endTime": 146872492447800}, "additional": {"logType": "info", "children": ["65d4a48a-3c8a-478e-ac46-40ed9305f16b", "f11e2ac5-d73b-4e68-9a07-8ecfc46ac134", "a4a262aa-359b-492e-b854-24cc93b9cbc7", "8712bbfa-dad8-46db-b112-f396350b20e7", "80719876-95f7-40ee-b522-370fe78d46b1", "ed25d6dd-7ba0-4417-8801-a0dc7d1c5899", "2cfa1b59-71a6-49b5-9c82-c29206c5ef88"], "durationId": "eebdb8f8-b7d6-46fa-a9d6-3026a8ab0ef5", "parent": "3435882e-f5ae-4d66-8b51-50c0f4248d80"}}, {"head": {"id": "a6322593-7253-49f8-859e-867a48d15c9e", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872506460100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3435882e-f5ae-4d66-8b51-50c0f4248d80", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146862017257600, "endTime": 146872506689800, "totalTime": 8103173800}, "additional": {"logType": "info", "children": ["b9501a78-a2fb-4a4e-bbe4-1ee62e02a6a4", "8cd1b2c1-bdde-4060-909b-77e0850391c9"], "durationId": "e9b8f15d-a766-4c7d-ae4f-3019c6db5a76"}}, {"head": {"id": "3f3dd88b-0325-4197-adce-79bf99273209", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872517923600, "endTime": 146872519521800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "782089a8-0014-4b88-9bd4-07c933a691bf", "logId": "83e6979f-7500-42af-9dc4-ee4ea95da60d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "782089a8-0014-4b88-9bd4-07c933a691bf", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872513760200}, "additional": {"logType": "detail", "children": [], "durationId": "3f3dd88b-0325-4197-adce-79bf99273209"}}, {"head": {"id": "9522ab9e-ed55-4089-a8d0-8df67316a866", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872515784300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18db1a2a-92d3-42f5-a0a8-3c7412b6ec06", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872515922100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b2d4031-8488-45c4-be2a-f1a018b19935", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872517941500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2986caf-4425-42fb-b689-fca782dc5407", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872518389000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16ed698f-0612-492f-94f0-cb2ef633a9e8", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872519328800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a1ed822-d473-4ffa-8240-bfd905d7b727", "name": "entry : default@GeneratePkgModuleJson cost memory 0.075286865234375", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872519448600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83e6979f-7500-42af-9dc4-ee4ea95da60d", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872517923600, "endTime": 146872519521800}, "additional": {"logType": "info", "children": [], "durationId": "3f3dd88b-0325-4197-adce-79bf99273209"}}, {"head": {"id": "5a476378-b3e2-45d2-a6d8-cf80f5b1fbaa", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872531044600, "endTime": 146872707154800}, "additional": {"children": ["db462042-c49d-44c7-90a0-1e07f47595a2", "992a0424-8c0d-4ea4-95e2-1108803e1afc"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "7951f80d-aa12-4330-a37c-058099b93ab9", "logId": "68a49c9e-9197-4e9a-b3ec-f192fd5477bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7951f80d-aa12-4330-a37c-058099b93ab9", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872522207400}, "additional": {"logType": "detail", "children": [], "durationId": "5a476378-b3e2-45d2-a6d8-cf80f5b1fbaa"}}, {"head": {"id": "e8b9fbf9-db7f-48e2-b259-281a07625937", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872523230200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40d1cb92-1072-4556-9b95-81fc86bec123", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872523370800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8034c349-1295-4b1e-b5da-091ae9705f94", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872531062300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d03875e4-202a-4014-a4ad-c4e8ee6b6d52", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872549074700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d8ec26-cfb6-49bc-9796-97e3c73bad5c", "name": "Incremental task entry:default@PackageHap pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872549263600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f14fd7c-4617-45e9-bc4f-e449bffba92e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872549363300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06d8e853-cea5-4674-9080-8abc7b7c8227", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872549417100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db462042-c49d-44c7-90a0-1e07f47595a2", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872551147000, "endTime": 146872553668100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5a476378-b3e2-45d2-a6d8-cf80f5b1fbaa", "logId": "69c2d4ab-e336-4d19-9b43-991f61c96d89"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed7eca94-5303-4b65-9d68-7b5f5b2035c3", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872553453900}, "additional": {"logType": "debug", "children": [], "durationId": "5a476378-b3e2-45d2-a6d8-cf80f5b1fbaa"}}, {"head": {"id": "69c2d4ab-e336-4d19-9b43-991f61c96d89", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872551147000, "endTime": 146872553668100}, "additional": {"logType": "info", "children": [], "durationId": "db462042-c49d-44c7-90a0-1e07f47595a2", "parent": "68a49c9e-9197-4e9a-b3ec-f192fd5477bb"}}, {"head": {"id": "992a0424-8c0d-4ea4-95e2-1108803e1afc", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872554447600, "endTime": 146872702650600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5a476378-b3e2-45d2-a6d8-cf80f5b1fbaa", "logId": "693c5a9e-d27a-4c3f-806d-f805798069f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cf69bab-be71-4ecf-b3a8-f215022f93dd", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872701934000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "693c5a9e-d27a-4c3f-806d-f805798069f1", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872554447600, "endTime": 146872702643300}, "additional": {"logType": "info", "children": [], "durationId": "992a0424-8c0d-4ea4-95e2-1108803e1afc", "parent": "68a49c9e-9197-4e9a-b3ec-f192fd5477bb"}}, {"head": {"id": "785f75ff-25be-4924-aa39-bb55602f664c", "name": "entry : default@PackageHap cost memory 1.6090774536132812", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872706983300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba12f519-be40-48cd-9704-7e18039a7753", "name": "runTaskFromQueue task cost before running: 11 s 179 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872707104800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68a49c9e-9197-4e9a-b3ec-f192fd5477bb", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872531044600, "endTime": 146872707154800, "totalTime": 176038500}, "additional": {"logType": "info", "children": ["69c2d4ab-e336-4d19-9b43-991f61c96d89", "693c5a9e-d27a-4c3f-806d-f805798069f1"], "durationId": "5a476378-b3e2-45d2-a6d8-cf80f5b1fbaa"}}, {"head": {"id": "64bb957e-719e-4e56-acb3-105d968b74bc", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872712858700, "endTime": 146873011896200}, "additional": {"children": ["4832578d-4545-4b63-af7f-fe17620fb012", "43621dd0-fe36-4176-bc71-101cf9e08e35"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "beac6f7f-9055-461a-9fc0-4fce6d523983", "logId": "fca80afd-5aee-4ac2-9b96-9b85c9d5dabd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "beac6f7f-9055-461a-9fc0-4fce6d523983", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872709942300}, "additional": {"logType": "detail", "children": [], "durationId": "64bb957e-719e-4e56-acb3-105d968b74bc"}}, {"head": {"id": "209a3168-f4da-489d-8bf5-eab031ac1cfa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872710675400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbf191fb-05aa-4186-9825-552d4be243fc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872710752100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "446791fe-6d8f-44b6-b79c-d3a49623118f", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872712869400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb4bc925-92e1-47be-91f6-0cdb627e26cb", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872714964000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce3e3fd7-622d-45a4-a775-c697b22d8f44", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872715073700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d544bbf-3fc8-45e7-b88f-a49e0fad0d55", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872715148200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f1024b2-01ff-42e3-b3e3-2cfc9a5fa378", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872715182000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4832578d-4545-4b63-af7f-fe17620fb012", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872716913200, "endTime": 146872811198200}, "additional": {"children": ["915f969f-2f6e-4dc7-9526-81ffdafd099e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64bb957e-719e-4e56-acb3-105d968b74bc", "logId": "18d239e6-1766-40a0-bca4-9f44cfee5e3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "915f969f-2f6e-4dc7-9526-81ffdafd099e", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872735779200, "endTime": 146872809947600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4832578d-4545-4b63-af7f-fe17620fb012", "logId": "b9f91131-7c31-4761-b9eb-a27e0c79e622"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1206585-a789-4bfd-86d5-6eaf1e194323", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872739487900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "278ad726-c48d-4d3f-8b8c-d58472778d4d", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872809224000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9f91131-7c31-4761-b9eb-a27e0c79e622", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872735779200, "endTime": 146872809947600}, "additional": {"logType": "info", "children": [], "durationId": "915f969f-2f6e-4dc7-9526-81ffdafd099e", "parent": "18d239e6-1766-40a0-bca4-9f44cfee5e3e"}}, {"head": {"id": "18d239e6-1766-40a0-bca4-9f44cfee5e3e", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872716913200, "endTime": 146872811198200}, "additional": {"logType": "info", "children": ["b9f91131-7c31-4761-b9eb-a27e0c79e622"], "durationId": "4832578d-4545-4b63-af7f-fe17620fb012", "parent": "fca80afd-5aee-4ac2-9b96-9b85c9d5dabd"}}, {"head": {"id": "43621dd0-fe36-4176-bc71-101cf9e08e35", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872811822600, "endTime": 146873011438900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64bb957e-719e-4e56-acb3-105d968b74bc", "logId": "35bac37a-dc33-4575-8640-19062ec69331"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5855e45-8fa9-451e-9c45-3e8505141510", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872813463300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a234f3e-d98d-439b-b6c0-83268027aa97", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873010934100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35bac37a-dc33-4575-8640-19062ec69331", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872811822600, "endTime": 146873011438900}, "additional": {"logType": "info", "children": [], "durationId": "43621dd0-fe36-4176-bc71-101cf9e08e35", "parent": "fca80afd-5aee-4ac2-9b96-9b85c9d5dabd"}}, {"head": {"id": "4a312b89-305f-4772-9d19-167847662e63", "name": "entry : default@SignHap cost memory -9.404678344726562", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873011734900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2608059a-2da1-4d65-8861-0942f53729df", "name": "runTaskFromQueue task cost before running: 11 s 483 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873011845900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fca80afd-5aee-4ac2-9b96-9b85c9d5dabd", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146872712858700, "endTime": 146873011896200, "totalTime": 298960900}, "additional": {"logType": "info", "children": ["18d239e6-1766-40a0-bca4-9f44cfee5e3e", "35bac37a-dc33-4575-8640-19062ec69331"], "durationId": "64bb957e-719e-4e56-acb3-105d968b74bc"}}, {"head": {"id": "5c4c1d5d-242b-4749-8456-7c4d2ff880a6", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873015169000, "endTime": 146873020537800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "788de443-03fb-4bae-9f0a-4cd296698d4c", "logId": "e08cb5c9-dc6f-4aba-be69-13784dc1aec2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "788de443-03fb-4bae-9f0a-4cd296698d4c", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873013703200}, "additional": {"logType": "detail", "children": [], "durationId": "5c4c1d5d-242b-4749-8456-7c4d2ff880a6"}}, {"head": {"id": "ec391502-2856-4206-9892-b817aa3469a6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873014459500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8369e10f-5236-4b2d-bee2-66c4f4cd894a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873014539100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee1fd7af-0c93-427c-a539-60f1e107d79d", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873015176500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "037bbe67-8e63-420b-b37f-d27210b9d129", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873020248600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88619cb5-5189-4c87-8283-6ca1e8395fa3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873020343900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5444cbba-e76a-4f76-ae43-f2e6523eaf66", "name": "entry : default@CollectDebugSymbol cost memory 0.2481231689453125", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873020435500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b4f40c6-6f93-4ab9-a451-7a5c068c963b", "name": "runTaskFromQueue task cost before running: 11 s 492 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873020501000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e08cb5c9-dc6f-4aba-be69-13784dc1aec2", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873015169000, "endTime": 146873020537800, "totalTime": 5310800}, "additional": {"logType": "info", "children": [], "durationId": "5c4c1d5d-242b-4749-8456-7c4d2ff880a6"}}, {"head": {"id": "0137513b-0551-4cc7-9460-6ebc4ec63bf8", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873021820000, "endTime": 146873022028100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "49ba2fc7-3488-46b7-9e9c-cef02a126cb5", "logId": "3ac81fd6-d1ae-4e1a-b229-c8d6f0be1ceb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49ba2fc7-3488-46b7-9e9c-cef02a126cb5", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873021780600}, "additional": {"logType": "detail", "children": [], "durationId": "0137513b-0551-4cc7-9460-6ebc4ec63bf8"}}, {"head": {"id": "54313428-02e4-40da-8e39-d6aeac9ebfb3", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873021826200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12eb4686-692b-4566-9de0-f2ca925c7763", "name": "entry : assembleHap cost memory 0.01372528076171875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873021939100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f7ab4dc-f0f4-4736-8036-408b80d93e55", "name": "runTaskFromQueue task cost before running: 11 s 494 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873021996000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ac81fd6-d1ae-4e1a-b229-c8d6f0be1ceb", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873021820000, "endTime": 146873022028100, "totalTime": 159800}, "additional": {"logType": "info", "children": [], "durationId": "0137513b-0551-4cc7-9460-6ebc4ec63bf8"}}, {"head": {"id": "895a9991-5b68-415f-a6eb-e7d3c916985f", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873032825500, "endTime": 146873032848300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a14addf8-31b7-4a07-8516-80dfe7e49d4e", "logId": "2b8a432b-bf32-4a0e-9d1d-9f7caf34c2c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b8a432b-bf32-4a0e-9d1d-9f7caf34c2c6", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873032825500, "endTime": 146873032848300}, "additional": {"logType": "info", "children": [], "durationId": "895a9991-5b68-415f-a6eb-e7d3c916985f"}}, {"head": {"id": "ffaffb71-e6c8-4f69-b4f2-f94d7e556a8a", "name": "BUILD SUCCESSFUL in 11 s 504 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873032920900}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "980576d2-2ca9-49e2-b4da-90a484f7cf51", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146861528895000, "endTime": 146873033443400}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 45, "second": 2}, "completeCommand": "{\"prop\":[\"product=default\"],\"mode\":\"module\",\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.8", "category": "build", "state": "success"}}, {"head": {"id": "134a66c5-e42e-420c-be71-478a11064413", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873033527100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbb71548-8e81-40f6-a878-4fd0735501a0", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873033778100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b04bf182-97ec-4988-be20-0da16b6c6e13", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873034281800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f082e6c6-3a47-4cde-b702-81d0caeeee88", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873034369000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd8e99a7-d01d-435f-9c2b-54c3316fced2", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873034409300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe39c880-d49d-4075-bf50-a07e30fc0336", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873034461900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68094fba-c3a3-4f55-985e-ac206e44b803", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873034490400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a974f9c-014d-4bb5-9cc2-4b3fe4f66236", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873035033500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "add9e9ef-cbef-4433-8dc0-ce95b8a930a9", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873035237000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c83c9521-ae3d-4b64-a00f-6db7e452f4c6", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873035286800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba7af193-145a-4874-9ea7-fd50c4d2d608", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873035318200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e40841c-f3f2-47b5-aa31-0ec71cf21052", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873035340200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aedea45-8e31-4e94-8ae0-01a5233b99de", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873035365300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "763ae635-5bf9-4346-a918-e07e55a20154", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873036254100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4670bbb-3f60-4f64-a11b-ccc6e797debc", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873036528800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2772887-2745-4dec-9450-167ae0554091", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873036711400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff049c0f-d6d2-4d84-9214-a23ff383a9ef", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873036758300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "553341fc-40e6-45d6-9114-33e9dc43f577", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873036784300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d97d95c7-d2f0-40a4-a6e1-0abfe152e207", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873036806300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4907ea40-2931-4c7e-9fb3-317a7dd45a89", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873036828800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e96b9222-1e87-4936-82f0-20aacb66afdf", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873036848900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d11fa975-5bf6-4f46-835a-712d573e3871", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873039152300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5a75341-ea88-497a-8cf1-d1ce8a94cbc2", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873039685400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9f89f8c-4e19-4fac-a049-16c40e3f1fd0", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873040019700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "436f1230-1c8e-4a37-bac4-44a0c977d79e", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873040204400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e42afc55-dbfc-4fa3-9e96-3888af866b3a", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873040390600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaaf64a5-9279-4a59-b478-6b091d14a71a", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873040957400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56ebf0f8-cc79-45ee-a703-09542126c7bf", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873045932000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d451b830-b381-4d4a-9bab-1366c0eef2e7", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873046136800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e23c8e1e-94da-467d-9d17-d9e15898bca7", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873046414400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b21ca5db-4a74-44ff-92cb-78d49605303f", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873047128500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "673e5f66-f9f9-4029-b712-fd5d99af1dd5", "name": "Incremental task entry:default@CompileArkTS post-execution cost:11 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873047641100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "972b1ba1-8ee4-43b3-8659-71907ec3e927", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873049096900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66eb2012-9a97-402c-b0f3-a9f2b54381b7", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873049567500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e0a1ef8-e3d4-4e8e-b0bd-4de5b02bfbef", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873049854100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ce182a7-087e-46d4-9177-80e8216e7073", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873050055000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00f75a29-8ec3-4bb4-a921-c7bed6be8417", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873050340200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46312e98-13de-4c47-998e-ea2c7b67066b", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873051068800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7e08605-26f8-4970-b43b-dac6605f82bd", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873051913300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f442b08-bbb7-4cf2-9f70-320822583d37", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873052163700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d753051e-d1d8-42e0-99aa-d3697021ef68", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873052214100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff0b5331-56d0-4329-968b-a7731e31dfbe", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873052242500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d57d8833-0402-4910-8106-a4108a674c36", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873053379000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba78fdeb-8416-4cff-979e-317581c7cbd9", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873053766000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af24f084-d9ab-4b14-88ed-7cafca979df4", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873053976700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "166da243-f5af-4ef9-b233-ca0bfd7301eb", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873059136900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "234ea4c1-f4da-43e3-b74f-30547045b8e5", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873059331000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79d3d5fa-c1b7-4188-9920-8ba4791bf71e", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873059502600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f058dd8-ab6e-4f42-a4a1-c380ccd575d9", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873059668300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c74d2e12-9bce-48a2-af13-7e1548cfe56b", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873059715900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61a9d864-0a03-4c51-bb09-10dd421d4fb5", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873059878200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c817a97-686f-4594-8bc3-237873725d1d", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873060062900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ff73037-1522-42ab-b411-ac2cc136a252", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873060796900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8e4fc39-64b6-4bcb-bfb0-52b3eb274d15", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873061027500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a288e28d-6d49-44b2-a9c8-c34f91960df3", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873061208800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c57b45c-0a26-4e36-9ad7-86d05f328c08", "name": "Incremental task entry:default@PackageHap post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873061406400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b429b9ce-b941-441e-a019-7026c4e1135b", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873061627100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f285429f-c52f-47d4-8245-73bc766f481a", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873061803100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dd731c7-155a-4009-bfe6-fc4e1a3b22ba", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873061988600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8f33117-7c20-447b-a78f-8f82e91ba2b8", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873062150400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d96c30b-acf7-4c0c-888c-9f123cbb3197", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873062195700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22c0885c-3c8e-436b-84c7-cd7180226059", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873062364500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5b25db1-a1c6-4f3f-96be-fa292adcffd2", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873064489300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5286718-f034-48f5-a567-fd03d40b6f31", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873064716900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fad9592c-3fb6-462c-a3a1-4bc48be9e811", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873065146700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29d7b83c-73ed-4059-a173-24d09de968ad", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146873065369700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}