"use strict";
import { Fragment, jsx } from "react/jsx-runtime";
import React from "react";
import { AccessContext } from "./context";
export const useAccess = () => {
  return React.useContext(AccessContext);
};
export const Access = (props) => {
  if (typeof props.accessible !== "boolean") {
    throw new Error("[access] the `accessible` property on <Access /> should be a boolean");
  }
  return /* @__PURE__ */ jsx(Fragment, { children: props.accessible ? props.children : props.fallback });
};
export const useAccessMarkedRoutes = (routes) => {
  const access = useAccess();
  const markdedRoutes = React.useMemo(() => {
    const process2 = (route, parentAccessCode, parentRoute) => {
      let accessCode = route.access;
      let detectorRoute = route;
      if (!accessCode && parentAccessCode) {
        accessCode = parentAccessCode;
        detectorRoute = parentRoute;
      }
      route.unaccessible = false;
      if (typeof accessCode === "string") {
        const detector = access[accessCode];
        if (typeof detector === "function") {
          route.unaccessible = !detector(detectorRoute);
        } else if (typeof detector === "boolean") {
          route.unaccessible = !detector;
        } else if (typeof detector === "undefined") {
          route.unaccessible = true;
        }
      }
      if (route.children?.length) {
        const isNoAccessibleChild = !route.children.reduce((hasAccessibleChild, child) => {
          process2(child, accessCode, route);
          return hasAccessibleChild || !child.unaccessible;
        }, false);
        if (isNoAccessibleChild) {
          route.unaccessible = true;
        }
      }
      if (route.routes?.length) {
        const isNoAccessibleChild = !route.routes.reduce((hasAccessibleChild, child) => {
          process2(child, accessCode, route);
          return hasAccessibleChild || !child.unaccessible;
        }, false);
        if (isNoAccessibleChild) {
          route.unaccessible = true;
        }
      }
      return route;
    };
    return routes.map((route) => process2(route));
  }, [routes.length, access]);
  return markdedRoutes;
};
