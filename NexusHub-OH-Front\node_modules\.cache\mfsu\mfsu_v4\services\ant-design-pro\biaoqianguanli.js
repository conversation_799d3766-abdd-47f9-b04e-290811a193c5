"use strict";
import { request } from "@umijs/max";
export async function getAppsIdTags(params, options) {
  const { id: param0, ...queryParams } = params;
  return request(`/apps/${param0}/tags`, {
    method: "GET",
    params: { ...queryParams },
    ...options || {}
  });
}
export async function postAppsIdTags(params, body, options) {
  const { id: param0, ...queryParams } = params;
  return request(`/apps/${param0}/tags`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    params: { ...queryParams },
    data: body,
    ...options || {}
  });
}
export async function deleteAppsIdTagsTagId(params, options) {
  const { id: param0, tag_id: param1, ...queryParams } = params;
  return request(`/apps/${param0}/tags/${param1}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...options || {}
  });
}
export async function getTags(params, options) {
  return request("/tags", {
    method: "GET",
    params: {
      ...params
    },
    ...options || {}
  });
}
export async function postTags(body, options) {
  return request("/tags", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    data: body,
    ...options || {}
  });
}
export async function getTagsId(params, options) {
  const { id: param0, ...queryParams } = params;
  return request(`/tags/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...options || {}
  });
}
export async function putTagsId(params, body, options) {
  const { id: param0, ...queryParams } = params;
  return request(`/tags/${param0}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json"
    },
    params: { ...queryParams },
    data: body,
    ...options || {}
  });
}
export async function deleteTagsId(params, options) {
  const { id: param0, ...queryParams } = params;
  return request(`/tags/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...options || {}
  });
}
export async function getTagsIdApps(params, options) {
  const { id: param0, ...queryParams } = params;
  return request(`/tags/${param0}/apps`, {
    method: "GET",
    params: { ...queryParams },
    ...options || {}
  });
}
