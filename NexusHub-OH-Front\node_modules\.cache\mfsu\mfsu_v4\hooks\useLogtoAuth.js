"use strict";
import { useLog<PERSON> } from "@logto/react";
import { useCallback, useEffect, useState } from "react";
export const useLogtoAuth = () => {
  const {
    isAuthenticated,
    isLoading,
    signIn,
    signOut,
    getAccessToken,
    getIdToken,
    getIdTokenClaims,
    fetchUserInfo,
    error
  } = useLogto();
  const [userInfo, setUserInfo] = useState(null);
  const [accessToken, setAccessToken] = useState(null);
  const getUserInfo = useCallback(async () => {
    if (!isAuthenticated) {
      setUserInfo(null);
      return null;
    }
    try {
      const info = await fetchUserInfo();
      const logtoUserInfo = {
        sub: info.sub,
        email: info.email,
        name: info.name,
        picture: info.picture,
        username: info.username,
        phone_number: info.phone_number,
        email_verified: info.email_verified,
        phone_number_verified: info.phone_number_verified
      };
      setUserInfo(logtoUserInfo);
      return logtoUserInfo;
    } catch (err) {
      console.error("Failed to fetch user info:", err);
      return null;
    }
  }, [isAuthenticated, fetchUserInfo]);
  const getToken = useCallback(async (resource) => {
    if (!isAuthenticated) {
      setAccessToken(null);
      localStorage.removeItem("logto_access_token");
      return null;
    }
    try {
      const token = await getAccessToken(resource);
      setAccessToken(token);
      if (token) {
        localStorage.setItem("logto_access_token", token);
      }
      return token;
    } catch (err) {
      console.error("Failed to get access token:", err);
      localStorage.removeItem("logto_access_token");
      return null;
    }
  }, [isAuthenticated, getAccessToken]);
  const handleSignIn = useCallback(async (redirectUri) => {
    try {
      await signIn(redirectUri || window.location.origin + "/callback");
    } catch (err) {
      console.error("Sign in failed:", err);
      throw err;
    }
  }, [signIn]);
  const handleSignOut = useCallback(async (postLogoutRedirectUri) => {
    try {
      setUserInfo(null);
      setAccessToken(null);
      localStorage.removeItem("logto_access_token");
      await signOut(postLogoutRedirectUri || window.location.origin);
    } catch (err) {
      console.error("Sign out failed:", err);
      throw err;
    }
  }, [signOut]);
  const hasPermission = useCallback((permission) => {
    return true;
  }, []);
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      getUserInfo();
      getToken(process.env.REACT_APP_LOGTO_API_RESOURCE);
    } else if (!isAuthenticated) {
      localStorage.removeItem("logto_access_token");
    }
  }, [isAuthenticated, isLoading, getUserInfo, getToken]);
  return {
    // 状态
    isAuthenticated,
    isLoading,
    userInfo,
    accessToken,
    error,
    // 方法
    signIn: handleSignIn,
    signOut: handleSignOut,
    getUserInfo,
    getToken,
    getIdToken,
    getIdTokenClaims,
    hasPermission
  };
};
export default useLogtoAuth;
