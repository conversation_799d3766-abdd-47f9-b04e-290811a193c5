{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "604d36cb-1172-4f5a-9dc5-d7b5ba7ab730", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930701228300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5bf20fb-307c-4fdd-9b99-6deed8daa7cb", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930701560700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89fd7d64-da1a-4d22-b7b2-2bc72ef5ce65", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930701954000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23549118-4eb6-4082-af62-3d3e937ae3b1", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930704806600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68b33d6c-8a3f-4273-83d2-09fc99edc675", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930705219700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72f2a7e3-3e8f-498d-9bff-0e9f13747168", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930706393300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffd1cac8-e561-408c-b841-b4bda93317d0", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930706907300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ede4005c-a8d1-42d7-ad12-b6046686b9ed", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930722496400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b71057f6-05bf-45b0-a02b-65f36c1e0b85", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930764833700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a7fc64-0ca5-44f5-8145-377059bcb77d", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962317424900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5648f8e9-9d32-489b-ba24-42decadf53a5", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962337546000, "endTime": 154962708292800}, "additional": {"children": ["b8fa361a-e00c-4acc-9b1e-d701d102ee2b", "843e3461-e16a-429c-8999-902d31f5bc9c", "544164b6-0f03-4981-b552-af5eae070f00", "0562ae55-46a9-46e3-8d9c-0095ad3d2115", "8188c915-553c-4725-934a-e3b14b65a686", "6ca53ff6-a2cb-4ea8-b0fc-bd6d2769fe3f", "210e7961-a80c-43a7-b263-c36e498c6c40"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "48bad539-e0a4-44ed-9b8c-49f4c541127c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8fa361a-e00c-4acc-9b1e-d701d102ee2b", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962337547800, "endTime": 154962369668400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5648f8e9-9d32-489b-ba24-42decadf53a5", "logId": "41a9bced-c554-4810-8984-427bee20f18a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "843e3461-e16a-429c-8999-902d31f5bc9c", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962369698000, "endTime": 154962706212700}, "additional": {"children": ["874afafc-acf0-40af-8adb-7edd019add66", "408cc1f4-a993-4969-b806-8169aa4e2a17", "74909c26-9fff-4c5a-ad80-03cc286c3360", "6f78f450-079d-4605-8f54-cb1c53c3f35a", "d21b4663-1cdb-4bd2-ba54-599738d907ee", "d880aa38-984a-4b72-8847-23b86bdacd18", "88b6059e-de0a-4e60-8cee-3a7b6c84c693", "f94718c4-92ee-479f-a8a8-bfce6a704f18", "e89c2a6f-506b-409f-adde-cbc318fa2709"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5648f8e9-9d32-489b-ba24-42decadf53a5", "logId": "9697e5a3-8101-49ae-84e1-a403e15e8d28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "544164b6-0f03-4981-b552-af5eae070f00", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962706252100, "endTime": 154962708265600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5648f8e9-9d32-489b-ba24-42decadf53a5", "logId": "a2947ae7-6364-404c-a70b-7d834f2e68ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0562ae55-46a9-46e3-8d9c-0095ad3d2115", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962708273300, "endTime": 154962708285500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5648f8e9-9d32-489b-ba24-42decadf53a5", "logId": "13e1d53c-1e67-4ad3-8466-eb119e799e14"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8188c915-553c-4725-934a-e3b14b65a686", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962347605700, "endTime": 154962347806400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5648f8e9-9d32-489b-ba24-42decadf53a5", "logId": "7aa13787-195c-4756-97fa-dedbb6cc174f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7aa13787-195c-4756-97fa-dedbb6cc174f", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962347605700, "endTime": 154962347806400}, "additional": {"logType": "info", "children": [], "durationId": "8188c915-553c-4725-934a-e3b14b65a686", "parent": "48bad539-e0a4-44ed-9b8c-49f4c541127c"}}, {"head": {"id": "6ca53ff6-a2cb-4ea8-b0fc-bd6d2769fe3f", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962358700000, "endTime": 154962358731100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5648f8e9-9d32-489b-ba24-42decadf53a5", "logId": "2a18c4f8-ab23-4dc3-aabb-61d05f62c1b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a18c4f8-ab23-4dc3-aabb-61d05f62c1b3", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962358700000, "endTime": 154962358731100}, "additional": {"logType": "info", "children": [], "durationId": "6ca53ff6-a2cb-4ea8-b0fc-bd6d2769fe3f", "parent": "48bad539-e0a4-44ed-9b8c-49f4c541127c"}}, {"head": {"id": "9e395d24-fd3b-43c5-89ac-b5890247df7c", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962358812900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87cd3bdf-bafa-4f73-ad08-dd1e26bfda58", "name": "Cache service initialization finished in 11 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962369437500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a9bced-c554-4810-8984-427bee20f18a", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962337547800, "endTime": 154962369668400}, "additional": {"logType": "info", "children": [], "durationId": "b8fa361a-e00c-4acc-9b1e-d701d102ee2b", "parent": "48bad539-e0a4-44ed-9b8c-49f4c541127c"}}, {"head": {"id": "874afafc-acf0-40af-8adb-7edd019add66", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962384924300, "endTime": 154962384946400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "843e3461-e16a-429c-8999-902d31f5bc9c", "logId": "d229977b-483e-4198-a109-7d4bd2c0ad56"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "408cc1f4-a993-4969-b806-8169aa4e2a17", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962384973600, "endTime": 154962395297900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "843e3461-e16a-429c-8999-902d31f5bc9c", "logId": "8480cb0e-d18c-480a-8583-3835b173257f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74909c26-9fff-4c5a-ad80-03cc286c3360", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962395322000, "endTime": 154962537334800}, "additional": {"children": ["3d66eaae-b395-4b1e-a9f0-d62b2ffc4595", "1abdac2d-eabd-4184-8052-580047e32160", "004c7b77-ff38-42ea-848f-ed2ad6b031ee"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "843e3461-e16a-429c-8999-902d31f5bc9c", "logId": "e5543eb1-2807-4fcb-b37a-f13946c282e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f78f450-079d-4605-8f54-cb1c53c3f35a", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962537358100, "endTime": 154962568681100}, "additional": {"children": ["8c484713-189b-497b-897d-a4dcc5d59ebf"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "843e3461-e16a-429c-8999-902d31f5bc9c", "logId": "40a6593a-55f0-4c26-8475-88bfd6b3cf73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d21b4663-1cdb-4bd2-ba54-599738d907ee", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962568690300, "endTime": 154962653836500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "843e3461-e16a-429c-8999-902d31f5bc9c", "logId": "4cd499e7-4cea-436e-9bfe-1749869ccfc4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d880aa38-984a-4b72-8847-23b86bdacd18", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962655466000, "endTime": 154962673746700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "843e3461-e16a-429c-8999-902d31f5bc9c", "logId": "d59cafe9-cbba-413b-8cc1-bf263649a681"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88b6059e-de0a-4e60-8cee-3a7b6c84c693", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962673776200, "endTime": 154962705899300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "843e3461-e16a-429c-8999-902d31f5bc9c", "logId": "562ef2eb-71eb-4efd-bbaf-68382a5c5969"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f94718c4-92ee-479f-a8a8-bfce6a704f18", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962705928800, "endTime": 154962706188300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "843e3461-e16a-429c-8999-902d31f5bc9c", "logId": "574f9339-7fc6-4fbe-ae12-2f9a5c1321b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d229977b-483e-4198-a109-7d4bd2c0ad56", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962384924300, "endTime": 154962384946400}, "additional": {"logType": "info", "children": [], "durationId": "874afafc-acf0-40af-8adb-7edd019add66", "parent": "9697e5a3-8101-49ae-84e1-a403e15e8d28"}}, {"head": {"id": "8480cb0e-d18c-480a-8583-3835b173257f", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962384973600, "endTime": 154962395297900}, "additional": {"logType": "info", "children": [], "durationId": "408cc1f4-a993-4969-b806-8169aa4e2a17", "parent": "9697e5a3-8101-49ae-84e1-a403e15e8d28"}}, {"head": {"id": "3d66eaae-b395-4b1e-a9f0-d62b2ffc4595", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962396875600, "endTime": 154962396909700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "74909c26-9fff-4c5a-ad80-03cc286c3360", "logId": "d9331a3d-c743-4905-8f82-937a466c1303"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9331a3d-c743-4905-8f82-937a466c1303", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962396875600, "endTime": 154962396909700}, "additional": {"logType": "info", "children": [], "durationId": "3d66eaae-b395-4b1e-a9f0-d62b2ffc4595", "parent": "e5543eb1-2807-4fcb-b37a-f13946c282e0"}}, {"head": {"id": "1abdac2d-eabd-4184-8052-580047e32160", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962401775500, "endTime": 154962536220800}, "additional": {"children": ["a2f52073-980c-4519-bd4a-b3f64170e424", "4b48a027-5fb3-47ef-aaa8-029fec23c103"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "74909c26-9fff-4c5a-ad80-03cc286c3360", "logId": "0a5651d6-4a27-4904-87af-846c3d9bb2c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2f52073-980c-4519-bd4a-b3f64170e424", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962401778700, "endTime": 154962410244400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1abdac2d-eabd-4184-8052-580047e32160", "logId": "66a3861f-b438-4a53-bf7c-3f828d8ebdca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b48a027-5fb3-47ef-aaa8-029fec23c103", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962410273200, "endTime": 154962536204300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1abdac2d-eabd-4184-8052-580047e32160", "logId": "f988226c-dfeb-4b94-901f-cb77884dae24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db269334-41c4-4b9a-b866-cc34e15027de", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962401785400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1d63063-2536-4576-b700-5fbe69a05e79", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962410017000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66a3861f-b438-4a53-bf7c-3f828d8ebdca", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962401778700, "endTime": 154962410244400}, "additional": {"logType": "info", "children": [], "durationId": "a2f52073-980c-4519-bd4a-b3f64170e424", "parent": "0a5651d6-4a27-4904-87af-846c3d9bb2c8"}}, {"head": {"id": "f3229799-eb7e-41b6-be49-ff5d7796879c", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962410304100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62e0eae5-c1ee-4218-8acf-bd73c77c711d", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962428991600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3ad41d3-994a-4620-a0d5-f04b9b63137a", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962429215700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "177012fb-75e2-4266-a533-c624ab39e940", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962430918400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca9b9c9c-5cdc-43b3-8f0d-5a66d71370af", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962431299900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "521764a6-0d12-496e-b982-33025464ab71", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962434822500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a69941d1-1257-471b-bf15-05f6abd22aea", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962459798100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b25afd9-30a7-41b1-8846-f75328aaf99d", "name": "Sdk init in 49 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962493272800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3aa7e1c-4276-4872-b274-c790b53f382c", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962493491500}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 59, "second": 52}, "markType": "other"}}, {"head": {"id": "49075e5d-2b1a-418c-908b-61a81df37b38", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962493513700}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 59, "second": 52}, "markType": "other"}}, {"head": {"id": "c9c4c907-c1ed-49a4-bc0a-8157dab74895", "name": "Project task initialization takes 40 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962535805000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53c22506-3a31-4a3d-9ef6-6518ea23d199", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962535979200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bb8e5fe-4a72-489e-8a4a-fe7528a1d541", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962536069900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fec38239-836d-4a3e-8e47-d63efb6b06c6", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962536139000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f988226c-dfeb-4b94-901f-cb77884dae24", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962410273200, "endTime": 154962536204300}, "additional": {"logType": "info", "children": [], "durationId": "4b48a027-5fb3-47ef-aaa8-029fec23c103", "parent": "0a5651d6-4a27-4904-87af-846c3d9bb2c8"}}, {"head": {"id": "0a5651d6-4a27-4904-87af-846c3d9bb2c8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962401775500, "endTime": 154962536220800}, "additional": {"logType": "info", "children": ["66a3861f-b438-4a53-bf7c-3f828d8ebdca", "f988226c-dfeb-4b94-901f-cb77884dae24"], "durationId": "1abdac2d-eabd-4184-8052-580047e32160", "parent": "e5543eb1-2807-4fcb-b37a-f13946c282e0"}}, {"head": {"id": "004c7b77-ff38-42ea-848f-ed2ad6b031ee", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962537294700, "endTime": 154962537314500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "74909c26-9fff-4c5a-ad80-03cc286c3360", "logId": "f39bdd79-064b-4d5e-8ccd-08d797337dd1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f39bdd79-064b-4d5e-8ccd-08d797337dd1", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962537294700, "endTime": 154962537314500}, "additional": {"logType": "info", "children": [], "durationId": "004c7b77-ff38-42ea-848f-ed2ad6b031ee", "parent": "e5543eb1-2807-4fcb-b37a-f13946c282e0"}}, {"head": {"id": "e5543eb1-2807-4fcb-b37a-f13946c282e0", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962395322000, "endTime": 154962537334800}, "additional": {"logType": "info", "children": ["d9331a3d-c743-4905-8f82-937a466c1303", "0a5651d6-4a27-4904-87af-846c3d9bb2c8", "f39bdd79-064b-4d5e-8ccd-08d797337dd1"], "durationId": "74909c26-9fff-4c5a-ad80-03cc286c3360", "parent": "9697e5a3-8101-49ae-84e1-a403e15e8d28"}}, {"head": {"id": "8c484713-189b-497b-897d-a4dcc5d59ebf", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962538401900, "endTime": 154962568669200}, "additional": {"children": ["26d3c83c-5235-4604-bdfc-87156fab8e09", "70403ed6-2fc7-4006-92bd-a4f769037784", "aa9d74b1-5d53-421b-8580-2c1a68c5ffca"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f78f450-079d-4605-8f54-cb1c53c3f35a", "logId": "87ae342f-edaf-4b42-8186-0fe9c036a361"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26d3c83c-5235-4604-bdfc-87156fab8e09", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962543392100, "endTime": 154962543409900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8c484713-189b-497b-897d-a4dcc5d59ebf", "logId": "04a520f8-1120-4fee-8a3e-116abb3b54af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04a520f8-1120-4fee-8a3e-116abb3b54af", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962543392100, "endTime": 154962543409900}, "additional": {"logType": "info", "children": [], "durationId": "26d3c83c-5235-4604-bdfc-87156fab8e09", "parent": "87ae342f-edaf-4b42-8186-0fe9c036a361"}}, {"head": {"id": "70403ed6-2fc7-4006-92bd-a4f769037784", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962546658300, "endTime": 154962566830800}, "additional": {"children": ["94ed15bd-9991-4b66-9372-96ee7adc1212", "3c5ad6a6-d74f-4611-89c6-a1e1fa45d9e4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8c484713-189b-497b-897d-a4dcc5d59ebf", "logId": "8b27a258-e121-4622-84c9-22d3b08af975"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94ed15bd-9991-4b66-9372-96ee7adc1212", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962546660100, "endTime": 154962549770300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "70403ed6-2fc7-4006-92bd-a4f769037784", "logId": "e9cc0ce4-6fa7-4b90-bd9b-8cf918412395"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c5ad6a6-d74f-4611-89c6-a1e1fa45d9e4", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962549785700, "endTime": 154962566818200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "70403ed6-2fc7-4006-92bd-a4f769037784", "logId": "818455fc-e311-461b-9c8e-e20dc25acbe5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62519f2e-cb16-4114-91b5-ee7cd6c97f33", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962546664500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25aa3999-28df-4fe7-9db4-6b668e08cb60", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962549655400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9cc0ce4-6fa7-4b90-bd9b-8cf918412395", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962546660100, "endTime": 154962549770300}, "additional": {"logType": "info", "children": [], "durationId": "94ed15bd-9991-4b66-9372-96ee7adc1212", "parent": "8b27a258-e121-4622-84c9-22d3b08af975"}}, {"head": {"id": "ed8b768d-b15d-41d1-928f-c0a72c348772", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962549801300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa59ca50-369f-4eaa-aafd-aed815d2f661", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962560349400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d8ffa19-0790-464f-bc0a-171181d0d33d", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962560486800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7da0a4c0-8329-42bc-a128-787a218c74dd", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962560752700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc34ef7c-5ce5-4680-b841-617205209537", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962560930900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f1920c2-0fbd-4e9c-a3fb-df261b563fd4", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962561013400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c647089-9d65-4aee-acd2-fe9541b9f848", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962561083800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5214700d-5d22-49db-93a9-d2480e57277f", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962561159100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99f30dfd-da6c-41e0-abd5-c02ccd18705c", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962561231100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d30dd47-c46c-4cb9-b917-da4f3b23eff3", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962561523400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd3fb501-13f1-41cb-a94f-79609ed78564", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962561693800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb6fd662-0e96-4611-b587-3b81171e6f3f", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962561766300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "792b1979-9b66-4f2a-97c2-890ce07522b8", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962561820200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ce8424e-e6b8-425d-85ef-e62df8fd6aee", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962561899800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b27eb2ec-acf6-4693-b83c-fbd355337ad2", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962561964900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5fe80d7-ba4f-496a-b6be-90c7109bdee7", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962562113900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c66c5845-014a-4e08-a987-272c13a9fb85", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962562279300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61f7d2ea-0c8e-4d9b-bdf1-e793aa8c13c5", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962562343500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae0c3766-455a-48cf-bf1d-0c48c7095494", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962562393100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02d17c93-9039-444a-a024-cdca542cf952", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962562456700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d73b93b9-e992-4bc0-a8b6-99a43e424781", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962566498400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4f52449-e012-48b7-9e2d-56ec79497e71", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962566638800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee545682-d29a-4dcb-956e-06a39348c8bc", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962566712200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cae1c080-0399-4512-b551-eb52cf9c43be", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962566768100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "818455fc-e311-461b-9c8e-e20dc25acbe5", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962549785700, "endTime": 154962566818200}, "additional": {"logType": "info", "children": [], "durationId": "3c5ad6a6-d74f-4611-89c6-a1e1fa45d9e4", "parent": "8b27a258-e121-4622-84c9-22d3b08af975"}}, {"head": {"id": "8b27a258-e121-4622-84c9-22d3b08af975", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962546658300, "endTime": 154962566830800}, "additional": {"logType": "info", "children": ["e9cc0ce4-6fa7-4b90-bd9b-8cf918412395", "818455fc-e311-461b-9c8e-e20dc25acbe5"], "durationId": "70403ed6-2fc7-4006-92bd-a4f769037784", "parent": "87ae342f-edaf-4b42-8186-0fe9c036a361"}}, {"head": {"id": "aa9d74b1-5d53-421b-8580-2c1a68c5ffca", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962568623900, "endTime": 154962568656000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8c484713-189b-497b-897d-a4dcc5d59ebf", "logId": "c743a61a-c4b9-4e90-9ada-e38653fc514d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c743a61a-c4b9-4e90-9ada-e38653fc514d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962568623900, "endTime": 154962568656000}, "additional": {"logType": "info", "children": [], "durationId": "aa9d74b1-5d53-421b-8580-2c1a68c5ffca", "parent": "87ae342f-edaf-4b42-8186-0fe9c036a361"}}, {"head": {"id": "87ae342f-edaf-4b42-8186-0fe9c036a361", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962538401900, "endTime": 154962568669200}, "additional": {"logType": "info", "children": ["04a520f8-1120-4fee-8a3e-116abb3b54af", "8b27a258-e121-4622-84c9-22d3b08af975", "c743a61a-c4b9-4e90-9ada-e38653fc514d"], "durationId": "8c484713-189b-497b-897d-a4dcc5d59ebf", "parent": "40a6593a-55f0-4c26-8475-88bfd6b3cf73"}}, {"head": {"id": "40a6593a-55f0-4c26-8475-88bfd6b3cf73", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962537358100, "endTime": 154962568681100}, "additional": {"logType": "info", "children": ["87ae342f-edaf-4b42-8186-0fe9c036a361"], "durationId": "6f78f450-079d-4605-8f54-cb1c53c3f35a", "parent": "9697e5a3-8101-49ae-84e1-a403e15e8d28"}}, {"head": {"id": "4a96a3ee-dc4c-47cc-b07b-6d98bde918b4", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962599424600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98122bda-880d-448c-bda8-160d43f9466e", "name": "hvigorfile, resolve hvigorfile dependencies in 85 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962653638200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cd499e7-4cea-436e-9bfe-1749869ccfc4", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962568690300, "endTime": 154962653836500}, "additional": {"logType": "info", "children": [], "durationId": "d21b4663-1cdb-4bd2-ba54-599738d907ee", "parent": "9697e5a3-8101-49ae-84e1-a403e15e8d28"}}, {"head": {"id": "e89c2a6f-506b-409f-adde-cbc318fa2709", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962655153300, "endTime": 154962655445700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "843e3461-e16a-429c-8999-902d31f5bc9c", "logId": "fc584641-bc4b-4751-b550-f34082ebffa9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "934b90a4-814c-46cf-a0ad-2e9521165722", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962655196900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc584641-bc4b-4751-b550-f34082ebffa9", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962655153300, "endTime": 154962655445700}, "additional": {"logType": "info", "children": [], "durationId": "e89c2a6f-506b-409f-adde-cbc318fa2709", "parent": "9697e5a3-8101-49ae-84e1-a403e15e8d28"}}, {"head": {"id": "decfeea9-b5fa-4ca1-85f5-a69cd7bdd37b", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962658777600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8959f55c-9933-4702-8d86-03c9335953ba", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962671783000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d59cafe9-cbba-413b-8cc1-bf263649a681", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962655466000, "endTime": 154962673746700}, "additional": {"logType": "info", "children": [], "durationId": "d880aa38-984a-4b72-8847-23b86bdacd18", "parent": "9697e5a3-8101-49ae-84e1-a403e15e8d28"}}, {"head": {"id": "e48623ea-4a68-44a0-8049-b7172659ef7b", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962673810300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "342591e8-99aa-49bd-86d1-b4f1bdaedbf0", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962691041500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2968dcd9-7d89-4ef3-8837-7dcb3093e29a", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962691226500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "920f9757-33e9-4229-900b-d7b93325c5ac", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962691487600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "554f1559-f6c9-4e19-b235-6362d8de217d", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962698606600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43d3dd04-23c0-497c-be30-74658d3878aa", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962698842000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "562ef2eb-71eb-4efd-bbaf-68382a5c5969", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962673776200, "endTime": 154962705899300}, "additional": {"logType": "info", "children": [], "durationId": "88b6059e-de0a-4e60-8cee-3a7b6c84c693", "parent": "9697e5a3-8101-49ae-84e1-a403e15e8d28"}}, {"head": {"id": "143c212e-4447-442d-b31c-ca19ef9b748b", "name": "Configuration phase cost:322 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962705978900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "574f9339-7fc6-4fbe-ae12-2f9a5c1321b9", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962705928800, "endTime": 154962706188300}, "additional": {"logType": "info", "children": [], "durationId": "f94718c4-92ee-479f-a8a8-bfce6a704f18", "parent": "9697e5a3-8101-49ae-84e1-a403e15e8d28"}}, {"head": {"id": "9697e5a3-8101-49ae-84e1-a403e15e8d28", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962369698000, "endTime": 154962706212700}, "additional": {"logType": "info", "children": ["d229977b-483e-4198-a109-7d4bd2c0ad56", "8480cb0e-d18c-480a-8583-3835b173257f", "e5543eb1-2807-4fcb-b37a-f13946c282e0", "40a6593a-55f0-4c26-8475-88bfd6b3cf73", "4cd499e7-4cea-436e-9bfe-1749869ccfc4", "d59cafe9-cbba-413b-8cc1-bf263649a681", "562ef2eb-71eb-4efd-bbaf-68382a5c5969", "574f9339-7fc6-4fbe-ae12-2f9a5c1321b9", "fc584641-bc4b-4751-b550-f34082ebffa9"], "durationId": "843e3461-e16a-429c-8999-902d31f5bc9c", "parent": "48bad539-e0a4-44ed-9b8c-49f4c541127c"}}, {"head": {"id": "210e7961-a80c-43a7-b263-c36e498c6c40", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962708230100, "endTime": 154962708252300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5648f8e9-9d32-489b-ba24-42decadf53a5", "logId": "9a2401f9-3e4d-4194-8750-19c77393ad9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a2401f9-3e4d-4194-8750-19c77393ad9e", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962708230100, "endTime": 154962708252300}, "additional": {"logType": "info", "children": [], "durationId": "210e7961-a80c-43a7-b263-c36e498c6c40", "parent": "48bad539-e0a4-44ed-9b8c-49f4c541127c"}}, {"head": {"id": "a2947ae7-6364-404c-a70b-7d834f2e68ee", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962706252100, "endTime": 154962708265600}, "additional": {"logType": "info", "children": [], "durationId": "544164b6-0f03-4981-b552-af5eae070f00", "parent": "48bad539-e0a4-44ed-9b8c-49f4c541127c"}}, {"head": {"id": "13e1d53c-1e67-4ad3-8466-eb119e799e14", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962708273300, "endTime": 154962708285500}, "additional": {"logType": "info", "children": [], "durationId": "0562ae55-46a9-46e3-8d9c-0095ad3d2115", "parent": "48bad539-e0a4-44ed-9b8c-49f4c541127c"}}, {"head": {"id": "48bad539-e0a4-44ed-9b8c-49f4c541127c", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962337546000, "endTime": 154962708292800}, "additional": {"logType": "info", "children": ["41a9bced-c554-4810-8984-427bee20f18a", "9697e5a3-8101-49ae-84e1-a403e15e8d28", "a2947ae7-6364-404c-a70b-7d834f2e68ee", "13e1d53c-1e67-4ad3-8466-eb119e799e14", "7aa13787-195c-4756-97fa-dedbb6cc174f", "2a18c4f8-ab23-4dc3-aabb-61d05f62c1b3", "9a2401f9-3e4d-4194-8750-19c77393ad9e"], "durationId": "5648f8e9-9d32-489b-ba24-42decadf53a5"}}, {"head": {"id": "78203474-e6ec-4ff1-be96-81eed7f0835a", "name": "Configuration task cost before running: 381 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962708726500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f06f4ec0-7eaf-4f4b-a049-cef06e157756", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962725988700, "endTime": 154962748414300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "aa6eb797-a0d0-4758-9522-6ae4578d0340", "logId": "4984828b-c5c6-4c34-ad6b-ea2174f2a9d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa6eb797-a0d0-4758-9522-6ae4578d0340", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962711631700}, "additional": {"logType": "detail", "children": [], "durationId": "f06f4ec0-7eaf-4f4b-a049-cef06e157756"}}, {"head": {"id": "ebe6cae2-463f-43d3-998a-b79dec1ef5de", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962713269300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddc7731f-cac7-47cd-8610-240ec703cddc", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962713523000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cfadf4f-3e48-4e69-be18-f5bc1f26d71c", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962715110500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f27e20bf-778b-4d7a-a361-7d514d78e5c5", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962716419800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2ae13b6-3ef3-457a-b574-b81dcb7bcd25", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962718317900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6babb963-48bd-4845-8079-0e32a38a1f85", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962718461900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54b3c22d-c06b-4015-993a-271dca04cc3e", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962726011100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1ba8daf-2056-44a5-85a8-0036a50a8e81", "name": "Incremental task entry:default@PreBuild pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962748100000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8194483e-8220-4874-8b0f-0f19b9663eed", "name": "entry : default@PreBuild cost memory 0.31511688232421875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962748297000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4984828b-c5c6-4c34-ad6b-ea2174f2a9d2", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962725988700, "endTime": 154962748414300}, "additional": {"logType": "info", "children": [], "durationId": "f06f4ec0-7eaf-4f4b-a049-cef06e157756"}}, {"head": {"id": "e9799a57-1260-4926-b46f-4fe743687f0d", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962760614700, "endTime": 154962766397500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f1dbe954-25d7-474c-a46e-12072cc30afd", "logId": "0d9a1cf0-be8e-485f-a5ff-86a046fb2e23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1dbe954-25d7-474c-a46e-12072cc30afd", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962756822100}, "additional": {"logType": "detail", "children": [], "durationId": "e9799a57-1260-4926-b46f-4fe743687f0d"}}, {"head": {"id": "c9bddd60-dd8d-4a03-a8e5-8622b8a052ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962759006100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8bc8a97-01b8-411c-b22e-30f1d8780ed3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962759183800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffd6ef5d-1bd0-4252-9988-606a8035af3a", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962760639500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b781902-e22b-465d-896e-2a0eaf647bce", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962761987800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f332307-4cd9-40c5-ac43-15e6242e0ea9", "name": "entry : default@CreateModuleInfo cost memory 0.06047821044921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962763697600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b863d106-f5b3-4dcb-a5a1-9e4739c4703c", "name": "runTaskFromQueue task cost before running: 436 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962763936600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d9a1cf0-be8e-485f-a5ff-86a046fb2e23", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962760614700, "endTime": 154962766397500, "totalTime": 3274300}, "additional": {"logType": "info", "children": [], "durationId": "e9799a57-1260-4926-b46f-4fe743687f0d"}}, {"head": {"id": "6f8ad727-9c2e-4c71-821c-6565f9b89143", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962777794600, "endTime": 154962782930700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "01172518-9f14-4f56-8ca2-3832f2b9c18b", "logId": "ac07ec02-99aa-41ac-bed0-b65796595264"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01172518-9f14-4f56-8ca2-3832f2b9c18b", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962768662300}, "additional": {"logType": "detail", "children": [], "durationId": "6f8ad727-9c2e-4c71-821c-6565f9b89143"}}, {"head": {"id": "49d28b6e-7b5a-41d2-89f6-5b7df5d789c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962769650900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d2aaf7a-4fe1-4eda-b315-1eedb74dceb6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962769752600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef79264a-f06b-4cda-a969-17a355f46357", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962777823900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5403d1c9-0cf4-45ca-b341-04e548854008", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962780101700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fe0b85f-f9c1-42eb-a96e-6ab6c143f46f", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962782596000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03f763a7-1d93-49d6-b294-f0df3fc0bd9a", "name": "entry : default@GenerateMetadata cost memory 0.10186767578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962782802700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac07ec02-99aa-41ac-bed0-b65796595264", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962777794600, "endTime": 154962782930700}, "additional": {"logType": "info", "children": [], "durationId": "6f8ad727-9c2e-4c71-821c-6565f9b89143"}}, {"head": {"id": "2b20783e-bd4f-4e3e-868a-3412ca9598e9", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962788166200, "endTime": 154962788670800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2e132994-3a32-442f-aba1-7828c9283d9c", "logId": "e588ae7d-a388-4b5d-b1e4-6226c9525a90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e132994-3a32-442f-aba1-7828c9283d9c", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962785811400}, "additional": {"logType": "detail", "children": [], "durationId": "2b20783e-bd4f-4e3e-868a-3412ca9598e9"}}, {"head": {"id": "4071ebfd-5ac8-4230-b3db-74325ca45db8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962787821200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "459703af-025c-4968-b7ac-c181a1bcb762", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962787974800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1e12a4a-9d75-47a5-929e-ea6a7cad872c", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962788177900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bc818cd-9f51-47aa-93b4-c81b867cc0dc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962788305100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d7a11f6-d255-4615-b19f-ae6a1cd4f45b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962788381700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de3be938-9de2-407f-b84a-4bf1f2095a7c", "name": "entry : default@ConfigureCmake cost memory 0.03708648681640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962788486900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e482866-1f08-42b4-9ed7-cfff0d6ab0cd", "name": "runTaskFromQueue task cost before running: 461 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962788594000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e588ae7d-a388-4b5d-b1e4-6226c9525a90", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962788166200, "endTime": 154962788670800, "totalTime": 404400}, "additional": {"logType": "info", "children": [], "durationId": "2b20783e-bd4f-4e3e-868a-3412ca9598e9"}}, {"head": {"id": "591133db-e230-4d75-a67a-8db7b54f7468", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962795246300, "endTime": 154962799648200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "44eed713-6a94-4776-8906-be63dcdc947e", "logId": "9df9e4bc-b467-4b7b-81b2-f866db9979cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44eed713-6a94-4776-8906-be63dcdc947e", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962791613600}, "additional": {"logType": "detail", "children": [], "durationId": "591133db-e230-4d75-a67a-8db7b54f7468"}}, {"head": {"id": "216573cb-48df-4a47-acd9-d2de6e3a105b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962793714100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62a184dc-2bd0-44e4-9a5e-dffeb89d5d78", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962793912200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a9fccdb-0f70-4f4f-8f1e-b44a596ff342", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962795268500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52a8a1ac-3968-454c-92a0-04fac2e7427a", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962799324900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e93a1f68-7aad-41d5-86cb-83180b3eff83", "name": "entry : default@MergeProfile cost memory 0.16614532470703125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962799528000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9df9e4bc-b467-4b7b-81b2-f866db9979cd", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962795246300, "endTime": 154962799648200}, "additional": {"logType": "info", "children": [], "durationId": "591133db-e230-4d75-a67a-8db7b54f7468"}}, {"head": {"id": "ea3a9dfb-283f-4470-b81d-1ebfdba0c8f2", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962806342300, "endTime": 154962812073100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3366fa8c-48ec-4645-81f0-5867366be5fa", "logId": "dbabd51b-c616-4a65-9da6-e6582038ba84"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3366fa8c-48ec-4645-81f0-5867366be5fa", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962802733400}, "additional": {"logType": "detail", "children": [], "durationId": "ea3a9dfb-283f-4470-b81d-1ebfdba0c8f2"}}, {"head": {"id": "5834e408-ed5b-4032-b715-10ae1a50ab85", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962804795500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b259a9db-40b8-440d-b6a6-6be7cbdf7e1f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962804955800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "594c4a0f-c4a7-4932-99ae-4e04373726c7", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962806355800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c60c250c-24e4-499d-a955-3e7bcc617f9b", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962808346000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21c1c4ba-b334-4f32-b4f2-daabf2b6bc3f", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962811695600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af3d183a-3325-42b7-bb1d-63542e930d54", "name": "entry : default@CreateBuildProfile cost memory 0.10581207275390625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962811930800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbabd51b-c616-4a65-9da6-e6582038ba84", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962806342300, "endTime": 154962812073100}, "additional": {"logType": "info", "children": [], "durationId": "ea3a9dfb-283f-4470-b81d-1ebfdba0c8f2"}}, {"head": {"id": "2196a5ea-f29b-4e90-b20f-56ac41ee755d", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962817959300, "endTime": 154962818816400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "8c904a0c-e1af-449c-8d41-a53b78dea06a", "logId": "7bac8ac3-3c3b-4243-ab8a-38e238263192"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c904a0c-e1af-449c-8d41-a53b78dea06a", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962814729700}, "additional": {"logType": "detail", "children": [], "durationId": "2196a5ea-f29b-4e90-b20f-56ac41ee755d"}}, {"head": {"id": "e62a486d-495b-4d6e-8f19-841130f77724", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962816569100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f44cecac-a3f2-44f7-9bea-d6a2bf70f657", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962816713400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59fabefb-7ee3-4ee5-b6e5-7a7591bace95", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962817971900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "901f7a0d-e91f-4775-88f3-c66c8b7e48b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962818136500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8921d298-9fe9-45b9-9875-2f631f21ed64", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962818233800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bc6de9e-5516-4596-a0c6-45565abedbd6", "name": "entry : default@PreCheckSyscap cost memory 0.0406494140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962818587400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "403d079f-9ced-4fac-81e1-0943f6e50ffe", "name": "runTaskFromQueue task cost before running: 491 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962818727600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bac8ac3-3c3b-4243-ab8a-38e238263192", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962817959300, "endTime": 154962818816400, "totalTime": 743300}, "additional": {"logType": "info", "children": [], "durationId": "2196a5ea-f29b-4e90-b20f-56ac41ee755d"}}, {"head": {"id": "6c8e2bd2-b079-4185-81fa-16316daf6513", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962826582600, "endTime": 154962837536900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7b9f6181-70cb-4878-bc1a-6409dca0e144", "logId": "f50b7b8d-9dee-4e56-a80f-9a49e141b7df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b9f6181-70cb-4878-bc1a-6409dca0e144", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962821239900}, "additional": {"logType": "detail", "children": [], "durationId": "6c8e2bd2-b079-4185-81fa-16316daf6513"}}, {"head": {"id": "37d13513-8cbf-49ca-b6c9-088aca1bc192", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962823328000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61f8815d-8233-42a4-9465-a00dae1aa5b9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962823498100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8030e4b-bae5-44ee-b24e-9c311a779a00", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962826603500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52cf2ea8-65c3-4b13-b4a0-9305f091bf54", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962835899900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "926991af-3a84-492d-b83a-a46252ec5c31", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962837251400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ca4f27b-1cab-453b-b110-26890a147bc6", "name": "entry : default@GeneratePkgContextInfo cost memory 0.256103515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962837427000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f50b7b8d-9dee-4e56-a80f-9a49e141b7df", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962826582600, "endTime": 154962837536900}, "additional": {"logType": "info", "children": [], "durationId": "6c8e2bd2-b079-4185-81fa-16316daf6513"}}, {"head": {"id": "73a1ffac-e465-4b8c-843c-c773116c4a74", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962853785500, "endTime": 154962857710000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "adb74598-fd7f-4ac1-a718-df430be6c16f", "logId": "1e1baed2-f55a-42c3-9b33-713da19c30b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "adb74598-fd7f-4ac1-a718-df430be6c16f", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962840455400}, "additional": {"logType": "detail", "children": [], "durationId": "73a1ffac-e465-4b8c-843c-c773116c4a74"}}, {"head": {"id": "9631b6cb-a39c-4ebd-930b-c9d6f9908850", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962842744800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2545a15c-0c64-40a7-a391-4a8addfc9fdb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962842944700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e71596d-29c7-427a-ad7b-57f9b1a0058c", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962853813300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99116382-22a6-49ad-ab28-ba062815bee7", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962857004000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7c88f11-b16d-4244-8efc-2b764ef967c1", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962857180100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51b42967-9ed1-4da0-8938-85f89aaa75a3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962857299500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9fcb000-13a1-4c38-8a33-77c051c18808", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962857396100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca98b880-31e5-49f7-b18e-b0c1ff169549", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11942291259765625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962857527100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a23504cc-8333-4fb1-81b5-ab4c8be015df", "name": "runTaskFromQueue task cost before running: 530 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962857637400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e1baed2-f55a-42c3-9b33-713da19c30b5", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962853785500, "endTime": 154962857710000, "totalTime": 3836300}, "additional": {"logType": "info", "children": [], "durationId": "73a1ffac-e465-4b8c-843c-c773116c4a74"}}, {"head": {"id": "bd450e24-afd8-4300-82d7-7078f0c0decf", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962864857900, "endTime": 154962865512000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c877d43c-e491-4726-a62c-933fa53a292c", "logId": "671a11a4-2344-4006-9839-2bdcb8bf69a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c877d43c-e491-4726-a62c-933fa53a292c", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962861405200}, "additional": {"logType": "detail", "children": [], "durationId": "bd450e24-afd8-4300-82d7-7078f0c0decf"}}, {"head": {"id": "a034740b-591a-476f-b513-4635b0cc0381", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962863431300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72d15a69-bc59-4b84-b7e7-9c58d171e08e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962863592000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afc25a04-21da-468c-8859-6ba8eb594077", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962864871100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0c16901-0cf0-454e-a5e2-0c9e70173712", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962865063300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "775e40c8-1bf4-4462-aae7-c928acd02a72", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962865179000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b417a44-e912-40b7-ba68-d96db48af0b2", "name": "entry : default@BuildNativeWithCmake cost memory 0.03806304931640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962865300300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40d7f14f-dc8f-499e-9071-bcbeaa855041", "name": "runTaskFromQueue task cost before running: 538 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962865424700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "671a11a4-2344-4006-9839-2bdcb8bf69a5", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962864857900, "endTime": 154962865512000, "totalTime": 541200}, "additional": {"logType": "info", "children": [], "durationId": "bd450e24-afd8-4300-82d7-7078f0c0decf"}}, {"head": {"id": "f463bffe-8590-49b6-8f15-526efa9b3085", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962871912400, "endTime": 154962879194500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5b889a7e-04ec-4221-b0a2-9ad57d948b88", "logId": "9a5a7d48-5e9f-48d1-ba2f-bb6887b202ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b889a7e-04ec-4221-b0a2-9ad57d948b88", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962868381300}, "additional": {"logType": "detail", "children": [], "durationId": "f463bffe-8590-49b6-8f15-526efa9b3085"}}, {"head": {"id": "a4b20fb3-218e-47e6-bb8b-642031d55f35", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962870502800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f4d8c58-8114-49d7-9e0e-f603952bd052", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962870657200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7523cd1d-8f37-409a-affb-8425de8f90c6", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962871928100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8b09d30-0652-4305-8aed-cfe8271d0e3b", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962878864900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "446e26f1-6e7a-420e-8fca-c0a1f7f3f5e0", "name": "entry : default@MakePackInfo cost memory 0.16091156005859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962879073900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a5a7d48-5e9f-48d1-ba2f-bb6887b202ca", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962871912400, "endTime": 154962879194500}, "additional": {"logType": "info", "children": [], "durationId": "f463bffe-8590-49b6-8f15-526efa9b3085"}}, {"head": {"id": "8b7858dc-459f-438c-9467-0bb8ccca171a", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962887496500, "endTime": 154962894924700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "79c44131-6fcb-4d94-bc73-10ad51de8ea3", "logId": "05e8116f-9a62-4086-a4d7-46db005ab706"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79c44131-6fcb-4d94-bc73-10ad51de8ea3", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962882958800}, "additional": {"logType": "detail", "children": [], "durationId": "8b7858dc-459f-438c-9467-0bb8ccca171a"}}, {"head": {"id": "665b6b32-16b0-4ed2-ba11-f4d2af0f570a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962884944000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4682986-00fb-45fa-8eb0-24fe83fdf89d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962885114100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb09fc67-61bc-4b1c-bc90-8ff1b47ac796", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962887511900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dc7dff5-2ef3-472a-af33-f280113c72dc", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962887845500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac784fb6-72ba-417b-90f6-8236165a9d60", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962889505000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08c194ae-69f4-4152-a087-4278ef6e53da", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962894619700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21be3851-1145-4700-9aec-c3ef48c8415d", "name": "entry : default@SyscapTransform cost memory 0.1489105224609375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962894815400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05e8116f-9a62-4086-a4d7-46db005ab706", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962887496500, "endTime": 154962894924700}, "additional": {"logType": "info", "children": [], "durationId": "8b7858dc-459f-438c-9467-0bb8ccca171a"}}, {"head": {"id": "3fa2a4aa-f5d3-48c4-9144-bcbc176c041f", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962901588500, "endTime": 154962904398500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "33b9619f-d09c-46d5-80dc-e1d7ef867477", "logId": "dd845952-cda7-4459-9e6f-0ab247f67894"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33b9619f-d09c-46d5-80dc-e1d7ef867477", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962897530400}, "additional": {"logType": "detail", "children": [], "durationId": "3fa2a4aa-f5d3-48c4-9144-bcbc176c041f"}}, {"head": {"id": "86b4f1c1-c0d9-42d5-9ed5-7cdcb9038590", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962899454300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53b31804-24e5-4dd0-9bd5-bbb41d17b5aa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962899704500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70e4260c-d11a-4fa3-887e-d143fc75d599", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962901600000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb7f6fd1-d376-4f2c-a9e0-e631ab5b929b", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962904190300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3139d93-a4bc-4419-a775-5843d3f86af4", "name": "entry : default@ProcessProfile cost memory 0.1209259033203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962904312800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd845952-cda7-4459-9e6f-0ab247f67894", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962901588500, "endTime": 154962904398500}, "additional": {"logType": "info", "children": [], "durationId": "3fa2a4aa-f5d3-48c4-9144-bcbc176c041f"}}, {"head": {"id": "e83cd30b-652d-4a94-b8a8-fe1ee8df9086", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962911421100, "endTime": 154962921228600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fce07a30-2e9e-4689-a0bf-b5a53640a7b9", "logId": "53e38d85-06ac-4551-8cd4-4c1b70c4c557"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fce07a30-2e9e-4689-a0bf-b5a53640a7b9", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962906609400}, "additional": {"logType": "detail", "children": [], "durationId": "e83cd30b-652d-4a94-b8a8-fe1ee8df9086"}}, {"head": {"id": "fdc974cc-a8d7-4ca0-8d8b-12b07174a51f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962908340300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f030f190-da79-4cfa-94c3-ec12b4e50feb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962908471500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75aac606-81fc-417f-b44d-4d0d24b0ba6e", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962911436900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "575af663-d173-44cf-a3fd-2e8fb174b8f1", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962920954400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0261df9-7f34-4bbb-9617-6ecebcdbba43", "name": "entry : default@ProcessRouterMap cost memory 0.23116302490234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962921130600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53e38d85-06ac-4551-8cd4-4c1b70c4c557", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962911421100, "endTime": 154962921228600}, "additional": {"logType": "info", "children": [], "durationId": "e83cd30b-652d-4a94-b8a8-fe1ee8df9086"}}, {"head": {"id": "84293376-cfb6-465d-ada2-a2154cb911ba", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962929181900, "endTime": 154962937811000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "3f845604-7df2-4929-942d-61507519dfe8", "logId": "9c03361a-4509-4f76-bb81-c06451c715a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f845604-7df2-4929-942d-61507519dfe8", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962925474500}, "additional": {"logType": "detail", "children": [], "durationId": "84293376-cfb6-465d-ada2-a2154cb911ba"}}, {"head": {"id": "397cdf43-5619-4764-91ea-8f8b0ccf4da8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962928921200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfc35bc1-e1ba-4f97-b8b0-5af4fbbd0b00", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962929060100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f1efc8c-fd3e-4bf8-ab28-a196b9051bd7", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962929192700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca71a3af-dba9-4403-b2c3-759040b34f89", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962929337500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09473bbe-6b95-4c0c-a5f6-44161ed9eb5e", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962935719300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae43d34b-0975-4940-8c65-e85ec839175e", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962935867800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7284e836-44a9-4b87-b4a1-6d3f2ac074dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962935971300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1b2f5cd-2400-40ae-a943-a330f3d037e6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962936043400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63a180bc-0a38-4320-87ba-6416c13ed816", "name": "entry : default@ProcessStartupConfig cost memory 0.2574615478515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962937589600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78ecff78-5b77-47d2-9553-ffc543f8c015", "name": "runTaskFromQueue task cost before running: 610 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962937733400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c03361a-4509-4f76-bb81-c06451c715a9", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962929181900, "endTime": 154962937811000, "totalTime": 8526700}, "additional": {"logType": "info", "children": [], "durationId": "84293376-cfb6-465d-ada2-a2154cb911ba"}}, {"head": {"id": "0ce780d2-b216-4c37-9ad1-a6080cb90d42", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962945291400, "endTime": 154962947483800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "1a7b3be9-bc3c-40e3-833e-c3861c15be9a", "logId": "852e6e83-0b40-4e75-b65e-44a9682c565d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a7b3be9-bc3c-40e3-833e-c3861c15be9a", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962941814500}, "additional": {"logType": "detail", "children": [], "durationId": "0ce780d2-b216-4c37-9ad1-a6080cb90d42"}}, {"head": {"id": "156532b4-90e0-45a4-8bab-f2bde837c946", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962943488300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23baf60b-c2e5-4747-8f8e-65028691ed3a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962943650700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "068c7b6a-71a5-4f4e-baf1-66ad12e0cac1", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962945311700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "940ffcdb-c1a1-4a64-b90a-f3d298a2268e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962945571100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a3d3eb4-cdbb-4cec-8e90-6476de51496f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962945656600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "096c3e1b-91d7-4ad6-9841-43627da8b233", "name": "entry : default@BuildNativeWithNinja cost memory 0.057708740234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962947253700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7d3ebed-f314-414a-93c1-c35406f885de", "name": "runTaskFromQueue task cost before running: 620 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962947404800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "852e6e83-0b40-4e75-b65e-44a9682c565d", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962945291400, "endTime": 154962947483800, "totalTime": 2089800}, "additional": {"logType": "info", "children": [], "durationId": "0ce780d2-b216-4c37-9ad1-a6080cb90d42"}}, {"head": {"id": "0e99d02a-97e9-4330-abdc-98adfb2c8bfb", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962956435400, "endTime": 154962965606000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "e66fa954-9335-44bc-89bc-28f904a1d651", "logId": "ba029c20-614c-4f4a-b658-63105c742305"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e66fa954-9335-44bc-89bc-28f904a1d651", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962950963700}, "additional": {"logType": "detail", "children": [], "durationId": "0e99d02a-97e9-4330-abdc-98adfb2c8bfb"}}, {"head": {"id": "e473340c-2684-42b1-a336-ba5dec29bb98", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962952670500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4f7e4e3-feb3-4b78-96c2-fa6a08f1ec18", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962952791000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71c11eef-49f8-44e4-a166-8ac5c8de2a4d", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962954526000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7d8156f-2814-4d90-bf9d-90c2c772a71f", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962959060900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "879157b9-f273-46af-95c4-bae08acbde9f", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962962690500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b6fc46-c670-4c72-a685-02c26254af47", "name": "entry : default@ProcessResource cost memory 0.16001129150390625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962962857800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba029c20-614c-4f4a-b658-63105c742305", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962956435400, "endTime": 154962965606000}, "additional": {"logType": "info", "children": [], "durationId": "0e99d02a-97e9-4330-abdc-98adfb2c8bfb"}}, {"head": {"id": "8e2007af-6e91-4cc8-a639-9f373d42e6de", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962976162800, "endTime": 154963000191500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "25386375-099a-4485-827c-8a00f5b8a3d6", "logId": "50a8003f-300b-45ea-8b7a-dd7d2a5c26f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25386375-099a-4485-827c-8a00f5b8a3d6", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962968693800}, "additional": {"logType": "detail", "children": [], "durationId": "8e2007af-6e91-4cc8-a639-9f373d42e6de"}}, {"head": {"id": "ee5e2548-fea7-43de-bc3f-26dc444312de", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962970335300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df20699f-27a4-487c-a1e3-d58672e01c97", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962970475500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca8bda2c-a81e-4726-8a36-c664b9b9e15f", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962976178300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87ba3425-11c3-4dd8-b696-182060fd9f72", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962999989200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6bcc644-6488-40d5-a081-1741bd0f2aa1", "name": "entry : default@GenerateLoaderJson cost memory 0.86444091796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963000133500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50a8003f-300b-45ea-8b7a-dd7d2a5c26f5", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962976162800, "endTime": 154963000191500}, "additional": {"logType": "info", "children": [], "durationId": "8e2007af-6e91-4cc8-a639-9f373d42e6de"}}, {"head": {"id": "4c7168f0-c2ad-4406-83ff-13a6ca121065", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963011498000, "endTime": 154963017533000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "8291d69d-bebc-4072-a734-6ac4e18100f3", "logId": "d95d36c7-a0b5-4936-80c7-05abc7663288"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8291d69d-bebc-4072-a734-6ac4e18100f3", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963008997500}, "additional": {"logType": "detail", "children": [], "durationId": "4c7168f0-c2ad-4406-83ff-13a6ca121065"}}, {"head": {"id": "d1e25e0d-dd90-4c8e-b43f-65d254e196b2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963009950900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dde68a9-4ff7-4fb5-af79-82ea5377561c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963010061300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fd0afd4-0221-40f4-bc55-43cf532c5550", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963011526500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f7a4006-4209-42f4-a2ee-87f0a49183e1", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963017310700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d1ba944-1cff-43d0-a70c-ef4547e762ef", "name": "entry : default@ProcessLibs cost memory 0.14017486572265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963017457600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d95d36c7-a0b5-4936-80c7-05abc7663288", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963011498000, "endTime": 154963017533000}, "additional": {"logType": "info", "children": [], "durationId": "4c7168f0-c2ad-4406-83ff-13a6ca121065"}}, {"head": {"id": "851bc0a8-6f16-4fc9-baaf-c648aebaefc0", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963029753100, "endTime": 154963086134300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "dbc7ba22-4b5e-4372-90da-35e5652b4eb2", "logId": "26cdae26-7a72-453f-99f6-139ff55adfef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbc7ba22-4b5e-4372-90da-35e5652b4eb2", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963020873400}, "additional": {"logType": "detail", "children": [], "durationId": "851bc0a8-6f16-4fc9-baaf-c648aebaefc0"}}, {"head": {"id": "c8f9accc-2e2c-488b-8af2-9a573b2b0abd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963022839100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c145878f-f98c-4ddc-9133-006b623e3c2a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963022977200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8ecabef-6a82-4b95-8169-0139e99f5b69", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963024950000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ce38c3e-b9e2-4e6a-ac2a-1edfee58ff48", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963029783300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52760427-9a5a-415f-91e8-f86e9030e8c9", "name": "Incremental task entry:default@CompileResource pre-execution cost: 55 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963085809600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b091228-efd3-49fa-9f0d-8d7d5ebfca63", "name": "entry : default@CompileResource cost memory 1.3094100952148438", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963086004900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26cdae26-7a72-453f-99f6-139ff55adfef", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963029753100, "endTime": 154963086134300}, "additional": {"logType": "info", "children": [], "durationId": "851bc0a8-6f16-4fc9-baaf-c648aebaefc0"}}, {"head": {"id": "6a2b3855-edfe-43ca-bfa9-c0d4c9fb4885", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963092041200, "endTime": 154963095520400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f225bf27-1b76-4e57-a792-3aa59134b9e4", "logId": "9d6f1015-9472-4b63-addd-4b05e334d4fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f225bf27-1b76-4e57-a792-3aa59134b9e4", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963088865700}, "additional": {"logType": "detail", "children": [], "durationId": "6a2b3855-edfe-43ca-bfa9-c0d4c9fb4885"}}, {"head": {"id": "a8e77f37-b16d-4112-8c5c-463f5fcfec93", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963089701700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cfab1db-eccd-4adb-a84e-d780d8f7bfe2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963089780300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01be7081-0f5b-48c1-be5f-81a37e04b222", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963092049100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dae7185e-d7e3-4eb4-9b1d-dbd040404f3b", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963092486000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d146b32-2d83-4700-8280-9395e434ff86", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963095237400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d1f4e83-8fc9-4ee4-a2fe-fa1aa4d91434", "name": "entry : default@DoNativeStrip cost memory 0.07787322998046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963095409900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d6f1015-9472-4b63-addd-4b05e334d4fb", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963092041200, "endTime": 154963095520400}, "additional": {"logType": "info", "children": [], "durationId": "6a2b3855-edfe-43ca-bfa9-c0d4c9fb4885"}}, {"head": {"id": "50897dd1-5a56-42f9-8214-21b9e4c576a1", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963108083200, "endTime": 154963154775900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "6551c85a-fbd1-4dd9-96c0-1beb602daf78", "logId": "6d8b339d-91d3-4356-baa9-efe58296916a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6551c85a-fbd1-4dd9-96c0-1beb602daf78", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963098200700}, "additional": {"logType": "detail", "children": [], "durationId": "50897dd1-5a56-42f9-8214-21b9e4c576a1"}}, {"head": {"id": "7441e5d4-3a0b-4d0b-b4d7-829e88742590", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963100109900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5589ad37-29af-46a8-84b6-0cf144abc135", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963100288800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44bff3db-4018-4aa4-825f-a2d8730f2ab4", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963108111900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd4a87ac-78e8-4549-9d7f-38e0b2903c1c", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963108433700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86ec209e-d435-4580-9e02-e14eb2735c06", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 34 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963154494100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "787fca2f-1285-4c22-bbd5-9595c3b64592", "name": "entry : default@CompileArkTS cost memory 1.1918258666992188", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963154669200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d8b339d-91d3-4356-baa9-efe58296916a", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963108083200, "endTime": 154963154775900}, "additional": {"logType": "info", "children": [], "durationId": "50897dd1-5a56-42f9-8214-21b9e4c576a1"}}, {"head": {"id": "b6e8387a-b9a6-4b66-a93c-0cc5e338aba9", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963167365100, "endTime": 154963174871300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "0c1bf7aa-fa12-46a6-9118-75de4370234e", "logId": "ad22791d-6717-43af-98e4-c44db5f6e6c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c1bf7aa-fa12-46a6-9118-75de4370234e", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963159915700}, "additional": {"logType": "detail", "children": [], "durationId": "b6e8387a-b9a6-4b66-a93c-0cc5e338aba9"}}, {"head": {"id": "60f87fb7-ef49-4d02-bdb7-9ce6c3833cdf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963161235200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06609a8f-2765-4092-90bc-31bca22ca8ba", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963161335800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fab734a-52a2-4a0f-a401-a5cfc4dbe4b3", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963167376500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be49ba36-0785-40b1-8701-c52ccceb768e", "name": "entry : default@BuildJS cost memory 0.33876800537109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963174667900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40dd8750-1c35-441d-b5a1-fbeddc67b205", "name": "runTaskFromQueue task cost before running: 847 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963174815500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad22791d-6717-43af-98e4-c44db5f6e6c4", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963167365100, "endTime": 154963174871300, "totalTime": 7433000}, "additional": {"logType": "info", "children": [], "durationId": "b6e8387a-b9a6-4b66-a93c-0cc5e338aba9"}}, {"head": {"id": "bc819fad-66b9-4583-988c-880c997e8eab", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963184706100, "endTime": 154963190665100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "df4202c3-3712-4d0e-a6c3-4e22495a4180", "logId": "9a84a508-9657-4628-8870-fd1e361120cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df4202c3-3712-4d0e-a6c3-4e22495a4180", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963176467200}, "additional": {"logType": "detail", "children": [], "durationId": "bc819fad-66b9-4583-988c-880c997e8eab"}}, {"head": {"id": "0350ba7f-604f-47e3-bf52-43467146cd93", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963179185300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "addd60d1-2857-4ac2-b728-b940f2388fad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963179426100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69fe6b7e-8b1a-4fc4-b574-def75057a290", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963184716100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34bf70e9-dec2-4e9b-866d-93dba470db0b", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963185884400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "207f015f-6667-4ce3-b6a6-b2a447cc8882", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963190291900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b1355d1-113e-490d-91f0-f3d7872bb6b4", "name": "entry : default@CacheNativeLibs cost memory 0.093963623046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963190483900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a84a508-9657-4628-8870-fd1e361120cd", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963184706100, "endTime": 154963190665100}, "additional": {"logType": "info", "children": [], "durationId": "bc819fad-66b9-4583-988c-880c997e8eab"}}, {"head": {"id": "d74f2380-15c8-4d11-85a2-ec182a0adc7f", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963197695700, "endTime": 154963200157900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "4388191c-e2fd-433e-b385-d80f7dd9ec3d", "logId": "0709803c-89ed-4b89-962a-e07fa1ff8894"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4388191c-e2fd-433e-b385-d80f7dd9ec3d", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963193322300}, "additional": {"logType": "detail", "children": [], "durationId": "d74f2380-15c8-4d11-85a2-ec182a0adc7f"}}, {"head": {"id": "17504d6b-ea17-46a1-9a67-a0e23df99c7a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963195461800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a75b9860-a4b1-48c5-8401-b9a60593c3d8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963195629800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "934887d5-bb42-447c-8d4c-630f3439d513", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963197713600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc73565c-e4a6-40a0-a4c9-e188a4dfa648", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963198252200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16ba1b8b-7bc6-47fc-b1eb-ae9ebb44fa5d", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963199896100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7c0a74a-2b52-49e1-81fe-71fdd83979f5", "name": "entry : default@GeneratePkgModuleJson cost memory 0.0742645263671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963200054100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0709803c-89ed-4b89-962a-e07fa1ff8894", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963197695700, "endTime": 154963200157900}, "additional": {"logType": "info", "children": [], "durationId": "d74f2380-15c8-4d11-85a2-ec182a0adc7f"}}, {"head": {"id": "f0982489-b351-4c45-bbc7-95daf443b62a", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963221071300, "endTime": 154963256394700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "3eb83ae3-4111-43b9-855c-3dc498567743", "logId": "201442dd-528c-4413-be69-6cc57e53b664"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3eb83ae3-4111-43b9-855c-3dc498567743", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963204637100}, "additional": {"logType": "detail", "children": [], "durationId": "f0982489-b351-4c45-bbc7-95daf443b62a"}}, {"head": {"id": "0ecc7628-a682-4ea1-b959-a392e737c4f8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963206404700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fdbd4a6-dd78-47e2-a7db-b3fecf1b7763", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963206548400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03019083-4433-4d40-9732-985a60951764", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963221087400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84cb62a7-1d0d-4a26-ac7c-586c04a3dbab", "name": "Incremental task entry:default@PackageHap pre-execution cost: 32 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963256114500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd7439b0-2357-4f18-a5bf-dc4348ebffd4", "name": "entry : default@PackageHap cost memory 0.9381942749023438", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963256296800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "201442dd-528c-4413-be69-6cc57e53b664", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963221071300, "endTime": 154963256394700}, "additional": {"logType": "info", "children": [], "durationId": "f0982489-b351-4c45-bbc7-95daf443b62a"}}, {"head": {"id": "3da517b2-1ad5-429e-b82d-48b27cc204e3", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963264100500, "endTime": 154963265980800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": [], "detailId": "63e07749-f2ba-405a-85cb-2f7375a89e6d", "logId": "5a26fe03-97a8-4395-b277-50337d8bfc6c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63e07749-f2ba-405a-85cb-2f7375a89e6d", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963261302300}, "additional": {"logType": "detail", "children": [], "durationId": "3da517b2-1ad5-429e-b82d-48b27cc204e3"}}, {"head": {"id": "1ddba83a-7745-4994-99b8-c68f0fec082b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963262194600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb76d152-fae5-4f72-8394-6a6984fda943", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963262288700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b88aae57-6220-481d-88df-5c4ef59549e0", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963264108700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45103d6a-b464-4c94-9442-f883e6734c15", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963265851700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a6de50c-f4b9-4c38-a102-1ad814819764", "name": "entry : default@SignHap cost memory 0.10276031494140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963265937600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a26fe03-97a8-4395-b277-50337d8bfc6c", "name": "UP-TO-DATE :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963264100500, "endTime": 154963265980800}, "additional": {"logType": "info", "children": [], "durationId": "3da517b2-1ad5-429e-b82d-48b27cc204e3"}}, {"head": {"id": "259912f9-6c28-4654-b5e1-ed20b67117e8", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963268590300, "endTime": 154963275279400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "143bfc66-1b7a-43fe-8e16-517d1948d6c7", "logId": "eef06404-0991-44d3-b000-2af3fe1f1d75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "143bfc66-1b7a-43fe-8e16-517d1948d6c7", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963267258600}, "additional": {"logType": "detail", "children": [], "durationId": "259912f9-6c28-4654-b5e1-ed20b67117e8"}}, {"head": {"id": "bdc878b0-4710-41b0-b96a-f718aa842f31", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963267997400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c415f78b-b68d-4a0f-b89c-35166c97b10b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963268065400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d21c312-c0a2-49e0-b301-267dee21f7e5", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963268597800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0758c9d-bff7-4f27-9d73-01a3b1e5225c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963274900300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00ab1ce2-af39-4a4d-8695-8b8d0cb9eba3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963275008000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91c4e7c7-f8d6-4407-8c5d-c73512badfe2", "name": "entry : default@CollectDebugSymbol cost memory 0.240753173828125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963275106300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e087e553-3c59-476a-b2e9-b6f4b6d7c117", "name": "runTaskFromQueue task cost before running: 948 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963275209500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eef06404-0991-44d3-b000-2af3fe1f1d75", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963268590300, "endTime": 154963275279400, "totalTime": 6594400}, "additional": {"logType": "info", "children": [], "durationId": "259912f9-6c28-4654-b5e1-ed20b67117e8"}}, {"head": {"id": "8fbb8de0-10fc-4873-89b1-0406451a6df7", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963277592700, "endTime": 154963278081500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d8a7a3ed-fac0-4707-8a42-5e62fc58a9af", "logId": "36905f2c-8a6d-48b1-9a8f-705f5db5ac03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8a7a3ed-fac0-4707-8a42-5e62fc58a9af", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963277559900}, "additional": {"logType": "detail", "children": [], "durationId": "8fbb8de0-10fc-4873-89b1-0406451a6df7"}}, {"head": {"id": "84d7718e-c184-422c-8f4f-795f2291dfe2", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963277597700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73ce0537-6059-493b-a029-a29db85dc171", "name": "entry : assembleHap cost memory 0.0115966796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963277891700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ada8d216-3f4b-4da9-8b80-a215d9ac0469", "name": "runTaskFromQueue task cost before running: 950 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963278006800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36905f2c-8a6d-48b1-9a8f-705f5db5ac03", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963277592700, "endTime": 154963278081500, "totalTime": 377000}, "additional": {"logType": "info", "children": [], "durationId": "8fbb8de0-10fc-4873-89b1-0406451a6df7"}}, {"head": {"id": "6291d3ee-23d6-49d1-805e-f2cdf9122961", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963292350800, "endTime": 154963292376400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b31d1d2d-912a-4c46-bbcc-1f3c401e13ec", "logId": "287c1258-18f8-4813-94fd-0d00bba8b91b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "287c1258-18f8-4813-94fd-0d00bba8b91b", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963292350800, "endTime": 154963292376400}, "additional": {"logType": "info", "children": [], "durationId": "6291d3ee-23d6-49d1-805e-f2cdf9122961"}}, {"head": {"id": "06d79b55-c0c7-4bc8-8426-f7b8c0ee855b", "name": "BUILD SUCCESSFUL in 965 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963292416000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "8d871ad7-7126-4b3f-9f8a-e02e91dd8445", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154962328147500, "endTime": 154963292683200}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 59, "second": 53}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "76bf30ad-e1c2-4674-a665-a40a464dc698", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963292708300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92773080-3d33-4d82-8f32-6d3d14db7654", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963292766400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa4ad37f-3084-4551-b006-ce647635673f", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963293342000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f543fc7-f34e-41d4-a5b7-0d979a0078cf", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963293453400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e832ee70-f619-4976-83aa-94e0071aec46", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963293598700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31087a33-8bae-44ec-ac27-0fe67d5908e2", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963293685900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42bfd226-fc97-4e86-823f-6f380f1f8448", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963293762700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f86c6c59-d957-4156-9c66-f5e1341b89bf", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963294593700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d347db13-fbab-4281-8bb4-b70c1f902aff", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963294919400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09bfbdd0-f307-44b7-96ad-68958982e663", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963294981600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96dd379b-500f-4ee3-ab78-0f6f16b2a962", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963295012000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caa89cb6-4272-4d88-83cb-5d159ba80cc6", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963295036900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b348fc13-6d03-4f92-83ea-aa1cf30b5f94", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963295060900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab39939e-7ee1-4e58-b643-f7b5131bb506", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963295937600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59853184-feca-45bd-bc73-c617f04a76ff", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963296210700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be4addc7-720c-472d-a4d2-9f83ce2bb494", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963296398900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d04ca542-477f-47aa-9b01-39e51ceaeb2a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963296445800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a39a5aa-2977-451e-a2e3-a9d1d747cc93", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963296471300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4fd9157-3b9a-4222-8bd0-3bc4db83252c", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963296492700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eba4e361-8ea9-419e-b056-f2439e11b774", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963296514400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51b2ec84-7d85-4318-82d4-c92f6ce9d758", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963296537300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e5715b4-e637-477f-a77f-e8365d5797f8", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963296560000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a6b8844-7338-4f0f-a821-15901e86b670", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963298170600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "173c7ebc-73e7-4fed-8d6f-b1d377cb52dc", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963298734000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "087d69eb-4000-4fbc-aee1-8076cb112903", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963299070500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "716844d4-f7c5-4689-96e2-215d1ba1d678", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963299258000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0316c406-3865-4148-b7c5-d3d904a61764", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963299418300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb08b066-18d5-42aa-b36f-eaf73a690c1d", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963299990500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6573aab-9583-4832-92ed-91772fcebe25", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963300713200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f87e6ca4-503b-45d4-a2a3-a6c41fb10605", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963300910900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3d3fc48-0a9d-4937-9a77-9311d977c30d", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963300959500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca8d15d0-fe3b-4f75-a2ab-173367d79aa7", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963300988000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a1cfb43-9898-44ab-9c78-54ad5b560989", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963301016600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89e79c88-5e42-4c83-a7cf-b5fb89ad2794", "name": "There is no need to refresh cache, since the incremental task entry:default@SignHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963301039900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b5a8687-a89a-4491-b0de-58fffaf51321", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963304204900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1b75228-a5dc-4847-946e-caf6dd78b84c", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963304648600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d3802ae-0c04-4bbc-a3a2-f34e2d880f62", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963305424800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f411f118-3077-4257-9349-746c588087e6", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963305743400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}