{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "b776feea-afca-43a3-b915-56a172fbc2ea", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456093753300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f773aa10-6970-4afc-b3ca-54914b0ab8e2", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456105914800, "endTime": 152459465931400}, "additional": {"children": ["37e437e2-2d6b-45c3-beb9-2bd63ecdf6c4", "6f0fd173-f113-4e7f-a7db-82f06b08a66c", "bb90b37c-26f1-4406-83ca-3e9d230d1e2d", "5e9b0166-ef5d-4f7b-80ac-814b2d3213a2", "00c764ed-a5a8-4e17-845c-1c5bf0a74576", "2c8b282d-8883-48ab-984c-9dbef2ad9800", "75dc8dd8-b96b-4506-8e1f-7bc97f9e9ec4"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "7d526ff3-9dc7-4208-9c3b-419641679e12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37e437e2-2d6b-45c3-beb9-2bd63ecdf6c4", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456105919100, "endTime": 152456124553200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f773aa10-6970-4afc-b3ca-54914b0ab8e2", "logId": "8f8625f5-e1a2-49d6-9601-14801ea4a877"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f0fd173-f113-4e7f-a7db-82f06b08a66c", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456124572500, "endTime": 152459462885600}, "additional": {"children": ["493bed38-a104-4f48-938a-296fa61ae058", "bf34bbf5-1f4d-4993-947a-9eeed57a5253", "afe327de-7ac9-40d0-98ca-a9874169165c", "9b0ba851-c044-4cb2-8664-f51f7508c303", "25e7af2c-b71c-4c64-a140-d1623d9695b2", "913d4027-44cd-492b-a168-eb9f29b5acd6", "3ea38cd3-b787-4964-b9a4-d9589a4964d7", "71270dd5-3e0e-4b3b-ac8c-22e917c75076", "ba3a4261-ae9c-4028-bf98-d4130e6ed4f2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f773aa10-6970-4afc-b3ca-54914b0ab8e2", "logId": "fae57bbb-2d8b-4992-a939-4005674580bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb90b37c-26f1-4406-83ca-3e9d230d1e2d", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459462913400, "endTime": 152459465896600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f773aa10-6970-4afc-b3ca-54914b0ab8e2", "logId": "684a5dfa-5b67-4055-a77d-f0197c0ffad6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e9b0166-ef5d-4f7b-80ac-814b2d3213a2", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459465903000, "endTime": 152459465924600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f773aa10-6970-4afc-b3ca-54914b0ab8e2", "logId": "8d806a63-b950-433b-9374-ae9c17cef490"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00c764ed-a5a8-4e17-845c-1c5bf0a74576", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456111029700, "endTime": 152456111250700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f773aa10-6970-4afc-b3ca-54914b0ab8e2", "logId": "07a1a2ce-e1cd-4824-a19b-4035a5ae7183"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07a1a2ce-e1cd-4824-a19b-4035a5ae7183", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456111029700, "endTime": 152456111250700}, "additional": {"logType": "info", "children": [], "durationId": "00c764ed-a5a8-4e17-845c-1c5bf0a74576", "parent": "7d526ff3-9dc7-4208-9c3b-419641679e12"}}, {"head": {"id": "2c8b282d-8883-48ab-984c-9dbef2ad9800", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456118303900, "endTime": 152456118361100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f773aa10-6970-4afc-b3ca-54914b0ab8e2", "logId": "0c5b7267-abc0-4e43-80b2-0db329352ae5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c5b7267-abc0-4e43-80b2-0db329352ae5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456118303900, "endTime": 152456118361100}, "additional": {"logType": "info", "children": [], "durationId": "2c8b282d-8883-48ab-984c-9dbef2ad9800", "parent": "7d526ff3-9dc7-4208-9c3b-419641679e12"}}, {"head": {"id": "21bfc1cd-3468-42d7-bc74-c378c1610570", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456118863100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e48d87d-6ae7-421b-81f3-3fcf43636f15", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456124424000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f8625f5-e1a2-49d6-9601-14801ea4a877", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456105919100, "endTime": 152456124553200}, "additional": {"logType": "info", "children": [], "durationId": "37e437e2-2d6b-45c3-beb9-2bd63ecdf6c4", "parent": "7d526ff3-9dc7-4208-9c3b-419641679e12"}}, {"head": {"id": "493bed38-a104-4f48-938a-296fa61ae058", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456129398800, "endTime": 152456129429900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f0fd173-f113-4e7f-a7db-82f06b08a66c", "logId": "835f1853-5143-4f4b-9e01-467a15ee533c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf34bbf5-1f4d-4993-947a-9eeed57a5253", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456129465100, "endTime": 152456135493800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f0fd173-f113-4e7f-a7db-82f06b08a66c", "logId": "7e3e6856-dec8-4e51-999c-13cc5249a57f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afe327de-7ac9-40d0-98ca-a9874169165c", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456135618100, "endTime": 152459323210200}, "additional": {"children": ["1f8c134a-6256-4976-bb9d-86433156d4ad", "284d6c9d-99aa-4548-a876-fdeca35a8761", "e7ca2d09-570f-4c02-9e5c-0e8f5bba8041"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f0fd173-f113-4e7f-a7db-82f06b08a66c", "logId": "64f3f5de-51ef-4387-ba9c-22e171547480"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b0ba851-c044-4cb2-8664-f51f7508c303", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459323362500, "endTime": 152459360391300}, "additional": {"children": ["34f744e0-e824-4de4-a9b9-52967b7384b7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f0fd173-f113-4e7f-a7db-82f06b08a66c", "logId": "a8f33beb-4498-4057-81a1-3413fa9ae164"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25e7af2c-b71c-4c64-a140-d1623d9695b2", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459360459900, "endTime": 152459419715600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f0fd173-f113-4e7f-a7db-82f06b08a66c", "logId": "0ceeff8c-0f1e-49c8-89be-7d1db7f81a04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "913d4027-44cd-492b-a168-eb9f29b5acd6", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459421368300, "endTime": 152459435305400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f0fd173-f113-4e7f-a7db-82f06b08a66c", "logId": "10cc717e-00ec-4723-9b3c-becbc0a78ccb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ea38cd3-b787-4964-b9a4-d9589a4964d7", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459435328900, "endTime": 152459462532900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f0fd173-f113-4e7f-a7db-82f06b08a66c", "logId": "6721971b-c5ec-4433-9ccc-c4e99e5509ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71270dd5-3e0e-4b3b-ac8c-22e917c75076", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459462557100, "endTime": 152459462869400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f0fd173-f113-4e7f-a7db-82f06b08a66c", "logId": "313fe717-deb6-4d0e-9c70-7e2decf117d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "835f1853-5143-4f4b-9e01-467a15ee533c", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456129398800, "endTime": 152456129429900}, "additional": {"logType": "info", "children": [], "durationId": "493bed38-a104-4f48-938a-296fa61ae058", "parent": "fae57bbb-2d8b-4992-a939-4005674580bd"}}, {"head": {"id": "7e3e6856-dec8-4e51-999c-13cc5249a57f", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456129465100, "endTime": 152456135493800}, "additional": {"logType": "info", "children": [], "durationId": "bf34bbf5-1f4d-4993-947a-9eeed57a5253", "parent": "fae57bbb-2d8b-4992-a939-4005674580bd"}}, {"head": {"id": "1f8c134a-6256-4976-bb9d-86433156d4ad", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456136370300, "endTime": 152456136424300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "afe327de-7ac9-40d0-98ca-a9874169165c", "logId": "51626e60-1c0b-46ff-9c0e-98e854f8ddd0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51626e60-1c0b-46ff-9c0e-98e854f8ddd0", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456136370300, "endTime": 152456136424300}, "additional": {"logType": "info", "children": [], "durationId": "1f8c134a-6256-4976-bb9d-86433156d4ad", "parent": "64f3f5de-51ef-4387-ba9c-22e171547480"}}, {"head": {"id": "284d6c9d-99aa-4548-a876-fdeca35a8761", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456138527600, "endTime": 152459322322600}, "additional": {"children": ["8639a975-b7f3-4dcf-a0b7-cad90632f842", "bf5e4689-808b-4287-9a51-09324f0c3b26"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "afe327de-7ac9-40d0-98ca-a9874169165c", "logId": "63e0be02-2dcf-4d44-8279-647be37c5a5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8639a975-b7f3-4dcf-a0b7-cad90632f842", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456138529300, "endTime": 152459036726200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "284d6c9d-99aa-4548-a876-fdeca35a8761", "logId": "ec263870-97cf-4c85-820d-53cb3e72eab3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf5e4689-808b-4287-9a51-09324f0c3b26", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459036749300, "endTime": 152459322308100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "284d6c9d-99aa-4548-a876-fdeca35a8761", "logId": "fa4ba54a-a5e2-4d90-bec0-1b27a546789d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "742050d2-cea3-45e5-90d6-b117d9dc9ebd", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456138536200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e203f0b0-552b-488e-884e-722d2a54d9cb", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459036571200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec263870-97cf-4c85-820d-53cb3e72eab3", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456138529300, "endTime": 152459036726200}, "additional": {"logType": "info", "children": [], "durationId": "8639a975-b7f3-4dcf-a0b7-cad90632f842", "parent": "63e0be02-2dcf-4d44-8279-647be37c5a5f"}}, {"head": {"id": "c9ded259-8a47-4da5-a73e-1599c4bfdd13", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459036879700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26f78b70-f4bf-432b-82ed-ed6734dada32", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459232408800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51fccc75-f683-4a2d-b54a-f8d5b2f25434", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459232699300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3276db62-5798-4a9b-ba4f-600cbe6aaab2", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459233244900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80e588cf-ec4c-448e-b1dc-49465c850eea", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459233372200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0079f62d-adfb-49ad-8ba9-4c2c8cb6d2f7", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459237747100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48e87bd6-bfa8-44aa-a6cb-cb053852b933", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459260439900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8426597-37bf-402c-a7da-229cbea7decd", "name": "Sdk init in 41 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459290221200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9d75868-6353-43fe-9ba6-dcdfa4f11e43", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459290597700}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 18, "second": 9}, "markType": "other"}}, {"head": {"id": "1cad9895-11bb-4d9c-8bc7-68e4bfb7fcf0", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459290689800}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 18, "second": 9}, "markType": "other"}}, {"head": {"id": "0eb5d24d-e086-4876-b24a-2a0393ff3cab", "name": "Project task initialization takes 24 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459321674600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f512ba06-f83f-4c55-88a0-9269f52c384b", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459322060200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdce8252-fa2b-4402-89f9-a6f640d2d23c", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459322156900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1cfb983-9fa9-40ec-91be-5de2fe267c88", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459322224400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa4ba54a-a5e2-4d90-bec0-1b27a546789d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459036749300, "endTime": 152459322308100}, "additional": {"logType": "info", "children": [], "durationId": "bf5e4689-808b-4287-9a51-09324f0c3b26", "parent": "63e0be02-2dcf-4d44-8279-647be37c5a5f"}}, {"head": {"id": "63e0be02-2dcf-4d44-8279-647be37c5a5f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456138527600, "endTime": 152459322322600}, "additional": {"logType": "info", "children": ["ec263870-97cf-4c85-820d-53cb3e72eab3", "fa4ba54a-a5e2-4d90-bec0-1b27a546789d"], "durationId": "284d6c9d-99aa-4548-a876-fdeca35a8761", "parent": "64f3f5de-51ef-4387-ba9c-22e171547480"}}, {"head": {"id": "e7ca2d09-570f-4c02-9e5c-0e8f5bba8041", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459323124700, "endTime": 152459323181800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "afe327de-7ac9-40d0-98ca-a9874169165c", "logId": "795fd14f-2f0a-49a4-9e00-9cb941b16870"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "795fd14f-2f0a-49a4-9e00-9cb941b16870", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459323124700, "endTime": 152459323181800}, "additional": {"logType": "info", "children": [], "durationId": "e7ca2d09-570f-4c02-9e5c-0e8f5bba8041", "parent": "64f3f5de-51ef-4387-ba9c-22e171547480"}}, {"head": {"id": "64f3f5de-51ef-4387-ba9c-22e171547480", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456135618100, "endTime": 152459323210200}, "additional": {"logType": "info", "children": ["51626e60-1c0b-46ff-9c0e-98e854f8ddd0", "63e0be02-2dcf-4d44-8279-647be37c5a5f", "795fd14f-2f0a-49a4-9e00-9cb941b16870"], "durationId": "afe327de-7ac9-40d0-98ca-a9874169165c", "parent": "fae57bbb-2d8b-4992-a939-4005674580bd"}}, {"head": {"id": "34f744e0-e824-4de4-a9b9-52967b7384b7", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459323957900, "endTime": 152459360343700}, "additional": {"children": ["894c00cb-5d8a-44a3-abe3-d1ad7c12c315", "f50bfb1b-2e90-4d3c-89e7-dfa710b7aef8", "0bca5cd7-4335-4141-a2d3-593a81faf0b1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b0ba851-c044-4cb2-8664-f51f7508c303", "logId": "5bd9dd09-7e8a-42c5-80d9-34faa93bb058"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "894c00cb-5d8a-44a3-abe3-d1ad7c12c315", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459326625700, "endTime": 152459326641100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "34f744e0-e824-4de4-a9b9-52967b7384b7", "logId": "6a882d3f-17ea-4a4b-a275-f88aa0a0b5d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a882d3f-17ea-4a4b-a275-f88aa0a0b5d4", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459326625700, "endTime": 152459326641100}, "additional": {"logType": "info", "children": [], "durationId": "894c00cb-5d8a-44a3-abe3-d1ad7c12c315", "parent": "5bd9dd09-7e8a-42c5-80d9-34faa93bb058"}}, {"head": {"id": "f50bfb1b-2e90-4d3c-89e7-dfa710b7aef8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459328406700, "endTime": 152459358723100}, "additional": {"children": ["6131fd11-0af7-4ef5-899d-127349af8387", "a549b60e-ad6d-4b7e-9a98-10a7876d3fbd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "34f744e0-e824-4de4-a9b9-52967b7384b7", "logId": "6f2bc839-5b5f-438d-9bb7-bd89cb34c508"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6131fd11-0af7-4ef5-899d-127349af8387", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459328407700, "endTime": 152459335522600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f50bfb1b-2e90-4d3c-89e7-dfa710b7aef8", "logId": "918d1499-2e04-46b5-a1ac-6304662f3922"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a549b60e-ad6d-4b7e-9a98-10a7876d3fbd", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459335544800, "endTime": 152459358713500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f50bfb1b-2e90-4d3c-89e7-dfa710b7aef8", "logId": "f6c5a75c-c6a9-4830-b65c-086a4fe090b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c9cd016-9800-4b1e-834b-ae43af830904", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459328411800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a50e31b6-39af-4897-a685-03ed7f472c68", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459335376200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "918d1499-2e04-46b5-a1ac-6304662f3922", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459328407700, "endTime": 152459335522600}, "additional": {"logType": "info", "children": [], "durationId": "6131fd11-0af7-4ef5-899d-127349af8387", "parent": "6f2bc839-5b5f-438d-9bb7-bd89cb34c508"}}, {"head": {"id": "14f4d763-f51e-4f5c-8277-8f09178671f9", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459335645400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fcc91b9-08b1-42d6-9140-cbee3fbb9388", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459349058400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "171bdf8e-65ef-43b7-9354-5b643f9ef1d6", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459349435000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ad17a81-c224-40b7-b7fb-fe7d93ab8343", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459349981600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff296427-0167-4271-832d-efc6a3e37b39", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459350263600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32058e0e-9fb4-4095-866d-aa87c321263e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459350353300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daca12f2-4c48-43a6-9ec7-f860ef905988", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459350433700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "917dfb62-666c-4841-8910-fff7dd7e2762", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459350508700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6645370d-90ca-4ac3-8685-f7026191abc6", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459350569000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "178acc49-3711-4e8a-8891-86e758bec3f4", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459350870500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa2116a5-0f2f-4b63-94ce-bd310dee9e57", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459351004900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d930c18-8551-4b38-a5a4-696a884085ef", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459351063600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "335d39bf-63ee-44b1-b66f-382c83de287d", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459351097100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2ec3623-5a5b-427f-acfc-168ba70fa11d", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459351263200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bea72fa2-1e2d-4469-889d-088fe07cd8e4", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459351325200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c98f3a88-b6b1-407d-9b94-6b8f4079a217", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459351512900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdfb3d86-a5a8-409e-b185-209f06f133e5", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459351622500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a45513aa-e22d-488d-af34-ff971d1ecef3", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459351669700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac5413fe-1d7d-421f-8dbe-934aa62c0177", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459351784900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd3fac94-a5d8-4e21-9848-20665f9526cc", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459352045000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "607239bd-5a1c-4d14-95ac-5ccfdd26284a", "name": "Module entry task initialization takes 4 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459358180400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeefeeac-8e24-4143-9c5f-ade8c4bccd76", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459358372100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95d1805a-edcb-4cde-87a9-4884605e78fd", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459358589600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c46bb73-519b-466a-8318-ef80b72feb76", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459358673100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6c5a75c-c6a9-4830-b65c-086a4fe090b3", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459335544800, "endTime": 152459358713500}, "additional": {"logType": "info", "children": [], "durationId": "a549b60e-ad6d-4b7e-9a98-10a7876d3fbd", "parent": "6f2bc839-5b5f-438d-9bb7-bd89cb34c508"}}, {"head": {"id": "6f2bc839-5b5f-438d-9bb7-bd89cb34c508", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459328406700, "endTime": 152459358723100}, "additional": {"logType": "info", "children": ["918d1499-2e04-46b5-a1ac-6304662f3922", "f6c5a75c-c6a9-4830-b65c-086a4fe090b3"], "durationId": "f50bfb1b-2e90-4d3c-89e7-dfa710b7aef8", "parent": "5bd9dd09-7e8a-42c5-80d9-34faa93bb058"}}, {"head": {"id": "0bca5cd7-4335-4141-a2d3-593a81faf0b1", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459360284500, "endTime": 152459360325400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "34f744e0-e824-4de4-a9b9-52967b7384b7", "logId": "36f84a0d-f6b1-4a78-a9a1-4cd0a5783ede"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36f84a0d-f6b1-4a78-a9a1-4cd0a5783ede", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459360284500, "endTime": 152459360325400}, "additional": {"logType": "info", "children": [], "durationId": "0bca5cd7-4335-4141-a2d3-593a81faf0b1", "parent": "5bd9dd09-7e8a-42c5-80d9-34faa93bb058"}}, {"head": {"id": "5bd9dd09-7e8a-42c5-80d9-34faa93bb058", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459323957900, "endTime": 152459360343700}, "additional": {"logType": "info", "children": ["6a882d3f-17ea-4a4b-a275-f88aa0a0b5d4", "6f2bc839-5b5f-438d-9bb7-bd89cb34c508", "36f84a0d-f6b1-4a78-a9a1-4cd0a5783ede"], "durationId": "34f744e0-e824-4de4-a9b9-52967b7384b7", "parent": "a8f33beb-4498-4057-81a1-3413fa9ae164"}}, {"head": {"id": "a8f33beb-4498-4057-81a1-3413fa9ae164", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459323362500, "endTime": 152459360391300}, "additional": {"logType": "info", "children": ["5bd9dd09-7e8a-42c5-80d9-34faa93bb058"], "durationId": "9b0ba851-c044-4cb2-8664-f51f7508c303", "parent": "fae57bbb-2d8b-4992-a939-4005674580bd"}}, {"head": {"id": "eba4f32e-08a2-45e3-aaef-2c69c4d72ef6", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459377470200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82178b6d-6332-447d-9d47-c30539e68628", "name": "hvigorfile, resolve hvigorfile dependencies in 60 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459419552100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ceeff8c-0f1e-49c8-89be-7d1db7f81a04", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459360459900, "endTime": 152459419715600}, "additional": {"logType": "info", "children": [], "durationId": "25e7af2c-b71c-4c64-a140-d1623d9695b2", "parent": "fae57bbb-2d8b-4992-a939-4005674580bd"}}, {"head": {"id": "ba3a4261-ae9c-4028-bf98-d4130e6ed4f2", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459420705000, "endTime": 152459421314300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f0fd173-f113-4e7f-a7db-82f06b08a66c", "logId": "1a8f1f6b-4617-44d3-a2e0-3034bf8dd001"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18d0abf0-c747-4da4-bd62-65314c2b275c", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459420855300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a8f1f6b-4617-44d3-a2e0-3034bf8dd001", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459420705000, "endTime": 152459421314300}, "additional": {"logType": "info", "children": [], "durationId": "ba3a4261-ae9c-4028-bf98-d4130e6ed4f2", "parent": "fae57bbb-2d8b-4992-a939-4005674580bd"}}, {"head": {"id": "d9577c2a-ed57-4951-a6e0-5fe417b7078c", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459423292600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1db67772-ae19-456e-9863-7b38c91ea8c6", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459434151400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10cc717e-00ec-4723-9b3c-becbc0a78ccb", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459421368300, "endTime": 152459435305400}, "additional": {"logType": "info", "children": [], "durationId": "913d4027-44cd-492b-a168-eb9f29b5acd6", "parent": "fae57bbb-2d8b-4992-a939-4005674580bd"}}, {"head": {"id": "221ad0b4-9bc6-4442-b192-f6c7c9403d97", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459435438400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ece7e2e0-f9eb-411d-acbb-5fc13c27b152", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459449980100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1de2318f-228d-4e5b-b080-1e41d57d4ec0", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459450137400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a9e7d97-f9e5-465f-ab74-cafd50cef435", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459450878900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc9a262d-49fa-4413-8083-3600ac1824a7", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459456042900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6e458a1-6fe9-4c1d-bf23-916cc90ed561", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459456211100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6721971b-c5ec-4433-9ccc-c4e99e5509ea", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459435328900, "endTime": 152459462532900}, "additional": {"logType": "info", "children": [], "durationId": "3ea38cd3-b787-4964-b9a4-d9589a4964d7", "parent": "fae57bbb-2d8b-4992-a939-4005674580bd"}}, {"head": {"id": "8029f19c-6760-43dd-a8fe-fdcb6db07f93", "name": "Configuration phase cost:3 s 334 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459462744600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "313fe717-deb6-4d0e-9c70-7e2decf117d3", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459462557100, "endTime": 152459462869400}, "additional": {"logType": "info", "children": [], "durationId": "71270dd5-3e0e-4b3b-ac8c-22e917c75076", "parent": "fae57bbb-2d8b-4992-a939-4005674580bd"}}, {"head": {"id": "fae57bbb-2d8b-4992-a939-4005674580bd", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456124572500, "endTime": 152459462885600}, "additional": {"logType": "info", "children": ["835f1853-5143-4f4b-9e01-467a15ee533c", "7e3e6856-dec8-4e51-999c-13cc5249a57f", "64f3f5de-51ef-4387-ba9c-22e171547480", "a8f33beb-4498-4057-81a1-3413fa9ae164", "0ceeff8c-0f1e-49c8-89be-7d1db7f81a04", "10cc717e-00ec-4723-9b3c-becbc0a78ccb", "6721971b-c5ec-4433-9ccc-c4e99e5509ea", "313fe717-deb6-4d0e-9c70-7e2decf117d3", "1a8f1f6b-4617-44d3-a2e0-3034bf8dd001"], "durationId": "6f0fd173-f113-4e7f-a7db-82f06b08a66c", "parent": "7d526ff3-9dc7-4208-9c3b-419641679e12"}}, {"head": {"id": "75dc8dd8-b96b-4506-8e1f-7bc97f9e9ec4", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459465817900, "endTime": 152459465879300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f773aa10-6970-4afc-b3ca-54914b0ab8e2", "logId": "0d2c2972-b931-4377-9885-e744d99029a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d2c2972-b931-4377-9885-e744d99029a8", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459465817900, "endTime": 152459465879300}, "additional": {"logType": "info", "children": [], "durationId": "75dc8dd8-b96b-4506-8e1f-7bc97f9e9ec4", "parent": "7d526ff3-9dc7-4208-9c3b-419641679e12"}}, {"head": {"id": "684a5dfa-5b67-4055-a77d-f0197c0ffad6", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459462913400, "endTime": 152459465896600}, "additional": {"logType": "info", "children": [], "durationId": "bb90b37c-26f1-4406-83ca-3e9d230d1e2d", "parent": "7d526ff3-9dc7-4208-9c3b-419641679e12"}}, {"head": {"id": "8d806a63-b950-433b-9374-ae9c17cef490", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459465903000, "endTime": 152459465924600}, "additional": {"logType": "info", "children": [], "durationId": "5e9b0166-ef5d-4f7b-80ac-814b2d3213a2", "parent": "7d526ff3-9dc7-4208-9c3b-419641679e12"}}, {"head": {"id": "7d526ff3-9dc7-4208-9c3b-419641679e12", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456105914800, "endTime": 152459465931400}, "additional": {"logType": "info", "children": ["8f8625f5-e1a2-49d6-9601-14801ea4a877", "fae57bbb-2d8b-4992-a939-4005674580bd", "684a5dfa-5b67-4055-a77d-f0197c0ffad6", "8d806a63-b950-433b-9374-ae9c17cef490", "07a1a2ce-e1cd-4824-a19b-4035a5ae7183", "0c5b7267-abc0-4e43-80b2-0db329352ae5", "0d2c2972-b931-4377-9885-e744d99029a8"], "durationId": "f773aa10-6970-4afc-b3ca-54914b0ab8e2"}}, {"head": {"id": "37a31570-7f37-46ae-9fe0-79abb599b80d", "name": "Configuration task cost before running: 3 s 365 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459466618100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b8f3e6e-d7aa-46d7-857b-a924c1247307", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459485701600, "endTime": 152459513452700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "3c3a10bf-4d0c-451c-83c3-aee6f2a46bf5", "logId": "784f126a-3768-49b7-9d66-ed66a93ca000"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c3a10bf-4d0c-451c-83c3-aee6f2a46bf5", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459468931100}, "additional": {"logType": "detail", "children": [], "durationId": "3b8f3e6e-d7aa-46d7-857b-a924c1247307"}}, {"head": {"id": "72a8fa4f-2ba2-47ec-9dc2-d116ca13bc9d", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459470515300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad11d057-186b-40f0-a972-1c5360e571ec", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459470716500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47b0fd1f-609d-4224-a5f1-fee5d2d0eecc", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459472300600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adc7e185-46ee-4961-8271-4dac151e66bc", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459474653200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17a258f8-6d75-4910-a673-e78ec6b0ff04", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459477439300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "663772a0-22df-46fe-8ccd-e323bc31d377", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459477569200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4a58881-6f92-49cc-9afd-0215f0cf0300", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459485750400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0470e0b0-852f-4261-9b8f-500982647eb5", "name": "Incremental task entry:default@PreBuild pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459512971400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "060d80ce-3e9b-4f38-a466-028ddc9e02c9", "name": "entry : default@PreBuild cost memory 0.58367919921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459513227800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "784f126a-3768-49b7-9d66-ed66a93ca000", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459485701600, "endTime": 152459513452700}, "additional": {"logType": "info", "children": [], "durationId": "3b8f3e6e-d7aa-46d7-857b-a924c1247307"}}, {"head": {"id": "8786082b-ffad-42cd-a207-1fe3ef49a112", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459521848400, "endTime": 152459524752200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5689bf3c-0af2-47e0-9994-bd6351a5cddd", "logId": "ae354081-f48f-43b2-b4df-d12c1e49a28a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5689bf3c-0af2-47e0-9994-bd6351a5cddd", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459519581300}, "additional": {"logType": "detail", "children": [], "durationId": "8786082b-ffad-42cd-a207-1fe3ef49a112"}}, {"head": {"id": "40489135-2512-4221-a702-662445486154", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459520930200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c54a8961-b233-488a-81de-f59ffadb9e78", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459521053600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6a94d62-431c-42b6-b502-4c2f285e2ce8", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459521864600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc5e1077-66d2-433d-8d30-29b5d69f6a78", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459523126900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4adc733-fac1-4170-b98d-1e2dc51e36d5", "name": "entry : default@CreateModuleInfo cost memory 0.2412872314453125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459524433300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21fd8098-3b6b-4eb0-9ae3-f92e30137752", "name": "runTaskFromQueue task cost before running: 3 s 423 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459524583400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae354081-f48f-43b2-b4df-d12c1e49a28a", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459521848400, "endTime": 152459524752200, "totalTime": 2711300}, "additional": {"logType": "info", "children": [], "durationId": "8786082b-ffad-42cd-a207-1fe3ef49a112"}}, {"head": {"id": "3e3030d3-3a3a-4769-afdd-ce9ca9e29b33", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459534601100, "endTime": 152459537853100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f0dcac4e-3938-4abe-b3b3-827e7324e453", "logId": "e7ff4bb3-4a61-4407-b786-80852ba156b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0dcac4e-3938-4abe-b3b3-827e7324e453", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459527538200}, "additional": {"logType": "detail", "children": [], "durationId": "3e3030d3-3a3a-4769-afdd-ce9ca9e29b33"}}, {"head": {"id": "588fc183-a1dd-4e58-9fe9-595667521073", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459528896300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6996e5d-9dc3-4857-9cd9-2811e1c40785", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459529012100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6984aac0-3bfa-44d9-a4be-02dbc52d8ecc", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459534622000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f391ad1-637b-4e20-b494-7e1f55e44486", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459536209400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dfd4336-789f-482f-85c1-7e016ded2685", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459537621100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aeeef10-395b-4ccd-a61c-391de85ad95e", "name": "entry : default@GenerateMetadata cost memory 0.10788726806640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459537781300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7ff4bb3-4a61-4407-b786-80852ba156b5", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459534601100, "endTime": 152459537853100}, "additional": {"logType": "info", "children": [], "durationId": "3e3030d3-3a3a-4769-afdd-ce9ca9e29b33"}}, {"head": {"id": "e388eb31-5079-4cfc-b1f7-57c7672d8ca6", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459541376300, "endTime": 152459542003400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "86e5c9ab-fde5-411d-861b-c22e77a7e355", "logId": "127301d8-986e-4204-b1b0-bda16010e45f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86e5c9ab-fde5-411d-861b-c22e77a7e355", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459539759800}, "additional": {"logType": "detail", "children": [], "durationId": "e388eb31-5079-4cfc-b1f7-57c7672d8ca6"}}, {"head": {"id": "2ea0d25d-1f22-4c7a-a660-6f4502f4d1a0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459540911600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "202d740f-8afb-44d7-93ac-9ea28697e3a9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459541032700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d09bd1c-70df-4797-bb17-4027bc6a3b4f", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459541389200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d47a5da5-c70e-48c9-9f53-d2dbf1fb8f5f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459541695100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1035b7f-f87b-4624-a153-e1592acf5418", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459541764600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ed20fa1-5eaf-46d5-bfcf-21e82631759c", "name": "entry : default@ConfigureCmake cost memory 0.037750244140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459541872500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5f185bc-ff56-48f7-8884-9d354e92246c", "name": "runTaskFromQueue task cost before running: 3 s 441 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459541955400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "127301d8-986e-4204-b1b0-bda16010e45f", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459541376300, "endTime": 152459542003400, "totalTime": 552500}, "additional": {"logType": "info", "children": [], "durationId": "e388eb31-5079-4cfc-b1f7-57c7672d8ca6"}}, {"head": {"id": "56cd2efc-b008-4856-a151-e630bfb8d6f5", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459546187500, "endTime": 152459550577500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d6f57e1c-fed4-48f4-a7cd-d552ddc30dc2", "logId": "4988ede2-5c98-41f9-8d6a-203dfbb8800c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6f57e1c-fed4-48f4-a7cd-d552ddc30dc2", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459543891900}, "additional": {"logType": "detail", "children": [], "durationId": "56cd2efc-b008-4856-a151-e630bfb8d6f5"}}, {"head": {"id": "042cffa5-388d-40d3-b7a4-23e2d2a51b54", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459545182500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53233c3d-0143-451d-b16e-bcf3dfddbbea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459545286200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b06350fe-e783-4374-b432-59d2a880d2db", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459546201900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b1032c7-8676-41fa-a345-e613ecb14b02", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459550271600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a165182-32a1-4aca-a78e-ab4bece3b53a", "name": "entry : default@MergeProfile cost memory 0.1244049072265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459550481900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4988ede2-5c98-41f9-8d6a-203dfbb8800c", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459546187500, "endTime": 152459550577500}, "additional": {"logType": "info", "children": [], "durationId": "56cd2efc-b008-4856-a151-e630bfb8d6f5"}}, {"head": {"id": "0847bfa3-7ca2-41ef-9796-ac2233c56cd9", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459555599300, "endTime": 152459559580100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fd8e1207-1f99-4e24-94f0-e3cdcad8be60", "logId": "9faefabb-8b5e-48df-9b43-47184308ee9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd8e1207-1f99-4e24-94f0-e3cdcad8be60", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459552691100}, "additional": {"logType": "detail", "children": [], "durationId": "0847bfa3-7ca2-41ef-9796-ac2233c56cd9"}}, {"head": {"id": "243420d2-7860-4025-9864-832342646ba2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459553934000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6c36d1a-4925-4472-8890-00e8f72bbbcc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459554053300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "473099aa-746b-4d05-bf4e-f99478ec2319", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459555614000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaff195b-ea3c-43ba-a69d-06d973429d80", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459557169700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7386162e-6afa-43dd-b48c-5a13d79a84e2", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459559370000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62074e75-725e-4cf5-80ee-974795a30434", "name": "entry : default@CreateBuildProfile cost memory 0.11444854736328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459559509400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9faefabb-8b5e-48df-9b43-47184308ee9b", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459555599300, "endTime": 152459559580100}, "additional": {"logType": "info", "children": [], "durationId": "0847bfa3-7ca2-41ef-9796-ac2233c56cd9"}}, {"head": {"id": "965b19e7-c517-467d-92df-b0b02fc3bafa", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459565099400, "endTime": 152459566411000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "59414a12-2f83-47a9-9bb9-d4ac07754573", "logId": "5e5cd899-0c88-443f-97da-60d6e1a8f8fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59414a12-2f83-47a9-9bb9-d4ac07754573", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459561516600}, "additional": {"logType": "detail", "children": [], "durationId": "965b19e7-c517-467d-92df-b0b02fc3bafa"}}, {"head": {"id": "3e0f11a4-661d-451e-9653-c4223572dd8c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459563069200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09f55505-bf91-4234-a916-eac10822cc11", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459563316100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05eee893-f6af-4099-98c7-3978ece4d2a2", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459565136400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34b8338c-907a-4863-9c3a-f51b8261ec54", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459565389700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8583fece-9df7-4b14-940d-d82a0be72921", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459565529700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88e7b677-9ff7-4432-a1f2-2bd496610479", "name": "entry : default@PreCheckSyscap cost memory 0.04160308837890625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459566115800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e4bf02b-b556-47ff-9e6f-89fe2518ef55", "name": "runTaskFromQueue task cost before running: 3 s 465 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459566307100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e5cd899-0c88-443f-97da-60d6e1a8f8fe", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459565099400, "endTime": 152459566411000, "totalTime": 1165900}, "additional": {"logType": "info", "children": [], "durationId": "965b19e7-c517-467d-92df-b0b02fc3bafa"}}, {"head": {"id": "629a7d1a-e981-4de9-a1aa-acd5bab2e8ce", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459572109800, "endTime": 152459581580900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4b6d48f6-c210-453e-930d-7b81f98ac10a", "logId": "7bd015af-a8bf-47ee-8552-0530f75f5e1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b6d48f6-c210-453e-930d-7b81f98ac10a", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459568941200}, "additional": {"logType": "detail", "children": [], "durationId": "629a7d1a-e981-4de9-a1aa-acd5bab2e8ce"}}, {"head": {"id": "d4f8cb5c-740e-44ef-9685-e5a1c831c622", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459570360500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "452d0108-fc25-4139-9332-5a17d3710eca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459570477900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14367158-76e5-47f6-a0c7-8386cb1e893a", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459572121400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6175b2b6-d1e2-41a2-ac23-8f41c35f6875", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459579497800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86facbbf-ddc3-47b1-824e-a5fb0c05fe15", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459580878300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c9a2cd4-2475-4b30-ad96-fbee29e987d7", "name": "entry : default@GeneratePkgContextInfo cost memory 0.48711395263671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459581328900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bd015af-a8bf-47ee-8552-0530f75f5e1c", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459572109800, "endTime": 152459581580900}, "additional": {"logType": "info", "children": [], "durationId": "629a7d1a-e981-4de9-a1aa-acd5bab2e8ce"}}, {"head": {"id": "a9614bc8-a9a2-44d0-8eea-6a4bcb8b660b", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459592279100, "endTime": 152459595012500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "b41b32e6-57e2-4f96-846e-950d098bd187", "logId": "b4f0c58f-0486-4d69-aa74-a31e73420b0d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b41b32e6-57e2-4f96-846e-950d098bd187", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459585282100}, "additional": {"logType": "detail", "children": [], "durationId": "a9614bc8-a9a2-44d0-8eea-6a4bcb8b660b"}}, {"head": {"id": "5cb25f62-f209-419f-84d9-5d4557f6e844", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459586557300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff06df89-e9e7-45f7-ae90-228bccb3d737", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459586688700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d55ca968-b728-4ddb-99c2-9a26edc786ce", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459592298600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec2fcd7a-9145-4473-9dfb-0ff8bb0612f1", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459594317000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b465293-8d22-4030-8f87-3401d9a48e1e", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459594494200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5dc2aee-c25f-4704-bd9a-8ecb375ba1e1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459594584800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfc55353-f031-4fe3-9e61-47b1711e599e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459594650700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b9df1a8-a58a-4090-be4e-2a34911ad21b", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12281036376953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459594861700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7edb63e0-a5f8-4c03-9257-f741706bcb5e", "name": "runTaskFromQueue task cost before running: 3 s 494 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459594962700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4f0c58f-0486-4d69-aa74-a31e73420b0d", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459592279100, "endTime": 152459595012500, "totalTime": 2660300}, "additional": {"logType": "info", "children": [], "durationId": "a9614bc8-a9a2-44d0-8eea-6a4bcb8b660b"}}, {"head": {"id": "f96b7e86-5d97-4c04-a85b-55cb37d82c94", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459603640500, "endTime": 152459604116200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "121cb6e0-aca6-48d6-8b58-ac387dbf34d4", "logId": "13340452-5ad1-4576-98ea-96f6a21308e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "121cb6e0-aca6-48d6-8b58-ac387dbf34d4", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459599869200}, "additional": {"logType": "detail", "children": [], "durationId": "f96b7e86-5d97-4c04-a85b-55cb37d82c94"}}, {"head": {"id": "63cbc21e-248e-402f-88be-c3fc38c0776a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459602351300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56f812cc-a1d4-4d20-bec4-2933a7196c70", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459602534800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f332c295-7348-4976-a45f-e3c8d3b81013", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459603657400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11eb9530-ebea-4cb0-9ec7-d9f46a7c7975", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459603828000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8f36738-80b0-49f5-a86d-d3940531390d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459603883600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da5efb7a-a6b2-4cca-97c0-e5a3c9367fcd", "name": "entry : default@BuildNativeWithCmake cost memory 0.03899383544921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459603989400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e347a8b-232c-4c27-9eb7-4d5235ff379e", "name": "runTaskFromQueue task cost before running: 3 s 503 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459604068600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13340452-5ad1-4576-98ea-96f6a21308e1", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459603640500, "endTime": 152459604116200, "totalTime": 404100}, "additional": {"logType": "info", "children": [], "durationId": "f96b7e86-5d97-4c04-a85b-55cb37d82c94"}}, {"head": {"id": "33a7ed80-a575-4e18-a170-ac85f426da1e", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459607979700, "endTime": 152459612463600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b8d9af73-c2f6-4bab-bd78-8cc399fe7e66", "logId": "6c7508f7-ff3b-4120-a94f-843c42dc4bb2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8d9af73-c2f6-4bab-bd78-8cc399fe7e66", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459605964600}, "additional": {"logType": "detail", "children": [], "durationId": "33a7ed80-a575-4e18-a170-ac85f426da1e"}}, {"head": {"id": "93ee7e38-0451-4304-a0db-46dea35c42b9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459607073600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9384d61e-d296-40aa-8ab4-7b3c48aee834", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459607175400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c8634a9-48a5-4654-b51c-4972c47e0132", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459607991400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73957bcd-b7ae-42da-9836-cf9e58bbff6c", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459612109800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7b95a12-95d9-41e0-80bf-725d65df728d", "name": "entry : default@MakePackInfo cost memory 0.17159271240234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459612238100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c7508f7-ff3b-4120-a94f-843c42dc4bb2", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459607979700, "endTime": 152459612463600}, "additional": {"logType": "info", "children": [], "durationId": "33a7ed80-a575-4e18-a170-ac85f426da1e"}}, {"head": {"id": "68c499c8-acdb-45b4-8ce3-e31ef8b4e23a", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459619230800, "endTime": 152459658291700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e2a326e0-c62f-466f-895c-2d4a0764bf53", "logId": "66fdad90-6bea-43aa-b054-2a95c80fbc17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2a326e0-c62f-466f-895c-2d4a0764bf53", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459615969300}, "additional": {"logType": "detail", "children": [], "durationId": "68c499c8-acdb-45b4-8ce3-e31ef8b4e23a"}}, {"head": {"id": "e4520cd3-54db-45e9-a840-632725c68b16", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459617496300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92ca3db9-9acc-48f9-8115-d0d9ee1eea66", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459617647000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7447a0cc-1e2d-4f41-8126-871af83b1cfc", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459619248000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a302863-18a0-4499-8876-1e4bb70fcbdf", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459619488700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f6f4b27-9fc9-47f5-a915-697375a72ded", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459620516500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afc1334c-bb23-4e15-99d3-76e1aaa0d995", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 38 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459658025000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10de02fd-7b98-447e-a0c2-384362eb93d9", "name": "entry : default@SyscapTransform cost memory 0.16075897216796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459658197700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66fdad90-6bea-43aa-b054-2a95c80fbc17", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459619230800, "endTime": 152459658291700}, "additional": {"logType": "info", "children": [], "durationId": "68c499c8-acdb-45b4-8ce3-e31ef8b4e23a"}}, {"head": {"id": "b4412915-a6f8-4ecb-9d83-2d478a2a6876", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459665352600, "endTime": 152459668256900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "7ae39176-caf3-4217-9862-4067f3ecee1f", "logId": "f915c8d2-8196-432b-a4a8-a44456a001df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ae39176-caf3-4217-9862-4067f3ecee1f", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459660673100}, "additional": {"logType": "detail", "children": [], "durationId": "b4412915-a6f8-4ecb-9d83-2d478a2a6876"}}, {"head": {"id": "5300c7ba-1d13-417e-a89b-c70ca7a8a319", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459662096800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b72faa0f-421f-4623-8839-4b93680aadb4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459662209900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf20e334-a1d7-4561-a5ea-c724d720dc36", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459665383400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5001e5a-1e31-4988-9198-c73f21c90bd3", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459668068700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab11142a-b246-4d27-96f4-9e85d4a7d7ce", "name": "entry : default@ProcessProfile cost memory 0.12787628173828125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459668194900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f915c8d2-8196-432b-a4a8-a44456a001df", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459665352600, "endTime": 152459668256900}, "additional": {"logType": "info", "children": [], "durationId": "b4412915-a6f8-4ecb-9d83-2d478a2a6876"}}, {"head": {"id": "238d0419-342e-45e7-a49a-6cca69f62d68", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459673179000, "endTime": 152459680055900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "024b9bbd-4e92-43ca-afcb-9d981354dcd1", "logId": "5e47d640-297a-4bbf-92ed-7c7075cc94d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "024b9bbd-4e92-43ca-afcb-9d981354dcd1", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459670122500}, "additional": {"logType": "detail", "children": [], "durationId": "238d0419-342e-45e7-a49a-6cca69f62d68"}}, {"head": {"id": "1d9a2fd3-c109-4687-85b9-db609d049667", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459671227500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c641a60c-1273-4584-a7fb-99063c3d16d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459671325900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6f48033-6c57-4225-aa46-e8598f5692f0", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459673192200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a966c3a5-6a51-4be1-a026-d4ed3aa2fc8c", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459679690600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cabd896d-71d9-47c2-83c9-762b9971e639", "name": "entry : default@ProcessRouterMap cost memory 0.24420928955078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459679862000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e47d640-297a-4bbf-92ed-7c7075cc94d7", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459673179000, "endTime": 152459680055900}, "additional": {"logType": "info", "children": [], "durationId": "238d0419-342e-45e7-a49a-6cca69f62d68"}}, {"head": {"id": "c536fe2b-4fc3-4d82-8333-8c6db6ad9d53", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459690610300, "endTime": 152459697444100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "1c4a688d-b54a-4d94-b4f0-b4dd571c3217", "logId": "10a4d1be-ae7f-46f3-afb6-e7f6f6bda503"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c4a688d-b54a-4d94-b4f0-b4dd571c3217", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459688641600}, "additional": {"logType": "detail", "children": [], "durationId": "c536fe2b-4fc3-4d82-8333-8c6db6ad9d53"}}, {"head": {"id": "8bd4b52c-cbe2-46bf-9beb-1bfcd1ee9c85", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459690381600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e91b233-f487-48b3-9aa9-005b706642a1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459690505900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa235c10-189a-4826-a49a-39707e4aa826", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459690619500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0231748-1356-4c74-8ec4-855f66d99a98", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459690769000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52a9beca-f2e6-43c5-ab47-2523898a9fe1", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459695003300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8b1e046-0f96-46f7-ab02-c296afd834db", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459695167700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fb6fc66-dce7-4074-85fe-4b64836efe57", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459695255900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "811bf270-6ddb-464d-b79e-1009bd7f277e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459695297500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed3de15d-215c-4750-a47f-ad00ba95c12a", "name": "entry : default@ProcessStartupConfig cost memory 0.2662353515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459697051700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41fea636-d881-47fc-8c76-0d0295ac709a", "name": "runTaskFromQueue task cost before running: 3 s 596 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459697355700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10a4d1be-ae7f-46f3-afb6-e7f6f6bda503", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459690610300, "endTime": 152459697444100, "totalTime": 6679800}, "additional": {"logType": "info", "children": [], "durationId": "c536fe2b-4fc3-4d82-8333-8c6db6ad9d53"}}, {"head": {"id": "b2ab8227-6fe8-41bd-b577-f8e747693693", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459703634300, "endTime": 152459705134400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3a6e98c3-c1d9-47f7-b9d2-e926c62db6bf", "logId": "3605caeb-d0d8-476b-b6ff-9bcbc7057792"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a6e98c3-c1d9-47f7-b9d2-e926c62db6bf", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459701530700}, "additional": {"logType": "detail", "children": [], "durationId": "b2ab8227-6fe8-41bd-b577-f8e747693693"}}, {"head": {"id": "ab59d93a-b0c5-422b-b618-3d64166681bf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459702711400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24474eb3-c4a8-4933-8506-5a18e9f782cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459702834400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bae5cd2-4df8-4ba8-839e-49106e1bbfbb", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459703648600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b8d4cbe-2943-4164-a4f1-a0fa2e08240a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459703795100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87a8d428-a6e2-4e0a-8623-4be9b3d6008b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459703846800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b2fdf32-f88f-46ef-9ce4-5003402d1218", "name": "entry : default@BuildNativeWithNinja cost memory 0.0587921142578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459704938100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0247b0c-7bdf-4dee-ad09-aa04e0c0f30c", "name": "runTaskFromQueue task cost before running: 3 s 604 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459705073300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3605caeb-d0d8-476b-b6ff-9bcbc7057792", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459703634300, "endTime": 152459705134400, "totalTime": 1415400}, "additional": {"logType": "info", "children": [], "durationId": "b2ab8227-6fe8-41bd-b577-f8e747693693"}}, {"head": {"id": "72dfa1a6-b4a6-4b38-8b42-1d9988f455e0", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459712210400, "endTime": 152459720270300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "a0ed4978-9a94-45e7-a59c-ac0eed2b7dc0", "logId": "2f88c9d3-62fd-448c-8e73-773854711962"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0ed4978-9a94-45e7-a59c-ac0eed2b7dc0", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459707797000}, "additional": {"logType": "detail", "children": [], "durationId": "72dfa1a6-b4a6-4b38-8b42-1d9988f455e0"}}, {"head": {"id": "345ea679-3e1f-4d87-8e5c-7ea8d7e6fccb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459708878000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7f50fa9-4910-4f2c-b52a-eff105dab4cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459708991800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8279cc69-6f7a-47b0-90fe-279dee314c58", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459710677900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82aaf045-95ba-4bbd-8f1a-c6f054bf1ae8", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459715117500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6067975e-cbda-4fa5-aff7-2d4efb8c0429", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459718089100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa15c80b-4670-4d00-80d5-196af7fc3fa6", "name": "entry : default@ProcessResource cost memory 0.16669464111328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459718251200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f88c9d3-62fd-448c-8e73-773854711962", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459712210400, "endTime": 152459720270300}, "additional": {"logType": "info", "children": [], "durationId": "72dfa1a6-b4a6-4b38-8b42-1d9988f455e0"}}, {"head": {"id": "543ddeb6-33e1-486c-988b-30f30dd5e29f", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459729470500, "endTime": 152459755963400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "10636b63-d06e-46d9-8e6a-35b16d84e04e", "logId": "647fcddb-f8dd-4893-8ca9-044c1921bd19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10636b63-d06e-46d9-8e6a-35b16d84e04e", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459724368900}, "additional": {"logType": "detail", "children": [], "durationId": "543ddeb6-33e1-486c-988b-30f30dd5e29f"}}, {"head": {"id": "dc65624a-de18-4bf4-bb99-9956ba6bc845", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459725432900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2afebe36-61bb-4956-9f20-24919990ffb6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459725550400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7db2c933-1757-4c8c-88c4-1ba2f00e8148", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459729484800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15ba44a2-f035-41e6-ad99-33dfc83d53f9", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459755697200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf83ed41-62b5-45ab-8c4f-b4eda69b73a8", "name": "entry : default@GenerateLoaderJson cost memory 1.0354766845703125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459755884900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "647fcddb-f8dd-4893-8ca9-044c1921bd19", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459729470500, "endTime": 152459755963400}, "additional": {"logType": "info", "children": [], "durationId": "543ddeb6-33e1-486c-988b-30f30dd5e29f"}}, {"head": {"id": "21c20b73-4a15-42eb-a33b-f81722198fcb", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459767687700, "endTime": 152459772828900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "47d9c0b6-b5c5-4e3a-8f65-17dcc16d6088", "logId": "cf5f9e42-2ceb-4c39-8088-2fd0c37433b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47d9c0b6-b5c5-4e3a-8f65-17dcc16d6088", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459765729200}, "additional": {"logType": "detail", "children": [], "durationId": "21c20b73-4a15-42eb-a33b-f81722198fcb"}}, {"head": {"id": "172759cf-94e0-4b53-b9c3-23b13164d7b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459766846500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3fef22a-3913-4f11-9307-405a7ce080e7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459766973100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18677309-9598-4d54-9bfa-2ced1685ecf8", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459767698000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea6d397b-bf5e-4c61-b7eb-ed39c0c38b88", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459772598800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e123272f-e2e0-4e43-85a7-65befce2fb5a", "name": "entry : default@ProcessLibs cost memory 0.1481781005859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459772758600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf5f9e42-2ceb-4c39-8088-2fd0c37433b2", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459767687700, "endTime": 152459772828900}, "additional": {"logType": "info", "children": [], "durationId": "21c20b73-4a15-42eb-a33b-f81722198fcb"}}, {"head": {"id": "510303b4-8af7-4b47-a67a-44fc5ce0d6f6", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459779771700, "endTime": 152459811151100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5c7c9a20-7286-4ce9-832b-4993d675186c", "logId": "bf8f5d7a-890a-44c4-b813-b078ef78c758"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c7c9a20-7286-4ce9-832b-4993d675186c", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459775018800}, "additional": {"logType": "detail", "children": [], "durationId": "510303b4-8af7-4b47-a67a-44fc5ce0d6f6"}}, {"head": {"id": "2105c53c-4784-4497-a2bf-3acff362b065", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459776164100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62433ad0-c062-430f-acc6-ba155e08cb38", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459776275000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8968f22d-fd64-4672-89e1-fbb12795ba8f", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459777199400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ebe2659-fbc0-4179-8f5d-d2e501e66cd9", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459779915600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "763a1944-41ed-4df1-9097-94496aa4db3c", "name": "Incremental task entry:default@CompileResource pre-execution cost: 28 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459810708800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f63d6f69-c460-4240-a1f3-df1d707cfbb1", "name": "entry : default@CompileResource cost memory 1.6798553466796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459811039900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf8f5d7a-890a-44c4-b813-b078ef78c758", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459779771700, "endTime": 152459811151100}, "additional": {"logType": "info", "children": [], "durationId": "510303b4-8af7-4b47-a67a-44fc5ce0d6f6"}}, {"head": {"id": "6956bd35-f17d-45b8-a1c7-f53124d9a74f", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459819715500, "endTime": 152459821989600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "57852fb8-b5e1-4fe8-848d-9c8ed557b1a3", "logId": "c8a41eac-70bf-41b9-b3c8-9b0edeb1a98f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57852fb8-b5e1-4fe8-848d-9c8ed557b1a3", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459815409800}, "additional": {"logType": "detail", "children": [], "durationId": "6956bd35-f17d-45b8-a1c7-f53124d9a74f"}}, {"head": {"id": "ec53b50b-0d3d-41d8-b493-373281fadc22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459816857200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6177b7e8-8e6a-485a-bf30-c9adcae448ac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459817029000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e207ccf-b38a-497c-bfa5-92e572a31546", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459819731100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3eda7e38-7b0a-4d49-b281-b70a8dbd061f", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459820330900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63afc675-94ee-4965-ba98-9756ee76afd8", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459821802900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ed06c00-e289-4baa-8483-dd41e112f28b", "name": "entry : default@DoNativeStrip cost memory 0.08416748046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459821924600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8a41eac-70bf-41b9-b3c8-9b0edeb1a98f", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459819715500, "endTime": 152459821989600}, "additional": {"logType": "info", "children": [], "durationId": "6956bd35-f17d-45b8-a1c7-f53124d9a74f"}}, {"head": {"id": "f5e37c0d-c561-4514-ac0d-9486ce701a23", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459829264900, "endTime": 152471193321600}, "additional": {"children": ["0dde4728-f2ce-4553-8e2e-b07ecdeb325e", "52b09311-aa66-4353-a114-87e303cfc2c5"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "0d86562d-0460-4828-add3-f76dd11d7f34", "logId": "750f8e23-df3a-4abb-af1f-56545bfd330d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d86562d-0460-4828-add3-f76dd11d7f34", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459823805400}, "additional": {"logType": "detail", "children": [], "durationId": "f5e37c0d-c561-4514-ac0d-9486ce701a23"}}, {"head": {"id": "d294d079-f2d0-4e43-93e4-a856ac8f8b3a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459824843700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "979e1e9d-4b0c-44f5-b75f-b429189b5d3c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459824959300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ce1bd02-8d7c-4e69-af31-9fa5cc7f8348", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459829282600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d14a8510-ef37-4809-b3f6-80e9d1699aa3", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459829951500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c13711b-f49b-4afb-9ce8-266bf638b53f", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459867188300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69ceb913-bdef-4385-ba03-e9e5d0b2eea5", "name": "default@CompileArkTS work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459869747400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dde4728-f2ce-4553-8e2e-b07ecdeb325e", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152459872490800, "endTime": 152471193091100}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f5e37c0d-c561-4514-ac0d-9486ce701a23", "logId": "37c7f78c-c266-4fb5-a575-e8591cf97860"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "adca493e-021b-4fde-80f7-577e6b3eeab5", "name": "default@CompileArkTS work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459870878100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7c36d96-2306-4403-a4e4-d935c67becd5", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871122700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76dee500-c80d-411c-a254-125d62071e91", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871182500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a69b8f3-97fa-4e05-a81f-1ef0c80123b7", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871215000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e7f22df-c45b-476f-9f4d-1b89072d9b90", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871241200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c8fe733-6105-46e0-a293-19f9d59e1950", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871266300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83bf9e60-4325-4269-a9ed-14df01d8bece", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871292500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0f5b788-b983-4e57-9acd-d3087f15d69a", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871319200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e4dce3c-5b4d-4e63-b030-ad71baa02a2d", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871342100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7ca1a89-1951-4870-8b19-b1ce00c59433", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871366300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b9c07be-14c2-474f-8d96-5b823ba1f9b2", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871391100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cab6692f-3aac-4e07-963d-4adb12f7413b", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871414700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76d26d37-94b9-49de-a28d-db6cdfd689a6", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871438600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8caaa198-d198-4dcb-bf27-4852be1c0ad7", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871462100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c4603be-f180-41a2-b356-c27334398a39", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871484900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79c3b6c8-07e8-4394-bf4a-bad1d9c35ad5", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871507600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53853e5d-2953-43d2-b886-7555a1104e73", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459871617700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b6c9e3c-b35c-461d-b23a-015ad29fc013", "name": "default@CompileArkTS work[0] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459872518300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a268b2b-a87d-418e-8c4d-c54dc944c414", "name": "default@CompileArkTS work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459872639000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d561961-a03a-43b9-8750-abdc397ceda2", "name": "CopyResources startTime: 152459872739500", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459872742500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "230ae1d3-7386-4df3-9de2-7f689269cdf3", "name": "default@CompileArkTS work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459872807100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52b09311-aa66-4353-a114-87e303cfc2c5", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 152461013233100, "endTime": 152461029210600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f5e37c0d-c561-4514-ac0d-9486ce701a23", "logId": "85bed412-54ca-4746-be32-c1d60b3e18d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9ae882f-9466-4866-8c63-f8381d74db26", "name": "default@CompileArkTS work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459873575000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9508b5c-5456-444e-8ef5-e41151844805", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459873653300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c30efc9-c711-4163-a513-349c7abbbed5", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459873702800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39c1e489-baae-4db2-aaca-b84d2ebb1ee9", "name": "default@CompileArkTS work[1] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459874467800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d483950b-a1e5-40b0-8f08-65213bfe4098", "name": "default@CompileArkTS work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459874547400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56489870-1984-4658-ba66-01297824f641", "name": "entry : default@CompileArkTS cost memory 1.9558258056640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459874669700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89a619b9-fe67-4323-b62a-4dec6ae2ca60", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459880756600, "endTime": 152459888427400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "0dab860a-66e9-41d2-9dcb-36fcfe81c034", "logId": "813a9c84-d294-442f-8834-13989558bef6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dab860a-66e9-41d2-9dcb-36fcfe81c034", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459876151000}, "additional": {"logType": "detail", "children": [], "durationId": "89a619b9-fe67-4323-b62a-4dec6ae2ca60"}}, {"head": {"id": "a746b402-1fc7-4cac-b58e-b68b16b0de91", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459877060200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08d8e9ea-14a6-482c-a223-2f2d1dafc8af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459877154700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c4761dd-a667-4e35-9d85-108f0870752e", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459880786400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eccecb0a-53b1-458e-b153-1c4de27a4158", "name": "entry : default@BuildJS cost memory 0.35546112060546875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459888206000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "530f3e8a-aa48-4a1e-820b-353f6f01ec41", "name": "runTaskFromQueue task cost before running: 3 s 787 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459888364700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "813a9c84-d294-442f-8834-13989558bef6", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459880756600, "endTime": 152459888427400, "totalTime": 7573200}, "additional": {"logType": "info", "children": [], "durationId": "89a619b9-fe67-4323-b62a-4dec6ae2ca60"}}, {"head": {"id": "04d085ec-b9c9-4842-a9b0-7b1879200ba8", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459893508800, "endTime": 152459897505700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e2a0df20-6846-46ee-bb62-05669052bccb", "logId": "0509a8e5-1f39-435e-996a-8a3e822fdb33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2a0df20-6846-46ee-bb62-05669052bccb", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459889962300}, "additional": {"logType": "detail", "children": [], "durationId": "04d085ec-b9c9-4842-a9b0-7b1879200ba8"}}, {"head": {"id": "19470689-dc1c-47ef-8b87-92a3753e0456", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459891087700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fca2a840-3ec1-431e-b949-c095a7edbf7c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459891184000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "643c351a-0760-4f8a-89a1-37d44d708c1c", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459893521100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0bbb2df-a72e-49b4-b0e2-769836897dc7", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459894358400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a212302-fa15-4fef-add2-940824dbc236", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459896812700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09f4aa10-7b15-46cd-a2b6-f3e985d94fad", "name": "entry : default@CacheNativeLibs cost memory 0.1003570556640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459897247000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0509a8e5-1f39-435e-996a-8a3e822fdb33", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459893508800, "endTime": 152459897505700}, "additional": {"logType": "info", "children": [], "durationId": "04d085ec-b9c9-4842-a9b0-7b1879200ba8"}}, {"head": {"id": "7970bf51-c6f8-4112-b7c5-261f018a79e6", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152461029939800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56560508-f739-49e7-8f4c-43312aad4447", "name": "CopyResources is end, endTime: 152461030320200", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152461030332800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "901fed24-5715-431f-9b9e-fa9e446e3d58", "name": "default@CompileArkTS work[1] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152461030752200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85bed412-54ca-4746-be32-c1d60b3e18d9", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 152461013233100, "endTime": 152461029210600}, "additional": {"logType": "info", "children": [], "durationId": "52b09311-aa66-4353-a114-87e303cfc2c5", "parent": "750f8e23-df3a-4abb-af1f-56545bfd330d"}}, {"head": {"id": "091ebcd0-2eb2-4774-b2ae-73de68074577", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152461031604500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b79b05d7-7151-4ac1-b516-83d1ce979b6e", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471192869300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5ea062c-963b-49c2-9191-f0845a0f9fc5", "name": "default@CompileArkTS work[0] failed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471193184300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37c7f78c-c266-4fb5-a575-e8591cf97860", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152459872490800, "endTime": 152471193091100}, "additional": {"logType": "error", "children": [], "durationId": "0dde4728-f2ce-4553-8e2e-b07ecdeb325e", "parent": "750f8e23-df3a-4abb-af1f-56545bfd330d"}}, {"head": {"id": "750f8e23-df3a-4abb-af1f-56545bfd330d", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152459829264900, "endTime": 152471193321600}, "additional": {"logType": "error", "children": ["37c7f78c-c266-4fb5-a575-e8591cf97860", "85bed412-54ca-4746-be32-c1d60b3e18d9"], "durationId": "f5e37c0d-c561-4514-ac0d-9486ce701a23"}}, {"head": {"id": "d5fa5fb2-7742-4179-89c8-b7a186791647", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471193470600}, "additional": {"logType": "debug", "children": [], "durationId": "f5e37c0d-c561-4514-ac0d-9486ce701a23"}}, {"head": {"id": "11dd68d8-51e6-44d5-b141-0908fcd99360", "name": "ERROR: stacktrace = Error: Duplicate export 'HomePage' (Note that you need plugins to import files that are not JavaScript)\r\nC:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets:795\r\n\u001b[33m1 WARN: \u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/HomePage.ets:19:1\n It's not a recommended way to export struct with '@Entry' decorator, which may cause ACE Engine error in component preview mode.\u001b[39m\u001b[39m\r\n\u001b[31m1 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot redeclare exported variable 'HomePage'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/HomePage.ets:21:15\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m2 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot redeclare exported variable 'HomePage'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/HomePage.ets:795:10\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m3 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Export declaration conflicts with exported declaration of 'HomePage'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/HomePage.ets:795:10\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:4 WARN:1}\u001b[39m\n    at runArkPack (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5479)\r\nRollupError: Duplicate export 'HomePage' (Note that you need plugins to import files that are not JavaScript)\n    at error (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-base\\node_modules\\rollup\\dist\\shared\\rollup.js:210:30)\n    at Module.error (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-base\\node_modules\\rollup\\dist\\shared\\rollup.js:13962:16)\n    at Module.tryParse (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-base\\node_modules\\rollup\\dist\\shared\\rollup.js:14728:25)\n    at Module.setSource (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-base\\node_modules\\rollup\\dist\\shared\\rollup.js:14257:37)\n    at ModuleLoader.addModuleSource (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-base\\node_modules\\rollup\\dist\\shared\\rollup.js:24000:20)", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471194868200}, "additional": {"logType": "debug", "children": [], "durationId": "f5e37c0d-c561-4514-ac0d-9486ce701a23"}}, {"head": {"id": "8a35284e-a3e1-4da9-8e9f-24045794752f", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471210635200, "endTime": 152471210964800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "70936d0f-a4ee-47b3-833b-cd12a129326a", "logId": "81f5b429-e261-4c40-8754-cf183903b2a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81f5b429-e261-4c40-8754-cf183903b2a5", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471210635200, "endTime": 152471210964800}, "additional": {"logType": "info", "children": [], "durationId": "8a35284e-a3e1-4da9-8e9f-24045794752f"}}, {"head": {"id": "63856898-947d-43dc-b3dd-6d67bfb64797", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152456101827700, "endTime": 152471211348300}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 18, "second": 20}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "8940bfc9-cac9-43bd-8e62-2ea48449b7e0", "name": "BUILD FAILED in 15 s 110 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471211395300}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "119bbf32-28aa-4fb7-b4f9-74bb3c7f91fa", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471211680000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0f4ca4f-9ce5-4e63-9070-a63628951d14", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471211795900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7df5898-3681-426d-91fb-69b0574dc081", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471212520600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7ad718f-7e75-4a58-86f0-b6e10fecc287", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471212621400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ae3f87a-3ba3-4fef-94c4-63b84dfa683e", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471212660700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c44c1755-6ded-4870-a535-45289bb95f8b", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471212692100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3318ac-a437-4007-b9ef-e7d25db56ade", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471212730200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d215989-8abc-47c1-9c4e-c57e9edc3775", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471213381800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74de4d62-9a4d-4515-a6e1-0001accc445e", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471213646900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d902e8e-82c2-47b4-b352-7abcc0cdb462", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471213722500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f321126-c077-4094-ae93-ae016f839dd1", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471213759700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff04bc70-74ec-4f0d-ba63-815585528431", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471213794800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77219844-1afb-493c-9fd4-0f52555ad865", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471213824300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5a2962c-5994-4ac1-ad69-1d6dcb09578a", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471214985400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "759c08dd-4437-4c64-910c-c1c159f5d227", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471215284200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8680eae-5936-47a5-b5ed-a227e9009792", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471215546600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "283ad115-2e85-4400-9b95-0aa4c63c3b74", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471215615300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16e752d6-892e-4276-87c8-9a22603a6986", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471215654900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "372feebb-6bac-408d-af16-548ed7b6df37", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471215689200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e696073b-9ade-4590-945c-be2d59db9263", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471215718400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bda17aab-b6a2-4a5e-8475-4182f521eec6", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471215747200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "128cb8a1-b706-4fd2-85f0-e858bbe50712", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471218716600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4c6a57e-1a2d-42b9-9465-37e5ca99da7c", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471219336100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f08d149-0045-40ac-a5ac-5b84951cc955", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471219706100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cffd75b-d1fb-4767-b1e0-4762f7044fc6", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471219931300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25f5f531-5ae4-4427-8cf9-027bc4ed0d47", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471220149000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53f30ef0-895a-4c7d-b247-9a8868c6fcd3", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471220834000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bd2a8a8-d9cc-4129-918d-c4ed33d9c073", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471227301400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6ad5ef0-8325-4741-882b-ad771837fcb9", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471227592100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "917e904f-2c51-4fa2-a12a-0f0b64154c13", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471227964200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b6841a2-b672-434a-9f6e-1e4ccdb58ccc", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471228954000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b680a76-233f-4059-a457-dcee89135f2e", "name": "Incremental task entry:default@CompileArkTS post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471229633200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "988681e4-f6a8-45f9-b45c-c97c6f58f720", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471232974400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d215730-4098-44da-ab00-4214aa0257f4", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471233702700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15278ddc-76ea-43ae-aa53-85e859bb490c", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471234115500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fbd712f-6f16-4d5e-b934-fc23a64134d4", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471234509900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b1e2e54-ea51-469e-9b9a-0ba06703fe3f", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471234787000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e24219bd-0bfc-4987-904b-dbf41ed3dfb5", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471235775700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0175b4e-0c05-465a-a868-03d19094cf73", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471237090500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "049cf207-3c42-4ace-aa9e-de28771cd532", "name": "Incremental task entry:default@BuildJS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471237607100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87d05a76-a63b-480f-bb2a-df9e140a0a1b", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471237698600}, "additional": {"logType": "debug", "children": []}}], "workLog": []}