import { AppModel, AppListResponse, AppSearchParams } from '../models/App';
import { CategoryModel, CategoryListResponse } from '../models/Category';
import { BannerModel, BannerResponse } from '../models/Banner';
import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { AppCard } from '../components/AppCard';
import { SearchBar } from '../components/SearchBar';
import { NavigationBar, NavItem } from '../components/NavigationBar';
import { LoadingView, LoadingState, LoadMoreView } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { preferences } from '@kit.ArkData';
// getContext is deprecated, use this.getUIContext().getHostContext() instead
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 主页面
 */
@Entry
@Component
struct HomePage {
  @State private recommendedApps: AppModel[] = [];
  @State private bannerList: BannerModel[] = [];
  @State private loadingState: LoadingState = LoadingState.LOADING;
  @State private refreshing: boolean = false;
  @State private searchKeyword: string = '';
  @State private needLogin: boolean = false;
  @State private currentBannerIndex: number = 0;
  private bannerTimer: number = -1;
  private swiperController: SwiperController = new SwiperController();

  private deviceUtils = DeviceUtils.getInstance();
  private apiService = ApiService.getInstance();

  // 注释：移除导航项配置，统一由Index.ets管理

  aboutToAppear() {
    this.loadHomeData();
    this.startBannerAutoPlay();
  }

  /**
   * 页面显示时的回调
   */
  onPageShow() {
    this.checkAndSetAuthToken();
    this.refreshData();
  }

  aboutToDisappear() {
    this.stopBannerAutoPlay();
  }

  /**
   * 加载首页数据
   */
  private async loadHomeData(): Promise<void> {
    this.loadingState = LoadingState.LOADING;
    
    try {
      hilog.info(0x0000, 'HomePage', '开始加载首页数据...');
      
      // 检查并设置认证令牌（可选）
      try {
        await this.checkAndSetAuthToken();
        hilog.info(0x0000, 'HomePage', '认证令牌设置成功');
      } catch (authError) {
        hilog.warn(0x0000, 'HomePage', '认证令牌设置失败，将使用匿名访问: %{public}s', JSON.stringify(authError));
      }
      
      // 尝试从API加载数据
      const results = await Promise.allSettled([
        this.apiService.getBanners(),
        this.apiService.getRecommendedApps()
      ]);
      
      const bannersResult = results[0];
      const recommendedResult = results[1];
      
      hilog.info(0x0000, 'HomePage', 'API请求结果: banners=%{public}s, recommended=%{public}s', 
        bannersResult.status, recommendedResult.status);
      
      let hasApiData = false;
      
      // 处理轮播图结果
      if (bannersResult.status === 'fulfilled' && bannersResult.value.code === 200) {
        this.bannerList = Array.isArray(bannersResult.value.data) ? bannersResult.value.data.filter(banner => banner.is_active) : [];
        hilog.info(0x0000, 'HomePage', '轮播图加载成功，数量: %{public}d', this.bannerList.length);
      } else {
        hilog.warn(0x0000, 'HomePage', '轮播图加载失败，使用模拟数据');
        this.loadMockBannerData();
      }
      
      // 处理推荐应用结果
      if (recommendedResult.status === 'fulfilled' && recommendedResult.value.code === 200) {
        // 后端返回的data包含list字段
        this.recommendedApps = Array.isArray(recommendedResult.value.data) ? recommendedResult.value.data : (recommendedResult.value.data?.list || []);
        hilog.info(0x0000, 'HomePage', '推荐应用加载成功，数量: %{public}d', this.recommendedApps.length);
        hasApiData = true;
      }
      
      // 如果API数据加载失败，使用模拟数据
      if (!hasApiData) {
        hilog.warn(0x0000, 'HomePage', 'API数据加载失败，使用模拟数据');
        this.loadMockData();
      }
      
      this.loadingState = LoadingState.SUCCESS;
      hilog.info(0x0000, 'HomePage', '首页数据加载完成');
    } catch (error) {
      hilog.error(0x0000, 'HomePage', '加载首页数据时发生异常: %{public}s', JSON.stringify(error));
      hilog.info(0x0000, 'HomePage', '使用模拟数据作为备选方案');
      this.loadMockData();
      this.loadMockBannerData();
      this.loadingState = LoadingState.SUCCESS;
    }
  }
  
  /**
   * 加载轮播图模拟数据
   */
  private loadMockBannerData() {
    this.bannerList = [
      {
        id: 1,
        title: '热门应用推荐',
        subtitle: '发现最受欢迎的应用',
        image_url: 'https://picsum.photos/800/300?random=1',
        link_type: 'category',
        link_value: '1',
        sort_order: 1,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        title: '新品上架',
        subtitle: '体验最新发布的应用',
        image_url: 'https://picsum.photos/800/300?random=2',
        link_type: 'app',
        link_value: '1',
        sort_order: 2,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 3,
        title: '编辑精选',
        subtitle: '编辑为您精心挑选的优质应用',
        image_url: 'https://picsum.photos/800/300?random=3',
        link_type: 'url',
        link_value: 'https://example.com',
        sort_order: 3,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }
    ];
    hilog.info(0x0000, 'HomePage', '轮播图模拟数据加载完成，数量: %{public}d', this.bannerList.length);
  }

  /**
   * 加载模拟数据
   */
  private loadMockData(): void {
    hilog.info(0x0000, 'HomePage', '正在加载模拟数据...');
    
    // 模拟推荐应用数据
    const mockRecommendedApps: AppModel[] = [
      {
        id: 1,
        created_at: '2024-01-15T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z',
        name: '微信',
        package_name: 'com.tencent.wechat',
        description: '一个为智能终端提供即时通讯服务的免费应用程序',
        short_description: '即时通讯服务',
        icon: Constants.PLACEHOLDER_IMAGE,
        category_id: 1,
        category_name: '社交',
        developer_id: 1,
        developer_name: '腾讯科技',
        version: '8.0.32',
        version_code: 80032,
        min_sdk_version: 21,
        target_sdk_version: 33,
        size: 245000000,
        download_url: '',
        download_count: 1000000,
        rating: 4.8,
        review_count: 50000,
        screenshots: [Constants.PLACEHOLDER_IMAGE],
        permissions: [],
        tags: [],
        changelog: '',
        privacy_policy: '',
        support_email: '',
        website: '',
        status: 'published',
        is_featured: false,
        is_editor_choice: true,
        is_top: false,
        published_at: '2024-01-15T00:00:00Z',
        review_status: 'approved',
        review_reason: '',
        reviewed_at: '2024-01-15T00:00:00Z',
        reviewer_id: 1
      },
      {
        id: 2,
        created_at: '2024-01-10T00:00:00Z',
        updated_at: '2024-01-10T00:00:00Z',
        name: '支付宝',
        package_name: 'com.alipay.android',
        description: '数字生活开放平台',
        short_description: '数字生活平台',
        icon: Constants.PLACEHOLDER_IMAGE,
        category_id: 2,
        category_name: '金融',
        developer_id: 2,
        developer_name: '蚂蚁集团',
        version: '10.3.20',
        version_code: 103020,
        min_sdk_version: 21,
        target_sdk_version: 33,
        size: 156000000,
        download_url: '',
        download_count: 800000,
        rating: 4.7,
        review_count: 40000,
        screenshots: [Constants.PLACEHOLDER_IMAGE],
        permissions: [],
        tags: [],
        changelog: '',
        privacy_policy: '',
        support_email: '',
        website: '',
        status: 'published',
        is_featured: false,
        is_editor_choice: true,
        is_top: false,
        published_at: '2024-01-10T00:00:00Z',
        review_status: 'approved',
        review_reason: '',
        reviewed_at: '2024-01-10T00:00:00Z',
        reviewer_id: 1
      },
      {
        id: 3,
        created_at: '2024-01-12T00:00:00Z',
        updated_at: '2024-01-12T00:00:00Z',
        name: '抖音',
        package_name: 'com.douyin.android',
        description: '记录美好生活',
        short_description: '短视频平台',
        icon: Constants.PLACEHOLDER_IMAGE,
        category_id: 3,
        category_name: '娱乐',
        developer_id: 3,
        developer_name: '北京微播视界科技有限公司',
        version: '28.5.0',
        version_code: 285000,
        min_sdk_version: 21,
        target_sdk_version: 33,
        size: 189000000,
        download_url: '',
        download_count: 1200000,
        rating: 4.6,
        review_count: 60000,
        screenshots: [Constants.PLACEHOLDER_IMAGE],
        permissions: [],
        tags: [],
        changelog: '',
        privacy_policy: '',
        support_email: '',
        website: '',
        status: 'published',
        is_featured: false,
        is_editor_choice: true,
        is_top: false,
        published_at: '2024-01-12T00:00:00Z',
        review_status: 'approved',
        review_reason: '',
        reviewed_at: '2024-01-12T00:00:00Z',
        reviewer_id: 1
      }
    ];
    this.recommendedApps = mockRecommendedApps;
    
    hilog.info(0x0000, 'HomePage', '模拟数据加载完成: recommendedApps=%{public}d', 
      this.recommendedApps.length);
  }

  /**
   * 检查并设置认证token
   */
  private async checkAndSetAuthToken(): Promise<boolean> {
    try {
      const context = this.getUIContext().getHostContext();
      const options: preferences.Options = { name: 'user_data' };
      const dataPreferences = preferences.getPreferencesSync(context, options);
      const token = dataPreferences.getSync('token', '') as string;
      
      if (token) {
        this.apiService.setAuthToken(token);
        return true;
      }
      return false;
    } catch (error) {
      hilog.error(0x0000, 'HomePage', 'Failed to check auth token: %{public}s', JSON.stringify(error));
      return false;
    }
  }

  /**
   * 清除过期的token
   */
  private async clearExpiredToken(): Promise<void> {
    try {
      const context = this.getUIContext().getHostContext();
      const options: preferences.Options = { name: 'user_data' };
      const dataPreferences = preferences.getPreferencesSync(context, options);
      dataPreferences.deleteSync('token');
      dataPreferences.flush();
      this.apiService.setAuthToken('');
    } catch (error) {
      hilog.error(0x0000, 'HomePage', '清除token失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 跳转到登录页面
   */
  private navigateToLogin(): void {
    this.getUIContext().getRouter().pushUrl({ url: 'pages/LoginPage' });
  }

  /**
   * 刷新数据
   */
  private async refreshData() {
    this.refreshing = true;
    await this.loadHomeData();
    this.refreshing = false;
  }

  /**
   * 开始轮播图自动播放
   */
  private startBannerAutoPlay() {
    if (this.bannerTimer !== -1) {
      clearInterval(this.bannerTimer);
    }
    this.bannerTimer = setInterval(() => {
      if (this.bannerList && this.bannerList.length > 0) {
        this.currentBannerIndex = (this.currentBannerIndex + 1) % this.bannerList.length;
        this.swiperController.showNext();
      }
    }, 3000);
  }

  /**
   * 停止轮播图自动播放
   */
  private stopBannerAutoPlay() {
    if (this.bannerTimer !== -1) {
      clearInterval(this.bannerTimer);
      this.bannerTimer = -1;
    }
  }

  /**
   * 搜索应用
   */
  private searchApps(keyword: string) {
    if (keyword.trim()) {
      this.getUIContext().getRouter().pushUrl({
        url: 'pages/SearchPage',
        params: { keyword: keyword.trim() }
      });
    }
  }

  /**
   * 跳转到应用详情
   */
  private navigateToAppDetail(app: AppModel) {
    this.getUIContext().getRouter().pushUrl({
      url: 'pages/AppDetailPage',
      params: { appId: app.id.toString() }
    });
  }

  /**
   * 跳转到分类页面
   */
  private navigateToCategory(category: CategoryModel) {
    this.getUIContext().getRouter().pushUrl({
      url: 'pages/CategoryPage',
      params: { categoryId: category.id.toString(), categoryName: category.name }
    });
  }

  /**
   * 跳转到应用列表
   */
  private navigateToAppList(title: string, params: AppSearchParams) {
    this.getUIContext().getRouter().pushUrl({
      url: 'pages/AppListPage',
      params: { title, searchParams: JSON.stringify(params) }
    });
  }

  /**
   * 顶部搜索栏
   */
  @Builder
  private TopSearchBar() {
    Row() {
      SearchBar({
        placeholder: '搜索应用',
        searchText: this.searchKeyword,
        showCancelButton: false,
        onSearch: (keyword: string): void => this.searchApps(keyword),
        onTextChange: (text: string): void => {
          this.searchKeyword = text;
        },
        onSearchClick: (): void => {
          // 点击搜索栏时跳转到搜索页面
          this.getUIContext().getRouter().pushUrl({
            url: 'pages/SearchPage',
            params: { keyword: this.searchKeyword || '' }
          });
        }
      })
        .layoutWeight(1)

      Text('📷')
        .width(24)
        .height(24)
        .fontColor($r('sys.color.ohos_id_color_text_primary'))
        .margin({ left: 12 })
        .onClick(() => {
          // 扫码功能
        })
    }
    .width('100%')
    .padding({ left: '16vp', right: '16vp', top: '8vp', bottom: '8vp' })
    .backgroundColor($r('sys.color.ohos_id_color_background'))
  }





  /**
   * 轮播图组件
   */
  @Builder
  private BannerSwiper() {
    if (this.bannerList && this.bannerList.length > 0) {
      Swiper(this.swiperController) {
        ForEach(this.bannerList, (banner: BannerModel, index: number) => {
          Stack({ alignContent: Alignment.BottomStart }) {
            Image(banner.image_url)
               .width('100%')
               .height(180)
               .objectFit(ImageFit.Cover)
               .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
               .alt($r('app.media.ic_empty'))

            // 渐变遮罩
            Column()
              .width('100%')
              .height(80)
              .linearGradient({
                direction: GradientDirection.Bottom,
                colors: [['rgba(0,0,0,0)', 0], ['rgba(0,0,0,0.6)', 1]]
              })
              .borderRadius({ bottomLeft: Constants.BORDER_RADIUS.MEDIUM, bottomRight: Constants.BORDER_RADIUS.MEDIUM })

            // 文字信息
            Column({ space: 4 }) {
              Text(banner.title)
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
                .fontWeight(FontWeight.Bold)
                .fontColor(Color.White)
                .maxLines(1)
                .textOverflow({ overflow: TextOverflow.Ellipsis })

              if (banner.subtitle) {
                Text(banner.subtitle)
                  .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
                  .fontColor('rgba(255,255,255,0.8)')
                  .maxLines(1)
                  .textOverflow({ overflow: TextOverflow.Ellipsis })
              }
            }
            .alignItems(HorizontalAlign.Start)
            .padding({ left: 16, right: 16, bottom: 16 })
          }
          .width('100%')
          .height(180)
          .onClick(() => this.handleBannerClick(banner))
        })
      }
      .width('100%')
      .height(180)
      .autoPlay(true)
      .interval(3000)
      .indicator(
        new DotIndicator()
          .itemWidth(8)
          .itemHeight(8)
          .selectedItemWidth(16)
          .selectedItemHeight(8)
          .color('rgba(255,255,255,0.4)')
          .selectedColor(Color.White)
      )
      .curve(Curve.Linear)
      .onChange((index: number) => {
        this.currentBannerIndex = index;
      })
      .margin({ left: 16, right: 16, top: 8, bottom: 8 })
    }
  }

  /**
   * 处理轮播图点击事件
   */
  private handleBannerClick(banner: BannerModel) {
    switch (banner.link_type) {
      case 'app':
        if (banner.link_value) {
          this.getUIContext().getRouter().pushUrl({
            url: 'pages/AppDetailPage',
            params: { appId: banner.link_value }
          });
        }
        break;
      case 'category':
        if (banner.link_value) {
          this.getUIContext().getRouter().pushUrl({
            url: 'pages/CategoryPage',
            params: { categoryId: banner.link_value, categoryName: banner.title }
          });
        }
        break;
      case 'url':
        if (banner.link_value) {
          // 这里可以打开外部链接或内部页面
          hilog.info(0x0000, 'HomePage', '点击轮播图链接: %{public}s', banner.link_value);
        }
        break;
      default:
        hilog.info(0x0000, 'HomePage', '点击轮播图: %{public}s', banner.title);
        break;
    }
  }

  /**
   * 推荐应用列表
   */
  @Builder
  private RecommendedApps() {
    if (this.recommendedApps && this.recommendedApps.length > 0) {
      Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
        Row() {
          Text('推荐应用')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontWeight(FontWeight.Bold)
            .fontColor($r('sys.color.ohos_id_color_text_primary'))
            .layoutWeight(1)

          Text('查看更多')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.PRIMARY)
            .onClick((): void => {
              const params: AppSearchParams = { page: 1, page_size: 20 };
              this.navigateToAppList('推荐应用', params);
            })
        }
        .width('100%')
        .padding({ left: '16vp', right: '16vp' })

        if (this.deviceUtils.isTablet()) {
          // 平板设备使用网格布局
          Grid() {
            ForEach(this.recommendedApps, (app: AppModel) => {
              GridItem() {
                AppCard({
                  app: app,
                  cardType: 'grid',
                  onAppClick: (app: AppModel): void => this.navigateToAppDetail(app)
                })
              }
            })
          }
          .columnsTemplate('1fr 1fr 1fr')
          .rowsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
          .columnsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
          .padding({ left: '16vp', right: '16vp' })
        } else {
          // 手机设备使用列表布局
          Column() {
            ForEach(this.recommendedApps, (app: AppModel) => {
              AppCard({
                app: app,
                cardType: 'list',
                onAppClick: (app: AppModel): void => this.navigateToAppDetail(app)
              })
                .margin({ left: 16, right: 16, bottom: 8 })
            })
          }
        }
      }
    }
  }

  /**
   * 快速入口
   */
  @Builder
  private QuickActions() {
    Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) }) {
      Column({ space: 8 }) {
        Text('🏆')
          .width(32)
          .height(32)
          .fontColor(Constants.COLORS.PRIMARY)
        Text('排行榜')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
      }
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
      .layoutWeight(1)
      .onClick((): void => {
        const params: AppSearchParams = { sort: 'downloadCount', page: 1, page_size: 20 };
        this.navigateToAppList('应用排行榜', params);
      })

      Column({ space: 8 }) {
        Text('🆕')
          .width(32)
          .height(32)
          .fontColor(Constants.COLORS.SUCCESS)
        Text('最新上架')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
      }
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
      .layoutWeight(1)
      .onClick(() => {
        const params: AppSearchParams = { sort: 'createdAt', page: 1, page_size: 20 };
        this.navigateToAppList('最新上架', params);
      })

      Column({ space: 8 }) {
        Text('🔄')
          .width(32)
          .height(32)
          .fontColor(Constants.COLORS.WARNING)
        Text('最近更新')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
      }
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
      .layoutWeight(1)
      .onClick((): void => {
        const params: AppSearchParams = { sort: 'updatedAt', page: 1, page_size: 20 };
        this.navigateToAppList('最近更新', params);
      })

      Column({ space: 8 }) {
        Text('🆓')
          .width(32)
          .height(32)
          .fontColor(Constants.COLORS.PRIMARY)
        Text('免费应用')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
      }
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
      .layoutWeight(1)
      .onClick(() => {
        const params: AppSearchParams = { page: 1, page_size: 20 };
        this.navigateToAppList('免费应用', params);
      })
    }
    .width('100%')
    .padding({ left: '16vp', right: '16vp', top: '16vp', bottom: '16vp' })
    .backgroundColor($r('sys.color.ohos_id_color_background'))
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .margin({ left: 16, right: 16, top: 8, bottom: 8 })
  }

  build() {
    Column() {
      if (this.loadingState === LoadingState.LOADING) {
        LoadingView({ state: LoadingState.LOADING })
          .layoutWeight(1)
      } else if (this.loadingState === LoadingState.ERROR) {
        if (this.needLogin) {
          // 需要登录的特殊错误状态
          Column({ space: 16 }) {
            Image($r('app.media.ic_empty'))
              .width(64)
              .height(64)
              .objectFit(ImageFit.Contain)
              .fillColor($r('sys.color.ohos_id_color_text_hint'))
            
            Text('请先登录')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
              .fontColor($r('sys.color.ohos_id_color_text_primary'))
              .textAlign(TextAlign.Center)
            
            Text('登录后即可浏览应用商店的精彩内容')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
              .fontColor($r('sys.color.ohos_id_color_text_secondary'))
              .textAlign(TextAlign.Center)
              .maxLines(2)
            
            Button('立即登录')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
              .backgroundColor(Constants.COLORS.PRIMARY)
              .fontColor($r('sys.color.ohos_id_color_background'))
              .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
              .padding({ left: 32, right: 32, top: 12, bottom: 12 })
              .onClick(() => {
                this.navigateToLogin();
              })
          }
          .width('100%')
          .height('100%')
          .justifyContent(FlexAlign.Center)
          .alignItems(HorizontalAlign.Center)
          .layoutWeight(1)
        } else {
          LoadingView({ 
            state: LoadingState.ERROR,
            onRetry: () => this.loadHomeData()
          })
            .layoutWeight(1)
        }
      } else {
        Column() {
          // 顶部搜索栏
          this.TopSearchBar()

          // 内容区域
          Refresh({ refreshing: this.refreshing, offset: 64, friction: 100 }) {
            Scroll() {
              Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) }) {
                // 轮播图
                this.BannerSwiper()

                // 快速入口
                this.QuickActions()

                // 推荐应用
                this.RecommendedApps()

                // 底部间距
                Column()
                  .height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
              }
            }
            .scrollable(ScrollDirection.Vertical)
            .scrollBar(BarState.Auto)
          }
          .onStateChange((refreshStatus: RefreshStatus) => {
            if (refreshStatus === RefreshStatus.Refresh) {
              this.refreshData();
            }
          })
          .layoutWeight(1)
        }
        .backgroundColor($r('sys.color.ohos_id_color_background'))
      }

      // 注释：移除独立的底部导航栏，统一由Index.ets管理导航
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
  }
}

