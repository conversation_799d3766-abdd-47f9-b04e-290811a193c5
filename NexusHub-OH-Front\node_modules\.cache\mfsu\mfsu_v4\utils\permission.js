"use strict";
const ROLE_LEVELS = {
  user: 1,
  developer: 2,
  operator: 3,
  reviewer: 3,
  admin: 5
};
export const hasRoleLevel = (userRole, requiredRole) => {
  return ROLE_LEVELS[userRole] >= ROLE_LEVELS[requiredRole];
};
export const canManageUser = (managerRole, targetRole) => {
  if (managerRole === "admin") {
    return true;
  }
  if (managerRole === "operator") {
    return ["user", "developer"].includes(targetRole);
  }
  return false;
};
export const canEditApp = (userRole, appOwnerId, currentUserId) => {
  if (["admin", "operator"].includes(userRole)) {
    return true;
  }
  if (userRole === "developer") {
    return appOwnerId === currentUserId;
  }
  return false;
};
export const canDeleteApp = (userRole, appOwnerId, currentUserId) => {
  return userRole === "admin";
};
export const canAuditApp = (userRole) => {
  return ["admin", "reviewer"].includes(userRole);
};
export const canViewStatistics = (userRole, dataType = "all") => {
  if (["admin", "operator", "reviewer"].includes(userRole)) {
    return true;
  }
  if (userRole === "developer" && dataType === "own") {
    return true;
  }
  return false;
};
export const getVisibleMenus = (userRole) => {
  const baseMenus = ["account"];
  switch (userRole) {
    case "admin":
      return [
        ...baseMenus,
        "dashboard",
        "statistics",
        "user-management",
        "app",
        "review",
        "settings"
      ];
    case "operator":
      return [
        ...baseMenus,
        "dashboard",
        "statistics",
        "user-management",
        "app",
        "review",
        "settings"
      ];
    case "reviewer":
      return [
        ...baseMenus,
        "dashboard",
        "statistics",
        "user-management",
        "app",
        "review"
      ];
    case "developer":
      return [
        ...baseMenus,
        "dashboard",
        "statistics",
        "app",
        "review"
      ];
    case "user":
    default:
      return baseMenus;
  }
};
export const getRoleDisplayName = (role) => {
  const roleNames = {
    user: "\u666E\u901A\u7528\u6237",
    developer: "\u5F00\u53D1\u8005",
    operator: "\u8FD0\u8425\u4EBA\u5458",
    reviewer: "\u5BA1\u6838\u5458",
    admin: "\u7BA1\u7406\u5458"
  };
  return roleNames[role] || role;
};
export const getRoleColor = (role) => {
  const roleColors = {
    user: "default",
    developer: "blue",
    operator: "orange",
    reviewer: "purple",
    admin: "red"
  };
  return roleColors[role] || "default";
};
export const isAdvancedRole = (role) => {
  return ["admin", "operator", "reviewer"].includes(role);
};
export const getAssignableRoles = (userRole) => {
  switch (userRole) {
    case "admin":
      return ["user", "developer", "operator", "reviewer", "admin"];
    case "operator":
      return ["user", "developer"];
    default:
      return [];
  }
};
