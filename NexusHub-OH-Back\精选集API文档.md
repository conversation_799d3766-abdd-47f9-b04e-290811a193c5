# 精选集模块 API 文档

## 概述

精选集模块提供了管理和展示应用精选集的功能，包括创建、编辑、删除精选集以及管理精选集中的应用。

## 公开接口

### 获取精选集列表

**请求**：
```
GET /api/v1/public/featured-collections
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100
- `status`: 状态过滤，可选值：active（默认）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "编辑精选",
        "description": "编辑团队精心挑选的优质应用",
        "cover_image": "https://example.com/cover1.jpg",
        "app_count": 12,
        "display_order": 1,
        "status": "active",
        "created_at": "2023-09-01T00:00:00Z",
        "updated_at": "2023-09-15T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 5,
      "total_pages": 1
    }
  }
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"active"的精选集
- 按display_order升序排列

### 获取精选集详情

**请求**：
```
GET /api/v1/public/featured-collections/{id}
```

**参数**：
- `id`: 精选集ID（路径参数）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "编辑精选",
    "description": "编辑团队精心挑选的优质应用",
    "cover_image": "https://example.com/cover1.jpg",
    "app_count": 12,
    "display_order": 1,
    "status": "active",
    "created_at": "2023-09-01T00:00:00Z",
    "updated_at": "2023-09-15T00:00:00Z"
  }
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只能获取状态为"active"的精选集详情
- 如果精选集不存在或状态不为"active"，返回404错误

### 获取精选集中的应用列表

**请求**：
```
GET /api/v1/public/featured-collections/{id}/apps
```

**参数**：
- `id`: 精选集ID（路径参数）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "Calculator Pro",
        "package_name": "com.example.calculator",
        "description": "A professional calculator app for OpenHarmony",
        "short_description": "Professional calculator",
        "icon": "https://example.com/app-icon.jpg",
        "category_id": 1,
        "category_name": "Tools",
        "developer_id": 1,
        "developer_name": "John Developer",
        "version": "1.0.0",
        "size": 2048000,
        "download_count": 1000,
        "rating": 4.5,
        "review_count": 200,
        "tags": ["calculator", "math", "tools"],
        "status": "published",
        "published_at": "2023-09-15T00:00:00Z",
        "added_at": "2023-09-20T00:00:00Z",
        "display_order": 1
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 12,
      "total_pages": 1
    }
  }
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"published"的应用
- 按精选集中的display_order升序排列
- 如果精选集不存在或状态不为"active"，返回404错误

## 管理员接口

### 获取精选集列表（管理员）

**请求**：
```
GET /api/v1/admin/featured-collections
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100
- `status`: 状态过滤，可选值：active、inactive、all（默认）
- `keyword`: 搜索关键词，可选

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "编辑精选",
        "description": "编辑团队精心挑选的优质应用",
        "cover_image": "https://example.com/cover1.jpg",
        "app_count": 12,
        "display_order": 1,
        "status": "active",
        "created_at": "2023-09-01T00:00:00Z",
        "updated_at": "2023-09-15T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 8,
      "total_pages": 1
    }
  }
}
```

**说明**：
- 需要管理员权限
- 可以查看所有状态的精选集
- 支持关键词搜索和状态过滤

### 创建精选集

**请求**：
```
POST /api/v1/admin/featured-collections
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "新游戏精选",                    // 必填，2-100字符
  "description": "最新最热门的游戏合集",    // 必填，10-500字符
  "cover_image": "https://example.com/cover.jpg", // 可选，封面图片URL
  "display_order": 1,                   // 可选，显示顺序，默认0
  "status": "active"                    // 可选，状态，默认active
}
```

**响应**：
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 2,
    "name": "新游戏精选",
    "description": "最新最热门的游戏合集",
    "cover_image": "https://example.com/cover.jpg",
    "app_count": 0,
    "display_order": 1,
    "status": "active",
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z"
  }
}
```

### 更新精选集

**请求**：
```
PUT /api/v1/admin/featured-collections/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "更新后的精选集名称",           // 可选，2-100字符
  "description": "更新后的描述",        // 可选，10-500字符
  "cover_image": "https://example.com/new-cover.jpg", // 可选，封面图片URL
  "display_order": 2,                 // 可选，显示顺序
  "status": "inactive"                // 可选，状态
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "name": "更新后的精选集名称",
    "description": "更新后的描述",
    "cover_image": "https://example.com/new-cover.jpg",
    "app_count": 12,
    "display_order": 2,
    "status": "inactive",
    "created_at": "2023-09-01T00:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z"
  }
}
```

### 删除精选集

**请求**：
```
DELETE /api/v1/admin/featured-collections/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

**说明**：
- 删除精选集会同时删除该精选集中的所有应用关联关系
- 删除操作不可逆，请谨慎操作

### 批量删除精选集

**请求**：
```
DELETE /api/v1/admin/featured-collections
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "ids": [1, 2, 3]  // 必填，精选集ID数组
}
```

**响应**：
```json
{
  "code": 200,
  "message": "批量删除成功",
  "data": {
    "deleted_count": 3,
    "failed_ids": []
  }
}
```

### 更新精选集状态

**请求**：
```
PUT /api/v1/admin/featured-collections/{id}/status
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "status": "inactive"  // 必填，状态：active或inactive
}
```

**响应**：
```json
{
  "code": 200,
  "message": "状态更新成功",
  "data": null
}
```

### 更新精选集显示顺序

**请求**：
```
PUT /api/v1/admin/featured-collections/order
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "orders": [
    {"id": 1, "display_order": 1},
    {"id": 2, "display_order": 2},
    {"id": 3, "display_order": 3}
  ]
}
```

**响应**：
```json
{
  "code": 200,
  "message": "顺序更新成功",
  "data": null
}
```

### 获取精选集中的应用列表（管理员）

**请求**：
```
GET /api/v1/admin/featured-collections/{id}/apps
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `id`: 精选集ID（路径参数）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "Calculator Pro",
        "package_name": "com.example.calculator",
        "icon": "https://example.com/app-icon.jpg",
        "category_name": "Tools",
        "developer_name": "John Developer",
        "version": "1.0.0",
        "download_count": 1000,
        "rating": 4.5,
        "status": "published",
        "added_at": "2023-09-20T00:00:00Z",
        "display_order": 1
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 12,
      "total_pages": 1
    }
  }
}
```

### 向精选集添加应用

**请求**：
```
POST /api/v1/admin/featured-collections/{id}/apps
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "app_ids": [1, 2, 3],      // 必填，应用ID数组
  "display_order": 1         // 可选，显示顺序，默认为当前最大值+1
}
```

**响应**：
```json
{
  "code": 200,
  "message": "添加成功",
  "data": {
    "added_count": 3,
    "failed_app_ids": [],
    "duplicate_app_ids": []
  }
}
```

### 从精选集移除应用

**请求**：
```
DELETE /api/v1/admin/featured-collections/{id}/apps
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "app_ids": [1, 2, 3]  // 必填，应用ID数组
}
```

**响应**：
```json
{
  "code": 200,
  "message": "移除成功",
  "data": {
    "removed_count": 3,
    "failed_app_ids": []
  }
}
```

## 错误码

除了通用错误码外，精选集模块还可能返回以下特定错误：

| 状态码 | 错误信息 | 描述 |
|--------|----------|------|
| 400 | 精选集名称已存在 | 创建或更新时名称重复 |
| 400 | 应用已在精选集中 | 添加应用时应用已存在 |
| 400 | 应用不在精选集中 | 移除应用时应用不存在 |
| 404 | 精选集不存在 | 指定的精选集ID不存在 |
| 404 | 应用不存在 | 指定的应用ID不存在 |
| 403 | 权限不足 | 非管理员访问管理员接口 |

## 数据模型

### FeaturedCollection（精选集）

| 字段 | 类型 | 描述 |
|------|------|------|
| id | int | 精选集ID |
| name | string | 精选集名称 |
| description | string | 精选集描述 |
| cover_image | string | 封面图片URL |
| app_count | int | 应用数量 |
| display_order | int | 显示顺序 |
| status | string | 状态（active/inactive） |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### FeaturedCollectionApp（精选集应用关联）

| 字段 | 类型 | 描述 |
|------|------|------|
| id | int | 关联ID |
| collection_id | int | 精选集ID |
| app_id | int | 应用ID |
| display_order | int | 在精选集中的显示顺序 |
| added_at | datetime | 添加时间 |

## 使用示例

### 获取首页精选集

```javascript
// 获取精选集列表
fetch('/api/v1/public/featured-collections?page=1&page_size=10')
  .then(response => response.json())
  .then(data => {
    console.log('精选集列表:', data.data.list);
  });

// 获取精选集中的应用
fetch('/api/v1/public/featured-collections/1/apps?page=1&page_size=20')
  .then(response => response.json())
  .then(data => {
    console.log('精选集应用:', data.data.list);
  });
```

### 管理精选集

```javascript
// 创建精选集
fetch('/api/v1/admin/featured-collections', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    name: '热门游戏',
    description: '最受欢迎的游戏应用',
    status: 'active'
  })
})
.then(response => response.json())
.then(data => {
  console.log('创建成功:', data.data);
});

// 添加应用到精选集
fetch('/api/v1/admin/featured-collections/1/apps', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    app_ids: [1, 2, 3]
  })
})
.then(response => response.json())
.then(data => {
  console.log('添加应用成功:', data.data);
});
```