"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { useState, useEffect } from "react";
import { Badge, List, Spin, Empty, Button, Tabs } from "antd";
import { BellOutlined, LoadingOutlined } from "@ant-design/icons";
import { createStyles } from "antd-style";
import { history } from "@umijs/max";
import { getNotifications, getUnreadCount, markAsRead, markAllAsRead } from "@/services/ant-design-pro/notifications";
import HeaderDropdown from "../HeaderDropdown";
const useStyles = createStyles(({ token }) => ({
  noticeIcon: {
    fontSize: "16px",
    cursor: "pointer",
    color: token.colorTextSecondary,
    "&:hover": {
      color: token.colorPrimary
    }
  },
  noticeDropdown: {
    width: "336px",
    maxHeight: "400px"
  },
  noticeHeader: {
    padding: "12px 16px",
    borderBottom: `1px solid ${token.colorBorderSecondary}`,
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    fontWeight: 500
  },
  noticeList: {
    maxHeight: "300px",
    overflow: "auto"
  },
  noticeItem: {
    padding: "12px 16px",
    cursor: "pointer",
    borderBottom: `1px solid ${token.colorBorderSecondary}`,
    "&:hover": {
      backgroundColor: token.colorBgTextHover
    },
    "&.unread": {
      backgroundColor: token.colorBgContainer,
      "&::before": {
        content: '""',
        position: "absolute",
        left: "8px",
        top: "50%",
        transform: "translateY(-50%)",
        width: "6px",
        height: "6px",
        borderRadius: "50%",
        backgroundColor: token.colorPrimary
      }
    }
  },
  noticeTitle: {
    fontSize: "14px",
    fontWeight: 500,
    marginBottom: "4px",
    lineHeight: "20px"
  },
  noticeDescription: {
    fontSize: "12px",
    color: token.colorTextSecondary,
    lineHeight: "16px",
    marginBottom: "4px"
  },
  noticeTime: {
    fontSize: "12px",
    color: token.colorTextTertiary
  },
  noticeFooter: {
    padding: "8px 16px",
    borderTop: `1px solid ${token.colorBorderSecondary}`,
    textAlign: "center"
  },
  clearButton: {
    fontSize: "12px",
    color: token.colorTextSecondary,
    "&:hover": {
      color: token.colorPrimary
    }
  },
  emptyContainer: {
    padding: "40px 16px",
    textAlign: "center"
  }
}));
const NoticeIcon = ({ className }) => {
  const { styles } = useStyles();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [activeTab, setActiveTab] = useState("all");
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const response = await getNotifications({ page: 1, pageSize: 20 });
      if (response.success) {
        setNotifications(response.data?.list || []);
      }
    } catch (error) {
      console.error("\u83B7\u53D6\u901A\u77E5\u5217\u8868\u5931\u8D25:", error);
    } finally {
      setLoading(false);
    }
  };
  const fetchUnreadCount = async () => {
    const token = localStorage.getItem("token");
    if (!token) {
      setUnreadCount(0);
      return;
    }
    try {
      const response = await getUnreadCount();
      if (response.success) {
        setUnreadCount(response.data?.count || 0);
      }
    } catch (error) {
      if (error?.response?.status === 401) {
        setUnreadCount(0);
        return;
      }
      console.error("\u83B7\u53D6\u672A\u8BFB\u6570\u91CF\u5931\u8D25:", error);
    }
  };
  const handleMarkAsRead = async (id) => {
    try {
      const response = await markAsRead({ id });
      if (response.success) {
        setNotifications(
          (prev) => prev.map(
            (item) => item.id === id ? { ...item, read: true } : item
          )
        );
        setUnreadCount((prev) => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error("\u6807\u8BB0\u5DF2\u8BFB\u5931\u8D25:", error);
    }
  };
  const handleMarkAllAsRead = async () => {
    try {
      const response = await markAllAsRead();
      if (response.success) {
        setNotifications(
          (prev) => prev.map((item) => ({ ...item, read: true }))
        );
        setUnreadCount(0);
      }
    } catch (error) {
      console.error("\u6807\u8BB0\u5168\u90E8\u5DF2\u8BFB\u5931\u8D25:", error);
    }
  };
  const handleNoticeClick = (item) => {
    if (!item.read) {
      handleMarkAsRead(item.id);
    }
    setVisible(false);
    if (item.type === "system" && item.extra?.url) {
      history.push(item.extra.url);
    }
  };
  const handleViewAll = () => {
    setVisible(false);
    history.push("/account/notifications");
  };
  const filteredNotifications = notifications.filter((item) => {
    if (activeTab === "unread") {
      return !item.read;
    }
    return true;
  });
  useEffect(() => {
    console.log("NoticeIcon\u7EC4\u4EF6\u5DF2\u6302\u8F7D");
    const token = localStorage.getItem("token");
    console.log("NoticeIcon\u68C0\u67E5token:", token ? "\u5B58\u5728" : "\u4E0D\u5B58\u5728");
    if (token) {
      const timer = setTimeout(() => {
        console.log("NoticeIcon\u5F00\u59CB\u83B7\u53D6\u672A\u8BFB\u6570\u91CF");
        fetchUnreadCount();
      }, 1e3);
      const interval = setInterval(() => {
        fetchUnreadCount();
      }, 3e4);
      return () => {
        clearTimeout(timer);
        clearInterval(interval);
      };
    }
  }, []);
  useEffect(() => {
    if (visible) {
      fetchNotifications();
    }
  }, [visible]);
  const dropdownContent = /* @__PURE__ */ jsxs("div", { className: styles.noticeDropdown, children: [
    /* @__PURE__ */ jsxs("div", { className: styles.noticeHeader, children: [
      /* @__PURE__ */ jsx("span", { children: "\u901A\u77E5" }),
      unreadCount > 0 && /* @__PURE__ */ jsx(
        Button,
        {
          type: "link",
          size: "small",
          className: styles.clearButton,
          onClick: handleMarkAllAsRead,
          children: "\u5168\u90E8\u5DF2\u8BFB"
        }
      )
    ] }),
    /* @__PURE__ */ jsx(
      Tabs,
      {
        size: "small",
        activeKey: activeTab,
        onChange: setActiveTab,
        items: [
          {
            key: "all",
            label: "\u5168\u90E8"
          },
          {
            key: "unread",
            label: `\u672A\u8BFB ${unreadCount > 0 ? `(${unreadCount})` : ""}`
          }
        ],
        style: { padding: "0 16px" }
      }
    ),
    /* @__PURE__ */ jsx("div", { className: styles.noticeList, children: loading ? /* @__PURE__ */ jsx("div", { style: { textAlign: "center", padding: "20px" }, children: /* @__PURE__ */ jsx(Spin, { indicator: /* @__PURE__ */ jsx(LoadingOutlined, { style: { fontSize: 24 }, spin: true }) }) }) : filteredNotifications.length > 0 ? /* @__PURE__ */ jsx(
      List,
      {
        dataSource: filteredNotifications,
        renderItem: (item) => /* @__PURE__ */ jsxs(
          "div",
          {
            className: `${styles.noticeItem} ${!item.read ? "unread" : ""}`,
            onClick: () => handleNoticeClick(item),
            children: [
              /* @__PURE__ */ jsx("div", { className: styles.noticeTitle, children: item.title }),
              item.description && /* @__PURE__ */ jsx("div", { className: styles.noticeDescription, children: item.description }),
              /* @__PURE__ */ jsx("div", { className: styles.noticeTime, children: item.created_at })
            ]
          },
          item.id
        )
      }
    ) : /* @__PURE__ */ jsx("div", { className: styles.emptyContainer, children: /* @__PURE__ */ jsx(
      Empty,
      {
        image: Empty.PRESENTED_IMAGE_SIMPLE,
        description: activeTab === "unread" ? "\u6682\u65E0\u672A\u8BFB\u901A\u77E5" : "\u6682\u65E0\u901A\u77E5"
      }
    ) }) }),
    filteredNotifications.length > 0 && /* @__PURE__ */ jsx("div", { className: styles.noticeFooter, children: /* @__PURE__ */ jsx(Button, { type: "link", size: "small", onClick: handleViewAll, children: "\u67E5\u770B\u5168\u90E8\u901A\u77E5" }) })
  ] });
  return /* @__PURE__ */ jsx(
    HeaderDropdown,
    {
      menu: {
        items: []
      },
      popupRender: () => dropdownContent,
      onOpenChange: setVisible,
      open: visible,
      trigger: ["click"],
      placement: "bottomRight",
      children: /* @__PURE__ */ jsx(Badge, { count: unreadCount, size: "small", offset: [0, 0], children: /* @__PURE__ */ jsx(BellOutlined, { className: `${styles.noticeIcon} ${className}` }) })
    }
  );
};
export default NoticeIcon;
