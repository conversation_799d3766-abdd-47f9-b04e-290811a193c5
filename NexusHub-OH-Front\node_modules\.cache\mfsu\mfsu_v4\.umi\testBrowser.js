"use strict";
import { jsx } from "react/jsx-runtime";
import React, { useEffect, useState } from "react";
import { ApplyPluginsType } from "umi";
import { renderClient } from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/@umijs+renderer-react@4.4.1_5d37cdc93ae4c557f74cbc834d273583/node_modules/@umijs/renderer-react";
import { createHistory } from "./core/history";
import { createPluginManager } from "./core/plugin";
import { getRoutes } from "./core/route";
import "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/src/global.less";
import "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/src/global.tsx";
import "antd/dist/reset.css";
const publicPath = "/";
const runtimePublicPath = false;
export function TestBrowser(props) {
  const pluginManager = createPluginManager();
  const [context, setContext] = useState(
    void 0
  );
  useEffect(() => {
    const genContext = async () => {
      const { routes, routeComponents } = await getRoutes(pluginManager);
      await pluginManager.applyPlugins({
        key: "patchRoutes",
        type: ApplyPluginsType.event,
        args: {
          routes,
          routeComponents
        }
      });
      const contextOpts = pluginManager.applyPlugins({
        key: "modifyContextOpts",
        type: ApplyPluginsType.modify,
        initialValue: {}
      });
      const basename = contextOpts.basename || "/";
      const history = createHistory({
        type: "memory",
        basename
      });
      const context2 = {
        routes,
        routeComponents,
        pluginManager,
        rootElement: contextOpts.rootElement || document.getElementById("root"),
        publicPath,
        runtimePublicPath,
        history,
        basename,
        components: true
      };
      const modifiedContext = pluginManager.applyPlugins({
        key: "modifyClientRenderOpts",
        type: ApplyPluginsType.modify,
        initialValue: context2
      });
      return modifiedContext;
    };
    genContext().then((context2) => {
      setContext(context2);
      if (props.location) {
        context2?.history?.push(props.location);
      }
      if (props.historyRef) {
        props.historyRef.current = context2?.history;
      }
    });
  }, []);
  if (context === void 0) {
    return /* @__PURE__ */ jsx("div", { id: "loading" });
  }
  const Children = renderClient(context);
  return /* @__PURE__ */ jsx(React.Fragment, { children: /* @__PURE__ */ jsx(Children, {}) });
}
