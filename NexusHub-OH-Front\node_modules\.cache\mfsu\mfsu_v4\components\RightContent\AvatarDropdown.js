"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { postUsersLogout as outLogin } from "@/services/ant-design-pro/yonghu";
import { LogoutOutlined, SettingOutlined, UserOutlined } from "@ant-design/icons";
import { history, useModel } from "@umijs/max";
import { Spin, Avatar } from "antd";
import { createStyles } from "antd-style";
import { stringify } from "querystring";
import { useCallback } from "react";
import { flushSync } from "react-dom";
import HeaderDropdown from "../HeaderDropdown";
export const AvatarName = () => {
  const { initialState } = useModel("@@initialState");
  const { currentUser } = initialState || {};
  return /* @__PURE__ */ jsx("span", { className: "anticon", children: currentUser?.username });
};
const useStyles = createStyles(({ token }) => {
  return {
    action: {
      display: "flex",
      height: "48px",
      marginLeft: "auto",
      overflow: "hidden",
      alignItems: "center",
      padding: "0 8px",
      cursor: "pointer",
      borderRadius: token.borderRadius,
      "&:hover": {
        backgroundColor: token.colorBgTextHover
      }
    }
  };
});
export const AvatarDropdown = ({ menu, children }) => {
  const loginOut = async () => {
    await outLogin();
    const { search, pathname } = window.location;
    const urlParams = new URL(window.location.href).searchParams;
    const redirect = urlParams.get("redirect");
    if (window.location.pathname !== "/user/login" && !redirect) {
      history.replace({
        pathname: "/user/login",
        search: stringify({
          redirect: pathname + search
        })
      });
    }
  };
  const { styles } = useStyles();
  const { initialState, setInitialState } = useModel("@@initialState");
  const onMenuClick = useCallback(
    (event) => {
      const { key } = event;
      if (key === "logout") {
        flushSync(() => {
          setInitialState((s) => ({ ...s, currentUser: void 0 }));
        });
        loginOut();
        return;
      }
      history.push(`/account/${key}`);
    },
    [setInitialState]
  );
  const loading = /* @__PURE__ */ jsx("span", { className: styles.action, children: /* @__PURE__ */ jsx(
    Spin,
    {
      size: "small",
      style: {
        marginLeft: 8,
        marginRight: 8
      }
    }
  ) });
  if (!initialState) {
    return loading;
  }
  const { currentUser } = initialState;
  if (!currentUser || !currentUser.username) {
    return loading;
  }
  const menuItems = [
    ...menu ? [
      {
        key: "settings",
        icon: /* @__PURE__ */ jsx(SettingOutlined, {}),
        label: "\u4E2A\u4EBA\u8BBE\u7F6E"
      },
      {
        type: "divider"
      }
    ] : [],
    {
      key: "logout",
      icon: /* @__PURE__ */ jsx(LogoutOutlined, {}),
      label: "\u9000\u51FA\u767B\u5F55"
    }
  ];
  return /* @__PURE__ */ jsx(
    HeaderDropdown,
    {
      menu: {
        selectedKeys: [],
        onClick: onMenuClick,
        items: menuItems
      },
      children: /* @__PURE__ */ jsx("span", { className: styles.action, children: children || /* @__PURE__ */ jsxs("span", { className: "ant-pro-avatar", children: [
        /* @__PURE__ */ jsx(Avatar, { size: "small", icon: /* @__PURE__ */ jsx(UserOutlined, {}), src: currentUser.avatar, alt: "avatar" }),
        /* @__PURE__ */ jsx("span", { className: "anticon", children: currentUser.username })
      ] }) })
    }
  );
};
