if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ProfilePage_Params {
    userInfo?: UserModel | null;
    loadingState?: LoadingState;
    isLoggedIn?: boolean;
    downloadedAppsCount?: number;
    favoriteAppsCount?: number;
    reviewsCount?: number;
    deviceUtils?;
    apiService?;
}
import type { UserModel } from '../models/User';
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { LoadingView, LoadingState } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import preferences from "@ohos:data.preferences";
import hilog from "@ohos:hilog";
export class ProfilePage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userInfo = new ObservedPropertyObjectPU(null, this, "userInfo");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.LOADING, this, "loadingState");
        this.__isLoggedIn = new ObservedPropertySimplePU(false, this, "isLoggedIn");
        this.__downloadedAppsCount = new ObservedPropertySimplePU(0, this, "downloadedAppsCount");
        this.__favoriteAppsCount = new ObservedPropertySimplePU(0, this, "favoriteAppsCount");
        this.__reviewsCount = new ObservedPropertySimplePU(0, this, "reviewsCount");
        this.deviceUtils = DeviceUtils.getInstance();
        this.apiService = ApiService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ProfilePage_Params) {
        if (params.userInfo !== undefined) {
            this.userInfo = params.userInfo;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.isLoggedIn !== undefined) {
            this.isLoggedIn = params.isLoggedIn;
        }
        if (params.downloadedAppsCount !== undefined) {
            this.downloadedAppsCount = params.downloadedAppsCount;
        }
        if (params.favoriteAppsCount !== undefined) {
            this.favoriteAppsCount = params.favoriteAppsCount;
        }
        if (params.reviewsCount !== undefined) {
            this.reviewsCount = params.reviewsCount;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
    }
    updateStateVars(params: ProfilePage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoggedIn.purgeDependencyOnElmtId(rmElmtId);
        this.__downloadedAppsCount.purgeDependencyOnElmtId(rmElmtId);
        this.__favoriteAppsCount.purgeDependencyOnElmtId(rmElmtId);
        this.__reviewsCount.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userInfo.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__isLoggedIn.aboutToBeDeleted();
        this.__downloadedAppsCount.aboutToBeDeleted();
        this.__favoriteAppsCount.aboutToBeDeleted();
        this.__reviewsCount.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userInfo: ObservedPropertyObjectPU<UserModel | null>;
    get userInfo() {
        return this.__userInfo.get();
    }
    set userInfo(newValue: UserModel | null) {
        this.__userInfo.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __isLoggedIn: ObservedPropertySimplePU<boolean>;
    get isLoggedIn() {
        return this.__isLoggedIn.get();
    }
    set isLoggedIn(newValue: boolean) {
        this.__isLoggedIn.set(newValue);
    }
    private __downloadedAppsCount: ObservedPropertySimplePU<number>;
    get downloadedAppsCount() {
        return this.__downloadedAppsCount.get();
    }
    set downloadedAppsCount(newValue: number) {
        this.__downloadedAppsCount.set(newValue);
    }
    private __favoriteAppsCount: ObservedPropertySimplePU<number>;
    get favoriteAppsCount() {
        return this.__favoriteAppsCount.get();
    }
    set favoriteAppsCount(newValue: number) {
        this.__favoriteAppsCount.set(newValue);
    }
    private __reviewsCount: ObservedPropertySimplePU<number>;
    get reviewsCount() {
        return this.__reviewsCount.get();
    }
    set reviewsCount(newValue: number) {
        this.__reviewsCount.set(newValue);
    }
    private deviceUtils;
    private apiService;
    aboutToAppear() {
        this.checkLoginStatus();
    }
    /**
     * 页面显示时的回调
     */
    onPageShow() {
        this.checkLoginStatus();
    }
    /**
     * 检查登录状态
     */
    private async checkLoginStatus(): Promise<void> {
        try {
            const context = this.getUIContext().getHostContext();
            const options: preferences.Options = { name: 'user_data' };
            const dataPreferences = preferences.getPreferencesSync(context, options);
            const token = dataPreferences.getSync('token', '') as string;
            if (token) {
                this.isLoggedIn = true;
                await this.loadUserProfile();
            }
            else {
                this.isLoggedIn = false;
                this.loadingState = LoadingState.SUCCESS;
            }
        }
        catch (error) {
            hilog.error(0x0000, 'ProfilePage', '检查登录状态失败: %{public}s', JSON.stringify(error));
            this.isLoggedIn = false;
            this.loadingState = LoadingState.SUCCESS;
        }
    }
    /**
     * 加载用户资料
     */
    private async loadUserProfile() {
        try {
            this.loadingState = LoadingState.LOADING;
            const response = await this.apiService.getUserProfile();
            if (response.code === 200 && response.data) {
                this.userInfo = response.data;
                await this.loadUserStats();
                this.loadingState = LoadingState.SUCCESS;
            }
            else {
                this.loadingState = LoadingState.ERROR;
            }
        }
        catch (error) {
            hilog.error(0x0000, 'ProfilePage', '加载用户资料失败: %{public}s', JSON.stringify(error));
            this.loadingState = LoadingState.ERROR;
        }
    }
    /**
     * 加载用户统计数据
     */
    private async loadUserStats() {
        try {
            // 这里可以调用相应的API获取用户统计数据
            // 暂时使用模拟数据
            this.downloadedAppsCount = 25;
            this.favoriteAppsCount = 12;
            this.reviewsCount = 8;
        }
        catch (error) {
            hilog.error(0x0000, 'ProfilePage', '加载用户统计数据失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 登出
     */
    private logout(): void {
        try {
            const context = this.getUIContext().getHostContext();
            const options: preferences.Options = { name: 'user_data' };
            const dataPreferences = preferences.getPreferencesSync(context, options);
            dataPreferences.clearSync();
            dataPreferences.flush();
            this.isLoggedIn = false;
            this.userInfo = null;
        }
        catch (error) {
            hilog.error(0x0000, 'ProfilePage', '登出失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 跳转到登录页面
     */
    private navigateToLogin() {
        this.getUIContext().getRouter().pushUrl({ url: 'pages/LoginPage' });
    }
    /**
     * 跳转到设置页面
     */
    private navigateToSettings() {
        this.getUIContext().getRouter().pushUrl({ url: 'pages/SettingsPage' });
    }
    /**
     * 跳转到我的应用页面
     */
    private navigateToMyApps() {
        this.getUIContext().getRouter().pushUrl({ url: 'pages/MyAppsPage' });
    }
    /**
     * 跳转到我的收藏页面
     */
    private navigateToFavorites() {
        this.getUIContext().getRouter().pushUrl({ url: 'pages/FavoritesPage' });
    }
    /**
     * 跳转到我的评论页面
     */
    private navigateToMyReviews() {
        this.getUIContext().getRouter().pushUrl({ url: 'pages/MyReviewsPage' });
    }
    /**
     * 用户头像和信息
     */
    private UserHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoggedIn && this.userInfo) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) });
                        Row.width('100%');
                        Row.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE));
                        Row.alignItems(VerticalAlign.Center);
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 用户头像
                        Image.create(this.userInfo.avatar || Constants.PLACEHOLDER_IMAGE);
                        // 用户头像
                        Image.width(this.deviceUtils.isTablet() ? 80 : 64);
                        // 用户头像
                        Image.height(this.deviceUtils.isTablet() ? 80 : 64);
                        // 用户头像
                        Image.borderRadius((this.deviceUtils.isTablet() ? 80 : 64) / 2);
                        // 用户头像
                        Image.objectFit(ImageFit.Cover);
                        // 用户头像
                        Image.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
                    }, Image);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 用户信息
                        Column.create({ space: 4 });
                        // 用户信息
                        Column.alignItems(HorizontalAlign.Start);
                        // 用户信息
                        Column.layoutWeight(1);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.userInfo.username || this.userInfo.email);
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontColor(Constants.COLORS.WHITE);
                        Text.maxLines(1);
                        Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.userInfo.username) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create(this.userInfo.username);
                                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                                    Text.fontColor(Constants.COLORS.WHITE);
                                    Text.opacity(0.9);
                                    Text.maxLines(1);
                                    Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                                }, Text);
                                Text.pop();
                            });
                        }
                        // 用户等级或状态
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 用户等级或状态
                        Row.create({ space: 8 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('普通用户');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(Constants.COLORS.WHITE);
                        Text.backgroundColor({ "id": 16777232, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Text.padding({ left: '8vp', right: '8vp', top: '2vp', bottom: '2vp' });
                        Text.borderRadius(Constants.BORDER_RADIUS.SMALL);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.userInfo.verify_status === 'verified') {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Row.create({ space: 4 });
                                    Row.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                                    Row.padding({ left: '6vp', right: '6vp', top: '2vp', bottom: '2vp' });
                                    Row.borderRadius(Constants.BORDER_RADIUS.SMALL);
                                }, Row);
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('✅');
                                    Text.width(12);
                                    Text.height(12);
                                    Text.fontColor(Constants.COLORS.SUCCESS);
                                }, Text);
                                Text.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('已认证');
                                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                                    Text.fontColor(Constants.COLORS.SUCCESS);
                                }, Text);
                                Text.pop();
                                Row.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    // 用户等级或状态
                    Row.pop();
                    // 用户信息
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 编辑按钮
                        Text.create('✏️');
                        // 编辑按钮
                        Text.width(20);
                        // 编辑按钮
                        Text.height(20);
                        // 编辑按钮
                        Text.fontColor(Constants.COLORS.WHITE);
                        // 编辑按钮
                        Text.onClick(() => {
                            this.getUIContext().getRouter().pushUrl({ url: 'pages/EditProfilePage' });
                        });
                    }, Text);
                    // 编辑按钮
                    Text.pop();
                    Row.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 未登录状态
                        Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) });
                        // 未登录状态
                        Column.width('100%');
                        // 未登录状态
                        Column.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE));
                        // 未登录状态
                        Column.justifyContent(FlexAlign.Center);
                        // 未登录状态
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('👤');
                        Text.width(this.deviceUtils.isTablet() ? 80 : 64);
                        Text.height(this.deviceUtils.isTablet() ? 80 : 64);
                        Text.fontColor(Constants.COLORS.WHITE);
                        Text.opacity(0.8);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('登录后享受更多功能');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.WHITE);
                        Text.opacity(0.9);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('立即登录');
                        Button.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Button.fontColor(Constants.COLORS.PRIMARY);
                        Button.backgroundColor(Constants.COLORS.WHITE);
                        Button.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                        Button.padding({ left: '24vp', right: '24vp', top: '8vp', bottom: '8vp' });
                        Button.onClick(() => this.navigateToLogin());
                    }, Button);
                    Button.pop();
                    // 未登录状态
                    Column.pop();
                });
            }
        }, If);
        If.pop();
    }
    /**
     * 用户统计数据
     */
    private UserStats(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoggedIn) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) });
                        Row.width('100%');
                        Row.justifyContent(FlexAlign.SpaceAround);
                        Row.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
                        Row.backgroundColor(Constants.COLORS.WHITE);
                        Row.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                        Row.margin({ left: 16, right: 16, top: -20 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: 4 });
                        Column.alignItems(HorizontalAlign.Center);
                        Column.onClick(() => this.navigateToMyApps());
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.downloadedAppsCount.toString());
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontColor(Constants.COLORS.PRIMARY);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('已下载');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                    }, Text);
                    Text.pop();
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: 4 });
                        Column.alignItems(HorizontalAlign.Center);
                        Column.onClick(() => this.navigateToFavorites());
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.favoriteAppsCount.toString());
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontColor(Constants.COLORS.ERROR);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('收藏');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                    }, Text);
                    Text.pop();
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: 4 });
                        Column.alignItems(HorizontalAlign.Center);
                        Column.onClick(() => this.navigateToMyReviews());
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.reviewsCount.toString());
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontColor(Constants.COLORS.SUCCESS);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('评论');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                    }, Text);
                    Text.pop();
                    Column.pop();
                    Row.pop();
                });
            }
            else /**
             * 菜单项
             */ {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    /**
     * 菜单项
     */
    private MenuItem(icon: Resource | string, title: string, subtitle?: string, onClick?: () => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) });
            Row.width('100%');
            Row.height(this.deviceUtils.isTablet() ? 64 : 56);
            Row.padding({ left: '16vp', right: '16vp' });
            Row.onClick(() => onClick?.());
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (typeof icon === 'string') {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(icon);
                        Text.fontSize(20);
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create(icon);
                        Image.width(24);
                        Image.height(24);
                        Image.fillColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                    }, Image);
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 2 });
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.fontWeight(FontWeight.Medium);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (subtitle) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(subtitle);
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor({ "id": 125829240, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('▶️');
            Text.width(16);
            Text.height(16);
            Text.fontColor({ "id": 125829240, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Row.pop();
    }
    /**
     * 功能菜单
     */
    private FunctionMenu(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.backgroundColor(Constants.COLORS.WHITE);
            Column.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Column.margin({ left: 16, right: 16, top: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoggedIn) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.MenuItem.bind(this)('📥', '我的下载', '管理已下载的应用', (): void => this.navigateToMyApps());
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color({ "id": 125829159, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Divider.margin({ left: 56 });
                    }, Divider);
                    this.MenuItem.bind(this)('❤️', '我的收藏', '查看收藏的应用', (): void => this.navigateToFavorites());
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color({ "id": 125829159, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Divider.margin({ left: 56 });
                    }, Divider);
                    this.MenuItem.bind(this)('💬', '我的评论', '查看发表的评论', (): void => this.navigateToMyReviews());
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                        Divider.margin({ left: 56 });
                    }, Divider);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.MenuItem.bind(this)({ "id": 16777259, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, '浏览历史', '查看最近浏览的应用', (): void => {
            this.getUIContext().getRouter().pushUrl({ url: 'pages/HistoryPage' });
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.color(Constants.COLORS.BORDER);
            Divider.margin({ left: 56 });
        }, Divider);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoggedIn) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.MenuItem.bind(this)('🔔', '通知中心', '查看系统通知和消息', (): void => {
                        this.getUIContext().getRouter().pushUrl({ url: 'pages/NotificationPage' });
                    });
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                        Divider.margin({ left: 56 });
                    }, Divider);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.MenuItem.bind(this)('⚙️', '设置', '应用设置和偏好', (): void => this.navigateToSettings());
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.color(Constants.COLORS.BORDER);
            Divider.margin({ left: 56 });
        }, Divider);
        this.MenuItem.bind(this)('📊', '系统状态', '查看系统健康状态', (): void => {
            this.getUIContext().getRouter().pushUrl({ url: 'pages/SystemStatusPage' });
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.color(Constants.COLORS.BORDER);
            Divider.margin({ left: 56 });
        }, Divider);
        this.MenuItem.bind(this)('❓', '帮助与反馈', '获取帮助或提供反馈', (): void => {
            this.getUIContext().getRouter().pushUrl({ url: 'pages/HelpPage' });
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.color(Constants.COLORS.BORDER);
            Divider.margin({ left: 56 });
        }, Divider);
        this.MenuItem.bind(this)('ℹ️', '关于我们', '了解应用信息', (): void => {
            this.getUIContext().getRouter().pushUrl({ url: 'pages/AboutPage' });
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoggedIn) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                        Divider.margin({ left: 56 });
                    }, Divider);
                    this.MenuItem.bind(this)('🚪', '退出登录', '', (): void => this.logout());
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, { state: LoadingState.LOADING }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/ProfilePage.ets", line: 407, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.LOADING
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.LOADING
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.ERROR,
                                    onRetry: (): Promise<void> => this.loadUserProfile()
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/ProfilePage.ets", line: 410, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.ERROR,
                                        onRetry: (): Promise<void> => this.loadUserProfile()
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.ERROR
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Scroll.create();
                        Scroll.scrollable(ScrollDirection.Vertical);
                        Scroll.scrollBar(BarState.Auto);
                        Scroll.layoutWeight(1);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 顶部背景和用户信息
                        Column.create();
                        // 顶部背景和用户信息
                        Column.width('100%');
                        // 顶部背景和用户信息
                        Column.linearGradient({
                            direction: GradientDirection.Bottom,
                            colors: [[{ "id": 125829186, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, 0], [{ "id": 125829186, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, 1]]
                        });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 状态栏占位
                        Column.create();
                        // 状态栏占位
                        Column.height(44);
                    }, Column);
                    // 状态栏占位
                    Column.pop();
                    // 用户头像和信息
                    this.UserHeader.bind(this)();
                    // 顶部背景和用户信息
                    Column.pop();
                    // 用户统计数据
                    this.UserStats.bind(this)();
                    // 功能菜单
                    this.FunctionMenu.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 底部间距
                        Column.create();
                        // 底部间距
                        Column.height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE));
                    }, Column);
                    // 底部间距
                    Column.pop();
                    Column.pop();
                    Scroll.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "ProfilePage";
    }
}
registerNamedRoute(() => new ProfilePage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/ProfilePage", pageFullPath: "entry/src/main/ets/pages/ProfilePage", integratedHsp: "false", moduleType: "followWithHap" });
