"use strict";
export const logtoConfig = {
  // Logto 服务端点
  endpoint: process.env.REACT_APP_LOGTO_ENDPOINT || "http://localhost:3001",
  // 应用 ID
  appId: process.env.REACT_APP_LOGTO_APP_ID || "your-app-id",
  // 重定向 URI
  redirectUri: `${window.location.origin}/callback`,
  // 登出后重定向 URI
  postLogoutRedirectUri: `${window.location.origin}`,
  // 请求的资源
  resources: [
    process.env.REACT_APP_API_RESOURCE || "http://localhost:8080"
  ],
  // 请求的权限范围
  scopes: [
    "openid",
    "profile",
    "email",
    "offline_access"
  ]
};
