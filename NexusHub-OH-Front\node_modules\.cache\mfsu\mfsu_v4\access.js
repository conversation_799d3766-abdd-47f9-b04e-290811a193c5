"use strict";
const ROLE_PERMISSIONS = {
  // 普通用户权限
  user: {
    // 基础权限
    canViewDashboard: true,
    // 允许访问仪表盘工作台
    canViewAccount: true,
    // 应用相关
    canViewApp: false,
    canCreateApp: false,
    canEditApp: false,
    canDeleteApp: false,
    // 用户管理
    canViewUserList: false,
    canEditUser: false,
    canDeleteUser: false,
    // 审核相关
    canViewAudit: false,
    canAuditApp: false,
    // 统计相关
    canViewStatistics: false,
    // 评论管理
    canViewReview: false,
    canManageReview: false,
    // 系统设置
    canViewSettings: false,
    canManageSettings: false,
    // 权限管理
    canViewPermission: false,
    canManageRole: false,
    canManagePermission: false,
    // 开发者相关
    canViewDeveloper: true,
    // 允许访问开发者认证页面
    canApplyDeveloper: true
    // 允许申请开发者认证
  },
  // 开发者权限
  developer: {
    // 基础权限
    canViewDashboard: true,
    canViewAccount: true,
    // 应用相关
    canViewApp: true,
    // 允许开发者查看应用详情页面
    canCreateApp: true,
    canEditApp: true,
    // 只能编辑自己的应用
    canDeleteApp: true,
    // 允许开发者删除自己创建的应用
    // 用户管理
    canViewUserList: false,
    canEditUser: false,
    canDeleteUser: false,
    // 审核相关
    canViewAudit: false,
    canAuditApp: false,
    // 统计相关
    canViewStatistics: true,
    // 只能查看自己应用的统计
    // 评论管理
    canViewReview: true,
    // 只能查看自己应用的评论
    canManageReview: false,
    // 系统设置
    canViewSettings: false,
    canManageSettings: false,
    // 权限管理
    canViewPermission: false,
    canManageRole: false,
    canManagePermission: false,
    // 开发者相关
    canViewDeveloper: true,
    canApplyDeveloper: false
    // 已经是开发者，不需要再申请
  },
  // 运营人员权限
  operator: {
    // 基础权限
    canViewDashboard: true,
    canViewAccount: true,
    // 应用相关
    canViewApp: true,
    canCreateApp: false,
    canEditApp: true,
    // 可以编辑应用信息
    canDeleteApp: false,
    // 用户管理
    canViewUserList: true,
    canEditUser: true,
    canDeleteUser: false,
    // 审核相关
    canViewAudit: false,
    canAuditApp: false,
    // 统计相关
    canViewStatistics: true,
    // 评论管理
    canViewReview: true,
    canManageReview: true,
    // 系统设置
    canViewSettings: true,
    canManageSettings: true,
    // 权限管理
    canViewPermission: false,
    canManageRole: false,
    canManagePermission: false,
    // 开发者相关
    canViewDeveloper: false,
    // 管理员无需访问开发者认证界面
    canApplyDeveloper: false
    // 管理员无需申请开发者认证
  },
  // 审核员权限
  reviewer: {
    // 基础权限
    canViewDashboard: true,
    canViewAccount: true,
    // 应用相关
    canViewApp: true,
    canCreateApp: false,
    canEditApp: false,
    canDeleteApp: false,
    // 用户管理
    canViewUserList: true,
    canEditUser: false,
    canDeleteUser: false,
    // 审核相关
    canViewAudit: true,
    canAuditApp: true,
    // 统计相关
    canViewStatistics: true,
    // 评论管理
    canViewReview: true,
    canManageReview: true,
    // 系统设置
    canViewSettings: false,
    canManageSettings: false,
    // 权限管理
    canViewPermission: false,
    canManageRole: false,
    canManagePermission: false,
    // 开发者相关
    canViewDeveloper: true,
    canApplyDeveloper: true
  },
  // 管理员权限
  admin: {
    // 基础权限
    canViewDashboard: true,
    canViewAccount: true,
    // 应用相关
    canViewApp: true,
    canCreateApp: true,
    canEditApp: true,
    canDeleteApp: true,
    // 用户管理
    canViewUserList: true,
    canEditUser: true,
    canDeleteUser: true,
    // 审核相关
    canViewAudit: true,
    canAuditApp: true,
    // 统计相关
    canViewStatistics: true,
    // 评论管理
    canViewReview: true,
    canManageReview: true,
    // 系统设置
    canViewSettings: true,
    canManageSettings: true,
    // 权限管理
    canViewPermission: true,
    canManageRole: true,
    canManagePermission: true,
    // 开发者相关
    canViewDeveloper: false,
    canApplyDeveloper: false
  }
};
function getUserPermissions(role) {
  return ROLE_PERMISSIONS[role] || ROLE_PERMISSIONS.user;
}
function hasPermission(role, permission) {
  const permissions = getUserPermissions(role);
  return permissions[permission] || false;
}
function hasAnyPermission(role, permissions) {
  return permissions.some((permission) => hasPermission(role, permission));
}
function hasAllPermissions(role, permissions) {
  return permissions.every((permission) => hasPermission(role, permission));
}
export default function access(initialState) {
  const { currentUser } = initialState ?? {};
  const userRole = currentUser?.role || "user";
  const permissions = getUserPermissions(userRole);
  return {
    // 兼容旧版本
    canAdmin: userRole === "admin",
    // 角色检查
    isUser: userRole === "user",
    isDeveloper: userRole === "developer",
    isOperator: userRole === "operator",
    isReviewer: userRole === "reviewer",
    isAdmin: userRole === "admin",
    // 基础权限
    canViewDashboard: permissions.canViewDashboard,
    canViewAccount: permissions.canViewAccount,
    // 应用相关权限
    canViewApp: permissions.canViewApp,
    canCreateApp: permissions.canCreateApp,
    canEditApp: permissions.canEditApp,
    canDeleteApp: permissions.canDeleteApp,
    // 用户管理权限
    canViewUserList: permissions.canViewUserList,
    canEditUser: permissions.canEditUser,
    canDeleteUser: permissions.canDeleteUser,
    // 审核相关权限
    canViewAudit: permissions.canViewAudit,
    canAuditApp: permissions.canAuditApp,
    // 统计相关权限
    canViewStatistics: permissions.canViewStatistics,
    // 评论管理权限
    canViewReview: permissions.canViewReview,
    canManageReview: permissions.canManageReview,
    // 系统设置权限
    canViewSettings: permissions.canViewSettings,
    canManageSettings: permissions.canManageSettings,
    // 权限管理权限
    canViewPermission: permissions.canViewPermission,
    canManageRole: permissions.canManageRole,
    canManagePermission: permissions.canManagePermission,
    // 开发者相关权限
    canViewDeveloper: permissions.canViewDeveloper,
    canApplyDeveloper: permissions.canApplyDeveloper,
    // 工具函数
    hasPermission: (permission) => hasPermission(userRole, permission),
    hasAnyPermission: (permissions2) => hasAnyPermission(userRole, permissions2),
    hasAllPermissions: (permissions2) => hasAllPermissions(userRole, permissions2),
    // 获取当前用户角色
    getCurrentRole: () => userRole,
    // 获取所有权限
    getAllPermissions: () => permissions
  };
}
export { getUserPermissions, hasPermission, hasAnyPermission, hasAllPermissions, ROLE_PERMISSIONS };
