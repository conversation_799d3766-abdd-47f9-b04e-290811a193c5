"use strict";
import { request } from "@umijs/max";
export async function register(params) {
  return request("/users/register", {
    method: "POST",
    data: params
  });
}
export async function login(params) {
  return request("/users/login", {
    method: "POST",
    data: params
  });
}
export async function emailLogin(params) {
  return request("/users/email-login", {
    method: "POST",
    data: params
  });
}
export async function sendEmailVerifyCode(email) {
  return request("/users/email/verify/send", {
    method: "POST",
    data: { email }
  });
}
export async function verifyEmail(params) {
  return request("/users/email/verify", {
    method: "POST",
    data: params
  });
}
export async function sendResetPasswordCode(email) {
  return request("/users/password/reset/send", {
    method: "POST",
    data: { email }
  });
}
export async function resetPassword(params) {
  return request("/users/password/reset", {
    method: "POST",
    data: params
  });
}
export async function sendLoginCaptcha(email) {
  return request("/users/login/captcha/send", {
    method: "POST",
    data: { email }
  });
}
export async function loginWithCaptcha(params) {
  return request("/users/login/captcha", {
    method: "POST",
    data: params
  });
}
export async function getUserProfile() {
  return request("/users/profile", {
    method: "GET"
  });
}
export async function updateUserProfile(params) {
  return request("/users/profile", {
    method: "PUT",
    data: params
  });
}
export async function changePassword(params) {
  return request("/users/profile", {
    method: "PUT",
    data: params
  });
}
export async function getUploadToken(params) {
  return request("/upload/token", {
    method: "GET",
    params
  });
}
export async function applyDeveloper(params) {
  return request("/developers/verify", {
    method: "POST",
    data: {
      developer_name: params.companyName,
      company_name: params.companyName,
      website: params.website,
      description: params.description,
      contact_email: params.contactEmail,
      contact_phone: params.contactPhone,
      developer_address: "",
      identity_card: ""
    }
  });
}
export async function getUserDetail(id) {
  return request(`/admin/users/${id}`, {
    method: "GET"
  });
}
export async function getUserLoginRecords(userId, params) {
  return request(`/admin/users/${userId}/login-records`, {
    method: "GET",
    params
  });
}
export async function getUserAppRecords(userId, params) {
  return request(`/admin/users/${userId}/app-records`, {
    method: "GET",
    params
  });
}
export async function updateUserRole(userId, role) {
  return request(`/admin/users/${userId}/role`, {
    method: "PUT",
    data: { role }
  });
}
export async function createUser(params) {
  return request("/admin/users", {
    method: "POST",
    data: params
  });
}
export async function toggleUserLockStatus(userId, locked) {
  return request(`/admin/users/${userId}/lock`, {
    method: "PUT",
    data: { locked }
  });
}
