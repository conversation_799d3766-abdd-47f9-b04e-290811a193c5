"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import isEqual from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/fast-deep-equal@3.1.3/node_modules/fast-deep-equal/index.js";
import React, { useContext, useEffect, useMemo, useRef, useState } from "react";
const Context = React.createContext(null);
class Dispatcher {
  callbacks = {};
  data = {};
  update = (namespace) => {
    if (this.callbacks[namespace]) {
      this.callbacks[namespace].forEach((cb) => {
        try {
          const data = this.data[namespace];
          cb(data);
        } catch (e) {
          cb(void 0);
        }
      });
    }
  };
}
function Executor(props) {
  const { hook, onUpdate, namespace } = props;
  const updateRef = useRef(onUpdate);
  const initialLoad = useRef(false);
  let data;
  try {
    data = hook();
  } catch (e) {
    console.error(
      `plugin-model: Invoking '${namespace || "unknown"}' model failed:`,
      e
    );
  }
  useMemo(() => {
    updateRef.current(data);
  }, []);
  useEffect(() => {
    if (initialLoad.current) {
      updateRef.current(data);
    } else {
      initialLoad.current = true;
    }
  });
  return null;
}
const dispatcher = new Dispatcher();
export function Provider(props) {
  return /* @__PURE__ */ jsxs(Context.Provider, { value: { dispatcher }, children: [
    Object.keys(props.models).map((namespace) => {
      return /* @__PURE__ */ jsx(
        Executor,
        {
          hook: props.models[namespace],
          namespace,
          onUpdate: (val) => {
            dispatcher.data[namespace] = val;
            dispatcher.update(namespace);
          }
        },
        namespace
      );
    }),
    props.children
  ] });
}
export function useModel(namespace, selector) {
  const { dispatcher: dispatcher2 } = useContext(Context);
  const selectorRef = useRef(selector);
  selectorRef.current = selector;
  const [state, setState] = useState(
    () => selectorRef.current ? selectorRef.current(dispatcher2.data[namespace]) : dispatcher2.data[namespace]
  );
  const stateRef = useRef(state);
  stateRef.current = state;
  const isMount = useRef(false);
  useEffect(() => {
    isMount.current = true;
    return () => {
      isMount.current = false;
    };
  }, []);
  useEffect(() => {
    const handler = (data) => {
      if (!isMount.current) {
        setTimeout(() => {
          dispatcher2.data[namespace] = data;
          dispatcher2.update(namespace);
        });
      } else {
        const currentState = selectorRef.current ? selectorRef.current(data) : data;
        const previousState = stateRef.current;
        if (!isEqual(currentState, previousState)) {
          stateRef.current = currentState;
          setState(currentState);
        }
      }
    };
    dispatcher2.callbacks[namespace] ||= /* @__PURE__ */ new Set();
    dispatcher2.callbacks[namespace].add(handler);
    dispatcher2.update(namespace);
    return () => {
      dispatcher2.callbacks[namespace].delete(handler);
    };
  }, [namespace]);
  return state;
}
