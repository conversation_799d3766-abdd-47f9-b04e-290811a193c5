// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/user","layout":false,"id":"1"},"2":{"path":"/user/login","layout":false,"name":"login","parentId":"1","id":"2"},"3":{"path":"/user","redirect":"/user/login","parentId":"1","id":"3"},"4":{"name":"register-result","icon":"smile","path":"/user/register-result","parentId":"1","id":"4"},"5":{"name":"register","icon":"smile","path":"/user/register","parentId":"1","id":"5"},"6":{"parentId":"1","id":"6"},"7":{"path":"/callback","layout":false,"id":"7"},"8":{"path":"/","redirect":"/dashboard","parentId":"ant-design-pro-layout","id":"8"},"9":{"path":"/dashboard","name":"dashboard","icon":"dashboard","parentId":"ant-design-pro-layout","id":"9"},"10":{"path":"/dashboard","redirect":"/dashboard/workplace","parentId":"9","id":"10"},"11":{"name":"analysis","icon":"BarChartOutlined","path":"/dashboard/analysis","access":"isAdmin","parentId":"9","id":"11"},"12":{"name":"monitor","icon":"MonitorOutlined","path":"/dashboard/monitor","access":"isAdmin","parentId":"9","id":"12"},"13":{"name":"workplace","icon":"DesktopOutlined","path":"/dashboard/workplace","parentId":"9","id":"13"},"14":{"path":"/audit","name":"audit","icon":"SafetyOutlined","access":"isAdmin","parentId":"ant-design-pro-layout","id":"14"},"15":{"path":"/audit","redirect":"/audit/app","parentId":"14","id":"15"},"16":{"name":"app-audit","icon":"AppstoreOutlined","path":"/audit/app","access":"isAdmin","parentId":"14","id":"16"},"17":{"name":"version-review","icon":"CheckCircleOutlined","path":"/audit/version","access":"isAdmin","parentId":"14","id":"17"},"18":{"path":"/statistics","name":"statistics","icon":"LineChartOutlined","access":"canViewStatistics","parentId":"ant-design-pro-layout","id":"18"},"19":{"path":"/statistics","redirect":"/statistics/app","parentId":"18","id":"19"},"20":{"name":"app-statistics","icon":"AppstoreOutlined","path":"/statistics/app","access":"canViewStatistics","parentId":"18","id":"20"},"21":{"name":"developer-statistics","icon":"DashboardOutlined","path":"/statistics/developer","access":"canManageRole","parentId":"18","id":"21"},"22":{"path":"/user-management","name":"user-management","icon":"TeamOutlined","access":"canViewUserList","parentId":"ant-design-pro-layout","id":"22"},"23":{"path":"/user-management","redirect":"/user-management/list","parentId":"22","id":"23"},"24":{"name":"user-list","icon":"UnorderedListOutlined","path":"/user-management/list","access":"canViewUserList","parentId":"22","id":"24"},"25":{"name":"user-detail","icon":"UserOutlined","path":"/user-management/detail/:id","hideInMenu":true,"access":"canViewUserList","parentId":"22","id":"25"},"26":{"name":"developer-verify","icon":"SafetyCertificateOutlined","path":"/user-management/developer-verify","access":"canManageRole","parentId":"22","id":"26"},"27":{"path":"/review","name":"review","icon":"CommentOutlined","access":"canViewAudit","parentId":"ant-design-pro-layout","id":"27"},"28":{"path":"/review","redirect":"/review/list","parentId":"27","id":"28"},"29":{"name":"review-list","icon":"UnorderedListOutlined","path":"/review/list","access":"canViewAudit","parentId":"27","id":"29"},"30":{"path":"/app","name":"app","icon":"AppstoreOutlined","access":"canViewApp","parentId":"ant-design-pro-layout","id":"30"},"31":{"path":"/app","redirect":"/app/list","parentId":"30","id":"31"},"32":{"name":"app-list","icon":"UnorderedListOutlined","path":"/app/list","access":"canViewApp","parentId":"30","id":"32"},"33":{"name":"app-detail","icon":"InfoCircleOutlined","path":"/app/detail/:id","hideInMenu":true,"access":"canViewApp","parentId":"30","id":"33"},"34":{"name":"app-create","icon":"PlusOutlined","path":"/app/create","hideInMenu":true,"access":"canCreateApp","parentId":"30","id":"34"},"35":{"name":"app-edit","icon":"EditOutlined","path":"/app/edit/:id","hideInMenu":true,"access":"canEditApp","parentId":"30","id":"35"},"36":{"name":"featured-collection","icon":"StarOutlined","path":"/app/featured-collection","access":"isAdmin","parentId":"30","id":"36"},"37":{"path":"/settings","name":"settings","icon":"SettingOutlined","access":"canViewSettings","parentId":"ant-design-pro-layout","id":"37"},"38":{"path":"/settings","redirect":"/settings/tags","parentId":"37","id":"38"},"39":{"name":"tag-management","icon":"TagsOutlined","path":"/settings/tags","access":"canViewSettings","parentId":"37","id":"39"},"40":{"name":"category-management","icon":"FolderOutlined","path":"/settings/categories","access":"canViewSettings","parentId":"37","id":"40"},"41":{"name":"openharmony-versions","icon":"MobileOutlined","path":"/settings/openharmony-versions","access":"isAdmin","parentId":"37","id":"41"},"42":{"name":"account","icon":"UserOutlined","path":"/account","parentId":"ant-design-pro-layout","id":"42"},"43":{"path":"/account","redirect":"/account/settings","parentId":"42","id":"43"},"44":{"name":"settings","icon":"SettingOutlined","path":"/account/settings","parentId":"42","id":"44"},"45":{"name":"notifications","icon":"BellOutlined","path":"/account/notifications","parentId":"42","id":"45"},"46":{"path":"/developer","name":"developer","icon":"CodeOutlined","access":"canViewDeveloper","parentId":"ant-design-pro-layout","id":"46"},"47":{"path":"/developer","redirect":"/developer/verify","parentId":"46","id":"47"},"48":{"name":"verify","icon":"SafetyCertificateOutlined","path":"/developer/verify","access":"canViewDeveloper","parentId":"46","id":"48"},"49":{"name":"app-versions","icon":"AppstoreOutlined","path":"/app/list/versions/:id","hideInMenu":true,"access":"canViewDeveloper","parentId":"ant-design-pro-layout","id":"49"},"50":{"name":"create-version","icon":"PlusOutlined","path":"/app/list/versions/:id/create","hideInMenu":true,"access":"canViewDeveloper","parentId":"ant-design-pro-layout","id":"50"},"51":{"name":"edit-version","icon":"EditOutlined","path":"/app/list/versions/:id/edit/:versionId","hideInMenu":true,"access":"canViewDeveloper","parentId":"ant-design-pro-layout","id":"51"},"52":{"path":"/","redirect":"/dashboard/workplace","parentId":"ant-design-pro-layout","id":"52"},"53":{"path":"/*","parentId":"ant-design-pro-layout","id":"53"},"ant-design-pro-layout":{"id":"ant-design-pro-layout","path":"/","isLayout":true},"umi/plugin/openapi":{"path":"/umi/plugin/openapi","id":"umi/plugin/openapi"}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import('./EmptyRoute')),
'2': React.lazy(() => import(/* webpackChunkName: "p__user__login__index" */'@/pages/user/login/index.tsx')),
'3': React.lazy(() => import('./EmptyRoute')),
'4': React.lazy(() => import(/* webpackChunkName: "p__user__register-result__index" */'@/pages/user/register-result/index.tsx')),
'5': React.lazy(() => import(/* webpackChunkName: "p__user__register__index" */'@/pages/user/register/index.tsx')),
'6': React.lazy(() => import(/* webpackChunkName: "p__404" */'@/pages/404.tsx')),
'7': React.lazy(() => import(/* webpackChunkName: "p__callback__index" */'@/pages/callback/index.tsx')),
'8': React.lazy(() => import('./EmptyRoute')),
'9': React.lazy(() => import('./EmptyRoute')),
'10': React.lazy(() => import('./EmptyRoute')),
'11': React.lazy(() => import(/* webpackChunkName: "p__dashboard__analysis__index" */'@/pages/dashboard/analysis/index.tsx')),
'12': React.lazy(() => import(/* webpackChunkName: "p__dashboard__monitor__index" */'@/pages/dashboard/monitor/index.tsx')),
'13': React.lazy(() => import(/* webpackChunkName: "p__dashboard__workplace__index" */'@/pages/dashboard/workplace/index.tsx')),
'14': React.lazy(() => import('./EmptyRoute')),
'15': React.lazy(() => import('./EmptyRoute')),
'16': React.lazy(() => import(/* webpackChunkName: "p__App__Audit__index" */'@/pages/App/Audit/index.tsx')),
'17': React.lazy(() => import(/* webpackChunkName: "p__admin__VersionReview__index" */'@/pages/admin/VersionReview/index.tsx')),
'18': React.lazy(() => import('./EmptyRoute')),
'19': React.lazy(() => import('./EmptyRoute')),
'20': React.lazy(() => import(/* webpackChunkName: "p__Statistics__App__index" */'@/pages/Statistics/App/index.tsx')),
'21': React.lazy(() => import(/* webpackChunkName: "p__admin__developer__dashboard__index" */'@/pages/admin/developer/dashboard/index.tsx')),
'22': React.lazy(() => import('./EmptyRoute')),
'23': React.lazy(() => import('./EmptyRoute')),
'24': React.lazy(() => import(/* webpackChunkName: "p__UserManagement__List__index" */'@/pages/UserManagement/List/index.tsx')),
'25': React.lazy(() => import(/* webpackChunkName: "p__UserManagement__Detail__index" */'@/pages/UserManagement/Detail/index.tsx')),
'26': React.lazy(() => import(/* webpackChunkName: "p__admin__developer__verify__index" */'@/pages/admin/developer/verify/index.tsx')),
'27': React.lazy(() => import('./EmptyRoute')),
'28': React.lazy(() => import('./EmptyRoute')),
'29': React.lazy(() => import(/* webpackChunkName: "p__Review__List__index" */'@/pages/Review/List/index.tsx')),
'30': React.lazy(() => import('./EmptyRoute')),
'31': React.lazy(() => import('./EmptyRoute')),
'32': React.lazy(() => import(/* webpackChunkName: "p__App__List__index" */'@/pages/App/List/index.tsx')),
'33': React.lazy(() => import(/* webpackChunkName: "p__App__Detail__index" */'@/pages/App/Detail/index.tsx')),
'34': React.lazy(() => import(/* webpackChunkName: "p__App__Create__index" */'@/pages/App/Create/index.tsx')),
'35': React.lazy(() => import(/* webpackChunkName: "p__App__Edit__index" */'@/pages/App/Edit/index.tsx')),
'36': React.lazy(() => import(/* webpackChunkName: "p__App__FeaturedCollection__index" */'@/pages/App/FeaturedCollection/index.tsx')),
'37': React.lazy(() => import('./EmptyRoute')),
'38': React.lazy(() => import('./EmptyRoute')),
'39': React.lazy(() => import(/* webpackChunkName: "p__Settings__Tags__index" */'@/pages/Settings/Tags/index.tsx')),
'40': React.lazy(() => import(/* webpackChunkName: "p__Settings__Categories__index" */'@/pages/Settings/Categories/index.tsx')),
'41': React.lazy(() => import(/* webpackChunkName: "p__Settings__OpenHarmonyVersions__index" */'@/pages/Settings/OpenHarmonyVersions/index.tsx')),
'42': React.lazy(() => import('./EmptyRoute')),
'43': React.lazy(() => import('./EmptyRoute')),
'44': React.lazy(() => import(/* webpackChunkName: "p__account__settings__index" */'@/pages/account/settings/index.tsx')),
'45': React.lazy(() => import(/* webpackChunkName: "p__account__notifications__index" */'@/pages/account/notifications/index.tsx')),
'46': React.lazy(() => import('./EmptyRoute')),
'47': React.lazy(() => import('./EmptyRoute')),
'48': React.lazy(() => import(/* webpackChunkName: "p__developer__verify__index" */'@/pages/developer/verify/index.tsx')),
'49': React.lazy(() => import(/* webpackChunkName: "p__App__Versions__index" */'@/pages/App/Versions/index.tsx')),
'50': React.lazy(() => import(/* webpackChunkName: "p__App__Versions__CreateEdit__index" */'@/pages/App/Versions/CreateEdit/index.tsx')),
'51': React.lazy(() => import(/* webpackChunkName: "p__App__Versions__CreateEdit__index" */'@/pages/App/Versions/CreateEdit/index.tsx')),
'52': React.lazy(() => import('./EmptyRoute')),
'53': React.lazy(() => import(/* webpackChunkName: "p__404" */'@/pages/404.tsx')),
'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: "umi__plugin-layout__Layout" */'C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/src/.umi/plugin-layout/Layout.tsx')),
'umi/plugin/openapi': React.lazy(() => import(/* webpackChunkName: "umi__plugin-openapi__openapi" */'C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/src/.umi/plugin-openapi/openapi.tsx')),
},
  };
}
