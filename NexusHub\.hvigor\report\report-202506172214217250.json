{"version": "2.0", "ppid": 33740, "events": [{"head": {"id": "8e1b7d1e-001f-4ad4-ba9a-260767af8e54", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862373184700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d19ff163-94a6-403d-b72d-faeed5f46c23", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862373278300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7d4a810-da6b-43b2-ae1f-1bf4383f4ec4", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862373406000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54f8c2d4-41b8-455e-84e1-8696e5cd2383", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862377052600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "349875fd-c238-407d-95cb-09e1cbd52560", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862377830000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c5798fe-4e93-4ec7-9b4c-f479d6ef3200", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862378408900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2e850c6-eed6-4d53-a972-ffe61ee83790", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862378839800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "346a06d3-fd05-4636-933f-63bd314f4705", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862380824300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42eb8228-1dae-45a4-87c0-58cd5b9c1402", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862414677300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89d52953-aee5-4264-8ffc-68d802629048", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031943134300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8d63b0f-e14c-4304-8809-5aaa3a5e29f3", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031952532900, "endTime": 145032245798300}, "additional": {"children": ["ad0bb350-e4aa-4d16-8f74-d4ba135451d5", "45a059dd-911d-4c15-adf8-4a80679d7723", "acfe0250-74da-4220-a126-858f4211948c", "b3c75391-156b-4495-bc6b-bd624ed8b78b", "444320f4-366c-43aa-bad1-3d12e047da8b", "1a197627-220c-4d8a-9464-dc4649de1ccd", "26bb6e4e-fb65-434b-8c28-6f4046af5c94"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "796db5f5-a94c-4e8e-87ab-a2cb9b16bf75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad0bb350-e4aa-4d16-8f74-d4ba135451d5", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031952534200, "endTime": 145031966547400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8d63b0f-e14c-4304-8809-5aaa3a5e29f3", "logId": "65e909bf-c803-4a37-9f69-d524b847bfcd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45a059dd-911d-4c15-adf8-4a80679d7723", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031966567200, "endTime": 145032243975000}, "additional": {"children": ["70097809-0523-4ab4-a771-1479b5bb6908", "f32420a2-a457-44e2-8dfa-62c19ea7fc4c", "4d4d47c3-1819-443c-93e8-df84b3fa38e9", "0201110e-3e05-4eb6-abdc-641aad357bef", "47bdd845-ee72-43cb-9eed-f7ae68639e5d", "023f4843-fad5-46ac-88ef-4f2ec736a6cf", "c7e4007a-62a6-4f73-bce4-78e984347275", "96f580f6-da60-428a-a15d-477683938ad0", "25f67d21-6d8f-4775-b802-9256508d11c3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8d63b0f-e14c-4304-8809-5aaa3a5e29f3", "logId": "37f2224f-b06b-4a87-b6f4-ed7d0a61ba59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "acfe0250-74da-4220-a126-858f4211948c", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032244011700, "endTime": 145032245750600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8d63b0f-e14c-4304-8809-5aaa3a5e29f3", "logId": "cb6f6e80-7ba0-43e6-b45c-2b5b0dbc6638"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3c75391-156b-4495-bc6b-bd624ed8b78b", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032245759200, "endTime": 145032245789500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8d63b0f-e14c-4304-8809-5aaa3a5e29f3", "logId": "e875883f-1ace-43e0-b555-98d0e973d723"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "444320f4-366c-43aa-bad1-3d12e047da8b", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031956166600, "endTime": 145031956213300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8d63b0f-e14c-4304-8809-5aaa3a5e29f3", "logId": "2653a17b-efdc-4462-8855-44cfdac885b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2653a17b-efdc-4462-8855-44cfdac885b6", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031956166600, "endTime": 145031956213300}, "additional": {"logType": "info", "children": [], "durationId": "444320f4-366c-43aa-bad1-3d12e047da8b", "parent": "796db5f5-a94c-4e8e-87ab-a2cb9b16bf75"}}, {"head": {"id": "1a197627-220c-4d8a-9464-dc4649de1ccd", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031960320600, "endTime": 145031960342400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8d63b0f-e14c-4304-8809-5aaa3a5e29f3", "logId": "f0722754-aca1-48ff-8c9d-e9fdba21467c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0722754-aca1-48ff-8c9d-e9fdba21467c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031960320600, "endTime": 145031960342400}, "additional": {"logType": "info", "children": [], "durationId": "1a197627-220c-4d8a-9464-dc4649de1ccd", "parent": "796db5f5-a94c-4e8e-87ab-a2cb9b16bf75"}}, {"head": {"id": "f19cd9f5-d827-42f3-b32c-ea637cb97129", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031960401300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca038c94-d806-43f4-8734-542d684d8a4f", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031966380100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65e909bf-c803-4a37-9f69-d524b847bfcd", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031952534200, "endTime": 145031966547400}, "additional": {"logType": "info", "children": [], "durationId": "ad0bb350-e4aa-4d16-8f74-d4ba135451d5", "parent": "796db5f5-a94c-4e8e-87ab-a2cb9b16bf75"}}, {"head": {"id": "70097809-0523-4ab4-a771-1479b5bb6908", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031975096100, "endTime": 145031975120700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "45a059dd-911d-4c15-adf8-4a80679d7723", "logId": "3d6e00db-cb56-4751-a2bb-d1c35ce698da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f32420a2-a457-44e2-8dfa-62c19ea7fc4c", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031975141500, "endTime": 145031982748400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "45a059dd-911d-4c15-adf8-4a80679d7723", "logId": "c64d3ee7-ca1c-4d62-b7c7-f39cf5db8521"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d4d47c3-1819-443c-93e8-df84b3fa38e9", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031982763300, "endTime": 145032076338300}, "additional": {"children": ["ae327f3c-df9e-4c69-9ec5-0b7635629730", "2c5f9245-9dbb-4fcc-8382-3fe682624c5d", "89bac6b9-5dcc-4134-96b7-5bf66ab34edc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "45a059dd-911d-4c15-adf8-4a80679d7723", "logId": "5ed3121b-b4c9-4acf-9155-1a21bd5d1901"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0201110e-3e05-4eb6-abdc-641aad357bef", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032076358700, "endTime": 145032107857100}, "additional": {"children": ["57573d59-1faa-4a1a-9a00-85d8cff0acfe"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "45a059dd-911d-4c15-adf8-4a80679d7723", "logId": "dc2c0218-0af7-46cb-aa35-191df8400b9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47bdd845-ee72-43cb-9eed-f7ae68639e5d", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032107866500, "endTime": 145032194882500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "45a059dd-911d-4c15-adf8-4a80679d7723", "logId": "0232dac9-594f-4049-9140-7b19089169c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "023f4843-fad5-46ac-88ef-4f2ec736a6cf", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032197119000, "endTime": 145032217628300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "45a059dd-911d-4c15-adf8-4a80679d7723", "logId": "3162b4e2-0d86-42a6-96a8-e19a8eb2ad65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7e4007a-62a6-4f73-bce4-78e984347275", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032217667100, "endTime": 145032243776200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "45a059dd-911d-4c15-adf8-4a80679d7723", "logId": "5c26ebb7-0758-4911-93e1-b16b4041f92e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96f580f6-da60-428a-a15d-477683938ad0", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032243800100, "endTime": 145032243956000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "45a059dd-911d-4c15-adf8-4a80679d7723", "logId": "13ac2026-35e1-4fe6-9d27-3f6a518d8d4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d6e00db-cb56-4751-a2bb-d1c35ce698da", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031975096100, "endTime": 145031975120700}, "additional": {"logType": "info", "children": [], "durationId": "70097809-0523-4ab4-a771-1479b5bb6908", "parent": "37f2224f-b06b-4a87-b6f4-ed7d0a61ba59"}}, {"head": {"id": "c64d3ee7-ca1c-4d62-b7c7-f39cf5db8521", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031975141500, "endTime": 145031982748400}, "additional": {"logType": "info", "children": [], "durationId": "f32420a2-a457-44e2-8dfa-62c19ea7fc4c", "parent": "37f2224f-b06b-4a87-b6f4-ed7d0a61ba59"}}, {"head": {"id": "ae327f3c-df9e-4c69-9ec5-0b7635629730", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031983527500, "endTime": 145031983589700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4d4d47c3-1819-443c-93e8-df84b3fa38e9", "logId": "03c5fa88-2da8-4e9e-a674-8c3c4389e548"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03c5fa88-2da8-4e9e-a674-8c3c4389e548", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031983527500, "endTime": 145031983589700}, "additional": {"logType": "info", "children": [], "durationId": "ae327f3c-df9e-4c69-9ec5-0b7635629730", "parent": "5ed3121b-b4c9-4acf-9155-1a21bd5d1901"}}, {"head": {"id": "2c5f9245-9dbb-4fcc-8382-3fe682624c5d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031985700300, "endTime": 145032075190000}, "additional": {"children": ["8508694e-6bee-41b9-8551-05b09990b301", "28d43f9f-4ba5-4eb0-9732-5c2f0ad25e18"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4d4d47c3-1819-443c-93e8-df84b3fa38e9", "logId": "cbe690c6-78b1-46ec-9996-9f8a82ee9821"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8508694e-6bee-41b9-8551-05b09990b301", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031985702100, "endTime": 145031992502000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2c5f9245-9dbb-4fcc-8382-3fe682624c5d", "logId": "e0787d18-6fd3-4d38-bd41-17c556b62f18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28d43f9f-4ba5-4eb0-9732-5c2f0ad25e18", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031992517300, "endTime": 145032075172000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2c5f9245-9dbb-4fcc-8382-3fe682624c5d", "logId": "19316fce-d7bc-4385-b0ab-20f438467023"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4878aa7f-c278-4e53-aca2-5c11e5e36ca3", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031985710300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db1a22d7-4d1e-4854-83cd-982104387b54", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031992370300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0787d18-6fd3-4d38-bd41-17c556b62f18", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031985702100, "endTime": 145031992502000}, "additional": {"logType": "info", "children": [], "durationId": "8508694e-6bee-41b9-8551-05b09990b301", "parent": "cbe690c6-78b1-46ec-9996-9f8a82ee9821"}}, {"head": {"id": "98d0c527-dee8-41b9-82f0-8c1da6971bf8", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031992529600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65c98765-3128-4d7a-bc4b-f97d6faa6b68", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031998130200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c328a8e-c566-4f67-806c-3c00ab801d61", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031998880100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca60490f-11d5-412d-9daa-21a7cba67d7a", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031999048200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "247bb2f8-e7c7-4c33-8c70-cca9ae9338a9", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031999115600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5afd8e3-71af-420e-80f2-564556bc08c7", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032000461000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51376e00-6585-44d5-a64b-afbaed816ca8", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032019033400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aabeb61-2fe2-439f-9baf-e01e3f188513", "name": "Sdk init in 34 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032041211900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6f12a38-43e4-4306-82ab-2f95e7116883", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032041416200}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 14, "second": 21}, "markType": "other"}}, {"head": {"id": "55861ecf-53a8-4b46-b2a8-551920ed8803", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032041451200}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 14, "second": 21}, "markType": "other"}}, {"head": {"id": "9bddb4af-36d0-4767-a161-77e41c33c919", "name": "Project task initialization takes 32 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032074727900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bedc4271-b589-4aed-a363-747042960635", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032074902400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2475f488-e5e7-4fdb-a1a3-cfd442c95300", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032075017500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa4fdc14-7135-4406-94aa-bfa79efc9c29", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032075099000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19316fce-d7bc-4385-b0ab-20f438467023", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031992517300, "endTime": 145032075172000}, "additional": {"logType": "info", "children": [], "durationId": "28d43f9f-4ba5-4eb0-9732-5c2f0ad25e18", "parent": "cbe690c6-78b1-46ec-9996-9f8a82ee9821"}}, {"head": {"id": "cbe690c6-78b1-46ec-9996-9f8a82ee9821", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031985700300, "endTime": 145032075190000}, "additional": {"logType": "info", "children": ["e0787d18-6fd3-4d38-bd41-17c556b62f18", "19316fce-d7bc-4385-b0ab-20f438467023"], "durationId": "2c5f9245-9dbb-4fcc-8382-3fe682624c5d", "parent": "5ed3121b-b4c9-4acf-9155-1a21bd5d1901"}}, {"head": {"id": "89bac6b9-5dcc-4134-96b7-5bf66ab34edc", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032076298600, "endTime": 145032076319100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4d4d47c3-1819-443c-93e8-df84b3fa38e9", "logId": "96ee3caa-6148-4b84-9e96-4464ac061190"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96ee3caa-6148-4b84-9e96-4464ac061190", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032076298600, "endTime": 145032076319100}, "additional": {"logType": "info", "children": [], "durationId": "89bac6b9-5dcc-4134-96b7-5bf66ab34edc", "parent": "5ed3121b-b4c9-4acf-9155-1a21bd5d1901"}}, {"head": {"id": "5ed3121b-b4c9-4acf-9155-1a21bd5d1901", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031982763300, "endTime": 145032076338300}, "additional": {"logType": "info", "children": ["03c5fa88-2da8-4e9e-a674-8c3c4389e548", "cbe690c6-78b1-46ec-9996-9f8a82ee9821", "96ee3caa-6148-4b84-9e96-4464ac061190"], "durationId": "4d4d47c3-1819-443c-93e8-df84b3fa38e9", "parent": "37f2224f-b06b-4a87-b6f4-ed7d0a61ba59"}}, {"head": {"id": "57573d59-1faa-4a1a-9a00-85d8cff0acfe", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032077386000, "endTime": 145032107843200}, "additional": {"children": ["5d245581-94f5-4134-83c2-78a05b5ef6c4", "3a61a703-6407-43ee-a53c-5d7467df3eef", "f04d7d5e-00c8-4385-b7db-f57f96455562"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0201110e-3e05-4eb6-abdc-641aad357bef", "logId": "efe33815-6646-46b2-be21-52284b898312"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d245581-94f5-4134-83c2-78a05b5ef6c4", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032081956300, "endTime": 145032081979700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57573d59-1faa-4a1a-9a00-85d8cff0acfe", "logId": "8f7db82e-1a46-4cb7-8077-a2feee6b2909"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f7db82e-1a46-4cb7-8077-a2feee6b2909", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032081956300, "endTime": 145032081979700}, "additional": {"logType": "info", "children": [], "durationId": "5d245581-94f5-4134-83c2-78a05b5ef6c4", "parent": "efe33815-6646-46b2-be21-52284b898312"}}, {"head": {"id": "3a61a703-6407-43ee-a53c-5d7467df3eef", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032084816400, "endTime": 145032106112000}, "additional": {"children": ["c238a361-845c-42dd-af68-853526616e1c", "50081b6e-ce65-429b-a64a-e02d921b9507"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57573d59-1faa-4a1a-9a00-85d8cff0acfe", "logId": "014b6c96-3113-4c77-ab15-60199510fd5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c238a361-845c-42dd-af68-853526616e1c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032084817700, "endTime": 145032089521700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a61a703-6407-43ee-a53c-5d7467df3eef", "logId": "b6618701-edaa-406d-9b4a-7d091fd4bfe7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50081b6e-ce65-429b-a64a-e02d921b9507", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032089538300, "endTime": 145032106098800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a61a703-6407-43ee-a53c-5d7467df3eef", "logId": "fafe50dd-bd24-4369-8185-983a434217fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63870889-a50b-4810-aaf5-4091232dded3", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032084824000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e5bf15d-43cb-4b89-81c1-571d855d80c2", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032089383800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6618701-edaa-406d-9b4a-7d091fd4bfe7", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032084817700, "endTime": 145032089521700}, "additional": {"logType": "info", "children": [], "durationId": "c238a361-845c-42dd-af68-853526616e1c", "parent": "014b6c96-3113-4c77-ab15-60199510fd5a"}}, {"head": {"id": "b71614db-1aa7-446e-af17-bc8a62a25ceb", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032089552600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d935e46a-2837-4890-989e-101833100fee", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032098351200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b94dae96-34a2-4eca-b366-8bc17483a1fa", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032098484300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcac2f27-d7a7-4e16-b217-e19048c9f8d9", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032098872000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4a2ca45-2ffd-4cf4-8f5c-e95586ea6abb", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099142600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98b9d5a4-a60b-4bca-84ee-8259d0c1b28a", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099191300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03e82c81-2bc1-4f17-894e-54487931a16d", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099226500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c479d03e-c107-4140-86b7-27e6bc10820d", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099324300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8be523f7-f5a9-454f-b0c1-3403185d7a36", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099360500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d2b7506-4b2b-435d-a86c-bcc3154338f3", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099494300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "899ccf2c-636c-44e1-8df8-1a675793cdd3", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099559900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c323e81-c6a7-444c-a751-08d395be4ae1", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099592400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f572fb16-93d1-43ea-8da3-a8df8d8ce3d7", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099617000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66433c6d-8498-4b91-a8ff-e9155642d891", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099650900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "942003f1-1ef0-4eba-b131-34ee2a4aa602", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099676500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14a5083a-3e31-43d1-b554-4f4f052244ce", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099776600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f1a49af-e613-48ab-a332-5e196513905d", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099834100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e586a15a-e7b7-4ce3-8159-2a1ae3d908d5", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099859000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08836dfa-d415-4872-a9c9-0e93c1b7ad5b", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099883100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aca791b3-0e49-452c-8a6d-1515092c6659", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032099913700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "600c38a2-07cc-48e0-a148-835934f0b306", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032105748800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "007a0481-b6ab-4351-b841-19f1c9eb05ca", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032105914000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d18a6da1-7490-4067-8dd5-d8799f5b4c2b", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032105987200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8192849e-f2b0-404e-8e92-3cc8024b5ed3", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032106044200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fafe50dd-bd24-4369-8185-983a434217fd", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032089538300, "endTime": 145032106098800}, "additional": {"logType": "info", "children": [], "durationId": "50081b6e-ce65-429b-a64a-e02d921b9507", "parent": "014b6c96-3113-4c77-ab15-60199510fd5a"}}, {"head": {"id": "014b6c96-3113-4c77-ab15-60199510fd5a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032084816400, "endTime": 145032106112000}, "additional": {"logType": "info", "children": ["b6618701-edaa-406d-9b4a-7d091fd4bfe7", "fafe50dd-bd24-4369-8185-983a434217fd"], "durationId": "3a61a703-6407-43ee-a53c-5d7467df3eef", "parent": "efe33815-6646-46b2-be21-52284b898312"}}, {"head": {"id": "f04d7d5e-00c8-4385-b7db-f57f96455562", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032107813300, "endTime": 145032107828700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57573d59-1faa-4a1a-9a00-85d8cff0acfe", "logId": "d6a6e162-3042-4a84-9021-f847f4207109"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6a6e162-3042-4a84-9021-f847f4207109", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032107813300, "endTime": 145032107828700}, "additional": {"logType": "info", "children": [], "durationId": "f04d7d5e-00c8-4385-b7db-f57f96455562", "parent": "efe33815-6646-46b2-be21-52284b898312"}}, {"head": {"id": "efe33815-6646-46b2-be21-52284b898312", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032077386000, "endTime": 145032107843200}, "additional": {"logType": "info", "children": ["8f7db82e-1a46-4cb7-8077-a2feee6b2909", "014b6c96-3113-4c77-ab15-60199510fd5a", "d6a6e162-3042-4a84-9021-f847f4207109"], "durationId": "57573d59-1faa-4a1a-9a00-85d8cff0acfe", "parent": "dc2c0218-0af7-46cb-aa35-191df8400b9c"}}, {"head": {"id": "dc2c0218-0af7-46cb-aa35-191df8400b9c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032076358700, "endTime": 145032107857100}, "additional": {"logType": "info", "children": ["efe33815-6646-46b2-be21-52284b898312"], "durationId": "0201110e-3e05-4eb6-abdc-641aad357bef", "parent": "37f2224f-b06b-4a87-b6f4-ed7d0a61ba59"}}, {"head": {"id": "783189d7-b366-4804-a803-a9f4e37cec96", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032139334100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb745979-d637-4f07-a83a-157b144357cd", "name": "hvigorfile, resolve hvigorfile dependencies in 87 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032194681600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0232dac9-594f-4049-9140-7b19089169c7", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032107866500, "endTime": 145032194882500}, "additional": {"logType": "info", "children": [], "durationId": "47bdd845-ee72-43cb-9eed-f7ae68639e5d", "parent": "37f2224f-b06b-4a87-b6f4-ed7d0a61ba59"}}, {"head": {"id": "25f67d21-6d8f-4775-b802-9256508d11c3", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032196770900, "endTime": 145032197094600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "45a059dd-911d-4c15-adf8-4a80679d7723", "logId": "0f0d36d5-3a34-4153-9f19-7cf2979dd473"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9f6224e-f1e9-4943-a1e5-d47f6e9d4e93", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032196823900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f0d36d5-3a34-4153-9f19-7cf2979dd473", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032196770900, "endTime": 145032197094600}, "additional": {"logType": "info", "children": [], "durationId": "25f67d21-6d8f-4775-b802-9256508d11c3", "parent": "37f2224f-b06b-4a87-b6f4-ed7d0a61ba59"}}, {"head": {"id": "c62c7f93-6319-4121-b44d-6561b89d804d", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032199217400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f58cf7ee-e1fb-4ca2-a38c-e3cceb48dc84", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032216301900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3162b4e2-0d86-42a6-96a8-e19a8eb2ad65", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032197119000, "endTime": 145032217628300}, "additional": {"logType": "info", "children": [], "durationId": "023f4843-fad5-46ac-88ef-4f2ec736a6cf", "parent": "37f2224f-b06b-4a87-b6f4-ed7d0a61ba59"}}, {"head": {"id": "95143a2e-1115-43be-b694-67e9e1998183", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032217696400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47cea72f-6165-427d-ac39-30f6978f4fde", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032231521000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d0a819-9fd1-415e-90e8-f09329732dd8", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032231687700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed6eef46-20d0-43ba-8295-fc4f597b8cc6", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032231926300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "177d27e0-6ccd-425f-b5ba-7e74fa3da441", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032238099700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cc2bf5e-9c85-4ad3-9981-3b1bc4b86883", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032238286200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c26ebb7-0758-4911-93e1-b16b4041f92e", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032217667100, "endTime": 145032243776200}, "additional": {"logType": "info", "children": [], "durationId": "c7e4007a-62a6-4f73-bce4-78e984347275", "parent": "37f2224f-b06b-4a87-b6f4-ed7d0a61ba59"}}, {"head": {"id": "101c8046-f476-4258-9fef-9d95fb85c3ee", "name": "Configuration phase cost:269 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032243840900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13ac2026-35e1-4fe6-9d27-3f6a518d8d4b", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032243800100, "endTime": 145032243956000}, "additional": {"logType": "info", "children": [], "durationId": "96f580f6-da60-428a-a15d-477683938ad0", "parent": "37f2224f-b06b-4a87-b6f4-ed7d0a61ba59"}}, {"head": {"id": "37f2224f-b06b-4a87-b6f4-ed7d0a61ba59", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031966567200, "endTime": 145032243975000}, "additional": {"logType": "info", "children": ["3d6e00db-cb56-4751-a2bb-d1c35ce698da", "c64d3ee7-ca1c-4d62-b7c7-f39cf5db8521", "5ed3121b-b4c9-4acf-9155-1a21bd5d1901", "dc2c0218-0af7-46cb-aa35-191df8400b9c", "0232dac9-594f-4049-9140-7b19089169c7", "3162b4e2-0d86-42a6-96a8-e19a8eb2ad65", "5c26ebb7-0758-4911-93e1-b16b4041f92e", "13ac2026-35e1-4fe6-9d27-3f6a518d8d4b", "0f0d36d5-3a34-4153-9f19-7cf2979dd473"], "durationId": "45a059dd-911d-4c15-adf8-4a80679d7723", "parent": "796db5f5-a94c-4e8e-87ab-a2cb9b16bf75"}}, {"head": {"id": "26bb6e4e-fb65-434b-8c28-6f4046af5c94", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032245719400, "endTime": 145032245737900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8d63b0f-e14c-4304-8809-5aaa3a5e29f3", "logId": "5da6694c-260b-43ef-a0ab-652b9cc12342"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5da6694c-260b-43ef-a0ab-652b9cc12342", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032245719400, "endTime": 145032245737900}, "additional": {"logType": "info", "children": [], "durationId": "26bb6e4e-fb65-434b-8c28-6f4046af5c94", "parent": "796db5f5-a94c-4e8e-87ab-a2cb9b16bf75"}}, {"head": {"id": "cb6f6e80-7ba0-43e6-b45c-2b5b0dbc6638", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032244011700, "endTime": 145032245750600}, "additional": {"logType": "info", "children": [], "durationId": "acfe0250-74da-4220-a126-858f4211948c", "parent": "796db5f5-a94c-4e8e-87ab-a2cb9b16bf75"}}, {"head": {"id": "e875883f-1ace-43e0-b555-98d0e973d723", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032245759200, "endTime": 145032245789500}, "additional": {"logType": "info", "children": [], "durationId": "b3c75391-156b-4495-bc6b-bd624ed8b78b", "parent": "796db5f5-a94c-4e8e-87ab-a2cb9b16bf75"}}, {"head": {"id": "796db5f5-a94c-4e8e-87ab-a2cb9b16bf75", "name": "init", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031952532900, "endTime": 145032245798300}, "additional": {"logType": "info", "children": ["65e909bf-c803-4a37-9f69-d524b847bfcd", "37f2224f-b06b-4a87-b6f4-ed7d0a61ba59", "cb6f6e80-7ba0-43e6-b45c-2b5b0dbc6638", "e875883f-1ace-43e0-b555-98d0e973d723", "2653a17b-efdc-4462-8855-44cfdac885b6", "f0722754-aca1-48ff-8c9d-e9fdba21467c", "5da6694c-260b-43ef-a0ab-652b9cc12342"], "durationId": "f8d63b0f-e14c-4304-8809-5aaa3a5e29f3"}}, {"head": {"id": "41d9f797-ffaa-4fc2-8470-3938a223be5f", "name": "Configuration task cost before running: 299 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032245986500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78186a33-7b6e-4aa4-b382-bab383260603", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032262149100, "endTime": 145032280666100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "69c68c95-ca7b-472f-a57d-ca1c5c05e52e", "logId": "606b2fca-d276-4515-8317-f871f486752a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69c68c95-ca7b-472f-a57d-ca1c5c05e52e", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032249912400}, "additional": {"logType": "detail", "children": [], "durationId": "78186a33-7b6e-4aa4-b382-bab383260603"}}, {"head": {"id": "5e8feae7-b365-4f98-bede-eb6a4629deba", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032251558500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffd72ea1-12ed-4e18-a09e-41db791f457e", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032251749900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c321a38-cea8-4108-915e-aed29029e8de", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032252610300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c19252b-888d-4657-a2ee-c6ba6c8569b5", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032253574300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2438d1a2-db33-43bc-9eeb-9ae752786cfd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032254552700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2debb3f-fd9f-4719-a172-172dca580def", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032254636300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab65e276-0537-42ee-8d96-234fd0567357", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032262170600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fa5fbf4-b76b-423f-9a03-ec708a035a9e", "name": "Incremental task entry:default@PreBuild pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032280387500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21c9669c-d6c2-4272-8204-9b50e0bb97ae", "name": "entry : default@PreBuild cost memory 0.3192138671875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032280566200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "606b2fca-d276-4515-8317-f871f486752a", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032262149100, "endTime": 145032280666100}, "additional": {"logType": "info", "children": [], "durationId": "78186a33-7b6e-4aa4-b382-bab383260603"}}, {"head": {"id": "8a0c1a68-aed7-4739-b88b-245e11041575", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032290471600, "endTime": 145032293633400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "234c87a0-0d42-4097-b2ba-5b87ce95b5e9", "logId": "772756c5-cd91-406b-84a2-e97f8f06f268"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "234c87a0-0d42-4097-b2ba-5b87ce95b5e9", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032287485400}, "additional": {"logType": "detail", "children": [], "durationId": "8a0c1a68-aed7-4739-b88b-245e11041575"}}, {"head": {"id": "48d9c790-71dc-4c64-bfe4-c5d11ae383bb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032289590700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a19cbcd3-72d9-4b2e-a3b6-b627976a1d69", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032289716200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9f16a70-45b4-4846-90e1-69ba9e67a6df", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032290493400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08587028-af46-497d-ac4b-f7eb1360bd5f", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032291811000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f57a313-834b-411b-bdf9-b06edaba8df6", "name": "entry : default@CreateModuleInfo cost memory 0.06186676025390625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032293364800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eda4f93-c321-4e57-bf69-5eee626999ab", "name": "runTaskFromQueue task cost before running: 347 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032293534000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "772756c5-cd91-406b-84a2-e97f8f06f268", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032290471600, "endTime": 145032293633400, "totalTime": 3028500}, "additional": {"logType": "info", "children": [], "durationId": "8a0c1a68-aed7-4739-b88b-245e11041575"}}, {"head": {"id": "bfc5821e-b985-4542-a73f-4f000085e1b9", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032305286600, "endTime": 145032308061600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "afe29f55-fbf5-4e71-9feb-0b78acf12b43", "logId": "918916ec-2bf7-4745-8d7e-71ed6525dbe6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afe29f55-fbf5-4e71-9feb-0b78acf12b43", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032297553400}, "additional": {"logType": "detail", "children": [], "durationId": "bfc5821e-b985-4542-a73f-4f000085e1b9"}}, {"head": {"id": "35369385-7feb-4e99-ae86-ec99417dc779", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032299697300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6426c07f-4904-4736-bd85-0875eef67b1a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032299853600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9643a6f-b1d6-4628-b5b7-c9bbb96d2b44", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032305307500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aa74824-e02a-440b-b52a-7680d003484c", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032306582400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e469d93f-6bc6-4a6c-95bb-7b78f59bdf3f", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032307841000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bf751ce-753c-4ff8-a461-95bf5dab86d0", "name": "entry : default@GenerateMetadata cost memory 0.1032562255859375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032307976300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "918916ec-2bf7-4745-8d7e-71ed6525dbe6", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032305286600, "endTime": 145032308061600}, "additional": {"logType": "info", "children": [], "durationId": "bfc5821e-b985-4542-a73f-4f000085e1b9"}}, {"head": {"id": "e71f48c3-bb4e-410b-b52a-8c1fe2476980", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032311768800, "endTime": 145032312222300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "cff917ec-6f86-49f8-abbf-480441735c7a", "logId": "f716b3fb-eb6d-446b-8333-477d3e5fc565"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cff917ec-6f86-49f8-abbf-480441735c7a", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032309964500}, "additional": {"logType": "detail", "children": [], "durationId": "e71f48c3-bb4e-410b-b52a-8c1fe2476980"}}, {"head": {"id": "abd36e6d-4753-47cd-91c1-42159ecf0d2b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032311419600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c53c69d9-3668-484a-b0d9-680164376d1e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032311568000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1aae19b-2837-452a-9b46-74dcede7ff59", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032311781800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2266e3d1-739a-4fa1-8a42-5cb2aa546d0b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032311906300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "832c2d71-9765-461e-b5b9-b481d807ed28", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032311974300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23a3e1bd-c8ab-407f-894b-98c13ebae8c9", "name": "entry : default@ConfigureCmake cost memory 0.0374755859375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032312064700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c629030c-ea13-4129-928e-426bcc6c7790", "name": "runTaskFromQueue task cost before running: 366 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032312165400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f716b3fb-eb6d-446b-8333-477d3e5fc565", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032311768800, "endTime": 145032312222300, "totalTime": 370300}, "additional": {"logType": "info", "children": [], "durationId": "e71f48c3-bb4e-410b-b52a-8c1fe2476980"}}, {"head": {"id": "c5677aa5-d7e7-4bcc-91aa-c0fc797ac607", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032317231200, "endTime": 145032320749900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1508fccb-935e-49c3-ae52-9cca6f4ce81b", "logId": "a6d8cb34-b04d-4a66-8762-0de67aba963b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1508fccb-935e-49c3-ae52-9cca6f4ce81b", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032314651600}, "additional": {"logType": "detail", "children": [], "durationId": "c5677aa5-d7e7-4bcc-91aa-c0fc797ac607"}}, {"head": {"id": "3c055574-06d8-4fc3-a068-3519ac5fd8c1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032316133200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fe978cb-dc20-4ac8-9f03-51e66f4e8236", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032316274600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7532b59-e65c-4a7d-9a64-dc6b699ef5a5", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032317248000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f2422b2-ebf6-4905-b526-c645d6b97b2c", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032320419800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57f33cf6-6e31-4907-9e1f-c527f09facaf", "name": "entry : default@MergeProfile cost memory 0.1185302734375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032320630700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6d8cb34-b04d-4a66-8762-0de67aba963b", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032317231200, "endTime": 145032320749900}, "additional": {"logType": "info", "children": [], "durationId": "c5677aa5-d7e7-4bcc-91aa-c0fc797ac607"}}, {"head": {"id": "5ed3a4f0-4a7c-490b-8f5c-fb79eeb07846", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032326655400, "endTime": 145032330869500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0785ad3a-6bea-4794-99a4-bba8063b9abf", "logId": "8d7b651b-acba-4564-ad8b-31e2d10fb57c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0785ad3a-6bea-4794-99a4-bba8063b9abf", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032323389400}, "additional": {"logType": "detail", "children": [], "durationId": "5ed3a4f0-4a7c-490b-8f5c-fb79eeb07846"}}, {"head": {"id": "4d13b74a-9e06-4af9-bbcb-1c3955b00cbb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032325166600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "974d6849-1efc-4dfd-aef9-b93e03f95680", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032325339300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fae71567-feaf-43ef-b53a-e051034e9a7e", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032326671100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e65f5bd4-d3a3-44db-b0b6-e74c39a3ee81", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032328177300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74a81522-ba73-4330-b5de-d69593f6f66b", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032330590700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "000f6f3a-5b2e-45cf-ae3e-e4af42532cfb", "name": "entry : default@CreateBuildProfile cost memory 0.10842132568359375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032330765800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d7b651b-acba-4564-ad8b-31e2d10fb57c", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032326655400, "endTime": 145032330869500}, "additional": {"logType": "info", "children": [], "durationId": "5ed3a4f0-4a7c-490b-8f5c-fb79eeb07846"}}, {"head": {"id": "6fccc580-b0ad-431a-9ac3-3467f4de19c8", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032337980800, "endTime": 145032338936900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "899354b2-101c-4377-8a05-e9aa12e52285", "logId": "aebed243-b5dd-495a-a3b9-4d526d35ce72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "899354b2-101c-4377-8a05-e9aa12e52285", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032333782800}, "additional": {"logType": "detail", "children": [], "durationId": "6fccc580-b0ad-431a-9ac3-3467f4de19c8"}}, {"head": {"id": "ef82e69f-af6b-4e60-983a-12feae66bf33", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032336192500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "462f44d3-4e5e-4efe-80b7-cbd7848c04d0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032336382800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8907979b-f0a6-424a-bfa4-d94a9ca0e79e", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032338001100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33a9bb3d-d850-47d6-81cc-46e39c24d0a3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032338193900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af0bd6b4-296f-454c-a9a6-4791a29da101", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032338291800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e379e35e-cf66-478b-9f07-dfbc1fa4f44d", "name": "entry : default@PreCheckSyscap cost memory 0.041107177734375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032338667600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a652f92-b20f-4f23-8607-d0287a1cb2ef", "name": "runTaskFromQueue task cost before running: 392 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032338833200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aebed243-b5dd-495a-a3b9-4d526d35ce72", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032337980800, "endTime": 145032338936900, "totalTime": 816700}, "additional": {"logType": "info", "children": [], "durationId": "6fccc580-b0ad-431a-9ac3-3467f4de19c8"}}, {"head": {"id": "e5048a28-649e-4af3-9946-1b21a4c7e04b", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032346314600, "endTime": 145032356189100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c371b9d6-8c25-4753-b1c6-9b9627c4cad3", "logId": "3d5bd4bb-3588-49cb-8787-80ac08423dba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c371b9d6-8c25-4753-b1c6-9b9627c4cad3", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032341778400}, "additional": {"logType": "detail", "children": [], "durationId": "e5048a28-649e-4af3-9946-1b21a4c7e04b"}}, {"head": {"id": "b6b7d1fe-b93a-4814-8329-45d0679cfd2f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032343762000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2cbf189-c22b-4a92-81ea-416a13be3c38", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032343891200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10908b32-c2a2-476a-a1e4-8d2165bfc2ba", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032346329700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b33a815-2653-422c-9c5d-e37df6537d36", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032354703500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e81a70ce-d831-40be-9f36-c0d4b8e35855", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032355940200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0589b286-62a0-4016-882b-d04db782e857", "name": "entry : default@GeneratePkgContextInfo cost memory 0.25026702880859375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032356086700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d5bd4bb-3588-49cb-8787-80ac08423dba", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032346314600, "endTime": 145032356189100}, "additional": {"logType": "info", "children": [], "durationId": "e5048a28-649e-4af3-9946-1b21a4c7e04b"}}, {"head": {"id": "dabfbfae-fbd7-487c-b8b6-b1322694cef7", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032373325500, "endTime": 145032377796400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "3c051ec0-da3a-475c-a29d-dedce0d5757e", "logId": "2921108c-3722-4d5a-a3ca-d4255a9bd81b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c051ec0-da3a-475c-a29d-dedce0d5757e", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032358800900}, "additional": {"logType": "detail", "children": [], "durationId": "dabfbfae-fbd7-487c-b8b6-b1322694cef7"}}, {"head": {"id": "4d4fd20e-5658-491f-9528-d7330d04f6c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032360710900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "520be34d-7ef0-49ee-bbd7-133385b45eab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032360844400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f91417c7-eb9c-46fc-a77e-7280e4925d1e", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032373356400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cab2a09-bc30-4447-a874-1036e9350a54", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032376959100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15cb4022-976e-4d20-98b3-65b9578e230e", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032377164400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9301a753-20d4-4be6-bfd5-3d1ec746c2b9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032377308100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "438be0be-6bcc-4534-9286-13057d3c90c4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032377399400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d71fb5e9-3964-40c5-ad27-7317255ffc81", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1214141845703125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032377542800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9161b18-2be8-4714-bf52-e166d0a8beca", "name": "runTaskFromQueue task cost before running: 431 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032377686700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2921108c-3722-4d5a-a3ca-d4255a9bd81b", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032373325500, "endTime": 145032377796400, "totalTime": 4334900}, "additional": {"logType": "info", "children": [], "durationId": "dabfbfae-fbd7-487c-b8b6-b1322694cef7"}}, {"head": {"id": "eefc1eab-51e5-4936-a7df-0f6fa22c4533", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032384774700, "endTime": 145032385114400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2cb68f38-7649-45b5-9fc6-4c7a22cc9fc8", "logId": "7590e607-9e02-4007-bc48-159f32a3ddc4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2cb68f38-7649-45b5-9fc6-4c7a22cc9fc8", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032381698100}, "additional": {"logType": "detail", "children": [], "durationId": "eefc1eab-51e5-4936-a7df-0f6fa22c4533"}}, {"head": {"id": "ae6a0d24-c965-461c-b419-88ad2dd73807", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032383801300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4fa820d-601c-45f9-b6d1-1bc3a319fa55", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032383956400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31e671a6-2f21-42fa-89d1-3f102c399562", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032384786300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "224ca92a-70d9-46db-bc33-9e89c01b7b4f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032384918700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6fb387f-d7e0-4937-bee7-6d0faff03480", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032384957900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21decc35-30af-4293-98e1-18abc5c620ec", "name": "entry : default@BuildNativeWithCmake cost memory 0.03852081298828125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032385013200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac376852-c939-48b6-a790-058de35ee70b", "name": "runTaskFromQueue task cost before running: 438 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032385070200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7590e607-9e02-4007-bc48-159f32a3ddc4", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032384774700, "endTime": 145032385114400, "totalTime": 278400}, "additional": {"logType": "info", "children": [], "durationId": "eefc1eab-51e5-4936-a7df-0f6fa22c4533"}}, {"head": {"id": "541093b6-feb9-4041-9f1e-74530b325e27", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032390461400, "endTime": 145032400021100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7dde6c85-99d2-43b2-a9d5-56232eaf338c", "logId": "cf26cbb5-1346-4d6d-9294-a828f696dc79"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7dde6c85-99d2-43b2-a9d5-56232eaf338c", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032386882900}, "additional": {"logType": "detail", "children": [], "durationId": "541093b6-feb9-4041-9f1e-74530b325e27"}}, {"head": {"id": "fbf2dd97-ec49-4d9d-8b05-4b22047bae6d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032388995200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02097507-fe52-4db9-aa1a-94fc13d9f6dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032389161200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e17a3004-8e28-42af-90ef-cb56fab6c232", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032390475900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46a35695-ef67-450e-82dc-147cd0a4f251", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032399681200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c39e0973-b8e1-4c72-86ff-0a53e1322ae1", "name": "entry : default@MakePackInfo cost memory -4.515411376953125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032399893000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf26cbb5-1346-4d6d-9294-a828f696dc79", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032390461400, "endTime": 145032400021100}, "additional": {"logType": "info", "children": [], "durationId": "541093b6-feb9-4041-9f1e-74530b325e27"}}, {"head": {"id": "37bbb8aa-12d0-46ce-9a3c-59f3934fe67c", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032408625200, "endTime": 145032417362700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f1729864-c85b-4063-b049-af78108d77a1", "logId": "714bbd9c-de09-4eb4-966a-ed02d7ef7727"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1729864-c85b-4063-b049-af78108d77a1", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032403898000}, "additional": {"logType": "detail", "children": [], "durationId": "37bbb8aa-12d0-46ce-9a3c-59f3934fe67c"}}, {"head": {"id": "409014d5-d8f0-4e09-b255-e96d19889701", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032405539300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6d7f027-fd48-4e72-abbd-015513a6aaa5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032405716500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d57ef0b0-0e92-4a9b-af51-7478ebbd1072", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032408647200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed5da3a6-c12c-43e9-8150-bc0edeed805c", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032409280700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e6987f2-77c9-40a0-8ff9-c2f580fc00e4", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032410932900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a61e5e3a-ccdb-4d05-bbbf-e1fcf03e03a4", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032416636700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e98524c5-2d1b-4ae7-893d-88e56eecc803", "name": "entry : default@SyscapTransform cost memory 0.151580810546875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032417128200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "714bbd9c-de09-4eb4-966a-ed02d7ef7727", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032408625200, "endTime": 145032417362700}, "additional": {"logType": "info", "children": [], "durationId": "37bbb8aa-12d0-46ce-9a3c-59f3934fe67c"}}, {"head": {"id": "9ec91420-9453-4ea0-9de2-b83509c288ae", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032425396100, "endTime": 145032437848900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "452809a3-82a7-4ec8-ae3f-b4c3f380acfa", "logId": "8347a675-d7bc-4484-a76f-bbd4f2b767d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "452809a3-82a7-4ec8-ae3f-b4c3f380acfa", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032420048100}, "additional": {"logType": "detail", "children": [], "durationId": "9ec91420-9453-4ea0-9de2-b83509c288ae"}}, {"head": {"id": "90c073be-945c-4dbf-bbc9-43b25e9827a8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032422670500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddc19a30-ff4b-4644-b4a6-c778beeb650a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032422863800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfbe7cb6-e826-44c6-b904-aa3f7d6172bc", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032425418000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4730272-be01-41dc-b4ac-895d6ab791d1", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032437472400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84554021-719c-4834-b124-d3e9b5ae177d", "name": "entry : default@ProcessProfile cost memory -5.9813385009765625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032437700700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8347a675-d7bc-4484-a76f-bbd4f2b767d9", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032425396100, "endTime": 145032437848900}, "additional": {"logType": "info", "children": [], "durationId": "9ec91420-9453-4ea0-9de2-b83509c288ae"}}, {"head": {"id": "9c9f9cf7-2f61-4f58-953e-e97cc88b1d92", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032447220600, "endTime": 145032460703000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c0753502-2c13-442d-aab3-a1409cb6130e", "logId": "9c547c63-055a-41f4-b8c1-995771ee0663"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0753502-2c13-442d-aab3-a1409cb6130e", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032441169100}, "additional": {"logType": "detail", "children": [], "durationId": "9c9f9cf7-2f61-4f58-953e-e97cc88b1d92"}}, {"head": {"id": "b573dea6-5ade-40da-a1ad-58f906c7a0d2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032443493400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56d4f3f0-a2f9-45a7-b428-deb72a9a739a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032443673600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3ec33a4-718a-4419-bba2-4cd77dee300a", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032447254900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43337f4e-3325-4294-8921-a598fbba0eec", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032460349700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dc9dc91-4b30-43d9-aaeb-82c92f259c68", "name": "entry : default@ProcessRouterMap cost memory 0.2512969970703125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032460568200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c547c63-055a-41f4-b8c1-995771ee0663", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032447220600, "endTime": 145032460703000}, "additional": {"logType": "info", "children": [], "durationId": "9c9f9cf7-2f61-4f58-953e-e97cc88b1d92"}}, {"head": {"id": "7247ab25-3c7a-4ece-a9c9-2bdf9abd2209", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032469224100, "endTime": 145032480218200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "2464a7c0-6582-4d0d-a2f1-04292d4505d2", "logId": "57cbffee-229f-49b8-8f6c-358e18433645"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2464a7c0-6582-4d0d-a2f1-04292d4505d2", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032466480800}, "additional": {"logType": "detail", "children": [], "durationId": "7247ab25-3c7a-4ece-a9c9-2bdf9abd2209"}}, {"head": {"id": "a3b220e5-63ae-4923-b547-fa6cf2d354dc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032468859000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dbe1eba-9a64-4026-a4cd-23e11b094d7e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032469046600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "595afdc7-0110-4639-8773-0e23717328b4", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032469237200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "696f40ab-ecec-448c-9abc-2ffd5df03ad3", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032469475200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "647b6d45-5a48-4b21-94d8-8bc72e34cf1e", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032477204100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b132a25e-e79b-43a1-a51d-ee31ea47dd9d", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032477418000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04b673e8-ebd3-410b-ac61-891c3d9c95b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032477558700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1445f69c-3e5f-4748-b74d-6ddcd58d3382", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032477646700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8f89b71-d515-4609-90b3-c6ed2f95eb57", "name": "entry : default@ProcessStartupConfig cost memory 0.2786865234375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032479937200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd53d831-a165-458e-b409-b1fcd2a2b838", "name": "runTaskFromQueue task cost before running: 533 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032480123300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57cbffee-229f-49b8-8f6c-358e18433645", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032469224100, "endTime": 145032480218200, "totalTime": 10860400}, "additional": {"logType": "info", "children": [], "durationId": "7247ab25-3c7a-4ece-a9c9-2bdf9abd2209"}}, {"head": {"id": "a231b90c-9d38-4f9a-96e5-afc8ff340108", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032488313300, "endTime": 145032490397900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c8fd180d-b6a2-4e0b-a3ab-67ab31fb50b8", "logId": "6db33606-2d27-4bcd-b081-5847c8fa3f3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8fd180d-b6a2-4e0b-a3ab-67ab31fb50b8", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032484975700}, "additional": {"logType": "detail", "children": [], "durationId": "a231b90c-9d38-4f9a-96e5-afc8ff340108"}}, {"head": {"id": "5139e3be-c291-474e-9eb7-3844c64edcc9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032486871700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d12dc6f5-6595-4e93-ba8d-ab3dc3d5b5c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032487008900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17baf14f-c68f-42f4-a36c-78e7808ea55e", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032488328900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9998addc-fcd8-404c-9971-1ef46a28649a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032488500100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f6ede6e-dd5d-4d4e-9743-9ce313a2d680", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032488587900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0297a94c-cea1-425d-a8a6-150b6d53fcc4", "name": "entry : default@BuildNativeWithNinja cost memory 0.05853271484375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032490154900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2bd636c-1208-4ec3-8bf1-57bf45464abb", "name": "runTaskFromQueue task cost before running: 544 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032490311300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6db33606-2d27-4bcd-b081-5847c8fa3f3b", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032488313300, "endTime": 145032490397900, "totalTime": 1966400}, "additional": {"logType": "info", "children": [], "durationId": "a231b90c-9d38-4f9a-96e5-afc8ff340108"}}, {"head": {"id": "03eb1e5c-ab20-4326-9bd6-584e197ef3d3", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032500779900, "endTime": 145032510201800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ce982394-4a8c-4392-88dc-7ab561edf40c", "logId": "d6716331-c2cc-42ca-945d-1f2ed9c1f7a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce982394-4a8c-4392-88dc-7ab561edf40c", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032494333200}, "additional": {"logType": "detail", "children": [], "durationId": "03eb1e5c-ab20-4326-9bd6-584e197ef3d3"}}, {"head": {"id": "ab15429c-e6e6-4ac0-89cc-9807fbf950dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032496150300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34ef58b0-55cc-4d4b-8201-efe9eb6b93c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032496336200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79996d2f-5ba6-4ec2-996f-c63cb5413f0e", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032498514900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e7c1eb0-30b5-4c07-ba8e-f7f77a6c3143", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032504006500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5830765-469f-4494-a42f-0ed8ea426fb7", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032507574200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91e931a6-a436-458e-85b9-e15fceabaa7f", "name": "entry : default@ProcessResource cost memory 0.16536712646484375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032507720500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6716331-c2cc-42ca-945d-1f2ed9c1f7a5", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032500779900, "endTime": 145032510201800}, "additional": {"logType": "info", "children": [], "durationId": "03eb1e5c-ab20-4326-9bd6-584e197ef3d3"}}, {"head": {"id": "7dc1d495-441e-4471-9caa-b3207ceaef63", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032523447100, "endTime": 145032542416400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "39da00a6-648d-49e3-a2c3-dca58bd87e11", "logId": "6c379d40-5eb9-475e-af63-85faf79aba4d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39da00a6-648d-49e3-a2c3-dca58bd87e11", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032515785300}, "additional": {"logType": "detail", "children": [], "durationId": "7dc1d495-441e-4471-9caa-b3207ceaef63"}}, {"head": {"id": "ffdecd5d-238f-4889-8ec3-703d0c4cbfaa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032517370900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b9e1c28-d58f-4052-9498-6b6f502dd227", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032517488700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f65a92f2-a483-409a-ac8f-8ae6a34c782c", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032523465100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "751fd908-3da2-4711-8a2f-6c8f85c8d8f6", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032542254700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ae5febd-a921-4e11-8496-3e6f54a5b1ca", "name": "entry : default@GenerateLoaderJson cost memory 0.8922348022460938", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032542366700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c379d40-5eb9-475e-af63-85faf79aba4d", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032523447100, "endTime": 145032542416400}, "additional": {"logType": "info", "children": [], "durationId": "7dc1d495-441e-4471-9caa-b3207ceaef63"}}, {"head": {"id": "0c9f7ca6-37d1-4db3-8e26-af3135d44e2e", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032549899500, "endTime": 145032554527200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "53533d49-9662-4439-8dc7-63f64259a508", "logId": "a41d44ae-4071-4f90-9c17-7f20530b5918"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53533d49-9662-4439-8dc7-63f64259a508", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032548257600}, "additional": {"logType": "detail", "children": [], "durationId": "0c9f7ca6-37d1-4db3-8e26-af3135d44e2e"}}, {"head": {"id": "f1f4b526-d6fd-4b50-8399-7ec633f3236f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032549184300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44cbc095-c451-4da1-9199-48666a97feb2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032549268700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f07c1b9a-3fbb-4888-a68c-872588f5c539", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032549907700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60287554-7299-4b77-ac5d-ff6141776ecd", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032554353200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fb4df97-dab0-46bc-99dc-6a1b5244bd01", "name": "entry : default@ProcessLibs cost memory 0.14470672607421875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032554472100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a41d44ae-4071-4f90-9c17-7f20530b5918", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032549899500, "endTime": 145032554527200}, "additional": {"logType": "info", "children": [], "durationId": "0c9f7ca6-37d1-4db3-8e26-af3135d44e2e"}}, {"head": {"id": "f1f7e9ad-9538-4acf-8561-bb387889c6f2", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032560140100, "endTime": 145032584208600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "a4d7fb6a-db16-45fc-8914-5cfcdc65495e", "logId": "ebc5a449-1b2d-4544-b9ef-f529b4caeb79"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4d7fb6a-db16-45fc-8914-5cfcdc65495e", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032556396600}, "additional": {"logType": "detail", "children": [], "durationId": "f1f7e9ad-9538-4acf-8561-bb387889c6f2"}}, {"head": {"id": "f42b80e4-6fa8-4c81-a3ee-e59d90237f71", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032557135900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcf8447d-ebab-4bd9-ac3d-4370ac36e6b2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032557211600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92b8caa7-1a86-4fb9-bf72-bd981f6152bd", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032558153400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cf186ed-d000-4aa5-a15c-9f4e0797c5e4", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032560239100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "685e38f7-aa4c-4106-82ba-dfc8c13a3b88", "name": "Incremental task entry:default@CompileResource pre-execution cost: 23 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032583972800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a8167ba-c022-404d-9eaa-df1a949ebedb", "name": "entry : default@CompileResource cost memory 1.3250198364257812", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032584124300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebc5a449-1b2d-4544-b9ef-f529b4caeb79", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032560140100, "endTime": 145032584208600}, "additional": {"logType": "info", "children": [], "durationId": "f1f7e9ad-9538-4acf-8561-bb387889c6f2"}}, {"head": {"id": "481d06e2-e4af-4250-98c9-8a0273fa5845", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032590109100, "endTime": 145032592038700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "65cdd5b7-9df8-42fb-a367-74b330834044", "logId": "eb4249ed-a9c3-40e3-b654-f6f87abff2a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65cdd5b7-9df8-42fb-a367-74b330834044", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032586677900}, "additional": {"logType": "detail", "children": [], "durationId": "481d06e2-e4af-4250-98c9-8a0273fa5845"}}, {"head": {"id": "d28f285e-0667-4c02-86cc-724aa20d5741", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032587687600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26974d42-7bb4-496f-b4f7-3902e4ca2aed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032587793900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb435a5c-bee5-4e9f-9ae8-db88e61958da", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032590118300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbffb701-74ba-478d-94d2-e0ed1d89be1b", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032590635100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5bb9656-4b43-4554-9dd3-c266e7ef30f7", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032591898900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dddb5a78-7308-41e9-84c6-b2a9acd6db72", "name": "entry : default@DoNativeStrip cost memory 0.08510589599609375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032591985900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb4249ed-a9c3-40e3-b654-f6f87abff2a6", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032590109100, "endTime": 145032592038700}, "additional": {"logType": "info", "children": [], "durationId": "481d06e2-e4af-4250-98c9-8a0273fa5845"}}, {"head": {"id": "5b9a2c6f-37c5-4f5f-a3fe-348a6b0327f3", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032597946400, "endTime": 145032623380800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "8ec8bdfe-de57-46a6-a3a0-ebfdd6459468", "logId": "5ab33d43-7103-4fa0-804a-949fd9a7a3bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ec8bdfe-de57-46a6-a3a0-ebfdd6459468", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032593534000}, "additional": {"logType": "detail", "children": [], "durationId": "5b9a2c6f-37c5-4f5f-a3fe-348a6b0327f3"}}, {"head": {"id": "0b1d1593-d088-403e-8054-799c519ea0bf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032594288100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a99433e6-1f34-476a-8303-e474c944f3b5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032594372600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f23d8e7-6766-4d65-a970-16a2546bade2", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032597960300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcbfc316-1323-4617-b83d-245af273b1d3", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032598133600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb80d0e8-0c2b-4957-a97d-82f1b49cca3a", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 19 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032623176600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c157b5d6-7ae6-44f6-9efa-0de85d3ab751", "name": "entry : default@CompileArkTS cost memory 1.1842727661132812", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032623314000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ab33d43-7103-4fa0-804a-949fd9a7a3bf", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032597946400, "endTime": 145032623380800}, "additional": {"logType": "info", "children": [], "durationId": "5b9a2c6f-37c5-4f5f-a3fe-348a6b0327f3"}}, {"head": {"id": "5e421f6f-7516-4b5f-8fdc-cf36e62f7e19", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032634280200, "endTime": 145032642062700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "1ca2e344-c469-48d7-9127-3a2fd813c792", "logId": "c57c3e85-9729-4c22-9aa4-8bddd25f0b3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ca2e344-c469-48d7-9127-3a2fd813c792", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032629445000}, "additional": {"logType": "detail", "children": [], "durationId": "5e421f6f-7516-4b5f-8fdc-cf36e62f7e19"}}, {"head": {"id": "d3f955c4-2851-4c14-9ee2-e2ddf847b855", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032630425100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38c5970f-8bc0-45d4-b24f-ebf7a7c5e86b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032630511400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c95c0ac8-e5ad-46c5-9fa7-6c766bafc7df", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032634292400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22ac3190-3aa4-4f0c-b183-e42a58b6bc01", "name": "entry : default@BuildJS cost memory 0.404693603515625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032641870200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "179e31a0-9fc8-4eda-b96c-be18da65a4f6", "name": "runTaskFromQueue task cost before running: 695 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032642012000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c57c3e85-9729-4c22-9aa4-8bddd25f0b3f", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032634280200, "endTime": 145032642062700, "totalTime": 7710200}, "additional": {"logType": "info", "children": [], "durationId": "5e421f6f-7516-4b5f-8fdc-cf36e62f7e19"}}, {"head": {"id": "83fe3ea3-b3cf-43fa-b129-c91aebc4d692", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032648052900, "endTime": 145032651073800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d56f9664-9f18-484d-84ee-494c907e357a", "logId": "9318fae2-e41a-48c2-9800-8bae6e53dc7e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d56f9664-9f18-484d-84ee-494c907e357a", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032643723100}, "additional": {"logType": "detail", "children": [], "durationId": "83fe3ea3-b3cf-43fa-b129-c91aebc4d692"}}, {"head": {"id": "d4d6831d-959f-429a-8e2f-edd091f20a2c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032644704100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d51ef5b-7745-4330-98da-50a1074e1bcb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032644956000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36f75ce8-e7cc-4f10-b954-e99571ede3fc", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032648061800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15aff5f8-eb71-42f1-ba86-1580956e7a98", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032648842800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17bbc656-7442-4818-a158-47395c807693", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032650892000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26648a80-0ae8-4cab-aaf4-cb4868447916", "name": "entry : default@CacheNativeLibs cost memory 0.097320556640625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032651009800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9318fae2-e41a-48c2-9800-8bae6e53dc7e", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032648052900, "endTime": 145032651073800}, "additional": {"logType": "info", "children": [], "durationId": "83fe3ea3-b3cf-43fa-b129-c91aebc4d692"}}, {"head": {"id": "01904ddb-d95e-4908-b94c-7208b968d5fe", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032654609700, "endTime": 145032655792600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "eafaf400-ba50-4f7a-a566-99d7851eb142", "logId": "7cfdc7e9-15a4-474e-950a-b397cf29afaf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eafaf400-ba50-4f7a-a566-99d7851eb142", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032652682800}, "additional": {"logType": "detail", "children": [], "durationId": "01904ddb-d95e-4908-b94c-7208b968d5fe"}}, {"head": {"id": "18cd1781-2dcd-4f06-99eb-01221e0ee48b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032653633700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44d79631-1866-46b0-880c-9496f77afac1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032653724900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb1ff739-8e92-4434-a950-3af5a6c196f2", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032654620500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f53d58c-a107-4a4e-a716-1d683d77ffed", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032654899500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0f57066-3c42-4ca5-b467-e66deaf96d9d", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032655639300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdbd93b4-53b4-452f-a087-01dad35d0754", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07822418212890625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032655730700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cfdc7e9-15a4-474e-950a-b397cf29afaf", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032654609700, "endTime": 145032655792600}, "additional": {"logType": "info", "children": [], "durationId": "01904ddb-d95e-4908-b94c-7208b968d5fe"}}, {"head": {"id": "11c97bae-3822-4a40-9df9-f576a0141ab2", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032665817300, "endTime": 145032683209000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "097a9c66-11bd-42c6-9e01-67d780393c00", "logId": "9b0b6b28-f046-4ca4-9ab0-a21af829a2cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "097a9c66-11bd-42c6-9e01-67d780393c00", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032657680400}, "additional": {"logType": "detail", "children": [], "durationId": "11c97bae-3822-4a40-9df9-f576a0141ab2"}}, {"head": {"id": "b1a43a6f-2e65-43bb-a365-ec26c9e28a46", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032658645100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0bcef5e-8188-4cd4-9d9a-5c58593f4159", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032658744600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30f28da6-5105-4101-bf27-5da143e6ee95", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032665829600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d780d6f9-19e8-4e76-9e1e-6e0be2b8336b", "name": "Incremental task entry:default@PackageHap pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032683012200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4616355-4066-41ef-8734-4bc202c8ad60", "name": "entry : default@PackageHap cost memory 0.9741897583007812", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032683154200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b0b6b28-f046-4ca4-9ab0-a21af829a2cb", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032665817300, "endTime": 145032683209000}, "additional": {"logType": "info", "children": [], "durationId": "11c97bae-3822-4a40-9df9-f576a0141ab2"}}, {"head": {"id": "c3ba4271-abb3-4b7a-a513-23e2e10b230f", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032689169000, "endTime": 145032691336000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": [], "detailId": "d89c370b-a0b9-41bb-9ace-7ace4dbb441f", "logId": "a30ac83c-2c5f-4e7a-9174-392453703c43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d89c370b-a0b9-41bb-9ace-7ace4dbb441f", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032686339200}, "additional": {"logType": "detail", "children": [], "durationId": "c3ba4271-abb3-4b7a-a513-23e2e10b230f"}}, {"head": {"id": "d99238bc-5c6c-48e9-ab2a-bdcf451f580d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032687138600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff4532a4-6680-400a-aad2-5911ad7c4390", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032687231300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7f1f7c6-371d-4f70-b9b6-51501bdbab83", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032689180500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58a083bd-8acf-4558-a575-ab72fb4b150e", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032691170300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5b7db01-0fdb-4f97-8fbf-e8628d856459", "name": "entry : default@SignHap cost memory 0.1100616455078125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032691282100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a30ac83c-2c5f-4e7a-9174-392453703c43", "name": "UP-TO-DATE :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032689169000, "endTime": 145032691336000}, "additional": {"logType": "info", "children": [], "durationId": "c3ba4271-abb3-4b7a-a513-23e2e10b230f"}}, {"head": {"id": "ebdf5394-ffda-4c69-a4e8-6553bca3df0d", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032694556500, "endTime": 145032699329300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "62b95f34-f2a7-4ccc-bf88-8af61513fa1e", "logId": "59c08778-4d9a-4a2b-b173-01d58381d6ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62b95f34-f2a7-4ccc-bf88-8af61513fa1e", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032692776500}, "additional": {"logType": "detail", "children": [], "durationId": "ebdf5394-ffda-4c69-a4e8-6553bca3df0d"}}, {"head": {"id": "84f2f805-406a-4503-90f6-e728f715bd03", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032693609900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4cd3e12-0809-49f4-8a80-146e8a885442", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032693695200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89947a61-de25-4886-ac19-c1f5ef93b177", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032694566000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06f3cf48-282b-4932-a967-159bf2539ce3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032699057100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4ec1035-cb8a-4960-9b32-fccafecc3020", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032699159400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5943ca24-1709-43f1-aee1-c1f2f3f6d54b", "name": "entry : default@CollectDebugSymbol cost memory 0.251129150390625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032699226300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f7278bb-6d3a-481e-8eb5-ba821ae7dfb6", "name": "runTaskFromQueue task cost before running: 753 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032699295300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59c08778-4d9a-4a2b-b173-01d58381d6ec", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032694556500, "endTime": 145032699329300, "totalTime": 4722000}, "additional": {"logType": "info", "children": [], "durationId": "ebdf5394-ffda-4c69-a4e8-6553bca3df0d"}}, {"head": {"id": "895e9d52-3476-4440-bfe0-5793e1bbbdbc", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032701033800, "endTime": 145032702474600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "f36f0397-cdb4-4f09-8c8e-f00bfa8e1e8f", "logId": "06474cb1-f3f7-4c7c-b97c-a47bc69725a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f36f0397-cdb4-4f09-8c8e-f00bfa8e1e8f", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032700932600}, "additional": {"logType": "detail", "children": [], "durationId": "895e9d52-3476-4440-bfe0-5793e1bbbdbc"}}, {"head": {"id": "183412b1-50a8-4826-a49f-c9cb89857bf2", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032701132800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce9cb362-7241-46a7-bd86-4c5df1647cca", "name": "entry : assembleHap cost memory 0.01184844970703125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032701604900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eab9123e-503d-4134-abc3-7ace83fb7e65", "name": "runTaskFromQueue task cost before running: 755 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032702061500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06474cb1-f3f7-4c7c-b97c-a47bc69725a6", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032701033800, "endTime": 145032702474600, "totalTime": 787900}, "additional": {"logType": "info", "children": [], "durationId": "895e9d52-3476-4440-bfe0-5793e1bbbdbc"}}, {"head": {"id": "18a4917e-98db-4b7f-8f07-bae20d276c7c", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032716790600, "endTime": 145032716836800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d8985a18-7429-4bb8-98f0-818cc537696b", "logId": "fd75d091-1e6c-48ed-9011-2996ebdb0e2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd75d091-1e6c-48ed-9011-2996ebdb0e2e", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032716790600, "endTime": 145032716836800}, "additional": {"logType": "info", "children": [], "durationId": "18a4917e-98db-4b7f-8f07-bae20d276c7c"}}, {"head": {"id": "544c14ef-9096-42d3-b29a-391ce986fca4", "name": "BUILD SUCCESSFUL in 770 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032716887200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "f75af27c-2ed3-483e-bde3-4e1d39876a61", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145031947110300, "endTime": 145032717142000}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 14, "second": 22}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "0a1b9a4a-eba0-47bd-9291-458d7fcbc9d0", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032717171300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6b3cfd0-7414-401b-8d37-15f2733e9961", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032717236500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "287dedb5-8247-4fb4-bcb3-4ec965051a12", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032717639000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "977dfdad-a2dd-4ba6-bf5f-a98ecbfb3ddb", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032717726500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8ea45a3-aa87-4c96-bd5d-79b50667af01", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032717774000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9391dc1-ff85-4c01-91bb-03d042b52606", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032717809700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3cf5490-64b7-43f8-9c0d-a84451858141", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032717844200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e9f1979-dd8e-44ea-9868-c1f822b4e150", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032718515400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d03ca00d-2104-4daf-b0f0-af2de91c2c3f", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032718785600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b19ac778-58c9-43aa-8979-97b9b6b23e9a", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032718845300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "362e48f6-7c01-4121-aca9-75748c2a13de", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032718880300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df9930e0-60ac-4f6a-a05e-44e27be5c0fa", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032718916400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dbac50e-146d-444b-b0bc-f7464849fb8f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032718943500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb1a9fa3-c3ca-4607-bf6f-5ff4ad9999fd", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032720010600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14a91472-4a02-43fb-9c48-5c57006a53f8", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032720303700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24ccbf64-b884-45d6-bf70-6172bb6f736b", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032720538800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3d78319-1e88-4cd3-9900-b996ee30fdc3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032720600100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99274064-6029-42f7-a98c-7d2ad397be75", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032720637200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11e8a48f-e518-467a-9690-66c32f8e9812", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032720670100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb67dcfa-5cfb-45c8-883c-6c25a3eb9c7b", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032720700000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9756074a-78ae-4fc3-83c7-1f77ff48e60a", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032720738900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9063117b-f7a4-48ae-bb7c-7dd054201b14", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032720771800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d77bcd-0c71-4cff-b598-21697c44a19f", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032722833400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3f6a84a-b98d-4838-9c98-e9aee1b6aaa0", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032723505600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd70d128-1e86-4e9d-9bb9-997ad887219d", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032723958300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da8f11b0-120a-4387-b8b6-c3ab79914f86", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032724209700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24f2bbc6-fc3c-4e35-bbd8-1f94788e6081", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032724420700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "620f853b-7703-4ea3-b94e-3645f79deafe", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032725125200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a19dd94f-d5c1-4343-9aaa-e35b70707537", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032726132400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3585c9c-d155-4896-8343-878ec8ec676e", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032726402900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8489a77b-6bba-463e-bf2e-67cae6d4b0d4", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032726465700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "786a8d4d-66fa-4fbd-b9d8-9f68f5e6c803", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032726505500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55523a2c-c948-4f7e-96e5-b07683ea88bf", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032726540700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "581aca60-9178-4a3a-a49b-d1e62c314eab", "name": "There is no need to refresh cache, since the incremental task entry:default@SignHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032726580100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6888482d-98b2-4699-87c8-6d05fa5c530f", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032729332600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a981cef-27e6-4f1f-ad2c-3de76cdc8194", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032729674700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2524451e-795e-452c-9682-6e63e95a26fb", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032730194200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82ae6317-0e54-45cb-a82c-d0d1ffe5797c", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032730473000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}