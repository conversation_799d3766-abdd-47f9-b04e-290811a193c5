if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface NavigationBar_Params {
    navItems?: NavItem[];
    currentIndex?: number;
    showLabels?: boolean;
    isBottomNav?: boolean;
    onItemClick?: (index: number) => void;
    deviceUtils?;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
/**
 * 导航项
 */
export interface NavItem {
    label: string;
    icon: Resource | string;
    activeIcon?: Resource | string;
    route: string;
}
export class NavigationBar extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__navItems = new SynchedPropertyObjectOneWayPU(params.navItems, this, "navItems");
        this.__currentIndex = new SynchedPropertySimpleOneWayPU(params.currentIndex, this, "currentIndex");
        this.__showLabels = new SynchedPropertySimpleOneWayPU(params.showLabels, this, "showLabels");
        this.__isBottomNav = new SynchedPropertySimpleOneWayPU(params.isBottomNav, this, "isBottomNav");
        this.onItemClick = undefined;
        this.deviceUtils = DeviceUtils.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: NavigationBar_Params) {
        if (params.currentIndex === undefined) {
            this.__currentIndex.set(0);
        }
        if (params.showLabels === undefined) {
            this.__showLabels.set(true);
        }
        if (params.isBottomNav === undefined) {
            this.__isBottomNav.set(false);
        }
        if (params.onItemClick !== undefined) {
            this.onItemClick = params.onItemClick;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
    }
    updateStateVars(params: NavigationBar_Params) {
        this.__navItems.reset(params.navItems);
        this.__currentIndex.reset(params.currentIndex);
        this.__showLabels.reset(params.showLabels);
        this.__isBottomNav.reset(params.isBottomNav);
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__navItems.purgeDependencyOnElmtId(rmElmtId);
        this.__currentIndex.purgeDependencyOnElmtId(rmElmtId);
        this.__showLabels.purgeDependencyOnElmtId(rmElmtId);
        this.__isBottomNav.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__navItems.aboutToBeDeleted();
        this.__currentIndex.aboutToBeDeleted();
        this.__showLabels.aboutToBeDeleted();
        this.__isBottomNav.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __navItems: SynchedPropertySimpleOneWayPU<NavItem[]>;
    get navItems() {
        return this.__navItems.get();
    }
    set navItems(newValue: NavItem[]) {
        this.__navItems.set(newValue);
    }
    private __currentIndex: SynchedPropertySimpleOneWayPU<number>;
    get currentIndex() {
        return this.__currentIndex.get();
    }
    set currentIndex(newValue: number) {
        this.__currentIndex.set(newValue);
    }
    private __showLabels: SynchedPropertySimpleOneWayPU<boolean>;
    get showLabels() {
        return this.__showLabels.get();
    }
    set showLabels(newValue: boolean) {
        this.__showLabels.set(newValue);
    }
    private __isBottomNav: SynchedPropertySimpleOneWayPU<boolean>;
    get isBottomNav() {
        return this.__isBottomNav.get();
    }
    set isBottomNav(newValue: boolean) {
        this.__isBottomNav.set(newValue);
    }
    private onItemClick?: (index: number) => void;
    private deviceUtils;
    /**
     * 底部导航栏
     */
    private BottomNavBar(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
            Row.height(64);
            Row.padding({ left: 8, right: 8 });
            Row.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Row.shadow({
                radius: 8,
                color: { "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" },
                offsetX: 0,
                offsetY: -2
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const item = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.width('100%');
                    Column.height('100%');
                    Column.justifyContent(FlexAlign.Center);
                    Column.alignItems(HorizontalAlign.Center);
                    Column.layoutWeight(1);
                    Column.padding({ top: 8, bottom: 8 });
                    Column.onClick(() => {
                        this.onItemClick?.(index);
                    });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    // 统一使用Image组件显示图标
                    Image.create(this.currentIndex === index ? (item.activeIcon || item.icon) : item.icon);
                    // 统一使用Image组件显示图标
                    Image.width(24);
                    // 统一使用Image组件显示图标
                    Image.height(24);
                    // 统一使用Image组件显示图标
                    Image.objectFit(ImageFit.Contain);
                    // 统一使用Image组件显示图标
                    Image.fillColor(this.currentIndex === index ? { "id": 125829186, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } : { "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    If.create();
                    if (this.showLabels) {
                        this.ifElseBranchUpdateFunction(0, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(item.label);
                                Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                                Text.fontColor(this.currentIndex === index ? { "id": 125829186, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } : { "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                                Text.margin({ top: 4 });
                                Text.maxLines(1);
                                Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                            }, Text);
                            Text.pop();
                        });
                    }
                    else {
                        this.ifElseBranchUpdateFunction(1, () => {
                        });
                    }
                }, If);
                If.pop();
                Column.pop();
            };
            this.forEachUpdateFunction(elmtId, this.navItems, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        Row.pop();
    }
    /**
     * 侧边导航栏（左侧竖直布局）
     */
    private SideNavBar(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width(this.deviceUtils.getSidebarWidth());
            Column.height('100%');
            Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Column.shadow({
                radius: 4,
                color: { "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" },
                offsetX: 1,
                offsetY: 0
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 应用标志
            Column.create();
            // 应用标志
            Column.width('100%');
            // 应用标志
            Column.padding({ top: 16, bottom: 8, left: 8, right: 8 });
            // 应用标志
            Column.justifyContent(FlexAlign.Center);
            // 应用标志
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777243, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(28);
            Image.height(28);
            Image.objectFit(ImageFit.Contain);
            Image.fillColor({ "id": 125829186, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.margin({ bottom: 4 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('NexusHub');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.textAlign(TextAlign.Center);
            Text.maxLines(1);
        }, Text);
        Text.pop();
        // 应用标志
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 导航项
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const item = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.width('100%');
                    Column.padding({ top: 10, bottom: 10, left: 6, right: 6 });
                    Column.justifyContent(FlexAlign.Center);
                    Column.alignItems(HorizontalAlign.Center);
                    Column.backgroundColor(this.currentIndex === index ? { "id": 125829186, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } : 'transparent');
                    Column.borderRadius(Constants.BORDER_RADIUS.SMALL);
                    Column.margin({ bottom: 3 });
                    Column.onClick(() => {
                        if (this.currentIndex !== index) {
                            this.onItemClick?.(index);
                        }
                    });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    // 统一使用Image组件显示图标
                    Image.create(this.currentIndex === index ? (item.activeIcon || item.icon) : item.icon);
                    // 统一使用Image组件显示图标
                    Image.width(20);
                    // 统一使用Image组件显示图标
                    Image.height(20);
                    // 统一使用Image组件显示图标
                    Image.objectFit(ImageFit.Contain);
                    // 统一使用Image组件显示图标
                    Image.fillColor(this.currentIndex === index ? { "id": 125829186, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } : { "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                    // 统一使用Image组件显示图标
                    Image.margin({ bottom: 3 });
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    If.create();
                    if (this.showLabels) {
                        this.ifElseBranchUpdateFunction(0, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(item.label);
                                Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                                Text.fontColor(this.currentIndex === index ? { "id": 125829186, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } : { "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                                Text.textAlign(TextAlign.Center);
                                Text.maxLines(1);
                                Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                            }, Text);
                            Text.pop();
                        });
                    }
                    else {
                        this.ifElseBranchUpdateFunction(1, () => {
                        });
                    }
                }, If);
                If.pop();
                Column.pop();
            };
            this.forEachUpdateFunction(elmtId, this.navItems, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        // 导航项
        ForEach.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 版本信息
            Text.create('v1.0.0');
            // 版本信息
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            // 版本信息
            Text.fontColor({ "id": 125829240, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 版本信息
            Text.textAlign(TextAlign.Center);
            // 版本信息
            Text.padding({ bottom: 8 });
        }, Text);
        // 版本信息
        Text.pop();
        Column.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isBottomNav) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.BottomNavBar.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.SideNavBar.bind(this)();
                });
            }
        }, If);
        If.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
