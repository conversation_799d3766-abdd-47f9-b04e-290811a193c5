"use strict";
import {
  createIntl
} from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/react-intl@3.12.1_react@18.3.1/node_modules/react-intl";
import { getPluginManager } from "../core/plugin";
import EventEmitter from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/event-emitter@0.3.5/node_modules/event-emitter";
import warning from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/warning@4.0.3/node_modules/warning";
export {
  createIntl
};
export {
  FormattedDate,
  FormattedDateParts,
  FormattedDisplayName,
  FormattedHTMLMessage,
  FormattedList,
  FormattedMessage,
  FormattedNumber,
  FormattedNumberParts,
  FormattedPlural,
  FormattedRelativeTime,
  FormattedTime,
  FormattedTimeParts,
  IntlContext,
  IntlProvider,
  RawIntlProvider,
  createIntlCache,
  defineMessages,
  injectIntl,
  useIntl
} from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/react-intl@3.12.1_react@18.3.1/node_modules/react-intl";
let g_intl;
const useLocalStorage = true;
export const event = new EventEmitter();
export const LANG_CHANGE_EVENT = Symbol("LANG_CHANGE");
import bnBD0 from "antd/es/locale/bn_BD";
import lang_bnBD0 from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/src/locales/bn-BD.ts";
import enUS0 from "antd/es/locale/en_US";
import lang_enUS0 from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/src/locales/en-US.ts";
import faIR0 from "antd/es/locale/fa_IR";
import lang_faIR0 from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/src/locales/fa-IR.ts";
import idID0 from "antd/es/locale/id_ID";
import lang_idID0 from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/src/locales/id-ID.ts";
import jaJP0 from "antd/es/locale/ja_JP";
import lang_jaJP0 from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/src/locales/ja-JP.ts";
import ptBR0 from "antd/es/locale/pt_BR";
import lang_ptBR0 from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/src/locales/pt-BR.ts";
import zhCN0 from "antd/es/locale/zh_CN";
import lang_zhCN0 from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/src/locales/zh-CN.ts";
import zhTW0 from "antd/es/locale/zh_TW";
import lang_zhTW0 from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/src/locales/zh-TW.ts";
const flattenMessages = (nestedMessages, prefix = "") => {
  return Object.keys(nestedMessages).reduce(
    (messages, key) => {
      const value = nestedMessages[key];
      const prefixedKey = prefix ? `${prefix}.${key}` : key;
      if (typeof value === "string") {
        messages[prefixedKey] = value;
      } else {
        Object.assign(messages, flattenMessages(value, prefixedKey));
      }
      return messages;
    },
    {}
  );
};
export const localeInfo = {
  "bn-BD": {
    messages: {
      ...flattenMessages(lang_bnBD0)
    },
    locale: "bn-BD",
    antd: {
      ...bnBD0
    },
    momentLocale: "bn-bd"
  },
  "en-US": {
    messages: {
      ...flattenMessages(lang_enUS0)
    },
    locale: "en-US",
    antd: {
      ...enUS0
    },
    momentLocale: "en"
  },
  "fa-IR": {
    messages: {
      ...flattenMessages(lang_faIR0)
    },
    locale: "fa-IR",
    antd: {
      ...faIR0
    },
    momentLocale: "fa"
  },
  "id-ID": {
    messages: {
      ...flattenMessages(lang_idID0)
    },
    locale: "id-ID",
    antd: {
      ...idID0
    },
    momentLocale: "id"
  },
  "ja-JP": {
    messages: {
      ...flattenMessages(lang_jaJP0)
    },
    locale: "ja-JP",
    antd: {
      ...jaJP0
    },
    momentLocale: "ja"
  },
  "pt-BR": {
    messages: {
      ...flattenMessages(lang_ptBR0)
    },
    locale: "pt-BR",
    antd: {
      ...ptBR0
    },
    momentLocale: "pt-br"
  },
  "zh-CN": {
    messages: {
      ...flattenMessages(lang_zhCN0)
    },
    locale: "zh-CN",
    antd: {
      ...zhCN0
    },
    momentLocale: "zh-cn"
  },
  "zh-TW": {
    messages: {
      ...flattenMessages(lang_zhTW0)
    },
    locale: "zh-TW",
    antd: {
      ...zhTW0
    },
    momentLocale: "zh-tw"
  }
};
export const addLocale = (name, messages, extraLocales) => {
  if (!name) {
    return;
  }
  const mergeMessages = localeInfo[name]?.messages ? Object.assign({}, localeInfo[name].messages, messages) : messages;
  const { momentLocale = localeInfo[name]?.momentLocale, antd = localeInfo[name]?.antd } = extraLocales || {};
  const locale = name.split("-")?.join("-");
  localeInfo[name] = {
    messages: mergeMessages,
    locale,
    momentLocale,
    antd
  };
  if (locale === getLocale()) {
    event.emit(LANG_CHANGE_EVENT, locale);
  }
};
const applyRuntimeLocalePlugin = (initialValue) => {
  return getPluginManager().applyPlugins({
    key: "locale",
    type: "modify",
    initialValue
  });
};
const _createIntl = (locale) => {
  const runtimeLocale = applyRuntimeLocalePlugin(localeInfo[locale]);
  const { cache, ...config } = runtimeLocale;
  return createIntl(config, cache);
};
export const getIntl = (locale, changeIntl) => {
  if (g_intl && !changeIntl && !locale) {
    return g_intl;
  }
  if (!locale) locale = getLocale();
  if (locale && localeInfo[locale]) {
    return _createIntl(locale);
  }
  warning(
    !locale || !!localeInfo[locale],
    `The current popular language does not exist, please check the locales folder!`
  );
  if (localeInfo["zh-CN"]) {
    return _createIntl("zh-CN");
  }
  return createIntl({
    locale: "zh-CN",
    messages: {}
  });
};
export const setIntl = (locale) => {
  g_intl = getIntl(locale, true);
};
export const getLocale = () => {
  const runtimeLocale = applyRuntimeLocalePlugin({});
  if (typeof runtimeLocale?.getLocale === "function") {
    return runtimeLocale.getLocale();
  }
  const lang = navigator.cookieEnabled && typeof localStorage !== "undefined" && useLocalStorage ? window.localStorage.getItem("umi_locale") : "";
  let browserLang;
  const isNavigatorLanguageValid = typeof navigator !== "undefined" && typeof navigator.language === "string";
  browserLang = isNavigatorLanguageValid ? navigator.language.split("-").join("-") : "";
  return lang || browserLang || "zh-CN";
};
export const getDirection = () => {
  const lang = getLocale();
  const rtlLangs = ["he", "ar", "fa", "ku"];
  const direction = rtlLangs.filter((lng) => lang.startsWith(lng)).length ? "rtl" : "ltr";
  return direction;
};
export const setLocale = (lang, realReload = true) => {
  const updater = () => {
    if (getLocale() !== lang) {
      if (navigator.cookieEnabled && typeof window.localStorage !== "undefined" && useLocalStorage) {
        window.localStorage.setItem("umi_locale", lang || "");
      }
      setIntl(lang);
      if (realReload) {
        window.location.reload();
      } else {
        event.emit(LANG_CHANGE_EVENT, lang);
        if (window.dispatchEvent) {
          const event2 = new Event("languagechange");
          window.dispatchEvent(event2);
        }
      }
    }
  };
  updater();
};
let firstWaring = true;
export const formatMessage = (descriptor, values) => {
  if (firstWaring) {
    warning(
      false,
      `Using this API will cause automatic refresh when switching languages, please use useIntl or injectIntl.

\u4F7F\u7528\u6B64 api \u4F1A\u9020\u6210\u5207\u6362\u8BED\u8A00\u7684\u65F6\u5019\u65E0\u6CD5\u81EA\u52A8\u5237\u65B0\uFF0C\u8BF7\u4F7F\u7528 useIntl \u6216 injectIntl\u3002

http://j.mp/37Fkd5Q
      `
    );
    firstWaring = false;
  }
  if (!g_intl) {
    setIntl(getLocale());
  }
  return g_intl.formatMessage(descriptor, values);
};
export const getAllLocales = () => Object.keys(localeInfo);
