{"version": "2.0", "ppid": 33740, "events": [{"head": {"id": "98ad5be3-c291-4880-9c07-b24375816d2d", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724897364100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15a954a4-42db-4bf6-97d8-d03c030a84c0", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724897842800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d2b8ba8-c57e-4547-9fd9-3cb4baf07a3b", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724898299100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c52b03-0935-4e69-b289-d3bba5b33cc0", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724903013300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e1cc2f5-3031-4e94-8df6-a703e995b06c", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724903425800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58cf780a-6168-486d-b1e4-645e026dd2c6", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724903694200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37614343-961c-4f14-9576-74b67b4163ea", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724904906800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87012401-aedb-4027-874c-766f6c6e7517", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724905136500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03f04496-35b5-4726-905b-2412a0469853", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144724947529200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "658a89c1-acc0-4089-ad4b-6b534499c73e", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851875204800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "008d4193-da24-4321-8353-161cc599fbfe", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851881936700, "endTime": 144852078267700}, "additional": {"children": ["e31aa767-d483-447a-a332-2b974658deec", "09def4dc-2cad-4205-b8f5-0e84fc564e1f", "1d7895ac-4135-4b15-b52c-7a383c1f0f59", "7556a54a-d433-4651-abdb-43310399c6ee", "c0c35c25-2799-4a9d-98eb-050a4711f73a", "aa0b6905-1959-4d81-8519-6e7d826d3ab1", "7ce5278f-b11d-4399-949a-85bf272c8cf6"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "67f322ae-8bbc-4b50-98ba-8e59485dd8cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e31aa767-d483-447a-a332-2b974658deec", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851881938000, "endTime": 144851894264200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008d4193-da24-4321-8353-161cc599fbfe", "logId": "99b7214c-85bd-4da5-809b-e4d1eeb4c06f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09def4dc-2cad-4205-b8f5-0e84fc564e1f", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851894281200, "endTime": 144852076705700}, "additional": {"children": ["624fc37c-7daa-47ba-830b-10263a3c6082", "2469ae11-7cd2-4d5f-be2e-85c47cd385ab", "b969d92a-e8da-4c2e-9bee-76c1b086a7e1", "677b3598-1eea-4290-9c99-bd6fe35372aa", "c85c5215-b058-4335-9f41-a0b1d73b6381", "a9505854-c61a-49a3-ad30-3c7ec77be572", "b12f45dc-e2ec-43a7-aeba-1f0d1c1eaa52", "63c0f6c0-5851-4541-8454-449492164865", "e794725c-3bf4-4512-939e-f4e1c146fba2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008d4193-da24-4321-8353-161cc599fbfe", "logId": "c2754e8f-d24b-43ab-b245-9dbe7a31b1de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d7895ac-4135-4b15-b52c-7a383c1f0f59", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852076735200, "endTime": 144852078249600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008d4193-da24-4321-8353-161cc599fbfe", "logId": "6fe78628-5c34-4c4e-bad9-b637334ac834"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7556a54a-d433-4651-abdb-43310399c6ee", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852078255600, "endTime": 144852078264000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008d4193-da24-4321-8353-161cc599fbfe", "logId": "a31c0d38-9e3d-4662-8e47-ad416912c2a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0c35c25-2799-4a9d-98eb-050a4711f73a", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851885573400, "endTime": 144851885613700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008d4193-da24-4321-8353-161cc599fbfe", "logId": "5c8830fb-7e6f-4d6a-8795-ce7848f12c13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c8830fb-7e6f-4d6a-8795-ce7848f12c13", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851885573400, "endTime": 144851885613700}, "additional": {"logType": "info", "children": [], "durationId": "c0c35c25-2799-4a9d-98eb-050a4711f73a", "parent": "67f322ae-8bbc-4b50-98ba-8e59485dd8cf"}}, {"head": {"id": "aa0b6905-1959-4d81-8519-6e7d826d3ab1", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851889612000, "endTime": 144851889631500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008d4193-da24-4321-8353-161cc599fbfe", "logId": "0ec3d816-aa23-445d-9b52-f10758471a28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ec3d816-aa23-445d-9b52-f10758471a28", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851889612000, "endTime": 144851889631500}, "additional": {"logType": "info", "children": [], "durationId": "aa0b6905-1959-4d81-8519-6e7d826d3ab1", "parent": "67f322ae-8bbc-4b50-98ba-8e59485dd8cf"}}, {"head": {"id": "e6aeec77-4fd5-4198-89e5-b8d3651196f9", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851889683400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "576ef05d-495f-4282-9d6f-dc76e78d6e21", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851894123200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99b7214c-85bd-4da5-809b-e4d1eeb4c06f", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851881938000, "endTime": 144851894264200}, "additional": {"logType": "info", "children": [], "durationId": "e31aa767-d483-447a-a332-2b974658deec", "parent": "67f322ae-8bbc-4b50-98ba-8e59485dd8cf"}}, {"head": {"id": "624fc37c-7daa-47ba-830b-10263a3c6082", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851898776000, "endTime": 144851898788400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09def4dc-2cad-4205-b8f5-0e84fc564e1f", "logId": "51ecead3-11b3-4eae-a448-db74732128c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2469ae11-7cd2-4d5f-be2e-85c47cd385ab", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851898802800, "endTime": 144851903162600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09def4dc-2cad-4205-b8f5-0e84fc564e1f", "logId": "39e386d7-1606-4c53-82b6-8b60c68b32ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b969d92a-e8da-4c2e-9bee-76c1b086a7e1", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851903175300, "endTime": 144851980885700}, "additional": {"children": ["48b3d489-5c4d-47a1-8ec8-fac0ff74ba5e", "8ee26d76-fff0-438e-8ae9-866c47714cb2", "5bb9b9ff-3aa5-41ec-9b37-f26e0b8d10c5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09def4dc-2cad-4205-b8f5-0e84fc564e1f", "logId": "431f9377-bf68-42fa-8db4-0209b109924d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "677b3598-1eea-4290-9c99-bd6fe35372aa", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851980899900, "endTime": 144852000194700}, "additional": {"children": ["779b3a36-78fb-4278-831e-71eee24faeb0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09def4dc-2cad-4205-b8f5-0e84fc564e1f", "logId": "c9edeaab-0c10-4719-a1e3-0ccbac53b23a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c85c5215-b058-4335-9f41-a0b1d73b6381", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852000201000, "endTime": 144852053538200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09def4dc-2cad-4205-b8f5-0e84fc564e1f", "logId": "170088b9-c2d0-4f9d-8669-331150eaed6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9505854-c61a-49a3-ad30-3c7ec77be572", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852054508700, "endTime": 144852063149700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09def4dc-2cad-4205-b8f5-0e84fc564e1f", "logId": "176e36e6-cd0f-400e-9320-edbc5bcaa352"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b12f45dc-e2ec-43a7-aeba-1f0d1c1eaa52", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852063168200, "endTime": 144852076450300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09def4dc-2cad-4205-b8f5-0e84fc564e1f", "logId": "0f852a2f-ed7f-4acc-b14b-ed615dd24f5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63c0f6c0-5851-4541-8454-449492164865", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852076473800, "endTime": 144852076686600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09def4dc-2cad-4205-b8f5-0e84fc564e1f", "logId": "14bca69d-0c7d-4759-afbc-d7c0d87ee436"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51ecead3-11b3-4eae-a448-db74732128c9", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851898776000, "endTime": 144851898788400}, "additional": {"logType": "info", "children": [], "durationId": "624fc37c-7daa-47ba-830b-10263a3c6082", "parent": "c2754e8f-d24b-43ab-b245-9dbe7a31b1de"}}, {"head": {"id": "39e386d7-1606-4c53-82b6-8b60c68b32ff", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851898802800, "endTime": 144851903162600}, "additional": {"logType": "info", "children": [], "durationId": "2469ae11-7cd2-4d5f-be2e-85c47cd385ab", "parent": "c2754e8f-d24b-43ab-b245-9dbe7a31b1de"}}, {"head": {"id": "48b3d489-5c4d-47a1-8ec8-fac0ff74ba5e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851903710900, "endTime": 144851903732800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b969d92a-e8da-4c2e-9bee-76c1b086a7e1", "logId": "5ca0d9a6-416c-4a24-b8be-e0b279d52835"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ca0d9a6-416c-4a24-b8be-e0b279d52835", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851903710900, "endTime": 144851903732800}, "additional": {"logType": "info", "children": [], "durationId": "48b3d489-5c4d-47a1-8ec8-fac0ff74ba5e", "parent": "431f9377-bf68-42fa-8db4-0209b109924d"}}, {"head": {"id": "8ee26d76-fff0-438e-8ae9-866c47714cb2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851905750900, "endTime": 144851980268100}, "additional": {"children": ["ce5702fa-6451-41f9-9ed8-eb54286493ba", "96332469-7e31-4268-8a8b-4512bbd677b1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b969d92a-e8da-4c2e-9bee-76c1b086a7e1", "logId": "0cf178c5-26d5-4bb2-be9b-ff38d662b64f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce5702fa-6451-41f9-9ed8-eb54286493ba", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851905752100, "endTime": 144851913337500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ee26d76-fff0-438e-8ae9-866c47714cb2", "logId": "463eff94-ba74-4eb5-8428-e41fa0b420a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96332469-7e31-4268-8a8b-4512bbd677b1", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851913358700, "endTime": 144851980256500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ee26d76-fff0-438e-8ae9-866c47714cb2", "logId": "48993b7b-d48e-4a9f-8944-0cf78a6b5923"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d7349c6-d528-454a-82a0-86d24042b492", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851905758900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1582fb7e-7914-4490-bc1e-6b01861773e6", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851913159800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "463eff94-ba74-4eb5-8428-e41fa0b420a2", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851905752100, "endTime": 144851913337500}, "additional": {"logType": "info", "children": [], "durationId": "ce5702fa-6451-41f9-9ed8-eb54286493ba", "parent": "0cf178c5-26d5-4bb2-be9b-ff38d662b64f"}}, {"head": {"id": "2173fd2d-7c96-45c6-9363-3c6d30e7db86", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851913375300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c095a7c3-20e8-4e2a-8817-8fa166a09201", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851921883300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c8cd705-25fc-48ac-b45a-5b8fc581e37b", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851922023900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac30e522-916c-48e1-ac01-9639aeea70bb", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851922917100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db358945-0907-4d6a-9124-05ef74488086", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851923005200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b6117de-72fd-45f6-b027-0ebbc38b85fb", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851924526100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d23a3b6-e8fc-469c-a61b-57df67101a70", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851937876700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "273335e5-0458-4a0d-8e17-e8b2ae5403a1", "name": "Sdk init in 31 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851960358500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3431f6b6-e84c-40fc-9198-cfc8ff349f2c", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851960544000}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 11, "second": 21}, "markType": "other"}}, {"head": {"id": "942bae27-1606-4253-b3cd-d32e21bd304d", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851960566700}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 11, "second": 21}, "markType": "other"}}, {"head": {"id": "b7c76a62-1203-46ea-8114-f6728f5c2db2", "name": "Project task initialization takes 19 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851980027500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "775c7f96-f36f-45db-a830-3a3fe199f5d0", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851980149100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29f30d70-d5fe-4c9d-9b03-0465c6eba483", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851980195500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c2fb807-8f4f-4b57-a272-b9ab974d9ba5", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851980226300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48993b7b-d48e-4a9f-8944-0cf78a6b5923", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851913358700, "endTime": 144851980256500}, "additional": {"logType": "info", "children": [], "durationId": "96332469-7e31-4268-8a8b-4512bbd677b1", "parent": "0cf178c5-26d5-4bb2-be9b-ff38d662b64f"}}, {"head": {"id": "0cf178c5-26d5-4bb2-be9b-ff38d662b64f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851905750900, "endTime": 144851980268100}, "additional": {"logType": "info", "children": ["463eff94-ba74-4eb5-8428-e41fa0b420a2", "48993b7b-d48e-4a9f-8944-0cf78a6b5923"], "durationId": "8ee26d76-fff0-438e-8ae9-866c47714cb2", "parent": "431f9377-bf68-42fa-8db4-0209b109924d"}}, {"head": {"id": "5bb9b9ff-3aa5-41ec-9b37-f26e0b8d10c5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851980852500, "endTime": 144851980869300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b969d92a-e8da-4c2e-9bee-76c1b086a7e1", "logId": "732e7a0b-01cb-4e42-b6d3-309b0f554c13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "732e7a0b-01cb-4e42-b6d3-309b0f554c13", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851980852500, "endTime": 144851980869300}, "additional": {"logType": "info", "children": [], "durationId": "5bb9b9ff-3aa5-41ec-9b37-f26e0b8d10c5", "parent": "431f9377-bf68-42fa-8db4-0209b109924d"}}, {"head": {"id": "431f9377-bf68-42fa-8db4-0209b109924d", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851903175300, "endTime": 144851980885700}, "additional": {"logType": "info", "children": ["5ca0d9a6-416c-4a24-b8be-e0b279d52835", "0cf178c5-26d5-4bb2-be9b-ff38d662b64f", "732e7a0b-01cb-4e42-b6d3-309b0f554c13"], "durationId": "b969d92a-e8da-4c2e-9bee-76c1b086a7e1", "parent": "c2754e8f-d24b-43ab-b245-9dbe7a31b1de"}}, {"head": {"id": "779b3a36-78fb-4278-831e-71eee24faeb0", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851981451800, "endTime": 144852000184500}, "additional": {"children": ["d584fcaa-e970-46f7-8f8d-60b78652267b", "e91045bc-de88-490c-b12f-dbcbb6d390d0", "d1db2ef0-e6a0-4a2d-acdc-fb0502ee52c7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "677b3598-1eea-4290-9c99-bd6fe35372aa", "logId": "f4b374ce-eab5-4896-b35a-e795df1efb9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d584fcaa-e970-46f7-8f8d-60b78652267b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851983921700, "endTime": 144851983936700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "779b3a36-78fb-4278-831e-71eee24faeb0", "logId": "d0e71b5b-a4bd-4eff-b8d0-ab127a825206"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0e71b5b-a4bd-4eff-b8d0-ab127a825206", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851983921700, "endTime": 144851983936700}, "additional": {"logType": "info", "children": [], "durationId": "d584fcaa-e970-46f7-8f8d-60b78652267b", "parent": "f4b374ce-eab5-4896-b35a-e795df1efb9a"}}, {"head": {"id": "e91045bc-de88-490c-b12f-dbcbb6d390d0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851985479000, "endTime": 144851999067500}, "additional": {"children": ["04ea83e9-723f-42b5-af34-aa4e98df0c2d", "b5785bb6-da81-4f92-b4cd-171542f0a942"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "779b3a36-78fb-4278-831e-71eee24faeb0", "logId": "17489e54-a926-4cfb-a3d6-7e2e5e9e0936"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04ea83e9-723f-42b5-af34-aa4e98df0c2d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851985480400, "endTime": 144851988848800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e91045bc-de88-490c-b12f-dbcbb6d390d0", "logId": "99c015d8-73d2-4b61-85d5-084f5c098de8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5785bb6-da81-4f92-b4cd-171542f0a942", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851988864000, "endTime": 144851999058700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e91045bc-de88-490c-b12f-dbcbb6d390d0", "logId": "89ff1b95-f21a-429c-88e9-454bb671a0b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ac939cc-af40-4266-be37-797c1676ef2b", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851985486300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6535494-880f-415d-9f22-f14faec14631", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851988727400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99c015d8-73d2-4b61-85d5-084f5c098de8", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851985480400, "endTime": 144851988848800}, "additional": {"logType": "info", "children": [], "durationId": "04ea83e9-723f-42b5-af34-aa4e98df0c2d", "parent": "17489e54-a926-4cfb-a3d6-7e2e5e9e0936"}}, {"head": {"id": "ef8414b9-12de-467f-af0c-cf8e74bb2c74", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851988882500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "003bd5f1-b635-4280-bea9-7943c842787b", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851995129300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d13d52fd-5bcf-464a-aee2-74b134dec7dd", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851995258300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31ec773f-ad2f-4b91-92d9-78a54679fce5", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851995407700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad8730b4-0605-4b83-b9bf-67ddc47d3d29", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851995489100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33df3c10-d772-479f-8a9f-c99422e34a44", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851995526000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2304417c-2c42-4e1a-8050-6fbe2224721d", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851995581000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee3e890e-3aca-44c9-9481-d3b0f0cd0542", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851995628100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3243807-65e8-45a8-b05d-43d1a6d4dd78", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851995726400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7dc3d7f-bdc8-4baf-9ffb-21814d4122ad", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851995930000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e5155aa-e47d-426e-8c0b-49a5e26c2f2f", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851996010300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "926f6802-5787-4173-a9d6-853885d15d04", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851996046500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62234e76-c582-445c-b3d8-e19d71e2ddbf", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851996072500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3ba339b-4481-4499-bf48-bc5cb00550ed", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851996123400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3b8258b-2ac1-480b-b31f-538668393e18", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851996152900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7c211a0-019d-4de0-bdde-e7afc898bd2d", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851996222300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5e7388b-733d-4bc6-bd7d-e92148f03a50", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851996398100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69847136-edde-4312-846e-697965d9fa48", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851996435300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f81188e5-dae1-4086-9e9a-f4993dfa4b77", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851996462600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0ff6149-d35a-43c9-ad50-03b080e9c458", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851996496000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d1ccff6-a62f-41cb-8069-0a968c4ca8c0", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851998847600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "947b5d1c-1e0b-4e88-86db-392a8b0b3262", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851998963300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ea27db8-a3ad-4d0a-8212-d8b45e2a0782", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851999004300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e24802a3-90f5-4df4-8cf8-2f5db171e3ac", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851999032300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89ff1b95-f21a-429c-88e9-454bb671a0b9", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851988864000, "endTime": 144851999058700}, "additional": {"logType": "info", "children": [], "durationId": "b5785bb6-da81-4f92-b4cd-171542f0a942", "parent": "17489e54-a926-4cfb-a3d6-7e2e5e9e0936"}}, {"head": {"id": "17489e54-a926-4cfb-a3d6-7e2e5e9e0936", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851985479000, "endTime": 144851999067500}, "additional": {"logType": "info", "children": ["99c015d8-73d2-4b61-85d5-084f5c098de8", "89ff1b95-f21a-429c-88e9-454bb671a0b9"], "durationId": "e91045bc-de88-490c-b12f-dbcbb6d390d0", "parent": "f4b374ce-eab5-4896-b35a-e795df1efb9a"}}, {"head": {"id": "d1db2ef0-e6a0-4a2d-acdc-fb0502ee52c7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852000157300, "endTime": 144852000170800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "779b3a36-78fb-4278-831e-71eee24faeb0", "logId": "60ea54c1-f226-4296-8722-aa5069cb69d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60ea54c1-f226-4296-8722-aa5069cb69d3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852000157300, "endTime": 144852000170800}, "additional": {"logType": "info", "children": [], "durationId": "d1db2ef0-e6a0-4a2d-acdc-fb0502ee52c7", "parent": "f4b374ce-eab5-4896-b35a-e795df1efb9a"}}, {"head": {"id": "f4b374ce-eab5-4896-b35a-e795df1efb9a", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851981451800, "endTime": 144852000184500}, "additional": {"logType": "info", "children": ["d0e71b5b-a4bd-4eff-b8d0-ab127a825206", "17489e54-a926-4cfb-a3d6-7e2e5e9e0936", "60ea54c1-f226-4296-8722-aa5069cb69d3"], "durationId": "779b3a36-78fb-4278-831e-71eee24faeb0", "parent": "c9edeaab-0c10-4719-a1e3-0ccbac53b23a"}}, {"head": {"id": "c9edeaab-0c10-4719-a1e3-0ccbac53b23a", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851980899900, "endTime": 144852000194700}, "additional": {"logType": "info", "children": ["f4b374ce-eab5-4896-b35a-e795df1efb9a"], "durationId": "677b3598-1eea-4290-9c99-bd6fe35372aa", "parent": "c2754e8f-d24b-43ab-b245-9dbe7a31b1de"}}, {"head": {"id": "e51722fc-40dc-4bd1-b559-1ae81986d27a", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852016711300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59114b3c-deb1-4886-8600-7c8d0434e6b6", "name": "hvigorfile, resolve hvigorfile dependencies in 54 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852053397400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "170088b9-c2d0-4f9d-8669-331150eaed6d", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852000201000, "endTime": 144852053538200}, "additional": {"logType": "info", "children": [], "durationId": "c85c5215-b058-4335-9f41-a0b1d73b6381", "parent": "c2754e8f-d24b-43ab-b245-9dbe7a31b1de"}}, {"head": {"id": "e794725c-3bf4-4512-939e-f4e1c146fba2", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852054310100, "endTime": 144852054496800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09def4dc-2cad-4205-b8f5-0e84fc564e1f", "logId": "1359b924-159b-4b83-893c-b69d3e25b57c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95f03b7b-df66-4be1-9657-1145d0a4fe89", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852054346200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1359b924-159b-4b83-893c-b69d3e25b57c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852054310100, "endTime": 144852054496800}, "additional": {"logType": "info", "children": [], "durationId": "e794725c-3bf4-4512-939e-f4e1c146fba2", "parent": "c2754e8f-d24b-43ab-b245-9dbe7a31b1de"}}, {"head": {"id": "8eefd1b6-0f89-4d18-804f-571e260bea91", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852055927700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0863e498-14e1-4fa1-8b17-039e6f3ea902", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852062327600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "176e36e6-cd0f-400e-9320-edbc5bcaa352", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852054508700, "endTime": 144852063149700}, "additional": {"logType": "info", "children": [], "durationId": "a9505854-c61a-49a3-ad30-3c7ec77be572", "parent": "c2754e8f-d24b-43ab-b245-9dbe7a31b1de"}}, {"head": {"id": "dfcf3713-8ea2-4777-8c14-a8d98cd73b3e", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852063186400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cca8468-28f2-49fe-8b4a-73d1b05fa6d6", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852069545700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfcddd7d-ea0f-42dd-8205-ccda8d672974", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852069659700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fb301b1-1266-4620-b98e-3aa3f651c5c8", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852069829300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84a3261e-4b85-4afc-8836-93a9d03d594c", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852072657500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bca5944-45df-47e6-bde5-6a55b58723fd", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852072750900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f852a2f-ed7f-4acc-b14b-ed615dd24f5f", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852063168200, "endTime": 144852076450300}, "additional": {"logType": "info", "children": [], "durationId": "b12f45dc-e2ec-43a7-aeba-1f0d1c1eaa52", "parent": "c2754e8f-d24b-43ab-b245-9dbe7a31b1de"}}, {"head": {"id": "64dac245-0c3c-4810-ab1f-9a5a3d0ad8f0", "name": "Configuration phase cost:178 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852076499800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14bca69d-0c7d-4759-afbc-d7c0d87ee436", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852076473800, "endTime": 144852076686600}, "additional": {"logType": "info", "children": [], "durationId": "63c0f6c0-5851-4541-8454-449492164865", "parent": "c2754e8f-d24b-43ab-b245-9dbe7a31b1de"}}, {"head": {"id": "c2754e8f-d24b-43ab-b245-9dbe7a31b1de", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851894281200, "endTime": 144852076705700}, "additional": {"logType": "info", "children": ["51ecead3-11b3-4eae-a448-db74732128c9", "39e386d7-1606-4c53-82b6-8b60c68b32ff", "431f9377-bf68-42fa-8db4-0209b109924d", "c9edeaab-0c10-4719-a1e3-0ccbac53b23a", "170088b9-c2d0-4f9d-8669-331150eaed6d", "176e36e6-cd0f-400e-9320-edbc5bcaa352", "0f852a2f-ed7f-4acc-b14b-ed615dd24f5f", "14bca69d-0c7d-4759-afbc-d7c0d87ee436", "1359b924-159b-4b83-893c-b69d3e25b57c"], "durationId": "09def4dc-2cad-4205-b8f5-0e84fc564e1f", "parent": "67f322ae-8bbc-4b50-98ba-8e59485dd8cf"}}, {"head": {"id": "7ce5278f-b11d-4399-949a-85bf272c8cf6", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852078210900, "endTime": 144852078233100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "008d4193-da24-4321-8353-161cc599fbfe", "logId": "c363f693-4157-4118-9a8e-a73de9269e5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c363f693-4157-4118-9a8e-a73de9269e5e", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852078210900, "endTime": 144852078233100}, "additional": {"logType": "info", "children": [], "durationId": "7ce5278f-b11d-4399-949a-85bf272c8cf6", "parent": "67f322ae-8bbc-4b50-98ba-8e59485dd8cf"}}, {"head": {"id": "6fe78628-5c34-4c4e-bad9-b637334ac834", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852076735200, "endTime": 144852078249600}, "additional": {"logType": "info", "children": [], "durationId": "1d7895ac-4135-4b15-b52c-7a383c1f0f59", "parent": "67f322ae-8bbc-4b50-98ba-8e59485dd8cf"}}, {"head": {"id": "a31c0d38-9e3d-4662-8e47-ad416912c2a8", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852078255600, "endTime": 144852078264000}, "additional": {"logType": "info", "children": [], "durationId": "7556a54a-d433-4651-abdb-43310399c6ee", "parent": "67f322ae-8bbc-4b50-98ba-8e59485dd8cf"}}, {"head": {"id": "67f322ae-8bbc-4b50-98ba-8e59485dd8cf", "name": "init", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851881936700, "endTime": 144852078267700}, "additional": {"logType": "info", "children": ["99b7214c-85bd-4da5-809b-e4d1eeb4c06f", "c2754e8f-d24b-43ab-b245-9dbe7a31b1de", "6fe78628-5c34-4c4e-bad9-b637334ac834", "a31c0d38-9e3d-4662-8e47-ad416912c2a8", "5c8830fb-7e6f-4d6a-8795-ce7848f12c13", "0ec3d816-aa23-445d-9b52-f10758471a28", "c363f693-4157-4118-9a8e-a73de9269e5e"], "durationId": "008d4193-da24-4321-8353-161cc599fbfe"}}, {"head": {"id": "4a89c14e-b483-4d5f-accf-19ac0962084a", "name": "Configuration task cost before running: 200 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852078424900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4be5118-e037-41a5-8093-46e686e79d22", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852087570600, "endTime": 144852099794700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "f469c311-b02d-475f-8e80-0b84dcdc334e", "logId": "11a4cb9e-8e97-4086-8b51-7749832f4f1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f469c311-b02d-475f-8e80-0b84dcdc334e", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852079845700}, "additional": {"logType": "detail", "children": [], "durationId": "f4be5118-e037-41a5-8093-46e686e79d22"}}, {"head": {"id": "14d355d5-58af-45d6-8af8-99fdc8dfb9ca", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852080505000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79392429-b0f7-4b33-8141-55ac7093da7b", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852080657400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09133a26-feff-426a-88f0-85bc2f3dd52d", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852081390000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7947c1c1-d0c0-406d-8776-0dcc0b96fc61", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852082056400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "476eda1d-d12d-4cbd-8bbc-b6e312a0857b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852083048700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73918ab3-071a-46f7-baf1-f16bd4b4c062", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852083129500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30dd03af-8b5e-4d41-a45a-c8db4481e535", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852087588400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4b8894c-effb-4863-9e29-557dba847cdb", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852099389300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8ee3bbc-66a5-4c21-a078-c9e9e4c493e7", "name": "entry : default@PreBuild cost memory 0.319061279296875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852099580800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11a4cb9e-8e97-4086-8b51-7749832f4f1b", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852087570600, "endTime": 144852099794700}, "additional": {"logType": "info", "children": [], "durationId": "f4be5118-e037-41a5-8093-46e686e79d22"}}, {"head": {"id": "261df120-76b2-40b3-a578-5afd583b75fe", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852106477500, "endTime": 144852108542400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0448416f-951e-4e3a-9418-719e7874dda6", "logId": "feafe329-7ea5-4ac9-8719-753a7e17961d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0448416f-951e-4e3a-9418-719e7874dda6", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852104677700}, "additional": {"logType": "detail", "children": [], "durationId": "261df120-76b2-40b3-a578-5afd583b75fe"}}, {"head": {"id": "af982980-3f9a-45f6-82b7-b961510998a1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852105758100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f60f200-4c6e-4327-82ee-3292d50a6056", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852105868700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58421923-7005-440c-b995-eab940b379bd", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852106491900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1e02b6b-4f9c-4c79-a1c1-aebb9b942685", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852107394600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95aaa579-84d8-45b2-8095-f31ec67d8b6c", "name": "entry : default@CreateModuleInfo cost memory 0.061279296875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852108328100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afe41609-c75e-4877-aadc-e49c623ea15d", "name": "runTaskFromQueue task cost before running: 230 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852108474500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feafe329-7ea5-4ac9-8719-753a7e17961d", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852106477500, "endTime": 144852108542400, "totalTime": 1969900}, "additional": {"logType": "info", "children": [], "durationId": "261df120-76b2-40b3-a578-5afd583b75fe"}}, {"head": {"id": "781609f2-1e3d-4d1c-a729-3c0b78b9e1aa", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852119478100, "endTime": 144852121999100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "367b8b54-2f4e-4248-aa3b-d9e6e3b9ce2f", "logId": "fcc36281-83ad-4eb4-9f9b-8a5f75b295da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "367b8b54-2f4e-4248-aa3b-d9e6e3b9ce2f", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852111791200}, "additional": {"logType": "detail", "children": [], "durationId": "781609f2-1e3d-4d1c-a729-3c0b78b9e1aa"}}, {"head": {"id": "92a95493-40b2-40f3-804b-f5a06289a389", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852113847400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "131eb88a-8b13-44d8-b96e-373aa367a0d9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852114041800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cb0fb45-7c1a-44bf-a1f5-6cb298d1b500", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852119496900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aa30414-0c23-4f9b-b980-4d0fb456cd1c", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852120564700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be5062fc-5175-4a92-ac15-396377dfe68f", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852121795100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "407a3166-fae1-481b-9237-824baefc6144", "name": "entry : default@GenerateMetadata cost memory 0.10279083251953125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852121922500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcc36281-83ad-4eb4-9f9b-8a5f75b295da", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852119478100, "endTime": 144852121999100}, "additional": {"logType": "info", "children": [], "durationId": "781609f2-1e3d-4d1c-a729-3c0b78b9e1aa"}}, {"head": {"id": "268e86b3-bc37-40d5-a3f3-739a2ab947e1", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852125171000, "endTime": 144852125465900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3dd90717-3479-46a8-863a-afceefa325af", "logId": "4c99e049-102c-4430-8743-2e357ee907c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3dd90717-3479-46a8-863a-afceefa325af", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852123834100}, "additional": {"logType": "detail", "children": [], "durationId": "268e86b3-bc37-40d5-a3f3-739a2ab947e1"}}, {"head": {"id": "ebf41b6f-4785-4c1b-a615-b0eb00f51af8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852124920900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7306502e-d95e-4336-a319-d6c7c4682839", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852125033400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6401b3c3-d61c-45af-ae1e-b5aad98ba401", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852125179500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b99de60-6261-4a60-aba3-9ca6eee82fa5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852125265200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91824044-6c6f-493a-ac76-edb7469520f1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852125301500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "993c96cb-6a7d-41d8-9f67-5a36861e3f44", "name": "entry : default@ConfigureCmake cost memory 0.0374755859375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852125361900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c708200-9361-4e4c-a1d0-58a017b43020", "name": "runTaskFromQueue task cost before running: 247 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852125424300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c99e049-102c-4430-8743-2e357ee907c2", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852125171000, "endTime": 144852125465900, "totalTime": 233400}, "additional": {"logType": "info", "children": [], "durationId": "268e86b3-bc37-40d5-a3f3-739a2ab947e1"}}, {"head": {"id": "a4b73e3c-5533-4d51-87de-a5a4fd8b4449", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852128853900, "endTime": 144852130798600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "4cb8e266-359b-4e32-b723-5b1e6c857c5f", "logId": "cfbe8315-3d17-4918-9bdd-9947d8d3594a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cb8e266-359b-4e32-b723-5b1e6c857c5f", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852127011700}, "additional": {"logType": "detail", "children": [], "durationId": "a4b73e3c-5533-4d51-87de-a5a4fd8b4449"}}, {"head": {"id": "88aa9eee-32e8-4e58-b349-195df6c564c2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852128078900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0a2b594-f605-4cc0-ad7a-7e32cc27a982", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852128186300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d03259d4-4b4c-4b96-8b61-fed83925e668", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852128865800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91ebf2b5-d5ca-4651-8edd-dee7585c2c46", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852130624300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6f5fc47-5861-4825-9144-956017d2cf97", "name": "entry : default@MergeProfile cost memory 0.118499755859375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852130734300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfbe8315-3d17-4918-9bdd-9947d8d3594a", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852128853900, "endTime": 144852130798600}, "additional": {"logType": "info", "children": [], "durationId": "a4b73e3c-5533-4d51-87de-a5a4fd8b4449"}}, {"head": {"id": "7f651355-9708-42fa-942f-60ef17d77c0e", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852136952000, "endTime": 144852139781700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "17246453-3f8b-4a64-989b-44f85a478896", "logId": "e566ec8c-eb12-42d6-9e26-368e774044a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17246453-3f8b-4a64-989b-44f85a478896", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852132651900}, "additional": {"logType": "detail", "children": [], "durationId": "7f651355-9708-42fa-942f-60ef17d77c0e"}}, {"head": {"id": "e4071439-dec5-4174-a493-4baaeefc730a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852133695400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7728ba9a-d478-4197-8fa4-ee90357798d7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852133855200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e531f567-cae5-44c1-8649-362f71f5399c", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852136966600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf85d317-67e0-42cf-89c9-282a90d3f560", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852138028800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1d5155c-06ca-42a2-933c-9161c5ef1894", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852139589400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29ecc9c8-92eb-499d-9bf5-11250588b782", "name": "entry : default@CreateBuildProfile cost memory 0.10790252685546875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852139715200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e566ec8c-eb12-42d6-9e26-368e774044a3", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852136952000, "endTime": 144852139781700}, "additional": {"logType": "info", "children": [], "durationId": "7f651355-9708-42fa-942f-60ef17d77c0e"}}, {"head": {"id": "6594b42a-5ed6-48a0-ae02-965ca487b9b1", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852143191800, "endTime": 144852143725300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d7135fca-5a18-472a-8c32-458f49295555", "logId": "e804fd91-466f-43c5-a689-708451c8bd19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7135fca-5a18-472a-8c32-458f49295555", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852141255100}, "additional": {"logType": "detail", "children": [], "durationId": "6594b42a-5ed6-48a0-ae02-965ca487b9b1"}}, {"head": {"id": "daf5eeef-1e8e-4584-9098-6812166f4972", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852142291800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a45b3a5e-91b7-4508-86d3-9a2b69b1d6c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852142406500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab3563b1-9462-4c12-8a9d-25fdb967500b", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852143202400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7a11b4d-b99b-41f9-b7d3-9645df76fad7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852143323200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a69e8c5f-2ca6-4240-965f-c948fd6b3bd8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852143365300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccac0a47-b09e-4f30-bd2b-1664f28e09a4", "name": "entry : default@PreCheckSyscap cost memory 0.0410919189453125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852143598700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3925cdf5-2337-4e86-be71-2459a0d5625f", "name": "runTaskFromQueue task cost before running: 265 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852143684500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e804fd91-466f-43c5-a689-708451c8bd19", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852143191800, "endTime": 144852143725300, "totalTime": 471600}, "additional": {"logType": "info", "children": [], "durationId": "6594b42a-5ed6-48a0-ae02-965ca487b9b1"}}, {"head": {"id": "d62a1c66-c99c-40fb-9d54-940cf05be07e", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852148312200, "endTime": 144852153567800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5ee235c7-18c8-49b9-a490-97bbde2ce055", "logId": "70cde989-b8b4-49e6-856e-cf910c21d51c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ee235c7-18c8-49b9-a490-97bbde2ce055", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852145714700}, "additional": {"logType": "detail", "children": [], "durationId": "d62a1c66-c99c-40fb-9d54-940cf05be07e"}}, {"head": {"id": "662c8556-6bf8-439f-946f-44d9929fb00b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852146839600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aa7b57f-a642-4853-a882-abaa6b8ce96b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852146953200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3fc381d-3e11-4feb-82cf-921597a46d94", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852148324900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdcfb9df-73df-4a82-9d6c-c86771277156", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852152752100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c049fdb3-cbff-47a2-9803-faa06ea4bf3e", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852153401600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "016e0e2e-0863-4941-b70f-bf256c2397fb", "name": "entry : default@GeneratePkgContextInfo cost memory 0.273834228515625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852153504800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70cde989-b8b4-49e6-856e-cf910c21d51c", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852148312200, "endTime": 144852153567800}, "additional": {"logType": "info", "children": [], "durationId": "d62a1c66-c99c-40fb-9d54-940cf05be07e"}}, {"head": {"id": "b73d2b78-86f7-4501-b413-84914adffad3", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852161368700, "endTime": 144852163531000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "aa1f224c-0bf6-40b0-bcd3-2588dd8f8c21", "logId": "b86d60d4-3f15-46c6-922a-1c6b011a6918"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa1f224c-0bf6-40b0-bcd3-2588dd8f8c21", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852155166300}, "additional": {"logType": "detail", "children": [], "durationId": "b73d2b78-86f7-4501-b413-84914adffad3"}}, {"head": {"id": "1d51b786-be68-4db5-bebd-6a3f43365b89", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852156258100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e14bd96e-22a2-43bd-8fa8-52c9a12f05f8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852156355500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8cf68ab-8b6a-4689-ab17-3c21c8105b56", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852161386000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c05eb4b0-8459-441b-b96e-356147495cd7", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852163126300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c46de5a6-d7b0-48eb-868a-9391e45a9442", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852163264900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c29bd52a-2733-4f39-aabb-9e73111b11d2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852163343500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84ec2461-c51e-4a1f-8c34-50421ad1730a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852163379400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18510c40-b89b-467c-b504-316c0fa7b39c", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12198638916015625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852163438600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e780e3c5-b27c-4200-aea2-cf727e74648c", "name": "runTaskFromQueue task cost before running: 285 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852163491400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b86d60d4-3f15-46c6-922a-1c6b011a6918", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852161368700, "endTime": 144852163531000, "totalTime": 2112400}, "additional": {"logType": "info", "children": [], "durationId": "b73d2b78-86f7-4501-b413-84914adffad3"}}, {"head": {"id": "0e766849-5224-4800-b4dd-94ebe5e624bc", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852167375500, "endTime": 144852167703000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7b0f32cf-f5d5-4dfd-944b-720ea7080252", "logId": "d5e44e0b-6aeb-4ebf-abdb-e8a010d4fde6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b0f32cf-f5d5-4dfd-944b-720ea7080252", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852165667600}, "additional": {"logType": "detail", "children": [], "durationId": "0e766849-5224-4800-b4dd-94ebe5e624bc"}}, {"head": {"id": "ea1f3b0f-dfd8-4c9f-8547-769b22cebe7f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852166602500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59369142-3583-4633-b62b-ee6cb069779a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852166692600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66de9b65-3ba2-45d6-b7a6-8d0e26f7a0ca", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852167385200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8adfcfe4-59c8-4c1c-bf89-e9cad7bebf5d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852167493400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32c4ade2-8595-442f-8961-aa5bd4e42f18", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852167531900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46fddaa7-ca0f-44ab-a62a-c1c7d712825b", "name": "entry : default@BuildNativeWithCmake cost memory 0.03858184814453125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852167603900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "254aa6a6-ec6e-4ff4-b71b-eda6f0552eee", "name": "runTaskFromQueue task cost before running: 289 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852167665000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5e44e0b-6aeb-4ebf-abdb-e8a010d4fde6", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852167375500, "endTime": 144852167703000, "totalTime": 272100}, "additional": {"logType": "info", "children": [], "durationId": "0e766849-5224-4800-b4dd-94ebe5e624bc"}}, {"head": {"id": "dc4ed86a-5e1a-4834-ab66-a8dffef69447", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852171070900, "endTime": 144852174843800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d1d8bbc9-5faf-495d-be4c-eb2ebebd1b10", "logId": "1c6bc32b-fb36-4236-a705-2612d8c960e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1d8bbc9-5faf-495d-be4c-eb2ebebd1b10", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852169129500}, "additional": {"logType": "detail", "children": [], "durationId": "dc4ed86a-5e1a-4834-ab66-a8dffef69447"}}, {"head": {"id": "a3206a3a-4642-49a5-ab93-80d97cb33b35", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852170229400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37af515b-8b24-4465-9ca2-cb4a872c7136", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852170333900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86b2d392-954c-4dd9-8d05-88673b2cf99c", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852171081600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f8de56b-4406-4f05-b005-67f21bffe22d", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852174666300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c3c6312-50ce-460e-ad3d-d2b2a70d96fc", "name": "entry : default@MakePackInfo cost memory 0.16510772705078125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852174776400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c6bc32b-fb36-4236-a705-2612d8c960e5", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852171070900, "endTime": 144852174843800}, "additional": {"logType": "info", "children": [], "durationId": "dc4ed86a-5e1a-4834-ab66-a8dffef69447"}}, {"head": {"id": "9836f0ec-ab95-456a-a293-648f899ab51b", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852180931400, "endTime": 144852184198100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bbf072cc-7d33-4d41-a447-668a5734ae7c", "logId": "8282d6d2-66d8-42bd-9f83-b9c542f71dc1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbf072cc-7d33-4d41-a447-668a5734ae7c", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852177173300}, "additional": {"logType": "detail", "children": [], "durationId": "9836f0ec-ab95-456a-a293-648f899ab51b"}}, {"head": {"id": "41fdf449-c515-4242-904d-9b69994e7f02", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852179095900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "661b50e6-7c90-44fd-9fbf-9bc9c6905189", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852179266500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc89f6d1-04a6-4acd-80eb-2f617656d7b1", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852180943600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc5733b5-0cf2-4d4c-be68-679f58d58fe9", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852181145300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1b6b55c-fdaf-4c0f-b50c-e81124476ef2", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852181864700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28029861-9cd4-412c-9b1e-b875021c912d", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852184029700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a70f6830-9fbf-4488-9a6e-5d34fec8b1e5", "name": "entry : default@SyscapTransform cost memory 0.1507720947265625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852184138700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8282d6d2-66d8-42bd-9f83-b9c542f71dc1", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852180931400, "endTime": 144852184198100}, "additional": {"logType": "info", "children": [], "durationId": "9836f0ec-ab95-456a-a293-648f899ab51b"}}, {"head": {"id": "4b21453f-e201-413c-adb8-ab7afb18bfa6", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852187623400, "endTime": 144852189600800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "845272f0-0ce9-4690-8b7e-ef95f403a065", "logId": "588b5bbf-2ff5-4d1d-8ffc-0a5650370ef7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "845272f0-0ce9-4690-8b7e-ef95f403a065", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852185563100}, "additional": {"logType": "detail", "children": [], "durationId": "4b21453f-e201-413c-adb8-ab7afb18bfa6"}}, {"head": {"id": "625795b9-1ced-4a8b-bb78-cd0a8229b025", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852186476200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a932eb26-b328-4d3a-a7ea-da3555dd2b2e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852186562700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e61bbe1a-451b-47a5-94fb-4385505ef739", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852187633600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d939c30-83e7-43f1-b624-2cddd1348c32", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852189441600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87704007-9d7f-4619-b3ee-3d8ceb16c9df", "name": "entry : default@ProcessProfile cost memory 0.12308502197265625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852189543600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "588b5bbf-2ff5-4d1d-8ffc-0a5650370ef7", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852187623400, "endTime": 144852189600800}, "additional": {"logType": "info", "children": [], "durationId": "4b21453f-e201-413c-adb8-ab7afb18bfa6"}}, {"head": {"id": "b6b6bb02-89a1-45e5-86a9-6026be130c0b", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852193574900, "endTime": 144852199454800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2b5fea62-1054-4901-9a54-a65258bb4aab", "logId": "f0710045-bed8-42e0-8ee5-084faf1c23e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b5fea62-1054-4901-9a54-a65258bb4aab", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852190950800}, "additional": {"logType": "detail", "children": [], "durationId": "b6b6bb02-89a1-45e5-86a9-6026be130c0b"}}, {"head": {"id": "97da82ee-2407-4b77-9248-e9962230176f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852191933500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "823fecaa-296e-4a7b-b73a-30bf0e640498", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852192024400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e226beac-a4e2-4de0-9fd3-bf9724b6f8b6", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852193585800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc454833-4d14-4923-948d-629305ab04d5", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852199245000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb5d72dc-a2fa-4a59-b8ed-e23f77b06877", "name": "entry : default@ProcessRouterMap cost memory 0.23523712158203125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852199396300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0710045-bed8-42e0-8ee5-084faf1c23e0", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852193574900, "endTime": 144852199454800}, "additional": {"logType": "info", "children": [], "durationId": "b6b6bb02-89a1-45e5-86a9-6026be130c0b"}}, {"head": {"id": "366dbee9-d5cc-4d72-886e-dc54aa1e6950", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852203318700, "endTime": 144852208276300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "6f57b615-651d-4e7f-a1e7-fa59a2e606de", "logId": "2a6c3984-058b-4c0c-bbba-296e3daacfc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f57b615-651d-4e7f-a1e7-fa59a2e606de", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852202215000}, "additional": {"logType": "detail", "children": [], "durationId": "366dbee9-d5cc-4d72-886e-dc54aa1e6950"}}, {"head": {"id": "3f681fa0-ba58-43f7-aab4-a07dc76cc681", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852203128100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac3a1ba7-d45b-406b-a1ec-0f0d59a06a8c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852203240800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dc1f6dc-6baa-4860-902f-b4718c05fb29", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852203325800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95784c0c-9fa7-4877-bc6d-1df656fc0acb", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852203404700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "273faaeb-c583-4166-b7e9-498d1547924a", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852206850400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "691e9a2c-d783-4a62-9c2e-65673e19e843", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852206980900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b1f28eb-5eaa-4aad-91c7-53d648e510ef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852207060100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09278d27-9d07-4421-9668-7e143b2cec91", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852207096400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "929a701d-2099-4dce-8bd6-d0bcfea293f8", "name": "entry : default@ProcessStartupConfig cost memory 0.2614898681640625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852208101200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b8bb33e-5ad6-48cd-bddd-f7013bf4ca8c", "name": "runTaskFromQueue task cost before running: 329 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852208224300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a6c3984-058b-4c0c-bbba-296e3daacfc2", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852203318700, "endTime": 144852208276300, "totalTime": 4880500}, "additional": {"logType": "info", "children": [], "durationId": "366dbee9-d5cc-4d72-886e-dc54aa1e6950"}}, {"head": {"id": "aa8a86c0-bc57-4a6f-91c4-675d1b9447ac", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852212675500, "endTime": 144852213895800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6c225ced-c485-4869-a00d-387e34171c72", "logId": "dea4bd9e-7a3b-4a18-bbcc-1faf5f7fba23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c225ced-c485-4869-a00d-387e34171c72", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852210885900}, "additional": {"logType": "detail", "children": [], "durationId": "aa8a86c0-bc57-4a6f-91c4-675d1b9447ac"}}, {"head": {"id": "094c47f6-b8ed-4d48-b9dd-29ac31764689", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852211810900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e09a6f88-ada2-4efc-81a9-dfe5a3cb8e79", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852211926200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa17ab03-1a92-43e7-9a45-b69d4414b80f", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852212688000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1a1e8f1-f79b-404d-9e45-c9125594f55b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852212822700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2dfe517-507f-4fb8-879a-e338e238684a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852212863900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61ee5fc7-ba44-4e25-b126-533d06b4e756", "name": "entry : default@BuildNativeWithNinja cost memory 0.05853271484375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852213709600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caaab171-332f-4bde-a978-799f2df1118d", "name": "runTaskFromQueue task cost before running: 335 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852213828200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dea4bd9e-7a3b-4a18-bbcc-1faf5f7fba23", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852212675500, "endTime": 144852213895800, "totalTime": 1131100}, "additional": {"logType": "info", "children": [], "durationId": "aa8a86c0-bc57-4a6f-91c4-675d1b9447ac"}}, {"head": {"id": "680b3299-5cb4-4f23-a022-a8941e837ba8", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852220510400, "endTime": 144852226633400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5acf1553-eb94-4b02-bbe2-5f4824ef3d7e", "logId": "8f7807b7-1167-4037-8ba5-7638a74fb0bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5acf1553-eb94-4b02-bbe2-5f4824ef3d7e", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852216925800}, "additional": {"logType": "detail", "children": [], "durationId": "680b3299-5cb4-4f23-a022-a8941e837ba8"}}, {"head": {"id": "56ea8bda-a501-4117-8c3f-d83f3789aa73", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852217939500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f81fa720-7242-4557-8596-b5db29843e22", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852218040300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a34893aa-27c7-409c-a625-beb5a2647b29", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852219060900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "713708c1-2050-4c38-866b-3e3b68c62194", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852222508500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "def748df-bb8f-4923-9875-29d8f9082e10", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852224777000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "856c92c9-6e61-4c87-a2ce-0ccd6ed1fc0d", "name": "entry : default@ProcessResource cost memory 0.16522216796875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852224914200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f7807b7-1167-4037-8ba5-7638a74fb0bf", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852220510400, "endTime": 144852226633400}, "additional": {"logType": "info", "children": [], "durationId": "680b3299-5cb4-4f23-a022-a8941e837ba8"}}, {"head": {"id": "bc7a3e94-5580-4615-975e-04a752f072b5", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852234605600, "endTime": 144852256034400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d61f570f-6661-4d8a-b46d-12216c8ba95c", "logId": "e7679f14-cb97-4ed5-be73-acce6012233c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d61f570f-6661-4d8a-b46d-12216c8ba95c", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852230292400}, "additional": {"logType": "detail", "children": [], "durationId": "bc7a3e94-5580-4615-975e-04a752f072b5"}}, {"head": {"id": "53e7e079-7615-4aff-be75-529ae8e1e846", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852231337900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8253de27-7d8a-4b17-ad41-ae1122c2c02b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852231454200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e96678cf-f177-4fdb-b277-3f21177d4bd5", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852234625400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "713e20d8-f004-40ee-ad05-f604cf17030c", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852255830700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8af9c3f1-457d-439b-b9e8-d91a44a54425", "name": "entry : default@GenerateLoaderJson cost memory -4.621315002441406", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852255974800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7679f14-cb97-4ed5-be73-acce6012233c", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852234605600, "endTime": 144852256034400}, "additional": {"logType": "info", "children": [], "durationId": "bc7a3e94-5580-4615-975e-04a752f072b5"}}, {"head": {"id": "7e9e40d6-07e7-46cd-8615-befa6fa1b80b", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852265589300, "endTime": 144852269695500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c9a515c7-d10b-437a-b7b3-ed37e45ed2c7", "logId": "7444d565-7877-4653-96fb-4dbdc26d31d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9a515c7-d10b-437a-b7b3-ed37e45ed2c7", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852263956800}, "additional": {"logType": "detail", "children": [], "durationId": "7e9e40d6-07e7-46cd-8615-befa6fa1b80b"}}, {"head": {"id": "79180f08-5c5d-4e22-8a68-ebac5fb824b9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852264866800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edbbbead-dad7-49f7-8b7a-fd8a7c801666", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852264962600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cab9c1e-6098-4d14-9283-ad614c72b52b", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852265599500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cecb7f05-66d7-4e13-84f4-0974cdb453bb", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852269518100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f1be5cd-8bfc-4d63-85ed-c3409d3a6b25", "name": "entry : default@ProcessLibs cost memory 0.1436767578125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852269636500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7444d565-7877-4653-96fb-4dbdc26d31d6", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852265589300, "endTime": 144852269695500}, "additional": {"logType": "info", "children": [], "durationId": "7e9e40d6-07e7-46cd-8615-befa6fa1b80b"}}, {"head": {"id": "a855a69d-9973-4973-8dd6-d0438a4609eb", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852276000700, "endTime": 144852302063600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "1a69c30f-5ddc-40de-ad94-a0aacad868e7", "logId": "effafd5e-ba2a-464e-ac7e-fd2363200ed0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a69c30f-5ddc-40de-ad94-a0aacad868e7", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852271431100}, "additional": {"logType": "detail", "children": [], "durationId": "a855a69d-9973-4973-8dd6-d0438a4609eb"}}, {"head": {"id": "a2a17668-7815-471a-9e64-d0eb3689bd22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852272389800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb160d5c-dc54-4dab-966e-fb3e22f87aed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852272479700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5b2e145-1834-4300-83c6-97f816713d7a", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852273518300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fc745d1-7cb1-4bb3-b8d5-ada4374d60a7", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852276036200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f534dbb-adf4-489c-b2db-cfa96f4baeba", "name": "Incremental task entry:default@CompileResource pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852301815900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d5003a5-450c-4bf7-9376-e137a0d48d41", "name": "entry : default@CompileResource cost memory 1.3240280151367188", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852301976900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "effafd5e-ba2a-464e-ac7e-fd2363200ed0", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852276000700, "endTime": 144852302063600}, "additional": {"logType": "info", "children": [], "durationId": "a855a69d-9973-4973-8dd6-d0438a4609eb"}}, {"head": {"id": "79b6ead8-5da5-4fa1-bc63-4fd589bfaffe", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852309255100, "endTime": 144852312733200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "186c508b-f8b9-4194-9e05-69560c79ab23", "logId": "0e2c4e2b-d28c-493b-8fd4-fb1f7a305121"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "186c508b-f8b9-4194-9e05-69560c79ab23", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852304710500}, "additional": {"logType": "detail", "children": [], "durationId": "79b6ead8-5da5-4fa1-bc63-4fd589bfaffe"}}, {"head": {"id": "22bbe0f0-2937-4b40-8a20-eaa447b93966", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852305765000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae0a35ec-c650-4642-a6e2-63f186b98edd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852305954400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "005be55e-7e61-48d1-81f1-bdeb32984aac", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852309274800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09ded743-edd8-4d03-bc89-1f9f9c775601", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852310643000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6901c7b0-1625-4ec8-bd22-43fdfd977c5c", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852312476100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e5ddf29-c399-4550-8563-f189d3ee358a", "name": "entry : default@DoNativeStrip cost memory 0.08040618896484375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852312664000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e2c4e2b-d28c-493b-8fd4-fb1f7a305121", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852309255100, "endTime": 144852312733200}, "additional": {"logType": "info", "children": [], "durationId": "79b6ead8-5da5-4fa1-bc63-4fd589bfaffe"}}, {"head": {"id": "cf60fe55-8575-4b37-ad73-119605ce9a51", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852318672100, "endTime": 144861823137200}, "additional": {"children": ["3be145ab-79f8-43cc-9780-aa8ebc28d7dd", "0e59a61f-1af2-4fc4-b09c-f0120baf81e4"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "2a7a65b4-9eec-4a84-9de6-2d606b7ea5cd", "logId": "d8d0da65-25ca-40a4-9294-2c1f7a3032c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a7a65b4-9eec-4a84-9de6-2d606b7ea5cd", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852314208400}, "additional": {"logType": "detail", "children": [], "durationId": "cf60fe55-8575-4b37-ad73-119605ce9a51"}}, {"head": {"id": "3f129091-42ab-47a2-8dab-f2fc1be3419c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852315104700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9fccc4c-6dcd-439c-bda5-cac3601e6eba", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852315204200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cbd40af-afed-4ffd-b19a-ae243f14cb2a", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852318687700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b163193-4b3a-4e0e-9f23-0abfc6a76587", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852318850000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7af3602-c63a-4c69-b557-a7548ff38901", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852348611500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffaee7e2-b09f-452b-801a-ad2dfecf4afe", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852348804000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe5d2d03-b824-47ea-9e34-c7ac563ac74b", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852367550200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b90ae83b-c493-41e1-aeb7-a81f36fb0c1e", "name": "default@CompileArkTS work[2] is submitted.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852369153100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3be145ab-79f8-43cc-9780-aa8ebc28d7dd", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144854906997500, "endTime": 144861816650400}, "additional": {"children": ["98870ef5-4989-4a3f-8a41-b523e43f519b", "365fdc93-7588-4eea-83fb-041a9f57316c", "c13e8885-2708-44b4-a9b7-235e5b4d0c37", "261efed2-f125-452a-9d32-71fb49601f55", "a84410d2-2da5-4753-a373-c24e6708d8e9", "fe202f67-220e-4f1f-8308-d141466aa344", "c0c7db37-f758-4f2e-8b94-718f05b7f8d0"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "cf60fe55-8575-4b37-ad73-119605ce9a51", "logId": "e3dfa533-d0d7-4704-a53b-1e0bdb411315"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c13bf0c7-f6a1-4f64-923e-a7e82a2ac2d8", "name": "default@CompileArkTS work[2] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370159200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e40144af-6995-46cc-9046-cd996c20f10e", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370284700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86d48220-0530-4879-8003-760de0cbc5ec", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370329500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb371828-44d5-4670-9659-5d8c99f270a6", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370360800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e702772-ffe2-49ac-a5e6-53e54a0803a9", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370390200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "740e8629-514e-4a15-b62b-3c69f99200f0", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370415700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db09b5a6-1de7-41ff-b968-46153092e8e1", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370442300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "551543e0-9618-4747-bb03-80b1d0d4e92c", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370475200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be717cd2-f466-4fcf-b452-1c99c86ccc5e", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370508000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8ecdc92-da08-4d5f-9ba3-c13d2bb2a33c", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370534100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e93c4ea2-f406-4ee5-ba41-575bee55a8e6", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370560500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe5f8ab3-8241-413e-b9eb-9004000cfd87", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370585700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd0ddc75-1851-4e35-9d03-a763f44c79ec", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370610900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48a82464-018a-4715-942c-1ba9ec76188a", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370637100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0620d5f3-2941-4482-bded-b1adb41958cd", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370662300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "369228ea-5def-4262-9c0e-1fd5dd76b6ca", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370686900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c916f903-ea71-4720-9263-6452e6f2b70d", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852370731500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a298d05-cd0c-445d-abb2-3592674f6a6d", "name": "default@CompileArkTS work[2] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852371682600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5be81fa5-52c4-4542-855a-a51c15326dbb", "name": "default@CompileArkTS work[2] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852371789100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f842d206-fdd4-4334-adab-35fdeb43f37c", "name": "CopyResources startTime: 144852371834800", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852371837600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cda1c39f-6c47-4a03-b690-6a1c653e8b94", "name": "default@CompileArkTS work[3] is submitted.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852371895200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e59a61f-1af2-4fc4-b09c-f0120baf81e4", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker18", "startTime": 144853406297400, "endTime": 144853420567100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "cf60fe55-8575-4b37-ad73-119605ce9a51", "logId": "f4e496dc-eb06-4474-91d9-9321f30dec8e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a37f00f6-d4fa-4e97-ad76-38669728060b", "name": "default@CompileArkTS work[3] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852372854700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74d8c37b-9faf-4c78-8883-2e13f5d27bc5", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852372993200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca66c502-2951-410c-b4bf-c11749b7eea1", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852373083100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4da1ca63-f40a-4dfb-901d-c5bab527ec10", "name": "default@CompileArkTS work[3] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852374095300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6bb29a2-47fb-4be0-b62e-aad30afc9a39", "name": "default@CompileArkTS work[3] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852374254200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5004f39-1c89-4d43-a9fa-eb010681c1c9", "name": "entry : default@CompileArkTS cost memory 2.3639602661132812", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852374422500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0802d1b1-16ab-465a-b71a-c146579f2e09", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852390764700, "endTime": 144852404414700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "b93ae8af-3152-4190-9a8a-1891a8bad4d0", "logId": "51b0cf76-3d07-4a28-8515-232a5abc7707"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b93ae8af-3152-4190-9a8a-1891a8bad4d0", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852378098500}, "additional": {"logType": "detail", "children": [], "durationId": "0802d1b1-16ab-465a-b71a-c146579f2e09"}}, {"head": {"id": "17ca1b6f-f66c-401a-a914-fa6db33cbdac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852380547200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "674cb3ae-d237-4dee-be3a-ab186a419e27", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852380726700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46e38234-11d1-47ed-9ad4-45921235fcc3", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852390796400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "135871c9-60c1-47cb-9c38-712ba78bdeda", "name": "entry : default@BuildJS cost memory 0.34589385986328125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852404217300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "916a2d0b-4de5-4c5e-a20d-e8228780d8ff", "name": "runTaskFromQueue task cost before running: 526 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852404362300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51b0cf76-3d07-4a28-8515-232a5abc7707", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852390764700, "endTime": 144852404414700, "totalTime": 13579600}, "additional": {"logType": "info", "children": [], "durationId": "0802d1b1-16ab-465a-b71a-c146579f2e09"}}, {"head": {"id": "aa032aaa-13c4-4560-91a5-f2b56fba39b0", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852409521000, "endTime": 144852414068200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4e5e9df8-d4c4-44aa-9edc-fcf42eb476b5", "logId": "86202a20-2dcb-40b9-a01d-084fc267cfbc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e5e9df8-d4c4-44aa-9edc-fcf42eb476b5", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852405823600}, "additional": {"logType": "detail", "children": [], "durationId": "aa032aaa-13c4-4560-91a5-f2b56fba39b0"}}, {"head": {"id": "73e94498-5e9b-42dd-b832-459cb271d3b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852406965700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb52241b-c017-49dc-a5d1-73c04073f4f6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852407072800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca15d183-22a4-4962-a68e-d5d9745c530a", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852409535600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0633f34b-305d-4123-a8d8-57faade04a37", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852411308700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ced620a3-fd0c-478c-ba6f-c44ad57a277b", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852413869900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed9c2cd6-0741-408a-b890-faca1f9d6f46", "name": "entry : default@CacheNativeLibs cost memory -7.050407409667969", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852414002100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86202a20-2dcb-40b9-a01d-084fc267cfbc", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852409521000, "endTime": 144852414068200}, "additional": {"logType": "info", "children": [], "durationId": "aa032aaa-13c4-4560-91a5-f2b56fba39b0"}}, {"head": {"id": "8ab16a99-a7a6-4203-9ec4-d74c99ae5675", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144853421007000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41686379-5d6e-4f65-82dd-bb63bf973ecb", "name": "CopyResources is end, endTime: 144853421172000", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144853421177100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fbac25b-8cc1-48e5-9852-0262bfc49022", "name": "default@CompileArkTS work[3] done.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144853421269000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4e496dc-eb06-4474-91d9-9321f30dec8e", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Worker18", "startTime": 144853406297400, "endTime": 144853420567100}, "additional": {"logType": "info", "children": [], "durationId": "0e59a61f-1af2-4fc4-b09c-f0120baf81e4", "parent": "d8d0da65-25ca-40a4-9294-2c1f7a3032c1"}}, {"head": {"id": "3d84195f-eeec-40f1-a967-b3ab1157720e", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144853421342900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c3756db-44af-4f1f-90b7-43198d289eeb", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861816962900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98870ef5-4989-4a3f-8a41-b523e43f519b", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144854908011300, "endTime": 144855899541100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3be145ab-79f8-43cc-9780-aa8ebc28d7dd", "logId": "18b89aec-e544-4c65-bdfa-bb5c0477c781"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18b89aec-e544-4c65-bdfa-bb5c0477c781", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144854908011300, "endTime": 144855899541100}, "additional": {"logType": "info", "children": [], "durationId": "98870ef5-4989-4a3f-8a41-b523e43f519b", "parent": "e3dfa533-d0d7-4704-a53b-1e0bdb411315"}}, {"head": {"id": "365fdc93-7588-4eea-83fb-041a9f57316c", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144855901004800, "endTime": 144855952202300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3be145ab-79f8-43cc-9780-aa8ebc28d7dd", "logId": "91cffe7a-4272-4a7b-b0bb-af4217474889"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91cffe7a-4272-4a7b-b0bb-af4217474889", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144855901004800, "endTime": 144855952202300}, "additional": {"logType": "info", "children": [], "durationId": "365fdc93-7588-4eea-83fb-041a9f57316c", "parent": "e3dfa533-d0d7-4704-a53b-1e0bdb411315"}}, {"head": {"id": "c13e8885-2708-44b4-a9b7-235e5b4d0c37", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144855952301100, "endTime": 144855952537600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3be145ab-79f8-43cc-9780-aa8ebc28d7dd", "logId": "327d91e9-7ced-4944-95d0-b312ebc3bd38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "327d91e9-7ced-4944-95d0-b312ebc3bd38", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144855952301100, "endTime": 144855952537600}, "additional": {"logType": "info", "children": [], "durationId": "c13e8885-2708-44b4-a9b7-235e5b4d0c37", "parent": "e3dfa533-d0d7-4704-a53b-1e0bdb411315"}}, {"head": {"id": "261efed2-f125-452a-9d32-71fb49601f55", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144855952598700, "endTime": 144861594261100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3be145ab-79f8-43cc-9780-aa8ebc28d7dd", "logId": "ebc2f31b-c34a-4659-b342-0238e75179a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ebc2f31b-c34a-4659-b342-0238e75179a0", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144855952598700, "endTime": 144861594261100}, "additional": {"logType": "info", "children": [], "durationId": "261efed2-f125-452a-9d32-71fb49601f55", "parent": "e3dfa533-d0d7-4704-a53b-1e0bdb411315"}}, {"head": {"id": "a84410d2-2da5-4753-a373-c24e6708d8e9", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144861594786600, "endTime": 144861606436300}, "additional": {"children": ["25d7c990-5347-46d1-af2b-7e243717f5c5", "3b3a36c7-42ee-4fad-9aeb-47baa0071717", "bb3dbfe3-2efc-48fe-ae69-34f76c5906fc"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3be145ab-79f8-43cc-9780-aa8ebc28d7dd", "logId": "7b0c9cfd-c03e-4e94-b816-df9263470c4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b0c9cfd-c03e-4e94-b816-df9263470c4c", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861594786600, "endTime": 144861606436300}, "additional": {"logType": "info", "children": ["ee580609-84ba-4f4d-977e-c8e0137fac67", "e891cf62-a4ce-4cc1-94ca-6e0c796fcb82", "4a404d31-cff3-49df-9079-f4099fe716e8"], "durationId": "a84410d2-2da5-4753-a373-c24e6708d8e9", "parent": "e3dfa533-d0d7-4704-a53b-1e0bdb411315"}}, {"head": {"id": "25d7c990-5347-46d1-af2b-7e243717f5c5", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144861594980800, "endTime": 144861595003500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a84410d2-2da5-4753-a373-c24e6708d8e9", "logId": "ee580609-84ba-4f4d-977e-c8e0137fac67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee580609-84ba-4f4d-977e-c8e0137fac67", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861594980800, "endTime": 144861595003500}, "additional": {"logType": "info", "children": [], "durationId": "25d7c990-5347-46d1-af2b-7e243717f5c5", "parent": "7b0c9cfd-c03e-4e94-b816-df9263470c4c"}}, {"head": {"id": "3b3a36c7-42ee-4fad-9aeb-47baa0071717", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144861595014100, "endTime": 144861601748400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a84410d2-2da5-4753-a373-c24e6708d8e9", "logId": "e891cf62-a4ce-4cc1-94ca-6e0c796fcb82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e891cf62-a4ce-4cc1-94ca-6e0c796fcb82", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861595014100, "endTime": 144861601748400}, "additional": {"logType": "info", "children": [], "durationId": "3b3a36c7-42ee-4fad-9aeb-47baa0071717", "parent": "7b0c9cfd-c03e-4e94-b816-df9263470c4c"}}, {"head": {"id": "bb3dbfe3-2efc-48fe-ae69-34f76c5906fc", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144861601756900, "endTime": 144861606254700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a84410d2-2da5-4753-a373-c24e6708d8e9", "logId": "4a404d31-cff3-49df-9079-f4099fe716e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a404d31-cff3-49df-9079-f4099fe716e8", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861601756900, "endTime": 144861606254700}, "additional": {"logType": "info", "children": [], "durationId": "bb3dbfe3-2efc-48fe-ae69-34f76c5906fc", "parent": "7b0c9cfd-c03e-4e94-b816-df9263470c4c"}}, {"head": {"id": "fe202f67-220e-4f1f-8308-d141466aa344", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144861606488700, "endTime": 144861814480100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3be145ab-79f8-43cc-9780-aa8ebc28d7dd", "logId": "a04e3398-f3f6-4470-be18-2f63c1c96205"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a04e3398-f3f6-4470-be18-2f63c1c96205", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861606488700, "endTime": 144861814480100}, "additional": {"logType": "info", "children": [], "durationId": "fe202f67-220e-4f1f-8308-d141466aa344", "parent": "e3dfa533-d0d7-4704-a53b-1e0bdb411315"}}, {"head": {"id": "c0c7db37-f758-4f2e-8b94-718f05b7f8d0", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144853326684000, "endTime": 144854905545200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3be145ab-79f8-43cc-9780-aa8ebc28d7dd", "logId": "95693a57-3623-47aa-893a-fc32ca350317"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95693a57-3623-47aa-893a-fc32ca350317", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144853326684000, "endTime": 144854905545200}, "additional": {"logType": "info", "children": [], "durationId": "c0c7db37-f758-4f2e-8b94-718f05b7f8d0", "parent": "e3dfa533-d0d7-4704-a53b-1e0bdb411315"}}, {"head": {"id": "faf05cbf-60fc-4e24-916d-d4272b47d309", "name": "default@CompileArkTS work[2] done.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861822895400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3dfa533-d0d7-4704-a53b-1e0bdb411315", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Worker4", "startTime": 144854906997500, "endTime": 144861816650400}, "additional": {"logType": "info", "children": ["18b89aec-e544-4c65-bdfa-bb5c0477c781", "91cffe7a-4272-4a7b-b0bb-af4217474889", "327d91e9-7ced-4944-95d0-b312ebc3bd38", "ebc2f31b-c34a-4659-b342-0238e75179a0", "7b0c9cfd-c03e-4e94-b816-df9263470c4c", "a04e3398-f3f6-4470-be18-2f63c1c96205", "95693a57-3623-47aa-893a-fc32ca350317"], "durationId": "3be145ab-79f8-43cc-9780-aa8ebc28d7dd", "parent": "d8d0da65-25ca-40a4-9294-2c1f7a3032c1"}}, {"head": {"id": "466fb583-1aa8-4181-811b-e41ae3a59ca0", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861823067200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8d0da65-25ca-40a4-9294-2c1f7a3032c1", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144852318672100, "endTime": 144861823137200, "totalTime": 6979786900}, "additional": {"logType": "info", "children": ["e3dfa533-d0d7-4704-a53b-1e0bdb411315", "f4e496dc-eb06-4474-91d9-9321f30dec8e"], "durationId": "cf60fe55-8575-4b37-ad73-119605ce9a51"}}, {"head": {"id": "7b055f21-c3da-490e-9771-f454649356bd", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861831290000, "endTime": 144861832700500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "44857a11-5b22-4ea3-b300-daab30a3a8f9", "logId": "f3bc946a-7402-4965-b022-1060d5a80e4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44857a11-5b22-4ea3-b300-daab30a3a8f9", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861829257500}, "additional": {"logType": "detail", "children": [], "durationId": "7b055f21-c3da-490e-9771-f454649356bd"}}, {"head": {"id": "9e4476a8-b665-4824-b1d8-b57fda9206d3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861830219300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76808e40-d1f3-4dcb-bca5-93ff301d5d89", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861830323000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ef90454-60e8-43da-9bac-69f07e57cf65", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861831300400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f7f86b1-5c90-474f-a7b3-adc99082ec21", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861831639800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad5f92ea-dd20-41a4-80f2-fd72b9fd5b19", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861832527100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da9dd395-6ca2-4b7e-9fa5-97a20c533808", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07881927490234375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861832644700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3bc946a-7402-4965-b022-1060d5a80e4b", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861831290000, "endTime": 144861832700500}, "additional": {"logType": "info", "children": [], "durationId": "7b055f21-c3da-490e-9771-f454649356bd"}}, {"head": {"id": "9b880fe9-c166-4f19-a89d-27aae6ef5b73", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861842883100, "endTime": 144861994278800}, "additional": {"children": ["fa425ba0-3487-41f6-b306-cdf3796e27fa", "574c61bd-93d5-42d7-93c8-64d16a931135"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "30f71ee6-904c-42c9-b889-fadf1eb4d1d4", "logId": "af584d07-da71-4a80-819c-49b63647c2e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30f71ee6-904c-42c9-b889-fadf1eb4d1d4", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861834775600}, "additional": {"logType": "detail", "children": [], "durationId": "9b880fe9-c166-4f19-a89d-27aae6ef5b73"}}, {"head": {"id": "f46bb7b9-b8a6-4a5c-81a2-c6516e310ac5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861835696300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40d09ed0-d722-4d1f-8c11-72450a0338b5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861835793800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f2ee008-8275-482e-ae78-03160046b018", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861842897000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d65e0a1-4b93-4c99-9bbb-97de6341560b", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861857356200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0a21fa3-798c-4e94-b480-902b8c59a6b6", "name": "Incremental task entry:default@PackageHap pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861857516000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8412717b-51ac-4691-a7e9-a6ce7e0a4f85", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861857586000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0745512d-34f2-445f-9eb0-d7c3656dbed3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861858046500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa425ba0-3487-41f6-b306-cdf3796e27fa", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861859169900, "endTime": 144861861294000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b880fe9-c166-4f19-a89d-27aae6ef5b73", "logId": "57f77ef7-57e1-48db-adae-d1d946ba22fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4004b27c-c8a7-4e4a-bb3e-85d6dd13e8de", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861861139600}, "additional": {"logType": "debug", "children": [], "durationId": "9b880fe9-c166-4f19-a89d-27aae6ef5b73"}}, {"head": {"id": "57f77ef7-57e1-48db-adae-d1d946ba22fb", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861859169900, "endTime": 144861861294000}, "additional": {"logType": "info", "children": [], "durationId": "fa425ba0-3487-41f6-b306-cdf3796e27fa", "parent": "af584d07-da71-4a80-819c-49b63647c2e3"}}, {"head": {"id": "574c61bd-93d5-42d7-93c8-64d16a931135", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861862076400, "endTime": 144861986145000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b880fe9-c166-4f19-a89d-27aae6ef5b73", "logId": "9baee489-8242-4a3b-beec-162e8a355964"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc6a37dd-1b2e-4608-99c5-df36f780601c", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861985521000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9baee489-8242-4a3b-beec-162e8a355964", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861862076400, "endTime": 144861986137100}, "additional": {"logType": "info", "children": [], "durationId": "574c61bd-93d5-42d7-93c8-64d16a931135", "parent": "af584d07-da71-4a80-819c-49b63647c2e3"}}, {"head": {"id": "8efd2b0b-f02d-4415-bc1f-eb529f1675c0", "name": "entry : default@PackageHap cost memory 0.7542495727539062", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861993853300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "877cd34f-a763-4258-8fe0-a77a85061f81", "name": "runTaskFromQueue task cost before running: 10 s 115 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861994149100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af584d07-da71-4a80-819c-49b63647c2e3", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144861842883100, "endTime": 144861994278800, "totalTime": 151212300}, "additional": {"logType": "info", "children": ["57f77ef7-57e1-48db-adae-d1d946ba22fb", "9baee489-8242-4a3b-beec-162e8a355964"], "durationId": "9b880fe9-c166-4f19-a89d-27aae6ef5b73"}}, {"head": {"id": "c50ccb11-1f33-4e76-b508-7e0806f41585", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862005622000, "endTime": 144862288439600}, "additional": {"children": ["b7c5664f-b76b-4899-8cf5-50003d528080", "68e627a4-dcc9-45cc-9269-8fd7021beaf1"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "394ae01a-0649-471f-9722-b4272266e553", "logId": "354e50df-9eda-4719-ad50-f5d7bd9e15d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "394ae01a-0649-471f-9722-b4272266e553", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862000691500}, "additional": {"logType": "detail", "children": [], "durationId": "c50ccb11-1f33-4e76-b508-7e0806f41585"}}, {"head": {"id": "638d3570-1a73-4532-9000-02bb08cf8cbc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862001790600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8075842-6767-4b97-bc2e-8ac4bc055804", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862001909100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "251731da-9278-4c3d-ad6a-aa6819ac8a45", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862005649500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "899d4136-d6af-4504-a032-ec2524e1fee5", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862007765400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6bc2cce-59f7-4c30-ac35-d9be3098d6fd", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862007950600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f49df25c-5f46-48a2-958a-0600bb0b39c2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862008033900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d82ebfe5-b60b-4211-81ac-26e939bcdec4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862008085800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7c5664f-b76b-4899-8cf5-50003d528080", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862009857700, "endTime": 144862090304600}, "additional": {"children": ["1f080260-fa4f-426f-90e9-8ee4dc8ead90"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c50ccb11-1f33-4e76-b508-7e0806f41585", "logId": "1f7c9761-35ca-4ae1-a3de-4f7c1011168f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f080260-fa4f-426f-90e9-8ee4dc8ead90", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862029295300, "endTime": 144862089214000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b7c5664f-b76b-4899-8cf5-50003d528080", "logId": "de62df3f-a357-481c-a200-a88c32fd8e4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab6bac61-f017-4f9f-a48f-7003b19dc4c8", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862031913500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "540d13e2-fcd9-43c4-9048-aa50e05e3969", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862088625600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de62df3f-a357-481c-a200-a88c32fd8e4a", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862029295300, "endTime": 144862089214000}, "additional": {"logType": "info", "children": [], "durationId": "1f080260-fa4f-426f-90e9-8ee4dc8ead90", "parent": "1f7c9761-35ca-4ae1-a3de-4f7c1011168f"}}, {"head": {"id": "1f7c9761-35ca-4ae1-a3de-4f7c1011168f", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862009857700, "endTime": 144862090304600}, "additional": {"logType": "info", "children": ["de62df3f-a357-481c-a200-a88c32fd8e4a"], "durationId": "b7c5664f-b76b-4899-8cf5-50003d528080", "parent": "354e50df-9eda-4719-ad50-f5d7bd9e15d8"}}, {"head": {"id": "68e627a4-dcc9-45cc-9269-8fd7021beaf1", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862090933700, "endTime": 144862287938900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c50ccb11-1f33-4e76-b508-7e0806f41585", "logId": "f5d2233d-7c9f-49f2-8db2-2fe76ac444c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d71fd6b5-9b7a-4144-b3f6-48d4f6762f84", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862092782300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7ca544a-2552-4f91-ac2e-a92ee35aeba7", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862287375600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5d2233d-7c9f-49f2-8db2-2fe76ac444c3", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862090933700, "endTime": 144862287938900}, "additional": {"logType": "info", "children": [], "durationId": "68e627a4-dcc9-45cc-9269-8fd7021beaf1", "parent": "354e50df-9eda-4719-ad50-f5d7bd9e15d8"}}, {"head": {"id": "be4fc86a-29bf-4f0f-9a2c-41568922ccfc", "name": "entry : default@SignHap cost memory 0.082427978515625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862288222300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95fa76cc-aa70-4ac6-aadf-e50b6b8a49d1", "name": "runTaskFromQueue task cost before running: 10 s 410 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862288365000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "354e50df-9eda-4719-ad50-f5d7bd9e15d8", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862005622000, "endTime": 144862288439600, "totalTime": 282716900}, "additional": {"logType": "info", "children": ["1f7c9761-35ca-4ae1-a3de-4f7c1011168f", "f5d2233d-7c9f-49f2-8db2-2fe76ac444c3"], "durationId": "c50ccb11-1f33-4e76-b508-7e0806f41585"}}, {"head": {"id": "49bec177-91b4-4e1d-b35a-266853a0275f", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862292268700, "endTime": 144862298014700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ba648f92-7a75-4d7b-b4ac-14ad741fdfa3", "logId": "12b8a4b0-436e-415e-a9ad-ae67dae04077"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba648f92-7a75-4d7b-b4ac-14ad741fdfa3", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862290391000}, "additional": {"logType": "detail", "children": [], "durationId": "49bec177-91b4-4e1d-b35a-266853a0275f"}}, {"head": {"id": "8620f3ac-8968-4796-9b7a-729503dcd4f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862291453200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6851b87-ba6c-4d04-b184-8586c8cd5c13", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862291546300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96b3267f-cd55-4fda-8117-1d9f1308f3bd", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862292277100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d058fae-f6dc-4d17-8ed2-42784d819954", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862297692200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cc09716-e1ad-4d95-aafe-0f49db5bc571", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862297824300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79609047-c0cb-41cb-99c8-593c2b5dd1dd", "name": "entry : default@CollectDebugSymbol cost memory 0.245086669921875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862297904800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f04baefb-af65-4709-aab4-aa4da89cdef5", "name": "runTaskFromQueue task cost before running: 10 s 419 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862297977100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12b8a4b0-436e-415e-a9ad-ae67dae04077", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862292268700, "endTime": 144862298014700, "totalTime": 5686500}, "additional": {"logType": "info", "children": [], "durationId": "49bec177-91b4-4e1d-b35a-266853a0275f"}}, {"head": {"id": "e93ab515-4c29-4efe-a28c-b23a35ece3e5", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862299632700, "endTime": 144862299892200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "8adbb0e8-b459-44ff-8ec5-b19c92577654", "logId": "d106149e-b855-44e3-9153-bb645dae845f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8adbb0e8-b459-44ff-8ec5-b19c92577654", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862299585500}, "additional": {"logType": "detail", "children": [], "durationId": "e93ab515-4c29-4efe-a28c-b23a35ece3e5"}}, {"head": {"id": "30694f8f-634b-448e-ae17-8d1dd290df56", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862299639400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0581088-d126-43db-b6cc-33be0a244b4a", "name": "entry : assembleHap cost memory 0.011749267578125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862299781700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc75a3e5-8432-41d4-a083-1a9f9ab207ad", "name": "runTaskFromQueue task cost before running: 10 s 421 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862299852500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d106149e-b855-44e3-9153-bb645dae845f", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862299632700, "endTime": 144862299892200, "totalTime": 199900}, "additional": {"logType": "info", "children": [], "durationId": "e93ab515-4c29-4efe-a28c-b23a35ece3e5"}}, {"head": {"id": "5e5990c6-4779-48b2-b719-39fd136a45d7", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862316798000, "endTime": 144862316823200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67949988-be49-4ed6-9eee-0c671ad5e031", "logId": "1af0cae1-948c-4ff3-b644-534ce674e670"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1af0cae1-948c-4ff3-b644-534ce674e670", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862316798000, "endTime": 144862316823200}, "additional": {"logType": "info", "children": [], "durationId": "5e5990c6-4779-48b2-b719-39fd136a45d7"}}, {"head": {"id": "1944255c-5097-4a14-9fbb-a84113f59f5a", "name": "BUILD SUCCESSFUL in 10 s 438 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862316867600}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "ee45e750-1589-49da-8482-cd107e8589c3", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144851879326900, "endTime": 144862317200800}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 11, "second": 32}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "b80574d8-5505-4323-ba6d-de2f1c91d553", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862317226800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b01dad27-cfe9-491e-b8ec-cbc5fdd334ba", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862317313700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0e788a6-f1d0-49e6-9777-659d0a904cf6", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862317724400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61b07a4b-597b-493f-a221-81ae6e1d340e", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862317800300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6d5cc1a-21c0-4679-a85c-0083295c6a1e", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862317840000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "986642d8-d16f-458f-8fff-f5dda18689d3", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862317869600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfcf8d12-bfce-4f15-81f7-2bc10ed311e8", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862317899100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c1fd8c5-2597-4a9f-ab1c-06a2dd1874ce", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862318458000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83b76e6d-e68c-4a11-aa72-317257ab25ca", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862318658800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6170e208-26f8-456e-9aed-e67cb55544fd", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862318705900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "053d79ff-c1ed-4f08-8328-abfb39e7eb04", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862318735800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f727c44c-5909-442c-be98-b1143dd41605", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862318762300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad916feb-9fc5-4761-bb10-f18d2bd90392", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862318788800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df93e236-41c0-4bce-8b01-a3aee7eae94a", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862319924400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45c7d97e-8d94-49ac-8e40-3651ad142b2d", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862320221200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9238b64-568b-4d8d-bb2c-e0d2bb1fffb0", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862320430500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bc3cc5c-d005-4215-8662-9644276e5723", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862320494200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf1fcc0f-1c3c-4853-b5a0-2b6877a25674", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862320531300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87ae396e-7d3c-4ffb-a40c-a074008b70a1", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862320561400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf60d5e5-4039-426c-b6f7-bf340b3548c3", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862320594200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f945f22-1685-41c5-8f5e-029e400b75de", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862320623800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e215c74-6c06-4adf-aefd-b28a56912b95", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862323852900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58535526-431a-409b-a02c-74340d5d71af", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862324621400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "975cf4b3-bd48-4485-bb44-9239c3f5bed8", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862325045300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5765e36-e957-4a33-87f3-409e0f1dc38a", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862325320800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78b49601-d206-4cb4-b97d-6508bcf8e1e4", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862325526200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "595096d9-0bf7-4776-b38c-30eea211ca23", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862326214700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7217c97-238e-404d-88fe-0c96ef352f22", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862326289400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58f8900a-f545-48aa-9533-497e7d93dd8c", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862326473200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c83d059c-1780-4656-8e4f-13cc605c5ec7", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862326796900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b82d20f1-a5bd-4ce1-89b1-29770e917a38", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862327713700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "998c1001-8c87-4ca3-97e5-223445223f80", "name": "Incremental task entry:default@CompileArkTS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862328367400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fc4c5da-232a-407a-b438-be371a604e5c", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862330816300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6068bef4-3aba-4ae8-a6b0-998fd37354ac", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862331487700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0c3af09-3b3e-4176-bfc5-809eedeeee5f", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862331860900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e829802-dadb-45cc-8699-a241411244a3", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862332113500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08fa0f21-bae3-451d-bbb9-81c331f2e735", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862332321200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d230ab14-d814-4274-8dfa-093205fa3eed", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862333036900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "192227ae-081d-43af-8c5f-5f70ca309ebd", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862333882300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62181f75-a6d2-46d6-90db-a9e2127a0fd6", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862334169200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ffb4682-9acc-4d61-913a-0f550b990caf", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862334235100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5897d6d2-8d69-41bb-b5a9-e913ac6b5af9", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862334271900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a997e23f-e486-479c-b4ff-2c3dde1eca3f", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862335634500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42e94e4a-4116-4907-b945-7e390c476618", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862336076600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d57ef317-c69e-40f4-8aca-c65e00cd1403", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862336419200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b08c76f8-6329-461a-b016-96882a48679c", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862342719500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebcc3fe6-186b-446e-84f5-549f06a0951d", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862343365100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9522f9ae-1d01-48bc-bec0-0cb042918fc1", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862343591200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45987dc4-edb7-4d3d-a962-1811c9da1e8b", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862343778600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "566b9677-59a9-45bb-bab7-83a5bb2d0648", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862343832800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3ffd701-6c01-450e-8948-1950ec458e5e", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862343999800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fd40be2-c996-4221-8861-83fe58b754e3", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862344195100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caebab82-2ea2-43cf-bfb1-9f31ea316844", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862344982200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5099d520-028e-4319-8961-b0d0d7a371b9", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862345336300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ce0441a-d1de-4e51-9ba3-51d8484e9222", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862345550900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35bf35e5-651c-40c7-ba4b-e258704bbd1f", "name": "Incremental task entry:default@PackageHap post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862345797500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ee21749-1d34-4e87-8694-f1e369a47dd0", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862346037900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d57880c-b73f-4d01-b874-55142c696b8b", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862346257600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0342aa6c-efc8-4356-9fb4-788aab82eec4", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862346454000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ee43ef0-ee00-452e-8e00-84eed172be98", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862346642500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "107b31d6-2b08-4cac-b53a-e504e446b191", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862346701000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d99ceda7-5542-4244-8e42-f2e1d3c8a088", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862346919600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68f6ed0c-117b-41d2-b92a-a0b3f613c2d5", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862349379100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd194a23-ebe0-4db5-9186-6ce93c5cf512", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862349641100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cccac15a-0675-4ada-b8c1-e283e96bf07c", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862350079200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a05683fd-0d04-4935-af01-92fea6702aec", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 144862350338200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}