"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { Link, useSearchParams } from "@umijs/max";
import { But<PERSON>, Result } from "antd";
import useStyles from "./style.style";
const RegisterResult = () => {
  const { styles } = useStyles();
  const [params] = useSearchParams();
  const actions = /* @__PURE__ */ jsxs("div", { className: styles.actions, children: [
    /* @__PURE__ */ jsx(Link, { to: "/user/login", children: /* @__PURE__ */ jsx(But<PERSON>, { size: "large", type: "primary", children: /* @__PURE__ */ jsx("span", { children: "\u7ACB\u5373\u767B\u5F55" }) }) }),
    /* @__PURE__ */ jsx(Link, { to: "/", children: /* @__PURE__ */ jsx(<PERSON><PERSON>, { size: "large", children: "\u8FD4\u56DE\u9996\u9875" }) })
  ] });
  const email = params?.get("account") || "<EMAIL>";
  return /* @__PURE__ */ jsx(
    Result,
    {
      className: styles.registerResult,
      status: "success",
      title: /* @__PURE__ */ jsx("div", { className: styles.title, children: /* @__PURE__ */ jsx("span", { children: "\u8D26\u6237\u6CE8\u518C\u6210\u529F\uFF01" }) }),
      subTitle: `\u606D\u559C\u60A8\uFF01\u8D26\u6237 ${email} \u5DF2\u6210\u529F\u6CE8\u518C\u3002\u60A8\u73B0\u5728\u53EF\u4EE5\u4F7F\u7528\u7528\u6237\u540D\u6216\u90AE\u7BB1\u767B\u5F55\u7CFB\u7EDF\uFF0C\u5F00\u59CB\u4F7F\u7528NexusHub\u7684\u6240\u6709\u529F\u80FD\u3002`,
      extra: actions
    }
  );
};
export default RegisterResult;
