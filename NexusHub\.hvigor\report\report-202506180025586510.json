{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "8b3ccb22-def5-46ab-b006-bfe11a19aeb3", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711244314900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b979b86-a08a-4351-adb2-7e9a5047086a", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711244472600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3284d04-05d2-4766-8472-22125cc49551", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711244672600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b9aae30-8663-4396-9dcb-909a9cbb74b2", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711247183300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d49f8d7-8c40-4e40-a2d3-4de4d3dc8ab2", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711247467300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61156c78-c155-4049-af8c-6c9060bb3a7c", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711248174900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee810c96-e5fe-4f7a-9cbc-c1d59df0c35b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711248384400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6d2b461-f85e-40f6-8181-35cd228f69a8", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711249494300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09be5164-54d3-458f-b89d-c4f94f9b0dbe", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711286974100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "209fb97f-c6c8-4569-af48-87d4192e77a5", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928867050900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23003238-80c2-455e-aae0-98b8b580a832", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928874971300, "endTime": 152929160039500}, "additional": {"children": ["2753ddbb-700c-4593-9e69-2e02fcce5e9f", "58080bb4-00c2-46bf-a685-b772bf3f8f44", "6d4e0f13-cd0f-494f-8e77-49be24d9e85f", "c2dddd83-66c4-4108-87f0-65f03e384a07", "5790e539-5348-4ec5-b235-6050d825f525", "a1a10247-665e-468a-856e-164a265805b3", "b7eaea6b-3ae5-4b7b-bdc1-f8bdcc9f3467"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "31522bb9-f587-4ce5-b12b-27b649490cc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2753ddbb-700c-4593-9e69-2e02fcce5e9f", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928874972700, "endTime": 152928890782400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23003238-80c2-455e-aae0-98b8b580a832", "logId": "b126e569-2a51-41f5-a0a7-53530c6ba817"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58080bb4-00c2-46bf-a685-b772bf3f8f44", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928890798000, "endTime": 152929158411700}, "additional": {"children": ["c202ee3d-ce2c-4712-b254-bfb8d84844e2", "9b72c212-c6c0-4b42-b7dd-f11ce111d7c1", "c25696c1-b36c-4e35-9106-d6374bc36cca", "5d0a6adb-5eb1-41fc-9089-784d501f443e", "526a0c49-e321-4388-927d-ac0473b0d3e5", "ea934abf-3683-49ff-86d1-ef7a66baa969", "7d7a7d92-0e40-498f-9100-89a431d40b60", "1ee92f35-d5b1-4251-afe0-5391e842abd7", "ca1fa291-6612-448e-9a39-9a4c2e5ae300"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23003238-80c2-455e-aae0-98b8b580a832", "logId": "b1e6a020-f490-41d4-8f5b-8c070266a633"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d4e0f13-cd0f-494f-8e77-49be24d9e85f", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929158437800, "endTime": 152929160005100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23003238-80c2-455e-aae0-98b8b580a832", "logId": "6a50c8e5-51a3-46a4-9a87-335fd0f92502"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2dddd83-66c4-4108-87f0-65f03e384a07", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929160010400, "endTime": 152929160035900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23003238-80c2-455e-aae0-98b8b580a832", "logId": "c2937e2a-d464-4126-a6e8-1472c9d1818e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5790e539-5348-4ec5-b235-6050d825f525", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928879310600, "endTime": 152928879363500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23003238-80c2-455e-aae0-98b8b580a832", "logId": "05f7542e-c125-499d-98fc-83fe8c003ebb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05f7542e-c125-499d-98fc-83fe8c003ebb", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928879310600, "endTime": 152928879363500}, "additional": {"logType": "info", "children": [], "durationId": "5790e539-5348-4ec5-b235-6050d825f525", "parent": "31522bb9-f587-4ce5-b12b-27b649490cc9"}}, {"head": {"id": "a1a10247-665e-468a-856e-164a265805b3", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928884888200, "endTime": 152928884905200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23003238-80c2-455e-aae0-98b8b580a832", "logId": "b3006c26-ea34-4d11-9a39-66731b16d6bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3006c26-ea34-4d11-9a39-66731b16d6bf", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928884888200, "endTime": 152928884905200}, "additional": {"logType": "info", "children": [], "durationId": "a1a10247-665e-468a-856e-164a265805b3", "parent": "31522bb9-f587-4ce5-b12b-27b649490cc9"}}, {"head": {"id": "798fd8d4-e1b7-4a98-9e17-daeb2edf7a40", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928885064500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54d4a851-00e5-4b91-b532-47524fd8b30c", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928890644500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b126e569-2a51-41f5-a0a7-53530c6ba817", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928874972700, "endTime": 152928890782400}, "additional": {"logType": "info", "children": [], "durationId": "2753ddbb-700c-4593-9e69-2e02fcce5e9f", "parent": "31522bb9-f587-4ce5-b12b-27b649490cc9"}}, {"head": {"id": "c202ee3d-ce2c-4712-b254-bfb8d84844e2", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928896208400, "endTime": 152928896219900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "58080bb4-00c2-46bf-a685-b772bf3f8f44", "logId": "723a75ee-1973-4c4d-a406-9d95f63f9f54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b72c212-c6c0-4b42-b7dd-f11ce111d7c1", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928896306600, "endTime": 152928900376100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "58080bb4-00c2-46bf-a685-b772bf3f8f44", "logId": "9b3610f1-89d9-4ad1-a47a-8bcbadcda5d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c25696c1-b36c-4e35-9106-d6374bc36cca", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928900387600, "endTime": 152929051747300}, "additional": {"children": ["28c8b702-c5a0-4d84-b428-3ff6e0a144fb", "ea2dbd55-a361-4a5c-9687-77a714483a22", "c31b60e7-df35-45d0-88bb-9ac237a3b694"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "58080bb4-00c2-46bf-a685-b772bf3f8f44", "logId": "b11e96c1-2b1e-4ae6-8cfc-f11c8bc24740"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d0a6adb-5eb1-41fc-9089-784d501f443e", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929051759900, "endTime": 152929077428600}, "additional": {"children": ["8f61cab6-2055-41db-9188-7167fc6d181d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "58080bb4-00c2-46bf-a685-b772bf3f8f44", "logId": "f57ceac4-7b4d-4f63-baf0-ba13ac59f191"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "526a0c49-e321-4388-927d-ac0473b0d3e5", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929077480500, "endTime": 152929121555600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "58080bb4-00c2-46bf-a685-b772bf3f8f44", "logId": "691af5ab-f90b-495d-84c2-fbf3d9bda994"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea934abf-3683-49ff-86d1-ef7a66baa969", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929122832100, "endTime": 152929139885900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "58080bb4-00c2-46bf-a685-b772bf3f8f44", "logId": "db1dbf30-0bd9-4732-90ba-a3b9e7cb3e7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d7a7d92-0e40-498f-9100-89a431d40b60", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929139909700, "endTime": 152929158210100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "58080bb4-00c2-46bf-a685-b772bf3f8f44", "logId": "f5949710-014d-45c6-ae97-4c87a0ba0485"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ee92f35-d5b1-4251-afe0-5391e842abd7", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929158236100, "endTime": 152929158397600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "58080bb4-00c2-46bf-a685-b772bf3f8f44", "logId": "e3aa55b6-3df4-41fe-b2b2-94584980059c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "723a75ee-1973-4c4d-a406-9d95f63f9f54", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928896208400, "endTime": 152928896219900}, "additional": {"logType": "info", "children": [], "durationId": "c202ee3d-ce2c-4712-b254-bfb8d84844e2", "parent": "b1e6a020-f490-41d4-8f5b-8c070266a633"}}, {"head": {"id": "9b3610f1-89d9-4ad1-a47a-8bcbadcda5d5", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928896306600, "endTime": 152928900376100}, "additional": {"logType": "info", "children": [], "durationId": "9b72c212-c6c0-4b42-b7dd-f11ce111d7c1", "parent": "b1e6a020-f490-41d4-8f5b-8c070266a633"}}, {"head": {"id": "28c8b702-c5a0-4d84-b428-3ff6e0a144fb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928901802900, "endTime": 152928901831700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c25696c1-b36c-4e35-9106-d6374bc36cca", "logId": "f05b93ba-6db2-4d83-bd9d-6e6582e3bd07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f05b93ba-6db2-4d83-bd9d-6e6582e3bd07", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928901802900, "endTime": 152928901831700}, "additional": {"logType": "info", "children": [], "durationId": "28c8b702-c5a0-4d84-b428-3ff6e0a144fb", "parent": "b11e96c1-2b1e-4ae6-8cfc-f11c8bc24740"}}, {"head": {"id": "ea2dbd55-a361-4a5c-9687-77a714483a22", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928903909900, "endTime": 152929051052000}, "additional": {"children": ["cdf836d0-32d1-47f9-b4b6-c8b96e04f2d2", "ceb0f797-12a4-4bc3-8196-67f93a477692"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c25696c1-b36c-4e35-9106-d6374bc36cca", "logId": "ea1b3005-9235-41c0-a1ed-87fe0545c96a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdf836d0-32d1-47f9-b4b6-c8b96e04f2d2", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928903911200, "endTime": 152928967129000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea2dbd55-a361-4a5c-9687-77a714483a22", "logId": "64634d41-a81f-4cd0-b0b5-d40d6bae0c7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ceb0f797-12a4-4bc3-8196-67f93a477692", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928967146600, "endTime": 152929051027300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea2dbd55-a361-4a5c-9687-77a714483a22", "logId": "2e8e19c0-97e0-4ed3-a8b7-a848848f59d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42321d4d-fd85-4998-9295-031d510944a8", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928903916100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b40aa36-90eb-4728-a48e-dcd1477559e2", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928966972600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64634d41-a81f-4cd0-b0b5-d40d6bae0c7b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928903911200, "endTime": 152928967129000}, "additional": {"logType": "info", "children": [], "durationId": "cdf836d0-32d1-47f9-b4b6-c8b96e04f2d2", "parent": "ea1b3005-9235-41c0-a1ed-87fe0545c96a"}}, {"head": {"id": "bb5b929b-0166-4c26-ad52-d800e44173a7", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928967187000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeffd36e-cefb-4164-8d43-46f659ca1e3b", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928987718800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caf4180a-de65-4794-a6d8-8c2474643c18", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928987952900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fffeeea-c1cf-41cc-b643-6685c0766a91", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928988089100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "950e043b-78ac-4c6c-b50a-904f0d5ae0eb", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928988202000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7114db73-c049-42e2-92c3-cd4fee28e87c", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928990726100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcb33fef-54fe-423c-9739-c8e1a2743d54", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929007047500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53046e74-231e-4f53-8ae2-0059e430fe71", "name": "Sdk init in 30 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929028737400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a128550-1c24-4ebe-8afd-751a08b51140", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929028956400}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 25, "second": 58}, "markType": "other"}}, {"head": {"id": "a1eae6f0-631b-43d6-8c7d-f8fa9062fa31", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929028982200}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 25, "second": 58}, "markType": "other"}}, {"head": {"id": "cf4b84f1-0bdd-42f9-a9c4-7a54579911df", "name": "Project task initialization takes 20 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929050718500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e9afb8c-8d51-4e98-abe0-df5c27c31d8d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929050838100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf56a7c1-b755-4dfb-93d8-8a5d0a0f1034", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929050886100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7aae9f4-c3de-4626-ba86-4077c09c6d40", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929050928300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e8e19c0-97e0-4ed3-a8b7-a848848f59d3", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928967146600, "endTime": 152929051027300}, "additional": {"logType": "info", "children": [], "durationId": "ceb0f797-12a4-4bc3-8196-67f93a477692", "parent": "ea1b3005-9235-41c0-a1ed-87fe0545c96a"}}, {"head": {"id": "ea1b3005-9235-41c0-a1ed-87fe0545c96a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928903909900, "endTime": 152929051052000}, "additional": {"logType": "info", "children": ["64634d41-a81f-4cd0-b0b5-d40d6bae0c7b", "2e8e19c0-97e0-4ed3-a8b7-a848848f59d3"], "durationId": "ea2dbd55-a361-4a5c-9687-77a714483a22", "parent": "b11e96c1-2b1e-4ae6-8cfc-f11c8bc24740"}}, {"head": {"id": "c31b60e7-df35-45d0-88bb-9ac237a3b694", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929051716800, "endTime": 152929051734300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c25696c1-b36c-4e35-9106-d6374bc36cca", "logId": "dd2ec59a-f421-4e6e-904b-bc5b46f8bdd7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd2ec59a-f421-4e6e-904b-bc5b46f8bdd7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929051716800, "endTime": 152929051734300}, "additional": {"logType": "info", "children": [], "durationId": "c31b60e7-df35-45d0-88bb-9ac237a3b694", "parent": "b11e96c1-2b1e-4ae6-8cfc-f11c8bc24740"}}, {"head": {"id": "b11e96c1-2b1e-4ae6-8cfc-f11c8bc24740", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928900387600, "endTime": 152929051747300}, "additional": {"logType": "info", "children": ["f05b93ba-6db2-4d83-bd9d-6e6582e3bd07", "ea1b3005-9235-41c0-a1ed-87fe0545c96a", "dd2ec59a-f421-4e6e-904b-bc5b46f8bdd7"], "durationId": "c25696c1-b36c-4e35-9106-d6374bc36cca", "parent": "b1e6a020-f490-41d4-8f5b-8c070266a633"}}, {"head": {"id": "8f61cab6-2055-41db-9188-7167fc6d181d", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929052258000, "endTime": 152929077418200}, "additional": {"children": ["cd2c790e-6806-4497-82cc-f438abd89fd1", "50dd83f5-2767-4a10-87df-d5e5c7ca108c", "2e2e1b8c-4806-4ad1-b391-b9dc330e7a99"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5d0a6adb-5eb1-41fc-9089-784d501f443e", "logId": "2840819e-9a50-45b6-a24a-d7fa7c7cb8a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd2c790e-6806-4497-82cc-f438abd89fd1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929054663600, "endTime": 152929054680800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f61cab6-2055-41db-9188-7167fc6d181d", "logId": "18256932-70a0-423f-a64a-b3a078f18869"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18256932-70a0-423f-a64a-b3a078f18869", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929054663600, "endTime": 152929054680800}, "additional": {"logType": "info", "children": [], "durationId": "cd2c790e-6806-4497-82cc-f438abd89fd1", "parent": "2840819e-9a50-45b6-a24a-d7fa7c7cb8a5"}}, {"head": {"id": "50dd83f5-2767-4a10-87df-d5e5c7ca108c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929056105900, "endTime": 152929076200100}, "additional": {"children": ["7897b3d6-f0bd-4003-945e-ed3ec6866a8b", "52855558-e494-42c2-b4e3-9bbc625a5f84"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f61cab6-2055-41db-9188-7167fc6d181d", "logId": "c5a46703-a7c8-495d-b9f2-eb1bbce3cf23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7897b3d6-f0bd-4003-945e-ed3ec6866a8b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929056106600, "endTime": 152929059221600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "50dd83f5-2767-4a10-87df-d5e5c7ca108c", "logId": "31f6ad2f-f664-45f5-b325-9f13bc8e866a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52855558-e494-42c2-b4e3-9bbc625a5f84", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929059235800, "endTime": 152929076189300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "50dd83f5-2767-4a10-87df-d5e5c7ca108c", "logId": "d68a80bb-2a8c-4551-b62a-0a00bd0d27b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f5aa8aa-010f-4c95-bce1-d7eae00da44c", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929056111700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b302ca1c-a6e3-43c4-a757-8d60949914ba", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929059099600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31f6ad2f-f664-45f5-b325-9f13bc8e866a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929056106600, "endTime": 152929059221600}, "additional": {"logType": "info", "children": [], "durationId": "7897b3d6-f0bd-4003-945e-ed3ec6866a8b", "parent": "c5a46703-a7c8-495d-b9f2-eb1bbce3cf23"}}, {"head": {"id": "35cc377a-454f-420f-b132-1a006f6a7db8", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929059246500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d59e61be-6f6d-472a-b1fa-49e1fa950daa", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929069536100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51601a2f-25e3-466c-9a07-882ae10a267c", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929069662300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b59c6600-089a-4234-bc74-8e2fdfde82d0", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929069878400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "710ec4d5-5e52-4ffe-b765-580e3aa2e2df", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929069988100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45187f12-de81-41b0-966e-b3cacb58cbb3", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929070031000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecbcde5b-8443-416e-ab2e-40fb23fb73c1", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929070065400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbcd4314-0454-4dd4-8227-3bcbe8313c53", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929070115300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6397279e-594f-485f-aaf1-4af88ef2d16a", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929070152900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebf04462-19c2-499e-b26b-b0ad19018e4d", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929070298700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49511096-5289-44ec-bc05-c1c1158de33a", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929070384600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dce3b94-49c0-4876-83c4-5eb694a2d59e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929070428600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e86059d5-0ae9-416e-b9a2-32a22228cd37", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929070459400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "174ecb46-2cd0-4c07-991d-d226ca1f9ea2", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929070509000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0b34db2-53fb-4f38-be3e-d8c87e96f28f", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929070543400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddeeb5c2-3e93-4468-a67f-ff859c51048a", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929070618000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1d7dd1d-dd29-4ff0-ab79-513d74d40096", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929070683500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe14a3d1-69ad-4b30-ad8e-63e97628f87d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929070719500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5cb29d1-47bc-4fcf-bacd-d526f4e94e17", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929070750900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6300762d-b5b8-4616-b8d0-b31c5f028f70", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929071274800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d63d1c3b-1f27-4aeb-b72c-5fc7cd609bed", "name": "Module entry task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929075896100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "451765ef-6b20-4c96-9fe8-cdf53c049347", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929076069800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bac516e4-71a9-42f6-898f-e56540208041", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929076121000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64b13adc-d399-473e-b151-6148df210745", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929076151700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d68a80bb-2a8c-4551-b62a-0a00bd0d27b7", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929059235800, "endTime": 152929076189300}, "additional": {"logType": "info", "children": [], "durationId": "52855558-e494-42c2-b4e3-9bbc625a5f84", "parent": "c5a46703-a7c8-495d-b9f2-eb1bbce3cf23"}}, {"head": {"id": "c5a46703-a7c8-495d-b9f2-eb1bbce3cf23", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929056105900, "endTime": 152929076200100}, "additional": {"logType": "info", "children": ["31f6ad2f-f664-45f5-b325-9f13bc8e866a", "d68a80bb-2a8c-4551-b62a-0a00bd0d27b7"], "durationId": "50dd83f5-2767-4a10-87df-d5e5c7ca108c", "parent": "2840819e-9a50-45b6-a24a-d7fa7c7cb8a5"}}, {"head": {"id": "2e2e1b8c-4806-4ad1-b391-b9dc330e7a99", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929077391100, "endTime": 152929077404500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f61cab6-2055-41db-9188-7167fc6d181d", "logId": "ceafc69e-37f7-455c-a42f-1750fef9c830"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ceafc69e-37f7-455c-a42f-1750fef9c830", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929077391100, "endTime": 152929077404500}, "additional": {"logType": "info", "children": [], "durationId": "2e2e1b8c-4806-4ad1-b391-b9dc330e7a99", "parent": "2840819e-9a50-45b6-a24a-d7fa7c7cb8a5"}}, {"head": {"id": "2840819e-9a50-45b6-a24a-d7fa7c7cb8a5", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929052258000, "endTime": 152929077418200}, "additional": {"logType": "info", "children": ["18256932-70a0-423f-a64a-b3a078f18869", "c5a46703-a7c8-495d-b9f2-eb1bbce3cf23", "ceafc69e-37f7-455c-a42f-1750fef9c830"], "durationId": "8f61cab6-2055-41db-9188-7167fc6d181d", "parent": "f57ceac4-7b4d-4f63-baf0-ba13ac59f191"}}, {"head": {"id": "f57ceac4-7b4d-4f63-baf0-ba13ac59f191", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929051759900, "endTime": 152929077428600}, "additional": {"logType": "info", "children": ["2840819e-9a50-45b6-a24a-d7fa7c7cb8a5"], "durationId": "5d0a6adb-5eb1-41fc-9089-784d501f443e", "parent": "b1e6a020-f490-41d4-8f5b-8c070266a633"}}, {"head": {"id": "57b82b47-8de6-4490-9643-2db59f87beea", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929089501400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2cb699e-8aa5-4026-aab5-3e53ee04a840", "name": "hvigorfile, resolve hvigorfile dependencies in 44 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929121405500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "691af5ab-f90b-495d-84c2-fbf3d9bda994", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929077480500, "endTime": 152929121555600}, "additional": {"logType": "info", "children": [], "durationId": "526a0c49-e321-4388-927d-ac0473b0d3e5", "parent": "b1e6a020-f490-41d4-8f5b-8c070266a633"}}, {"head": {"id": "ca1fa291-6612-448e-9a39-9a4c2e5ae300", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929122385000, "endTime": 152929122817700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "58080bb4-00c2-46bf-a685-b772bf3f8f44", "logId": "9c78a877-91ea-49c7-bef5-205d943bf93b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "485971d5-6bc7-4ed4-8ce0-676076bc1f2c", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929122518000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c78a877-91ea-49c7-bef5-205d943bf93b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929122385000, "endTime": 152929122817700}, "additional": {"logType": "info", "children": [], "durationId": "ca1fa291-6612-448e-9a39-9a4c2e5ae300", "parent": "b1e6a020-f490-41d4-8f5b-8c070266a633"}}, {"head": {"id": "dadaabc5-c40c-4a00-af23-20ede6daae2e", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929124816000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2212a39d-3332-4281-80d1-c1ccf1e948f0", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929138664400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db1dbf30-0bd9-4732-90ba-a3b9e7cb3e7a", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929122832100, "endTime": 152929139885900}, "additional": {"logType": "info", "children": [], "durationId": "ea934abf-3683-49ff-86d1-ef7a66baa969", "parent": "b1e6a020-f490-41d4-8f5b-8c070266a633"}}, {"head": {"id": "5cd3d9d7-af86-45d2-8ce8-88d359bc8af9", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929140019800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0f23640-056d-4c5a-ab89-6e2fd953e39a", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929147175300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bde2cdf-b1b2-4ae3-83e2-958fbcb48cdc", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929147309200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21415a41-3cb2-4647-a50d-7401bf9f40a9", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929147992800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46b66307-bd18-461b-8086-36315e5285d5", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929151277300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "511d58b4-26d0-462b-94b7-f08c5ef962bd", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929151444200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5949710-014d-45c6-ae97-4c87a0ba0485", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929139909700, "endTime": 152929158210100}, "additional": {"logType": "info", "children": [], "durationId": "7d7a7d92-0e40-498f-9100-89a431d40b60", "parent": "b1e6a020-f490-41d4-8f5b-8c070266a633"}}, {"head": {"id": "08ce0d09-5978-45f5-b069-91e44459d127", "name": "Configuration phase cost:263 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929158269500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3aa55b6-3df4-41fe-b2b2-94584980059c", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929158236100, "endTime": 152929158397600}, "additional": {"logType": "info", "children": [], "durationId": "1ee92f35-d5b1-4251-afe0-5391e842abd7", "parent": "b1e6a020-f490-41d4-8f5b-8c070266a633"}}, {"head": {"id": "b1e6a020-f490-41d4-8f5b-8c070266a633", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928890798000, "endTime": 152929158411700}, "additional": {"logType": "info", "children": ["723a75ee-1973-4c4d-a406-9d95f63f9f54", "9b3610f1-89d9-4ad1-a47a-8bcbadcda5d5", "b11e96c1-2b1e-4ae6-8cfc-f11c8bc24740", "f57ceac4-7b4d-4f63-baf0-ba13ac59f191", "691af5ab-f90b-495d-84c2-fbf3d9bda994", "db1dbf30-0bd9-4732-90ba-a3b9e7cb3e7a", "f5949710-014d-45c6-ae97-4c87a0ba0485", "e3aa55b6-3df4-41fe-b2b2-94584980059c", "9c78a877-91ea-49c7-bef5-205d943bf93b"], "durationId": "58080bb4-00c2-46bf-a685-b772bf3f8f44", "parent": "31522bb9-f587-4ce5-b12b-27b649490cc9"}}, {"head": {"id": "b7eaea6b-3ae5-4b7b-bdc1-f8bdcc9f3467", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929159966800, "endTime": 152929159989000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23003238-80c2-455e-aae0-98b8b580a832", "logId": "48885095-21e2-47ed-b504-029b86c5d2a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48885095-21e2-47ed-b504-029b86c5d2a5", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929159966800, "endTime": 152929159989000}, "additional": {"logType": "info", "children": [], "durationId": "b7eaea6b-3ae5-4b7b-bdc1-f8bdcc9f3467", "parent": "31522bb9-f587-4ce5-b12b-27b649490cc9"}}, {"head": {"id": "6a50c8e5-51a3-46a4-9a87-335fd0f92502", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929158437800, "endTime": 152929160005100}, "additional": {"logType": "info", "children": [], "durationId": "6d4e0f13-cd0f-494f-8e77-49be24d9e85f", "parent": "31522bb9-f587-4ce5-b12b-27b649490cc9"}}, {"head": {"id": "c2937e2a-d464-4126-a6e8-1472c9d1818e", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929160010400, "endTime": 152929160035900}, "additional": {"logType": "info", "children": [], "durationId": "c2dddd83-66c4-4108-87f0-65f03e384a07", "parent": "31522bb9-f587-4ce5-b12b-27b649490cc9"}}, {"head": {"id": "31522bb9-f587-4ce5-b12b-27b649490cc9", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928874971300, "endTime": 152929160039500}, "additional": {"logType": "info", "children": ["b126e569-2a51-41f5-a0a7-53530c6ba817", "b1e6a020-f490-41d4-8f5b-8c070266a633", "6a50c8e5-51a3-46a4-9a87-335fd0f92502", "c2937e2a-d464-4126-a6e8-1472c9d1818e", "05f7542e-c125-499d-98fc-83fe8c003ebb", "b3006c26-ea34-4d11-9a39-66731b16d6bf", "48885095-21e2-47ed-b504-029b86c5d2a5"], "durationId": "23003238-80c2-455e-aae0-98b8b580a832"}}, {"head": {"id": "9e63007d-b855-4cc5-b8bb-0009cdec6622", "name": "Configuration task cost before running: 289 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929160655900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bb908f0-ead9-4a0b-94dc-d59776b4eb97", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929174454400, "endTime": 152929187327100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "4836faf1-cd85-465e-8064-a16ebf4fb409", "logId": "37d35f9b-9366-4c89-a1b0-67c2f81d7412"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4836faf1-cd85-465e-8064-a16ebf4fb409", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929162851500}, "additional": {"logType": "detail", "children": [], "durationId": "1bb908f0-ead9-4a0b-94dc-d59776b4eb97"}}, {"head": {"id": "64ab8eb4-71ec-4a6e-bdbc-347ec8d8a064", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929163606100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "406ccd0b-2bbe-4cf5-96b2-269927188e8d", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929163838000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7132218f-73ea-497f-a555-31ea3598c729", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929164967100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4a2b0eb-20e9-4372-b9ce-691ce3dddf0b", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929165953500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd3f33e3-57c4-4e14-85e1-17ff1688ba53", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929167297700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2bffc57-4372-46b5-b031-d212a0ab06bd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929167408200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81b11e19-f98e-4986-ae5f-81b061104cad", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929174480700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12db23e2-db35-4537-a1cb-8f8974aa9f8e", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929187097600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3d4e1f-3ba0-4301-9916-f5ef9856b2a4", "name": "entry : default@PreBuild cost memory 0.318115234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929187262200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37d35f9b-9366-4c89-a1b0-67c2f81d7412", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929174454400, "endTime": 152929187327100}, "additional": {"logType": "info", "children": [], "durationId": "1bb908f0-ead9-4a0b-94dc-d59776b4eb97"}}, {"head": {"id": "50e9524a-87a7-49b2-8b51-3487516fb73d", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929195231300, "endTime": 152929197527400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3cfba021-0166-4e27-af3a-2c4469687467", "logId": "b35ed64e-36c0-4543-b69c-3823a31f98b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cfba021-0166-4e27-af3a-2c4469687467", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929192694100}, "additional": {"logType": "detail", "children": [], "durationId": "50e9524a-87a7-49b2-8b51-3487516fb73d"}}, {"head": {"id": "4f7a00ef-9df6-427b-8e17-420010506e3e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929194323200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73d12b1b-cbe7-4449-96ce-510479426653", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929194458300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bf0c095-bc6b-415f-8e6d-7f90fee63528", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929195244100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fa39c93-9eff-4101-b6c0-d4bbf26fbcb8", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929196035700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f9d5465-37d6-46ae-b4e2-3f90d582404d", "name": "entry : default@CreateModuleInfo cost memory 0.06037139892578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929197266900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12fc1e3a-9de9-4b4a-a071-e46938065af6", "name": "runTaskFromQueue task cost before running: 326 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929197430500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b35ed64e-36c0-4543-b69c-3823a31f98b9", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929195231300, "endTime": 152929197527400, "totalTime": 2162300}, "additional": {"logType": "info", "children": [], "durationId": "50e9524a-87a7-49b2-8b51-3487516fb73d"}}, {"head": {"id": "636ebad7-468f-4285-b6f0-b3a0bb6ff360", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929209599100, "endTime": 152929212525400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f218c9ce-4931-4cb2-813c-844c1423ad7e", "logId": "a459ad90-c482-4d2f-8e5b-62f86db71f01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f218c9ce-4931-4cb2-813c-844c1423ad7e", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929200808000}, "additional": {"logType": "detail", "children": [], "durationId": "636ebad7-468f-4285-b6f0-b3a0bb6ff360"}}, {"head": {"id": "5ec0bc9e-beb3-43ba-83ac-7559def3bad2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929203378800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca6cbe1d-04e1-464b-93f3-f3d5bd99c4e3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929203524700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed87e284-f07f-48f6-97bb-9289a4a9addb", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929209617300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "306364c1-53ab-452b-88d4-a489df5c85a8", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929211181500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a200fc-c760-40bc-b6b1-5f021844cf57", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929212326500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7eabaf8-baa9-4393-a3ca-eff64191a2c9", "name": "entry : default@GenerateMetadata cost memory 0.10242462158203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929212456700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a459ad90-c482-4d2f-8e5b-62f86db71f01", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929209599100, "endTime": 152929212525400}, "additional": {"logType": "info", "children": [], "durationId": "636ebad7-468f-4285-b6f0-b3a0bb6ff360"}}, {"head": {"id": "2283447e-4e8e-4b87-827f-590dc7732a25", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929218224000, "endTime": 152929218569400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "9d79b37c-4371-4248-b880-af2f0f56ed87", "logId": "31c283f8-a932-4951-be1d-e5eb1277d90d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d79b37c-4371-4248-b880-af2f0f56ed87", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929216655700}, "additional": {"logType": "detail", "children": [], "durationId": "2283447e-4e8e-4b87-827f-590dc7732a25"}}, {"head": {"id": "13051efe-5f9a-4e20-9969-21be2e1eabe7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929217843300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62135745-ace9-4a35-b040-9a0593008a37", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929217967300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "629f84e1-d65c-4fff-8230-e2887e4ed63c", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929218235100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2fa852c-4fb1-4fe7-8792-99b670a29df4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929218341100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "467fcb42-b2c2-4750-9a22-c93bb0f22028", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929218382300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c31e2503-91ca-4b8d-a446-dc86a4218684", "name": "entry : default@ConfigureCmake cost memory 0.0374755859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929218463000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12522a36-67a9-45ad-bb9e-3bb1fdb890b1", "name": "runTaskFromQueue task cost before running: 347 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929218532900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31c283f8-a932-4951-be1d-e5eb1277d90d", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929218224000, "endTime": 152929218569400, "totalTime": 290000}, "additional": {"logType": "info", "children": [], "durationId": "2283447e-4e8e-4b87-827f-590dc7732a25"}}, {"head": {"id": "d394a2f0-e319-423f-82a7-9cbc9f33b47b", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929222292800, "endTime": 152929225207800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "c0136d68-7759-4c68-b543-651719ae0c05", "logId": "e18c76dc-075c-4def-b817-55b933a6a55b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0136d68-7759-4c68-b543-651719ae0c05", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929220315500}, "additional": {"logType": "detail", "children": [], "durationId": "d394a2f0-e319-423f-82a7-9cbc9f33b47b"}}, {"head": {"id": "e57c05ca-3f27-4410-be87-7db3ef61154b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929221353900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d12b75e6-4e8c-4b5e-a36f-ffcc1d000ae5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929221458100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fc37032-a350-42b0-8f5d-2c6f6ff34c01", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929222306200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2b61701-069a-42a0-8208-c0abc59bfef7", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929224977200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9887ff2-0c98-440b-bd71-2ec109d8add6", "name": "entry : default@MergeProfile cost memory 0.12766265869140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929225138300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e18c76dc-075c-4def-b817-55b933a6a55b", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929222292800, "endTime": 152929225207800}, "additional": {"logType": "info", "children": [], "durationId": "d394a2f0-e319-423f-82a7-9cbc9f33b47b"}}, {"head": {"id": "37a76239-6baf-4a89-9391-2dcbb962b51b", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929229198200, "endTime": 152929232118100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b7186545-f4d1-4aff-939e-35cd6d766188", "logId": "84022c5c-0542-4572-a544-3de754382d4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7186545-f4d1-4aff-939e-35cd6d766188", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929227085300}, "additional": {"logType": "detail", "children": [], "durationId": "37a76239-6baf-4a89-9391-2dcbb962b51b"}}, {"head": {"id": "8abb88c5-2e79-4dd7-af05-d04e7dd649a5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929228179100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd8e148f-edb6-4f15-beb4-5bf6b81db691", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929228293900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cc02221-e543-4655-a071-8982ebaa6c40", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929229209900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d8b72a2-ae7d-4718-afd7-45af10b7614f", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929230353000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "976d269c-b0c8-459a-a6b3-50bf920e7db9", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929231920500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca051194-bebb-4c6a-bf7c-9186d364713c", "name": "entry : default@CreateBuildProfile cost memory 0.1083526611328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929232044400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84022c5c-0542-4572-a544-3de754382d4f", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929229198200, "endTime": 152929232118100}, "additional": {"logType": "info", "children": [], "durationId": "37a76239-6baf-4a89-9391-2dcbb962b51b"}}, {"head": {"id": "2febb40d-e8ac-4e2a-b798-69086ef86b89", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929235937500, "endTime": 152929236804300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "71bc76b5-7b80-46e5-9bb0-0ea9db9cc5c1", "logId": "f8d2930e-0626-4903-9933-d3f61d0c50e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71bc76b5-7b80-46e5-9bb0-0ea9db9cc5c1", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929233791800}, "additional": {"logType": "detail", "children": [], "durationId": "2febb40d-e8ac-4e2a-b798-69086ef86b89"}}, {"head": {"id": "fa099798-8a18-42df-8711-00c5fdf70896", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929234925200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fea91e36-8a36-41e9-9969-d7196b46ed4e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929235051300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed0c8669-1f82-442c-85f5-52ed28cb897a", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929235953000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7281f00f-c8a4-4cb8-b2aa-e01b92dc1ca1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929236140200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5959fe79-3004-4961-a2f2-dc0eaccc4dad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929236214300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b68043e-8014-4fd1-a635-50bc8fb5323f", "name": "entry : default@PreCheckSyscap cost memory 0.041107177734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929236638400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b3e7b8d-93ae-430f-8092-a47ebce600b8", "name": "runTaskFromQueue task cost before running: 365 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929236756900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8d2930e-0626-4903-9933-d3f61d0c50e0", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929235937500, "endTime": 152929236804300, "totalTime": 794700}, "additional": {"logType": "info", "children": [], "durationId": "2febb40d-e8ac-4e2a-b798-69086ef86b89"}}, {"head": {"id": "51f945fc-6cb6-464c-836c-98ae1deeeb59", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929241884700, "endTime": 152929250114700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2b0d71f5-5d80-4220-b488-d33386005047", "logId": "cfc7b412-9c64-4def-8ded-753325e88ccf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b0d71f5-5d80-4220-b488-d33386005047", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929238873100}, "additional": {"logType": "detail", "children": [], "durationId": "51f945fc-6cb6-464c-836c-98ae1deeeb59"}}, {"head": {"id": "f981aeec-a6b9-4ead-9557-82a1b6ca5628", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929240230900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e4136d5-e31c-4cd9-a112-422ff25da113", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929240363600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b9b2f22-e6bd-4b21-9e60-0574439b95fa", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929241899000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e341fcc-34b1-4e28-9412-e8098372312f", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929248983600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d45a93b-fade-4b92-905a-910b3690e9b8", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929249794600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e05a152b-5d2b-4db3-b97d-757ed923409f", "name": "entry : default@GeneratePkgContextInfo cost memory 0.41455078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929250041800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfc7b412-9c64-4def-8ded-753325e88ccf", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929241884700, "endTime": 152929250114700}, "additional": {"logType": "info", "children": [], "durationId": "51f945fc-6cb6-464c-836c-98ae1deeeb59"}}, {"head": {"id": "8e1f8009-fff1-4a9e-ad28-d478b78273e9", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929262390000, "endTime": 152929265776100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "f8dfcffe-8d80-496e-9330-3fe91229ae31", "logId": "dde5e93a-e37f-4559-9a5c-96cc923cf435"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8dfcffe-8d80-496e-9330-3fe91229ae31", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929252638300}, "additional": {"logType": "detail", "children": [], "durationId": "8e1f8009-fff1-4a9e-ad28-d478b78273e9"}}, {"head": {"id": "3d3e055f-6ed4-41c8-8582-d6a6bfdbf490", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929255414100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f970140c-aaaa-437f-8c9a-92f1f6d8b83b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929255909800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c6960a9-5c41-4315-9838-5928f8afeaa1", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929262409900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3af48c92-11db-4f78-8eab-61c271968117", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929265084100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22a021a0-9304-4dbd-8bba-a732a9f785ff", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929265271000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9f77e98-d64a-4203-985d-a070fb17c742", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929265409200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b90d8b29-05b0-4cec-8fb2-8d9e14141963", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929265553800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a317e263-fed9-4b6d-b43d-7beb6484d679", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12042236328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929265643100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88fc2524-645f-47f0-a7f7-1de6e370e6ef", "name": "runTaskFromQueue task cost before running: 394 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929265724100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dde5e93a-e37f-4559-9a5c-96cc923cf435", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929262390000, "endTime": 152929265776100, "totalTime": 3317000}, "additional": {"logType": "info", "children": [], "durationId": "8e1f8009-fff1-4a9e-ad28-d478b78273e9"}}, {"head": {"id": "b0331726-6d85-4bc5-9f66-afa575869759", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929282637400, "endTime": 152929283167100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "1c8634e3-94f7-40e4-9509-fd7c0dbbf798", "logId": "64e6367d-d043-4111-8dca-3d06c3390955"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c8634e3-94f7-40e4-9509-fd7c0dbbf798", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929270426800}, "additional": {"logType": "detail", "children": [], "durationId": "b0331726-6d85-4bc5-9f66-afa575869759"}}, {"head": {"id": "1c35e805-ff9c-452f-9b16-c0532c84be2f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929272854800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf577bec-23e5-46b6-8f72-12435509843d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929273087700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec801b50-a6bb-4e8d-8a26-3726f753c828", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929282653100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "546c91d9-d68b-48a5-bd7c-ee8a3e80ff8e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929282844600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b26c0d3-183a-457b-9efe-011db0eeafc4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929282901700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18995fff-6182-4577-8fbb-4f435f52bb0f", "name": "entry : default@BuildNativeWithCmake cost memory 0.03861236572265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929283018300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5e651a6-435b-4c0e-8b04-e69a22749f96", "name": "runTaskFromQueue task cost before running: 412 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929283122100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64e6367d-d043-4111-8dca-3d06c3390955", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929282637400, "endTime": 152929283167100, "totalTime": 463400}, "additional": {"logType": "info", "children": [], "durationId": "b0331726-6d85-4bc5-9f66-afa575869759"}}, {"head": {"id": "388880f5-9b21-4136-bb16-3ac505f0762b", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929288299300, "endTime": 152929293975000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ca7700ec-18ac-4770-9d29-bf59af00005c", "logId": "666e4b79-efe3-47e9-85b8-1e6661cb038e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca7700ec-18ac-4770-9d29-bf59af00005c", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929285291400}, "additional": {"logType": "detail", "children": [], "durationId": "388880f5-9b21-4136-bb16-3ac505f0762b"}}, {"head": {"id": "33f70697-aa11-483d-b093-0e1d5b5d124a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929286579900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81753cfb-4bd6-4a36-a768-d55f64496228", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929286715700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b577a37-5257-40eb-bbe0-cda7275257fe", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929288318500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d4041bc-756b-48b7-b3c9-4eab4ce05bc1", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929293723100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfb804be-0a5d-4cc6-8e5e-fdf22a5b85c0", "name": "entry : default@MakePackInfo cost memory 0.1647186279296875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929293898900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "666e4b79-efe3-47e9-85b8-1e6661cb038e", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929288299300, "endTime": 152929293975000}, "additional": {"logType": "info", "children": [], "durationId": "388880f5-9b21-4136-bb16-3ac505f0762b"}}, {"head": {"id": "d5919807-07a6-44b1-90fd-4b6f934558a1", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929300746900, "endTime": 152929306352400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9d747aa6-9cae-4f71-b063-4855da236fab", "logId": "6eda7ec0-2f99-42a0-b0b2-46daec62b2b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d747aa6-9cae-4f71-b063-4855da236fab", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929297103600}, "additional": {"logType": "detail", "children": [], "durationId": "d5919807-07a6-44b1-90fd-4b6f934558a1"}}, {"head": {"id": "dc201662-a701-407d-ac39-687cd725190d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929298718300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c74f9f79-743f-40b5-b1d4-066b3e2e4c31", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929298862400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f96055fa-bde6-4de5-b6ba-fd7829a7dfa3", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929300762200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98f9f6df-5c9a-45b9-9485-781d2a17af9c", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929301043200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebedbc03-bc83-4e58-ae86-1a83ff44702c", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929302213800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca4e86f2-d879-48a8-bdef-23f7add69478", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929306104600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "876e5a47-cf32-4adf-9ac6-9a301071eb5c", "name": "entry : default@SyscapTransform cost memory 0.1515350341796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929306277000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eda7ec0-2f99-42a0-b0b2-46daec62b2b4", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929300746900, "endTime": 152929306352400}, "additional": {"logType": "info", "children": [], "durationId": "d5919807-07a6-44b1-90fd-4b6f934558a1"}}, {"head": {"id": "0a0684ee-008f-40fb-afca-55b9491d2d49", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929311553200, "endTime": 152929314273200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "922d4319-26ee-4c97-8a0c-1c7cf2540252", "logId": "876073dd-7b46-4655-a279-fe9745562d82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "922d4319-26ee-4c97-8a0c-1c7cf2540252", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929308213100}, "additional": {"logType": "detail", "children": [], "durationId": "0a0684ee-008f-40fb-afca-55b9491d2d49"}}, {"head": {"id": "34dbf9d0-9ef6-4a98-af6c-08b2693bd3b1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929309467300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8349e36c-d2de-4c1c-aac4-0bce95ce72cf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929309606700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cec879c9-e092-4906-afd7-7eab4af3be08", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929311569100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a37cb940-e57d-457d-a5ac-71a4f7c268a0", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929314076000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ef88bda-b7ca-4709-8e3a-2a0817141ea6", "name": "entry : default@ProcessProfile cost memory 0.12511444091796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929314209200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "876073dd-7b46-4655-a279-fe9745562d82", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929311553200, "endTime": 152929314273200}, "additional": {"logType": "info", "children": [], "durationId": "0a0684ee-008f-40fb-afca-55b9491d2d49"}}, {"head": {"id": "fd8135a9-cac5-4f6d-b6ce-902561969d77", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929320028200, "endTime": 152929328415400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a8748d38-fb46-4d84-82b4-df757ad9a801", "logId": "92af696b-0cde-4d32-978a-2785519944c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8748d38-fb46-4d84-82b4-df757ad9a801", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929316274000}, "additional": {"logType": "detail", "children": [], "durationId": "fd8135a9-cac5-4f6d-b6ce-902561969d77"}}, {"head": {"id": "e1ef9427-ac1f-471e-85e6-c6752ac8b345", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929317435800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "971efc0a-4382-4595-91db-bd344f26bc0d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929317632300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13713bdb-0242-47f6-8531-966129c80d71", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929320042700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ab6ab79-7016-47a8-9e91-a850c38779f1", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929328182900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8ec9c60-a2ae-409b-8313-509c87558594", "name": "entry : default@ProcessRouterMap cost memory 0.23334503173828125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929328344000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92af696b-0cde-4d32-978a-2785519944c3", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929320028200, "endTime": 152929328415400}, "additional": {"logType": "info", "children": [], "durationId": "fd8135a9-cac5-4f6d-b6ce-902561969d77"}}, {"head": {"id": "51ce3d32-9e61-4c04-a4d7-8f39b5cc0cf2", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929333354700, "endTime": 152929339735300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "8921470b-957e-4bf5-8a07-e8f244726598", "logId": "752a8d4b-c5d1-45f7-82e2-18580ca4b139"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8921470b-957e-4bf5-8a07-e8f244726598", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929331753000}, "additional": {"logType": "detail", "children": [], "durationId": "51ce3d32-9e61-4c04-a4d7-8f39b5cc0cf2"}}, {"head": {"id": "13620582-4850-491c-b749-5b7c7d8a3b31", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929333119700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7575c6e5-6334-4c71-9e9a-1028d15bf8c8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929333249300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7151cf24-9ad6-49cd-819d-ffe149c550c3", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929333362900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1f4e3c2-c765-42d5-bec2-c3c83a40b7f7", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929333510500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd49264-f4e5-4c3a-8d5e-4ade6a3439a1", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929337804200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cda9722-ac88-43dd-abdb-b4b879238b47", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929337990400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d497b01a-44d8-4e12-9ccc-0cc67aa8c3f0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929338104100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e227ecf-3313-4c2f-9299-b6c88875dc56", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929338147100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30dcd2e4-70e4-4a2b-9fcc-ea35e652cc2d", "name": "entry : default@ProcessStartupConfig cost memory 0.35218048095703125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929339515100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00c46e5e-8af2-4c35-9fd8-1cb97952e17f", "name": "runTaskFromQueue task cost before running: 468 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929339674500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "752a8d4b-c5d1-45f7-82e2-18580ca4b139", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929333354700, "endTime": 152929339735300, "totalTime": 6288600}, "additional": {"logType": "info", "children": [], "durationId": "51ce3d32-9e61-4c04-a4d7-8f39b5cc0cf2"}}, {"head": {"id": "fac1a011-e43c-4bf0-974d-a8775cccbef9", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929344942400, "endTime": 152929346428300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d2f2f00f-d462-43ac-acb7-1298c37317d6", "logId": "d6a59d14-c76f-47c3-9573-5525510272e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2f2f00f-d462-43ac-acb7-1298c37317d6", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929342854400}, "additional": {"logType": "detail", "children": [], "durationId": "fac1a011-e43c-4bf0-974d-a8775cccbef9"}}, {"head": {"id": "af7be971-479e-49ea-8917-0577a15e1f5e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929343944800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "872f2667-40a0-4155-affd-fc423fb2dc20", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929344059500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2acb1082-ad1d-4b07-ae7f-71cee6997a96", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929344953100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b8fff7c-2fb0-4637-8c18-ac0366a8a776", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929345084500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7d5b723-0df1-4532-8fd0-1d4f1c99afb9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929345144100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "859621e1-0d62-46c9-9f84-5febf08157a4", "name": "entry : default@BuildNativeWithNinja cost memory 0.0582122802734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929346232200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aed3f509-c457-4ff3-98c9-0c3acdbd58f3", "name": "runTaskFromQueue task cost before running: 475 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929346372200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6a59d14-c76f-47c3-9573-5525510272e5", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929344942400, "endTime": 152929346428300, "totalTime": 1404700}, "additional": {"logType": "info", "children": [], "durationId": "fac1a011-e43c-4bf0-974d-a8775cccbef9"}}, {"head": {"id": "431265bb-d538-4769-bae2-cf577f80d0e5", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929353461100, "endTime": 152929360774600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "e6fa5cd8-d8b9-49f8-8564-1e2cdac3fef2", "logId": "a0163286-670d-4cdf-a7d6-1cdd84d20c11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6fa5cd8-d8b9-49f8-8564-1e2cdac3fef2", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929349090400}, "additional": {"logType": "detail", "children": [], "durationId": "431265bb-d538-4769-bae2-cf577f80d0e5"}}, {"head": {"id": "4ba6e318-e479-4848-be1f-982df5d0eb41", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929350186100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f882b808-f91f-419e-af65-614ed0a2b764", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929350299400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a05171ce-625f-460f-8c51-cf9843ae62ee", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929351846400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b36caba8-8789-4133-9c7b-b8f089dd52dc", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929355619200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7bd5ab1-06e3-4b48-98fb-f0943ea670e4", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929358693600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c02d2bc6-0b63-4d36-85f9-5627d888e28e", "name": "entry : default@ProcessResource cost memory 0.16214752197265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929358912800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0163286-670d-4cdf-a7d6-1cdd84d20c11", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929353461100, "endTime": 152929360774600}, "additional": {"logType": "info", "children": [], "durationId": "431265bb-d538-4769-bae2-cf577f80d0e5"}}, {"head": {"id": "34e6612b-ff00-4d06-aca3-b62da344f9d2", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929371566800, "endTime": 152929398183700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a0ef5bc7-fbb0-46a0-870d-e8507c786e30", "logId": "ef75c673-f171-4923-a036-7b84cf2ae495"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0ef5bc7-fbb0-46a0-870d-e8507c786e30", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929365086500}, "additional": {"logType": "detail", "children": [], "durationId": "34e6612b-ff00-4d06-aca3-b62da344f9d2"}}, {"head": {"id": "3491ac12-c449-48ae-ab60-05e59832ef79", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929366426100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eea3c20-ac3c-4289-806e-9e61bf657d05", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929366561300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7636dff7-2509-4484-a714-1272297ed534", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929371588200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97ef06bf-9492-46cb-a02a-6d0ce91f3fe1", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929397961900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d22687d-74f0-4360-b870-da73402ef238", "name": "entry : default@GenerateLoaderJson cost memory 0.8805465698242188", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929398115700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef75c673-f171-4923-a036-7b84cf2ae495", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929371566800, "endTime": 152929398183700}, "additional": {"logType": "info", "children": [], "durationId": "34e6612b-ff00-4d06-aca3-b62da344f9d2"}}, {"head": {"id": "3d73769a-651b-4f17-8c40-1de1fa273657", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929408496500, "endTime": 152929413795800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "19666a07-a59f-42af-b316-8e005ff50cf4", "logId": "dd520e1d-9ac8-42ac-8085-ababc2923fa4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19666a07-a59f-42af-b316-8e005ff50cf4", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929406468800}, "additional": {"logType": "detail", "children": [], "durationId": "3d73769a-651b-4f17-8c40-1de1fa273657"}}, {"head": {"id": "9f80caee-24d0-4c6e-89a4-a0422d36e9da", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929407495100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cf37d98-6114-4656-a714-d0422526cedb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929407604000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3afd3c77-b9e0-4bab-927f-780f184cb3c3", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929408507000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d454da8-b4d8-4780-bb7b-78ab7f7c5287", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929413553700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "323526f7-e126-4d71-8308-f49f730ee992", "name": "entry : default@ProcessLibs cost memory 0.1527099609375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929413721200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd520e1d-9ac8-42ac-8085-ababc2923fa4", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929408496500, "endTime": 152929413795800}, "additional": {"logType": "info", "children": [], "durationId": "3d73769a-651b-4f17-8c40-1de1fa273657"}}, {"head": {"id": "b3547955-9540-4dd1-8f0c-5a2750ab3450", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929422417100, "endTime": 152929457763900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "932d82ed-25ef-428e-aac5-fd30c05ca290", "logId": "d27c4e4b-1253-4d69-b363-a522d451b7cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "932d82ed-25ef-428e-aac5-fd30c05ca290", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929416128300}, "additional": {"logType": "detail", "children": [], "durationId": "b3547955-9540-4dd1-8f0c-5a2750ab3450"}}, {"head": {"id": "afbf3550-4c0c-45d2-b89e-0d6bfb2c0050", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929417222400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61c0e3de-8444-4983-aef9-abb1c8a96bb2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929417355300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e27dbcf-424e-4889-aab1-33d19be20676", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929418503800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "161de52e-8e1d-4a68-a262-9baa5d2e8aad", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929422606700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "359b8744-cf36-4154-a645-abc9614035b7", "name": "Incremental task entry:default@CompileResource pre-execution cost: 31 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929457407500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18f28df9-27db-467a-ba98-01090b19a2b6", "name": "entry : default@CompileResource cost memory 1.3323745727539062", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929457628300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d27c4e4b-1253-4d69-b363-a522d451b7cf", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929422417100, "endTime": 152929457763900}, "additional": {"logType": "info", "children": [], "durationId": "b3547955-9540-4dd1-8f0c-5a2750ab3450"}}, {"head": {"id": "1c9126ec-1ad6-4778-a80d-be71049decce", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929465197400, "endTime": 152929468007600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7fd15d94-3c05-48d2-b716-760f95cf0260", "logId": "69f777a9-8156-4d13-a87f-383bed603a87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7fd15d94-3c05-48d2-b716-760f95cf0260", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929461279300}, "additional": {"logType": "detail", "children": [], "durationId": "1c9126ec-1ad6-4778-a80d-be71049decce"}}, {"head": {"id": "e08289f6-2d19-4563-ac36-c15b5ddd6572", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929462290800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "277325da-eb91-4ddc-8209-5ca84a6d7bf0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929462425500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16b57d38-5297-4cd5-950a-b125b4cd4113", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929465214100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba75c1b2-31b7-49bb-8d51-bed533428381", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929465917700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "209ff65b-a3af-471f-bd5e-38dc39d70279", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929467778300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ab677c2-f254-4ada-a6e4-082fc6d93327", "name": "entry : default@DoNativeStrip cost memory 0.08587646484375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929467930300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69f777a9-8156-4d13-a87f-383bed603a87", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929465197400, "endTime": 152929468007600}, "additional": {"logType": "info", "children": [], "durationId": "1c9126ec-1ad6-4778-a80d-be71049decce"}}, {"head": {"id": "632a2269-3cae-43c8-b78e-91c29ffcf3d5", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929479866500, "endTime": 152940368168600}, "additional": {"children": ["a6828c0a-adac-49de-8407-6be91099acea", "d2e914b7-9a74-44b1-86c3-89e926b8ae5f"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "6e30e8a8-085f-43cc-825e-144d4679ef3e", "logId": "8b99f1e5-5a98-4390-9ece-829da252750f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e30e8a8-085f-43cc-825e-144d4679ef3e", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929470403500}, "additional": {"logType": "detail", "children": [], "durationId": "632a2269-3cae-43c8-b78e-91c29ffcf3d5"}}, {"head": {"id": "61957f10-8fe9-488e-83bf-1cb994f521ea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929472089400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa4f4cb0-3250-4e9c-980a-4f2dabb4bd43", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929472270300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bec00ecf-d826-473b-9f51-01de8c4cd807", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929479878800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4db9378-29d2-4be0-ae1b-a01e3cbf88b3", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929480070100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86720256-a52f-415e-9eae-b0b5e95b4c94", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929508295400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0eabfc7-d97b-4887-91f1-9434b9db01f3", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929508497600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d56a7ed-70da-404b-9857-99281256d4aa", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929534669900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e54f459c-fd15-4aa7-bf87-be9aad976ee1", "name": "default@CompileArkTS work[6] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929537272300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6828c0a-adac-49de-8407-6be91099acea", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152929542017000, "endTime": 152940367878700}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "632a2269-3cae-43c8-b78e-91c29ffcf3d5", "logId": "9ea57818-5aa9-4fcc-bed4-18b6339b2d24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4758c478-435b-43c7-827a-71ff68bf1db4", "name": "default@CompileArkTS work[6] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929538879100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acf1abfd-55b8-44d3-bf29-f13314048151", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539004200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e8b4303-c1e6-48e7-8748-662ee1f4781d", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539067700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4135087-e8fa-4ac8-8d99-66db61981183", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539111100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "574c1847-a8a8-43d3-87be-558cb465724c", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539147600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77a7b529-34cc-4514-abe8-62e9e73bc348", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539185300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a16d544-4d6a-4373-aaac-11cada100de2", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539213900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "012effaf-21aa-4866-ac08-f5c9b83ae6aa", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539241800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa96ad3e-9f3e-4d4f-ad43-c4eddd352eae", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539268000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bd3e146-efe2-4b7a-98b1-87a5ce8184c5", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539371300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c65b87fb-f7a8-4c2c-8f26-7dc8a1321aeb", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539466200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "268239f8-5859-4bba-966e-591a33903fff", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539506200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d85a39eb-4a6b-40f5-a6cc-3e94bcc33dfc", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539537200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4011017-5ea2-4812-9d0e-48452350ed2d", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539566600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a18ffc35-3ee1-4afa-9550-1984fda6dbc6", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539594500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af8188b7-b9a9-4285-a4f1-0cde08728c72", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539623200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16cd4811-929e-4642-b630-a8bd370984c1", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929539712800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9431031-76a3-423b-b1c6-2234caceec4c", "name": "default@CompileArkTS work[6] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929542088000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bc0312c-56c3-490e-9c14-f4181cfe0dee", "name": "default@CompileArkTS work[6] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929542273100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccb850ef-895f-4ad8-be41-16fecb922d03", "name": "CopyResources startTime: 152929542351300", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929542354600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d27bcd7-2e69-4c1b-81a2-e3fb38573f54", "name": "default@CompileArkTS work[7] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929542472300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2e914b7-9a74-44b1-86c3-89e926b8ae5f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 152930666818900, "endTime": 152930682740000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "632a2269-3cae-43c8-b78e-91c29ffcf3d5", "logId": "38d487f7-7c4a-43fd-a840-8e4f647c9385"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2b1828f-63fb-49e4-909a-82394b906394", "name": "default@CompileArkTS work[7] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929543850700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "964b330c-df29-4119-b586-469361744977", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929543968600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd8c4923-7012-4b1e-a27a-c77b0cf20627", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929544031000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be1d50bc-3242-47cd-a0cc-6d90074a3503", "name": "default@CompileArkTS work[7] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929544919200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaea67a1-395b-42ff-949b-b6a021641bab", "name": "default@CompileArkTS work[7] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929545038700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b8a878f-2ce8-4345-bb80-d6af7871b704", "name": "entry : default@CompileArkTS cost memory 2.5202178955078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929545214600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bc758b9-6cc6-4508-986e-bcdb5cf32e98", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929554479600, "endTime": 152929564259900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "e772a4d5-526a-4b00-a425-0086cdec7753", "logId": "3b986ad3-521e-4eaa-b333-2a3c9d16dced"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e772a4d5-526a-4b00-a425-0086cdec7753", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929547480800}, "additional": {"logType": "detail", "children": [], "durationId": "6bc758b9-6cc6-4508-986e-bcdb5cf32e98"}}, {"head": {"id": "14dc4f9a-ee0c-4acf-a0f1-8c8b63668c7d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929548878700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1762ec1a-5350-47c0-97f8-809f254d3cee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929549028500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "526bf67b-23ef-4e4f-b5c4-92cb29ed5a52", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929554500000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9754658-d471-4693-b260-6382c1f71288", "name": "entry : default@BuildJS cost memory 0.35052490234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929563879500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4838ac9-526a-4b1b-9705-fe0cf585e942", "name": "runTaskFromQueue task cost before running: 693 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929564139700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b986ad3-521e-4eaa-b333-2a3c9d16dced", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929554479600, "endTime": 152929564259900, "totalTime": 9602100}, "additional": {"logType": "info", "children": [], "durationId": "6bc758b9-6cc6-4508-986e-bcdb5cf32e98"}}, {"head": {"id": "ba0320b3-6095-4879-b3a0-ab27b9bc099e", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929572889900, "endTime": 152929581194000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "44c95e22-e735-4f4d-b00d-d2c6d2e2f806", "logId": "4d96de9f-f565-4569-9fcd-b63580eff913"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44c95e22-e735-4f4d-b00d-d2c6d2e2f806", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929567050200}, "additional": {"logType": "detail", "children": [], "durationId": "ba0320b3-6095-4879-b3a0-ab27b9bc099e"}}, {"head": {"id": "174a8ab4-6ea0-4550-8fa3-7282e149f2c8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929568696300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9169f0fc-7fa2-4b28-bfd4-70bdb56aded3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929568886000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48c820fd-2980-4dc5-9d89-b350fb5d1742", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929572910700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a66382b-8220-4bf8-bb55-186bc7c9a164", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929574447600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c677bc1-f179-416c-8446-da08ff140fac", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929580764600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68f5f297-a65f-42a2-ab56-921fccbfa203", "name": "entry : default@CacheNativeLibs cost memory 0.09992218017578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929581018300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d96de9f-f565-4569-9fcd-b63580eff913", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929572889900, "endTime": 152929581194000}, "additional": {"logType": "info", "children": [], "durationId": "ba0320b3-6095-4879-b3a0-ab27b9bc099e"}}, {"head": {"id": "715545c6-54b5-46b6-91e2-b12e08656463", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152930683041500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b86e131-f1a1-4a1a-8770-91d26326067f", "name": "CopyResources is end, endTime: 152930683346600", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152930683357700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7f59053-7b85-4518-a610-0c83e5c105cd", "name": "default@CompileArkTS work[7] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152930683685800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38d487f7-7c4a-43fd-a840-8e4f647c9385", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 152930666818900, "endTime": 152930682740000}, "additional": {"logType": "info", "children": [], "durationId": "d2e914b7-9a74-44b1-86c3-89e926b8ae5f", "parent": "8b99f1e5-5a98-4390-9ece-829da252750f"}}, {"head": {"id": "cf35824c-7821-41ec-9c14-c14cb8fb9da7", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152930683923300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97d238d7-d1a3-425c-94ce-9ff1084a2ae3", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940367619400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86c81080-98b9-408a-b498-29badacc0e39", "name": "default@CompileArkTS work[6] failed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940367993600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ea57818-5aa9-4fcc-bed4-18b6339b2d24", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152929542017000, "endTime": 152940367878700}, "additional": {"logType": "error", "children": [], "durationId": "a6828c0a-adac-49de-8407-6be91099acea", "parent": "8b99f1e5-5a98-4390-9ece-829da252750f"}}, {"head": {"id": "8b99f1e5-5a98-4390-9ece-829da252750f", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152929479866500, "endTime": 152940368168600}, "additional": {"logType": "error", "children": ["9ea57818-5aa9-4fcc-bed4-18b6339b2d24", "38d487f7-7c4a-43fd-a840-8e4f647c9385"], "durationId": "632a2269-3cae-43c8-b78e-91c29ffcf3d5"}}, {"head": {"id": "82ea63b0-73ee-44e1-8af4-65f2a315740e", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940368521100}, "additional": {"logType": "debug", "children": [], "durationId": "632a2269-3cae-43c8-b78e-91c29ffcf3d5"}}, {"head": {"id": "8187ab6c-1e37-42c7-84ea-4ba9eae836c9", "name": "ERROR: stacktrace = Error: ArkTS Compiler Error\r\n\u001b[31m1 ERROR: \u001b[31m10905402 ArkTS Compiler Error\r\nError Message: A page configured in 'main_pages.json or build-profile.json5' must have one and only one '@Entry' decorator. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/HomePage.ets\r\n\r\n* Try the following:\r\n  > Please make sure that the splash page has one and only one '@Entry' decorator.\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m2 ERROR: \u001b[31m10905402 ArkTS Compiler Error\r\nError Message: A page configured in 'main_pages.json or build-profile.json5' must have one and only one '@Entry' decorator. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/CategoryListPage.ets\r\n\r\n* Try the following:\r\n  > Please make sure that the splash page has one and only one '@Entry' decorator.\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m3 ERROR: \u001b[31m10905402 ArkTS Compiler Error\r\nError Message: A page configured in 'main_pages.json or build-profile.json5' must have one and only one '@Entry' decorator. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/ProfilePage.ets\r\n\r\n* Try the following:\r\n  > Please make sure that the splash page has one and only one '@Entry' decorator.\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m4 ERROR: \u001b[31m10905402 ArkTS Compiler Error\r\nError Message: A page configured in 'main_pages.json or build-profile.json5' must have one and only one '@Entry' decorator. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/SearchPage.ets\r\n\r\n* Try the following:\r\n  > Please make sure that the splash page has one and only one '@Entry' decorator.\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m5 ERROR: \u001b[31m10905402 ArkTS Compiler Error\r\nError Message: A page configured in 'main_pages.json or build-profile.json5' must have one and only one '@Entry' decorator. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedPage.ets\r\n\r\n* Try the following:\r\n  > Please make sure that the splash page has one and only one '@Entry' decorator.\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:6}\u001b[39m\n    at runArkPack (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5479)", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940369187600}, "additional": {"logType": "debug", "children": [], "durationId": "632a2269-3cae-43c8-b78e-91c29ffcf3d5"}}, {"head": {"id": "ddcbb619-f975-48a6-b469-7bc34035b8f1", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940382781900, "endTime": 152940382977200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0ede3d97-40a4-4d51-8181-5b90e9a393c7", "logId": "33afca31-9b44-4696-8a27-2b97c4534e23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33afca31-9b44-4696-8a27-2b97c4534e23", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940382781900, "endTime": 152940382977200}, "additional": {"logType": "info", "children": [], "durationId": "ddcbb619-f975-48a6-b469-7bc34035b8f1"}}, {"head": {"id": "3d9bc12c-4538-43ae-911a-f8805b033e2f", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152928871907800, "endTime": 152940383244600}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 26, "second": 10}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "6da83ebc-242f-42e1-86f6-1d25df8fbd9f", "name": "BUILD FAILED in 11 s 512 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940383303800}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "faf155e5-36a7-4237-91e1-d8a9cdb1c25f", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940383471300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57c09977-b3ad-4d3f-a0af-058390cbb2b2", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940383531000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd44f938-29b8-42ab-a023-1652c79420c3", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940383915000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e354057-b571-4231-be2e-b8cc88a7e61f", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940383987400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b685e501-5553-4c6c-872f-d928806a6632", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940384023900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0fbb7ac-f56d-4179-a388-dab702368e3a", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940384061600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eb3b322-b94a-4ac5-882d-342f9bd80aa4", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940384117300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb557014-ddc7-4b69-adfc-b401a51505db", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940384662300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58c6ea79-39b1-4608-9119-61b1b95c6df5", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940384866200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5855762d-09ef-4091-8b50-41f607f53d10", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940384925500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e2be836-ab51-40a5-bce1-b7a3dcbbb4d3", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940384965300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb421b8f-ade2-4893-8e36-b77b79a346cc", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940384994400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d2046c8-a495-419f-a52e-a48caf579599", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940385025300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59149608-75df-413d-ad3e-15c4513990e7", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940386083000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1bdad78-24be-4b99-98ef-9b4c4bfa61d7", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940386359600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "387c0891-c29d-4331-9df1-c5b8339ac5a7", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940386574300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16669daa-67a5-4fbd-9841-868ef533878f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940386634300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72a1f218-f869-4b58-9671-163529b0ae22", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940386670600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37909fe8-098c-4827-9336-44e4b94b0dab", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940386700000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e26cccec-cee4-4999-858c-4396bb4e8462", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940386725200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b9c1b6d-5c9c-4572-9a24-deaee38c0933", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940386765100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9f5828f-0f14-43b2-bfee-77da7004c37a", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940390615400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0122dd59-d841-4105-97c9-b1ff562f364b", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940391421100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51e63998-ac84-4072-bdf9-28ba7d6363ed", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940391839900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47f5d0e4-6f97-4192-8a91-fdfd7a54f45f", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940392121900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba2fe825-8ff6-49be-bcff-c5c0695443cf", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940392337500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66d4660c-e949-4583-bc23-69deef27f099", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940393070700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96d71b76-ba25-4859-aec1-30df9b62387e", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940393149900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17acc023-4533-4c2b-9492-7ebad0a3ac1d", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940393369300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b37eb56-26eb-4fd9-9313-6b41c27e87ce", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940393724800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f856384d-b628-491a-a32d-49c1aeb17fc0", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940394629900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a17bb09e-e1da-42c5-a1e8-760749b1a33f", "name": "Incremental task entry:default@CompileArkTS post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940395250900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea53e856-893e-4599-9d40-6c979b1de23d", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940397137000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f738cf9-e72f-45af-99b6-1fbf8c70d733", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940397764600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b719a638-4eb5-416c-bf4c-2d9ed30b99e2", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940398148500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "009eaaeb-a9bf-4cd7-b6e6-93c500d723dd", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940398407400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "717c6c1c-c3db-440b-9a28-4de9f7e42613", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940398634700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5e4a2d4-abcd-456e-ab8f-a519553f8577", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940399347500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e79f015-f4df-4296-bd50-ee02dc185319", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940400208800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f812cec6-ac32-44ae-a7c9-3b2e5f249067", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940400563000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44276625-9f3a-4c5d-a9a5-4383adce4be6", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940400633800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}