# NexusHub-OH API 文档

## 概述

NexusHub-OH-Back提供了一套完整的RESTful API，为OpenHarmony系统应用商店提供后端支持。所有API均采用JSON格式进行数据交换，并通过JWT进行身份验证。

### 基本信息

- **基础URL**: `/api/v1`
- **内容类型**: `application/json`
- **版本**: 1.0
- **主机**: `localhost:8080`
- **协议**: `http`
- **Swagger文档**: `/swagger/index.html`

### 响应格式

所有API响应都遵循统一的JSON格式：

```json
{
  "code": 200,       // HTTP状态码
  "message": "操作成功", // 操作结果消息
  "data": {}         // 响应数据（可能是对象、数组或null）
}
```

分页响应格式：

```json
{
  "code": 200,       // HTTP状态码
  "message": "操作成功", // 操作结果消息
  "data": [],        // 数据数组
  "total": 100,      // 总记录数
  "page": 1,         // 当前页码
  "page_size": 20    // 每页数量
}
```

### 错误码

| 状态码 | 描述 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权/认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 认证

API使用基于JWT的认证机制。除了少数公开接口外，大多数API都需要在请求头中包含有效的JWT令牌。

### 获取令牌

通过登录接口获取JWT令牌：

```
POST /api/v1/users/login
```

### 使用令牌

在需要认证的请求中，添加Authorization请求头：

```
Authorization: Bearer {token}
```

## 用户模块

### 注册

**请求**：
```
POST /api/v1/users/register
```

**请求体**：
```json
{
  "username": "johndoe",     // 必填，3-50字符
  "email": "<EMAIL>", // 必填，有效邮箱
  "phone": "***********",    // 可选，11位手机号
  "password": "password123"  // 必填，6-50字符
}
```

**响应**：
```json
{
  "code": 200,
  "message": "注册成功",
  "data": null
}
```

### 登录

**请求**：
```
POST /api/v1/users/login
```

**请求体**：
```json
{
  "username_or_email": "johndoe", // 必填，用户名或邮箱
  "password": "password123"      // 必填，密码
}
```

**响应**：
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiZXhwIjoxNjg4MDQ1MzYwfQ.GYk45OUH7MHk9E4H-4s9NJEXpGBPuuJ3WZJu9j1_GxA",
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin"
    }
  }
}
```

### 获取用户资料

**请求**：
```
GET /api/v1/users/profile
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "username": "johndoe",
    "email": "<EMAIL>",
    "phone": "***********",
    "role": "user",
    "status": "active",
    "avatar": "https://example.com/avatar.jpg",
    "created_at": "2023-09-01T12:00:00Z",
    "updated_at": "2023-09-30T12:00:00Z",
    "last_login_at": "2023-09-30T12:00:00Z",
    "login_count": 15,
    "is_developer": false,
    "developer_name": "",
    "company_name": "",
    "website": "",
    "description": "",
    "contact_email": "",
    "contact_phone": "",
    "business_license": "",
    "identity_card": "",
    "developer_avatar": "",
    "developer_address": "",
    "submitted_at": "0001-01-01T00:00:00Z",
    "verified_at": null,
    "verify_reason": "",
    "verify_status": "pending"
  }
}
```

### 更新用户资料

**请求**：
```
PUT /api/v1/users/profile
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "username": "newname",       // 可选，3-50字符
  "email": "<EMAIL>",  // 可选，有效邮箱
  "phone": "***********",      // 可选，11位手机号
  "avatar": "https://example.com/new-avatar.jpg", // 可选，头像URL
  "old_password": "password123", // 可选，当前密码（修改密码时必填）
  "new_password": "newpassword", // 可选，新密码（6-50字符）
  "is_developer": true,        // 可选，是否为开发者
  "developer_info": {          // 可选，开发者信息
    "developer_name": "John Dev", // is_developer为true时必填
    "company_name": "John Inc",
    "website": "https://johndoe.com",
    "description": "Mobile app developer"
  }
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "username": "newname",
    "email": "<EMAIL>",
    "phone": "***********",
    "role": "user",
    "status": "active",
    "avatar": "https://example.com/new-avatar.jpg",
    "created_at": "2023-09-01T12:00:00Z",
    "updated_at": "2023-09-30T12:00:00Z",
    "last_login_at": "2023-09-30T12:00:00Z",
    "login_count": 15,
    "is_developer": true,
    "developer_name": "John Dev",
    "company_name": "John Inc",
    "website": "https://johndoe.com",
    "description": "Mobile app developer",
    "contact_email": "<EMAIL>",
    "contact_phone": "***********",
    "business_license": "",
    "identity_card": "",
    "developer_avatar": "",
    "developer_address": "",
    "submitted_at": "0001-01-01T00:00:00Z",
    "verified_at": null,
    "verify_reason": "",
    "verify_status": "pending"
  }
}
```

### 管理员创建用户

**请求**：
```
POST /api/v1/admin/users
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "username": "newuser",       // 必填，3-50字符
  "email": "<EMAIL>", // 必填，有效邮箱
  "phone": "***********",      // 可选，11位手机号
  "password": "password123",   // 必填，6-50字符
  "role": "developer"          // 必填，角色(user/developer/operator/reviewer/admin)
}
```

**响应**：
```json
{
  "code": 200,
  "message": "创建成功",
  "data": null
}
```

### 更新用户角色

**请求**：
```
PUT /api/v1/admin/users/{id}/role
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "role": "operator" // 必填，角色(user/developer/operator/reviewer/admin)
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

## 仪表盘模块

仪表盘模块提供了分析页、监控页和工作台三个主要功能区域，用于展示系统统计数据、监控信息和个人工作台信息。

### 分析页接口

#### 获取分析页摘要数据

**请求**：
```
GET /api/v1/dashboard/analytics/summary
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_users": 1250,           // 总用户数
    "total_apps": 89,              // 总应用数
    "total_downloads": 15420,      // 总下载量
    "total_reviews": 342,          // 总评论数
    "total_developers": 45,        // 总开发者数
    "new_users_today": 12,         // 今日新增用户
    "new_apps_today": 3,           // 今日新增应用
    "new_downloads_today": 156,    // 今日新增下载
    "pending_apps_count": 8,       // 待审核应用数
    "pending_reviews_count": 5     // 待审核评论数
  }
}
```

#### 获取趋势分析数据

**请求**：
```
GET /api/v1/dashboard/analytics/trend?days=30
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `days`：可选，统计天数，默认30天

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "user_trend": [
      {"date": "2023-09-01", "value": 45},
      {"date": "2023-09-02", "value": 52}
    ],
    "app_trend": [
      {"date": "2023-09-01", "value": 3},
      {"date": "2023-09-02", "value": 5}
    ],
    "download_trend": [
      {"date": "2023-09-01", "value": 234},
      {"date": "2023-09-02", "value": 289}
    ],
    "developer_trend": [
      {"date": "2023-09-01", "value": 2},
      {"date": "2023-09-02", "value": 3}
    ]
  }
}
```

#### 获取分类统计数据

**请求**：
```
GET /api/v1/dashboard/analytics/categories
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "category_id": 1,
      "category_name": "游戏",
      "app_count": 25,
      "download_count": 5420
    },
    {
      "category_id": 2,
      "category_name": "工具",
      "app_count": 18,
      "download_count": 3210
    }
  ]
}
```

#### 获取热门应用

**请求**：
```
GET /api/v1/dashboard/analytics/popular-apps?limit=10
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `limit`：可选，返回数量限制，默认10

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "app_id": 1,
      "app_name": "超级游戏",
      "app_icon": "https://example.com/icon1.png",
      "developer_name": "游戏工作室",
      "download_count": 1520,
      "rating": 4.8,
      "category_name": "游戏"
    }
  ]
}
```

### 监控页接口

#### 获取系统监控数据

**请求**：
```
GET /api/v1/dashboard/monitoring/data
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "summary": {
      "server_status": "running",
      "cpu_usage": 45.2,
      "memory_usage": 68.5,
      "disk_usage": 32.1,
      "database_connections": 15,
      "average_response_time": 120.5,
      "requests_per_minute": 450,
      "error_rate": 0.02,
      "uptime_hours": 168.5
    },
    "details": {
      "database": {
        "status": "healthy",
        "connections": 15,
        "max_connections": 100,
        "query_time": 25.3
      },
      "redis": {
        "status": "healthy",
        "memory_usage": 45.2,
        "connected_clients": 8
      },
      "storage": {
        "status": "healthy",
        "total_space": "500GB",
        "used_space": "160GB",
        "available_space": "340GB"
      }
    }
  }
}
```

#### 获取系统日志

**请求**：
```
GET /api/v1/dashboard/monitoring/logs?page=1&page_size=20&level=error
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `page`：可选，页码，默认1
- `page_size`：可选，每页数量，默认20
- `level`：可选，日志级别过滤（info/warning/error/critical）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "logs": [
      {
        "id": 1,
        "level": "error",
        "source": "database",
        "message": "连接超时",
        "created_at": "2023-09-30T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 156,
      "total_pages": 8
    }
  }
}
```

#### 获取告警事件

**请求**：
```
GET /api/v1/dashboard/monitoring/alerts?page=1&page_size=20&status=active
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `page`：可选，页码，默认1
- `page_size`：可选，每页数量，默认20
- `status`：可选，状态过滤（active/resolved）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "alerts": [
      {
        "id": 1,
        "severity": "high",
        "type": "system",
        "description": "CPU使用率过高",
        "status": "active",
        "created_at": "2023-09-30T12:00:00Z",
        "resolved_at": null
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 23,
      "total_pages": 2
    }
  }
}
```

### 工作台接口

#### 获取工作台摘要

**请求**：
```
GET /api/v1/dashboard/workbench/summary
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "my_app_count": 5,              // 我的应用数量
    "total_downloads": 2340,        // 我的应用总下载量
    "average_rating": 4.6,          // 我的应用平均评分
    "new_reviews_count": 8,         // 我的应用新评论数量（最近7天）
    "pending_apps_count": 2,        // 我的待审核应用数量
    "completed_task_count": 12,     // 已完成任务数量
    "total_task_count": 18          // 总任务数量
  }
}
```

#### 获取最近活动

**请求**：
```
GET /api/v1/dashboard/workbench/activities?limit=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `limit`：可选，返回数量限制，默认20

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "user_id": 123,
      "type": "app_create",
      "content": "创建了新应用《超级工具》",
      "created_at": "2023-09-30T12:00:00Z"
    },
    {
      "id": 2,
      "user_id": 123,
      "type": "review",
      "content": "收到了新的应用评论",
      "created_at": "2023-09-30T11:30:00Z"
    }
  ]
}
```

#### 获取任务列表

**请求**：
```
GET /api/v1/dashboard/workbench/tasks?status=pending&page=1&page_size=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `status`：可选，任务状态过滤（pending/in_progress/completed）
- `page`：可选，页码，默认1
- `page_size`：可选，每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "tasks": [
      {
        "id": 1,
        "user_id": 123,
        "title": "完成应用审核",
        "description": "审核新提交的应用",
        "status": "pending",
        "priority": "high",
        "due_date": "2023-10-01T18:00:00Z",
        "created_at": "2023-09-30T12:00:00Z",
        "updated_at": "2023-09-30T12:00:00Z",
        "completed_at": null
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 18,
      "total_pages": 1
    }
  }
}
```

#### 创建任务

**请求**：
```
POST /api/v1/dashboard/workbench/tasks
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "title": "完成应用审核",           // 必填，任务标题
  "description": "审核新提交的应用", // 可选，任务描述
  "priority": "high",            // 可选，优先级（low/medium/high），默认medium
  "due_date": "2023-10-01T18:00:00Z" // 可选，截止日期
}
```

**响应**：
```json
{
  "code": 200,
  "message": "创建任务成功",
  "data": {
    "id": 1,
    "user_id": 123,
    "title": "完成应用审核",
    "description": "审核新提交的应用",
    "status": "pending",
    "priority": "high",
    "due_date": "2023-10-01T18:00:00Z",
    "created_at": "2023-09-30T12:00:00Z",
    "updated_at": "2023-09-30T12:00:00Z",
    "completed_at": null
  }
}
```

#### 更新任务

**请求**：
```
PUT /api/v1/dashboard/workbench/tasks/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "title": "完成应用审核（已更新）", // 可选，任务标题
  "description": "审核新提交的应用", // 可选，任务描述
  "status": "completed",         // 可选，任务状态（pending/in_progress/completed）
  "priority": "medium",          // 可选，优先级（low/medium/high）
  "due_date": "2023-10-02T18:00:00Z" // 可选，截止日期
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新任务成功",
  "data": {
    "id": 1,
    "user_id": 123,
    "title": "完成应用审核（已更新）",
    "description": "审核新提交的应用",
    "status": "completed",
    "priority": "medium",
    "due_date": "2023-10-02T18:00:00Z",
    "created_at": "2023-09-30T12:00:00Z",
    "updated_at": "2023-09-30T14:30:00Z",
    "completed_at": "2023-09-30T14:30:00Z"
  }
}
```

#### 删除任务

**请求**：
```
DELETE /api/v1/dashboard/workbench/tasks/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除任务成功",
  "data": null
}
```

## 开发者模块

### 提交开发者认证

**请求**：
```
POST /api/v1/developers/verify
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "developer_name": "John Developer",    // 必填，2-100字符
  "company_name": "John Inc",           // 可选，公司名称
  "contact_email": "<EMAIL>",   // 必填，联系邮箱
  "contact_phone": "***********",       // 必填，联系电话
  "website": "https://johndoe.com",     // 可选，网站URL
  "description": "Mobile app developer focusing on OpenHarmony", // 必填，10-1000字符
  "developer_address": "Beijing, China", // 必填，5-255字符
  "developer_avatar": "https://example.com/dev-avatar.jpg", // 可选，头像URL
  "business_license": "https://example.com/license.jpg",    // 可选，营业执照URL
  "identity_card": "https://example.com/id-card.jpg"        // 必填，身份证URL
}
```

**响应**：
```json
{
  "code": 200,
  "message": "提交成功",
  "data": {
    "id": 1,
    "username": "johndoe",
    "developer_name": "John Developer",
    "company_name": "John Inc",
    "contact_email": "<EMAIL>",
    "contact_phone": "***********",
    "website": "https://johndoe.com",
    "description": "Mobile app developer focusing on OpenHarmony",
    "developer_address": "Beijing, China",
    "developer_avatar": "https://example.com/dev-avatar.jpg",
    "business_license": "https://example.com/license.jpg",
    "identity_card": "https://example.com/id-card.jpg",
    "submitted_at": "2023-10-01T12:00:00Z",
    "verified_at": null,
    "verify_reason": "",
    "verify_status": "pending"
  }
}
```

### 获取认证状态

**请求**：
```
GET /api/v1/developers/verify/status
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "username": "johndoe",
    "developer_name": "John Developer",
    "company_name": "John Inc",
    "contact_email": "<EMAIL>",
    "contact_phone": "***********",
    "website": "https://johndoe.com",
    "description": "Mobile app developer focusing on OpenHarmony",
    "developer_address": "Beijing, China",
    "developer_avatar": "https://example.com/dev-avatar.jpg",
    "business_license": "https://example.com/license.jpg",
    "identity_card": "https://example.com/id-card.jpg",
    "submitted_at": "2023-10-01T12:00:00Z",
    "verified_at": "2023-10-02T10:00:00Z",
    "verify_reason": "",
    "verify_status": "approved"
  }
}
```

### 获取上传凭证

**请求**：
```
GET /api/v1/upload/token
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `file_type`: 文件类型(avatar/license/identity/screenshot/package)
- `file_name`: 文件名称

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "file_url": "https://storage.example.com/avatar/user1/avatar.jpg"
  }
}
```

### 管理员获取待审核的开发者列表

**请求**：
```
GET /api/v1/admin/developers/verify
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `status`: 认证状态(pending/approved/rejected)，默认pending

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "username": "johndoe",
      "developer_name": "John Developer",
      "company_name": "John Inc",
      "contact_email": "<EMAIL>",
      "contact_phone": "***********",
      "website": "https://johndoe.com",
      "description": "Mobile app developer focusing on OpenHarmony",
      "developer_address": "Beijing, China",
      "developer_avatar": "https://example.com/dev-avatar.jpg",
      "business_license": "https://example.com/license.jpg",
      "identity_card": "https://example.com/id-card.jpg",
      "submitted_at": "2023-10-01T12:00:00Z",
      "verified_at": null,
      "verify_reason": "",
      "verify_status": "pending"
    }
  ],
  "total": 5,
  "page": 1,
  "page_size": 20
}
```

### 管理员审核开发者认证

**请求**：
```
POST /api/v1/admin/developers/{id}/verify
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "verify_status": "approved",  // 必填，审核结果(approved/rejected)
  "verify_reason": "信息完善，符合要求"  // 拒绝时必填，拒绝理由
}
```

**响应**：
```json
{
  "code": 200,
  "message": "审核成功",
  "data": {
    "id": 1,
    "username": "johndoe",
    "developer_name": "John Developer",
    "company_name": "John Inc",
    "contact_email": "<EMAIL>",
    "contact_phone": "***********",
    "website": "https://johndoe.com",
    "description": "Mobile app developer focusing on OpenHarmony",
    "developer_address": "Beijing, China",
    "developer_avatar": "https://example.com/dev-avatar.jpg",
    "business_license": "https://example.com/license.jpg",
    "identity_card": "https://example.com/id-card.jpg",
    "submitted_at": "2023-10-01T12:00:00Z",
    "verified_at": "2023-10-02T10:00:00Z",
    "verify_reason": "信息完善，符合要求",
    "verify_status": "approved"
  }
}
```

## 应用模块

### 公开API接口

#### 获取应用排行

**请求**：
```
GET /api/v1/public/apps/featured
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": true,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ],
  "total": 50,
  "page": 1,
  "page_size": 20
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"且标记为精选的应用（is_featured=true）
- 按创建时间倒序排列

#### 获取推荐应用

**请求**：
```
GET /api/v1/public/apps/recommended
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": true,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ],
  "total": 50,
  "page": 1,
  "page_size": 20
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"且标记为推荐的应用
- 按创建时间倒序排列

#### 获取热门应用

**请求**：
```
GET /api/v1/public/apps/popular
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": false,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "page_size": 20
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"的应用
- 按下载量和评分排序

#### 获取最新应用

**请求**：
```
GET /api/v1/public/apps/latest
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": false,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ],
  "total": 80,
  "page": 1,
  "page_size": 20
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"的应用
- 按创建时间倒序排列

#### 获取公开应用列表

**请求**：
```
GET /api/v1/public/apps
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100
- `category`: 应用分类名称，可选
- `keyword`: 搜索关键词，可选
- `sort`: 排序方式，可选值：latest(最新)、popular(热门)、rating(评分)，默认latest

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": false,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ],
  "total": 200,
  "page": 1,
  "page_size": 20
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"的应用
- 支持分类筛选、关键词搜索和多种排序方式
- 关键词搜索会匹配应用名称、描述和标签

#### 根据分类获取应用

**请求**：
```
GET /api/v1/public/categories/{id}/apps
```

**参数**：
- `id`: 分类ID（路径参数）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100
- `sort`: 排序方式，可选值：latest(最新)、popular(热门)、rating(评分)，默认latest

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": false,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ],
  "total": 25,
  "page": 1,
  "page_size": 20
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"且属于指定分类的应用
- 支持多种排序方式
- 如果分类不存在，返回404错误

#### 获取应用详情

**请求**：
```
GET /api/v1/public/apps/{id}
```

**参数**：
- `id`：应用ID（路径参数）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "Calculator Pro",
    "package_name": "com.example.calculator",
    "description": "A professional calculator app for OpenHarmony with scientific features.",
    "short_description": "Professional calculator",
    "icon": "https://example.com/app-icon.jpg",
    "category_id": 1,
    "category_name": "Tools",
    "developer_id": 1,
    "developer_name": "John Developer",
    "version": "1.0.0",
    "version_code": 100,
    "min_sdk_version": 21,
    "target_sdk_version": 33,
    "size": 2048000,
    "download_url": "https://example.com/calculator.apk",
    "download_count": 1000,
    "rating": 4.5,
    "review_count": 200,
    "permissions": [
      "android.permission.INTERNET",
      "android.permission.CAMERA"
    ],
    "tags": ["calculator", "math", "tools"],
    "changelog": "A professional calculator app with scientific features.",
    "privacy_policy": "https://calculator-pro.example.com/privacy",
    "support_email": "<EMAIL>",
    "website": "https://calculator-pro.example.com",
    "status": "approved",
    "published_at": "2023-09-15T00:00:00Z",
    "screenshots": [
      {
        "id": 1,
        "application_id": 1,
        "image_url": "https://example.com/screenshot1.jpg",
        "sort_order": 1,
        "created_at": "2023-09-01T00:00:00Z"
      }
    ],
    "published_versions": [
      {
        "id": 1,
        "application_id": 1,
        "version_name": "1.0.0",
        "version_code": 100,
        "change_log": "Initial release",
        "package_url": "https://example.com/calculator-1.0.0.hap",
        "size": 2048000,
        "status": "approved",
        "min_open_harmony_os_ver": "3.0.0",
        "released_at": "2023-09-15T00:00:00Z",
        "download_count": 1000,
        "incremental_update": false,
        "created_at": "2023-09-01T00:00:00Z",
        "updated_at": "2023-09-15T00:00:00Z"
      }
    ]
  }
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"的应用信息
- 只返回已发布的应用版本
- 包含应用的基本信息、截图和已发布版本列表

#### 记录应用下载

**请求**：
```
POST /api/v1/public/apps/{id}/versions/{version_id}/download
```

**参数**：
- `id`：应用ID（路径参数）
- `version_id`：版本ID（路径参数）

**请求体**：
```json
{
  "device_info": "OpenHarmony 3.0",  // 可选，设备信息
  "source": "web"                    // 可选，下载来源
}
```

**响应**：
```json
{
  "code": 200,
  "message": "下载记录成功",
  "data": {
    "download_id": 12345,
    "app_id": 1,
    "version_id": 1,
    "download_count": 1001,
    "downloaded_at": "2023-10-01T12:00:00Z"
  }
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只能下载状态为"approved"且已发布的应用版本
- 会自动更新应用和版本的下载计数
- 未登录用户的UserID会被设置为0

### 获取应用列表

**请求**：
```
GET /api/v1/apps
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `category`: 应用分类
- `sort`: 排序方式(newest/popular/rating)
- `keyword`: 搜索关键词

**响应**：
```json
{  "code": 200,
m  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "created_at": "2023-09-01T00:00:00Z",
        "updated_at": "2023-09-15T00:00:00Z",
        "deleted_at": null,
        "name": "Calculator Pro",
        "package_name": "com.example.calculator",
        "description": "A professional calculator app for OpenHarmony",
        "short_description": "Professional calculator",
        "icon": "https://example.com/app-icon.jpg",
        "category_id": 1,
        "category_name": "Tools",
        "developer_id": 1,
        "developer_name": "John Developer",
        "version": "1.0.0",
        "version_code": 100,
        "min_sdk_version": 21,
        "target_sdk_version": 33,
        "size": 2048000,
        "download_url": "https://example.com/calculator.apk",
        "download_count": 1000,
        "rating": 4.5,
        "review_count": 200,
        "screenshots": [
          "https://example.com/screenshot1.jpg",
          "https://example.com/screenshot2.jpg"
        ],
        "permissions": [
          "android.permission.INTERNET",
          "android.permission.CAMERA"
        ],
        "tags": ["calculator", "math", "tools"],
        "changelog": "Initial release with basic calculator functions",
        "privacy_policy": "https://example.com/privacy",
        "support_email": "<EMAIL>",
        "website": "https://example.com",
        "status": "published",
        "is_featured": false,
        "is_editor_choice": true,
        "is_top": false,
        "featured_at": null,
        "published_at": "2023-09-15T00:00:00Z",
        "review_status": "approved",
        "review_reason": "",
        "reviewed_at": "2023-09-14T00:00:00Z",
        "reviewer_id": 2
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 100,
      "total_pages": 5
    }
  }
}
```

### 获取应用详情

**请求**：
```
GET /api/v1/apps/{id}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "created_at": "2023-09-01T00:00:00Z",
    "updated_at": "2023-09-15T00:00:00Z",
    "deleted_at": null,
    "name": "Calculator Pro",
    "package_name": "com.example.calculator",
    "description": "A professional calculator app for OpenHarmony with scientific features.",
    "short_description": "Professional calculator",
    "icon": "https://example.com/app-icon.jpg",
    "category_id": 1,
    "category_name": "Tools",
    "developer_id": 1,
    "developer_name": "John Developer",
    "version": "1.0.0",
    "version_code": 100,
    "min_sdk_version": 21,
    "target_sdk_version": 33,
    "size": 2048000,
    "download_url": "https://example.com/calculator.apk",
    "download_count": 1000,
    "rating": 4.5,
    "review_count": 200,
    "permissions": [
      "android.permission.INTERNET",
      "android.permission.CAMERA"
    ],
    "tags": ["calculator", "math", "tools"],
    "changelog": "A professional calculator app with scientific features.",
    "privacy_policy": "https://calculator-pro.example.com/privacy",
    "support_email": "<EMAIL>",
    "website": "https://calculator-pro.example.com",
    "status": "published",
    "is_featured": false,
    "is_editor_choice": true,
    "is_top": false,
    "featured_at": null,
    "published_at": "2023-09-15T00:00:00Z",
    "review_status": "approved",
    "review_reason": "",
    "reviewed_at": "2023-09-14T00:00:00Z",
    "reviewer_id": 2,
    "screenshots": [
      {
        "id": 1,
        "application_id": 1,
        "image_url": "https://example.com/screenshot1.jpg",
        "sort_order": 1,
        "created_at": "2023-09-01T00:00:00Z"
      },
      {
        "id": 2,
        "application_id": 1,
        "image_url": "https://example.com/screenshot2.jpg",
        "sort_order": 2,
        "created_at": "2023-09-01T00:00:00Z"
      }
    ],
    "versions": [
      {
        "id": 1,
        "application_id": 1,
        "version_name": "1.0.0",
        "version_code": 100,
        "change_log": "Initial release",
        "package_url": "https://example.com/calculator-1.0.0.hap",
        "size": 2048000,
        "status": "approved",
        "min_open_harmony_os_ver": "3.0.0",
        "released_at": "2023-09-15T00:00:00Z",
        "download_count": 1000,
        "incremental_update": false,
        "created_at": "2023-09-01T00:00:00Z",
        "updated_at": "2023-09-15T00:00:00Z"
      }
    ]
  }
}
```

### 创建应用

**请求**：
```
POST /api/v1/apps
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "Calculator Pro",       // 必填，2-100字符
  "package_name": "com.example.calculator", // 必填，3-100字符
  "description": "A professional calculator app for OpenHarmony with scientific features.", // 可选，应用详细描述
  "short_description": "Professional calculator", // 可选，最多200字符
  "category_id": 1,               // 必填，应用分类ID
  "icon": "https://example.com/app-icon.jpg", // 必填，应用图标URL
  "version": "1.0.0",             // 必填，版本号
  "version_code": 100,            // 必填，版本代码
  "min_sdk_version": 21,          // 必填，最低SDK版本
  "target_sdk_version": 33,       // 必填，目标SDK版本
  "download_url": "https://example.com/calculator.apk", // 必填，下载链接
  "size": 2048000,                // 必填，应用大小（字节）
  "permissions": [                // 可选，权限列表
    "android.permission.INTERNET",
    "android.permission.CAMERA"
  ],
  "tags": ["calculator", "math", "tools"], // 可选，标签数组
  "changelog": "Initial release with basic calculator functions", // 可选，更新日志
  "privacy_policy": "https://example.com/privacy", // 可选，隐私政策URL
  "support_email": "<EMAIL>", // 可选，支持邮箱
  "website": "https://example.com" // 可选，应用网站URL
}
```

**响应**：
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z",
    "deleted_at": null,
    "name": "Calculator Pro",
    "package_name": "com.example.calculator",
    "description": "A professional calculator app for OpenHarmony with scientific features.",
    "short_description": "Professional calculator",
    "icon": "https://example.com/app-icon.jpg",
    "category_id": 1,
    "category_name": "Tools",
    "developer_id": 1,
    "developer_name": "John Developer",
    "version": "1.0.0",
    "version_code": 100,
    "min_sdk_version": 21,
    "target_sdk_version": 33,
    "size": 2048000,
    "download_url": "https://example.com/calculator.apk",
    "download_count": 0,
    "rating": 0,
    "review_count": 0,
    "screenshots": [],
    "permissions": [
      "android.permission.INTERNET",
      "android.permission.CAMERA"
    ],
    "tags": ["calculator", "math", "tools"],
    "changelog": "Initial release with basic calculator functions",
    "privacy_policy": "https://example.com/privacy",
    "support_email": "<EMAIL>",
    "website": "https://example.com",
    "status": "draft",
    "is_featured": false,
    "is_editor_choice": false,
    "is_top": false,
    "featured_at": null,
    "published_at": null,
    "review_status": "pending",
    "review_reason": "",
    "reviewed_at": null,
    "reviewer_id": null
  }
}
```

### 更新应用

**请求**：
```
PUT /api/v1/apps/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "Calculator Pro Plus",  // 可选，2-100字符
  "description": "Updated description", // 可选，应用详细描述
  "short_description": "Professional calculator with more features", // 可选，最多200字符
  "category_id": 2,               // 可选，应用分类ID
  "icon": "https://example.com/new-icon.jpg", // 可选，应用图标URL
  "version": "1.1.0",             // 可选，版本号
  "version_code": 110,            // 可选，版本代码
  "min_sdk_version": 23,          // 可选，最低SDK版本
  "target_sdk_version": 34,       // 可选，目标SDK版本
  "download_url": "https://example.com/calculator-plus.apk", // 可选，下载链接
  "size": 2560000,                // 可选，应用大小（字节）
  "permissions": [                // 可选，权限列表
    "android.permission.INTERNET",
    "android.permission.CAMERA",
    "android.permission.WRITE_EXTERNAL_STORAGE"
  ],
  "tags": ["calculator", "math", "tools", "utilities"], // 可选，标签数组
  "changelog": "Added scientific calculator features", // 可选，更新日志
  "privacy_policy": "https://calculator-pro-plus.example.com/privacy", // 可选，隐私政策URL
  "support_email": "<EMAIL>", // 可选，支持邮箱
  "website": "https://calculator-pro-plus.example.com" // 可选，应用网站URL
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "created_at": "2023-09-01T00:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z",
    "deleted_at": null,
    "name": "Calculator Pro Plus",
    "package_name": "com.example.calculator",
    "description": "Updated description",
    "short_description": "Professional calculator with more features",
    "icon": "https://example.com/new-icon.jpg",
    "category_id": 2,
    "category_name": "Utilities",
    "developer_id": 1,
    "developer_name": "John Developer",
    "version": "1.1.0",
    "version_code": 110,
    "min_sdk_version": 23,
    "target_sdk_version": 34,
    "size": 2560000,
    "download_url": "https://example.com/calculator-plus.apk",
    "download_count": 1000,
    "rating": 4.5,
    "review_count": 200,
    "screenshots": [
      "https://example.com/screenshot1.jpg",
      "https://example.com/screenshot2.jpg"
    ],
    "permissions": [
      "android.permission.INTERNET",
      "android.permission.CAMERA",
      "android.permission.WRITE_EXTERNAL_STORAGE"
    ],
    "tags": ["calculator", "math", "tools", "utilities"],
    "changelog": "Added scientific calculator features",
    "privacy_policy": "https://calculator-pro-plus.example.com/privacy",
    "support_email": "<EMAIL>",
    "website": "https://calculator-pro-plus.example.com",
    "status": "published",
    "is_featured": false,
    "is_editor_choice": true,
    "is_top": false,
    "featured_at": null,
    "published_at": "2023-09-15T00:00:00Z",
    "review_status": "approved",
    "review_reason": "",
    "reviewed_at": "2023-09-14T00:00:00Z",
    "reviewer_id": 2
  }
}
``` 


## 精选集模块

精选集模块提供了管理和展示应用精选集的功能，包括创建、编辑、删除精选集以及管理精选集中的应用。

### 公开接口

#### 获取精选集列表

**请求**：
```
GET /api/v1/public/featured-collections
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100
- `status`: 状态过滤，可选值：active（默认）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "编辑精选",
        "description": "编辑团队精心挑选的优质应用",
        "cover_image": "https://example.com/cover1.jpg",
        "app_count": 12,
        "display_order": 1,
        "status": "active",
        "created_at": "2023-09-01T00:00:00Z",
        "updated_at": "2023-09-15T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 5,
      "total_pages": 1
    }
  }
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"active"的精选集
- 按display_order升序排列

#### 获取精选集详情

**请求**：
```
GET /api/v1/public/featured-collections/{id}
```

**参数**：
- `id`: 精选集ID（路径参数）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "编辑精选",
    "description": "编辑团队精心挑选的优质应用",
    "cover_image": "https://example.com/cover1.jpg",
    "app_count": 12,
    "display_order": 1,
    "status": "active",
    "created_at": "2023-09-01T00:00:00Z",
    "updated_at": "2023-09-15T00:00:00Z"
  }
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只能获取状态为"active"的精选集详情
- 如果精选集不存在或状态不为"active"，返回404错误

#### 获取精选集中的应用列表

**请求**：
```
GET /api/v1/public/featured-collections/{id}/apps
```

**参数**：
- `id`: 精选集ID（路径参数）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "Calculator Pro",
        "package_name": "com.example.calculator",
        "description": "A professional calculator app for OpenHarmony",
        "short_description": "Professional calculator",
        "icon": "https://example.com/app-icon.jpg",
        "category_id": 1,
        "category_name": "Tools",
        "developer_id": 1,
        "developer_name": "John Developer",
        "version": "1.0.0",
        "size": 2048000,
        "download_count": 1000,
        "rating": 4.5,
        "review_count": 200,
        "tags": ["calculator", "math", "tools"],
        "status": "published",
        "published_at": "2023-09-15T00:00:00Z",
        "added_at": "2023-09-20T00:00:00Z",
        "display_order": 1
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 12,
      "total_pages": 1
    }
  }
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"published"的应用
- 按精选集中的display_order升序排列
- 如果精选集不存在或状态不为"active"，返回404错误

### 管理员接口

#### 获取精选集列表（管理员）

**请求**：
```
GET /api/v1/admin/featured-collections
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100
- `status`: 状态过滤，可选值：active、inactive、all（默认）
- `keyword`: 搜索关键词，可选

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "编辑精选",
        "description": "编辑团队精心挑选的优质应用",
        "cover_image": "https://example.com/cover1.jpg",
        "app_count": 12,
        "display_order": 1,
        "status": "active",
        "created_at": "2023-09-01T00:00:00Z",
        "updated_at": "2023-09-15T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 8,
      "total_pages": 1
    }
  }
}
```

**说明**：
- 需要管理员权限
- 可以查看所有状态的精选集
- 支持关键词搜索和状态过滤

#### 创建精选集

**请求**：
```
POST /api/v1/admin/featured-collections
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "新游戏精选",                    // 必填，2-100字符
  "description": "最新最热门的游戏合集",    // 必填，10-500字符
  "cover_image": "https://example.com/cover.jpg", // 可选，封面图片URL
  "display_order": 1,                   // 可选，显示顺序，默认0
  "status": "active"                    // 可选，状态，默认active
}
```

**响应**：
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 2,
    "name": "新游戏精选",
    "description": "最新最热门的游戏合集",
    "cover_image": "https://example.com/cover.jpg",
    "app_count": 0,
    "display_order": 1,
    "status": "active",
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z"
  }
}
```

#### 更新精选集

**请求**：
```
PUT /api/v1/admin/featured-collections/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "更新后的精选集名称",           // 可选，2-100字符
  "description": "更新后的描述",        // 可选，10-500字符
  "cover_image": "https://example.com/new-cover.jpg", // 可选，封面图片URL
  "display_order": 2,                 // 可选，显示顺序
  "status": "inactive"                // 可选，状态
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "name": "更新后的精选集名称",
    "description": "更新后的描述",
    "cover_image": "https://example.com/new-cover.jpg",
    "app_count": 12,
    "display_order": 2,
    "status": "inactive",
    "created_at": "2023-09-01T00:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z"
  }
}
```

#### 删除精选集

**请求**：
```
DELETE /api/v1/admin/featured-collections/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

**说明**：
- 删除精选集会同时删除该精选集中的所有应用关联关系
- 删除操作不可逆，请谨慎操作

#### 批量删除精选集

**请求**：
```
DELETE /api/v1/admin/featured-collections
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "ids": [1, 2, 3]  // 必填，精选集ID数组
}
```

**响应**：
```json
{
  "code": 200,
  "message": "批量删除成功",
  "data": {
    "deleted_count": 3,
    "failed_ids": []
  }
}
```

#### 更新精选集状态

**请求**：
```
PUT /api/v1/admin/featured-collections/{id}/status
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "status": "inactive"  // 必填，状态：active或inactive
}
```

**响应**：
```json
{
  "code": 200,
  "message": "状态更新成功",
  "data": null
}
```

#### 更新精选集显示顺序

**请求**：
```
PUT /api/v1/admin/featured-collections/order
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "orders": [
    {"id": 1, "display_order": 1},
    {"id": 2, "display_order": 2},
    {"id": 3, "display_order": 3}
  ]
}
```

**响应**：
```json
{
  "code": 200,
  "message": "顺序更新成功",
  "data": null
}
```

#### 获取精选集中的应用列表（管理员）

**请求**：
```
GET /api/v1/admin/featured-collections/{id}/apps
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `id`: 精选集ID（路径参数）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "Calculator Pro",
        "package_name": "com.example.calculator",
        "icon": "https://example.com/app-icon.jpg",
        "category_name": "Tools",
        "developer_name": "John Developer",
        "version": "1.0.0",
        "download_count": 1000,
        "rating": 4.5,
        "status": "published",
        "added_at": "2023-09-20T00:00:00Z",
        "display_order": 1
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 12,
      "total_pages": 1
    }
  }
}
```

#### 向精选集添加应用

**请求**：
```
POST /api/v1/admin/featured-collections/{id}/apps
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "app_ids": [1, 2, 3],      // 必填，应用ID数组
  "display_order": 1         // 可选，显示顺序，默认为当前最大值+1
}
```

**响应**：
```json
{
  "code": 200,
  "message": "添加成功",
  "data": {
    "added_count": 3,
    "failed_app_ids": [],
    "duplicate_app_ids": []
  }
}
```

#### 从精选集移除应用

**请求**：
```
DELETE /api/v1/admin/featured-collections/{id}/apps
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "app_ids": [1, 2, 3]  // 必填，应用ID数组
}
```

**响应**：
```json
{
  "code": 200,
  "message": "移除成功",
  "data": {
    "removed_count": 3,
    "failed_app_ids": []
  }
}
```

## 评论模块

### 获取应用评论

**请求**：
```
GET /api/v1/apps/{id}/reviews
```

**参数**：
- 应用ID (path)
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "application_id": 1,
      "app_name": "Calculator Pro",
      "user_id": 2,
      "username": "jane_user",
      "avatar": "https://example.com/user-avatar.jpg",
      "title": "Great calculator app",
      "content": "This is a very useful calculator with all the features I need.",
      "rating": 5,
      "app_version": "1.0.0",
      "like_count": 10,
      "dev_response": "Thank you for your feedback!",
      "status": "active",
      "created_at": "2023-09-20T00:00:00Z",
      "updated_at": "2023-09-21T00:00:00Z"
    }
  ],
  "total": 42,
  "page": 1,
  "page_size": 20
}
```

### 发表评论

**请求**：
```
POST /api/v1/apps/{id}/reviews
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "title": "Great calculator app",     // 必填，最多100字符
  "content": "This is a very useful calculator with all the features I need.", // 必填，1-1000字符
  "rating": 5,                         // 必填，1-5分
  "app_version": "1.0.0"               // 必填，应用版本，最多50字符
}
```

**响应**：
```json
{
  "code": 200,
  "message": "评论发表成功",
  "data": {
    "id": 1,
    "application_id": 1,
    "app_name": "Calculator Pro",
    "user_id": 2,
    "username": "jane_user",
    "avatar": "https://example.com/user-avatar.jpg",
    "title": "Great calculator app",
    "content": "This is a very useful calculator with all the features I need.",
    "rating": 5,
    "app_version": "1.0.0",
    "like_count": 0,
    "dev_response": "",
    "status": "active",
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z"
  }
}
```

### 开发者回复评论

**请求**：
```
POST /api/v1/apps/{id}/reviews/{review_id}/respond
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "content": "Thank you for your feedback! We're glad you enjoy our app." // 必填，1-1000字符
}
```

**响应**：
```json
{
  "code": 200,
  "message": "回复成功",
  "data": {
    "id": 1,
    "application_id": 1,
    "app_name": "Calculator Pro",
    "user_id": 2,
    "username": "jane_user",
    "avatar": "https://example.com/user-avatar.jpg",
    "title": "Great calculator app",
    "content": "This is a very useful calculator with all the features I need.",
    "rating": 5,
    "app_version": "1.0.0",
    "like_count": 0,
    "dev_response": "Thank you for your feedback! We're glad you enjoy our app.",
    "status": "active",
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T13:00:00Z"
  }
}
```

### 点赞评论

**请求**：
```
POST /api/v1/apps/{id}/reviews/{review_id}/like
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "点赞成功",
  "data": null
}
```

### 取消点赞评论

**请求**：
```
POST /api/v1/apps/{id}/reviews/{review_id}/unlike
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "取消点赞成功",
  "data": null
}
```

## 统计模块

### 获取应用下载统计

**请求**：
```
GET /api/v1/stats/apps/{id}/downloads
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- 应用ID (path)
- `days`: 统计天数，默认30

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_downloads": 5280,
    "daily_stats": [
      {"date": "2023-09-01", "downloads": 120},
      {"date": "2023-09-02", "downloads": 135},
      {"date": "2023-09-03", "downloads": 150}
      // ...更多日期数据
    ],
    "device_stats": [
      {"device_type": "phone", "downloads": 4224},
      {"device_type": "tablet", "downloads": 1056}
    ]
  }
}
```

### 获取用户下载记录

**请求**：
```
GET /api/v1/stats/downloads
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "user_id": 2,
      "application_id": 1,
      "app_name": "Calculator Pro",
      "app_icon": "https://example.com/app-icon.jpg",
      "version_name": "1.0.0",
      "device_type": "phone",
      "device_model": "Mate 40 Pro",
      "device_os": "HarmonyOS 3.0.0",
      "status": "completed",
      "created_at": "2023-09-15T10:30:00Z"
    }
  ],
  "total": 15,
  "page": 1,
  "page_size": 20
}
```

### 记录应用下载

**请求**：
```
POST /api/v1/apps/{id}/versions/{version_id}/download
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "device_type": "phone",        // 设备类型
  "device_model": "Mate 40 Pro", // 设备型号
  "device_os": "HarmonyOS 3.0.0" // 设备操作系统版本
}
```

**响应**：
```json
{
  "code": 200,
  "message": "下载记录成功",
  "data": {
    "id": 1,
    "user_id": 2,
    "application_id": 1,
    "version_id": 1,
    "device_type": "phone",
    "device_model": "Mate 40 Pro",
    "device_os": "HarmonyOS 3.0.0",
    "status": "completed",
    "created_at": "2023-10-01T12:00:00Z"
  }
}
``` 


## 审核员模块

### 获取待审核应用

**请求**：
```
GET /api/v1/reviewer/apps/pending
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 3,
      "name": "Weather App",
      "package": "com.example.weather",
      "description": "A weather forecasting app for OpenHarmony.",
      "short_desc": "Weather forecasting",
      "icon": "https://example.com/weather-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "status": "pending",
      "min_open_harmony_os_ver": "3.0.0",
      "submitted_at": "2023-09-25T08:45:00Z"
    }
  ],
  "total": 5,
  "page": 1,
  "page_size": 20
}
```

### 审核应用

**请求**：
```
POST /api/v1/reviewer/apps/{id}/review
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "status": "approved",               // 必填，审核结果(approved/rejected)
  "reason": "应用符合商店发布规范"  // 拒绝时必填，拒绝理由
}
```

**响应**：
```json
{
  "code": 200,
  "message": "审核成功",
  "data": {
    "id": 3,
    "name": "Weather App",
    "package": "com.example.weather",
    "status": "approved",
    "reviewed_by": 5,
    "reviewed_at": "2023-10-01T12:00:00Z",
    "review_reason": "应用符合商店发布规范"
  }
}
```

## 运营人员模块

### 设置应用推荐状态

**请求**：
```
PUT /api/v1/operator/apps/{id}/featured
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "is_featured": true,        // 必填，是否推荐
  "featured_reason": "高质量应用，推荐给用户" // 可选，推荐理由
}
```

**响应**：
```json
{
  "code": 200,
  "message": "设置成功",
  "data": {
    "id": 1,
    "name": "Calculator Pro",
    "is_featured": true,
    "featured_at": "2023-10-01T12:00:00Z",
    "featured_by": 4,
    "featured_reason": "高质量应用，推荐给用户"
  }
}
```

### 设置应用编辑精选状态

**请求**：
```
PUT /api/v1/operator/apps/{id}/editor
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "is_editor": true,          // 必填，是否编辑精选
  "editor_comment": "编辑推荐，功能全面的计算器应用" // 可选，编辑评语
}
```

**响应**：
```json
{
  "code": 200,
  "message": "设置成功",
  "data": {
    "id": 1,
    "name": "Calculator Pro",
    "is_editor": true,
    "editor_at": "2023-10-01T12:00:00Z",
    "editor_by": 4,
    "editor_comment": "编辑推荐，功能全面的计算器应用"
  }
}
```

### 设置应用置顶状态

**请求**：
```
PUT /api/v1/operator/apps/{id}/top
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "is_top": true,            // 必填，是否置顶
  "top_order": 1,            // 可选，置顶排序，数字越小越靠前
  "top_reason": "精品应用，值得置顶推荐" // 可选，置顶理由
}
```

**响应**：
```json
{
  "code": 200,
  "message": "设置成功",
  "data": {
    "id": 1,
    "name": "Calculator Pro",
    "is_top": true,
    "top_order": 1,
    "top_at": "2023-10-01T12:00:00Z",
    "top_by": 4,
    "top_reason": "精品应用，值得置顶推荐"
  }
}
```

## 系统通用接口

### 健康检查

**请求**：
```
GET /api/v1/health
```

**响应**：
```json
{
  "code": 200,
  "message": "服务正常",
  "data": {
    "status": "ok",
    "time": "2023-10-01T12:00:00Z",
    "version": "1.0.0"
  }
}
```

## 分类管理模块

### 获取应用分类

**请求**：
```
GET /api/v1/categories
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "created_at": "2023-09-01T12:00:00Z",
      "updated_at": "2023-09-01T12:00:00Z",
      "deleted_at": null,
      "name": "工具",
      "description": "实用工具类应用",
      "icon": "https://example.com/category-tools.png",
      "sort_order": 1,
      "is_active": true,
      "parent_id": null
    },
    {
      "id": 2,
      "created_at": "2023-09-01T12:00:00Z",
      "updated_at": "2023-09-01T12:00:00Z",
      "deleted_at": null,
      "name": "社交",
      "description": "社交通讯类应用",
      "icon": "https://example.com/category-social.png",
      "sort_order": 2,
      "is_active": true,
      "parent_id": null
    },
    {
      "id": 3,
      "created_at": "2023-09-01T12:00:00Z",
      "updated_at": "2023-09-01T12:00:00Z",
      "deleted_at": null,
      "name": "游戏",
      "description": "游戏娱乐类应用",
      "icon": "https://example.com/category-games.png",
      "sort_order": 3,
      "is_active": true,
      "parent_id": null
    }
  ]
}
```

### 创建分类（管理员）

**请求**：
```
POST /api/v1/admin/categories
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "教育",                    // 必填，最多50字符
  "description": "教育学习类应用",    // 可选，最多1000字符
  "icon": "https://example.com/category-education.png", // 可选，图标URL
  "sort_order": 4,                 // 可选，排序权重，默认0
  "parent_id": null                // 可选，父分类ID，支持多级分类
}
```

**响应**：
```json
{
  "code": 201,
  "message": "创建成功",
  "data": {
    "id": 4,
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z",
    "deleted_at": null,
    "name": "教育",
    "description": "教育学习类应用",
    "icon": "https://example.com/category-education.png",
    "sort_order": 4,
    "is_active": true,
    "parent_id": null
  }
}
```

### 更新分类（管理员）

**请求**：
```
PUT /api/v1/admin/categories/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "教育学习",               // 可选，最多50字符
  "description": "教育学习类应用",   // 可选，最多1000字符
  "icon": "https://example.com/category-education-new.png", // 可选，图标URL
  "sort_order": 5,                // 可选，排序权重
  "is_active": true,              // 可选，是否启用
  "parent_id": null               // 可选，父分类ID
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 4,
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T13:00:00Z",
    "deleted_at": null,
    "name": "教育学习",
    "description": "教育学习类应用",
    "icon": "https://example.com/category-education-new.png",
    "sort_order": 5,
    "is_active": true,
    "parent_id": null
  }
}
```

### 删除分类（管理员）

**请求**：
```
DELETE /api/v1/admin/categories/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

### 获取标签列表

**请求**：
```
GET /api/v1/tags
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "计算器",
      "name_en": "calculator",
      "use_count": 15
    },
    {
      "id": 2,
      "name": "工具",
      "name_en": "tools",
      "use_count": 120
    },
    {
      "id": 3,
      "name": "效率",
      "name_en": "productivity",
      "use_count": 85
    }
  ]
}
```

## 用户退出登录

### 请求

```
POST /api/v1/users/logout
```

### 请求头

```
Content-Type: application/json
Authorization: Bearer {token}
```

### 响应

```
{
    "code": 200,
    "message": "登出成功",
    "data": null
}
```

## 开发者认证模块

### 提交开发者认证

**请求**：
```
POST /api/v1/developers/verify
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "developer_name": "张三",
  "company_name": "北京科技有限公司",
  "website": "https://example.com",
  "description": "专注于移动应用开发",
  "contact_email": "<EMAIL>",
  "contact_phone": "***********",
  "business_license": "https://example.com/license.jpg",
  "id_card_front": "https://example.com/id_front.jpg",
  "id_card_back": "https://example.com/id_back.jpg"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "认证申请提交成功",
  "data": null
}
```

### 获取开发者认证状态

**请求**：
```
GET /api/v1/developers/verify/status
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "status": "pending",
    "submitted_at": "2023-09-01T12:00:00Z",
    "reviewed_at": null,
    "reject_reason": null
  }
}
```

## 统计模块

### 获取用户下载记录

**请求**：
```
GET /api/v1/stats/downloads?page=1&page_size=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "application_id": 1,
      "app_version_id": 1,
      "version_name": "1.0.0",
      "downloaded_at": "2023-09-01T12:00:00Z",
      "device_type": "phone",
      "device_os": "HarmonyOS 4.0",
      "status": "success"
    }
  ],
  "total": 50,
  "page": 1,
  "page_size": 20
}
```

### 获取应用下载统计

**请求**：
```
GET /api/v1/stats/apps/{id}/downloads?days=30
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_downloads": 1500,
    "daily_stats": [
      {
        "date": "2023-09-30",
        "downloads": 45
      },
      {
        "date": "2023-09-29",
        "downloads": 38
      }
    ]
  }
}
```

## 管理员模块

### 获取待审核开发者列表

**请求**：
```
GET /api/v1/admin/developers/verify?page=1&page_size=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "user_id": 10,
      "username": "developer1",
      "developer_name": "张三",
      "company_name": "北京科技有限公司",
      "website": "https://example.com",
      "description": "专注于移动应用开发",
      "contact_email": "<EMAIL>",
      "contact_phone": "***********",
      "business_license": "https://example.com/license.jpg",
      "id_card_front": "https://example.com/id_front.jpg",
      "id_card_back": "https://example.com/id_back.jpg",
      "status": "pending",
      "submitted_at": "2023-09-01T12:00:00Z"
    }
  ],
  "total": 5,
  "page": 1,
  "page_size": 20
}
```

### 审核开发者认证

**请求**：
```
POST /api/v1/admin/developers/{id}/verify
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "action": "approve",  // approve 或 reject
  "reason": "审核通过"    // 审核理由，拒绝时必填
}
```

**响应**：
```json
{
  "code": 200,
  "message": "审核完成",
  "data": null
}
```

### 获取用户列表

**请求**：
```
GET /api/v1/admin/users?page=1&page_size=20&role=&status=&keyword=
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "username": "user1",
      "email": "<EMAIL>",
      "phone": "***********",
      "role": "user",
      "status": "active",
      "is_developer": false,
      "created_at": "2023-09-01T12:00:00Z",
      "last_login_at": "2023-09-30T12:00:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "page_size": 20
}
```

### 创建用户

**请求**：
```
POST /api/v1/admin/users
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "phone": "***********",
  "password": "password123",
  "role": "user",
  "status": "active"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "用户创建成功",
  "data": {
    "id": 101,
    "username": "newuser",
    "email": "<EMAIL>",
    "role": "user",
    "status": "active"
  }
}
```

### 更新用户角色

**请求**：
```
PUT /api/v1/admin/users/{id}/role
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "role": "admin"  // user, admin, operator, reviewer
}
```

**响应**：
```json
{
  "code": 200,
  "message": "角色更新成功",
  "data": null
}
```

### 更新用户状态

**请求**：
```
PUT /api/v1/admin/users/{id}/status
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "status": "banned"  // active, inactive, banned
}
```

**响应**：
```json
{
  "code": 200,
  "message": "状态更新成功",
  "data": null
}
```

## 审核员模块

### 获取待审核应用列表

**请求**：
```
GET /api/v1/reviewer/apps/pending?page=1&page_size=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "计算器应用",
      "package_name": "com.example.calculator",
      "developer_name": "张三",
      "version": "1.0.0",
      "status": "pending",
      "submitted_at": "2023-09-01T12:00:00Z",
      "description": "一个简单的计算器应用"
    }
  ],
  "total": 10,
  "page": 1,
  "page_size": 20
}
```

### 审核应用

**请求**：
```
POST /api/v1/reviewer/apps/{id}/review
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "action": "approve",  // approve 或 reject
  "reason": "应用功能完善，符合上架要求"  // 审核理由
}
```

**响应**：
```json
{
  "code": 200,
  "message": "审核完成",
  "data": null
}
```

## 仪表盘模块

### 分析页 - 获取摘要数据

**请求**：
```
GET /api/v1/dashboard/analytics/summary
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_apps": 1250,
    "total_users": 8500,
    "total_downloads": 125000,
    "total_reviews": 3200,
    "growth_rate": {
      "apps": 12.5,
      "users": 8.3,
      "downloads": 15.2,
      "reviews": 6.8
    }
  }
}
```

### 分析页 - 获取趋势数据

**请求**：
```
GET /api/v1/dashboard/analytics/trend?period=30d&metric=downloads
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "period": "30d",
    "metric": "downloads",
    "data": [
      {
        "date": "2023-09-01",
        "value": 1250
      },
      {
        "date": "2023-09-02",
        "value": 1380
      }
    ]
  }
}
```

### 分析页 - 获取分类统计

**请求**：
```
GET /api/v1/dashboard/analytics/categories
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "category_name": "工具",
      "app_count": 320,
      "download_count": 45000,
      "percentage": 25.6
    },
    {
      "category_name": "游戏",
      "app_count": 280,
      "download_count": 38000,
      "percentage": 22.4
    }
  ]
}
```

### 分析页 - 获取热门应用

**请求**：
```
GET /api/v1/dashboard/analytics/popular-apps?limit=10
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "计算器Pro",
      "package_name": "com.example.calculator",
      "download_count": 15000,
      "rating": 4.8,
      "category": "工具"
    }
  ]
}
```

### 监控页 - 获取监控数据

**请求**：
```
GET /api/v1/dashboard/monitoring/data
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "server_status": "healthy",
    "cpu_usage": 45.2,
    "memory_usage": 68.5,
    "disk_usage": 32.1,
    "active_users": 1250,
    "api_response_time": 120,
    "error_rate": 0.02
  }
}
```

### 监控页 - 获取系统日志

**请求**：
```
GET /api/v1/dashboard/monitoring/logs?level=error&page=1&page_size=50
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "level": "error",
      "message": "数据库连接超时",
      "source": "database",
      "timestamp": "2023-09-30T12:00:00Z",
      "details": {}
    }
  ],
  "total": 25,
  "page": 1,
  "page_size": 50
}
```

### 监控页 - 获取告警事件

**请求**：
```
GET /api/v1/dashboard/monitoring/alerts?status=active&page=1&page_size=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "title": "CPU使用率过高",
      "description": "服务器CPU使用率超过80%",
      "level": "warning",
      "status": "active",
      "created_at": "2023-09-30T12:00:00Z",
      "resolved_at": null
    }
  ],
  "total": 3,
  "page": 1,
  "page_size": 20
}
```

### 工作台 - 获取摘要数据

**请求**：
```
GET /api/v1/dashboard/workbench/summary
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "pending_tasks": 5,
    "completed_tasks": 12,
    "pending_reviews": 8,
    "new_messages": 3,
    "recent_activities": [
      {
        "type": "app_review",
        "description": "审核了应用《计算器Pro》",
        "timestamp": "2023-09-30T12:00:00Z"
      }
    ]
  }
}
```

### 工作台 - 获取最近活动

**请求**：
```
GET /api/v1/dashboard/workbench/activities?page=1&page_size=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "type": "app_review",
      "title": "应用审核",
      "description": "审核了应用《计算器Pro》",
      "user": "管理员",
      "timestamp": "2023-09-30T12:00:00Z"
    }
  ],
  "total": 50,
  "page": 1,
  "page_size": 20
}
```

### 工作台 - 获取任务列表

**请求**：
```
GET /api/v1/dashboard/workbench/tasks?status=pending&page=1&page_size=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "title": "审核新应用",
      "description": "审核开发者提交的新应用",
      "priority": "high",
      "status": "pending",
      "assignee": "审核员A",
      "due_date": "2023-10-01T18:00:00Z",
      "created_at": "2023-09-30T09:00:00Z"
    }
  ],
  "total": 15,
  "page": 1,
  "page_size": 20
}
```

### 工作台 - 创建任务

**请求**：
```
POST /api/v1/dashboard/workbench/tasks
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "title": "优化数据库性能",
  "description": "分析并优化数据库查询性能",
  "priority": "medium",
  "assignee": "开发工程师B",
  "due_date": "2023-10-05T18:00:00Z"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "任务创建成功",
  "data": {
    "id": 16,
    "title": "优化数据库性能",
    "status": "pending",
    "created_at": "2023-09-30T14:00:00Z"
  }
}
```

### 工作台 - 更新任务

**请求**：
```
PUT /api/v1/dashboard/workbench/tasks/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "title": "优化数据库性能（已完成50%）",
  "status": "in_progress",
  "priority": "high"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "任务更新成功",
  "data": null
}
```

### 工作台 - 删除任务

**请求**：
```
DELETE /api/v1/dashboard/workbench/tasks/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "任务删除成功",
  "data": null
}
```

## 搜索模块

### 搜索应用

**请求**：
```
GET /api/v1/search/apps
```

**参数**：
- `keyword`: 搜索关键词
- `category`: 分类ID
- `tags`: 标签（多个用逗号分隔）
- `min_rating`: 最低评分
- `max_rating`: 最高评分
- `sort`: 排序方式（relevance/rating/downloads/newest）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "搜索成功",
  "data": {
    "apps": [
      {
        "id": 1,
        "name": "Calculator Pro",
        "package_name": "com.example.calculator",
        "description": "A professional calculator app",
        "icon": "https://example.com/app-icon.jpg",
        "category_name": "Tools",
        "developer_name": "John Developer",
        "version": "1.0.0",
        "rating": 4.5,
        "download_count": 1000,
        "tags": ["calculator", "math", "tools"]
      }
    ],
    "total": 50,
    "page": 1,
    "page_size": 20
  }
}
```

### 获取搜索建议

**请求**：
```
GET /api/v1/search/suggestions?q=calc
```

**参数**：
- `q`: 搜索关键词（必填）

**响应**：
```json
{
  "code": 200,
  "message": "获取搜索建议成功",
  "data": {
    "suggestions": ["calculator", "calendar", "call recorder"]
  }
}
```

### 搜索评论

**请求**：
```
GET /api/v1/search/reviews
```

**参数**：
- `keyword`: 搜索关键词
- `app_id`: 应用ID
- `user_id`: 用户ID
- `min_rating`: 最低评分
- `max_rating`: 最高评分
- `start_date`: 开始日期（YYYY-MM-DD）
- `end_date`: 结束日期（YYYY-MM-DD）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "搜索成功",
  "data": {
    "reviews": [
      {
        "id": 1,
        "application_id": 1,
        "app_name": "Calculator Pro",
        "user_id": 2,
        "username": "jane_user",
        "title": "Great calculator app",
        "content": "This is a very useful calculator",
        "rating": 5,
        "created_at": "2023-09-20T00:00:00Z"
      }
    ],
    "total": 25,
    "page": 1,
    "page_size": 20
  }
}
```

### 搜索标签

**请求**：
```
GET /api/v1/search/tags
```

**参数**：
- `keyword`: 搜索关键词
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "搜索成功",
  "data": {
    "tags": [
      {
        "id": 1,
        "name": "calculator",
        "description": "计算器相关应用",
        "app_count": 15,
        "created_at": "2023-09-01T00:00:00Z"
      }
    ],
    "total": 10,
    "page": 1,
    "page_size": 20
  }
}
```

### 标签建议

**请求**：
```
GET /api/v1/search/tags/suggest?prefix=calc&limit=10
```

**参数**：
- `prefix`: 搜索前缀（必填）
- `limit`: 返回数量限制，默认10

**响应**：
```json
{
  "code": 200,
  "message": "获取建议成功",
  "data": {
    "suggestions": ["calculator", "calendar"]
  }
}
```

## 管理员搜索接口

### 搜索用户（管理员）

**请求**：
```
GET /api/v1/admin/search/users
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `keyword`: 搜索关键词（用户名、邮箱、开发者信息）
- `role`: 用户角色（user/developer/admin/operator/reviewer）
- `status`: 用户状态（active/inactive/banned）
- `is_developer`: 是否为开发者（true/false）
- `verify_status`: 开发者认证状态（pending/approved/rejected）
- `start_date`: 注册开始日期（YYYY-MM-DD）
- `end_date`: 注册结束日期（YYYY-MM-DD）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "搜索成功",
  "data": {
    "users": [
      {
        "id": 1,
        "username": "johndoe",
        "email": "<EMAIL>",
        "phone": "***********",
        "role": "developer",
        "status": "active",
        "is_developer": true,
        "developer_name": "John Developer",
        "company_name": "John Inc",
        "verify_status": "approved",
        "created_at": "2023-09-01T00:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20
  }
}
```

### 获取标签统计（管理员）

**请求**：
```
GET /api/v1/admin/search/tags/stats
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "tag_name": "calculator",
      "app_count": 15,
      "total_downloads": 50000,
      "avg_rating": 4.2
    },
    {
      "tag_name": "game",
      "app_count": 120,
      "total_downloads": 2000000,
      "avg_rating": 4.5
    }
  ]
}
```

### 初始化搜索索引（管理员）

**请求**：
```
POST /api/v1/admin/search/init
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "搜索索引初始化成功",
  "data": null
}
```

### 同步搜索索引（管理员）

**请求**：
```
POST /api/v1/admin/search/sync
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "搜索索引同步成功",
  "data": null
}
```

### 初始化所有搜索索引（管理员）

**请求**：
```
POST /api/v1/admin/search/initialize-all
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "所有搜索索引初始化成功",
  "data": null
}
```

## 通知模块

### 获取通知列表

**请求**：
```
GET /api/v1/notifications
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`：页码，默认1
- `page_size`：每页数量，默认20
- `type`：通知类型过滤
- `read`：是否已读（true/false）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "notifications": [
      {
        "id": 1,
        "user_id": 123,
        "type": "app_review",
        "title": "应用审核通知",
        "content": "您的应用《计算器Pro》已通过审核",
        "data": {
          "app_id": 1,
          "app_name": "计算器Pro"
        },
        "read": false,
        "created_at": "2023-10-01T12:00:00Z",
        "read_at": null
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 50,
      "total_pages": 3
    }
  }
}
```

### 标记通知为已读

**请求**：
```
POST /api/v1/notifications/read
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "notification_ids": [1, 2, 3]  // 通知ID数组
}
```

**响应**：
```json
{
  "code": 200,
  "message": "标记成功",
  "data": null
}
```

### 标记所有通知为已读

**请求**：
```
POST /api/v1/notifications/read-all
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "标记成功",
  "data": null
}
```

### 获取未读通知数量

**请求**：
```
GET /api/v1/notifications/unread-count
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "unread_count": 5
  }
}
```

### 获取通知设置

**请求**：
```
GET /api/v1/notifications/settings
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "email_notifications": true,
    "push_notifications": true,
    "app_review_notifications": true,
    "comment_notifications": true,
    "system_notifications": true
  }
}
```

### 更新通知设置

**请求**：
```
PUT /api/v1/notifications/settings
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "email_notifications": false,
  "push_notifications": true,
  "app_review_notifications": true,
  "comment_notifications": false,
  "system_notifications": true
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

### 删除通知

**请求**：
```
DELETE /api/v1/notifications/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

## 消息队列模块

### 发送通知消息

**请求**：
```
POST /api/v1/messages/notification
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "user_id": 123,
  "type": "app_review",
  "title": "应用审核通知",
  "content": "您的应用已通过审核",
  "data": {
    "app_id": 1,
    "app_name": "计算器Pro"
  }
}
```

**响应**：
```json
{
  "code": 200,
  "message": "消息发送成功",
  "data": null
}
```

### 发送邮件

**请求**：
```
POST /api/v1/messages/email
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "to": "<EMAIL>",
  "subject": "应用审核通知",
  "content": "您的应用已通过审核",
  "template": "app_review"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "邮件发送成功",
  "data": null
}
```

### 记录用户活动

**请求**：
```
POST /api/v1/messages/activity
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "user_id": 123,
  "action": "app_create",
  "description": "创建了新应用",
  "metadata": {
    "app_id": 1,
    "app_name": "计算器Pro"
  }
}
```

**响应**：
```json
{
  "code": 200,
  "message": "活动记录成功",
  "data": null
}
```

### 触发应用审核

**请求**：
```
POST /api/v1/messages/app-review/{app_id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "审核流程已触发",
  "data": null
}
```

### 获取队列状态

**请求**：
```
GET /api/v1/messages/status
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "queues": [
      {
        "name": "notification_queue",
        "message_count": 10,
        "consumer_count": 2
      },
      {
        "name": "email_queue",
        "message_count": 5,
        "consumer_count": 1
      }
    ]
  }
}
```

## 地理位置模块

### 获取国家列表

**请求**：
```
GET /api/v1/geographic/country
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "code": "CN",
      "name": "中国",
      "name_en": "China"
    },
    {
      "code": "US",
      "name": "美国",
      "name_en": "United States"
    }
  ]
}
```

### 获取省份列表

**请求**：
```
GET /api/v1/geographic/province?country=CN
```

**参数**：
- `country`：国家代码（可选）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "code": "110000",
      "name": "北京市",
      "country_code": "CN"
    },
    {
      "code": "120000",
      "name": "天津市",
      "country_code": "CN"
    }
  ]
}
```

### 获取城市列表

**请求**：
```
GET /api/v1/geographic/city/{province}
```

**参数**：
- `province`：省份代码（路径参数）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "code": "110100",
      "name": "北京市",
      "province_code": "110000"
    }
  ]
}
```

### 获取区县列表

**请求**：
```
GET /api/v1/geographic/district/{city}
```

**参数**：
- `city`：城市代码（路径参数）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "code": "110101",
      "name": "东城区",
      "city_code": "110100"
    },
    {
      "code": "110102",
      "name": "西城区",
      "city_code": "110100"
    }
  ]
}
```

### 获取街道列表

**请求**：
```
GET /api/v1/geographic/street/{district}
```

**参数**：
- `district`：区县代码（路径参数）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "code": "110101001",
      "name": "东华门街道",
      "district_code": "110101"
    }
  ]
}
```

### 按级别获取地理信息

**请求**：
```
GET /api/v1/geographic/level?level=province&parent_code=CN
```

**参数**：
- `level`：级别（country/province/city/district/street）
- `parent_code`：父级代码（可选）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "code": "110000",
      "name": "北京市",
      "parent_code": "CN",
      "level": "province"
    }
  ]
}
```

## OpenHarmony版本管理模块

### 创建OpenHarmony版本（管理员）

**请求**：
```
POST /api/v1/admin/harmony-versions
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "version_name": "OpenHarmony 4.0",
  "version_code": "4.0.0",
  "api_level": 10,
  "release_date": "2023-10-01",
  "description": "OpenHarmony 4.0 正式版",
  "is_stable": true,
  "min_sdk_version": 8,
  "target_sdk_version": 10
}
```

**响应**：
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "version_name": "OpenHarmony 4.0",
    "version_code": "4.0.0",
    "api_level": 10,
    "release_date": "2023-10-01T00:00:00Z",
    "description": "OpenHarmony 4.0 正式版",
    "is_stable": true,
    "min_sdk_version": 8,
    "target_sdk_version": 10,
    "created_at": "2023-10-01T12:00:00Z"
  }
}
```

### 获取OpenHarmony版本列表（管理员）

**请求**：
```
GET /api/v1/admin/harmony-versions
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`：页码，默认1
- `page_size`：每页数量，默认20
- `is_stable`：是否稳定版（true/false）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "versions": [
      {
        "id": 1,
        "version_name": "OpenHarmony 4.0",
        "version_code": "4.0.0",
        "api_level": 10,
        "release_date": "2023-10-01T00:00:00Z",
        "description": "OpenHarmony 4.0 正式版",
        "is_stable": true,
        "min_sdk_version": 8,
        "target_sdk_version": 10,
        "created_at": "2023-10-01T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 10,
      "total_pages": 1
    }
  }
}
```

### 获取OpenHarmony版本详情（管理员）

**请求**：
```
GET /api/v1/admin/harmony-versions/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "version_name": "OpenHarmony 4.0",
    "version_code": "4.0.0",
    "api_level": 10,
    "release_date": "2023-10-01T00:00:00Z",
    "description": "OpenHarmony 4.0 正式版",
    "is_stable": true,
    "min_sdk_version": 8,
    "target_sdk_version": 10,
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z"
  }
}
```

### 更新OpenHarmony版本（管理员）

**请求**：
```
PUT /api/v1/admin/harmony-versions/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "version_name": "OpenHarmony 4.0.1",
  "description": "OpenHarmony 4.0.1 修复版",
  "is_stable": true
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

### 删除OpenHarmony版本（管理员）

**请求**：
```
DELETE /api/v1/admin/harmony-versions/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

## 系统接口

### 健康检查

**请求**：
```
GET /api/v1/health
```

**响应**：
```json
{
  "status": "ok",
  "time": "2023-09-30T12:00:00Z"
}
```

### 获取配置信息

**请求**：
```
GET /config
```

**响应**：
```json
{
  "server_port": "8080",
  "server_mode": "release",
  "db_host": "localhost",
  "redis_addr": "localhost:6379",
  "storage_type": "local"
}
```