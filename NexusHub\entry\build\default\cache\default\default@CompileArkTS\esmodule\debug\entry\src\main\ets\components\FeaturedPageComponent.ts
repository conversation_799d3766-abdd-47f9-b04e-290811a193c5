if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface FeaturedPageComponent_Params {
    isLoading?: boolean;
    errorMessage?: string;
    deviceUtils?;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import hilog from "@ohos:hilog";
export class FeaturedPageComponent extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__errorMessage = new ObservedPropertySimplePU('', this, "errorMessage");
        this.deviceUtils = DeviceUtils.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: FeaturedPageComponent_Params) {
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.errorMessage !== undefined) {
            this.errorMessage = params.errorMessage;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
    }
    updateStateVars(params: FeaturedPageComponent_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__errorMessage.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__isLoading.aboutToBeDeleted();
        this.__errorMessage.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __errorMessage: ObservedPropertySimplePU<string>;
    get errorMessage() {
        return this.__errorMessage.get();
    }
    set errorMessage(newValue: string) {
        this.__errorMessage.set(newValue);
    }
    private deviceUtils;
    aboutToAppear() {
        hilog.info(0x0000, 'FeaturedPageComponent', '精选页面组件初始化');
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 24 });
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题区域
            Text.create('精选集合');
            // 标题区域
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.EXTRA_LARGE));
            // 标题区域
            Text.fontWeight(FontWeight.Bold);
            // 标题区域
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 标题区域
            Text.alignSelf(ItemAlign.Start);
            // 标题区域
            Text.margin({ left: 16, right: 16, top: 16 });
        }, Text);
        // 标题区域
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 内容区域
            Column.create({ space: 16 });
            // 内容区域
            Column.width('100%');
            // 内容区域
            Column.layoutWeight(1);
            // 内容区域
            Column.justifyContent(FlexAlign.Center);
            // 内容区域
            Column.padding(24);
            // 内容区域
            Column.backgroundColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 内容区域
            Column.borderRadius(16);
            // 内容区域
            Column.margin({ left: 16, right: 16, bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('发现精彩应用集合');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM));
            Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('这里将展示各种主题的应用集合，帮助您快速找到感兴趣的应用。');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.textAlign(TextAlign.Center);
            Text.margin({ left: 32, right: 32 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('即将推出');
            Button.type(ButtonType.Normal);
            Button.borderRadius(8);
            Button.backgroundColor({ "id": 125829510, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Button.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Button.enabled(false);
            Button.margin({ top: 16 });
        }, Button);
        Button.pop();
        // 内容区域
        Column.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
