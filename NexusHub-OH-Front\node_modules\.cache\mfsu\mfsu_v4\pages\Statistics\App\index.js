"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { PageContainer } from "@ant-design/pro-components";
import { Card, Row, Col, Statistic, Table, Button, Radio, Tabs } from "antd";
import { Line, Column, Pie } from "@ant-design/plots";
import { DownloadOutlined, ReloadOutlined } from "@ant-design/icons";
import React, { useState } from "react";
import { useAccess, useModel, request } from "@umijs/max";
import PermissionWrapper from "@/components/PermissionWrapper";
import { canViewStatistics } from "@/utils/permission";
const fetchAppStatistics = async (params) => {
  console.log("Fetching app statistics with params:", params);
  try {
    const response = await request("/stats/apps/overall", {
      method: "GET",
      params: {
        time_range: params.timeRange,
        developer_id: params.developerId
      }
    });
    console.log("App statistics response:", response);
    console.log("Response code:", response.code);
    console.log("Response data:", response.data);
    if (response.code !== 200) {
      throw new Error(response.message || "\u83B7\u53D6\u7EDF\u8BA1\u6570\u636E\u5931\u8D25");
    }
    const statsData = response.data;
    console.log("Stats data from API:", statsData);
    console.log("Status stats field exists:", "status_stats" in statsData);
    console.log("Status stats value:", statsData.status_stats);
    console.log("Status stats type:", typeof statsData.status_stats);
    console.log("Status stats length:", statsData.status_stats?.length);
    const downloadData = statsData.download_trends && statsData.download_trends.length > 0 ? statsData.download_trends.map((trend) => ({
      date: trend.date,
      downloads: trend.downloads
    })) : [
      { date: "2024-01", downloads: 1250 },
      { date: "2024-02", downloads: 1380 },
      { date: "2024-03", downloads: 1500 },
      { date: "2024-04", downloads: 1650 },
      { date: "2024-05", downloads: 1800 },
      { date: "2024-06", downloads: 2100 }
    ];
    const categoryData = statsData.category_stats?.map((cat) => ({
      category: cat.category,
      count: cat.count,
      percentage: statsData.total_apps > 0 ? (cat.count / statsData.total_apps * 100).toFixed(1) : "0"
    })) || [];
    const statusMap = {
      "approved": "\u5DF2\u53D1\u5E03",
      "pending": "\u5BA1\u6838\u4E2D",
      "removed": "\u5DF2\u4E0B\u67B6",
      "draft": "\u8349\u7A3F",
      "rejected": "\u5DF2\u62D2\u7EDD"
    };
    const statusData = statsData.status_stats && statsData.status_stats.length > 0 ? statsData.status_stats.map((status) => ({
      status: statusMap[status.status] || status.status || "\u672A\u77E5",
      count: status.count,
      percentage: statsData.total_apps > 0 ? (status.count / statsData.total_apps * 100).toFixed(1) : "0"
    })) : [
      { status: "\u5DF2\u53D1\u5E03", count: 65, percentage: "56.0" },
      { status: "\u5BA1\u6838\u4E2D", count: 28, percentage: "24.1" },
      { status: "\u5DF2\u4E0B\u67B6", count: 15, percentage: "12.9" },
      { status: "\u8349\u7A3F", count: 8, percentage: "6.9" }
    ];
    const topApps = statsData.top_apps?.map((app) => ({
      id: app.id,
      name: app.name,
      icon: app.icon,
      downloads: app.download_count,
      rating: app.rating,
      category: app.category
    })) || [];
    const result = {
      totalApps: statsData.total_apps || 0,
      totalDownloads: statsData.total_downloads || 0,
      activeUsers: Math.floor((statsData.total_downloads || 0) * 0.3),
      // 估算活跃用户数
      averageRating: Number((statsData.average_rating || 0).toFixed(1)),
      downloadData,
      categoryData,
      statusData,
      topApps
    };
    console.log("Final processed data:", result);
    console.log("About to return result:", result);
    return result;
  } catch (error) {
    console.error("\u83B7\u53D6\u5E94\u7528\u7EDF\u8BA1\u5931\u8D25:", error);
    const mockData = getMockData();
    console.log("Returning mock data:", mockData);
    return mockData;
  }
};
const getMockData = () => {
  const downloadData = [
    { date: "2023-01", downloads: 1250 },
    { date: "2023-02", downloads: 1380 },
    { date: "2023-03", downloads: 1500 },
    { date: "2023-04", downloads: 1650 },
    { date: "2023-05", downloads: 1800 },
    { date: "2023-06", downloads: 2100 },
    { date: "2023-07", downloads: 2300 },
    { date: "2023-08", downloads: 2450 },
    { date: "2023-09", downloads: 2600 },
    { date: "2023-10", downloads: 2750 },
    { date: "2023-11", downloads: 2900 },
    { date: "2023-12", downloads: 3200 }
  ];
  const categoryData = [
    { category: "\u6E38\u620F", count: 3500 },
    { category: "\u5DE5\u5177", count: 2800 },
    { category: "\u6559\u80B2", count: 1900 },
    { category: "\u793E\u4EA4", count: 1600 },
    { category: "\u97F3\u4E50", count: 1200 },
    { category: "\u5176\u4ED6", count: 800 }
  ];
  const statusData = [
    { status: "\u5DF2\u53D1\u5E03", count: 65 },
    { status: "\u5BA1\u6838\u4E2D", count: 28 },
    { status: "\u5DF2\u4E0B\u67B6", count: 15 },
    { status: "\u8349\u7A3F", count: 8 }
  ];
  const topApps = [
    { id: 1, name: "\u8D85\u7EA7\u5DE5\u5177\u7BB1", category: "\u5DE5\u5177", downloads: 125e3, rating: 4.8 },
    { id: 2, name: "\u97F3\u4E50\u64AD\u653E\u5668Pro", category: "\u97F3\u4E50", downloads: 98e3, rating: 4.7 },
    { id: 3, name: "\u5B66\u4E60\u52A9\u624B", category: "\u6559\u80B2", downloads: 87e3, rating: 4.6 },
    { id: 4, name: "\u4F11\u95F2\u5C0F\u6E38\u620F", category: "\u6E38\u620F", downloads: 76e3, rating: 4.5 },
    { id: 5, name: "\u793E\u4EA4\u804A\u5929", category: "\u793E\u4EA4", downloads: 65e3, rating: 4.4 },
    { id: 6, name: "\u5065\u5EB7\u8FFD\u8E2A", category: "\u5065\u5EB7", downloads: 54e3, rating: 4.3 },
    { id: 7, name: "\u9605\u8BFB\u5668", category: "\u5DE5\u5177", downloads: 43e3, rating: 4.2 },
    { id: 8, name: "\u89C6\u9891\u7F16\u8F91", category: "\u5DE5\u5177", downloads: 32e3, rating: 4.1 },
    { id: 9, name: "\u5929\u6C14\u9884\u62A5", category: "\u5DE5\u5177", downloads: 21e3, rating: 4 },
    { id: 10, name: "\u8BB0\u4E8B\u672C", category: "\u5DE5\u5177", downloads: 1e4, rating: 3.9 }
  ];
  return {
    totalApps: 11580,
    totalDownloads: 158e5,
    activeUsers: 32e5,
    averageRating: 4.2,
    downloadData,
    categoryData,
    statusData,
    topApps
  };
};
const AppStatistics = () => {
  const access = useAccess();
  const { initialState } = useModel("@@initialState");
  const currentUser = initialState?.currentUser;
  const [timeRange, setTimeRange] = useState("year");
  const [activeTab, setActiveTab] = useState("overview");
  console.log("AppStatistics component rendered");
  console.log("Current user:", currentUser);
  console.log("Time range:", timeRange);
  const canViewAllStats = canViewStatistics(currentUser?.role, "all");
  const canViewOwnStats = canViewStatistics(currentUser?.role, "own");
  console.log("Can view all stats:", canViewAllStats);
  console.log("Can view own stats:", canViewOwnStats);
  const [data, setData] = useState(void 0);
  const [loading, setLoading] = useState(false);
  const fetchData = async () => {
    if (!currentUser) return;
    setLoading(true);
    try {
      console.log("Manually fetching data with params:", {
        timeRange,
        developerId: currentUser?.role === "developer" ? currentUser?.id : void 0
      });
      const result = await fetchAppStatistics({
        timeRange,
        developerId: currentUser?.role === "developer" ? currentUser?.id : void 0
      });
      console.log("Successfully fetched data:", result);
      setData(result);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };
  React.useEffect(() => {
    fetchData();
  }, [timeRange, currentUser]);
  const refresh = () => {
    fetchData();
  };
  console.log("Current data state:", data);
  console.log("Loading state:", loading);
  const downloadConfig = {
    data: data?.downloadData || [],
    xField: "date",
    yField: "downloads",
    smooth: true,
    point: {
      size: 5,
      shape: "diamond"
    },
    label: {},
    color: "#1890ff"
  };
  const categoryConfig = {
    data: data?.categoryData || [],
    xField: "category",
    yField: "count",
    label: {
      position: "top",
      style: {
        fill: "#000000",
        opacity: 0.8
      }
    },
    color: "#1890ff"
  };
  console.log("Component data state:", data);
  console.log("StatusData in component:", data?.statusData);
  const statusConfig = {
    data: data?.statusData || [],
    angleField: "count",
    colorField: "status",
    radius: 0.8,
    label: {
      content: (item) => {
        const status = item.status || "\u672A\u77E5";
        const count = item.count || 0;
        const percentage = item.percentage || "0.0";
        return `${status}: ${count}\u4E2A (${percentage}%)`;
      }
    },
    interactions: [{ type: "element-active" }],
    legend: {
      position: "top"
    }
  };
  const columns = [
    {
      title: "\u6392\u540D",
      dataIndex: "id",
      key: "id"
    },
    {
      title: "\u5E94\u7528\u540D\u79F0",
      dataIndex: "name",
      key: "name"
    },
    {
      title: "\u5206\u7C7B",
      dataIndex: "category",
      key: "category"
    },
    {
      title: "\u4E0B\u8F7D\u91CF",
      dataIndex: "downloads",
      key: "downloads",
      sorter: (a, b) => a.downloads - b.downloads,
      render: (downloads) => `${(downloads / 1e3).toFixed(1)}k`
    },
    {
      title: "\u8BC4\u5206",
      dataIndex: "rating",
      key: "rating",
      sorter: (a, b) => a.rating - b.rating
    }
  ];
  return /* @__PURE__ */ jsx(PermissionWrapper, { permission: "canViewStatistics", children: /* @__PURE__ */ jsxs(PageContainer, { children: [
    /* @__PURE__ */ jsxs("div", { style: { marginBottom: 16 }, children: [
      /* @__PURE__ */ jsxs(
        Radio.Group,
        {
          value: timeRange,
          onChange: (e) => setTimeRange(e.target.value),
          style: { marginRight: 16 },
          children: [
            /* @__PURE__ */ jsx(Radio.Button, { value: "week", children: "\u6700\u8FD1\u4E00\u5468" }),
            /* @__PURE__ */ jsx(Radio.Button, { value: "month", children: "\u6700\u8FD1\u4E00\u4E2A\u6708" }),
            /* @__PURE__ */ jsx(Radio.Button, { value: "quarter", children: "\u6700\u8FD1\u4E00\u5B63" }),
            /* @__PURE__ */ jsx(Radio.Button, { value: "year", children: "\u6700\u8FD1\u4E00\u5E74" })
          ]
        }
      ),
      /* @__PURE__ */ jsx(Button, { icon: /* @__PURE__ */ jsx(ReloadOutlined, {}), onClick: refresh, children: "\u5237\u65B0" }),
      canViewAllStats && /* @__PURE__ */ jsx(Button, { icon: /* @__PURE__ */ jsx(DownloadOutlined, {}), style: { marginLeft: 8 }, children: "\u5BFC\u51FA\u62A5\u544A" })
    ] }),
    /* @__PURE__ */ jsx(
      Tabs,
      {
        activeKey: activeTab,
        onChange: setActiveTab,
        items: [
          {
            key: "overview",
            label: "\u6982\u89C8\u7EDF\u8BA1",
            children: /* @__PURE__ */ jsxs(Fragment, { children: [
              /* @__PURE__ */ jsxs(Row, { gutter: 16, style: { marginBottom: 16 }, children: [
                /* @__PURE__ */ jsx(Col, { span: 6, children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx(
                  Statistic,
                  {
                    title: canViewAllStats ? "\u603B\u5E94\u7528\u6570" : "\u6211\u7684\u5E94\u7528\u6570",
                    value: data?.totalApps || 0,
                    suffix: "\u4E2A"
                  }
                ) }) }),
                /* @__PURE__ */ jsx(Col, { span: 6, children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx(
                  Statistic,
                  {
                    title: "\u603B\u4E0B\u8F7D\u91CF",
                    value: data?.totalDownloads || 0,
                    suffix: "\u6B21"
                  }
                ) }) }),
                /* @__PURE__ */ jsx(Col, { span: 6, children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx(
                  Statistic,
                  {
                    title: "\u6D3B\u8DC3\u7528\u6237",
                    value: data?.activeUsers || 0,
                    suffix: "\u4EBA"
                  }
                ) }) }),
                /* @__PURE__ */ jsx(Col, { span: 6, children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx(
                  Statistic,
                  {
                    title: "\u5E73\u5747\u8BC4\u5206",
                    value: data?.averageRating || 0,
                    precision: 1,
                    suffix: "\u5206"
                  }
                ) }) })
              ] }),
              /* @__PURE__ */ jsx(Card, { title: "\u4E0B\u8F7D\u8D8B\u52BF", style: { marginBottom: 16 }, children: /* @__PURE__ */ jsx(Line, { ...downloadConfig, data: data?.downloadData || [], loading }) }),
              /* @__PURE__ */ jsxs(Row, { gutter: 16, children: [
                /* @__PURE__ */ jsx(Col, { span: 12, children: /* @__PURE__ */ jsx(Card, { title: "\u5206\u7C7B\u7EDF\u8BA1", children: /* @__PURE__ */ jsx(Column, { ...categoryConfig, data: data?.categoryData || [], loading }) }) }),
                /* @__PURE__ */ jsx(Col, { span: 12, children: /* @__PURE__ */ jsx(Card, { title: "\u5E94\u7528\u72B6\u6001\u7EDF\u8BA1", children: /* @__PURE__ */ jsx(Pie, { ...statusConfig, data: data?.statusData || [], loading }) }) })
              ] })
            ] })
          },
          {
            key: "ranking",
            label: canViewAllStats ? "\u5E94\u7528\u6392\u884C" : "\u6211\u7684\u5E94\u7528\u6392\u884C",
            children: /* @__PURE__ */ jsx(Card, { title: canViewAllStats ? "\u70ED\u95E8\u5E94\u7528\u6392\u884C\u699C" : "\u6211\u7684\u5E94\u7528\u6392\u884C\u699C", children: /* @__PURE__ */ jsx(
              Table,
              {
                columns: [
                  {
                    title: "\u6392\u540D",
                    dataIndex: "id",
                    width: 80,
                    render: (_, __, index) => index + 1
                  },
                  {
                    title: "\u5E94\u7528\u540D\u79F0",
                    dataIndex: "name",
                    key: "name"
                  },
                  {
                    title: "\u5206\u7C7B",
                    dataIndex: "category",
                    key: "category"
                  },
                  {
                    title: "\u4E0B\u8F7D\u91CF",
                    dataIndex: "downloads",
                    key: "downloads",
                    render: (value) => value?.toLocaleString()
                  },
                  {
                    title: "\u8BC4\u5206",
                    dataIndex: "rating",
                    key: "rating",
                    render: (value) => `${value} \u2B50`
                  }
                ],
                dataSource: data?.topApps || [],
                loading,
                pagination: false,
                rowKey: "id"
              }
            ) })
          }
        ]
      }
    )
  ] }) });
};
export default AppStatistics;
