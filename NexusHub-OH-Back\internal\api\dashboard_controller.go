package api

import (
	"strconv"
	"time"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/logger"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// DashboardController 仪表盘控制器
type DashboardController struct {
	DB *gorm.DB
}

// NewDashboardController 创建仪表盘控制器
func NewDashboardController(db *gorm.DB) *DashboardController {
	return &DashboardController{
		DB: db,
	}
}

// GetAnalyticsSummary 获取分析页摘要数据
//	@Summary		获取分析页摘要数据
//	@Description	获取平台总体数据摘要，包括用户、应用、下载、评论等统计
//	@Tags			仪表盘
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Success		200	{object}	Response{data=models.AnalyticsSummary}	"返回分析页摘要数据"
//	@Failure		401	{object}	Response								"未授权"
//	@Failure		500	{object}	Response								"服务器错误"
//	@Router			/dashboard/analytics/summary [get]
func (c *DashboardController) GetAnalyticsSummary(ctx *gin.Context) {
	summary, err := models.GetAnalyticsSummary(c.DB)
	if err != nil {
		logger.Error("获取分析页摘要数据失败", zap.Error(err))
		ServerError(ctx, "获取摘要数据失败，请稍后重试", err)
		return
	}

	Success(ctx, summary)
}

// GetAnalyticsTrend 获取趋势分析数据
//	@Summary		获取趋势分析数据
//	@Description	获取用户、应用、下载、开发者等趋势数据
//	@Tags			仪表盘
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			days	query		int										false	"统计天数，默认30天"	default(30)
//	@Success		200		{object}	Response{data=models.AnalyticsTrend}	"返回趋势分析数据"
//	@Failure		401		{object}	Response								"未授权"
//	@Failure		500		{object}	Response								"服务器错误"
//	@Router			/dashboard/analytics/trend [get]
func (c *DashboardController) GetAnalyticsTrend(ctx *gin.Context) {
	days, _ := strconv.Atoi(ctx.DefaultQuery("days", "30"))
	if days <= 0 || days > 365 {
		days = 30
	}

	trend, err := models.GetAnalyticsTrend(c.DB, days)
	if err != nil {
		logger.Error("获取趋势分析数据失败", zap.Error(err))
		ServerError(ctx, "获取趋势数据失败，请稍后重试", err)
		return
	}

	Success(ctx, trend)
}

// GetCategoryStats 获取分类统计
//	@Summary		获取分类统计
//	@Description	获取各应用分类的应用数量和下载统计
//	@Tags			仪表盘
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Success		200	{object}	Response{data=[]models.CategoryStats}	"返回分类统计数据"
//	@Failure		401	{object}	Response								"未授权"
//	@Failure		500	{object}	Response								"服务器错误"
//	@Router			/dashboard/analytics/categories [get]
func (c *DashboardController) GetCategoryStats(ctx *gin.Context) {
	stats, err := models.GetCategoryStats(c.DB)
	if err != nil {
		logger.Error("获取分类统计数据失败", zap.Error(err))
		ServerError(ctx, "获取分类统计失败，请稍后重试", err)
		return
	}

	Success(ctx, stats)
}

// GetPopularApps 获取热门应用
//	@Summary		获取热门应用
//	@Description	获取平台上下载量和评分最高的应用
//	@Tags			仪表盘
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			limit	query		int									false	"返回数量，默认10个"	default(10)
//	@Success		200		{object}	Response{data=[]models.PopularApp}	"返回热门应用列表"
//	@Failure		401		{object}	Response							"未授权"
//	@Failure		500		{object}	Response							"服务器错误"
//	@Router			/dashboard/analytics/popular-apps [get]
func (c *DashboardController) GetPopularApps(ctx *gin.Context) {
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "10"))
	if limit <= 0 || limit > 50 {
		limit = 10
	}

	apps, err := models.GetPopularApps(c.DB, limit)
	if err != nil {
		logger.Error("获取热门应用失败", zap.Error(err))
		ServerError(ctx, "获取热门应用失败，请稍后重试", err)
		return
	}

	Success(ctx, apps)
}

// MonitoringData 监控数据
type MonitoringData struct {
	Summary      models.MonitoringSummary `json:"summary"`
	SystemStatus map[string]interface{}   `json:"system_status"`
}

// GetMonitoringData 获取监控数据
//	@Summary		获取监控数据
//	@Description	获取系统运行状态监控数据
//	@Tags			仪表盘
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Success		200	{object}	Response{data=MonitoringData}	"返回监控数据"
//	@Failure		401	{object}	Response						"未授权"
//	@Failure		500	{object}	Response						"服务器错误"
//	@Router			/dashboard/monitoring/data [get]
func (c *DashboardController) GetMonitoringData(ctx *gin.Context) {
	// 这里应该调用系统监控服务获取实际数据
	// 由于是示例，使用模拟数据
	summary := models.MonitoringSummary{
		ServerStatus:        "正常",
		CPUUsage:            45.2,
		MemoryUsage:         38.7,
		DiskUsage:           56.1,
		DatabaseConnections: 25,
		AverageResponseTime: 120.5,
		RequestsPerMinute:   843,
		ErrorRate:           0.2,
		UptimeHours:         720.5,
	}

	// 系统状态详情
	systemStatus := map[string]interface{}{
		"database": map[string]interface{}{
			"status":             "正常",
			"connections":        25,
			"queries_per_second": 35.2,
		},
		"redis": map[string]interface{}{
			"status":            "正常",
			"memory_usage":      "256MB",
			"connected_clients": 18,
		},
		"storage": map[string]interface{}{
			"status":          "正常",
			"total_size":      "1.2TB",
			"used_space":      "682GB",
			"available_space": "518GB",
		},
	}

	monitoring := MonitoringData{
		Summary:      summary,
		SystemStatus: systemStatus,
	}

	Success(ctx, monitoring)
}

// GetSystemLogs 获取系统日志
//	@Summary		获取系统日志
//	@Description	获取系统运行日志记录
//	@Tags			仪表盘
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			page		query		int										false	"页码，默认1"	default(1)
//	@Param			page_size	query		int										false	"每页数量，默认20"	default(20)
//	@Param			level		query		string									false	"日志级别过滤(info/warning/error/critical)"
//	@Success		200			{object}	PageResponse{data=[]models.SystemLog}	"返回系统日志和分页信息"
//	@Failure		401			{object}	Response								"未授权"
//	@Failure		500			{object}	Response								"服务器错误"
//	@Router			/dashboard/monitoring/logs [get]
func (c *DashboardController) GetSystemLogs(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	level := ctx.Query("level")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	logs, total, err := models.GetSystemLogs(c.DB, page, pageSize, level)
	if err != nil {
		logger.Error("获取系统日志失败", zap.Error(err))
		ServerError(ctx, "获取系统日志失败，请稍后重试", err)
		return
	}

	SuccessWithPage(ctx, logs, total, page, pageSize)
}

// GetAlertEvents 获取告警事件
//	@Summary		获取告警事件
//	@Description	获取系统告警事件
//	@Tags			仪表盘
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			page		query		int										false	"页码，默认1"	default(1)
//	@Param			page_size	query		int										false	"每页数量，默认20"	default(20)
//	@Param			status		query		string									false	"状态过滤(active/resolved)"
//	@Success		200			{object}	PageResponse{data=[]models.AlertEvent}	"返回告警事件和分页信息"
//	@Failure		401			{object}	Response								"未授权"
//	@Failure		500			{object}	Response								"服务器错误"
//	@Router			/dashboard/monitoring/alerts [get]
func (c *DashboardController) GetAlertEvents(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	status := ctx.Query("status")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	events, total, err := models.GetAlertEvents(c.DB, page, pageSize, status)
	if err != nil {
		logger.Error("获取告警事件失败", zap.Error(err))
		ServerError(ctx, "获取告警事件失败，请稍后重试", err)
		return
	}

	SuccessWithPage(ctx, events, total, page, pageSize)
}

// GetWorkbenchSummary 获取工作台摘要
//	@Summary		获取工作台摘要
//	@Description	获取当前用户的工作台摘要数据
//	@Tags			仪表盘
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Success		200	{object}	Response{data=models.WorkbenchSummary}	"返回工作台摘要数据"
//	@Failure		401	{object}	Response								"未授权"
//	@Failure		500	{object}	Response								"服务器错误"
//	@Router			/dashboard/workbench/summary [get]
func (c *DashboardController) GetWorkbenchSummary(ctx *gin.Context) {
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "")
		return
	}

	summary, err := models.GetWorkbenchSummary(c.DB, userID.(uint))
	if err != nil {
		logger.Error("获取工作台摘要数据失败", zap.Error(err))
		ServerError(ctx, "获取工作台数据失败，请稍后重试", err)
		return
	}

	Success(ctx, summary)
}

// GetRecentActivities 获取最近活动
//	@Summary		获取最近活动
//	@Description	获取当前用户的最近活动记录
//	@Tags			仪表盘
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			limit	query		int										false	"返回数量，默认10条"	default(10)
//	@Success		200		{object}	Response{data=[]models.RecentActivity}	"返回最近活动列表"
//	@Failure		401		{object}	Response								"未授权"
//	@Failure		500		{object}	Response								"服务器错误"
//	@Router			/dashboard/workbench/activities [get]
func (c *DashboardController) GetRecentActivities(ctx *gin.Context) {
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "")
		return
	}

	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "10"))
	if limit <= 0 || limit > 50 {
		limit = 10
	}

	// 查询最近活动
	var activities []models.RecentActivity
	if err := c.DB.Where("user_id = ?", userID).Order("created_at DESC").Limit(limit).Find(&activities).Error; err != nil {
		// 如果表不存在，返回空数组
		if err != gorm.ErrRecordNotFound {
			logger.Error("获取最近活动失败", zap.Error(err))
			ServerError(ctx, "获取最近活动失败，请稍后重试", err)
			return
		}
	}

	Success(ctx, activities)
}

// GetTasks 获取任务列表
//	@Summary		获取任务列表
//	@Description	获取当前用户的任务列表
//	@Tags			仪表盘
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			page		query		int										false	"页码，默认1"	default(1)
//	@Param			page_size	query		int										false	"每页数量，默认20"	default(20)
//	@Param			status		query		string									false	"任务状态过滤(pending/in_progress/completed)"
//	@Success		200			{object}	PageResponse{data=[]models.TaskItem}	"返回任务列表和分页信息"
//	@Failure		401			{object}	Response								"未授权"
//	@Failure		500			{object}	Response								"服务器错误"
//	@Router			/dashboard/workbench/tasks [get]
func (c *DashboardController) GetTasks(ctx *gin.Context) {
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "")
		return
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	status := ctx.Query("status")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 构建查询
	query := c.DB.Model(&models.TaskItem{}).Where("user_id = ?", userID)
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		// 如果表不存在，返回空数组
		if err != gorm.ErrRecordNotFound {
			logger.Error("获取任务总数失败", zap.Error(err))
			ServerError(ctx, "获取任务列表失败，请稍后重试", err)
			return
		}
		total = 0
	}

	// 查询任务列表
	var tasks []models.TaskItem
	if total > 0 {
		offset := (page - 1) * pageSize
		if err := query.Order("due_date ASC").Offset(offset).Limit(pageSize).Find(&tasks).Error; err != nil {
			logger.Error("获取任务列表失败", zap.Error(err))
			ServerError(ctx, "获取任务列表失败，请稍后重试", err)
			return
		}
	}

	SuccessWithPage(ctx, tasks, total, page, pageSize)
}

// CreateTask 创建任务
//	@Summary		创建任务
//	@Description	创建新的任务
//	@Tags			仪表盘
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			data	body		models.TaskItem					true	"任务信息"
//	@Success		200		{object}	Response{data=models.TaskItem}	"创建成功，返回任务详情"
//	@Failure		400		{object}	Response						"参数错误"
//	@Failure		401		{object}	Response						"未授权"
//	@Failure		500		{object}	Response						"服务器错误"
//	@Router			/dashboard/workbench/tasks [post]
func (c *DashboardController) CreateTask(ctx *gin.Context) {
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "")
		return
	}

	var task models.TaskItem
	if err := ctx.ShouldBindJSON(&task); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 设置任务所属用户
	task.UserID = userID.(uint)
	task.CreatedAt = time.Now()
	task.UpdatedAt = time.Now()

	// 创建任务
	if err := c.DB.Create(&task).Error; err != nil {
		logger.Error("创建任务失败", zap.Error(err))
		ServerError(ctx, "创建任务失败，请稍后重试", err)
		return
	}

	SuccessWithMessage(ctx, "创建任务成功", task)
}

// UpdateTask 更新任务
//	@Summary		更新任务
//	@Description	更新任务信息
//	@Tags			仪表盘
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id		path		int								true	"任务ID"
//	@Param			data	body		models.TaskItem					true	"任务信息"
//	@Success		200		{object}	Response{data=models.TaskItem}	"更新成功，返回任务详情"
//	@Failure		400		{object}	Response						"参数错误"
//	@Failure		401		{object}	Response						"未授权"
//	@Failure		403		{object}	Response						"无权限操作此任务"
//	@Failure		404		{object}	Response						"任务不存在"
//	@Failure		500		{object}	Response						"服务器错误"
//	@Router			/dashboard/workbench/tasks/{id} [put]
func (c *DashboardController) UpdateTask(ctx *gin.Context) {
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "")
		return
	}

	taskID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		ParamError(ctx, "无效的任务ID")
		return
	}

	// 查询任务
	var task models.TaskItem
	if err := c.DB.First(&task, taskID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "任务不存在")
		} else {
			logger.Error("查询任务失败", zap.Error(err))
			ServerError(ctx, "更新任务失败，请稍后重试", err)
		}
		return
	}

	// 验证任务所属用户
	if task.UserID != userID.(uint) {
		Forbidden(ctx, "无权限操作此任务")
		return
	}

	// 解析更新数据
	var updateData models.TaskItem
	if err := ctx.ShouldBindJSON(&updateData); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 更新任务
	updateData.ID = task.ID
	updateData.UserID = task.UserID
	updateData.UpdatedAt = time.Now()

	// 如果状态从非完成变为完成，设置完成时间
	if task.Status != "completed" && updateData.Status == "completed" {
		updateData.CompletedAt = time.Now()
	}

	if err := c.DB.Model(&task).Updates(updateData).Error; err != nil {
		logger.Error("更新任务失败", zap.Error(err))
		ServerError(ctx, "更新任务失败，请稍后重试", err)
		return
	}

	// 查询更新后的任务
	if err := c.DB.First(&task, taskID).Error; err != nil {
		logger.Error("查询更新后的任务失败", zap.Error(err))
		ServerError(ctx, "更新任务失败，请稍后重试", err)
		return
	}

	SuccessWithMessage(ctx, "更新任务成功", task)
}

// DeleteTask 删除任务
//	@Summary		删除任务
//	@Description	删除指定的任务
//	@Tags			仪表盘
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id	path		int			true	"任务ID"
//	@Success		200	{object}	Response	"删除成功"
//	@Failure		400	{object}	Response	"参数错误"
//	@Failure		401	{object}	Response	"未授权"
//	@Failure		403	{object}	Response	"无权限操作此任务"
//	@Failure		404	{object}	Response	"任务不存在"
//	@Failure		500	{object}	Response	"服务器错误"
//	@Router			/dashboard/workbench/tasks/{id} [delete]
func (c *DashboardController) DeleteTask(ctx *gin.Context) {
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "")
		return
	}

	taskID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		ParamError(ctx, "无效的任务ID")
		return
	}

	// 查询任务
	var task models.TaskItem
	if err := c.DB.First(&task, taskID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "任务不存在")
		} else {
			logger.Error("查询任务失败", zap.Error(err))
			ServerError(ctx, "删除任务失败，请稍后重试", err)
		}
		return
	}

	// 验证任务所属用户
	if task.UserID != userID.(uint) {
		Forbidden(ctx, "无权限操作此任务")
		return
	}

	// 删除任务
	if err := c.DB.Delete(&task).Error; err != nil {
		logger.Error("删除任务失败", zap.Error(err))
		ServerError(ctx, "删除任务失败，请稍后重试", err)
		return
	}

	SuccessWithMessage(ctx, "删除任务成功", nil)
}
