{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "72d4123e-9ebb-4306-9a67-bf3db4a6f82a", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471269266900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f46100f8-2dbb-4b18-96a8-c2028501cc20", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471276569700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baaf90d3-d478-4870-8add-09b34ffdc676", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471277331200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bf12999-ff34-4001-8845-354616f4d470", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471278874400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14170565-daba-4fb8-920e-85f71ef38668", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471280122800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ca04fd4-82b4-4ab8-b867-c729cf72e8d3", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471280539500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ae39445-72a5-43e7-9ca2-082d1a2ea75f", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471280849400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82ca93be-bc8f-49f4-93b8-b79a758aebf1", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152471326160400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2bac0b4-b671-4b87-ac0d-9b5ff93e6c9f", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509439330600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b070e00-be9a-435a-a246-5334bae7b0e6", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509451427700, "endTime": 152509730470400}, "additional": {"children": ["1995a797-a1ff-43b2-a378-a918be697e41", "7463edf8-d3b2-4a79-b7e5-1b4ef7f11ea0", "014b58bd-0e1c-48e1-9a52-f5f5ff73e0b1", "04dad0e3-bcf6-43be-b1c1-067c1749bc0e", "a23480c5-9a86-4e0b-b6a6-37aa7793176b", "ee19f12c-688e-46cf-9ef2-3baf46c03dcf", "f0c58cea-aa32-43c0-bb75-cf011593780b"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "7d37f16b-0b4f-46fd-90f7-560192e27e60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1995a797-a1ff-43b2-a378-a918be697e41", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509451429900, "endTime": 152509477230000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b070e00-be9a-435a-a246-5334bae7b0e6", "logId": "a2fb8f9e-9cc5-4734-96e1-1dbdf3d722da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7463edf8-d3b2-4a79-b7e5-1b4ef7f11ea0", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509477256800, "endTime": 152509728734100}, "additional": {"children": ["340df34a-62c2-442f-a709-fe57e22d525c", "9293f408-8351-434f-8076-ae217d450138", "8587ac9c-2ef3-4e8d-a84f-f4ed3fd78697", "295e8566-2628-4a31-97d1-d2164a19dcbc", "0bf694fb-3a12-4afc-9be1-427eedd152be", "48c98ea6-00ab-49e8-a7b8-e5c15ebcb678", "8031f2a0-95b9-421e-aabe-92949916ae67", "92c03713-bc26-414e-bc8d-3f3612958c12", "b853beca-1850-40cf-8ceb-d9cf769d1106"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b070e00-be9a-435a-a246-5334bae7b0e6", "logId": "4cfc049e-b9f3-414d-89dc-60c57417ab5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "014b58bd-0e1c-48e1-9a52-f5f5ff73e0b1", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509728771800, "endTime": 152509730428700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b070e00-be9a-435a-a246-5334bae7b0e6", "logId": "c9607b58-cb9f-4cfa-92a4-061fe7771598"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04dad0e3-bcf6-43be-b1c1-067c1749bc0e", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509730434800, "endTime": 152509730465100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b070e00-be9a-435a-a246-5334bae7b0e6", "logId": "620ea51c-bfb5-4b1a-98db-462632e96c3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a23480c5-9a86-4e0b-b6a6-37aa7793176b", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509458525800, "endTime": 152509458582300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b070e00-be9a-435a-a246-5334bae7b0e6", "logId": "952c4ff7-7196-46db-a94a-73bc34f95d48"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "952c4ff7-7196-46db-a94a-73bc34f95d48", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509458525800, "endTime": 152509458582300}, "additional": {"logType": "info", "children": [], "durationId": "a23480c5-9a86-4e0b-b6a6-37aa7793176b", "parent": "7d37f16b-0b4f-46fd-90f7-560192e27e60"}}, {"head": {"id": "ee19f12c-688e-46cf-9ef2-3baf46c03dcf", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509469550500, "endTime": 152509469578800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b070e00-be9a-435a-a246-5334bae7b0e6", "logId": "c4f54f13-0dea-4290-b390-bf97012ab465"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4f54f13-0dea-4290-b390-bf97012ab465", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509469550500, "endTime": 152509469578800}, "additional": {"logType": "info", "children": [], "durationId": "ee19f12c-688e-46cf-9ef2-3baf46c03dcf", "parent": "7d37f16b-0b4f-46fd-90f7-560192e27e60"}}, {"head": {"id": "89f18d59-3a51-4291-99ec-ff3756e610c1", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509469682500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e09243ea-4e78-491a-bb51-f2311b1b21f3", "name": "Cache service initialization finished in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509477005400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2fb8f9e-9cc5-4734-96e1-1dbdf3d722da", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509451429900, "endTime": 152509477230000}, "additional": {"logType": "info", "children": [], "durationId": "1995a797-a1ff-43b2-a378-a918be697e41", "parent": "7d37f16b-0b4f-46fd-90f7-560192e27e60"}}, {"head": {"id": "340df34a-62c2-442f-a709-fe57e22d525c", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509486596200, "endTime": 152509486607600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7463edf8-d3b2-4a79-b7e5-1b4ef7f11ea0", "logId": "23713cfa-7817-4a52-bad6-6791bc1d0c3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9293f408-8351-434f-8076-ae217d450138", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509486625300, "endTime": 152509491744000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7463edf8-d3b2-4a79-b7e5-1b4ef7f11ea0", "logId": "6b773626-b289-4472-bc76-83cb2a9d7c85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8587ac9c-2ef3-4e8d-a84f-f4ed3fd78697", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509491771000, "endTime": 152509612116100}, "additional": {"children": ["3df63602-ca4b-44fe-92d8-3a45887a4498", "3a080f57-1daf-461a-9f37-634dde49bad4", "42ccc63f-c6a0-4645-9b83-695dafa9b573"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7463edf8-d3b2-4a79-b7e5-1b4ef7f11ea0", "logId": "b29358fb-9265-46e6-92e8-c9d6c98121a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "295e8566-2628-4a31-97d1-d2164a19dcbc", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509612130800, "endTime": 152509639322300}, "additional": {"children": ["d9ac8668-c17f-47c2-b5ad-aaee956099bb"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7463edf8-d3b2-4a79-b7e5-1b4ef7f11ea0", "logId": "b2e15263-d3c4-45c8-bfe6-826674e2ce86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0bf694fb-3a12-4afc-9be1-427eedd152be", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509639330300, "endTime": 152509698464000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7463edf8-d3b2-4a79-b7e5-1b4ef7f11ea0", "logId": "a38dc7b9-f1e4-4899-97f4-44221f5bc8dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48c98ea6-00ab-49e8-a7b8-e5c15ebcb678", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509699823000, "endTime": 152509711020400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7463edf8-d3b2-4a79-b7e5-1b4ef7f11ea0", "logId": "47b597d9-a6ee-4d0c-88b9-8550c9f5138d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8031f2a0-95b9-421e-aabe-92949916ae67", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509711066000, "endTime": 152509728426700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7463edf8-d3b2-4a79-b7e5-1b4ef7f11ea0", "logId": "bd44d33f-e939-4241-922b-de1d9b36afdc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92c03713-bc26-414e-bc8d-3f3612958c12", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509728465000, "endTime": 152509728710300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7463edf8-d3b2-4a79-b7e5-1b4ef7f11ea0", "logId": "57e883d9-d975-4948-9bc4-32465ba4be5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23713cfa-7817-4a52-bad6-6791bc1d0c3f", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509486596200, "endTime": 152509486607600}, "additional": {"logType": "info", "children": [], "durationId": "340df34a-62c2-442f-a709-fe57e22d525c", "parent": "4cfc049e-b9f3-414d-89dc-60c57417ab5b"}}, {"head": {"id": "6b773626-b289-4472-bc76-83cb2a9d7c85", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509486625300, "endTime": 152509491744000}, "additional": {"logType": "info", "children": [], "durationId": "9293f408-8351-434f-8076-ae217d450138", "parent": "4cfc049e-b9f3-414d-89dc-60c57417ab5b"}}, {"head": {"id": "3df63602-ca4b-44fe-92d8-3a45887a4498", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509492471300, "endTime": 152509492493900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8587ac9c-2ef3-4e8d-a84f-f4ed3fd78697", "logId": "330b9ebf-4a87-4f4d-9a40-35c8731c33c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "330b9ebf-4a87-4f4d-9a40-35c8731c33c8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509492471300, "endTime": 152509492493900}, "additional": {"logType": "info", "children": [], "durationId": "3df63602-ca4b-44fe-92d8-3a45887a4498", "parent": "b29358fb-9265-46e6-92e8-c9d6c98121a2"}}, {"head": {"id": "3a080f57-1daf-461a-9f37-634dde49bad4", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509494860300, "endTime": 152509611245000}, "additional": {"children": ["5de0cbdb-f8a0-4d49-938f-45d49bfac640", "4c4f91d1-aa14-4ce5-ac3f-af4f452f80d5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8587ac9c-2ef3-4e8d-a84f-f4ed3fd78697", "logId": "41403e90-3894-42fe-b6e3-a8fbde0994f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5de0cbdb-f8a0-4d49-938f-45d49bfac640", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509494861900, "endTime": 152509502172700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a080f57-1daf-461a-9f37-634dde49bad4", "logId": "fc3ae532-048c-40f5-8175-8769869f9f66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c4f91d1-aa14-4ce5-ac3f-af4f452f80d5", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509502190600, "endTime": 152509611233000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a080f57-1daf-461a-9f37-634dde49bad4", "logId": "99b6a33b-e7c2-4bdb-ab78-3d8597d32791"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "469eefee-9859-4751-96e0-5f5eb393c3b7", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509494870500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7509346a-f89e-4b02-91bf-992a52afd85d", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509502017900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc3ae532-048c-40f5-8175-8769869f9f66", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509494861900, "endTime": 152509502172700}, "additional": {"logType": "info", "children": [], "durationId": "5de0cbdb-f8a0-4d49-938f-45d49bfac640", "parent": "41403e90-3894-42fe-b6e3-a8fbde0994f8"}}, {"head": {"id": "cbd91cae-a0bd-40f4-a58d-2b495715cfac", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509502208700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d0a91d3-7b44-401a-943d-2d7306da0e84", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509510620100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b857d31-ddf8-4023-bb86-e033f8089ec2", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509510764800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3b51434-0124-4967-abbe-c4dce027b230", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509510935800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b97822c7-1418-4ca4-a2e4-e1e397b8461a", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509511061600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "029a3b48-49f7-4671-bfdf-6b3178d04c40", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509513820000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57a5f669-3ba3-46db-a006-4982c9837005", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509528899100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30fea795-95b1-4af6-89a3-f7366813089b", "name": "Sdk init in 55 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509574499900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75f10d80-74b6-4876-9bdb-ea228b53c864", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509574749500}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 18, "second": 59}, "markType": "other"}}, {"head": {"id": "0b9b7219-d40f-4d7c-9137-20920057e3b8", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509574817000}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 18, "second": 59}, "markType": "other"}}, {"head": {"id": "154482f7-25c5-45d3-bba5-10a43e61693f", "name": "Project task initialization takes 34 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509610900000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3ffe03f-d6b0-488f-b476-4a3934e51b8d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509611060700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "634ca0fe-118a-43d8-ab9c-b206e0924d8e", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509611140800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621fb1d7-fbe3-416d-b613-5b155d95c3f1", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509611191100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99b6a33b-e7c2-4bdb-ab78-3d8597d32791", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509502190600, "endTime": 152509611233000}, "additional": {"logType": "info", "children": [], "durationId": "4c4f91d1-aa14-4ce5-ac3f-af4f452f80d5", "parent": "41403e90-3894-42fe-b6e3-a8fbde0994f8"}}, {"head": {"id": "41403e90-3894-42fe-b6e3-a8fbde0994f8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509494860300, "endTime": 152509611245000}, "additional": {"logType": "info", "children": ["fc3ae532-048c-40f5-8175-8769869f9f66", "99b6a33b-e7c2-4bdb-ab78-3d8597d32791"], "durationId": "3a080f57-1daf-461a-9f37-634dde49bad4", "parent": "b29358fb-9265-46e6-92e8-c9d6c98121a2"}}, {"head": {"id": "42ccc63f-c6a0-4645-9b83-695dafa9b573", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509612070500, "endTime": 152509612094700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8587ac9c-2ef3-4e8d-a84f-f4ed3fd78697", "logId": "2c73ec79-45df-4677-8d97-16ce3e8dde14"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c73ec79-45df-4677-8d97-16ce3e8dde14", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509612070500, "endTime": 152509612094700}, "additional": {"logType": "info", "children": [], "durationId": "42ccc63f-c6a0-4645-9b83-695dafa9b573", "parent": "b29358fb-9265-46e6-92e8-c9d6c98121a2"}}, {"head": {"id": "b29358fb-9265-46e6-92e8-c9d6c98121a2", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509491771000, "endTime": 152509612116100}, "additional": {"logType": "info", "children": ["330b9ebf-4a87-4f4d-9a40-35c8731c33c8", "41403e90-3894-42fe-b6e3-a8fbde0994f8", "2c73ec79-45df-4677-8d97-16ce3e8dde14"], "durationId": "8587ac9c-2ef3-4e8d-a84f-f4ed3fd78697", "parent": "4cfc049e-b9f3-414d-89dc-60c57417ab5b"}}, {"head": {"id": "d9ac8668-c17f-47c2-b5ad-aaee956099bb", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509612855200, "endTime": 152509639309100}, "additional": {"children": ["ef83df9f-c5ee-47cd-8367-bdd713a56c21", "cd508c14-56b9-4550-b8d9-ff8245a0a102", "41d9f0eb-8244-4aa1-b6a3-3c84732ac36b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "295e8566-2628-4a31-97d1-d2164a19dcbc", "logId": "1d7c51c3-7c48-427e-b864-3312c342598d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef83df9f-c5ee-47cd-8367-bdd713a56c21", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509617032600, "endTime": 152509617058800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9ac8668-c17f-47c2-b5ad-aaee956099bb", "logId": "44a71d7a-1ffa-4e68-8137-c469a6e8d250"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44a71d7a-1ffa-4e68-8137-c469a6e8d250", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509617032600, "endTime": 152509617058800}, "additional": {"logType": "info", "children": [], "durationId": "ef83df9f-c5ee-47cd-8367-bdd713a56c21", "parent": "1d7c51c3-7c48-427e-b864-3312c342598d"}}, {"head": {"id": "cd508c14-56b9-4550-b8d9-ff8245a0a102", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509619519300, "endTime": 152509637794300}, "additional": {"children": ["46ebbf9c-016e-43fd-b4a8-d6e9820fb698", "25451285-9c33-4eef-a747-0a4e4ffec017"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9ac8668-c17f-47c2-b5ad-aaee956099bb", "logId": "4d376b55-6c9b-486c-99c1-0af8536dc038"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46ebbf9c-016e-43fd-b4a8-d6e9820fb698", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509619520500, "endTime": 152509624608700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cd508c14-56b9-4550-b8d9-ff8245a0a102", "logId": "ccee8d77-6f0f-4f51-a512-ec06c12d8131"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25451285-9c33-4eef-a747-0a4e4ffec017", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509624625900, "endTime": 152509637784000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cd508c14-56b9-4550-b8d9-ff8245a0a102", "logId": "67e27241-422b-4e32-a3ea-65d7da708a2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99b5e386-3fda-428e-9ed7-3027d4f2d774", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509619527200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6989918-45e2-4937-9c04-ea5575c75452", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509624462400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccee8d77-6f0f-4f51-a512-ec06c12d8131", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509619520500, "endTime": 152509624608700}, "additional": {"logType": "info", "children": [], "durationId": "46ebbf9c-016e-43fd-b4a8-d6e9820fb698", "parent": "4d376b55-6c9b-486c-99c1-0af8536dc038"}}, {"head": {"id": "61482d80-b71f-41d2-8ed1-6d04bce1e16f", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509624641700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e33a541e-2ea0-4787-9491-5a962c80d714", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509631941400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2c0b071-f6f4-469c-8eb0-49e11f522d5d", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509632149400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65454b98-ef20-4b0e-98e5-c159ed296e59", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509632493500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac6e77fe-0f4d-49b1-9c9b-e1475a03427c", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509632700900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8efdae6b-6233-44c9-ae43-993f13516baf", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509632804400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc28a430-8de9-4577-b180-c31eb5747552", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509632888300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eb2058d-23f8-46aa-85da-3cae19d383e0", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509632988900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92252bab-9e23-42b9-b467-2a1b1cab71d3", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509633048100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "099a1fc3-6ec0-4661-8a81-82ca92c0cbd5", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509633513400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2cce9ef-f788-47e1-92c1-b8e56b9420e5", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509633675300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef33378a-2d95-4d3f-a128-d360b25b0db9", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509633735700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27d7f3d9-12f7-4db6-9f25-ccb9eb3d1645", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509633783300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71873a1d-74a7-4e6d-a2ce-9222e0a4f97f", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509633845400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ffc507d-f0c1-4381-9f3f-971504298e01", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509633894600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc2b0105-43b8-45fc-a88a-944a096e0797", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509634010900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4d79262-64cb-44f6-97f7-2e695778b15f", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509634104200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c0b7d9c-ab17-46e4-9cb9-7d98e59d422d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509634144200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5161d4a0-a9f8-4c44-964d-a54af98119ee", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509634189600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "446e80a2-d1b0-4294-8eca-f3b2ee9b1e57", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509634236700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05459842-d50a-4006-ab66-211e2d3b05bc", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509637499000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48bd7342-47f5-43ec-8c74-184e1a1d2d3c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509637654800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdd02140-8b7c-441f-bea6-b5bcd637d39b", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509637709000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b66341bd-2d22-457a-ad24-9aeb32014469", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509637746600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67e27241-422b-4e32-a3ea-65d7da708a2f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509624625900, "endTime": 152509637784000}, "additional": {"logType": "info", "children": [], "durationId": "25451285-9c33-4eef-a747-0a4e4ffec017", "parent": "4d376b55-6c9b-486c-99c1-0af8536dc038"}}, {"head": {"id": "4d376b55-6c9b-486c-99c1-0af8536dc038", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509619519300, "endTime": 152509637794300}, "additional": {"logType": "info", "children": ["ccee8d77-6f0f-4f51-a512-ec06c12d8131", "67e27241-422b-4e32-a3ea-65d7da708a2f"], "durationId": "cd508c14-56b9-4550-b8d9-ff8245a0a102", "parent": "1d7c51c3-7c48-427e-b864-3312c342598d"}}, {"head": {"id": "41d9f0eb-8244-4aa1-b6a3-3c84732ac36b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509639264800, "endTime": 152509639283900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9ac8668-c17f-47c2-b5ad-aaee956099bb", "logId": "ab28d07a-1c3a-4cc2-b4e9-8cbfd7e94565"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab28d07a-1c3a-4cc2-b4e9-8cbfd7e94565", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509639264800, "endTime": 152509639283900}, "additional": {"logType": "info", "children": [], "durationId": "41d9f0eb-8244-4aa1-b6a3-3c84732ac36b", "parent": "1d7c51c3-7c48-427e-b864-3312c342598d"}}, {"head": {"id": "1d7c51c3-7c48-427e-b864-3312c342598d", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509612855200, "endTime": 152509639309100}, "additional": {"logType": "info", "children": ["44a71d7a-1ffa-4e68-8137-c469a6e8d250", "4d376b55-6c9b-486c-99c1-0af8536dc038", "ab28d07a-1c3a-4cc2-b4e9-8cbfd7e94565"], "durationId": "d9ac8668-c17f-47c2-b5ad-aaee956099bb", "parent": "b2e15263-d3c4-45c8-bfe6-826674e2ce86"}}, {"head": {"id": "b2e15263-d3c4-45c8-bfe6-826674e2ce86", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509612130800, "endTime": 152509639322300}, "additional": {"logType": "info", "children": ["1d7c51c3-7c48-427e-b864-3312c342598d"], "durationId": "295e8566-2628-4a31-97d1-d2164a19dcbc", "parent": "4cfc049e-b9f3-414d-89dc-60c57417ab5b"}}, {"head": {"id": "e15ab30e-7018-4a77-a7e8-ac5e3986177e", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509658359800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edb731e8-7a22-4ad2-8abe-60997249ec39", "name": "hvigorfile, resolve hvigorfile dependencies in 59 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509698296300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a38dc7b9-f1e4-4899-97f4-44221f5bc8dc", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509639330300, "endTime": 152509698464000}, "additional": {"logType": "info", "children": [], "durationId": "0bf694fb-3a12-4afc-9be1-427eedd152be", "parent": "4cfc049e-b9f3-414d-89dc-60c57417ab5b"}}, {"head": {"id": "b853beca-1850-40cf-8ceb-d9cf769d1106", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509699410100, "endTime": 152509699804200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7463edf8-d3b2-4a79-b7e5-1b4ef7f11ea0", "logId": "4270afa6-77e0-4a4e-904a-a43e9527dd22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9f86d8c-0d6a-4a69-8c3e-147adfd986f1", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509699450400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4270afa6-77e0-4a4e-904a-a43e9527dd22", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509699410100, "endTime": 152509699804200}, "additional": {"logType": "info", "children": [], "durationId": "b853beca-1850-40cf-8ceb-d9cf769d1106", "parent": "4cfc049e-b9f3-414d-89dc-60c57417ab5b"}}, {"head": {"id": "386992d5-7ead-4fdf-aa2c-af5a66f3f142", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509701755900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d6ce1ca-e9f1-4628-921e-3a62e4493e64", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509709281400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47b597d9-a6ee-4d0c-88b9-8550c9f5138d", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509699823000, "endTime": 152509711020400}, "additional": {"logType": "info", "children": [], "durationId": "48c98ea6-00ab-49e8-a7b8-e5c15ebcb678", "parent": "4cfc049e-b9f3-414d-89dc-60c57417ab5b"}}, {"head": {"id": "a8327b66-ce70-4de9-935b-80bc28146289", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509711108600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a86d80e-0701-49b4-a94e-5354f38db101", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509719816900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a367311f-08e1-411c-9351-e2804657f3c6", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509719962000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd5b0e70-04b8-48c1-99a8-7be69499f7d0", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509720132400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4d76f05-b85b-4075-bd3e-81a6612b8413", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509723602900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db28c193-ae55-4f7d-8fde-2301773fa5a1", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509723752600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd44d33f-e939-4241-922b-de1d9b36afdc", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509711066000, "endTime": 152509728426700}, "additional": {"logType": "info", "children": [], "durationId": "8031f2a0-95b9-421e-aabe-92949916ae67", "parent": "4cfc049e-b9f3-414d-89dc-60c57417ab5b"}}, {"head": {"id": "e78e21a5-217f-4b98-9563-092686c533d8", "name": "Configuration phase cost:242 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509728514000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57e883d9-d975-4948-9bc4-32465ba4be5e", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509728465000, "endTime": 152509728710300}, "additional": {"logType": "info", "children": [], "durationId": "92c03713-bc26-414e-bc8d-3f3612958c12", "parent": "4cfc049e-b9f3-414d-89dc-60c57417ab5b"}}, {"head": {"id": "4cfc049e-b9f3-414d-89dc-60c57417ab5b", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509477256800, "endTime": 152509728734100}, "additional": {"logType": "info", "children": ["23713cfa-7817-4a52-bad6-6791bc1d0c3f", "6b773626-b289-4472-bc76-83cb2a9d7c85", "b29358fb-9265-46e6-92e8-c9d6c98121a2", "b2e15263-d3c4-45c8-bfe6-826674e2ce86", "a38dc7b9-f1e4-4899-97f4-44221f5bc8dc", "47b597d9-a6ee-4d0c-88b9-8550c9f5138d", "bd44d33f-e939-4241-922b-de1d9b36afdc", "57e883d9-d975-4948-9bc4-32465ba4be5e", "4270afa6-77e0-4a4e-904a-a43e9527dd22"], "durationId": "7463edf8-d3b2-4a79-b7e5-1b4ef7f11ea0", "parent": "7d37f16b-0b4f-46fd-90f7-560192e27e60"}}, {"head": {"id": "f0c58cea-aa32-43c0-bb75-cf011593780b", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509730385900, "endTime": 152509730412100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b070e00-be9a-435a-a246-5334bae7b0e6", "logId": "3b18ce7c-5a43-4e70-8726-fd93c637644d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b18ce7c-5a43-4e70-8726-fd93c637644d", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509730385900, "endTime": 152509730412100}, "additional": {"logType": "info", "children": [], "durationId": "f0c58cea-aa32-43c0-bb75-cf011593780b", "parent": "7d37f16b-0b4f-46fd-90f7-560192e27e60"}}, {"head": {"id": "c9607b58-cb9f-4cfa-92a4-061fe7771598", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509728771800, "endTime": 152509730428700}, "additional": {"logType": "info", "children": [], "durationId": "014b58bd-0e1c-48e1-9a52-f5f5ff73e0b1", "parent": "7d37f16b-0b4f-46fd-90f7-560192e27e60"}}, {"head": {"id": "620ea51c-bfb5-4b1a-98db-462632e96c3f", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509730434800, "endTime": 152509730465100}, "additional": {"logType": "info", "children": [], "durationId": "04dad0e3-bcf6-43be-b1c1-067c1749bc0e", "parent": "7d37f16b-0b4f-46fd-90f7-560192e27e60"}}, {"head": {"id": "7d37f16b-0b4f-46fd-90f7-560192e27e60", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509451427700, "endTime": 152509730470400}, "additional": {"logType": "info", "children": ["a2fb8f9e-9cc5-4734-96e1-1dbdf3d722da", "4cfc049e-b9f3-414d-89dc-60c57417ab5b", "c9607b58-cb9f-4cfa-92a4-061fe7771598", "620ea51c-bfb5-4b1a-98db-462632e96c3f", "952c4ff7-7196-46db-a94a-73bc34f95d48", "c4f54f13-0dea-4290-b390-bf97012ab465", "3b18ce7c-5a43-4e70-8726-fd93c637644d"], "durationId": "2b070e00-be9a-435a-a246-5334bae7b0e6"}}, {"head": {"id": "28979127-5c04-4db5-8275-c0d88adcc689", "name": "Configuration task cost before running: 286 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509730689200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccfaf7cd-ed6e-4e2c-81bd-4cfed1b94d02", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509741555600, "endTime": 152509756899900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d34b3759-0124-4cf6-8aa9-dcad7a6609cc", "logId": "19b0dbeb-25a5-4515-8181-52fde6472512"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d34b3759-0124-4cf6-8aa9-dcad7a6609cc", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509732372200}, "additional": {"logType": "detail", "children": [], "durationId": "ccfaf7cd-ed6e-4e2c-81bd-4cfed1b94d02"}}, {"head": {"id": "22b864d9-1a63-435f-ba71-3b3d5e3c3ce4", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509733163500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d72bfbe-721d-4c37-b809-dcb740ec66a6", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509733318300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "097f939f-440c-4929-9405-5d469f6106a8", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509734130500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf6dd763-b58f-41ba-b071-51c12d2c067d", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509735251000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "304a112c-64cc-4a87-974f-ced4d5adc0b1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509736463400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1715a5eb-8f15-45e4-b61a-b19ca902a0ec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509736575000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c787bcda-8b38-4f13-9922-5339ecd0175b", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509741577700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1db6f35-b416-4777-9f68-7b81a72aa12c", "name": "Incremental task entry:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509756625600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e3a2020-f503-49a4-82ea-c2cdbb8ba681", "name": "entry : default@PreBuild cost memory 0.32472991943359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509756823300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19b0dbeb-25a5-4515-8181-52fde6472512", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509741555600, "endTime": 152509756899900}, "additional": {"logType": "info", "children": [], "durationId": "ccfaf7cd-ed6e-4e2c-81bd-4cfed1b94d02"}}, {"head": {"id": "715f193b-d22d-44e9-800e-1c68a3a2b8b5", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509764042700, "endTime": 152509766046300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ce76ece6-33fb-4a4a-b7f7-3b7a4ca91493", "logId": "c88d8ec8-8c1b-4608-b880-b88d11ebcc27"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce76ece6-33fb-4a4a-b7f7-3b7a4ca91493", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509762083600}, "additional": {"logType": "detail", "children": [], "durationId": "715f193b-d22d-44e9-800e-1c68a3a2b8b5"}}, {"head": {"id": "a06b9e7e-450e-4a98-9471-b0af46accd6b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509763264300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5de92ae-a43a-4ab9-85a1-7af3c26f7f7d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509763392100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07818e91-5bae-412b-8e34-2836019c029f", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509764054600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d720e61a-34d1-4b24-adeb-b2ba98f5b233", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509764761800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adfca89e-f21a-40d3-bb08-12d3eba4134c", "name": "entry : default@CreateModuleInfo cost memory 0.0616607666015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509765842600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0398fc18-42ec-41f8-b29c-579112c33834", "name": "runTaskFromQueue task cost before running: 321 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509765982800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c88d8ec8-8c1b-4608-b880-b88d11ebcc27", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509764042700, "endTime": 152509766046300, "totalTime": 1913800}, "additional": {"logType": "info", "children": [], "durationId": "715f193b-d22d-44e9-800e-1c68a3a2b8b5"}}, {"head": {"id": "d5e648a8-5f27-4513-ae83-f7838e691ef3", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509776323700, "endTime": 152509779685500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a84edb91-5e96-461d-be40-0e0227b5a78c", "logId": "028940af-face-43a3-b08a-7806b8edf68b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a84edb91-5e96-461d-be40-0e0227b5a78c", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509769476700}, "additional": {"logType": "detail", "children": [], "durationId": "d5e648a8-5f27-4513-ae83-f7838e691ef3"}}, {"head": {"id": "49a12259-d108-4b61-84cb-527dcece86a1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509770765100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf6fbcdc-1603-48db-a04e-c786af54c825", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509770914900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f216db89-9207-461d-81a3-2c8010aa7a75", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509776367300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a2a8cb6-2120-463a-92a6-77add46e0ae5", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509778192300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32e8d7cb-d176-40c7-8e96-aa21bcdafbf7", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509779448300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "334e489a-df27-4a47-897e-52517d0470d6", "name": "entry : default@GenerateMetadata cost memory 0.10425567626953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509779611900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "028940af-face-43a3-b08a-7806b8edf68b", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509776323700, "endTime": 152509779685500}, "additional": {"logType": "info", "children": [], "durationId": "d5e648a8-5f27-4513-ae83-f7838e691ef3"}}, {"head": {"id": "230cda44-9746-4d17-a6dd-7c103eab20ce", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509783801600, "endTime": 152509784195200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c699d194-a14d-4f91-8de7-c6173131ce44", "logId": "315cfab7-0633-46f4-bd83-09908f50e812"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c699d194-a14d-4f91-8de7-c6173131ce44", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509782023000}, "additional": {"logType": "detail", "children": [], "durationId": "230cda44-9746-4d17-a6dd-7c103eab20ce"}}, {"head": {"id": "7169ef6f-629f-450f-8378-1ac2cdcf6800", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509783441000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e1b24ee-696e-4aa0-9b35-1289d875969c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509783627500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91c4452b-7958-4787-9dd7-88f0f8036c12", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509783810900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36d898b1-a865-4e46-ab0a-6c065e9e79c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509783931000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37e46236-0110-44d6-9c11-28997a9659ca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509783977700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec19ea81-f2bf-40af-b66f-636ab81de72a", "name": "entry : default@ConfigureCmake cost memory 0.08583831787109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509784047000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "955dca66-f4ca-4d3a-a3ad-5ae150db1de0", "name": "runTaskFromQueue task cost before running: 339 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509784139000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "315cfab7-0633-46f4-bd83-09908f50e812", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509783801600, "endTime": 152509784195200, "totalTime": 316400}, "additional": {"logType": "info", "children": [], "durationId": "230cda44-9746-4d17-a6dd-7c103eab20ce"}}, {"head": {"id": "0b6583c8-cd86-4a04-b9f1-d5a50a29ddb7", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509791508700, "endTime": 152509793712700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "92462e19-bcee-4134-a71f-3145c2084c01", "logId": "d21396f9-92cf-4dea-a4b4-632ce80bbf47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92462e19-bcee-4134-a71f-3145c2084c01", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509788530100}, "additional": {"logType": "detail", "children": [], "durationId": "0b6583c8-cd86-4a04-b9f1-d5a50a29ddb7"}}, {"head": {"id": "56d1ed23-157a-49b4-a7ef-f4a4cff7f7af", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509790182000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b141ce0-becf-4526-98cf-fb8dd10bad77", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509790379600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89093825-6c36-40ad-af46-6219b249d763", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509791523300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c1bb1f1-efaf-4f64-8f76-771d27e95208", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509793494300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69fe4881-d5ea-4185-b41b-b9b9b42470d8", "name": "entry : default@MergeProfile cost memory 0.118499755859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509793645600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d21396f9-92cf-4dea-a4b4-632ce80bbf47", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509791508700, "endTime": 152509793712700}, "additional": {"logType": "info", "children": [], "durationId": "0b6583c8-cd86-4a04-b9f1-d5a50a29ddb7"}}, {"head": {"id": "f3fe474b-694e-4e02-a713-9f765c80b6d2", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509797773500, "endTime": 152509801501700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "42561d84-eec2-44f9-8fe6-c0aaae6c5073", "logId": "16f83c90-c191-468a-8ca8-60fefd9f0e40"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42561d84-eec2-44f9-8fe6-c0aaae6c5073", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509795799800}, "additional": {"logType": "detail", "children": [], "durationId": "f3fe474b-694e-4e02-a713-9f765c80b6d2"}}, {"head": {"id": "265a115b-4821-4d2d-a379-4341b1eaba0a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509796829300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "748ee1db-3ad3-4c04-add9-7beeee6e6f65", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509796977500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcae41ce-8a90-436e-ac4a-90f6851f9a9b", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509797785000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3b57b79-8177-44d5-b71f-80cf20c438d2", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509798730800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e977fd38-6e37-4137-9315-0c8ba34cccbf", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509801267300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df8b4477-3939-417b-b1ea-67426783e968", "name": "entry : default@CreateBuildProfile cost memory 0.1085205078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509801427400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16f83c90-c191-468a-8ca8-60fefd9f0e40", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509797773500, "endTime": 152509801501700}, "additional": {"logType": "info", "children": [], "durationId": "f3fe474b-694e-4e02-a713-9f765c80b6d2"}}, {"head": {"id": "1e1c2130-c53e-414d-9f10-cb04723c278c", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509805253200, "endTime": 152509805835700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "fc7c15f2-a0b7-453e-9f96-63779437a9c9", "logId": "94190b2b-da6d-416e-a07b-0c36820b2b94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc7c15f2-a0b7-453e-9f96-63779437a9c9", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509803353900}, "additional": {"logType": "detail", "children": [], "durationId": "1e1c2130-c53e-414d-9f10-cb04723c278c"}}, {"head": {"id": "5c9e1272-85e4-453b-bf60-aa7753aac36e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509804390100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "357d4581-cfa9-447f-af75-4c8008443ae1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509804508300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91f1b2ee-da25-4d36-bf12-ed3865f23403", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509805264300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bd96b72-c0bb-4ba4-8b2e-c0fac5018197", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509805399900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6604863a-317b-46a1-952c-fac9d110705a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509805449000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d783a7f-9f50-43ad-bfe7-f1fc9bd1da5d", "name": "entry : default@PreCheckSyscap cost memory 0.0413055419921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509805690700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e57c5da7-3133-48ef-aba5-7318ad872c0c", "name": "runTaskFromQueue task cost before running: 361 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509805787300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94190b2b-da6d-416e-a07b-0c36820b2b94", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509805253200, "endTime": 152509805835700, "totalTime": 513100}, "additional": {"logType": "info", "children": [], "durationId": "1e1c2130-c53e-414d-9f10-cb04723c278c"}}, {"head": {"id": "5029b257-e598-41c6-9f29-8409f232777d", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509810203100, "endTime": 152509816229600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "529d6a39-9124-40c0-93b6-43fe9913e52e", "logId": "654d4f1c-6003-4172-bfc1-622be2d040e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "529d6a39-9124-40c0-93b6-43fe9913e52e", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509807610100}, "additional": {"logType": "detail", "children": [], "durationId": "5029b257-e598-41c6-9f29-8409f232777d"}}, {"head": {"id": "5ac0f9ba-934f-448a-ba85-e785e6ee68db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509808655900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52e3f161-6516-4a42-b77c-8d05ce7e1459", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509808765700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14fac709-3165-4aa8-8853-9b63acb18c08", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509810215400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "579cb1f0-3bbc-453d-8698-957c3b12b490", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509815291100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9730bc51-44bf-412e-8e9a-959b7c10f454", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509815991900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54f7439c-12ab-41a6-851c-87e634046091", "name": "entry : default@GeneratePkgContextInfo cost memory 0.24021148681640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509816150700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "654d4f1c-6003-4172-bfc1-622be2d040e2", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509810203100, "endTime": 152509816229600}, "additional": {"logType": "info", "children": [], "durationId": "5029b257-e598-41c6-9f29-8409f232777d"}}, {"head": {"id": "4723070b-850d-4c58-9050-c2c33aa70d2d", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509824191500, "endTime": 152509826418100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "8a91eba0-11cd-4d76-bb5b-8c3806e84a30", "logId": "9f7be907-5db8-4e34-9dfe-b402784199c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a91eba0-11cd-4d76-bb5b-8c3806e84a30", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509817958300}, "additional": {"logType": "detail", "children": [], "durationId": "4723070b-850d-4c58-9050-c2c33aa70d2d"}}, {"head": {"id": "013ac52a-bfd8-4a9d-bb67-e85b635a26cb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509819159500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "343226b2-18f6-4066-9624-a426a13edb34", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509819276100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3803551a-f1b1-40d9-bba8-18e4788084ef", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509824208900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfb1c876-4313-45f8-9424-9c4ca7bdc48f", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509825939600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d6d90e4-7e8b-41b5-9a6e-a0afbfd9d04b", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509826086400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaa71671-3a4a-44a9-8d08-17219dc8680a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509826174500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95beb4c9-d915-43c6-9bb3-8bdd406dd3b1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509826215400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "263e42e9-6f90-4040-8c70-edbf6fafbbef", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12221527099609375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509826284400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b41cce6f-584c-4539-818d-76219b142625", "name": "runTaskFromQueue task cost before running: 382 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509826364800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f7be907-5db8-4e34-9dfe-b402784199c4", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509824191500, "endTime": 152509826418100, "totalTime": 2152800}, "additional": {"logType": "info", "children": [], "durationId": "4723070b-850d-4c58-9050-c2c33aa70d2d"}}, {"head": {"id": "95947970-f724-4c10-8bd2-9d0aa0fee38b", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509833254800, "endTime": 152509833695900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "63f3ad78-030f-4091-95b5-cf12a20073a3", "logId": "7019aa68-514c-4d2d-95ce-f704d22d3c0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63f3ad78-030f-4091-95b5-cf12a20073a3", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509829384900}, "additional": {"logType": "detail", "children": [], "durationId": "95947970-f724-4c10-8bd2-9d0aa0fee38b"}}, {"head": {"id": "4ff0aab2-e040-41af-b23c-865e5728a087", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509832123800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "075c1f36-01ef-4c8d-a1e9-813e62d67261", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509832326500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04349118-f9d3-49ae-a514-9fec067b2424", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509833270500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0875fc9-1616-48e9-b51c-21fbeefcf00b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509833441000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "909485a3-8479-49d1-a88c-7c5d2d89fa05", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509833503200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a736c938-7507-4793-88ea-5f3c7b3fe51a", "name": "entry : default@BuildNativeWithCmake cost memory 0.0387725830078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509833575300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b56f8569-bf67-4cea-b5ca-010d41046c2c", "name": "runTaskFromQueue task cost before running: 389 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509833652800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7019aa68-514c-4d2d-95ce-f704d22d3c0b", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509833254800, "endTime": 152509833695900, "totalTime": 378200}, "additional": {"logType": "info", "children": [], "durationId": "95947970-f724-4c10-8bd2-9d0aa0fee38b"}}, {"head": {"id": "c9246bfa-2497-4f97-b1b5-f4a60f76426c", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509837239300, "endTime": 152509841143900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c54f1703-23e7-40d0-923a-279f31fd4445", "logId": "1e3d2934-508b-479b-b9f3-56433bbbcd1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c54f1703-23e7-40d0-923a-279f31fd4445", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509835392900}, "additional": {"logType": "detail", "children": [], "durationId": "c9246bfa-2497-4f97-b1b5-f4a60f76426c"}}, {"head": {"id": "dfec082e-4f44-4aba-88b1-e458b94131d3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509836361900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34258080-8676-425a-922c-3f1e80af2355", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509836494500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef0a4cd8-d5ab-4528-bd25-689b8bff3052", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509837250700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54d53558-3ad6-4f1b-8793-1f1674abb750", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509840927900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06c5b2b7-e0fe-4b7b-a0e4-2ea6a2861507", "name": "entry : default@MakePackInfo cost memory 0.1654510498046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509841079400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e3d2934-508b-479b-b9f3-56433bbbcd1b", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509837239300, "endTime": 152509841143900}, "additional": {"logType": "info", "children": [], "durationId": "c9246bfa-2497-4f97-b1b5-f4a60f76426c"}}, {"head": {"id": "655d6c10-041a-46ad-b1a7-f226b96564c8", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509846509200, "endTime": 152509850482600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9620a247-8c73-48aa-a0ed-25d1847967bf", "logId": "29ef8bdc-9982-478e-9bfe-72e13a0ba637"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9620a247-8c73-48aa-a0ed-25d1847967bf", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509843353300}, "additional": {"logType": "detail", "children": [], "durationId": "655d6c10-041a-46ad-b1a7-f226b96564c8"}}, {"head": {"id": "2b90fe80-9534-43a9-ad3a-508496f2387d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509844696700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e873ca58-cfbd-4a6f-b357-39d1b68c60e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509844909800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d1a5c5e-ab2b-447e-8aa5-61ca0a34d86a", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509846524100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdc1de8d-784f-4ec5-91ae-a0999c9ab0b5", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509846775000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99271030-a770-430e-924f-f91fd639ba7e", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509847529900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aad2ae7a-f190-45ad-b2cb-54a9528236c6", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509850234600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1da19659-d524-4ddb-bb6f-d58df452a545", "name": "entry : default@SyscapTransform cost memory 0.1511993408203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509850408100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29ef8bdc-9982-478e-9bfe-72e13a0ba637", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509846509200, "endTime": 152509850482600}, "additional": {"logType": "info", "children": [], "durationId": "655d6c10-041a-46ad-b1a7-f226b96564c8"}}, {"head": {"id": "0fed9e71-4165-49f5-93f6-d882c74bbde2", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509854319900, "endTime": 152509856265800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "0d99d4d5-3f0c-48a4-ba8f-8f302c096b6b", "logId": "24ebc017-6c64-4a49-8ef2-b0f5cda0b1d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d99d4d5-3f0c-48a4-ba8f-8f302c096b6b", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509852094100}, "additional": {"logType": "detail", "children": [], "durationId": "0fed9e71-4165-49f5-93f6-d882c74bbde2"}}, {"head": {"id": "f951dabb-da1e-4173-909a-f4942c47815c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509853142600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a6984e9-e2bd-434b-94a0-05067385fc04", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509853265300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88f315c6-abfa-4db4-ae86-4e0909e5d521", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509854331700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91a8a523-2bb7-4733-9a65-597bcf71a8f4", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509856060500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23a9c75e-57c3-43ab-8dda-961407918b1c", "name": "entry : default@ProcessProfile cost memory 0.12425994873046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509856195900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24ebc017-6c64-4a49-8ef2-b0f5cda0b1d2", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509854319900, "endTime": 152509856265800}, "additional": {"logType": "info", "children": [], "durationId": "0fed9e71-4165-49f5-93f6-d882c74bbde2"}}, {"head": {"id": "b80c5896-bf26-4215-8655-f4b3b367a7d7", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509861272700, "endTime": 152509870425100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0d44eba9-5c7a-47d3-926b-3191e3bbe449", "logId": "93469ca3-cb1a-4381-b93e-d8fe10efca27"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d44eba9-5c7a-47d3-926b-3191e3bbe449", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509857853100}, "additional": {"logType": "detail", "children": [], "durationId": "b80c5896-bf26-4215-8655-f4b3b367a7d7"}}, {"head": {"id": "e90eea8e-9dc2-441c-b9a4-5defad874bd8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509858936000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29277655-4c73-4a51-bcc2-c307aa57bb8e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509859065200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "467a5571-6392-4e43-914f-4fd97a23531b", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509861324500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91d6e789-1aa2-4887-85be-62a621b1b501", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509870177900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba4425b5-d185-47bb-afa4-61c5a8261c2e", "name": "entry : default@ProcessRouterMap cost memory 0.23663330078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509870352800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93469ca3-cb1a-4381-b93e-d8fe10efca27", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509861272700, "endTime": 152509870425100}, "additional": {"logType": "info", "children": [], "durationId": "b80c5896-bf26-4215-8655-f4b3b367a7d7"}}, {"head": {"id": "78f0bdb8-e715-433e-849a-817a93a070f9", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509874968000, "endTime": 152509881147100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "cbc8a54b-ed90-4d12-b7fc-6b3780ca9639", "logId": "9593be22-ad45-4f37-aedb-1717c044b94a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cbc8a54b-ed90-4d12-b7fc-6b3780ca9639", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509873664600}, "additional": {"logType": "detail", "children": [], "durationId": "78f0bdb8-e715-433e-849a-817a93a070f9"}}, {"head": {"id": "97ad39ec-297e-4d26-bbfb-0af05ad7e542", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509874747100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4dbe619-0df1-4c8c-85d8-bb3adf1e7d61", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509874879800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07ddc5a0-103c-4e26-ae27-ea568da13fd5", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509874976600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d336219-a191-4c77-9647-d0f0ace1c50c", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509875075300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d027bbe4-3d5f-457f-bb52-cc2ce53f98b9", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509879346400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02416485-fee9-4781-ae7d-204f3ce46fbe", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509879542700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27fd7588-0a78-4c4c-aeb5-6428e3e9e078", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509879709400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8e4d768-de21-4f01-8538-76b278e3c85a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509879813100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3421ce9d-5da5-4781-bb84-2c6419a05baf", "name": "entry : default@ProcessStartupConfig cost memory 0.26386260986328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509880942400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a1d5ad4-71c1-4fcd-bda5-5aff1d4cf646", "name": "runTaskFromQueue task cost before running: 436 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509881074900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9593be22-ad45-4f37-aedb-1717c044b94a", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509874968000, "endTime": 152509881147100, "totalTime": 6077800}, "additional": {"logType": "info", "children": [], "durationId": "78f0bdb8-e715-433e-849a-817a93a070f9"}}, {"head": {"id": "19aa569b-0215-4ccb-bede-a48ca34bb3c9", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509886555300, "endTime": 152509887948100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f1eb878a-ab77-411d-8886-083a315a49fd", "logId": "f8b3b5f3-bac6-4bef-bbdc-be223712ab12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1eb878a-ab77-411d-8886-083a315a49fd", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509884450000}, "additional": {"logType": "detail", "children": [], "durationId": "19aa569b-0215-4ccb-bede-a48ca34bb3c9"}}, {"head": {"id": "a00dacac-b68b-40ea-be42-c271cf93120f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509885525700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9cbdef1-1b71-4415-99e3-d6e4ef159097", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509885732000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1743ee5b-6bc4-49b5-9ef0-5a7f0210689d", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509886566200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26d4c316-c203-49bd-aa95-aeaa022e14e0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509886720300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e0772b6-2f7e-4f59-b503-0decc2c99aa9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509886777700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5063be02-9d72-4c9c-9d95-350467cf6bb6", "name": "entry : default@BuildNativeWithNinja cost memory 0.05950927734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509887746500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c3dffe2-19fd-4d65-97a1-71741b71266f", "name": "runTaskFromQueue task cost before running: 443 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509887890000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8b3b5f3-bac6-4bef-bbdc-be223712ab12", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509886555300, "endTime": 152509887948100, "totalTime": 1311000}, "additional": {"logType": "info", "children": [], "durationId": "19aa569b-0215-4ccb-bede-a48ca34bb3c9"}}, {"head": {"id": "34712930-213a-40ea-b794-dcead2da1c74", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509894300300, "endTime": 152509901076200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5d6f0ecb-8f39-4de8-a224-431c43d73bca", "logId": "e252cfd4-8b10-4002-bc10-29e297b92356"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d6f0ecb-8f39-4de8-a224-431c43d73bca", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509890417900}, "additional": {"logType": "detail", "children": [], "durationId": "34712930-213a-40ea-b794-dcead2da1c74"}}, {"head": {"id": "e39b21aa-adb4-40d0-90e2-c66878ccfe12", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509891459900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57f6b5ec-5d8a-4e1c-af4a-95c65b406b46", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509891588000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4948ab7-93f4-4cc1-946a-f698e8814d4d", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509892855500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf8d67c9-59bc-4505-9503-fe91058af840", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509896514800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31eed882-a0aa-494f-ade1-414ddbddf997", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509899105100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31fc91f1-e446-45d4-b5f8-0c8794f43f2c", "name": "entry : default@ProcessResource cost memory 0.16426849365234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509899266500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e252cfd4-8b10-4002-bc10-29e297b92356", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509894300300, "endTime": 152509901076200}, "additional": {"logType": "info", "children": [], "durationId": "34712930-213a-40ea-b794-dcead2da1c74"}}, {"head": {"id": "91aefc8d-1863-4197-810f-3033ae1970f4", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509912258300, "endTime": 152509935284700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d5fb2aab-d868-4607-ae05-4206575169d8", "logId": "55417b34-7a5b-45f7-92c7-adead8d06000"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5fb2aab-d868-4607-ae05-4206575169d8", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509906810800}, "additional": {"logType": "detail", "children": [], "durationId": "91aefc8d-1863-4197-810f-3033ae1970f4"}}, {"head": {"id": "1e405388-cfcf-43a5-bc4b-f65b0e63dcf2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509907881600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5f30005-e4e9-47b8-944b-797920a6ef10", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509908004100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf78b375-7e74-4271-99c7-26faa8281b74", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509912283500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0a67794-b1fe-45dc-8afe-aee1e15612f9", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509935066400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e5788a0-fa6e-4281-aaea-62074e106091", "name": "entry : default@GenerateLoaderJson cost memory 0.892974853515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509935220900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55417b34-7a5b-45f7-92c7-adead8d06000", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509912258300, "endTime": 152509935284700}, "additional": {"logType": "info", "children": [], "durationId": "91aefc8d-1863-4197-810f-3033ae1970f4"}}, {"head": {"id": "ba6d01ad-f6c2-49a5-bd48-6b19b1e017df", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509947115400, "endTime": 152509951545000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "68d3e95b-1efb-41d1-9dce-433220787c0d", "logId": "6fc71786-2713-4679-bad2-c8a810302168"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68d3e95b-1efb-41d1-9dce-433220787c0d", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509944905800}, "additional": {"logType": "detail", "children": [], "durationId": "ba6d01ad-f6c2-49a5-bd48-6b19b1e017df"}}, {"head": {"id": "a7fc7342-bf3e-4528-bf0f-cc6fa7efd916", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509946192900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0d770c-db11-4519-8623-00d0167916f4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509946326400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41b2e1df-24f5-453c-a193-ecaa481aef05", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509947127900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a359748-7c26-41d0-9a87-e738cc7750af", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509951345900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4e6e097-12cb-4f7f-9020-8890dab1761f", "name": "entry : default@ProcessLibs cost memory 0.14391326904296875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509951480400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fc71786-2713-4679-bad2-c8a810302168", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509947115400, "endTime": 152509951545000}, "additional": {"logType": "info", "children": [], "durationId": "ba6d01ad-f6c2-49a5-bd48-6b19b1e017df"}}, {"head": {"id": "7616e975-bb53-498a-ae33-39a9f38c16fd", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509958594200, "endTime": 152509986702500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "b8496a3a-1cea-4c98-8753-ed368e6432af", "logId": "75b80df1-5426-4739-901a-2ed6b3c6d9e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8496a3a-1cea-4c98-8753-ed368e6432af", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509953563200}, "additional": {"logType": "detail", "children": [], "durationId": "7616e975-bb53-498a-ae33-39a9f38c16fd"}}, {"head": {"id": "a17a7254-9d82-4e47-b185-14f8650cc3a6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509954501100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae89460a-aa74-49ec-8b92-6dd31e8c12c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509954609300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfd83189-29fd-4952-a090-1473ab6b421f", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509955581000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b04ea8de-f74a-46d5-b927-7ba0cd399acd", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509958622800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a52f71d-003b-4246-97ca-66ccc8441e43", "name": "Incremental task entry:default@CompileResource pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509986334400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65810eb5-5f9a-4930-932c-8f0e8dc197de", "name": "entry : default@CompileResource cost memory 1.3252334594726562", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509986564600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75b80df1-5426-4739-901a-2ed6b3c6d9e3", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509958594200, "endTime": 152509986702500}, "additional": {"logType": "info", "children": [], "durationId": "7616e975-bb53-498a-ae33-39a9f38c16fd"}}, {"head": {"id": "b3ba0f33-6f13-442d-8e1c-d32b34871488", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509994899500, "endTime": 152509997627400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "eb76afc6-301c-46a6-843b-4d773636822a", "logId": "39503990-5e5e-45db-a5e1-61d35b06da9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb76afc6-301c-46a6-843b-4d773636822a", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509990224600}, "additional": {"logType": "detail", "children": [], "durationId": "b3ba0f33-6f13-442d-8e1c-d32b34871488"}}, {"head": {"id": "6c702ea3-a390-4e00-928c-82e94e43c903", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509991657900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f112f5f-260a-467e-85ce-d22df0f0aacf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509991772900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f75f4aac-abda-4973-872a-fa3435a43e29", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509994934100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fddf7920-a55a-475c-b112-63bfa673aec9", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509995602500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc3ccb7d-f836-47af-a024-ac91ab8d3b8e", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509997400300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85da0512-46aa-4e7d-8bf2-5edfff48c98d", "name": "entry : default@DoNativeStrip cost memory 0.08040618896484375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509997553400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39503990-5e5e-45db-a5e1-61d35b06da9e", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509994899500, "endTime": 152509997627400}, "additional": {"logType": "info", "children": [], "durationId": "b3ba0f33-6f13-442d-8e1c-d32b34871488"}}, {"head": {"id": "f75771c9-f99e-40be-a492-ab2bf4f401b1", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510004308600, "endTime": 152520358926200}, "additional": {"children": ["6434c232-077e-425a-9699-bf0a38834656", "1794787b-5b0b-45e2-8a1b-9421724ed48c"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "e1e7b67a-1e5e-47c6-81df-8a573f5a48e3", "logId": "4ff8537d-9924-4a6d-a199-63409141a724"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1e7b67a-1e5e-47c6-81df-8a573f5a48e3", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509999189600}, "additional": {"logType": "detail", "children": [], "durationId": "f75771c9-f99e-40be-a492-ab2bf4f401b1"}}, {"head": {"id": "068bf741-4855-4bc6-893a-bad9d62335ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510000165100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ede1ebf1-061c-4fbc-8648-105b39464a85", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510000281200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61c50997-d273-42e9-8232-9d0d17a69cfd", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510004321700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7da40f60-2357-43fa-be02-cd7241d60b7c", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510004486400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db29fcc8-c71c-41c4-a42e-db0a9ded76d8", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510035285200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd5b92c1-fe59-4430-808e-b9fc764521f0", "name": "default@CompileArkTS work[2] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510037683100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6434c232-077e-425a-9699-bf0a38834656", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152510040114700, "endTime": 152520358667400}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f75771c9-f99e-40be-a492-ab2bf4f401b1", "logId": "1efe9685-f6f9-4bb2-a92d-7d2a7c401500"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16449bb1-b6d8-42a7-a839-2686<PERSON><PERSON><PERSON><PERSON>", "name": "default@CompileArkTS work[2] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510038629900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99134d39-46d3-431b-bb9d-a93fb5a3f857", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510038728200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bed02118-d3ff-43b9-8a5d-d7f4d976550f", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510038773200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d5902bc-9481-4f6d-9d27-6f94dd410796", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510038803700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eed281e-7ba6-4b8e-8973-3d2ee87f7f9b", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510038832200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64d60275-97b6-4012-8321-6de73441a623", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510038859000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3675641c-1b00-47e0-ab23-6d4a821ec2f7", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510038885600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20e4e1a9-c590-427b-ae72-dbbbd5b383af", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510038912300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0272bbf2-a7b7-4edd-8c31-8bb07a023c8e", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510038937900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7651107e-403d-4be9-805b-c8f5cbac5e5e", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510038964900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "459acaea-7d71-4009-8c9f-c6fa345ee3e4", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510038991200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9821e2db-81df-468b-9531-4a22aa787717", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510039021800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9986dd05-ab09-4010-98f4-90fe44f90992", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510039052500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fc45d70-7e37-44b5-9618-420f4689c2ed", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510039080700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fd5fc5f-ea7f-424e-b0cc-1bb27834d823", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510039123400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a278e093-27ff-48ef-a144-1f1b546ec0a3", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510039150900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d0b8ce5-f96d-4dcb-bd7d-2a3f3382fe3c", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510039194100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "966f5d9c-92c6-4cbd-b3e3-58a1b9354bf9", "name": "default@CompileArkTS work[2] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510040132600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9bb01fd-0e0b-40f8-98aa-04d1caf42642", "name": "default@CompileArkTS work[2] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510040233200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fd716aa-1b9d-4e45-a874-ec58030b58c6", "name": "CopyResources startTime: 152510040277100", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510040279400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb3b0eee-04c7-4da0-a22e-fa54821cedba", "name": "default@CompileArkTS work[3] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510040339900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1794787b-5b0b-45e2-8a1b-9421724ed48c", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 152511132726300, "endTime": 152511146726600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f75771c9-f99e-40be-a492-ab2bf4f401b1", "logId": "e0b082b6-f84f-41ca-a24b-5b6094b3bed6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1cd08ff9-a2b6-42c6-9bec-57d60c218691", "name": "default@CompileArkTS work[3] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510041204200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb50e191-71ea-4cda-8a5b-68032fab6c7b", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510041291700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dd3c964-b40f-4466-acb5-272fd4899f00", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510041343200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fab0664-2c5a-4376-b976-cdeb21de7b50", "name": "default@CompileArkTS work[3] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510042085400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "374acf4d-c34a-4174-88af-981f6d01bade", "name": "default@CompileArkTS work[3] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510042170600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b46152-cf65-4bb0-a1ae-db6a66482932", "name": "entry : default@CompileArkTS cost memory 1.793609619140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510042247800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06d2e71f-9ce4-4ad7-8409-88d976ac677b", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510053048400, "endTime": 152510067209000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "2a6974af-5b83-41a6-9100-9ff219a52c02", "logId": "e76f38cd-39e4-4936-b2c5-3ba7294e5d20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a6974af-5b83-41a6-9100-9ff219a52c02", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510044231800}, "additional": {"logType": "detail", "children": [], "durationId": "06d2e71f-9ce4-4ad7-8409-88d976ac677b"}}, {"head": {"id": "e9630ff2-505d-408b-b4d3-b7b9c92b3baa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510047131600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae9d87f7-61ae-4dc8-9dbe-92bd588c9ac7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510047326100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deb3ae47-a6b2-4de4-a12b-09665ebd41b9", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510053070800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8594b54f-450a-4815-a6b4-08d526c6399b", "name": "entry : default@BuildJS cost memory 0.344573974609375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510066923800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f074fe7-dc75-4cc7-a461-6c7f6d183d04", "name": "runTaskFromQueue task cost before running: 622 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510067119900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e76f38cd-39e4-4936-b2c5-3ba7294e5d20", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510053048400, "endTime": 152510067209000, "totalTime": 14029300}, "additional": {"logType": "info", "children": [], "durationId": "06d2e71f-9ce4-4ad7-8409-88d976ac677b"}}, {"head": {"id": "8310e191-1da0-41cb-9990-30625930637e", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510073885100, "endTime": 152510080550000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3a65ea31-3134-4c05-9713-3ad9e4045a12", "logId": "0b116dbd-65d0-4d3a-a46e-b0e229a0a2bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a65ea31-3134-4c05-9713-3ad9e4045a12", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510069317300}, "additional": {"logType": "detail", "children": [], "durationId": "8310e191-1da0-41cb-9990-30625930637e"}}, {"head": {"id": "111aa02e-1c75-41d9-ad92-476d1b6cbb4e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510070318500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16e412e0-c84d-4e6b-b743-9a35c5239b7b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510070509900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d91ad77b-eb28-4e05-8bb8-cc615ca22ab7", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510073902600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "414348d0-0125-47de-92d3-0757d2577792", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510074884900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5010821d-a3e8-45c1-9b6b-79702a82d666", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510078626400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f03611ab-5b19-41e8-84da-1ef8a2749b3d", "name": "entry : default@CacheNativeLibs cost memory -6.988838195800781", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510080439100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b116dbd-65d0-4d3a-a46e-b0e229a0a2bf", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510073885100, "endTime": 152510080550000}, "additional": {"logType": "info", "children": [], "durationId": "8310e191-1da0-41cb-9990-30625930637e"}}, {"head": {"id": "a42acf48-7d4a-4394-ab9f-9aaf4a372817", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152511147301900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dce0405f-3992-48f4-8900-988a892d5514", "name": "CopyResources is end, endTime: 152511147479800", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152511147486100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5323b4c-534a-4bae-a916-445ec70676d2", "name": "default@CompileArkTS work[3] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152511147670400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0b082b6-f84f-41ca-a24b-5b6094b3bed6", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 152511132726300, "endTime": 152511146726600}, "additional": {"logType": "info", "children": [], "durationId": "1794787b-5b0b-45e2-8a1b-9421724ed48c", "parent": "4ff8537d-9924-4a6d-a199-63409141a724"}}, {"head": {"id": "2a03c6fe-acd1-4f0a-9c34-be35788c443b", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152511147874900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c69b4c2-8d09-4e9a-a341-6f7b12d16584", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520358433200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93662e6d-e12a-4b24-8f62-ece246ee731f", "name": "default@CompileArkTS work[2] failed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520358772300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1efe9685-f6f9-4bb2-a92d-7d2a7c401500", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152510040114700, "endTime": 152520358667400}, "additional": {"logType": "error", "children": [], "durationId": "6434c232-077e-425a-9699-bf0a38834656", "parent": "4ff8537d-9924-4a6d-a199-63409141a724"}}, {"head": {"id": "4ff8537d-9924-4a6d-a199-63409141a724", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152510004308600, "endTime": 152520358926200}, "additional": {"logType": "error", "children": ["1efe9685-f6f9-4bb2-a92d-7d2a7c401500", "e0b082b6-f84f-41ca-a24b-5b6094b3bed6"], "durationId": "f75771c9-f99e-40be-a492-ab2bf4f401b1"}}, {"head": {"id": "db968a52-7cf9-4c3a-aaeb-0efd324e025e", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520359083300}, "additional": {"logType": "debug", "children": [], "durationId": "f75771c9-f99e-40be-a492-ab2bf4f401b1"}}, {"head": {"id": "fa137a16-6436-4719-aec5-27ddd4e7c28a", "name": "ERROR: stacktrace = Error: ArkTS Compiler Error\r\n\u001b[33m1 WARN: \u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/HomePage.ets:19:1\n It's not a recommended way to export struct with '@Entry' decorator, which may cause ACE Engine error in component preview mode.\u001b[39m\u001b[39m\r\n\u001b[33m2 WARN: \u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/ProfilePage.ets:14:1\n It's not a recommended way to export struct with '@Entry' decorator, which may cause ACE Engine error in component preview mode.\u001b[39m\u001b[39m\r\n\u001b[33m3 WARN: \u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/ProfilePage.ets:14:1\n It's not a recommended way to export struct with '@Entry' decorator, which may cause ACE Engine error in component preview mode.\u001b[39m\u001b[39m\r\n\u001b[33m4 WARN: \u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/CategoryListPage.ets:15:1\n It's not a recommended way to export struct with '@Entry' decorator, which may cause ACE Engine error in component preview mode.\u001b[39m\u001b[39m\r\n\u001b[33m5 WARN: \u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedPage.ets:17:1\n It's not a recommended way to export struct with '@Entry' decorator, which may cause ACE Engine error in component preview mode.\u001b[39m\u001b[39m\r\n\u001b[33m6 WARN: \u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/SearchPage.ets:32:1\n It's not a recommended way to export struct with '@Entry' decorator, which may cause ACE Engine error in component preview mode.\u001b[39m\u001b[39m\r\n\u001b[31m1 ERROR: \u001b[31m10905231 ArkTS Compiler Error\r\nError Message: A page can't contain more than one '@Entry' decorator. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/ProfilePage.ets\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m2 ERROR: \u001b[31m10905402 ArkTS Compiler Error\r\nError Message: A page configured in 'main_pages.json or build-profile.json5' must have one and only one '@Entry' decorator. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/ProfilePage.ets\r\n\r\n* Try the following:\r\n  > Please make sure that the splash page has one and only one '@Entry' decorator.\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:3 WARN:6}\u001b[39m\n    at runArkPack (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5479)", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520359800900}, "additional": {"logType": "debug", "children": [], "durationId": "f75771c9-f99e-40be-a492-ab2bf4f401b1"}}, {"head": {"id": "84a1b06a-f8ae-44d0-9971-b85d2d869c46", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520372308400, "endTime": 152520372464900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e38288a6-8a39-4597-97ff-e2f0e3a6e9ed", "logId": "efefd9c1-dd48-4326-b98f-3980dee3b278"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "efefd9c1-dd48-4326-b98f-3980dee3b278", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520372308400, "endTime": 152520372464900}, "additional": {"logType": "info", "children": [], "durationId": "84a1b06a-f8ae-44d0-9971-b85d2d869c46"}}, {"head": {"id": "94d2552a-450e-4fea-87fc-cd926b321702", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152509445268800, "endTime": 152520372865000}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 19, "second": 10}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "18856883-9bd9-4133-9327-12525bc8d927", "name": "BUILD FAILED in 10 s 928 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520372905100}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "81757faa-dc89-405e-9459-09a5f4e2a56f", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520373141500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26e77f64-54fa-45d8-8f7f-9daef50db29b", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520373250100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1e062fb-9eb8-403b-9a0b-1a101cad21f5", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520373796400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e188331a-0f98-4b1d-b538-2292f66d566e", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520373868400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f07f96e1-2fe2-40a1-8f71-0e377f8f5cc1", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520373905600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e82c7c4f-d935-41fc-8e97-5dc75d2353d6", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520373935900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41c8a71d-f2c5-4c67-a659-257bce8c81b5", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520373963000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08172984-e64c-47c3-8581-2e4199a6b8a2", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520374519700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ea911e2-01ee-455b-88cb-2d7ea17517b9", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520374731400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e054d428-a9b4-4c6c-a21e-5c5ebfda9c22", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520374783100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6be83fa-5fb4-4ab1-94a9-7c1c82a35c4d", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520374822700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9260a27f-7828-4967-b508-13fa3ba36320", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520374850000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4fa0f63-d16d-4ed3-9347-4547971fda05", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520374875400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adb6fd9d-672d-49c7-9409-9e7d76d4fefd", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520376054400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "102fdc49-8e45-41d9-9703-c6ac23fe8c6a", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520376447800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b405401-8690-4217-833b-30f059f0705b", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520376769900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df751ae2-c6ab-4449-abbf-a1d967391a45", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520376853000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4afafed2-cbcb-48ae-8454-cff4effe4d6e", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520376915000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a452371-da76-4b4c-83fb-7ab5d4606096", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520376949000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6abfd860-ac49-416d-a22d-4b73c3e04eb2", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520376974900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7880a69b-345d-42bb-8577-b755718ec245", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520377001600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09de5d7c-3ae5-4e38-98ab-16f62d62edeb", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520379883200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a16129d-7cb1-449b-8679-2001645606d4", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520380583700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d9b33ec-cf30-4b41-83c4-3bb1a7490b44", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520380960600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3b3ecae-37e9-4e4d-b668-6cf0b4bb4a23", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520381189700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b5e5f05-5e9a-4e2e-b55d-ae7113c7684f", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520381377000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7274d96-bd98-43ed-8cb8-c66ebf7473c1", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520382024100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac50c2ed-0013-44fd-a521-c4226a6d9d4e", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520388497900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf6784c1-b33c-4807-85ef-bc084bf0e2b9", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520388744100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03232465-afc5-4237-9447-60a59f1403e2", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520389133500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36d5f32c-7e41-4f96-97a5-57aa5cd14a7b", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520390093500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66f4c72e-eba2-457b-966b-82710cd9420f", "name": "Incremental task entry:default@CompileArkTS post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520390695300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "793ce6f9-542c-46f4-be42-538b7405f35c", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520392663900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2723b1cc-8d20-42ae-a5a1-c3a98a068faf", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520393272100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db9a1fa1-24db-42dc-a42e-b6114d78bad6", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520393625300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c3e22c1-d936-4617-a406-53acb93d6f75", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520393967100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa077b4e-eb6b-4eae-98b6-cea0641a7a7b", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520394554500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e61f6e75-e9f5-4c34-81e7-314030ac660f", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520395350300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c43a5a9e-f48b-42af-a13f-4e009231ac37", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520396409800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9098a9b-1f69-4c01-a9f0-e65a7f51a554", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520396763900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fcb6dec-7f37-4fd6-92a7-ac327fc0f51b", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520396831900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}