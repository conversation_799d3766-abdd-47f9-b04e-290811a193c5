{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "3cdd34e5-517a-40bf-b543-9c3c14ab27de", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427328292600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b68959b7-dc30-4b34-8984-058ced98ad49", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427328507000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cfced55-829c-42bd-8664-086d2e799030", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427352935300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "539be855-1538-44a9-b435-97fcf914c53d", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427353373100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45b4f0a9-dc03-4399-bf42-6635da076f66", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427354769000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d64f3bf4-9eaa-4119-8b89-6ee24fa2419f", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427355099100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a20e5b0f-a14c-449a-b19f-d23b8bca4b69", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432932683000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7024d419-6aa3-49aa-a790-410c1b9aec72", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432937660800, "endTime": 153433214804600}, "additional": {"children": ["1327a9c7-d8f6-412e-b94f-1a4679ede1e3", "9238fb28-ce50-471b-88cb-bdfc5fba91ba", "f5d5b777-9679-4b08-8d30-1479690fcc9c", "c8aff786-5b75-4a33-a61a-1636d3dbbbe7", "6ef08134-9e55-4447-a649-799c8dca6c43", "b0c13a28-e721-4407-91fa-f5b23acf6bf9", "b41e48df-3059-4a99-9d86-d6e8754d11b1"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "6292c287-9f4a-4ac2-b21e-bb9ec8b0d829"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1327a9c7-d8f6-412e-b94f-1a4679ede1e3", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432937661900, "endTime": 153432952454600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7024d419-6aa3-49aa-a790-410c1b9aec72", "logId": "791334ce-e884-4710-96f7-69a8233b64e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9238fb28-ce50-471b-88cb-bdfc5fba91ba", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432952470000, "endTime": 153433212463100}, "additional": {"children": ["549effd3-8525-472a-8482-1f95f085144d", "033e91f2-e66d-4d6b-b368-2066e35b95be", "2aabe704-c39c-4d59-a395-8ea0fc7d5eda", "f66a7f5c-0bde-4490-862f-cde4aaa11b44", "68690d11-61ae-4731-8c9b-e55fa921a41f", "66f059e4-d8a8-4342-b135-b9eb325f507c", "ec0171e9-6a07-4c34-aecb-988d1e0219c9", "e9e26f34-8653-46c5-bc06-e42a63c5f903", "61a63973-b454-4f52-8195-e6edd18139f9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7024d419-6aa3-49aa-a790-410c1b9aec72", "logId": "ee35dbf8-60cd-47f3-ae36-45b54ebe7d04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5d5b777-9679-4b08-8d30-1479690fcc9c", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433212511300, "endTime": 153433214765600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7024d419-6aa3-49aa-a790-410c1b9aec72", "logId": "84b96a77-a873-473b-8af6-b019802a9bf7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8aff786-5b75-4a33-a61a-1636d3dbbbe7", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433214779300, "endTime": 153433214794700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7024d419-6aa3-49aa-a790-410c1b9aec72", "logId": "6a152169-c57e-47b0-b2ba-521a38246b06"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ef08134-9e55-4447-a649-799c8dca6c43", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432941444700, "endTime": 153432941603300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7024d419-6aa3-49aa-a790-410c1b9aec72", "logId": "38c9b86c-38ba-4524-91ba-058d461c45d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38c9b86c-38ba-4524-91ba-058d461c45d1", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432941444700, "endTime": 153432941603300}, "additional": {"logType": "info", "children": [], "durationId": "6ef08134-9e55-4447-a649-799c8dca6c43", "parent": "6292c287-9f4a-4ac2-b21e-bb9ec8b0d829"}}, {"head": {"id": "b0c13a28-e721-4407-91fa-f5b23acf6bf9", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432946295000, "endTime": 153432946318900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7024d419-6aa3-49aa-a790-410c1b9aec72", "logId": "23b7f5a0-cb02-43ff-8ce9-2524378e26dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23b7f5a0-cb02-43ff-8ce9-2524378e26dc", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432946295000, "endTime": 153432946318900}, "additional": {"logType": "info", "children": [], "durationId": "b0c13a28-e721-4407-91fa-f5b23acf6bf9", "parent": "6292c287-9f4a-4ac2-b21e-bb9ec8b0d829"}}, {"head": {"id": "bc442d47-745f-4252-824a-ed4e4ead6ef2", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432946456500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7fffc8a-d496-4c02-8716-877913f51317", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432952297200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "791334ce-e884-4710-96f7-69a8233b64e0", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432937661900, "endTime": 153432952454600}, "additional": {"logType": "info", "children": [], "durationId": "1327a9c7-d8f6-412e-b94f-1a4679ede1e3", "parent": "6292c287-9f4a-4ac2-b21e-bb9ec8b0d829"}}, {"head": {"id": "549effd3-8525-472a-8482-1f95f085144d", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432958782700, "endTime": 153432958795500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9238fb28-ce50-471b-88cb-bdfc5fba91ba", "logId": "c0dff08c-6b03-42ae-b3b3-328d368bcb8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "033e91f2-e66d-4d6b-b368-2066e35b95be", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432958827400, "endTime": 153432962613000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9238fb28-ce50-471b-88cb-bdfc5fba91ba", "logId": "07e4f765-e500-45b7-861c-52164<PERSON><PERSON><PERSON>b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2aabe704-c39c-4d59-a395-8ea0fc7d5eda", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432962625800, "endTime": 153433069971500}, "additional": {"children": ["5e311a22-a348-4125-be15-5dbdcbb1cd73", "7a896caa-9995-43c4-9e46-b0ac126498b8", "42b4b8aa-81a6-4a67-926e-2c2149afebfb"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9238fb28-ce50-471b-88cb-bdfc5fba91ba", "logId": "729d5acd-98dd-455b-a375-9e51aec0872f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f66a7f5c-0bde-4490-862f-cde4aaa11b44", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433069990300, "endTime": 153433096970300}, "additional": {"children": ["8877f5d6-eac4-43b3-b970-fd375a96242d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9238fb28-ce50-471b-88cb-bdfc5fba91ba", "logId": "b7970ece-7709-4281-98d1-d781716ccf69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68690d11-61ae-4731-8c9b-e55fa921a41f", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433096986700, "endTime": 153433163326500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9238fb28-ce50-471b-88cb-bdfc5fba91ba", "logId": "4ecf7548-1707-4734-a4ad-b48ea0497432"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66f059e4-d8a8-4342-b135-b9eb325f507c", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433164906000, "endTime": 153433181651000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9238fb28-ce50-471b-88cb-bdfc5fba91ba", "logId": "90fd9ffa-3c84-49f2-bec5-bb2ddc7f75e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec0171e9-6a07-4c34-aecb-988d1e0219c9", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433181685800, "endTime": 153433212124500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9238fb28-ce50-471b-88cb-bdfc5fba91ba", "logId": "d26c6834-0d2b-40f8-8457-385041e0a3db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9e26f34-8653-46c5-bc06-e42a63c5f903", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433212153200, "endTime": 153433212422100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9238fb28-ce50-471b-88cb-bdfc5fba91ba", "logId": "c3309ce5-484f-4b14-95d6-7628aef74380"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0dff08c-6b03-42ae-b3b3-328d368bcb8d", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432958782700, "endTime": 153432958795500}, "additional": {"logType": "info", "children": [], "durationId": "549effd3-8525-472a-8482-1f95f085144d", "parent": "ee35dbf8-60cd-47f3-ae36-45b54ebe7d04"}}, {"head": {"id": "07e4f765-e500-45b7-861c-52164<PERSON><PERSON><PERSON>b", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432958827400, "endTime": 153432962613000}, "additional": {"logType": "info", "children": [], "durationId": "033e91f2-e66d-4d6b-b368-2066e35b95be", "parent": "ee35dbf8-60cd-47f3-ae36-45b54ebe7d04"}}, {"head": {"id": "5e311a22-a348-4125-be15-5dbdcbb1cd73", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432963042500, "endTime": 153432963181300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2aabe704-c39c-4d59-a395-8ea0fc7d5eda", "logId": "cb1b3dbb-1894-4b20-a56a-2095793deedb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb1b3dbb-1894-4b20-a56a-2095793deedb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432963042500, "endTime": 153432963181300}, "additional": {"logType": "info", "children": [], "durationId": "5e311a22-a348-4125-be15-5dbdcbb1cd73", "parent": "729d5acd-98dd-455b-a375-9e51aec0872f"}}, {"head": {"id": "7a896caa-9995-43c4-9e46-b0ac126498b8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432965871900, "endTime": 153433068612200}, "additional": {"children": ["9b1284c3-fb55-4039-b8d2-481213af3e5e", "a9e40674-8ffd-4e7d-a8bd-91aa81c31daf"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2aabe704-c39c-4d59-a395-8ea0fc7d5eda", "logId": "c1bb236b-0059-4eb1-aa1b-cae42ab66dc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b1284c3-fb55-4039-b8d2-481213af3e5e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432965873300, "endTime": 153432971732700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a896caa-9995-43c4-9e46-b0ac126498b8", "logId": "11b6d04f-70ed-404d-92a6-9c47420eaa1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9e40674-8ffd-4e7d-a8bd-91aa81c31daf", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432971750300, "endTime": 153433068596800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a896caa-9995-43c4-9e46-b0ac126498b8", "logId": "cd039f4f-bf14-4f9b-b62c-91bbbea554d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5bcd7391-29f0-4fca-aa45-5163afe8f6b2", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432965878300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77f07854-f235-4d45-b17a-b892ad500221", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432971589000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11b6d04f-70ed-404d-92a6-9c47420eaa1e", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432965873300, "endTime": 153432971732700}, "additional": {"logType": "info", "children": [], "durationId": "9b1284c3-fb55-4039-b8d2-481213af3e5e", "parent": "c1bb236b-0059-4eb1-aa1b-cae42ab66dc2"}}, {"head": {"id": "6768cbb4-e463-4c63-a2d5-dd16f2b87445", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432971766100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e74e5d09-f9b5-4e9b-ab36-67059b88d843", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432982168600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4a11990-febd-4030-ac1c-fe5aaaab56eb", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432982308300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8892a4e1-a835-4480-a677-232fa9e6a593", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432982492300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c571d86-cea1-4296-8f77-6fce8b2f506c", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432982614100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a5ae8d7-6a98-46b0-865f-dc0391299c04", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432985007400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08b686d2-3828-49e0-8b13-6d8b2f9c8d13", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433002350100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66ed427c-92b6-4eb0-94d6-4ffa655d0e80", "name": "Sdk init in 30 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433024114900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4b84b40-ed7a-4146-929b-3937bb06ac8b", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433024371000}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 34, "second": 22}, "markType": "other"}}, {"head": {"id": "f6400d63-8f62-413f-a10c-cabd1e46a253", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433024383300}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 34, "second": 22}, "markType": "other"}}, {"head": {"id": "dfa4ff11-61a5-4329-972a-ddfd6c82ed82", "name": "Project task initialization takes 40 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433068204800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c94062c-241a-4255-aa52-5dbd8819cdbe", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433068366300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4275ec24-5f49-4008-98b2-62e239d87ed8", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433068456500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d993a68-71f3-4135-9cc3-9539391b456b", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433068533700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd039f4f-bf14-4f9b-b62c-91bbbea554d4", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432971750300, "endTime": 153433068596800}, "additional": {"logType": "info", "children": [], "durationId": "a9e40674-8ffd-4e7d-a8bd-91aa81c31daf", "parent": "c1bb236b-0059-4eb1-aa1b-cae42ab66dc2"}}, {"head": {"id": "c1bb236b-0059-4eb1-aa1b-cae42ab66dc2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432965871900, "endTime": 153433068612200}, "additional": {"logType": "info", "children": ["11b6d04f-70ed-404d-92a6-9c47420eaa1e", "cd039f4f-bf14-4f9b-b62c-91bbbea554d4"], "durationId": "7a896caa-9995-43c4-9e46-b0ac126498b8", "parent": "729d5acd-98dd-455b-a375-9e51aec0872f"}}, {"head": {"id": "42b4b8aa-81a6-4a67-926e-2c2149afebfb", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433069927500, "endTime": 153433069947300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2aabe704-c39c-4d59-a395-8ea0fc7d5eda", "logId": "31d2797b-8d8e-408c-acda-ffabd9758f05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31d2797b-8d8e-408c-acda-ffabd9758f05", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433069927500, "endTime": 153433069947300}, "additional": {"logType": "info", "children": [], "durationId": "42b4b8aa-81a6-4a67-926e-2c2149afebfb", "parent": "729d5acd-98dd-455b-a375-9e51aec0872f"}}, {"head": {"id": "729d5acd-98dd-455b-a375-9e51aec0872f", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432962625800, "endTime": 153433069971500}, "additional": {"logType": "info", "children": ["cb1b3dbb-1894-4b20-a56a-2095793deedb", "c1bb236b-0059-4eb1-aa1b-cae42ab66dc2", "31d2797b-8d8e-408c-acda-ffabd9758f05"], "durationId": "2aabe704-c39c-4d59-a395-8ea0fc7d5eda", "parent": "ee35dbf8-60cd-47f3-ae36-45b54ebe7d04"}}, {"head": {"id": "8877f5d6-eac4-43b3-b970-fd375a96242d", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433071136800, "endTime": 153433096959900}, "additional": {"children": ["d6d4405b-2c7e-4e61-a773-dad73ee87fa1", "c61de8bf-2246-4142-96c8-cb09c7678d10", "8726ee92-f194-4b5f-8f17-07d0af702675"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f66a7f5c-0bde-4490-862f-cde4aaa11b44", "logId": "f7d1a831-5f5d-4320-85ab-4a134deda3a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6d4405b-2c7e-4e61-a773-dad73ee87fa1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433076178800, "endTime": 153433076197700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8877f5d6-eac4-43b3-b970-fd375a96242d", "logId": "ef1119b6-13c1-4394-81dc-2c91ae8446f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef1119b6-13c1-4394-81dc-2c91ae8446f6", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433076178800, "endTime": 153433076197700}, "additional": {"logType": "info", "children": [], "durationId": "d6d4405b-2c7e-4e61-a773-dad73ee87fa1", "parent": "f7d1a831-5f5d-4320-85ab-4a134deda3a0"}}, {"head": {"id": "c61de8bf-2246-4142-96c8-cb09c7678d10", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433079050000, "endTime": 153433095573500}, "additional": {"children": ["76558399-9969-48fb-8328-43dd0f674ba6", "e386c2f9-92e5-4066-bd30-381259c228e9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8877f5d6-eac4-43b3-b970-fd375a96242d", "logId": "12bae9f9-2aac-4202-8fe5-fae5d50df5e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76558399-9969-48fb-8328-43dd0f674ba6", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433079050900, "endTime": 153433083652700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c61de8bf-2246-4142-96c8-cb09c7678d10", "logId": "95a4300c-d7dc-413a-bfc8-30dad65998e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e386c2f9-92e5-4066-bd30-381259c228e9", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433083685300, "endTime": 153433095563600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c61de8bf-2246-4142-96c8-cb09c7678d10", "logId": "b74c8eb7-4060-4b95-904a-91f305032beb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b8cc023-20ad-4a07-ad17-d3026a564d9a", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433079056300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98446647-452e-4d09-b50b-8c28af93330f", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433083359600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95a4300c-d7dc-413a-bfc8-30dad65998e3", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433079050900, "endTime": 153433083652700}, "additional": {"logType": "info", "children": [], "durationId": "76558399-9969-48fb-8328-43dd0f674ba6", "parent": "12bae9f9-2aac-4202-8fe5-fae5d50df5e2"}}, {"head": {"id": "25c2d967-fdf5-4c6d-bce0-1618e94aa3c7", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433083711200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c07e88bb-fca9-4f96-ab84-5967bbfb55a2", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433090707200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1000729-a7a2-4c51-94fc-5cdd9ac78077", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433090870400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72e51b79-c121-4827-a8d1-99b6da6d92b4", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433091106300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c8a0599-e93a-4dad-97d9-53f17d75f8e8", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433091251700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcb7fda9-d115-4aa2-b8f6-73cfebcdaa17", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433091308100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f744116b-4c67-497f-8e26-84d5c43bc906", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433091359200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efa5d6e6-12db-45e2-a993-b48556ea78a9", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433091418200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18cc4263-9ebb-486f-bccf-44adff91df38", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433091470700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54011cee-7705-4554-8a7a-dfea227b7975", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433091721600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00380b89-9b54-4692-b289-b97b1384<PERSON>ca", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433091850400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d867a42-832d-42fe-adac-aa376c2af23c", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433091907500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a98fc65-c62c-4815-a470-ee27862f520b", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433091956400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab5b0215-c0c7-4c81-b1ce-8dbdcef79e6b", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433092007500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4da2c32b-a1dd-4231-af95-b01f1ecf9fe5", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433092054000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56ee66b9-0fc5-4fa3-8aac-dd794ce3635f", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433092165700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e17ec52e-f85a-47c6-a312-240d881ae7f0", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433092255600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22006cff-17dd-4962-9ffc-290c37e95550", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433092301700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce2ff7be-f230-40d5-a922-95e9a6488761", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433092342400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b80e91df-8577-4c6e-ad80-c6915e0e93bd", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433092392600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57229800-2474-4bf1-b6f0-d80aa52b00df", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433095332200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3665196-b67b-4cb6-bac5-556dc333d77d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433095462300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ef51257-d40e-4f5b-8f9e-3e1a3e48ac0e", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433095505300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd5b10dd-7445-464e-af6c-066499f21130", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433095536600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b74c8eb7-4060-4b95-904a-91f305032beb", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433083685300, "endTime": 153433095563600}, "additional": {"logType": "info", "children": [], "durationId": "e386c2f9-92e5-4066-bd30-381259c228e9", "parent": "12bae9f9-2aac-4202-8fe5-fae5d50df5e2"}}, {"head": {"id": "12bae9f9-2aac-4202-8fe5-fae5d50df5e2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433079050000, "endTime": 153433095573500}, "additional": {"logType": "info", "children": ["95a4300c-d7dc-413a-bfc8-30dad65998e3", "b74c8eb7-4060-4b95-904a-91f305032beb"], "durationId": "c61de8bf-2246-4142-96c8-cb09c7678d10", "parent": "f7d1a831-5f5d-4320-85ab-4a134deda3a0"}}, {"head": {"id": "8726ee92-f194-4b5f-8f17-07d0af702675", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433096933000, "endTime": 153433096946000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8877f5d6-eac4-43b3-b970-fd375a96242d", "logId": "93bd454a-2569-4a39-b388-d8d8b98847d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93bd454a-2569-4a39-b388-d8d8b98847d4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433096933000, "endTime": 153433096946000}, "additional": {"logType": "info", "children": [], "durationId": "8726ee92-f194-4b5f-8f17-07d0af702675", "parent": "f7d1a831-5f5d-4320-85ab-4a134deda3a0"}}, {"head": {"id": "f7d1a831-5f5d-4320-85ab-4a134deda3a0", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433071136800, "endTime": 153433096959900}, "additional": {"logType": "info", "children": ["ef1119b6-13c1-4394-81dc-2c91ae8446f6", "12bae9f9-2aac-4202-8fe5-fae5d50df5e2", "93bd454a-2569-4a39-b388-d8d8b98847d4"], "durationId": "8877f5d6-eac4-43b3-b970-fd375a96242d", "parent": "b7970ece-7709-4281-98d1-d781716ccf69"}}, {"head": {"id": "b7970ece-7709-4281-98d1-d781716ccf69", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433069990300, "endTime": 153433096970300}, "additional": {"logType": "info", "children": ["f7d1a831-5f5d-4320-85ab-4a134deda3a0"], "durationId": "f66a7f5c-0bde-4490-862f-cde4aaa11b44", "parent": "ee35dbf8-60cd-47f3-ae36-45b54ebe7d04"}}, {"head": {"id": "24cd773b-86a7-4873-85aa-986cff1d7994", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433127482300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9be126a-e36c-45f1-82a4-4c54833b2b2d", "name": "hvigorfile, resolve hvigorfile dependencies in 67 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433163110700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ecf7548-1707-4734-a4ad-b48ea0497432", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433096986700, "endTime": 153433163326500}, "additional": {"logType": "info", "children": [], "durationId": "68690d11-61ae-4731-8c9b-e55fa921a41f", "parent": "ee35dbf8-60cd-47f3-ae36-45b54ebe7d04"}}, {"head": {"id": "61a63973-b454-4f52-8195-e6edd18139f9", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433164634100, "endTime": 153433164884500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9238fb28-ce50-471b-88cb-bdfc5fba91ba", "logId": "d9e6ae0b-2c41-4d7c-a61c-e142b1a53ae1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4abbbf14-e00f-4d5e-969f-ea35dabaddf0", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433164673900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9e6ae0b-2c41-4d7c-a61c-e142b1a53ae1", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433164634100, "endTime": 153433164884500}, "additional": {"logType": "info", "children": [], "durationId": "61a63973-b454-4f52-8195-e6edd18139f9", "parent": "ee35dbf8-60cd-47f3-ae36-45b54ebe7d04"}}, {"head": {"id": "f7617027-1274-453c-bd66-a539e5b50480", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433167859900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72c74556-5f05-4553-bdba-c128a37f6eea", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433179769200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90fd9ffa-3c84-49f2-bec5-bb2ddc7f75e9", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433164906000, "endTime": 153433181651000}, "additional": {"logType": "info", "children": [], "durationId": "66f059e4-d8a8-4342-b135-b9eb325f507c", "parent": "ee35dbf8-60cd-47f3-ae36-45b54ebe7d04"}}, {"head": {"id": "13a30aab-6b10-41be-a6b4-f7563b27c544", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433181716400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cccd972f-c9de-45ae-995f-8042821ef67a", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433196934600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40e0d7d0-0ed8-4695-a1e6-75d987769072", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433197196600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19fba742-d97f-41ef-b191-42cdbcea8767", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433197649200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81f40dd0-626d-430f-bf99-0e00478c1a17", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433204932000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f2bb53b-95f4-4da9-9169-a27c172f2b70", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433205134900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d26c6834-0d2b-40f8-8457-385041e0a3db", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433181685800, "endTime": 153433212124500}, "additional": {"logType": "info", "children": [], "durationId": "ec0171e9-6a07-4c34-aecb-988d1e0219c9", "parent": "ee35dbf8-60cd-47f3-ae36-45b54ebe7d04"}}, {"head": {"id": "7eddd8e5-f114-47ab-a4d5-e80356ad9db8", "name": "Configuration phase cost:254 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433212185000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3309ce5-484f-4b14-95d6-7628aef74380", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433212153200, "endTime": 153433212422100}, "additional": {"logType": "info", "children": [], "durationId": "e9e26f34-8653-46c5-bc06-e42a63c5f903", "parent": "ee35dbf8-60cd-47f3-ae36-45b54ebe7d04"}}, {"head": {"id": "ee35dbf8-60cd-47f3-ae36-45b54ebe7d04", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432952470000, "endTime": 153433212463100}, "additional": {"logType": "info", "children": ["c0dff08c-6b03-42ae-b3b3-328d368bcb8d", "07e4f765-e500-45b7-861c-52164<PERSON><PERSON><PERSON>b", "729d5acd-98dd-455b-a375-9e51aec0872f", "b7970ece-7709-4281-98d1-d781716ccf69", "4ecf7548-1707-4734-a4ad-b48ea0497432", "90fd9ffa-3c84-49f2-bec5-bb2ddc7f75e9", "d26c6834-0d2b-40f8-8457-385041e0a3db", "c3309ce5-484f-4b14-95d6-7628aef74380", "d9e6ae0b-2c41-4d7c-a61c-e142b1a53ae1"], "durationId": "9238fb28-ce50-471b-88cb-bdfc5fba91ba", "parent": "6292c287-9f4a-4ac2-b21e-bb9ec8b0d829"}}, {"head": {"id": "b41e48df-3059-4a99-9d86-d6e8754d11b1", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433214715300, "endTime": 153433214742400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7024d419-6aa3-49aa-a790-410c1b9aec72", "logId": "2722e7dc-92bf-483a-98f9-5bb9269a49de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2722e7dc-92bf-483a-98f9-5bb9269a49de", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433214715300, "endTime": 153433214742400}, "additional": {"logType": "info", "children": [], "durationId": "b41e48df-3059-4a99-9d86-d6e8754d11b1", "parent": "6292c287-9f4a-4ac2-b21e-bb9ec8b0d829"}}, {"head": {"id": "84b96a77-a873-473b-8af6-b019802a9bf7", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433212511300, "endTime": 153433214765600}, "additional": {"logType": "info", "children": [], "durationId": "f5d5b777-9679-4b08-8d30-1479690fcc9c", "parent": "6292c287-9f4a-4ac2-b21e-bb9ec8b0d829"}}, {"head": {"id": "6a152169-c57e-47b0-b2ba-521a38246b06", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433214779300, "endTime": 153433214794700}, "additional": {"logType": "info", "children": [], "durationId": "c8aff786-5b75-4a33-a61a-1636d3dbbbe7", "parent": "6292c287-9f4a-4ac2-b21e-bb9ec8b0d829"}}, {"head": {"id": "6292c287-9f4a-4ac2-b21e-bb9ec8b0d829", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432937660800, "endTime": 153433214804600}, "additional": {"logType": "info", "children": ["791334ce-e884-4710-96f7-69a8233b64e0", "ee35dbf8-60cd-47f3-ae36-45b54ebe7d04", "84b96a77-a873-473b-8af6-b019802a9bf7", "6a152169-c57e-47b0-b2ba-521a38246b06", "38c9b86c-38ba-4524-91ba-058d461c45d1", "23b7f5a0-cb02-43ff-8ce9-2524378e26dc", "2722e7dc-92bf-483a-98f9-5bb9269a49de"], "durationId": "7024d419-6aa3-49aa-a790-410c1b9aec72"}}, {"head": {"id": "5385351c-ae88-431f-95ef-041ecc0aebd1", "name": "Configuration task cost before running: 280 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433215067400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed875bc5-07fe-4f4e-ad56-c5cbb0248a1c", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433231491300, "endTime": 153433252053200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "219c56df-3b66-43e2-80c3-f0773f1268dc", "logId": "5e73a9fc-4730-412c-a122-2189dc8eee2a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "219c56df-3b66-43e2-80c3-f0773f1268dc", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433217752400}, "additional": {"logType": "detail", "children": [], "durationId": "ed875bc5-07fe-4f4e-ad56-c5cbb0248a1c"}}, {"head": {"id": "5c675171-a6d5-4141-8475-426fe45e67d6", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433219249400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd2e1210-27a3-4dce-8c18-d4e1c8c9adb6", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433219441600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aba4bc0-24da-4ebd-a347-6af755b5feef", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433220865800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31bd5328-c3d7-46ea-84fe-1f54e733fa67", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433222212800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cbe3f6c-95e4-4d87-adb0-d5c4f95e306b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433224274000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85c049c7-033b-4fa5-817e-83a13b06b07e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433224409400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8e2fb0d-0114-4f1e-a3d0-d32ba57e71a1", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433231515600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17b3e6d1-15ce-44e0-8596-5f73390f15f1", "name": "Incremental task entry:default@PreBuild pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433251650100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfa07196-56b5-4de7-bbcd-7df524439aed", "name": "entry : default@PreBuild cost memory 0.3238372802734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433251864300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e73a9fc-4730-412c-a122-2189dc8eee2a", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433231491300, "endTime": 153433252053200}, "additional": {"logType": "info", "children": [], "durationId": "ed875bc5-07fe-4f4e-ad56-c5cbb0248a1c"}}, {"head": {"id": "0c490287-66dc-41c0-83d2-e23fd12f264e", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433264586000, "endTime": 153433268016500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a67d681a-8308-42fc-9ed0-f0d13c99e67b", "logId": "f2752753-b7c1-4325-8744-2837f7d6837a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a67d681a-8308-42fc-9ed0-f0d13c99e67b", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433261169500}, "additional": {"logType": "detail", "children": [], "durationId": "0c490287-66dc-41c0-83d2-e23fd12f264e"}}, {"head": {"id": "5b0869df-7cda-431a-977e-8b9c6d6a04f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433263039900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7531a6e8-1b27-4e19-b45f-1dbdd43a7fe4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433263224500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60ef9f63-884b-4813-af12-cddc8baf5564", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433264605800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67c11f4f-8648-4315-b72c-c7fe69593dec", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433265997800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60a73996-833a-4ff5-b51a-0e2b05b68c13", "name": "entry : default@CreateModuleInfo cost memory 0.06107330322265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433267673100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b9bfb98-23ba-4cfd-b11e-b8be2e64f8e8", "name": "runTaskFromQueue task cost before running: 333 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433267895500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2752753-b7c1-4325-8744-2837f7d6837a", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433264586000, "endTime": 153433268016500, "totalTime": 3252900}, "additional": {"logType": "info", "children": [], "durationId": "0c490287-66dc-41c0-83d2-e23fd12f264e"}}, {"head": {"id": "e726c873-ce1e-46e3-b746-d6d61ce73b7c", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433284005100, "endTime": 153433287853200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ffc82442-f6f2-41d9-bea0-e62c04b82ba4", "logId": "95801477-7e55-47c8-b3e6-cd1758445223"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffc82442-f6f2-41d9-bea0-e62c04b82ba4", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433272442500}, "additional": {"logType": "detail", "children": [], "durationId": "e726c873-ce1e-46e3-b746-d6d61ce73b7c"}}, {"head": {"id": "1a88902d-f3a9-4958-9690-a7f6d7ffee08", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433274889400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a471434-90d4-4efc-9b22-4fb35d6b55f2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433275109700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cef0f838-1c81-41cd-81fe-a8bf8633e064", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433284030000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63aaebd3-f6fd-45ae-90ad-24e115260be5", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433285985700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4b59446-c740-44f7-a7c8-77170fb24dab", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433287596600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed949868-58e7-4298-95af-e5064a14e793", "name": "entry : default@GenerateMetadata cost memory 0.1027069091796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433287754900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95801477-7e55-47c8-b3e6-cd1758445223", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433284005100, "endTime": 153433287853200}, "additional": {"logType": "info", "children": [], "durationId": "e726c873-ce1e-46e3-b746-d6d61ce73b7c"}}, {"head": {"id": "35bb33d6-3e8b-4632-94b0-ad5312145246", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433294098200, "endTime": 153433295296600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "1c357ae9-e7cd-45da-b2e7-54453f7a2945", "logId": "2e057f80-532f-4516-bcea-dc11930ef7e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c357ae9-e7cd-45da-b2e7-54453f7a2945", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433290978900}, "additional": {"logType": "detail", "children": [], "durationId": "35bb33d6-3e8b-4632-94b0-ad5312145246"}}, {"head": {"id": "9f2a0a59-0a2b-4ee1-8778-76da06f7ac63", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433293593700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6a48ff4-6238-485d-bb41-8bb28f5a97bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433293858700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "693b4f4e-8a0f-49d9-81db-2dd660755a25", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433294110200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d4fa390-a371-49e3-b89b-45c61aec1f36", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433294384400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "628db22d-2397-471a-b768-9a3066040561", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433294719500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30811c83-8cf5-4532-8e45-770e2ad05980", "name": "entry : default@ConfigureCmake cost memory 0.0374755859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433294903200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7755ef6-98a7-4964-bc52-06903e921f0d", "name": "runTaskFromQueue task cost before running: 360 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433295150000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e057f80-532f-4516-bcea-dc11930ef7e2", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433294098200, "endTime": 153433295296600, "totalTime": 896200}, "additional": {"logType": "info", "children": [], "durationId": "35bb33d6-3e8b-4632-94b0-ad5312145246"}}, {"head": {"id": "784a3093-2fee-421a-8ebc-a975319b4f51", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433302002600, "endTime": 153433307249500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "102e95fb-76e2-4dcf-9438-395c8eadc462", "logId": "754d9297-85e4-41fa-a563-fcce9e659e6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "102e95fb-76e2-4dcf-9438-395c8eadc462", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433298789200}, "additional": {"logType": "detail", "children": [], "durationId": "784a3093-2fee-421a-8ebc-a975319b4f51"}}, {"head": {"id": "e32f18c1-6d82-45b1-a119-468fd266f4a4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433300504900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7d6a029-9a0d-4d40-a7b3-c209493a79cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433300669500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a8163e8-8ba5-4431-a04b-6c79ed700f66", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433302023800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c887e2f-65eb-40cd-bc76-d9e32503db5d", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433306908200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74bfdf00-610a-43a2-902a-2f1bc81edc92", "name": "entry : default@MergeProfile cost memory 0.1183319091796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433307120000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "754d9297-85e4-41fa-a563-fcce9e659e6d", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433302002600, "endTime": 153433307249500}, "additional": {"logType": "info", "children": [], "durationId": "784a3093-2fee-421a-8ebc-a975319b4f51"}}, {"head": {"id": "742e7e92-2312-44ac-90a1-922803cbd194", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433314492700, "endTime": 153433320531100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9a2627e0-6e2f-48e4-911a-648286b0c676", "logId": "d1bcd4c5-3586-4ac4-b642-6c2f0e8313a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a2627e0-6e2f-48e4-911a-648286b0c676", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433310468700}, "additional": {"logType": "detail", "children": [], "durationId": "742e7e92-2312-44ac-90a1-922803cbd194"}}, {"head": {"id": "d89faf61-f03f-46d1-a085-74e7ff5fa3b2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433312745500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22b27bf1-948f-460c-b4b4-0532eb0edf08", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433312917500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1868e1dc-9509-4836-90b3-915f4873077b", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433314511200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ce30746-8c10-485b-a471-b958a28142d5", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433316617200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5536c74-c9b5-48c4-9dc2-0a1c1be0ecc9", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433320215000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d23eb8ea-f6d9-4401-9226-53a055111cf9", "name": "entry : default@CreateBuildProfile cost memory 0.10761260986328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433320422800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1bcd4c5-3586-4ac4-b642-6c2f0e8313a8", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433314492700, "endTime": 153433320531100}, "additional": {"logType": "info", "children": [], "durationId": "742e7e92-2312-44ac-90a1-922803cbd194"}}, {"head": {"id": "80623901-d35c-4b85-abf3-2ba0f7f5caf6", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433327485300, "endTime": 153433328469700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "22ed1d57-2567-40da-8ec6-db5266dd84e9", "logId": "7be48413-6b46-4743-86b3-0068625324a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22ed1d57-2567-40da-8ec6-db5266dd84e9", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433323586500}, "additional": {"logType": "detail", "children": [], "durationId": "80623901-d35c-4b85-abf3-2ba0f7f5caf6"}}, {"head": {"id": "1ecb56f7-d9dc-4713-a029-93c2f55860c3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433325796500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82c40310-cc60-4df6-94c0-e4729a64b709", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433325979200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dc3bada-cc35-4ddb-8860-e195bbe04525", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433327504100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e85afa3d-c907-4248-8006-e732f0df25a2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433327726700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9ab2869-f05f-4cf9-8d72-c2bcc959fae2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433327834800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6f2da0d-05fc-4b2a-a2ff-b85d533a6202", "name": "entry : default@PreCheckSyscap cost memory 0.04114532470703125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433328196000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f8d3249-9d47-4617-91d6-b70b19c91075", "name": "runTaskFromQueue task cost before running: 393 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433328361400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7be48413-6b46-4743-86b3-0068625324a4", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433327485300, "endTime": 153433328469700, "totalTime": 845400}, "additional": {"logType": "info", "children": [], "durationId": "80623901-d35c-4b85-abf3-2ba0f7f5caf6"}}, {"head": {"id": "65029f4e-fa20-46a8-99f9-04700308ff4c", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433334758900, "endTime": 153433344692100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0ca91ad7-5e93-4982-ab0d-af916808dbaa", "logId": "1ff332a5-16d6-446e-8a09-2df61d6608c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ca91ad7-5e93-4982-ab0d-af916808dbaa", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433331535700}, "additional": {"logType": "detail", "children": [], "durationId": "65029f4e-fa20-46a8-99f9-04700308ff4c"}}, {"head": {"id": "ef1c863c-849c-446c-900a-afe3aeaeaa02", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433333093500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69ff1af4-89a8-4708-bada-8c5b44db7dd2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433333217000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5e7c6ac-12d0-45ce-aefd-1f3bda0d95c5", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433334771200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c993e2e-f1e6-4258-9feb-a73d57e60e3e", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433342863600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bba1e87f-a419-4fe7-842e-7b3c4c0068c8", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433344329200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83bec5ea-0f49-4ae0-aa13-1deadee99670", "name": "entry : default@GeneratePkgContextInfo cost memory 0.25882720947265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433344549700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ff332a5-16d6-446e-8a09-2df61d6608c5", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433334758900, "endTime": 153433344692100}, "additional": {"logType": "info", "children": [], "durationId": "65029f4e-fa20-46a8-99f9-04700308ff4c"}}, {"head": {"id": "5e0b2adb-ba8a-4fd2-a423-ed4a8d5e9c51", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433360206600, "endTime": 153433364125000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "06bfe2c6-809f-40b7-b832-129edd350241", "logId": "1aaa8369-b593-4af0-ba7d-e988941fe16e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06bfe2c6-809f-40b7-b832-129edd350241", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433347667100}, "additional": {"logType": "detail", "children": [], "durationId": "5e0b2adb-ba8a-4fd2-a423-ed4a8d5e9c51"}}, {"head": {"id": "124b0ca4-8a5f-4f81-b328-e476db5739e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433349850100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c69e4ecd-4463-4e3d-a286-7e48c5a2bebc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433350028800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2334673b-8ccd-4fde-a987-a102774fb4cc", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433360233400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e36cb3d1-8156-48e7-8832-48dfe01686f2", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433363451100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd0027c5-3fb5-477e-a459-cccdab16f1d5", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433363645000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a08e83c5-5a13-4821-a879-14e1c9e5c7c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433363763400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de3322d6-3474-4f3e-a8d5-b142aa712e92", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433363835700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c01f0eed-f53d-4d84-97e7-fee75f8a0551", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12108612060546875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433363945500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17ca75b7-dc20-4dbc-a301-0ec102ca319f", "name": "runTaskFromQueue task cost before running: 429 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433364051300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aaa8369-b593-4af0-ba7d-e988941fe16e", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433360206600, "endTime": 153433364125000, "totalTime": 3827600}, "additional": {"logType": "info", "children": [], "durationId": "5e0b2adb-ba8a-4fd2-a423-ed4a8d5e9c51"}}, {"head": {"id": "acbd53ab-cce4-48c5-bb6c-1dc94d0ef10b", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433370513200, "endTime": 153433371030300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "49bdcc7c-004b-4c2b-8682-1e9d386cfcd3", "logId": "40b86bf2-1d31-4336-9606-18359adb593e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49bdcc7c-004b-4c2b-8682-1e9d386cfcd3", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433367539700}, "additional": {"logType": "detail", "children": [], "durationId": "acbd53ab-cce4-48c5-bb6c-1dc94d0ef10b"}}, {"head": {"id": "0ec9259b-6766-4fa7-8820-007c8b86f964", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433369255500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41ce6b65-fbb5-4403-b159-67c1239407cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433369374400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bed93f7-3615-4cea-9fac-36b5af336322", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433370524400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d144e7e5-b81d-4f5f-b823-69bb559e0fae", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433370669700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b080d51-8595-4153-a0de-58a1a9599026", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433370743300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "811742a9-4ce2-4333-9142-ba36584d1d36", "name": "entry : default@BuildNativeWithCmake cost memory 0.03852081298828125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433370836000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81ebf5df-0c33-4524-8ade-fd8d65b89648", "name": "runTaskFromQueue task cost before running: 436 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433370956800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40b86bf2-1d31-4336-9606-18359adb593e", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433370513200, "endTime": 153433371030300, "totalTime": 420100}, "additional": {"logType": "info", "children": [], "durationId": "acbd53ab-cce4-48c5-bb6c-1dc94d0ef10b"}}, {"head": {"id": "99a70129-518b-4c6e-952f-609797f29050", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433376292800, "endTime": 153433383033800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d3da10b8-874b-46f3-bfbe-26930e412e03", "logId": "57e6ba69-a0d7-40de-a143-03b7992e9ad3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3da10b8-874b-46f3-bfbe-26930e412e03", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433373225600}, "additional": {"logType": "detail", "children": [], "durationId": "99a70129-518b-4c6e-952f-609797f29050"}}, {"head": {"id": "f76663cc-453c-42d8-be18-812185fa8204", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433374995200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6a02f9d-b980-4fa7-8e17-a8d1166ec541", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433375135800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31f170de-c925-4e90-b10c-61d787fe65fb", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433376305600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ae0c38b-b2aa-4a78-adde-04a863dc9c92", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433382733200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d09d3b12-ab37-4064-9c84-d715f6d8f887", "name": "entry : default@MakePackInfo cost memory 0.33046722412109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433382925600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57e6ba69-a0d7-40de-a143-03b7992e9ad3", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433376292800, "endTime": 153433383033800}, "additional": {"logType": "info", "children": [], "durationId": "99a70129-518b-4c6e-952f-609797f29050"}}, {"head": {"id": "4634410c-8c14-437c-877a-ec48404da55e", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433391401900, "endTime": 153433395674700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "189ef625-28f9-4b0d-b8fc-ca5e14b1ca7a", "logId": "3a287ff6-fb2e-4c26-b51b-d77fc4492756"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "189ef625-28f9-4b0d-b8fc-ca5e14b1ca7a", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433387186800}, "additional": {"logType": "detail", "children": [], "durationId": "4634410c-8c14-437c-877a-ec48404da55e"}}, {"head": {"id": "7a3daadb-1bed-4040-98cd-5934ada405a4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433389205900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66ba1fde-4e2e-4eb6-bdd2-003765743cc0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433389308900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71767d85-4744-4b38-88fe-40cd2430d5bf", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433391418500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea4852fe-48be-4616-9ff0-694e6c08fcdb", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433391796800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63228c71-3d10-4027-9f3f-fb62d6e33519", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433393221300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edb11e1b-0529-4d11-a2d4-ff11e360193d", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433395479700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e41857f-b5e7-415a-a4ba-2d58d20d7117", "name": "entry : default@SyscapTransform cost memory 0.150604248046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433395612100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a287ff6-fb2e-4c26-b51b-d77fc4492756", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433391401900, "endTime": 153433395674700}, "additional": {"logType": "info", "children": [], "durationId": "4634410c-8c14-437c-877a-ec48404da55e"}}, {"head": {"id": "7d4ccc0c-12b4-4475-9fd9-960b132759e6", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433399910600, "endTime": 153433403043700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d11867eb-c42a-4827-8505-38ad16795843", "logId": "856798fd-7808-4c44-8be9-0703b18de992"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d11867eb-c42a-4827-8505-38ad16795843", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433397270300}, "additional": {"logType": "detail", "children": [], "durationId": "7d4ccc0c-12b4-4475-9fd9-960b132759e6"}}, {"head": {"id": "2c0aec4a-71e7-4269-9e82-23e363bec4cf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433398246000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d509274-cc78-4efd-8ed0-119e2963f957", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433398334300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eee3fde0-29f3-4b30-ac8a-aa26e386237d", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433399925600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "050023fe-3ffb-4b65-a5ba-d6d8a1589ea8", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433402809400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "862f394c-0ee9-4f3f-8f80-5abee14496e2", "name": "entry : default@ProcessProfile cost memory 0.12456512451171875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433402949200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "856798fd-7808-4c44-8be9-0703b18de992", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433399910600, "endTime": 153433403043700}, "additional": {"logType": "info", "children": [], "durationId": "7d4ccc0c-12b4-4475-9fd9-960b132759e6"}}, {"head": {"id": "8bf9bf58-972c-41fc-a5e0-f5e256eaba9c", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433409886000, "endTime": 153433422471900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "32ac4d84-e309-4af8-83c6-51f85b7eaf7a", "logId": "eafdd4cb-7a2d-42c5-a183-52fad45ee4ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32ac4d84-e309-4af8-83c6-51f85b7eaf7a", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433405350000}, "additional": {"logType": "detail", "children": [], "durationId": "8bf9bf58-972c-41fc-a5e0-f5e256eaba9c"}}, {"head": {"id": "0458dc49-ea17-4e0a-91b6-74634d895a61", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433406948200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddf7761b-147c-46ef-a6c4-c5b273a06db2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433407085000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67fc0002-c714-434d-9fc3-8329ba766801", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433409899300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24f37a33-5edb-44a5-8e4e-fc357d054c28", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433419395800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a973d91f-96c7-4031-bf93-4cce547448c1", "name": "entry : default@ProcessRouterMap cost memory -8.726760864257812", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433422363100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eafdd4cb-7a2d-42c5-a183-52fad45ee4ba", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433409886000, "endTime": 153433422471900}, "additional": {"logType": "info", "children": [], "durationId": "8bf9bf58-972c-41fc-a5e0-f5e256eaba9c"}}, {"head": {"id": "59fa4ac3-9093-4665-a103-e8161b8a2860", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433428353300, "endTime": 153433436054700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "df40103b-4274-4d1a-b2d6-861cf471c9ab", "logId": "3acc1994-519e-416c-9499-652c8fc7b1fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df40103b-4274-4d1a-b2d6-861cf471c9ab", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433426374400}, "additional": {"logType": "detail", "children": [], "durationId": "59fa4ac3-9093-4665-a103-e8161b8a2860"}}, {"head": {"id": "35820f74-2c9b-44fb-889a-018cfffd296e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433428011200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab4fb3f8-2a73-4190-ba1b-70b97556417e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433428205100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b66aba3-512d-4549-ac51-7d92b0e21ba3", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433428364600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ab39186-500d-4756-8be5-cc4ea74d7720", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433428536000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45166f50-e30d-47b1-89bc-eda1c703757e", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433434209900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e91efc9-7f77-417a-8462-ee14b807370b", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433434347100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b131b40c-8d46-419d-899f-d8fc68540370", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433434447000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eb36246-0d92-4755-bd78-28a43dd7192b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433434508200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad283edb-dd67-4191-892e-b59321cceb05", "name": "entry : default@ProcessStartupConfig cost memory 0.26012420654296875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433435852000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfe023d5-3a3c-42c9-b7b1-9dd91babdb62", "name": "runTaskFromQueue task cost before running: 501 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433435979800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3acc1994-519e-416c-9499-652c8fc7b1fa", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433428353300, "endTime": 153433436054700, "totalTime": 7598800}, "additional": {"logType": "info", "children": [], "durationId": "59fa4ac3-9093-4665-a103-e8161b8a2860"}}, {"head": {"id": "438ce29c-ac71-4c17-ad2c-7388cf46f2c9", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433442421400, "endTime": 153433443990800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c0c59e10-a6ab-4977-af02-4140c0b7e4aa", "logId": "a04dbcff-d371-4d15-8e3c-a157d6e0df95"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0c59e10-a6ab-4977-af02-4140c0b7e4aa", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433439867400}, "additional": {"logType": "detail", "children": [], "durationId": "438ce29c-ac71-4c17-ad2c-7388cf46f2c9"}}, {"head": {"id": "ca04d16a-dd7a-4751-a6f6-354cf9968fc7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433441378500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67241b4d-ac15-4d7f-a402-148722722624", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433441486800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50b95f5a-d6cb-4df1-9115-3af344b79f97", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433442431200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "144291be-12da-4064-9504-47847b2000a8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433442550900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fab80bc-97ed-435f-868e-7c56a4ff1985", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433442618100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f32297b-2e8a-4f89-a14d-1ea282673aed", "name": "entry : default@BuildNativeWithNinja cost memory 0.05817413330078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433443850400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "492110bc-424b-4be4-ae18-cde458b1d2ee", "name": "runTaskFromQueue task cost before running: 509 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433443951100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a04dbcff-d371-4d15-8e3c-a157d6e0df95", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433442421400, "endTime": 153433443990800, "totalTime": 1513800}, "additional": {"logType": "info", "children": [], "durationId": "438ce29c-ac71-4c17-ad2c-7388cf46f2c9"}}, {"head": {"id": "26d3b97f-b7bf-47a6-afed-bbf2c8ea8923", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433448099000, "endTime": 153433453340000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "8c251cd3-1f5a-4479-80f1-421c8a4b7c38", "logId": "20d1583d-57b5-40da-b881-92ff9ba9d65e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c251cd3-1f5a-4479-80f1-421c8a4b7c38", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433445880200}, "additional": {"logType": "detail", "children": [], "durationId": "26d3b97f-b7bf-47a6-afed-bbf2c8ea8923"}}, {"head": {"id": "c08d583d-03e1-4871-a0c0-e8aae11cb4e7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433446563200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e6914f4-6ce7-4555-b998-6612a63d6b98", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433446630200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a788088-83ad-47eb-bb7d-d96db91c327b", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433447319400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19d7b536-115b-43cb-9f02-9910fc95b9af", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433449108400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54bf725d-2998-459d-8229-db887732a5da", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433450895200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b35425c-bb52-4751-bb48-002424e3fde8", "name": "entry : default@ProcessResource cost memory 0.16216278076171875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433451011900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20d1583d-57b5-40da-b881-92ff9ba9d65e", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433448099000, "endTime": 153433453340000}, "additional": {"logType": "info", "children": [], "durationId": "26d3b97f-b7bf-47a6-afed-bbf2c8ea8923"}}, {"head": {"id": "1e0e4a36-61b0-46bc-b200-492a58ae4d0f", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433463615000, "endTime": 153433487643100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "648e8daa-529b-4c50-8895-5fc47f6c4e92", "logId": "0904d918-85a8-4f88-a8ca-591a8e331ad2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "648e8daa-529b-4c50-8895-5fc47f6c4e92", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433458175700}, "additional": {"logType": "detail", "children": [], "durationId": "1e0e4a36-61b0-46bc-b200-492a58ae4d0f"}}, {"head": {"id": "f27a323f-9a36-4052-9c66-d04b4ec157e2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433459582000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "258d2e92-3c02-42dc-84e9-8672bd64391e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433459693100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1b421d2-9632-48f5-acf5-e39f08ee17f0", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433463626500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53c72aa9-8717-4444-951e-7a500932611d", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433487440900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30bcf5c7-0c82-4937-96d7-3226eaca615c", "name": "entry : default@GenerateLoaderJson cost memory 0.8841323852539062", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433487584100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0904d918-85a8-4f88-a8ca-591a8e331ad2", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433463615000, "endTime": 153433487643100}, "additional": {"logType": "info", "children": [], "durationId": "1e0e4a36-61b0-46bc-b200-492a58ae4d0f"}}, {"head": {"id": "20eda755-25ea-471c-a99c-7be0cf05395a", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433501880700, "endTime": 153433507548500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5bf0b8ee-96d5-476b-b698-17ceaa6acabc", "logId": "3cf8915c-6882-4b52-9db2-5bce5a515f9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5bf0b8ee-96d5-476b-b698-17ceaa6acabc", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433498956100}, "additional": {"logType": "detail", "children": [], "durationId": "20eda755-25ea-471c-a99c-7be0cf05395a"}}, {"head": {"id": "421b93ea-b070-4e81-b587-75569c79d5f6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433500847800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92f91e5f-b286-471a-a13b-48f52b7e7ea1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433500985900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f88ef23b-5363-4f93-b439-0e981aa59382", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433501888500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c308efcb-372b-447c-aaf8-7e9138937842", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433507255200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92a527c9-7765-4a25-acd5-fd55ee65d507", "name": "entry : default@ProcessLibs cost memory 0.14215087890625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433507437100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cf8915c-6882-4b52-9db2-5bce5a515f9d", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433501880700, "endTime": 153433507548500}, "additional": {"logType": "info", "children": [], "durationId": "20eda755-25ea-471c-a99c-7be0cf05395a"}}, {"head": {"id": "00e8b277-a3ea-49e2-8a75-9a4d4d8dcbc3", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433521232800, "endTime": 153433561206400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "edccfb9e-82b7-4f98-b90b-bed71064fd98", "logId": "66b17893-d67e-40c0-9813-af1a9883e6dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "edccfb9e-82b7-4f98-b90b-bed71064fd98", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433511175600}, "additional": {"logType": "detail", "children": [], "durationId": "00e8b277-a3ea-49e2-8a75-9a4d4d8dcbc3"}}, {"head": {"id": "2bf549f6-9bfb-44c4-a300-eef2a8db37dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433513628700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b2f5625-64ee-4bec-95f4-bf1afc310f66", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433513834100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d05ac4a4-12be-413e-9461-16ac78658bf9", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433515963800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee52d55a-f4a9-4a2f-92aa-86188cd5ce32", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433521283700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a4e2012-f0bf-4001-abdd-79f382b46180", "name": "Incremental task entry:default@CompileResource pre-execution cost: 38 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433560835200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9adcd45f-1ce0-4274-bfc9-75dc43cc5295", "name": "entry : default@CompileResource cost memory 1.3156814575195312", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433561077800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b17893-d67e-40c0-9813-af1a9883e6dd", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433521232800, "endTime": 153433561206400}, "additional": {"logType": "info", "children": [], "durationId": "00e8b277-a3ea-49e2-8a75-9a4d4d8dcbc3"}}, {"head": {"id": "ef68bee0-ef6e-436a-8e0a-99cff11f1b17", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433570296600, "endTime": 153433574701500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "73553d8f-9eb3-4c6f-a78f-2be12fda4bc1", "logId": "bf4f0616-ee28-4640-9d19-f912a550ce29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73553d8f-9eb3-4c6f-a78f-2be12fda4bc1", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433564297800}, "additional": {"logType": "detail", "children": [], "durationId": "ef68bee0-ef6e-436a-8e0a-99cff11f1b17"}}, {"head": {"id": "22fce476-f245-4f47-b8ca-8afe8d7d03db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433566006200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0050ffa-1ffb-413b-a442-613318ed29c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433566138600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74fb09ad-38b8-4ff9-8448-6cf0ea19122d", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433570308300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb913e31-c627-485a-9ed8-377377e47559", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433571020800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97e59158-744e-4832-a734-63f6ec6fb932", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433574463100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d17a71ba-994d-401b-af58-7236364fa735", "name": "entry : default@DoNativeStrip cost memory 0.31253814697265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433574602900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf4f0616-ee28-4640-9d19-f912a550ce29", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433570296600, "endTime": 153433574701500}, "additional": {"logType": "info", "children": [], "durationId": "ef68bee0-ef6e-436a-8e0a-99cff11f1b17"}}, {"head": {"id": "e49757c8-9522-471a-aa49-76b55e39b005", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433586897500, "endTime": 153433621680000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "482ac728-6553-4641-a4f6-6501d881c1ab", "logId": "0ce1e92f-2b89-4f65-8c11-0256c16470cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "482ac728-6553-4641-a4f6-6501d881c1ab", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433577718400}, "additional": {"logType": "detail", "children": [], "durationId": "e49757c8-9522-471a-aa49-76b55e39b005"}}, {"head": {"id": "1baaffee-9f2b-4705-b926-c5a0e43ecb09", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433579566500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c90776b2-6b7c-4b59-8aed-4e66c6a8db4e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433579701200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cf24279-9ed9-4401-aadb-3c55415785aa", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433586911200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abdb1c9c-2afb-4ced-8a54-dee7fb4db1e2", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433587091100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c6891c1-a492-4251-97cc-8851390f4e18", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 26 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433621460200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "453e6581-b557-4050-943f-4d7d4e215708", "name": "entry : default@CompileArkTS cost memory 1.1710968017578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433621604800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ce1e92f-2b89-4f65-8c11-0256c16470cd", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433586897500, "endTime": 153433621680000}, "additional": {"logType": "info", "children": [], "durationId": "e49757c8-9522-471a-aa49-76b55e39b005"}}, {"head": {"id": "36efc5c2-ad21-41aa-8969-c8abea69943e", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433631445100, "endTime": 153433638781300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "e87effee-d33b-40b0-93ae-0c60072bb5dc", "logId": "8225a95e-b3f8-4703-9a68-6f29f1ae2cac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e87effee-d33b-40b0-93ae-0c60072bb5dc", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433627124200}, "additional": {"logType": "detail", "children": [], "durationId": "36efc5c2-ad21-41aa-8969-c8abea69943e"}}, {"head": {"id": "fa66a7e3-d8a1-46f0-a651-0573aae9c124", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433627991400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7c7145e-d6ac-428f-b3ba-8d20a498c3b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433628085900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a7837b4-fe15-4952-8b02-e51ba87c0417", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433631453900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83f83adb-a826-4861-8aca-2191508c7d1b", "name": "entry : default@BuildJS cost memory 0.3417205810546875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433638514200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "316dcd4f-2766-46c3-a096-979812312b11", "name": "runTaskFromQueue task cost before running: 704 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433638694200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8225a95e-b3f8-4703-9a68-6f29f1ae2cac", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433631445100, "endTime": 153433638781300, "totalTime": 7212000}, "additional": {"logType": "info", "children": [], "durationId": "36efc5c2-ad21-41aa-8969-c8abea69943e"}}, {"head": {"id": "1c19f4b8-3eec-4e2b-b0d4-a8934c85e52e", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433644928800, "endTime": 153433649126600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7d1bd91c-5194-4616-bade-bd1559dd9257", "logId": "4ff7c3a1-ca4c-4d5a-bff6-e69102175c59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d1bd91c-5194-4616-bade-bd1559dd9257", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433640120800}, "additional": {"logType": "detail", "children": [], "durationId": "1c19f4b8-3eec-4e2b-b0d4-a8934c85e52e"}}, {"head": {"id": "75997c14-4a7d-4f4b-a713-d9f9358d07c3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433641401900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a482b637-de95-493a-a0ad-a7d03d94c174", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433641500100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b427cf4f-9dad-427f-acc2-ef6788e3212b", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433644941200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9b7067a-3841-44ac-b6b2-986b0edabe96", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433645921400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd6f72b5-bc34-4028-8c7d-d9c8760ef375", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433648941900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ddb1961-3466-4631-b78d-9d23f096e82c", "name": "entry : default@CacheNativeLibs cost memory 0.0944976806640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433649052500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ff7c3a1-ca4c-4d5a-bff6-e69102175c59", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433644928800, "endTime": 153433649126600}, "additional": {"logType": "info", "children": [], "durationId": "1c19f4b8-3eec-4e2b-b0d4-a8934c85e52e"}}, {"head": {"id": "4b9b09ea-3c3e-4a72-a8dd-bff47f94fcb2", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433653679200, "endTime": 153433655756800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "e2120add-8abc-4a81-849b-6067ef9321d0", "logId": "c45ec65a-e420-40f5-9727-3dd65dc262b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2120add-8abc-4a81-849b-6067ef9321d0", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433650985500}, "additional": {"logType": "detail", "children": [], "durationId": "4b9b09ea-3c3e-4a72-a8dd-bff47f94fcb2"}}, {"head": {"id": "07b8f7cd-006c-4e6c-a699-d2d49d1c1f4b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433652296800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29a762e8-659f-4c76-8477-8528361da380", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433652385000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "222cb3ec-9e96-45d6-9fb8-014ef3144657", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433653687700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "693340fe-7002-4fc6-93fb-c6e93c074e09", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433654047900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b14bb66c-6c01-4341-939c-614ee3763be1", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433655500500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98fecde2-4c28-40c4-8754-5f2ab33c6653", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07517242431640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433655652200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c45ec65a-e420-40f5-9727-3dd65dc262b2", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433653679200, "endTime": 153433655756800}, "additional": {"logType": "info", "children": [], "durationId": "4b9b09ea-3c3e-4a72-a8dd-bff47f94fcb2"}}, {"head": {"id": "3c228ad5-95eb-435e-aa30-88b4c4b8b53d", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433671912500, "endTime": 153433712955000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "d94c56d1-1c56-4c58-aebc-81e139a59614", "logId": "7f0a4fe1-fe5a-4e1e-8598-34bb379de06b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d94c56d1-1c56-4c58-aebc-81e139a59614", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433658803400}, "additional": {"logType": "detail", "children": [], "durationId": "3c228ad5-95eb-435e-aa30-88b4c4b8b53d"}}, {"head": {"id": "46835fa1-99aa-4fef-9830-dd97fa37770c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433660068400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11e50ab4-27ae-4252-a863-d43dd55deb66", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433660168800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57d998b2-2193-4bc0-8490-594a3b263877", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433671939800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15739c58-a4bb-4f7d-9f5b-006ec3cd0e48", "name": "Incremental task entry:default@PackageHap pre-execution cost: 36 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433712588700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b18c5f31-1b57-451e-9d1c-293fe4383a34", "name": "entry : default@PackageHap cost memory 0.947845458984375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433712832800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f0a4fe1-fe5a-4e1e-8598-34bb379de06b", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433671912500, "endTime": 153433712955000}, "additional": {"logType": "info", "children": [], "durationId": "3c228ad5-95eb-435e-aa30-88b4c4b8b53d"}}, {"head": {"id": "cfe23810-5ca6-454b-9ef7-a616423e7fa6", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433727310300, "endTime": 153433732326800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": [], "detailId": "2535f9aa-c97f-4698-b253-69089988200a", "logId": "0d43ef79-bb2d-42a6-9f65-9a487ca95c00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2535f9aa-c97f-4698-b253-69089988200a", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433720960000}, "additional": {"logType": "detail", "children": [], "durationId": "cfe23810-5ca6-454b-9ef7-a616423e7fa6"}}, {"head": {"id": "17c8c0ea-6aa4-4220-9b1e-bb4d256c26ba", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433722988400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60448b6f-b08e-496b-996f-94d0e1759a30", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433723152300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ffc75fd-ce89-4e66-a623-c4940cd5ae0d", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433727325200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26d083ad-d1c6-4c5d-a7d7-9dad30c18984", "name": "Incremental task entry:default@SignHap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433732042900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abe22516-5935-4120-874c-86f6b419618e", "name": "entry : default@SignHap cost memory 0.10555267333984375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433732228300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d43ef79-bb2d-42a6-9f65-9a487ca95c00", "name": "UP-TO-DATE :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433727310300, "endTime": 153433732326800}, "additional": {"logType": "info", "children": [], "durationId": "cfe23810-5ca6-454b-9ef7-a616423e7fa6"}}, {"head": {"id": "a3b590a9-b1b3-4beb-9a0b-9ff5bc48fbb3", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433737987400, "endTime": 153433747989400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6e644fc8-cf2f-4fab-afe3-c9630f564061", "logId": "942a12ea-3353-492e-8e6b-37758259cfb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e644fc8-cf2f-4fab-afe3-c9630f564061", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433734995100}, "additional": {"logType": "detail", "children": [], "durationId": "a3b590a9-b1b3-4beb-9a0b-9ff5bc48fbb3"}}, {"head": {"id": "03f8c046-66a2-4015-94ef-d9eccf00d8e7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433736606800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbd3ed6e-29e1-44af-83f3-239825c53673", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433736738300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5690077-2422-4ab2-8bdb-68a4b8078d70", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433737998700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "129c36e0-c2cd-456d-a6b9-91caf6255234", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433747479900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43bfe803-cbda-4935-b30d-756c1a4f7ce0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433747638700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e67c40c0-5997-44f3-b7fa-197f6c070850", "name": "entry : default@CollectDebugSymbol cost memory 0.2425079345703125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433747799200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2a377ef-7d40-406f-bb9c-91f5a12517a3", "name": "runTaskFromQueue task cost before running: 813 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433747914700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "942a12ea-3353-492e-8e6b-37758259cfb3", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433737987400, "endTime": 153433747989400, "totalTime": 9903100}, "additional": {"logType": "info", "children": [], "durationId": "a3b590a9-b1b3-4beb-9a0b-9ff5bc48fbb3"}}, {"head": {"id": "a32db466-1c4b-4b3d-bc5f-e3e4ca0ccf47", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433750394500, "endTime": 153433750746300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "6193d4cb-ee23-46df-ac4f-7e1f5003a509", "logId": "4027132f-8a50-4968-9583-68d60e73a134"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6193d4cb-ee23-46df-ac4f-7e1f5003a509", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433750348500}, "additional": {"logType": "detail", "children": [], "durationId": "a32db466-1c4b-4b3d-bc5f-e3e4ca0ccf47"}}, {"head": {"id": "914102f9-c01b-48f3-940e-a5f7c2708c59", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433750402500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33ad4564-c0a5-4170-b4bd-639c89f53883", "name": "entry : assembleHap cost memory 0.0117034912109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433750573800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "426810d8-12e3-47c1-aa97-d6090942de51", "name": "runTaskFromQueue task cost before running: 816 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433750678700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4027132f-8a50-4968-9583-68d60e73a134", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433750394500, "endTime": 153433750746300, "totalTime": 264600}, "additional": {"logType": "info", "children": [], "durationId": "a32db466-1c4b-4b3d-bc5f-e3e4ca0ccf47"}}, {"head": {"id": "37e79f7e-de92-4fac-98cc-aa2f633d12d0", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433765914300, "endTime": 153433765945300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "082e67b3-1dc6-47a0-94a2-bf8191246825", "logId": "7ed1a5fd-a179-4097-92c8-9103a7bed9e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ed1a5fd-a179-4097-92c8-9103a7bed9e7", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433765914300, "endTime": 153433765945300}, "additional": {"logType": "info", "children": [], "durationId": "37e79f7e-de92-4fac-98cc-aa2f633d12d0"}}, {"head": {"id": "4d285d70-50f5-4a7d-8147-a5dfc8f52a57", "name": "BUILD SUCCESSFUL in 831 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433765993000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "b54810ef-4008-4a86-88b7-508574016f7f", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153432935332400, "endTime": 153433766254900}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 34, "second": 23}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "f2586bc5-f585-4e15-938d-24168e5d3168", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433766281000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f65605d2-f08f-4734-b646-3319b9f1d056", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433766363600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "147f4d91-1f48-480c-acf2-755c0ef9194b", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433766856300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d687edc-8925-48e5-9c50-6f38cc60bded", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433766941000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e1beb8f-7e1d-444a-b223-9c08945085ff", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433767001200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb4c9b47-4156-47fd-b3f3-30fb0acc8e8c", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433767060000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e72f0e4a-fd65-4533-8d3c-c05ea4a5e22b", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433767116000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04ed4fab-12e6-48bb-8f85-086b9b4d7b16", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433767970000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68652dc0-0574-44a0-8502-898d095737cd", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433768234500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68fe7e3f-f629-4467-914a-d720dfa878a2", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433768325300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82353181-9710-409f-ae9a-e999ee517068", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433768388600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2400021-4ebf-4a7c-9406-c776cd731cf0", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433768436900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c8c4f85-1c1e-4652-9ad1-072e45be613f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433768488200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bf8c25a-c0e6-4ee4-91de-c7df28c971e5", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433770121000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db3b4530-6361-47f8-94b3-200c2dbb0002", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433770537200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b7f3774-5730-4864-b714-9b44094b5485", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433770808900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64211976-fcbd-4057-a0ef-428d2e30c210", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433770886900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50581cec-e652-463e-be6d-f8234cce3cc4", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433770944600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bae4f0d3-8bba-4701-aa72-dffa529124e5", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433770994700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa08c136-f95b-40d0-8efa-d60a2f606a42", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433771061300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a0ab2f0-b608-46b3-9ff8-014a9e456f98", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433771114600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d9eb6a-f17d-48cb-8199-ea360611b444", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433771161400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad02d123-cb0e-4385-914d-f27066408882", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433774344200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aac3ee2a-4d41-4455-9da2-a9c518497fed", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433775635800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36ff9b7c-1b1b-4619-a04e-e9a86672ba1f", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433776412100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eddc6d29-0fd4-435e-bd3a-c0b4970d6797", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433776992900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dc2f00b-656b-4b34-b9bb-be9c14295868", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433777442900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bdc0ae3-56b7-4bf7-9238-b6fdffcefd76", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433778555700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c58533a5-8b6e-441d-937f-c42f0d5a6075", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433779667400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fd32c40-6f34-403e-9635-9b50818141af", "name": "Incremental task entry:default@BuildJS post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433782836100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9e717d1-3731-4c48-af71-0c0161a042ec", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433783059700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6b90d12-eaad-4dc5-8c7a-bddc73f35020", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433783181800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0742b6f4-5a60-428d-b7a5-745804041605", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433783263000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58da48ba-4ba3-45e9-aa76-816000a39194", "name": "There is no need to refresh cache, since the incremental task entry:default@SignHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433783325500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f97f3bc1-a736-4f9f-958c-abb9f0821577", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433788163400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7b19ece-9fa7-43ea-b66d-f18196657466", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433788636500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98fef1ca-2f9a-4bed-b8ca-15988fc9ba04", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433789371000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23006d03-6598-4c02-9b36-c3454025fb79", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433789730300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}