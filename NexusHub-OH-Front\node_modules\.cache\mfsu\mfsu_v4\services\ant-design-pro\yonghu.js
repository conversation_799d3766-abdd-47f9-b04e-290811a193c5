"use strict";
import { request } from "@umijs/max";
export async function postUsersLogin(body, options) {
  return request("/users/login", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    data: body,
    ...options || {}
  });
}
export async function postUsersLogout(options) {
  return request("/users/logout", {
    method: "POST",
    ...options || {}
  });
}
export async function getUsersProfile(options) {
  return request("/users/profile", {
    method: "GET",
    ...options || {}
  });
}
export async function putUsersProfile(body, options) {
  return request("/users/profile", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json"
    },
    data: body,
    ...options || {}
  });
}
export async function postUsersRegister(body, options) {
  return request("/users/register", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    data: body,
    ...options || {}
  });
}
