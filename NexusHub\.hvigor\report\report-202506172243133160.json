{"version": "2.0", "ppid": 30328, "events": [{"head": {"id": "c68e5228-fd4e-42ca-9feb-e410f2795dc1", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763533359700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09500be5-1ce9-4356-8fd4-7b8661326a4d", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763541411200, "endTime": 146765944434400}, "additional": {"children": ["0f625bab-a96f-46c7-963d-052982c365c7", "e0332a21-6cdf-4ac1-add2-46009d27db2f", "efae300f-4f0a-44a4-95b9-15e90c1f6fa9", "6a267b88-bd88-4911-b0b7-96abe4338a77", "f4ebe7bf-704d-480e-bf2f-8a22ec7753af", "0771544a-aebc-4a08-b025-0dac96573599", "f793a3ff-55a9-4208-be3e-92a76ad14535"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "e808f566-7b5b-4dbe-8f60-9c92467ea162"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f625bab-a96f-46c7-963d-052982c365c7", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763541414200, "endTime": 146763555927200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09500be5-1ce9-4356-8fd4-7b8661326a4d", "logId": "6fc18587-8646-4695-90e3-3f1746207d91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0332a21-6cdf-4ac1-add2-46009d27db2f", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763555949100, "endTime": 146765942062000}, "additional": {"children": ["e601b7f9-27dd-4080-885b-bb70938f6feb", "f7a1efd6-f4f5-4e97-993a-973ac5ef932f", "4441d4ae-fa35-42e8-8cb0-8684dcde9fa1", "aae812a7-2918-4a76-8386-5c8d99af639c", "58ef7573-30d4-4f0c-a86f-2890d1124e27", "71ba9322-3e92-4665-8e1a-8eda829d5d45", "2691b72c-464c-45fb-9f91-c2c7203decdf", "86080153-24cf-499b-8e4a-221db4e943f3", "487dfc1d-c5f9-432f-ac18-714016865d56"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09500be5-1ce9-4356-8fd4-7b8661326a4d", "logId": "9a0c6810-6a60-4384-85cb-c8ff440e7e0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "efae300f-4f0a-44a4-95b9-15e90c1f6fa9", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765942088100, "endTime": 146765944407100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09500be5-1ce9-4356-8fd4-7b8661326a4d", "logId": "3e68c4e0-2138-4ba8-8668-4971d705b43d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a267b88-bd88-4911-b0b7-96abe4338a77", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765944412100, "endTime": 146765944427200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09500be5-1ce9-4356-8fd4-7b8661326a4d", "logId": "c32c7357-e323-4814-9d51-fc0036f30266"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4ebe7bf-704d-480e-bf2f-8a22ec7753af", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763545538800, "endTime": 146763545714600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09500be5-1ce9-4356-8fd4-7b8661326a4d", "logId": "4fe7275e-01b0-4010-8856-80a991f59ae0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4fe7275e-01b0-4010-8856-80a991f59ae0", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763545538800, "endTime": 146763545714600}, "additional": {"logType": "info", "children": [], "durationId": "f4ebe7bf-704d-480e-bf2f-8a22ec7753af", "parent": "e808f566-7b5b-4dbe-8f60-9c92467ea162"}}, {"head": {"id": "0771544a-aebc-4a08-b025-0dac96573599", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763550257200, "endTime": 146763550296200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09500be5-1ce9-4356-8fd4-7b8661326a4d", "logId": "1a6b4f22-5091-4c44-9e4c-a7c5d7b4ff08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a6b4f22-5091-4c44-9e4c-a7c5d7b4ff08", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763550257200, "endTime": 146763550296200}, "additional": {"logType": "info", "children": [], "durationId": "0771544a-aebc-4a08-b025-0dac96573599", "parent": "e808f566-7b5b-4dbe-8f60-9c92467ea162"}}, {"head": {"id": "981f98e4-25d3-4cb3-b0cf-846ddbf5d3d2", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763550659700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cdd49d2-2b91-4cc7-abf4-88dc9e2cc88f", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763555756500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fc18587-8646-4695-90e3-3f1746207d91", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763541414200, "endTime": 146763555927200}, "additional": {"logType": "info", "children": [], "durationId": "0f625bab-a96f-46c7-963d-052982c365c7", "parent": "e808f566-7b5b-4dbe-8f60-9c92467ea162"}}, {"head": {"id": "e601b7f9-27dd-4080-885b-bb70938f6feb", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763560873500, "endTime": 146763560910500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0332a21-6cdf-4ac1-add2-46009d27db2f", "logId": "d688a627-8998-401d-ae9e-db61931bccb9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7a1efd6-f4f5-4e97-993a-973ac5ef932f", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763560948200, "endTime": 146763565075000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0332a21-6cdf-4ac1-add2-46009d27db2f", "logId": "045683dc-ee1a-4921-931b-72b2a9f544b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4441d4ae-fa35-42e8-8cb0-8684dcde9fa1", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763565184100, "endTime": 146765792701800}, "additional": {"children": ["d128117b-8ba0-42fc-9edc-b4c2e8206efb", "f13ffdc0-7481-4397-b30c-fd62a74cb3d9", "0351bb78-8be3-472b-92e2-dfc3da9fb172"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0332a21-6cdf-4ac1-add2-46009d27db2f", "logId": "437d9658-fceb-46c3-ba49-6ef47a27cd46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aae812a7-2918-4a76-8386-5c8d99af639c", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765792851400, "endTime": 146765830112500}, "additional": {"children": ["11e920c3-8f21-4a1d-96ed-270bbdfc0685"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0332a21-6cdf-4ac1-add2-46009d27db2f", "logId": "e463d917-9787-4e54-9f43-b9c552617712"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58ef7573-30d4-4f0c-a86f-2890d1124e27", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765830165000, "endTime": 146765899971000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0332a21-6cdf-4ac1-add2-46009d27db2f", "logId": "36cb5a20-ec30-4505-a93c-722a4f7ee293"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71ba9322-3e92-4665-8e1a-8eda829d5d45", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765901604400, "endTime": 146765915283500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0332a21-6cdf-4ac1-add2-46009d27db2f", "logId": "b016290a-24c9-4814-936a-fba1487b6597"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2691b72c-464c-45fb-9f91-c2c7203decdf", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765915308300, "endTime": 146765941722300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0332a21-6cdf-4ac1-add2-46009d27db2f", "logId": "e2fe62ac-b757-40e8-8d2d-6f8a2b9f54b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86080153-24cf-499b-8e4a-221db4e943f3", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765941748400, "endTime": 146765942048500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0332a21-6cdf-4ac1-add2-46009d27db2f", "logId": "a93f2ad8-1b61-48e9-8a55-b7e1453feee1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d688a627-8998-401d-ae9e-db61931bccb9", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763560873500, "endTime": 146763560910500}, "additional": {"logType": "info", "children": [], "durationId": "e601b7f9-27dd-4080-885b-bb70938f6feb", "parent": "9a0c6810-6a60-4384-85cb-c8ff440e7e0b"}}, {"head": {"id": "045683dc-ee1a-4921-931b-72b2a9f544b7", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763560948200, "endTime": 146763565075000}, "additional": {"logType": "info", "children": [], "durationId": "f7a1efd6-f4f5-4e97-993a-973ac5ef932f", "parent": "9a0c6810-6a60-4384-85cb-c8ff440e7e0b"}}, {"head": {"id": "d128117b-8ba0-42fc-9edc-b4c2e8206efb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763565947900, "endTime": 146763565994600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4441d4ae-fa35-42e8-8cb0-8684dcde9fa1", "logId": "0f06439c-553f-40f7-8d99-1f3a2bea61cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f06439c-553f-40f7-8d99-1f3a2bea61cd", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763565947900, "endTime": 146763565994600}, "additional": {"logType": "info", "children": [], "durationId": "d128117b-8ba0-42fc-9edc-b4c2e8206efb", "parent": "437d9658-fceb-46c3-ba49-6ef47a27cd46"}}, {"head": {"id": "f13ffdc0-7481-4397-b30c-fd62a74cb3d9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763568099500, "endTime": 146765791703800}, "additional": {"children": ["9e7d90e4-d4b6-44c0-8b51-adfbf66f2943", "c5e8a090-2297-4bb5-ba7c-f1e9283347b5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4441d4ae-fa35-42e8-8cb0-8684dcde9fa1", "logId": "883838ee-f17f-40d5-a7f3-1c7b903aa0f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e7d90e4-d4b6-44c0-8b51-adfbf66f2943", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763568101000, "endTime": 146765529348900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f13ffdc0-7481-4397-b30c-fd62a74cb3d9", "logId": "a67224da-7b78-43bc-ac88-fb73bb11a356"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5e8a090-2297-4bb5-ba7c-f1e9283347b5", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765529372700, "endTime": 146765791688300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f13ffdc0-7481-4397-b30c-fd62a74cb3d9", "logId": "752f7ba0-2c0e-4513-8c4b-dc0aca4f55bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12028faf-fb47-4c2f-a0fd-8be0cbe2f07d", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763568107200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4efc1e63-edb0-4f1c-9678-25c5376b61df", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765529206200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a67224da-7b78-43bc-ac88-fb73bb11a356", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763568101000, "endTime": 146765529348900}, "additional": {"logType": "info", "children": [], "durationId": "9e7d90e4-d4b6-44c0-8b51-adfbf66f2943", "parent": "883838ee-f17f-40d5-a7f3-1c7b903aa0f1"}}, {"head": {"id": "c8857375-a476-4ef3-a82d-199816a8bc89", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765529481300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07794da6-7e51-4448-9a2d-6663ce864647", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765705855900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f26c382c-556b-4851-b2f3-29ab1366f750", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765706096400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2a8cbe8-55f7-402a-9128-2739be61e806", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765706632700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6223f38-715f-477f-b68d-1b1f11961f61", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765706889100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c7d82b8-c69d-4efd-98b0-9563582e46cd", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765711511400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "276921b8-ea6e-4f9d-af5b-e3b64a3167b4", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765730944500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d942b2a5-c108-467c-a554-4c6fa227c6a6", "name": "Sdk init in 35 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765755609400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cd8c272-170b-4aab-ab48-351f6ca96fdf", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765755966400}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 43, "second": 15}, "markType": "other"}}, {"head": {"id": "d2ef8148-cafb-4677-8dc7-04acb7d8dfe6", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765756061500}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 43, "second": 15}, "markType": "other"}}, {"head": {"id": "3b071540-587a-4aa7-b11e-aeb0426f1918", "name": "Project task initialization takes 28 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765791055000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d60821d-0aaf-4cd1-a1a5-f87ae11f023d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765791467400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "288eca68-c328-42e5-9bd2-ae1aad5eae52", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765791566100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e21ece4-c7e4-488c-b741-2f70bcd9b102", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765791619800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "752f7ba0-2c0e-4513-8c4b-dc0aca4f55bd", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765529372700, "endTime": 146765791688300}, "additional": {"logType": "info", "children": [], "durationId": "c5e8a090-2297-4bb5-ba7c-f1e9283347b5", "parent": "883838ee-f17f-40d5-a7f3-1c7b903aa0f1"}}, {"head": {"id": "883838ee-f17f-40d5-a7f3-1c7b903aa0f1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763568099500, "endTime": 146765791703800}, "additional": {"logType": "info", "children": ["a67224da-7b78-43bc-ac88-fb73bb11a356", "752f7ba0-2c0e-4513-8c4b-dc0aca4f55bd"], "durationId": "f13ffdc0-7481-4397-b30c-fd62a74cb3d9", "parent": "437d9658-fceb-46c3-ba49-6ef47a27cd46"}}, {"head": {"id": "0351bb78-8be3-472b-92e2-dfc3da9fb172", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765792613000, "endTime": 146765792670500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4441d4ae-fa35-42e8-8cb0-8684dcde9fa1", "logId": "c8dbb64c-1626-4bab-9475-ba8fdf93c220"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8dbb64c-1626-4bab-9475-ba8fdf93c220", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765792613000, "endTime": 146765792670500}, "additional": {"logType": "info", "children": [], "durationId": "0351bb78-8be3-472b-92e2-dfc3da9fb172", "parent": "437d9658-fceb-46c3-ba49-6ef47a27cd46"}}, {"head": {"id": "437d9658-fceb-46c3-ba49-6ef47a27cd46", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763565184100, "endTime": 146765792701800}, "additional": {"logType": "info", "children": ["0f06439c-553f-40f7-8d99-1f3a2bea61cd", "883838ee-f17f-40d5-a7f3-1c7b903aa0f1", "c8dbb64c-1626-4bab-9475-ba8fdf93c220"], "durationId": "4441d4ae-fa35-42e8-8cb0-8684dcde9fa1", "parent": "9a0c6810-6a60-4384-85cb-c8ff440e7e0b"}}, {"head": {"id": "11e920c3-8f21-4a1d-96ed-270bbdfc0685", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765793527300, "endTime": 146765830088500}, "additional": {"children": ["72392727-5d97-4a29-a690-859140b9a511", "cf783c52-8850-47fc-8f9d-32c91bd33a42", "ef01b585-3cb9-47ff-b289-facd0f62b544"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aae812a7-2918-4a76-8386-5c8d99af639c", "logId": "59f6305f-fe8a-406b-8859-01d97f3f2dec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72392727-5d97-4a29-a690-859140b9a511", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765796573900, "endTime": 146765796592700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11e920c3-8f21-4a1d-96ed-270bbdfc0685", "logId": "01d70ffa-202c-42c3-9cd5-a0eaee79ae1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01d70ffa-202c-42c3-9cd5-a0eaee79ae1d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765796573900, "endTime": 146765796592700}, "additional": {"logType": "info", "children": [], "durationId": "72392727-5d97-4a29-a690-859140b9a511", "parent": "59f6305f-fe8a-406b-8859-01d97f3f2dec"}}, {"head": {"id": "cf783c52-8850-47fc-8f9d-32c91bd33a42", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765798513800, "endTime": 146765828637400}, "additional": {"children": ["fb418195-80f2-49a2-b5ec-f96c592a4842", "2ab026d9-3a17-4f18-901f-090e176016fc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11e920c3-8f21-4a1d-96ed-270bbdfc0685", "logId": "0bfb9bed-a179-4290-8529-946f5c133a4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb418195-80f2-49a2-b5ec-f96c592a4842", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765798514900, "endTime": 146765804511300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf783c52-8850-47fc-8f9d-32c91bd33a42", "logId": "a855dfef-e209-451b-acda-39ccb87866c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ab026d9-3a17-4f18-901f-090e176016fc", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765804530000, "endTime": 146765828628300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf783c52-8850-47fc-8f9d-32c91bd33a42", "logId": "b6ec9b6f-a081-4784-a06c-1cfca33f1a0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e95b64fb-be94-4093-a96d-90154ee3bb1f", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765798520000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bceed0f-96be-4ed6-88fc-10d518655bf6", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765804388600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a855dfef-e209-451b-acda-39ccb87866c3", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765798514900, "endTime": 146765804511300}, "additional": {"logType": "info", "children": [], "durationId": "fb418195-80f2-49a2-b5ec-f96c592a4842", "parent": "0bfb9bed-a179-4290-8529-946f5c133a4e"}}, {"head": {"id": "3f9c6ae0-629e-4482-9a21-06aeaa55ab42", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765804632600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e48a96c0-58c9-4428-8816-cb3de1e1d3f4", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765819080400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97ee6a9f-6440-49b3-8c7b-9d89bc299737", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765819403800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd7ebbe9-6608-454c-9bec-94fdd5d0572a", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765819877900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9434010d-043d-45fb-9532-0ab1d315dfb1", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765820232000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c599a829-ec06-4403-82e6-ebc8107ca6b2", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765820316600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c7d4cfb-122e-4023-8f75-a6d40e2b1887", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765820364200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cb8706c-d7c4-44cf-80bb-5ecbeb197c9c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765820467300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7cb6a79-46a7-4012-8a4c-bb09eb21b239", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765820551400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af131d98-e7db-4445-b39c-491072c23111", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765820863200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ef46c3c-23e5-4f8c-a032-b2f02dd0b077", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765820988000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fb02c06-5189-4d0e-b57f-9d20ebb93bee", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765821039600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c54dd7ab-06e3-4627-b9c2-ea678c32d7d8", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765821070900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72834811-b731-4610-81ef-13e38a9abf40", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765821227800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9da80ab5-2c12-472b-b852-00e34aa2afbf", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765821290200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25dc124d-761c-4f99-9695-1e9fbacced90", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765821476900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7152bd3-bb6e-48c0-9e7a-8adeab4968b4", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765821583300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d996e484-2765-455d-be86-2e0fbeb4f35e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765821621500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08296c4d-1c72-4490-b1bf-f2a26c4b7620", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765821734500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "388c0b5f-bdc1-4a50-8883-73ca9233b6f2", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765821987300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2d9b7bc-a540-410a-83cb-0c3c4a2f61b7", "name": "Module entry task initialization takes 4 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765828310200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "720bfe0b-4a82-49b7-99a5-38e08d7c2625", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765828496300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f58e34e1-d3d1-4b59-b346-4440424adedf", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765828551800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb4d8ca2-1b89-4dcc-877c-aa7f95e10db3", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765828593100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6ec9b6f-a081-4784-a06c-1cfca33f1a0f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765804530000, "endTime": 146765828628300}, "additional": {"logType": "info", "children": [], "durationId": "2ab026d9-3a17-4f18-901f-090e176016fc", "parent": "0bfb9bed-a179-4290-8529-946f5c133a4e"}}, {"head": {"id": "0bfb9bed-a179-4290-8529-946f5c133a4e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765798513800, "endTime": 146765828637400}, "additional": {"logType": "info", "children": ["a855dfef-e209-451b-acda-39ccb87866c3", "b6ec9b6f-a081-4784-a06c-1cfca33f1a0f"], "durationId": "cf783c52-8850-47fc-8f9d-32c91bd33a42", "parent": "59f6305f-fe8a-406b-8859-01d97f3f2dec"}}, {"head": {"id": "ef01b585-3cb9-47ff-b289-facd0f62b544", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765830061900, "endTime": 146765830074500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11e920c3-8f21-4a1d-96ed-270bbdfc0685", "logId": "f0b8cd67-95f5-4616-b6ba-45fa38522b44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0b8cd67-95f5-4616-b6ba-45fa38522b44", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765830061900, "endTime": 146765830074500}, "additional": {"logType": "info", "children": [], "durationId": "ef01b585-3cb9-47ff-b289-facd0f62b544", "parent": "59f6305f-fe8a-406b-8859-01d97f3f2dec"}}, {"head": {"id": "59f6305f-fe8a-406b-8859-01d97f3f2dec", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765793527300, "endTime": 146765830088500}, "additional": {"logType": "info", "children": ["01d70ffa-202c-42c3-9cd5-a0eaee79ae1d", "0bfb9bed-a179-4290-8529-946f5c133a4e", "f0b8cd67-95f5-4616-b6ba-45fa38522b44"], "durationId": "11e920c3-8f21-4a1d-96ed-270bbdfc0685", "parent": "e463d917-9787-4e54-9f43-b9c552617712"}}, {"head": {"id": "e463d917-9787-4e54-9f43-b9c552617712", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765792851400, "endTime": 146765830112500}, "additional": {"logType": "info", "children": ["59f6305f-fe8a-406b-8859-01d97f3f2dec"], "durationId": "aae812a7-2918-4a76-8386-5c8d99af639c", "parent": "9a0c6810-6a60-4384-85cb-c8ff440e7e0b"}}, {"head": {"id": "e08204ab-09f8-4d36-b980-03c187c958a4", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765847056200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe05eb6c-cc56-4f3b-b5cf-9f6647b9d1e3", "name": "hvigorfile, resolve hvigorfile dependencies in 70 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765899824300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36cb5a20-ec30-4505-a93c-722a4f7ee293", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765830165000, "endTime": 146765899971000}, "additional": {"logType": "info", "children": [], "durationId": "58ef7573-30d4-4f0c-a86f-2890d1124e27", "parent": "9a0c6810-6a60-4384-85cb-c8ff440e7e0b"}}, {"head": {"id": "487dfc1d-c5f9-432f-ac18-714016865d56", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765900986200, "endTime": 146765901553100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0332a21-6cdf-4ac1-add2-46009d27db2f", "logId": "c46556ee-bf94-40d1-b7d9-4ec31a65850f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d84a97cf-1b79-435d-aa04-3947cf7295f1", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765901136300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c46556ee-bf94-40d1-b7d9-4ec31a65850f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765900986200, "endTime": 146765901553100}, "additional": {"logType": "info", "children": [], "durationId": "487dfc1d-c5f9-432f-ac18-714016865d56", "parent": "9a0c6810-6a60-4384-85cb-c8ff440e7e0b"}}, {"head": {"id": "40a1c6d8-d344-4498-b257-ed85e9e4ab9b", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765903569300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ae7c25d-2072-44a7-8898-55441d60e297", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765914066500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b016290a-24c9-4814-936a-fba1487b6597", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765901604400, "endTime": 146765915283500}, "additional": {"logType": "info", "children": [], "durationId": "71ba9322-3e92-4665-8e1a-8eda829d5d45", "parent": "9a0c6810-6a60-4384-85cb-c8ff440e7e0b"}}, {"head": {"id": "6bb571f1-c726-4d46-85d5-d67045b5c94a", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765915429800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9988639e-2b66-48a4-879a-13c2f8e87955", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765928628800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "055da508-bf61-4019-a320-15877964671e", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765928766000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "709c6ae3-5544-40ab-b897-393b49740840", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765929465300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c72a040-9539-4a24-afa8-2a2c91925aab", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765934457100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ae6fbe9-4c81-4bd3-b46f-b7f44be29041", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765934581500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2fe62ac-b757-40e8-8d2d-6f8a2b9f54b3", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765915308300, "endTime": 146765941722300}, "additional": {"logType": "info", "children": [], "durationId": "2691b72c-464c-45fb-9f91-c2c7203decdf", "parent": "9a0c6810-6a60-4384-85cb-c8ff440e7e0b"}}, {"head": {"id": "0bdb0881-802a-41a5-b13b-8ab9ced95e11", "name": "Configuration phase cost:2 s 381 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765941926100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a93f2ad8-1b61-48e9-8a55-b7e1453feee1", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765941748400, "endTime": 146765942048500}, "additional": {"logType": "info", "children": [], "durationId": "86080153-24cf-499b-8e4a-221db4e943f3", "parent": "9a0c6810-6a60-4384-85cb-c8ff440e7e0b"}}, {"head": {"id": "9a0c6810-6a60-4384-85cb-c8ff440e7e0b", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763555949100, "endTime": 146765942062000}, "additional": {"logType": "info", "children": ["d688a627-8998-401d-ae9e-db61931bccb9", "045683dc-ee1a-4921-931b-72b2a9f544b7", "437d9658-fceb-46c3-ba49-6ef47a27cd46", "e463d917-9787-4e54-9f43-b9c552617712", "36cb5a20-ec30-4505-a93c-722a4f7ee293", "b016290a-24c9-4814-936a-fba1487b6597", "e2fe62ac-b757-40e8-8d2d-6f8a2b9f54b3", "a93f2ad8-1b61-48e9-8a55-b7e1453feee1", "c46556ee-bf94-40d1-b7d9-4ec31a65850f"], "durationId": "e0332a21-6cdf-4ac1-add2-46009d27db2f", "parent": "e808f566-7b5b-4dbe-8f60-9c92467ea162"}}, {"head": {"id": "f793a3ff-55a9-4208-be3e-92a76ad14535", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765944338100, "endTime": 146765944393700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09500be5-1ce9-4356-8fd4-7b8661326a4d", "logId": "124b094f-7d28-4c2f-ac91-ffbdaf27943d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "124b094f-7d28-4c2f-ac91-ffbdaf27943d", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765944338100, "endTime": 146765944393700}, "additional": {"logType": "info", "children": [], "durationId": "f793a3ff-55a9-4208-be3e-92a76ad14535", "parent": "e808f566-7b5b-4dbe-8f60-9c92467ea162"}}, {"head": {"id": "3e68c4e0-2138-4ba8-8668-4971d705b43d", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765942088100, "endTime": 146765944407100}, "additional": {"logType": "info", "children": [], "durationId": "efae300f-4f0a-44a4-95b9-15e90c1f6fa9", "parent": "e808f566-7b5b-4dbe-8f60-9c92467ea162"}}, {"head": {"id": "c32c7357-e323-4814-9d51-fc0036f30266", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765944412100, "endTime": 146765944427200}, "additional": {"logType": "info", "children": [], "durationId": "6a267b88-bd88-4911-b0b7-96abe4338a77", "parent": "e808f566-7b5b-4dbe-8f60-9c92467ea162"}}, {"head": {"id": "e808f566-7b5b-4dbe-8f60-9c92467ea162", "name": "init", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763541411200, "endTime": 146765944434400}, "additional": {"logType": "info", "children": ["6fc18587-8646-4695-90e3-3f1746207d91", "9a0c6810-6a60-4384-85cb-c8ff440e7e0b", "3e68c4e0-2138-4ba8-8668-4971d705b43d", "c32c7357-e323-4814-9d51-fc0036f30266", "4fe7275e-01b0-4010-8856-80a991f59ae0", "1a6b4f22-5091-4c44-9e4c-a7c5d7b4ff08", "124b094f-7d28-4c2f-ac91-ffbdaf27943d"], "durationId": "09500be5-1ce9-4356-8fd4-7b8661326a4d"}}, {"head": {"id": "5bec5a2a-8b07-472d-b4e2-aaaca30d6360", "name": "Configuration task cost before running: 2 s 407 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765945021600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ebfe526-b685-449d-b451-162e9d6c2502", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765967893000, "endTime": 146765988813300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "f3a035f5-803e-4311-b962-116f27564fe6", "logId": "e336274f-e5c3-4a9d-9d55-468148bd8bc1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3a035f5-803e-4311-b962-116f27564fe6", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765947202300}, "additional": {"logType": "detail", "children": [], "durationId": "9ebfe526-b685-449d-b451-162e9d6c2502"}}, {"head": {"id": "f2dcc738-405f-4c16-9d04-fa13b556090a", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765948626900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "406fa24b-96a5-4ed5-8144-acc7f6a915ec", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765948827400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e7b42c8-9771-4a20-b66f-129450cf8a1a", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765950399400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba89143b-6d35-4215-bb58-dd83d8701c26", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765952779900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a10e1e87-55cc-455c-969f-aa75ea265bd5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765955898400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c2a70d2-da4e-4e88-97a0-5057a9d07f26", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765956092000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42a5a3f6-f63a-42b0-a480-084c2641a56d", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765967968200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a802528-af72-4e53-9ad0-180620093888", "name": "Incremental task entry:default@PreBuild pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765988413600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fac2adc-0e84-4022-8377-581fcd6eabdb", "name": "entry : default@PreBuild cost memory 0.34978485107421875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765988628800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e336274f-e5c3-4a9d-9d55-468148bd8bc1", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765967893000, "endTime": 146765988813300}, "additional": {"logType": "info", "children": [], "durationId": "9ebfe526-b685-449d-b451-162e9d6c2502"}}, {"head": {"id": "f7dde19e-bc67-492b-ac92-9b1f58898522", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765997595200, "endTime": 146766002287000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7881d8a1-5d4f-4fa6-b9bc-33591932a519", "logId": "f41b498b-4a2e-4a3a-a6eb-628c04df65ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7881d8a1-5d4f-4fa6-b9bc-33591932a519", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765994586000}, "additional": {"logType": "detail", "children": [], "durationId": "f7dde19e-bc67-492b-ac92-9b1f58898522"}}, {"head": {"id": "b1864515-1ac2-4d23-a69c-3be03e37b3e7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765995980300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df81a977-be8f-441b-90ca-260739d629ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765996115000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ee7cf0e-4450-4b14-ab0b-e2badeab96c1", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765997623100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da1f2e1a-d8b7-485c-9b04-0da1b92ec78f", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765999605700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5372e69d-f6f0-434d-a6cc-1f4b9a92e83f", "name": "entry : default@CreateModuleInfo cost memory 0.06359100341796875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766001727900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b19a08c-0ad1-4b68-9e47-d3574845cf9d", "name": "runTaskFromQueue task cost before running: 2 s 464 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766002005400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f41b498b-4a2e-4a3a-a6eb-628c04df65ba", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146765997595200, "endTime": 146766002287000, "totalTime": 4365500}, "additional": {"logType": "info", "children": [], "durationId": "f7dde19e-bc67-492b-ac92-9b1f58898522"}}, {"head": {"id": "ccbf9739-789b-458d-b1ca-d5036d96d03f", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766017092000, "endTime": 146766020424600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "36c27700-c704-4fbc-b0ce-1a176ecb3fdb", "logId": "6399ee75-5469-43b3-b7a7-c249d6b93a22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36c27700-c704-4fbc-b0ce-1a176ecb3fdb", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766007822200}, "additional": {"logType": "detail", "children": [], "durationId": "ccbf9739-789b-458d-b1ca-d5036d96d03f"}}, {"head": {"id": "9c2b07af-dce7-4952-bf69-b4ccced67b5d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766010846100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09f4063f-d6cb-4aee-bc45-212c4c57cace", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766011051600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faf1c7d2-c4d7-4a25-bf47-402f6eb0dc86", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766017112900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79081070-3e4b-4c7a-bcfd-c6a8ddbf68ee", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766018707800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12db04a3-4f67-4e49-9fff-d7b4cb3860f2", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766020193100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bda9279-138d-4138-a7d6-a9245fab5629", "name": "entry : default@GenerateMetadata cost memory 0.1072998046875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766020344800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6399ee75-5469-43b3-b7a7-c249d6b93a22", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766017092000, "endTime": 146766020424600}, "additional": {"logType": "info", "children": [], "durationId": "ccbf9739-789b-458d-b1ca-d5036d96d03f"}}, {"head": {"id": "22099119-581a-4e4c-b6ce-af017dd615bf", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766025081700, "endTime": 146766025905200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "28b705ba-e66c-4e28-9257-a6f67fcfec55", "logId": "3ee00f4f-92e3-4f3f-b6fd-67acf526add7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28b705ba-e66c-4e28-9257-a6f67fcfec55", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766022737500}, "additional": {"logType": "detail", "children": [], "durationId": "22099119-581a-4e4c-b6ce-af017dd615bf"}}, {"head": {"id": "fbe08d18-54b5-4980-8949-5e28a0e9aa0e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766024486000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62f8196b-882e-4ae4-8ceb-681cc57b8081", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766024676800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "459bdc7f-919a-4bb7-880f-e398768f634e", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766025098800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aaa330d-2635-4eea-919e-b898f045935b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766025509800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41b4644c-f549-41dc-90a9-16ff37da4220", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766025603100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef16672a-8d26-4f28-a95a-3da34923316c", "name": "entry : default@ConfigureCmake cost memory 0.03771209716796875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766025746800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "835b7e30-842f-4c68-acbd-9ed7fd99b722", "name": "runTaskFromQueue task cost before running: 2 s 488 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766025855000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ee00f4f-92e3-4f3f-b6fd-67acf526add7", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766025081700, "endTime": 146766025905200, "totalTime": 742900}, "additional": {"logType": "info", "children": [], "durationId": "22099119-581a-4e4c-b6ce-af017dd615bf"}}, {"head": {"id": "fb697731-a2bb-4551-bf9a-b7bc8611d1c2", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766030671000, "endTime": 146766034005400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "4f833ae2-9e93-4bcb-a454-be837cd42b14", "logId": "fd6485b5-41f1-4010-8766-1be92693ecd2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f833ae2-9e93-4bcb-a454-be837cd42b14", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766028203100}, "additional": {"logType": "detail", "children": [], "durationId": "fb697731-a2bb-4551-bf9a-b7bc8611d1c2"}}, {"head": {"id": "fd144d15-e62e-4e51-b627-5c91197be69f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766029566500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32587c71-9955-46bb-bedd-d48848473124", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766029713100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e84c07ee-823b-4d2d-8607-de9b87748aa6", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766030685700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff5a24ed-9eea-4895-8e8c-1f6f1aa39d52", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766033797300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceea5bf6-32bb-45b5-bad9-c7daeab2769e", "name": "entry : default@MergeProfile cost memory 0.12441253662109375", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766033938000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd6485b5-41f1-4010-8766-1be92693ecd2", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766030671000, "endTime": 146766034005400}, "additional": {"logType": "info", "children": [], "durationId": "fb697731-a2bb-4551-bf9a-b7bc8611d1c2"}}, {"head": {"id": "ecf256b2-0da4-412b-9efd-97a55ece8d2a", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766038338100, "endTime": 146766042006800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cad1b16b-29c7-4830-94b5-85ba95abc49d", "logId": "d2e9af17-d2a0-4566-9044-149c5d24065e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cad1b16b-29c7-4830-94b5-85ba95abc49d", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766036040600}, "additional": {"logType": "detail", "children": [], "durationId": "ecf256b2-0da4-412b-9efd-97a55ece8d2a"}}, {"head": {"id": "0089294a-8a4b-48dd-980d-cc7bb86ba50a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766037189200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be3c9fb5-b8e8-43f7-b0df-401a7dbbdbef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766037323200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17faa3b8-325a-42ff-8b23-97677fbfd779", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766038351500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55c39439-1756-4bba-a652-8e7741b9d24d", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766039786300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd4cde09-7d79-41fe-b926-340b92ea9b5b", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766041763900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f7544d0-40e2-4f43-8b15-adb4b076ea6b", "name": "entry : default@CreateBuildProfile cost memory 0.11461639404296875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766041926200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2e9af17-d2a0-4566-9044-149c5d24065e", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766038338100, "endTime": 146766042006800}, "additional": {"logType": "info", "children": [], "durationId": "ecf256b2-0da4-412b-9efd-97a55ece8d2a"}}, {"head": {"id": "8eae54e2-fa9f-4bcc-a54d-cbdbdf74256d", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766047390900, "endTime": 146766048161600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d3079d9c-0785-4ed7-9db4-8b2a2737fa79", "logId": "6bfa3394-c768-46d0-99cc-f75a35fde7e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3079d9c-0785-4ed7-9db4-8b2a2737fa79", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766044980400}, "additional": {"logType": "detail", "children": [], "durationId": "8eae54e2-fa9f-4bcc-a54d-cbdbdf74256d"}}, {"head": {"id": "e14f2d8f-eca2-495c-b117-6df817fccd38", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766046445400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79c9eff3-afb7-4be9-a72e-e3dcc32f4239", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766046569100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acb9e811-c824-4ed9-b66c-ed23793a309f", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766047408800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "281a0e61-8783-4555-97c8-41d6d04e98d4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766047555600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e16f6548-8061-495d-980b-9167d36a9896", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766047606600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13df714a-bfd9-4a3d-8f79-ba6e95c6c125", "name": "entry : default@PreCheckSyscap cost memory 0.04163360595703125", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766047970300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb5f9958-166b-4171-b466-4bfab78bac54", "name": "runTaskFromQueue task cost before running: 2 s 510 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766048108400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bfa3394-c768-46d0-99cc-f75a35fde7e2", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766047390900, "endTime": 146766048161600, "totalTime": 692000}, "additional": {"logType": "info", "children": [], "durationId": "8eae54e2-fa9f-4bcc-a54d-cbdbdf74256d"}}, {"head": {"id": "dda2fdac-4136-4b06-bafd-763c7e5e4ad0", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766053206700, "endTime": 146766063680400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8ee3181e-957c-42b1-9901-e1e37da9b814", "logId": "9e2a201e-bf4d-4b8f-bdda-35b00ac422a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ee3181e-957c-42b1-9901-e1e37da9b814", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766050248000}, "additional": {"logType": "detail", "children": [], "durationId": "dda2fdac-4136-4b06-bafd-763c7e5e4ad0"}}, {"head": {"id": "5ff536ce-e762-4a56-a3e6-8cffbd9f70cc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766051466600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a215985-a437-4bdc-985e-1ed77cb1e0fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766051580000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "331a8078-bf65-46e9-b684-125949b8fc3b", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766053219800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9044d1d7-17eb-4146-9201-dd45ca67bb5c", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766062427800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15bd1224-0368-4a39-aea9-c1973780e2ed", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766063469600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6e553fa-60cb-4c4e-b6a2-88684f0b82a5", "name": "entry : default@GeneratePkgContextInfo cost memory 0.25028228759765625", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766063610600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e2a201e-bf4d-4b8f-bdda-35b00ac422a6", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766053206700, "endTime": 146766063680400}, "additional": {"logType": "info", "children": [], "durationId": "dda2fdac-4136-4b06-bafd-763c7e5e4ad0"}}, {"head": {"id": "00e50ed2-aa81-46ad-a8a7-fbc42e17c50e", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766074158100, "endTime": 146766077125800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "fcdce4b6-4e13-4e9a-8b81-c1fcb45ecd71", "logId": "dcd8759f-b769-4d68-9d4f-365230753946"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcdce4b6-4e13-4e9a-8b81-c1fcb45ecd71", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766065854600}, "additional": {"logType": "detail", "children": [], "durationId": "00e50ed2-aa81-46ad-a8a7-fbc42e17c50e"}}, {"head": {"id": "876b2444-895d-461b-9dc2-010e5f0a5e2d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766067007500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "125cc71d-ec43-4c25-a7c6-6265846ba0eb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766067134100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88e3e004-28e5-4c5a-a5af-bdab43bf0643", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766074182100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9501bb29-9342-4b9e-be8f-e21b48054502", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766076502900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3868ad1e-7686-4b78-b526-abe93937ebb7", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766076685900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16150222-1f4a-477b-8f5c-e26f85ddd3fa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766076774800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fbce939-58f2-44a2-850a-6f11465001de", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766076836300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9ba1140-1eb2-41e3-8abc-5c414f399100", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12277984619140625", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766076995200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6705e328-2762-43b9-b632-d07f2a43300c", "name": "runTaskFromQueue task cost before running: 2 s 539 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766077083300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcd8759f-b769-4d68-9d4f-365230753946", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766074158100, "endTime": 146766077125800, "totalTime": 2902500}, "additional": {"logType": "info", "children": [], "durationId": "00e50ed2-aa81-46ad-a8a7-fbc42e17c50e"}}, {"head": {"id": "971a769c-71c1-4208-8bc8-c47a62ab9ccc", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766082867400, "endTime": 146766083336100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2df3a429-fdd2-434c-9906-e893b5967fde", "logId": "1142f514-5203-4ed1-9d06-86bba10b3e52"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2df3a429-fdd2-434c-9906-e893b5967fde", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766079871200}, "additional": {"logType": "detail", "children": [], "durationId": "971a769c-71c1-4208-8bc8-c47a62ab9ccc"}}, {"head": {"id": "27ab73df-40f5-449c-8d6f-9d2e790f0456", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766081466200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cdde00d-d73f-4ee7-a145-25a9371eb5f9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766081633300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78488a32-f34e-4f03-8aa8-d6918a5ab749", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766082881000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ca7074d-930a-480f-8ff0-56783e4f2636", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766083031800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93d223f9-2c77-494f-81fd-9c331b4811d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766083085600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ed4f178-b258-43cd-b632-45293eb5677b", "name": "entry : default@BuildNativeWithCmake cost memory 0.03896331787109375", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766083198100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edadd205-5c3f-4994-bb91-1639935e609b", "name": "runTaskFromQueue task cost before running: 2 s 545 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766083290700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1142f514-5203-4ed1-9d06-86bba10b3e52", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766082867400, "endTime": 146766083336100, "totalTime": 394100}, "additional": {"logType": "info", "children": [], "durationId": "971a769c-71c1-4208-8bc8-c47a62ab9ccc"}}, {"head": {"id": "b60e48eb-37bd-42d5-9567-2ca9b0235b39", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766087257500, "endTime": 146766093499600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b6372415-6e3b-47d0-9b9e-74b9da9ca9c7", "logId": "c3098dad-af84-45bb-960d-561693e5a956"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6372415-6e3b-47d0-9b9e-74b9da9ca9c7", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766085212700}, "additional": {"logType": "detail", "children": [], "durationId": "b60e48eb-37bd-42d5-9567-2ca9b0235b39"}}, {"head": {"id": "5682ce54-b9df-46c5-8559-850d2fcc7564", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766086310500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b54b320a-8a77-4872-8bbf-187b1aacac41", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766086417900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99b01bf8-a86b-44f3-ae1f-55949d712ea6", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766087269700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92a233a9-7ec5-47ea-9718-4ef26bd1ba24", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766093097600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1515239e-6ae2-4b47-afc0-b56617bb6c88", "name": "entry : default@MakePackInfo cost memory 0.170654296875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766093263000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3098dad-af84-45bb-960d-561693e5a956", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766087257500, "endTime": 146766093499600}, "additional": {"logType": "info", "children": [], "durationId": "b60e48eb-37bd-42d5-9567-2ca9b0235b39"}}, {"head": {"id": "76d1bd73-077f-4620-8f20-49e01284dc16", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766104039600, "endTime": 146766126644500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "65c0d434-09ee-4f9e-a597-595baa419252", "logId": "be0d3029-91b6-400e-a207-569dfb20cb45"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65c0d434-09ee-4f9e-a597-595baa419252", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766098190300}, "additional": {"logType": "detail", "children": [], "durationId": "76d1bd73-077f-4620-8f20-49e01284dc16"}}, {"head": {"id": "a27f2d58-68af-4c89-a001-b89cc91c8194", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766100777800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be4684ac-7a37-4a4d-b146-99564901534e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766101007700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d02cfb56-24a2-43fe-bccc-6d7021b39a25", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766104068600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c62bd1fa-4119-476e-8ff0-6aa08c222e3b", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766104717000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "699cd18b-b913-48b0-8f32-3ce6bf3c9aa6", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 4 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766108042900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ed81abe-2272-4049-91c3-1a0f05bdcc7f", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766126306300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65257c0a-211c-439a-b6e6-fb73a7c5a74d", "name": "entry : default@SyscapTransform cost memory 0.15926361083984375", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766126512900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be0d3029-91b6-400e-a207-569dfb20cb45", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766104039600, "endTime": 146766126644500}, "additional": {"logType": "info", "children": [], "durationId": "76d1bd73-077f-4620-8f20-49e01284dc16"}}, {"head": {"id": "f0e420b6-8d24-4c29-afa6-2a98da28734a", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766133076400, "endTime": 146766136356400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "eb231899-6300-4e18-83a6-1dbfc619b95d", "logId": "f1315641-6639-437f-980f-9ad14515c447"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb231899-6300-4e18-83a6-1dbfc619b95d", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766129267900}, "additional": {"logType": "detail", "children": [], "durationId": "f0e420b6-8d24-4c29-afa6-2a98da28734a"}}, {"head": {"id": "93c2892c-9913-40ca-8fbf-893480ec9f9b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766130962800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54950ffb-9cbe-486f-a0c9-48dfddee48d8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766131103300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee2302b1-8b4d-4308-ae93-f54237c99c1e", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766133094800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2817d256-1852-42cf-afae-fa5dc635c033", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766136109000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c7ef74d-16ae-4b93-a266-8a88ce5e0618", "name": "entry : default@ProcessProfile cost memory 0.12860870361328125", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766136280300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1315641-6639-437f-980f-9ad14515c447", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766133076400, "endTime": 146766136356400}, "additional": {"logType": "info", "children": [], "durationId": "f0e420b6-8d24-4c29-afa6-2a98da28734a"}}, {"head": {"id": "bf633e1e-5d9d-4c57-aeef-06251474f30e", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766146561500, "endTime": 146766154467600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0ad7df6e-a89c-45a7-bd39-976a23e2aa2b", "logId": "323d2b24-a952-4d8e-9c73-08d08689e23e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ad7df6e-a89c-45a7-bd39-976a23e2aa2b", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766138669900}, "additional": {"logType": "detail", "children": [], "durationId": "bf633e1e-5d9d-4c57-aeef-06251474f30e"}}, {"head": {"id": "dcd36583-dd5c-4f48-bd44-95ae1deb1886", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766142028300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f499e5b-5131-4329-9a9d-00e5b525c858", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766142263100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d7893e0-7772-4237-822d-2163a538be49", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766146589500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38adad3b-f92b-4ec2-a81e-b52962ff8a8f", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766154214500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b830fee6-7e39-44ae-b1f9-cc33e5f9b830", "name": "entry : default@ProcessRouterMap cost memory 0.24289703369140625", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766154383900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "323d2b24-a952-4d8e-9c73-08d08689e23e", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766146561500, "endTime": 146766154467600}, "additional": {"logType": "info", "children": [], "durationId": "bf633e1e-5d9d-4c57-aeef-06251474f30e"}}, {"head": {"id": "5354eed0-2ceb-4f9e-8398-938f3a15091a", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766163433400, "endTime": 146766170676900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "60e68fa9-7bc1-4498-aa89-106211a85cc0", "logId": "c61c1145-695c-4cfa-a42a-41e8f1fbcc7d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60e68fa9-7bc1-4498-aa89-106211a85cc0", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766162033900}, "additional": {"logType": "detail", "children": [], "durationId": "5354eed0-2ceb-4f9e-8398-938f3a15091a"}}, {"head": {"id": "86fbc920-a730-4b1b-ab24-99bb50b1f908", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766163194400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f787b3d-e6eb-4b0c-a916-1d9a40e9a2fd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766163318700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b492f75-70af-4308-8121-3d4ee4e690cd", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766163442000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e652dc8-9032-4526-9c00-fa0e2776106c", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766163595600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "154a5f6e-8eca-4e74-b1dc-98dd3105d0ef", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766168257500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "863f51ff-425b-4283-9c73-cd7045785a3e", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766168419600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2cc2197-b0a1-4b0e-a6ee-a5d947d764d7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766168508500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19c3c1d7-8c80-4eda-8ad7-004088534fe9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766168549000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e12e366d-eb8d-467a-b097-a9398675a65c", "name": "entry : default@ProcessStartupConfig cost memory 0.2664947509765625", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766170457400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a98d3cc2-b8a0-4e0b-94ef-bbebd5d87328", "name": "runTaskFromQueue task cost before running: 2 s 633 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766170618400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c61c1145-695c-4cfa-a42a-41e8f1fbcc7d", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766163433400, "endTime": 146766170676900, "totalTime": 7154500}, "additional": {"logType": "info", "children": [], "durationId": "5354eed0-2ceb-4f9e-8398-938f3a15091a"}}, {"head": {"id": "04064362-6c5f-4225-b7b3-93e4a39aa6b6", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766176550600, "endTime": 146766178122200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "770e1be3-125b-4384-a0fb-07c65e9b1029", "logId": "a5cd1774-3cd6-47ee-8f0e-e4b05e8b7f3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "770e1be3-125b-4384-a0fb-07c65e9b1029", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766174116800}, "additional": {"logType": "detail", "children": [], "durationId": "04064362-6c5f-4225-b7b3-93e4a39aa6b6"}}, {"head": {"id": "b1f974fb-a42a-46aa-a439-cf69efc57d02", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766175337100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bffbcf4a-7a6c-4c02-906d-f8f7a4c4c473", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766175501400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2248dfa4-5502-4a0d-9e3f-d7d253d788be", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766176566300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bdc5e70-5c31-4371-963f-47c759b52be8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766176728800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66ffa593-9f36-4322-b62f-6aca267a266b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766176788500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d22b20a6-f5ad-4b49-b234-55e2f3a31080", "name": "entry : default@BuildNativeWithNinja cost memory 0.05876922607421875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766177896800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdb463bb-800f-4d2e-8c14-53ad87ca6ce6", "name": "runTaskFromQueue task cost before running: 2 s 640 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766178049800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5cd1774-3cd6-47ee-8f0e-e4b05e8b7f3b", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766176550600, "endTime": 146766178122200, "totalTime": 1472300}, "additional": {"logType": "info", "children": [], "durationId": "04064362-6c5f-4225-b7b3-93e4a39aa6b6"}}, {"head": {"id": "6d8a7bfb-5973-41a9-9565-a372ba352399", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766185734400, "endTime": 146766194533700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "3b41b2a5-7b23-4cf1-a472-40ded70d71f9", "logId": "c80e80af-085f-415f-957c-cb99a69e22f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b41b2a5-7b23-4cf1-a472-40ded70d71f9", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766180913700}, "additional": {"logType": "detail", "children": [], "durationId": "6d8a7bfb-5973-41a9-9565-a372ba352399"}}, {"head": {"id": "148fad9a-cc3d-44d2-acc5-d7d6871a15c2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766182029900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f9c483b-22af-46f6-8eed-a31e44cc5e79", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766182142500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdbf476c-6259-4f4c-b72e-7bb720f2b8b9", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766183982600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18447c62-fd76-40c3-b156-b5da3a908fbd", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766188072800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ae8ee59-0a78-4087-8bac-6bc218ba4868", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766192463200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcbc8669-4759-4d56-83ee-98f1bc770270", "name": "entry : default@ProcessResource cost memory 0.1677093505859375", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766192629800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c80e80af-085f-415f-957c-cb99a69e22f7", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766185734400, "endTime": 146766194533700}, "additional": {"logType": "info", "children": [], "durationId": "6d8a7bfb-5973-41a9-9565-a372ba352399"}}, {"head": {"id": "b2052151-c56c-41c1-87b5-815728165e3e", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766204733100, "endTime": 146766229188700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cae1a182-589c-4d70-8ea3-3496616bb2e8", "logId": "aac1305e-9e98-4fde-a43b-fbb9babbca5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cae1a182-589c-4d70-8ea3-3496616bb2e8", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766198616300}, "additional": {"logType": "detail", "children": [], "durationId": "b2052151-c56c-41c1-87b5-815728165e3e"}}, {"head": {"id": "84b73e32-ae8d-4d59-82ff-3b978880aeb4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766199622400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "103173ae-7152-4e9f-8151-a40601be9901", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766199722800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22918f31-45d1-46fc-9022-e5341977a402", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766204747700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c355f67b-b904-4d0a-b346-84743139b291", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766228911800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2fa3cb3-4698-4af4-ae9f-5ac650c960ee", "name": "entry : default@GenerateLoaderJson cost memory 1.1260452270507812", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766229081500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aac1305e-9e98-4fde-a43b-fbb9babbca5e", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766204733100, "endTime": 146766229188700}, "additional": {"logType": "info", "children": [], "durationId": "b2052151-c56c-41c1-87b5-815728165e3e"}}, {"head": {"id": "54ac735a-8701-42f5-8151-45db6e287884", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766240686300, "endTime": 146766246603600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "821af55f-f2bf-4505-9006-e6745ba61849", "logId": "8f9165e6-5839-466e-99ec-6bcbfd49c22b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "821af55f-f2bf-4505-9006-e6745ba61849", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766238418000}, "additional": {"logType": "detail", "children": [], "durationId": "54ac735a-8701-42f5-8151-45db6e287884"}}, {"head": {"id": "534cfe65-6115-4f03-9ccb-ed1f4ec5b88e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766239625900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc64a777-1448-4ea8-aab5-9247e1f82dfe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766239793300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2204f46-9690-4bdc-b22d-9d59aa40023e", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766240698200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69292496-d2eb-4943-8c8a-a260739dc8f1", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766246379000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b9a7ec8-4e9c-4ce8-a226-a2acdd02708e", "name": "entry : default@ProcessLibs cost memory 0.148193359375", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766246534000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f9165e6-5839-466e-99ec-6bcbfd49c22b", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766240686300, "endTime": 146766246603600}, "additional": {"logType": "info", "children": [], "durationId": "54ac735a-8701-42f5-8151-45db6e287884"}}, {"head": {"id": "2f2979e9-0ee4-47ba-9c27-e2631ef59660", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766253082800, "endTime": 146766285097300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "33ae172b-61be-4f46-a9b6-f5b76ff4a47d", "logId": "69502f18-ce7a-4671-ba48-e5cc3555f3f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33ae172b-61be-4f46-a9b6-f5b76ff4a47d", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766248783100}, "additional": {"logType": "detail", "children": [], "durationId": "2f2979e9-0ee4-47ba-9c27-e2631ef59660"}}, {"head": {"id": "001add3d-a64e-451d-acd0-bc0a8a8f3844", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766249737900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fe39568-682d-4d1d-a10d-54c9e573d3de", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766249832400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3010c9b1-10ef-483c-9424-7c7de89552a6", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766250738200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d4efb9a-4a4a-4338-a225-64b545848eda", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766253158100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8feb78b6-755b-49d7-be1b-3e2d6ad6edcc", "name": "Incremental task entry:default@CompileResource pre-execution cost: 30 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766284699200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4e3169e-7d43-4c6d-90a2-99a2aad24199", "name": "entry : default@CompileResource cost memory 1.7126007080078125", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766284983100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69502f18-ce7a-4671-ba48-e5cc3555f3f4", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766253082800, "endTime": 146766285097300}, "additional": {"logType": "info", "children": [], "durationId": "2f2979e9-0ee4-47ba-9c27-e2631ef59660"}}, {"head": {"id": "74e6cd9f-5175-4ef8-ace3-1a76a264dd7f", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766292263000, "endTime": 146766294553500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "fb267e28-e893-4895-a808-585f8faa2e24", "logId": "1ce49582-f58b-4aa2-9f2d-f8ae24a5f428"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb267e28-e893-4895-a808-585f8faa2e24", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766287901200}, "additional": {"logType": "detail", "children": [], "durationId": "74e6cd9f-5175-4ef8-ace3-1a76a264dd7f"}}, {"head": {"id": "4b701f1a-e003-4d6c-a77e-93cfb495022f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766289108200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f3d234e-46c6-4246-9a45-171a90d567df", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766289256000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8278f516-df74-4deb-bf50-504513ef57e8", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766292278100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24f036c0-904d-473f-80c4-693e7197e85e", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766292832700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75a431e4-8a1f-4c30-8eaf-911634cf3ae9", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766294356000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "972c6789-f00e-4d08-8e2a-4a8110c487a0", "name": "entry : default@DoNativeStrip cost memory 0.083953857421875", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766294485800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ce49582-f58b-4aa2-9f2d-f8ae24a5f428", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766292263000, "endTime": 146766294553500}, "additional": {"logType": "info", "children": [], "durationId": "74e6cd9f-5175-4ef8-ace3-1a76a264dd7f"}}, {"head": {"id": "5e70aadb-1e39-47fb-b994-b39593a6b309", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766301702500, "endTime": 146778563477900}, "additional": {"children": ["89405332-d19a-4b7e-86ff-f296e88fcba5", "d2a2d6ca-d908-4a96-ac7f-d8b038effce6"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "0974e818-3318-4976-9096-347b8be5ea9d", "logId": "9d744911-60b0-4822-bc2b-a5dab36ecd8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0974e818-3318-4976-9096-347b8be5ea9d", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766296475300}, "additional": {"logType": "detail", "children": [], "durationId": "5e70aadb-1e39-47fb-b994-b39593a6b309"}}, {"head": {"id": "567f7c9b-7659-4a53-8bef-e8aa4839809c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766297488800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9e5f825-0a1f-42b8-80e8-e2f8ae936328", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766297608500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "586a1bfc-8a8f-4465-bb7e-8dd576a1c55c", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766301718000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fca8c89-a5b1-4dc0-9719-155c4a6db33d", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766302144100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce179769-0769-45f5-9c55-ed68d5c18809", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766332314600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "309e4c3c-baf6-474b-b3c6-1e3b189d1476", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 21 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766332470900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04b469da-888c-4d2a-82f4-7961e7fe8b62", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766351398900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d4f2733-6601-4029-85c8-eda77d976990", "name": "default@CompileArkTS work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766353773100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89405332-d19a-4b7e-86ff-f296e88fcba5", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Worker4", "startTime": 146766357732000, "endTime": 146778563245700}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "5e70aadb-1e39-47fb-b994-b39593a6b309", "logId": "b327389b-2ccd-4ab5-9328-2d4ca2d14b71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e548e408-8672-4a58-8945-ddfba050064d", "name": "default@CompileArkTS work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766354982800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1267dc56-17d0-4ad7-9990-2db4cdba4890", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355265400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d8b1483-a43f-468a-85d3-f3a759253840", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355338800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "646fb36f-7f96-4177-8d7e-aad815e3833e", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355375400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8606edd8-1c39-4a7c-8b4d-e07044bf0b7c", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355404000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fe20ad1-caf7-47f3-bd08-5b7452c3748e", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355432500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "271ff20b-4001-43b5-aee0-4eed0eadf576", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355461500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dacf342-4206-46d4-9fd6-cba6279ca890", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355488700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b48d51e8-1e60-44db-851b-5bea5def4751", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355516700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc73cdc8-4d74-4a9a-aacb-fb28edcd68ba", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355543700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a52dfb2-6c73-4dd9-922d-67f074abedd9", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355569800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29ee0dd5-f604-4ad8-a18a-2de02d5d14e5", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355594400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "512fecf0-3d62-4147-8a15-c42350db091c", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355620200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "521e6eed-48d9-4e30-a198-fbfb27d797f5", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355646900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f92d1dce-82ec-485b-ae80-9dfa32e8db1f", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355672600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be748742-bd8d-442c-af5c-959bf22bc51b", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355696600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "768e22d2-b429-4f13-b9b6-205d9cc43c79", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766355895800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbed8ceb-b80f-45b9-b990-000ac2e870fe", "name": "default@CompileArkTS work[0] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766357784400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d134f426-2f1b-432b-96aa-be0d9dededa3", "name": "default@CompileArkTS work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766357985300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71175643-04b2-43a8-ab80-30632def44b6", "name": "CopyResources startTime: 146766358117200", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766358121400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff3b7a13-3633-4c9c-b2de-42b7a4a4069e", "name": "default@CompileArkTS work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766358210200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2a2d6ca-d908-4a96-ac7f-d8b038effce6", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Worker18", "startTime": 146767412417100, "endTime": 146767428183300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "5e70aadb-1e39-47fb-b994-b39593a6b309", "logId": "9e3e7dd6-151f-450c-83f7-5050c11d4831"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1eedea65-d1fe-4e33-8557-1b220721a93a", "name": "default@CompileArkTS work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766359192600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a256ba1-4ae5-45b2-a3be-c7997a1b260e", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766359290100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a40325a-392f-4665-9a63-345bf188950d", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766359337900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d3d7670-62d0-4b2c-975c-7ea32a315bf8", "name": "default@CompileArkTS work[1] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766360180100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3d05735-c096-4892-ac22-335382439099", "name": "default@CompileArkTS work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766360268800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00786291-c2b0-44d5-bf7b-49b77a83c1ee", "name": "entry : default@CompileArkTS cost memory 2.5058670043945312", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766360444100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9fc1e54-a1b7-4ca9-b838-31d1d7fa8392", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766366966500, "endTime": 146766374858300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "49d393ad-df2c-4b24-b3c5-3eb3f3184715", "logId": "e1b7fe2a-6730-469a-aa7e-e989c2d7f744"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49d393ad-df2c-4b24-b3c5-3eb3f3184715", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766362118500}, "additional": {"logType": "detail", "children": [], "durationId": "f9fc1e54-a1b7-4ca9-b838-31d1d7fa8392"}}, {"head": {"id": "3fcf84de-a9e7-4bf1-973b-7931dc02e85e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766362996000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f75fc925-acf8-4d54-bd32-143a81ffeb77", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766363096100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b117013a-cb93-47ae-a3d4-85c80e419318", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766366980700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e08df304-1fe8-4b69-a7d8-e2cee3c7e680", "name": "entry : default@BuildJS cost memory 0.35483551025390625", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766374582400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4913ad6-b7d7-4cce-bdbb-39b9083f7fc3", "name": "runTaskFromQueue task cost before running: 2 s 837 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766374782400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1b7fe2a-6730-469a-aa7e-e989c2d7f744", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766366966500, "endTime": 146766374858300, "totalTime": 7766600}, "additional": {"logType": "info", "children": [], "durationId": "f9fc1e54-a1b7-4ca9-b838-31d1d7fa8392"}}, {"head": {"id": "75776779-47a0-4c8d-bb1c-b96bf0818e8e", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766380334800, "endTime": 146766383477600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6f021440-6a48-4b29-9793-7b0971554a92", "logId": "b584eb7e-f57e-4b39-a274-896f184a9808"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f021440-6a48-4b29-9793-7b0971554a92", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766376847300}, "additional": {"logType": "detail", "children": [], "durationId": "75776779-47a0-4c8d-bb1c-b96bf0818e8e"}}, {"head": {"id": "c60a9561-ccb3-4afd-8b51-6c95bd9170b9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766377857800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54775c8f-8625-47a5-98ba-59245972bd8b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766377981100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15bee2e5-41ab-47ba-9042-d48faf4a1634", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766380347600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "370903f6-32e9-43b9-a8ef-997ebbacdb8e", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766381261400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88efebd6-67d5-450e-b9e8-41953f79c330", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766383258800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77e877e2-e06f-4d81-97e7-ad3b433bb491", "name": "entry : default@CacheNativeLibs cost memory 0.09920501708984375", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766383396500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b584eb7e-f57e-4b39-a274-896f184a9808", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766380334800, "endTime": 146766383477600}, "additional": {"logType": "info", "children": [], "durationId": "75776779-47a0-4c8d-bb1c-b96bf0818e8e"}}, {"head": {"id": "ea670ce4-4827-4397-bf31-538762d1147c", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146767428616000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee9ed57c-7676-404e-95be-bd5e0c85bd85", "name": "CopyResources is end, endTime: 146767428806100", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146767428812200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14377703-bc5e-45d8-bbb7-45439998ead8", "name": "default@CompileArkTS work[1] done.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146767429005000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e3e7dd6-151f-450c-83f7-5050c11d4831", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Worker18", "startTime": 146767412417100, "endTime": 146767428183300}, "additional": {"logType": "info", "children": [], "durationId": "d2a2d6ca-d908-4a96-ac7f-d8b038effce6", "parent": "9d744911-60b0-4822-bc2b-a5dab36ecd8d"}}, {"head": {"id": "278f4340-2849-4c0a-b7c6-89285f88afb7", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146767429211300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df5904a0-06e1-4d2b-81e4-ead7241377c8", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778563027400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f4828b1-c8ae-4083-914d-f7382e0cb7b4", "name": "default@CompileArkTS work[0] failed.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778563338300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b327389b-2ccd-4ab5-9328-2d4ca2d14b71", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Worker4", "startTime": 146766357732000, "endTime": 146778563245700}, "additional": {"logType": "error", "children": [], "durationId": "89405332-d19a-4b7e-86ff-f296e88fcba5", "parent": "9d744911-60b0-4822-bc2b-a5dab36ecd8d"}}, {"head": {"id": "9d744911-60b0-4822-bc2b-a5dab36ecd8d", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146766301702500, "endTime": 146778563477900}, "additional": {"logType": "error", "children": ["b327389b-2ccd-4ab5-9328-2d4ca2d14b71", "9e3e7dd6-151f-450c-83f7-5050c11d4831"], "durationId": "5e70aadb-1e39-47fb-b994-b39593a6b309"}}, {"head": {"id": "d98b83c9-b1ec-47ae-979c-5083d6bf45eb", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778563634100}, "additional": {"logType": "debug", "children": [], "durationId": "5e70aadb-1e39-47fb-b994-b39593a6b309"}}, {"head": {"id": "373b547a-802a-4861-b6bb-027e233d61f0", "name": "ERROR: stacktrace = Error: ArkTS Compiler Error\r\n\u001b[33m1 WARN: \u001b[33m\u001b[33mArkTS:WARN: For details about ArkTS syntax errors, see FAQs\u001b[39m\u001b[39m\r\n\u001b[31m1 ERROR: \u001b[31m10605099 ArkTS Compiler Error\r\nError Message: It is possible to spread only arrays or classes derived from arrays into the rest parameter or array literals (arkts-no-spread) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppListPage.ets:128:38\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m2 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'reviews' does not exist on type 'AppReviewListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppDetailPage.ets:107:40\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m3 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'reviews' does not exist on type 'AppReviewListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppDetailPage.ets:109:60\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m4 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'apps' does not exist on type 'AppListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppListPage.ets:128:55\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m5 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'apps' does not exist on type 'AppListData'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppListPage.ets:131:37\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:6 WARN:1}\u001b[39m\n    at runArkPack (C:\\Program Files\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5479)", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778564233900}, "additional": {"logType": "debug", "children": [], "durationId": "5e70aadb-1e39-47fb-b994-b39593a6b309"}}, {"head": {"id": "5ab6e583-22b6-41a4-891c-5782be312a77", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778575693700, "endTime": 146778575964400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e08d766b-04b6-4983-b6e9-4e4c24674527", "logId": "e4eb5795-20c6-493e-8988-274ccdbd5b21"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4eb5795-20c6-493e-8988-274ccdbd5b21", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778575693700, "endTime": 146778575964400}, "additional": {"logType": "info", "children": [], "durationId": "5ab6e583-22b6-41a4-891c-5782be312a77"}}, {"head": {"id": "f9b75539-c40f-4549-b137-eeaa1b4cd5df", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146763538280500, "endTime": 146778576286900}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 43, "second": 28}, "completeCommand": "{\"prop\":[\"product=default\"],\"mode\":\"module\",\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.8", "category": "build", "state": "failed"}}, {"head": {"id": "510992eb-845d-4420-9cfa-6cfa1f5f871e", "name": "BUILD FAILED in 15 s 38 ms ", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778576327800}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "9fde4cfa-e47a-4389-a7db-38adcee88c4f", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778576603200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a8c159f-50ec-4ec1-92af-1953f783e5dc", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778576704800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de7df27a-e975-4683-abdb-041e5e0baae3", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778577126700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30f6b164-12d9-4d91-a691-2cf64416cd94", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778577189400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "235435ca-80e8-40bd-88f7-e44c59804cc5", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778577221400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f581427-10d1-40c7-abc7-1b1a30eeea0c", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778577247500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faf850d3-2ba2-4194-a82c-3a887d5d0c8b", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778577273400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "833c258c-d1d8-465f-af3d-ecc55a1af912", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778577766000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41e3da28-ce68-4e7f-86c9-9112a575e1c2", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778577962000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ceb9fe5-dde0-4961-b311-1344e87a3aa3", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778578014900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66869078-ac9c-4d1e-a0b3-b0c6c8412cc9", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778578045000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "598db5d6-6667-42ca-a806-a16ab028ba56", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778578073500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df73b355-763c-476b-bd8c-adb0301f11c3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778578098500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70bf501d-dab0-4fa4-a708-0c0c72b58259", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778579055000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dda0185-3ce3-4ebd-850c-d37c2e4d8261", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778579335100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8e72f22-ee27-4924-980e-19971ab357bf", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778579563500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7af567ca-d8e6-4d24-ab01-3f08f17a4cf0", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778579621500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a019ea85-53cd-4db2-95d8-3a25e1a3ef26", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778579654400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43051b44-e562-4a1f-884b-bf483732afc0", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778579679400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db2a81ff-75a0-495e-877a-ec59a3dd0620", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778579703300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9ab45f0-5140-480e-9720-de983199cde7", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778579726700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e9bd168-23df-4b9e-a330-9b2117249228", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778582787800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff1bbad1-90c0-4b12-9a18-f4f81948a2b3", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778583645400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbc97a68-1280-40bd-a851-4e6b6d727e50", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778584026900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e486f9c-bce1-4f80-b666-e2ebcf36431c", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778584246800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e33fcc5-ffab-4d31-ae4b-bb104cb27416", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778584432600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b0a4d76-9148-4537-8e20-f0a233f3520a", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778585043200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fc10593-38aa-4b5e-ba1e-a45ec86a22fe", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778585108500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c46c262-543f-4a7d-9ae7-77f90e33cab1", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778585297500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6b0725d-1fff-48a7-b297-4418c187eef7", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778585590900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c88b0670-93b8-4036-ab2c-344fcb61c31f", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778586439700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "604fc339-b305-4e4b-9b13-40f378735dc2", "name": "Incremental task entry:default@CompileArkTS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778587062500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcbd9e75-bc94-4382-806b-e53f0da99186", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778588665800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "234af79b-5f2d-4442-b2db-b11b2287331b", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778589219400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87def8f7-d357-4e85-8ca0-959ec6e1427d", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778589554100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac160b7b-1039-4c19-a0ef-36839c35d931", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778589797000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c95a732-9f61-424c-93ea-45b73b26a54a", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778589993700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a698a00a-a82d-4b0f-923c-da778b4e68cc", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778590617900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "953bcb3e-3cd3-4cc6-b558-a3679ac77557", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778591590400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc9dbfa6-1bcb-4aeb-be2f-64320d1e9c70", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778591942600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "172d9c3d-c4a3-4fce-a86c-877b7c0f06a7", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 5440, "tid": "Main Thread", "startTime": 146778592019700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}