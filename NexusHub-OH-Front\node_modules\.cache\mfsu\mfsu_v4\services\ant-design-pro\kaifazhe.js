"use strict";
import { request } from "@umijs/max";
export async function postAdminDevelopersIdVerify(params, body, options) {
  const { id: param0, ...queryParams } = params;
  return request(
    `/admin/developers/${param0}/verify`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      params: { ...queryParams },
      data: body,
      ...options || {}
    }
  );
}
export async function getAdminDevelopersVerify(params, options) {
  return request(
    "/admin/developers/verify",
    {
      method: "GET",
      params: {
        // page has a default value: 1
        page: "1",
        // page_size has a default value: 20
        page_size: "20",
        // status has a default value: "pending"
        status: '"pending"',
        ...params
      },
      ...options || {}
    }
  );
}
export async function getAdminDevelopersStats(options) {
  return request(
    "/admin/developers/stats",
    {
      method: "GET",
      ...options || {}
    }
  );
}
export async function getAdminDevelopersRecent(params, options) {
  return request(
    "/admin/developers/recent",
    {
      method: "GET",
      params: {
        limit: 10,
        ...params
      },
      ...options || {}
    }
  );
}
export async function getApiV1UploadToken(params, options) {
  return request("/upload/token", {
    method: "GET",
    params: {
      ...params
    },
    ...options || {}
  });
}
export async function postDevelopersVerify(body, options) {
  return request("/developers/verify", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    data: body,
    ...options || {}
  });
}
export async function getDevelopersVerifyStatus(options) {
  return request(
    "/developers/verify/status",
    {
      method: "GET",
      ...options || {}
    }
  );
}
