"use strict";
export default {
  "component.globalHeader.search": "\u7AD9\u5167\u641C\u7D22",
  "component.globalHeader.search.example1": "\u641C\u7D22\u63D0\u793A\u58F9",
  "component.globalHeader.search.example2": "\u641C\u7D22\u63D0\u793A\u4E8C",
  "component.globalHeader.search.example3": "\u641C\u7D22\u63D0\u793A\u4E09",
  "component.globalHeader.help": "\u4F7F\u7528\u624B\u518A",
  "component.globalHeader.notification": "\u901A\u77E5",
  "component.globalHeader.notification.empty": "\u59B3\u5DF2\u67E5\u770B\u6240\u6709\u901A\u77E5",
  "component.globalHeader.message": "\u6D88\u606F",
  "component.globalHeader.message.empty": "\u60A8\u5DF2\u8B80\u5B8C\u6240\u6709\u6D88\u606F",
  "component.globalHeader.event": "\u5F85\u8FA6",
  "component.globalHeader.event.empty": "\u59B3\u5DF2\u5B8C\u6210\u6240\u6709\u5F85\u8FA6",
  "component.noticeIcon.clear": "\u6E05\u7A7A",
  "component.noticeIcon.cleared": "\u6E05\u7A7A\u4E86",
  "component.noticeIcon.empty": "\u66AB\u7121\u8CC7\u6599",
  "component.noticeIcon.view-more": "\u67E5\u770B\u66F4\u591A"
};
