if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface FeaturedCollectionDetailPage_Params {
    collection?: FeaturedCollectionModel | null;
    apps?: FeaturedCollectionAppModel[];
    isLoading?: boolean;
    isLoadingMore?: boolean;
    hasMore?: boolean;
    currentPage?: number;
    pageSize?: number;
    errorMessage?: string;
    showError?: boolean;
    collectionId?: number;
    apiService?: ApiService;
    deviceUtils?: DeviceUtils;
}
import type { FeaturedCollectionModel, FeaturedCollectionDetailResponse, FeaturedCollectionAppsResponse, FeaturedCollectionAppModel } from '../models/FeaturedCollection';
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { LoadingView, LoadMoreView } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import hilog from "@ohos:hilog";
class FeaturedCollectionDetailPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__collection = new ObservedPropertyObjectPU(null, this, "collection");
        this.__apps = new ObservedPropertyObjectPU([], this, "apps");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__isLoadingMore = new ObservedPropertySimplePU(false, this, "isLoadingMore");
        this.__hasMore = new ObservedPropertySimplePU(true, this, "hasMore");
        this.__currentPage = new ObservedPropertySimplePU(1, this, "currentPage");
        this.__pageSize = new ObservedPropertySimplePU(20, this, "pageSize");
        this.__errorMessage = new ObservedPropertySimplePU('', this, "errorMessage");
        this.__showError = new ObservedPropertySimplePU(false, this, "showError");
        this.__collectionId = new ObservedPropertySimplePU(0, this, "collectionId");
        this.apiService = ApiService.getInstance();
        this.deviceUtils = DeviceUtils.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: FeaturedCollectionDetailPage_Params) {
        if (params.collection !== undefined) {
            this.collection = params.collection;
        }
        if (params.apps !== undefined) {
            this.apps = params.apps;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.isLoadingMore !== undefined) {
            this.isLoadingMore = params.isLoadingMore;
        }
        if (params.hasMore !== undefined) {
            this.hasMore = params.hasMore;
        }
        if (params.currentPage !== undefined) {
            this.currentPage = params.currentPage;
        }
        if (params.pageSize !== undefined) {
            this.pageSize = params.pageSize;
        }
        if (params.errorMessage !== undefined) {
            this.errorMessage = params.errorMessage;
        }
        if (params.showError !== undefined) {
            this.showError = params.showError;
        }
        if (params.collectionId !== undefined) {
            this.collectionId = params.collectionId;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
    }
    updateStateVars(params: FeaturedCollectionDetailPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__collection.purgeDependencyOnElmtId(rmElmtId);
        this.__apps.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoadingMore.purgeDependencyOnElmtId(rmElmtId);
        this.__hasMore.purgeDependencyOnElmtId(rmElmtId);
        this.__currentPage.purgeDependencyOnElmtId(rmElmtId);
        this.__pageSize.purgeDependencyOnElmtId(rmElmtId);
        this.__errorMessage.purgeDependencyOnElmtId(rmElmtId);
        this.__showError.purgeDependencyOnElmtId(rmElmtId);
        this.__collectionId.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__collection.aboutToBeDeleted();
        this.__apps.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__isLoadingMore.aboutToBeDeleted();
        this.__hasMore.aboutToBeDeleted();
        this.__currentPage.aboutToBeDeleted();
        this.__pageSize.aboutToBeDeleted();
        this.__errorMessage.aboutToBeDeleted();
        this.__showError.aboutToBeDeleted();
        this.__collectionId.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __collection: ObservedPropertyObjectPU<FeaturedCollectionModel | null>;
    get collection() {
        return this.__collection.get();
    }
    set collection(newValue: FeaturedCollectionModel | null) {
        this.__collection.set(newValue);
    }
    private __apps: ObservedPropertyObjectPU<FeaturedCollectionAppModel[]>;
    get apps() {
        return this.__apps.get();
    }
    set apps(newValue: FeaturedCollectionAppModel[]) {
        this.__apps.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __isLoadingMore: ObservedPropertySimplePU<boolean>;
    get isLoadingMore() {
        return this.__isLoadingMore.get();
    }
    set isLoadingMore(newValue: boolean) {
        this.__isLoadingMore.set(newValue);
    }
    private __hasMore: ObservedPropertySimplePU<boolean>;
    get hasMore() {
        return this.__hasMore.get();
    }
    set hasMore(newValue: boolean) {
        this.__hasMore.set(newValue);
    }
    private __currentPage: ObservedPropertySimplePU<number>;
    get currentPage() {
        return this.__currentPage.get();
    }
    set currentPage(newValue: number) {
        this.__currentPage.set(newValue);
    }
    private __pageSize: ObservedPropertySimplePU<number>;
    get pageSize() {
        return this.__pageSize.get();
    }
    set pageSize(newValue: number) {
        this.__pageSize.set(newValue);
    }
    private __errorMessage: ObservedPropertySimplePU<string>;
    get errorMessage() {
        return this.__errorMessage.get();
    }
    set errorMessage(newValue: string) {
        this.__errorMessage.set(newValue);
    }
    private __showError: ObservedPropertySimplePU<boolean>;
    get showError() {
        return this.__showError.get();
    }
    set showError(newValue: boolean) {
        this.__showError.set(newValue);
    }
    private __collectionId: ObservedPropertySimplePU<number>;
    get collectionId() {
        return this.__collectionId.get();
    }
    set collectionId(newValue: number) {
        this.__collectionId.set(newValue);
    }
    private apiService: ApiService;
    private deviceUtils: DeviceUtils;
    /**
     * 页面即将出现时的回调
     */
    aboutToAppear() {
        // 获取路由参数
        const params = this.getUIContext().getRouter().getParams();
        if (params) {
            this.collectionId = (params as Record<string, Object>).collectionId as number;
            this.collection = (params as Record<string, Object>).collection as FeaturedCollectionModel;
        }
        if (this.collectionId > 0) {
            this.loadCollectionDetail();
            this.loadCollectionApps();
        }
    }
    /**
     * 加载精选集详情
     */
    private async loadCollectionDetail() {
        try {
            const response: FeaturedCollectionDetailResponse = await this.apiService.getFeaturedCollectionDetail(this.collectionId);
            if (response && response.code === 200 && response.data) {
                this.collection = response.data;
            }
        }
        catch (error) {
            hilog.error(0x0000, 'FeaturedCollectionDetailPage', '加载精选集详情失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 加载精选集应用列表
     */
    private async loadCollectionApps() {
        if (this.isLoading)
            return;
        this.isLoading = true;
        this.showError = false;
        this.currentPage = 1;
        this.hasMore = true;
        try {
            const response: FeaturedCollectionAppsResponse = await this.apiService.getFeaturedCollectionApps(this.collectionId, this.currentPage, this.pageSize);
            if (response && response.code === 200 && response.data) {
                this.apps = response.data.list || [];
                this.hasMore = response.data.pagination ?
                    response.data.pagination.page < response.data.pagination.total_pages : false;
            }
            else {
                this.showError = true;
                this.errorMessage = response?.message || '加载失败';
            }
        }
        catch (error) {
            hilog.error(0x0000, 'FeaturedCollectionDetailPage', '加载精选集应用失败: %{public}s', JSON.stringify(error));
            this.showError = true;
            this.errorMessage = '网络连接失败，请检查网络设置';
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 加载更多应用
     */
    private async loadMoreApps() {
        if (this.isLoadingMore || !this.hasMore)
            return;
        this.isLoadingMore = true;
        try {
            const nextPage = this.currentPage + 1;
            const response: FeaturedCollectionAppsResponse = await this.apiService.getFeaturedCollectionApps(this.collectionId, nextPage, this.pageSize);
            if (response && response.code === 200 && response.data) {
                const newApps: FeaturedCollectionAppModel[] = response.data.list || [];
                this.apps = this.apps.concat(newApps);
                this.currentPage = nextPage;
                this.hasMore = response.data.pagination ?
                    response.data.pagination.page < response.data.pagination.total_pages : false;
            }
            else {
                this.hasMore = false;
                this.getUIContext().getPromptAction().showToast({
                    message: '加载失败',
                    duration: 2000
                });
            }
        }
        catch (error) {
            hilog.error(0x0000, 'FeaturedCollectionDetailPage', '加载更多应用失败: %{public}s', JSON.stringify(error));
            this.hasMore = false;
            this.getUIContext().getPromptAction().showToast({
                message: '网络连接失败',
                duration: 2000
            });
        }
        finally {
            this.isLoadingMore = false;
        }
    }
    /**
     * 处理应用卡片点击
     */
    private handleAppClick(app: FeaturedCollectionAppModel) {
        this.getUIContext().getRouter().pushUrl({
            url: 'pages/AppDetailPage',
            params: {
                appId: app.id,
                app: app
            }
        }).catch((error: Error) => {
            hilog.error(0x0000, 'FeaturedCollectionDetailPage', '导航到应用详情页失败: %{public}s', JSON.stringify(error));
            this.getUIContext().getPromptAction().showToast({
                message: '页面跳转失败',
                duration: 2000
            });
        });
    }
    /**
     * 返回上一页
     */
    private goBack() {
        this.getUIContext().getRouter().back();
    }
    /**
     * 构建精选集头部信息
     */
    private buildCollectionHeader(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.collection) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: 16 });
                        Column.padding(16);
                        Column.backgroundColor(Constants.COLORS.WHITE);
                        Column.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                        Column.margin({ left: 16, right: 16, bottom: 8 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 封面图片
                        Image.create(this.collection.cover_image || Constants.PLACEHOLDER_IMAGE);
                        // 封面图片
                        Image.width('100%');
                        // 封面图片
                        Image.height(200);
                        // 封面图片
                        Image.objectFit(ImageFit.Cover);
                        // 封面图片
                        Image.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                    }, Image);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 标题和描述
                        Column.create({ space: 8 });
                        // 标题和描述
                        Column.alignItems(HorizontalAlign.Start);
                        // 标题和描述
                        Column.width('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.collection.name);
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.XLARGE));
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.maxLines(2);
                        Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.collection.description);
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM));
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                        Text.maxLines(3);
                        Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`共${this.collection.app_count}个应用`);
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        Text.fontColor(Constants.COLORS.TEXT_TERTIARY);
                    }, Text);
                    Text.pop();
                    // 标题和描述
                    Column.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    /**
     * 构建应用卡片（适配精选集应用数据结构）
     */
    buildAppCard(app: FeaturedCollectionAppModel, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
            Row.padding(16);
            Row.backgroundColor(Constants.COLORS.WHITE);
            Row.borderRadius(12);
            Row.shadow({
                radius: 4,
                color: '#0D000000',
                offsetX: 0,
                offsetY: 1
            });
            Row.onClick(() => this.handleAppClick(app));
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 应用图标
            Image.create(app.icon || { "id": 16777243, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 应用图标
            Image.width(56);
            // 应用图标
            Image.height(56);
            // 应用图标
            Image.borderRadius(12);
            // 应用图标
            Image.objectFit(ImageFit.Cover);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 应用信息
            Column.create();
            // 应用信息
            Column.layoutWeight(1);
            // 应用信息
            Column.margin({ left: 12 });
            // 应用信息
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 应用名称
            Text.create(app.name);
            // 应用名称
            Text.fontSize(Constants.FONT_SIZE.MEDIUM);
            // 应用名称
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            // 应用名称
            Text.fontWeight(FontWeight.Medium);
            // 应用名称
            Text.maxLines(1);
            // 应用名称
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
            // 应用名称
            Text.width('100%');
            // 应用名称
            Text.textAlign(TextAlign.Start);
        }, Text);
        // 应用名称
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 应用描述
            Text.create(app.short_description || app.description);
            // 应用描述
            Text.fontSize(Constants.FONT_SIZE.SMALL);
            // 应用描述
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            // 应用描述
            Text.maxLines(2);
            // 应用描述
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
            // 应用描述
            Text.width('100%');
            // 应用描述
            Text.textAlign(TextAlign.Start);
            // 应用描述
            Text.margin({ top: 4 });
        }, Text);
        // 应用描述
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 评分和下载量
            Row.create();
            // 评分和下载量
            Row.width('100%');
            // 评分和下载量
            Row.margin({ top: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 评分
            Row.create();
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777274, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(12);
            Image.height(12);
            Image.fillColor(Constants.COLORS.RATING);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create((app.rating || 0).toFixed(1));
            Text.fontSize(Constants.FONT_SIZE.SMALL);
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            Text.margin({ left: 4 });
        }, Text);
        Text.pop();
        // 评分
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 下载量
            Text.create(`${this.formatDownloadCount(app.download_count)}下载`);
            // 下载量
            Text.fontSize(Constants.FONT_SIZE.SMALL);
            // 下载量
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            // 下载量
            Text.margin({ left: 16 });
        }, Text);
        // 下载量
        Text.pop();
        // 评分和下载量
        Row.pop();
        // 应用信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 下载按钮
            Button.createWithLabel('下载');
            // 下载按钮
            Button.fontSize(Constants.FONT_SIZE.SMALL);
            // 下载按钮
            Button.fontColor(Constants.COLORS.WHITE);
            // 下载按钮
            Button.backgroundColor(Constants.COLORS.PRIMARY);
            // 下载按钮
            Button.borderRadius(16);
            // 下载按钮
            Button.padding({ left: 16, right: 16, top: 6, bottom: 6 });
            // 下载按钮
            Button.margin({ left: 12 });
        }, Button);
        // 下载按钮
        Button.pop();
        Row.pop();
    }
    /**
     * 格式化下载数量
     */
    private formatDownloadCount(count: number): string {
        if (count >= 10000) {
            return `${Math.floor(count / 10000)}万`;
        }
        else if (count >= 1000) {
            return `${Math.floor(count / 1000)}千`;
        }
        else {
            return count.toString();
        }
    }
    /**
     * 构建页面UI
     */
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.width('100%');
            Stack.height('100%');
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部标题栏
            Row.create();
            // 顶部标题栏
            Row.width('100%');
            // 顶部标题栏
            Row.height(56);
            // 顶部标题栏
            Row.padding({ left: 16, right: 16 });
            // 顶部标题栏
            Row.backgroundColor(Constants.COLORS.WHITE);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.width(40);
            Button.height(40);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => this.goBack());
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777246, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor(Constants.COLORS.TEXT_PRIMARY);
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.collection?.name || '精选集详情');
            Text.fontSize(Constants.FONT_SIZE.LARGE);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 占位，保持标题居中
            Row.create();
            // 占位，保持标题居中
            Row.width(40);
            // 占位，保持标题居中
            Row.height(40);
        }, Row);
        // 占位，保持标题居中
        Row.pop();
        // 顶部标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 内容区域
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/FeaturedCollectionDetailPage.ets", line: 353, col: 11 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.showError) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create({ "id": 16777254, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Image.width(64);
                        Image.height(64);
                        Image.fillColor(Constants.COLORS.TEXT_SECONDARY);
                        Image.margin({ bottom: 16 });
                    }, Image);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.errorMessage);
                        Text.fontSize(Constants.FONT_SIZE.NORMAL);
                        Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
                        Text.textAlign(TextAlign.Center);
                        Text.margin({ bottom: 24 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('重试');
                        Button.fontSize(Constants.FONT_SIZE.NORMAL);
                        Button.fontColor(Constants.COLORS.PRIMARY);
                        Button.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
                        Button.borderRadius(8);
                        Button.padding({ left: 24, right: 24, top: 8, bottom: 8 });
                        Button.onClick(() => this.loadCollectionApps());
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 应用列表
                        List.create();
                        // 应用列表
                        List.layoutWeight(1);
                        // 应用列表
                        List.scrollBar(BarState.Auto);
                        // 应用列表
                        List.edgeEffect(EdgeEffect.Spring);
                        // 应用列表
                        List.onReachEnd(() => {
                            if (this.hasMore && !this.isLoadingMore) {
                                this.loadMoreApps();
                            }
                        });
                    }, List);
                    {
                        const itemCreation = (elmtId, isInitialRender) => {
                            ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                            itemCreation2(elmtId, isInitialRender);
                            if (!isInitialRender) {
                                // 精选集头部信息
                                ListItem.pop();
                            }
                            ViewStackProcessor.StopGetAccessRecording();
                        };
                        const itemCreation2 = (elmtId, isInitialRender) => {
                            ListItem.create(deepRenderFunction, true);
                        };
                        const deepRenderFunction = (elmtId, isInitialRender) => {
                            itemCreation(elmtId, isInitialRender);
                            this.buildCollectionHeader.bind(this)();
                            // 精选集头部信息
                            ListItem.pop();
                        };
                        this.observeComponentCreation2(itemCreation2, ListItem);
                        // 精选集头部信息
                        ListItem.pop();
                    }
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 应用列表
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const app = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        Column.create();
                                        Column.margin({ left: 16, right: 16, bottom: 12 });
                                    }, Column);
                                    this.buildAppCard.bind(this)(app);
                                    Column.pop();
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.apps, forEachItemGenFunction, (app: FeaturedCollectionAppModel) => app.id.toString(), true, false);
                    }, ForEach);
                    // 应用列表
                    ForEach.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 加载更多组件
                        if (this.hasMore || this.isLoadingMore) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                {
                                    const itemCreation = (elmtId, isInitialRender) => {
                                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                        itemCreation2(elmtId, isInitialRender);
                                        if (!isInitialRender) {
                                            ListItem.pop();
                                        }
                                        ViewStackProcessor.StopGetAccessRecording();
                                    };
                                    const itemCreation2 = (elmtId, isInitialRender) => {
                                        ListItem.create(deepRenderFunction, true);
                                        ListItem.padding({ top: 12, bottom: 12 });
                                    };
                                    const deepRenderFunction = (elmtId, isInitialRender) => {
                                        itemCreation(elmtId, isInitialRender);
                                        {
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                if (isInitialRender) {
                                                    let componentCall = new LoadMoreView(this, {
                                                        isLoading: this.isLoadingMore,
                                                        hasMore: this.hasMore,
                                                        onLoadMore: () => {
                                                            if (!this.isLoadingMore && this.hasMore) {
                                                                this.loadMoreApps();
                                                            }
                                                        }
                                                    }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/FeaturedCollectionDetailPage.ets", line: 401, col: 17 });
                                                    ViewPU.create(componentCall);
                                                    let paramsLambda = () => {
                                                        return {
                                                            isLoading: this.isLoadingMore,
                                                            hasMore: this.hasMore,
                                                            onLoadMore: () => {
                                                                if (!this.isLoadingMore && this.hasMore) {
                                                                    this.loadMoreApps();
                                                                }
                                                            }
                                                        };
                                                    };
                                                    componentCall.paramsGenerator_ = paramsLambda;
                                                }
                                                else {
                                                    this.updateStateVarsOfChildByElmtId(elmtId, {
                                                        isLoading: this.isLoadingMore,
                                                        hasMore: this.hasMore
                                                    });
                                                }
                                            }, { name: "LoadMoreView" });
                                        }
                                        ListItem.pop();
                                    };
                                    this.observeComponentCreation2(itemCreation2, ListItem);
                                    ListItem.pop();
                                }
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    // 应用列表
                    List.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
        Stack.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "FeaturedCollectionDetailPage";
    }
}
registerNamedRoute(() => new FeaturedCollectionDetailPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/FeaturedCollectionDetailPage", pageFullPath: "entry/src/main/ets/pages/FeaturedCollectionDetailPage", integratedHsp: "false", moduleType: "followWithHap" });
