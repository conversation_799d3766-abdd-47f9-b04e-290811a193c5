"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { List } from "antd";
const passwordStrength = {
  strong: /* @__PURE__ */ jsx("span", { className: "strong", children: "\u5F3A" }),
  medium: /* @__PURE__ */ jsx("span", { className: "medium", children: "\u4E2D" }),
  weak: /* @__PURE__ */ jsx("span", { className: "weak", children: "\u5F31 Weak" })
};
const SecurityView = () => {
  const getData = () => [
    {
      title: "\u8D26\u6237\u5BC6\u7801",
      description: /* @__PURE__ */ jsxs(Fragment, { children: [
        "\u5F53\u524D\u5BC6\u7801\u5F3A\u5EA6\uFF1A",
        passwordStrength.strong
      ] }),
      actions: [/* @__PURE__ */ jsx("a", { children: "\u4FEE\u6539" }, "Modify")]
    }
  ];
  const data = getData();
  return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsx(
    List,
    {
      itemLayout: "horizontal",
      dataSource: data,
      renderItem: (item) => /* @__PURE__ */ jsx(List.Item, { actions: item.actions, children: /* @__PURE__ */ jsx(List.Item.Meta, { title: item.title, description: item.description }) })
    }
  ) });
};
export default SecurityView;
