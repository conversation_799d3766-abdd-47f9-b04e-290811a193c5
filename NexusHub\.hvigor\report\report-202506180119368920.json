{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "84509b40-aa28-4d9c-bda8-95aa32813ada", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126329451500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a61d566-de0c-45ff-b8bf-995a1e0c134d", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126329699700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31b57390-07db-4e13-b86d-7d21de36b927", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126329944300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9819860f-c71f-4a96-914a-f2229cbcae94", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126334265000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6542c4c-918b-47f1-8403-a1c16311fb3c", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126334958300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8978adfa-d95e-4c22-8387-eec5e0780809", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126335178700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4af30ba-7bd3-48e7-93ee-ad99b2f8241d", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126335329000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78233ee6-6e02-4a3b-9cdb-5bb744191a29", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126335438600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cfdfc32-a296-4556-8a58-e4db2aa7fa62", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126369864900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff1a16ad-3cc4-489c-b265-a9ba1688136b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147106815400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f720bd5-7ea7-47b6-943b-e9ce45f40a4d", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147117008700, "endTime": 156147555785000}, "additional": {"children": ["90b46cbc-40d8-4253-92ba-e7ce1686b7b2", "06fc69bb-795b-442b-84da-c5b1f4583689", "689a7650-7715-4603-a45f-2f9315fd4977", "8cf5ec09-a960-41b7-9ba0-9baf3ff7880d", "2b8b2b19-33ce-4daf-8488-ac051e3d2c4d", "a719c1d7-3b60-4c19-a75e-2c63b003bf31", "eb15ad2c-4871-4b1a-8170-714b3cd0e358"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "1a034e7a-0577-4b45-86e9-57a2ec215be0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90b46cbc-40d8-4253-92ba-e7ce1686b7b2", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147117010100, "endTime": 156147146897300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f720bd5-7ea7-47b6-943b-e9ce45f40a4d", "logId": "41396d10-2e95-44aa-9b45-43f53474787c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06fc69bb-795b-442b-84da-c5b1f4583689", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147146926400, "endTime": 156147553727300}, "additional": {"children": ["b66e8966-e9e7-495d-bf17-c5580649ca6d", "9a60f6a4-6dad-4d0b-847a-2869d00b33d8", "67e13046-ffda-4cf2-8716-02955e94843d", "5d266380-0d55-4cae-8814-28ea8e8ece0d", "5d19163f-88dd-4a41-9fb4-e94b680a41fb", "300fd450-52bb-4165-8ce3-8581906726af", "57d79f14-6516-459e-8d57-dadf6dd3f6b2", "18cec3ef-8803-43eb-b697-5bba1bc6bb1f", "4613c294-ea3e-47c9-8a41-2816fc468feb"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f720bd5-7ea7-47b6-943b-e9ce45f40a4d", "logId": "18ac34fe-93c7-4176-8117-1aa904e41a6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "689a7650-7715-4603-a45f-2f9315fd4977", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147553768200, "endTime": 156147555766400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f720bd5-7ea7-47b6-943b-e9ce45f40a4d", "logId": "d1f755c0-0b40-41f9-992f-19fb61633e00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8cf5ec09-a960-41b7-9ba0-9baf3ff7880d", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147555772000, "endTime": 156147555781600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f720bd5-7ea7-47b6-943b-e9ce45f40a4d", "logId": "feab2760-96fb-4305-92da-974bdcff6c6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b8b2b19-33ce-4daf-8488-ac051e3d2c4d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147122257300, "endTime": 156147122314800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f720bd5-7ea7-47b6-943b-e9ce45f40a4d", "logId": "d6739033-5096-4206-918b-ead313cce70c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6739033-5096-4206-918b-ead313cce70c", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147122257300, "endTime": 156147122314800}, "additional": {"logType": "info", "children": [], "durationId": "2b8b2b19-33ce-4daf-8488-ac051e3d2c4d", "parent": "1a034e7a-0577-4b45-86e9-57a2ec215be0"}}, {"head": {"id": "a719c1d7-3b60-4c19-a75e-2c63b003bf31", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147134822800, "endTime": 156147134858600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f720bd5-7ea7-47b6-943b-e9ce45f40a4d", "logId": "22158d86-aeca-494d-b411-84b5f5c71ac2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22158d86-aeca-494d-b411-84b5f5c71ac2", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147134822800, "endTime": 156147134858600}, "additional": {"logType": "info", "children": [], "durationId": "a719c1d7-3b60-4c19-a75e-2c63b003bf31", "parent": "1a034e7a-0577-4b45-86e9-57a2ec215be0"}}, {"head": {"id": "793c5bf3-3776-41b8-ab41-e27103258227", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147135167400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4298fa8c-51ad-4839-bd9c-a178d21569da", "name": "Cache service initialization finished in 12 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147146526500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41396d10-2e95-44aa-9b45-43f53474787c", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147117010100, "endTime": 156147146897300}, "additional": {"logType": "info", "children": [], "durationId": "90b46cbc-40d8-4253-92ba-e7ce1686b7b2", "parent": "1a034e7a-0577-4b45-86e9-57a2ec215be0"}}, {"head": {"id": "b66e8966-e9e7-495d-bf17-c5580649ca6d", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147160005700, "endTime": 156147160019900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "06fc69bb-795b-442b-84da-c5b1f4583689", "logId": "acc1b113-789b-41c6-a0e6-f4500e01fd44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a60f6a4-6dad-4d0b-847a-2869d00b33d8", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147160126000, "endTime": 156147167701300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "06fc69bb-795b-442b-84da-c5b1f4583689", "logId": "35431c3d-9cfc-4de7-914b-13e51f4e675e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67e13046-ffda-4cf2-8716-02955e94843d", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147167720200, "endTime": 156147351109500}, "additional": {"children": ["e208b1b6-a906-47fc-9d3b-a59587467c0d", "278d89dd-6e09-448d-bb7a-72e422e1b213", "cad0036f-0b71-4242-b2b5-c2d41107c493"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "06fc69bb-795b-442b-84da-c5b1f4583689", "logId": "02c1dfd7-5d59-4730-8e57-14c7351fa664"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d266380-0d55-4cae-8814-28ea8e8ece0d", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147351129300, "endTime": 156147402225700}, "additional": {"children": ["839e39d9-0ca3-4027-b340-09656e33fb14"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "06fc69bb-795b-442b-84da-c5b1f4583689", "logId": "0256e9ac-7cdf-450d-944d-697c94eaa36e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d19163f-88dd-4a41-9fb4-e94b680a41fb", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147402298400, "endTime": 156147490912000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "06fc69bb-795b-442b-84da-c5b1f4583689", "logId": "6abd6c7b-4d01-492e-b9be-4da2764d7193"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "300fd450-52bb-4165-8ce3-8581906726af", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147493152300, "endTime": 156147521668100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "06fc69bb-795b-442b-84da-c5b1f4583689", "logId": "90bdda74-3d10-4ae4-923a-6136456cc120"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57d79f14-6516-459e-8d57-dadf6dd3f6b2", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147521697600, "endTime": 156147553436900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "06fc69bb-795b-442b-84da-c5b1f4583689", "logId": "29ed1b84-ca40-4ecd-adfb-b4c208ca127e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18cec3ef-8803-43eb-b697-5bba1bc6bb1f", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147553462700, "endTime": 156147553701800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "06fc69bb-795b-442b-84da-c5b1f4583689", "logId": "4fe99edd-b16f-447c-bfb9-456a9f6cd809"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "acc1b113-789b-41c6-a0e6-f4500e01fd44", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147160005700, "endTime": 156147160019900}, "additional": {"logType": "info", "children": [], "durationId": "b66e8966-e9e7-495d-bf17-c5580649ca6d", "parent": "18ac34fe-93c7-4176-8117-1aa904e41a6a"}}, {"head": {"id": "35431c3d-9cfc-4de7-914b-13e51f4e675e", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147160126000, "endTime": 156147167701300}, "additional": {"logType": "info", "children": [], "durationId": "9a60f6a4-6dad-4d0b-847a-2869d00b33d8", "parent": "18ac34fe-93c7-4176-8117-1aa904e41a6a"}}, {"head": {"id": "e208b1b6-a906-47fc-9d3b-a59587467c0d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147168667800, "endTime": 156147168695700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67e13046-ffda-4cf2-8716-02955e94843d", "logId": "8232b19d-e954-4123-abfa-cae286c612f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8232b19d-e954-4123-abfa-cae286c612f2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147168667800, "endTime": 156147168695700}, "additional": {"logType": "info", "children": [], "durationId": "e208b1b6-a906-47fc-9d3b-a59587467c0d", "parent": "02c1dfd7-5d59-4730-8e57-14c7351fa664"}}, {"head": {"id": "278d89dd-6e09-448d-bb7a-72e422e1b213", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147173460100, "endTime": 156147349981000}, "additional": {"children": ["d49522e6-c890-4a7a-89d6-5cac41ad0b3a", "4ef67e9f-e2e2-45ae-b125-5b01ab673908"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67e13046-ffda-4cf2-8716-02955e94843d", "logId": "347696c5-c0d5-4f1c-883d-f7050c54e160"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d49522e6-c890-4a7a-89d6-5cac41ad0b3a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147173462700, "endTime": 156147202657500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "278d89dd-6e09-448d-bb7a-72e422e1b213", "logId": "8d9016ae-4536-49eb-948e-ff4ebcb9b99e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ef67e9f-e2e2-45ae-b125-5b01ab673908", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147202680800, "endTime": 156147349960800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "278d89dd-6e09-448d-bb7a-72e422e1b213", "logId": "8209dc47-f99e-4706-87c3-c64f717a7bb7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e1fdd82-ad8f-432d-8006-5c9d770be4bc", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147173471300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3c0a37e-2061-43f1-9cd6-4386d787bc4d", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147202441900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d9016ae-4536-49eb-948e-ff4ebcb9b99e", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147173462700, "endTime": 156147202657500}, "additional": {"logType": "info", "children": [], "durationId": "d49522e6-c890-4a7a-89d6-5cac41ad0b3a", "parent": "347696c5-c0d5-4f1c-883d-f7050c54e160"}}, {"head": {"id": "b62429e6-0fc1-4a50-b51a-0d7b2d424b26", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147202734700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d203824-15d4-44b8-9e97-a1edc06fba7d", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147237286900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99a57e9a-98ba-4a8f-8eb2-a0b1fe69394e", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147237746000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "673e925f-9a78-4f42-be02-a87657106815", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147237973300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4fe01f9-0c65-4b4b-912b-3a5c018225dd", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147238327000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eae7e769-c449-425c-a2de-6c9bd21509fe", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147242221900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6fa44db-5c7e-4ec0-b1db-fa5bf372cd09", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147271357400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d47e9eb9-449d-483a-a596-9b0ba651248e", "name": "Sdk init in 50 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147304276600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b89a4ae-d04b-4270-bb97-b4d092c33d8e", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147304552900}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 19, "second": 37}, "markType": "other"}}, {"head": {"id": "b20b35ea-f625-4c15-9263-e69a3397736d", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147304572800}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 19, "second": 37}, "markType": "other"}}, {"head": {"id": "a2c45367-7174-49c9-9c48-3857180220b2", "name": "Project task initialization takes 42 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147349295000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29a18526-b70b-44a9-8f63-962431302c90", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147349478000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54dbcc67-f1a5-41f6-bc17-24a3c3639cbf", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147349582400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d45d242c-9dda-49dc-b477-7fb6c664654f", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147349860000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8209dc47-f99e-4706-87c3-c64f717a7bb7", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147202680800, "endTime": 156147349960800}, "additional": {"logType": "info", "children": [], "durationId": "4ef67e9f-e2e2-45ae-b125-5b01ab673908", "parent": "347696c5-c0d5-4f1c-883d-f7050c54e160"}}, {"head": {"id": "347696c5-c0d5-4f1c-883d-f7050c54e160", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147173460100, "endTime": 156147349981000}, "additional": {"logType": "info", "children": ["8d9016ae-4536-49eb-948e-ff4ebcb9b99e", "8209dc47-f99e-4706-87c3-c64f717a7bb7"], "durationId": "278d89dd-6e09-448d-bb7a-72e422e1b213", "parent": "02c1dfd7-5d59-4730-8e57-14c7351fa664"}}, {"head": {"id": "cad0036f-0b71-4242-b2b5-c2d41107c493", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147351074500, "endTime": 156147351095800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67e13046-ffda-4cf2-8716-02955e94843d", "logId": "bde3bcef-6550-4100-a5f4-4f6d37a67292"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bde3bcef-6550-4100-a5f4-4f6d37a67292", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147351074500, "endTime": 156147351095800}, "additional": {"logType": "info", "children": [], "durationId": "cad0036f-0b71-4242-b2b5-c2d41107c493", "parent": "02c1dfd7-5d59-4730-8e57-14c7351fa664"}}, {"head": {"id": "02c1dfd7-5d59-4730-8e57-14c7351fa664", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147167720200, "endTime": 156147351109500}, "additional": {"logType": "info", "children": ["8232b19d-e954-4123-abfa-cae286c612f2", "347696c5-c0d5-4f1c-883d-f7050c54e160", "bde3bcef-6550-4100-a5f4-4f6d37a67292"], "durationId": "67e13046-ffda-4cf2-8716-02955e94843d", "parent": "18ac34fe-93c7-4176-8117-1aa904e41a6a"}}, {"head": {"id": "839e39d9-0ca3-4027-b340-09656e33fb14", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147352224400, "endTime": 156147402215500}, "additional": {"children": ["23dc3f5f-c012-477f-9cf3-4a9d92adaca6", "641b1443-37c9-43da-9f0a-a60c847ea435", "1a278303-76f5-4ba3-a809-7d84150a8f80"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5d266380-0d55-4cae-8814-28ea8e8ece0d", "logId": "5d9fc94e-5e6d-4d4f-94f6-b6edca57913a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23dc3f5f-c012-477f-9cf3-4a9d92adaca6", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147357101400, "endTime": 156147357117800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "839e39d9-0ca3-4027-b340-09656e33fb14", "logId": "0a22e7ed-5be7-4c44-a2cf-451b4163581a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a22e7ed-5be7-4c44-a2cf-451b4163581a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147357101400, "endTime": 156147357117800}, "additional": {"logType": "info", "children": [], "durationId": "23dc3f5f-c012-477f-9cf3-4a9d92adaca6", "parent": "5d9fc94e-5e6d-4d4f-94f6-b6edca57913a"}}, {"head": {"id": "641b1443-37c9-43da-9f0a-a60c847ea435", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147358744400, "endTime": 156147399280900}, "additional": {"children": ["835aa953-2683-40ea-bb1a-4261b8e92eda", "336c7901-5daf-403b-ad14-9a07a8f92ade"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "839e39d9-0ca3-4027-b340-09656e33fb14", "logId": "68ed4e02-b2d9-4cf5-8238-c4d4535d4237"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "835aa953-2683-40ea-bb1a-4261b8e92eda", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147358745300, "endTime": 156147363117900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "641b1443-37c9-43da-9f0a-a60c847ea435", "logId": "55229c6c-ce7e-4964-9828-0479d5c38ffe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "336c7901-5daf-403b-ad14-9a07a8f92ade", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147363137800, "endTime": 156147399264500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "641b1443-37c9-43da-9f0a-a60c847ea435", "logId": "48a52828-d43c-42c0-a9d7-5a09daccba1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e4bd3ae-cff3-4492-a686-3f900c85bda1", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147358747800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "252573dc-12b4-426d-a898-f9ef9afdedf7", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147362939000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55229c6c-ce7e-4964-9828-0479d5c38ffe", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147358745300, "endTime": 156147363117900}, "additional": {"logType": "info", "children": [], "durationId": "835aa953-2683-40ea-bb1a-4261b8e92eda", "parent": "68ed4e02-b2d9-4cf5-8238-c4d4535d4237"}}, {"head": {"id": "0cdae49f-d4ad-4397-a53b-e44477abfa82", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147363150400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "833ddf41-a3a4-49aa-acdc-8e391830537e", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147384660800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e003101-58a1-4cea-a668-2b892582fbd6", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147384867100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7931f08a-5dde-4e75-8ec4-24e4efb9cfa5", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147385196000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf434509-45e2-4e31-b834-eaf44e6f3318", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147385414600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7ef2399-b58e-4d1a-8d46-2b85a7fbbf4b", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147385518900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3d0bb4b-af50-47c2-851b-1f1e58c40567", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147385603100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f397e3ed-93c7-4404-b958-20b61584c273", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147385706400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ae02732-309c-4d57-8db6-56b40f2b3d80", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147385807900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6882ebfd-d544-4495-9e77-2b559b029721", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147386163100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac457af4-6360-444b-a936-591e16b981e8", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147386559100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "545a86d5-2770-4638-8728-87f4a99c4e37", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147386749400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17a0872c-f93e-4a46-bc73-4c24c3e1d920", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147386878700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b28f58c5-2f67-49cf-aa0d-345c477ca15a", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147387045000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96845c46-acae-408a-969a-0d0782044e52", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147387195300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65a3be8b-9605-4926-8285-16f87d481462", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147387431100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c82237cc-7abf-4c68-892d-a3e56fcdc232", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147387641400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4663a2e1-873d-4c8d-a722-df9c3a0063e8", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147387755500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc24a229-f961-416a-9d10-9dc60153e500", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147387892400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5628b6aa-1943-4984-9f78-9ace406fd7b5", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147388320500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d44d8ba6-2bd1-42f5-a671-a2e1e5d81cee", "name": "Module entry task initialization takes 6 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147398676900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d58b8e3-38fd-4eac-a941-253125f8cceb", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147398992400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e619409b-aeab-4331-b360-29eaa1bf4bce", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147399101400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "829781ce-6f53-4d43-8f8e-d094765e64e8", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147399174800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48a52828-d43c-42c0-a9d7-5a09daccba1f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147363137800, "endTime": 156147399264500}, "additional": {"logType": "info", "children": [], "durationId": "336c7901-5daf-403b-ad14-9a07a8f92ade", "parent": "68ed4e02-b2d9-4cf5-8238-c4d4535d4237"}}, {"head": {"id": "68ed4e02-b2d9-4cf5-8238-c4d4535d4237", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147358744400, "endTime": 156147399280900}, "additional": {"logType": "info", "children": ["55229c6c-ce7e-4964-9828-0479d5c38ffe", "48a52828-d43c-42c0-a9d7-5a09daccba1f"], "durationId": "641b1443-37c9-43da-9f0a-a60c847ea435", "parent": "5d9fc94e-5e6d-4d4f-94f6-b6edca57913a"}}, {"head": {"id": "1a278303-76f5-4ba3-a809-7d84150a8f80", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147402182500, "endTime": 156147402202600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "839e39d9-0ca3-4027-b340-09656e33fb14", "logId": "63977102-ede3-40d8-a86c-571f1977badd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63977102-ede3-40d8-a86c-571f1977badd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147402182500, "endTime": 156147402202600}, "additional": {"logType": "info", "children": [], "durationId": "1a278303-76f5-4ba3-a809-7d84150a8f80", "parent": "5d9fc94e-5e6d-4d4f-94f6-b6edca57913a"}}, {"head": {"id": "5d9fc94e-5e6d-4d4f-94f6-b6edca57913a", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147352224400, "endTime": 156147402215500}, "additional": {"logType": "info", "children": ["0a22e7ed-5be7-4c44-a2cf-451b4163581a", "68ed4e02-b2d9-4cf5-8238-c4d4535d4237", "63977102-ede3-40d8-a86c-571f1977badd"], "durationId": "839e39d9-0ca3-4027-b340-09656e33fb14", "parent": "0256e9ac-7cdf-450d-944d-697c94eaa36e"}}, {"head": {"id": "0256e9ac-7cdf-450d-944d-697c94eaa36e", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147351129300, "endTime": 156147402225700}, "additional": {"logType": "info", "children": ["5d9fc94e-5e6d-4d4f-94f6-b6edca57913a"], "durationId": "5d266380-0d55-4cae-8814-28ea8e8ece0d", "parent": "18ac34fe-93c7-4176-8117-1aa904e41a6a"}}, {"head": {"id": "b7a05be3-5d90-4a7e-9389-c3d3aabeaa01", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147431653400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "037fb3df-7077-4fd9-8c9c-f84720615696", "name": "hvigorfile, resolve hvigorfile dependencies in 89 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147490742100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6abd6c7b-4d01-492e-b9be-4da2764d7193", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147402298400, "endTime": 156147490912000}, "additional": {"logType": "info", "children": [], "durationId": "5d19163f-88dd-4a41-9fb4-e94b680a41fb", "parent": "18ac34fe-93c7-4176-8117-1aa904e41a6a"}}, {"head": {"id": "4613c294-ea3e-47c9-8a41-2816fc468feb", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147492384400, "endTime": 156147493129600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "06fc69bb-795b-442b-84da-c5b1f4583689", "logId": "3748886a-9c1e-4098-933e-f370ad549112"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e45ffdab-7e08-4989-824e-a7074d879c8d", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147492576300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3748886a-9c1e-4098-933e-f370ad549112", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147492384400, "endTime": 156147493129600}, "additional": {"logType": "info", "children": [], "durationId": "4613c294-ea3e-47c9-8a41-2816fc468feb", "parent": "18ac34fe-93c7-4176-8117-1aa904e41a6a"}}, {"head": {"id": "4a46ffac-136c-4640-9fd9-331958eb8cb2", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147497600900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37df2265-cd88-416c-b7fe-214c4380c874", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147518978400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90bdda74-3d10-4ae4-923a-6136456cc120", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147493152300, "endTime": 156147521668100}, "additional": {"logType": "info", "children": [], "durationId": "300fd450-52bb-4165-8ce3-8581906726af", "parent": "18ac34fe-93c7-4176-8117-1aa904e41a6a"}}, {"head": {"id": "e4424eaa-08a1-46db-96b7-0241cec3c3b5", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147521856400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "515099e1-dceb-4a21-815f-24d934e1d7b0", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147538407300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b247652d-7a3d-4537-a2df-1241d941c890", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147538628300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebf948af-d8f6-4ee2-8b8f-2a944d505963", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147539974400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b502012e-b08d-494a-9ba5-aecfbe070cb9", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147545551500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d68566bf-7a37-4d89-b5e2-aa64b85bcb40", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147545698600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29ed1b84-ca40-4ecd-adfb-b4c208ca127e", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147521697600, "endTime": 156147553436900}, "additional": {"logType": "info", "children": [], "durationId": "57d79f14-6516-459e-8d57-dadf6dd3f6b2", "parent": "18ac34fe-93c7-4176-8117-1aa904e41a6a"}}, {"head": {"id": "b4ffb0a1-c661-4b0a-8291-b7727ee4132a", "name": "Configuration phase cost:394 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147553495400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fe99edd-b16f-447c-bfb9-456a9f6cd809", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147553462700, "endTime": 156147553701800}, "additional": {"logType": "info", "children": [], "durationId": "18cec3ef-8803-43eb-b697-5bba1bc6bb1f", "parent": "18ac34fe-93c7-4176-8117-1aa904e41a6a"}}, {"head": {"id": "18ac34fe-93c7-4176-8117-1aa904e41a6a", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147146926400, "endTime": 156147553727300}, "additional": {"logType": "info", "children": ["acc1b113-789b-41c6-a0e6-f4500e01fd44", "35431c3d-9cfc-4de7-914b-13e51f4e675e", "02c1dfd7-5d59-4730-8e57-14c7351fa664", "0256e9ac-7cdf-450d-944d-697c94eaa36e", "6abd6c7b-4d01-492e-b9be-4da2764d7193", "90bdda74-3d10-4ae4-923a-6136456cc120", "29ed1b84-ca40-4ecd-adfb-b4c208ca127e", "4fe99edd-b16f-447c-bfb9-456a9f6cd809", "3748886a-9c1e-4098-933e-f370ad549112"], "durationId": "06fc69bb-795b-442b-84da-c5b1f4583689", "parent": "1a034e7a-0577-4b45-86e9-57a2ec215be0"}}, {"head": {"id": "eb15ad2c-4871-4b1a-8170-714b3cd0e358", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147555734400, "endTime": 156147555754700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f720bd5-7ea7-47b6-943b-e9ce45f40a4d", "logId": "40349665-c854-4c08-88cb-27b7181af023"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40349665-c854-4c08-88cb-27b7181af023", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147555734400, "endTime": 156147555754700}, "additional": {"logType": "info", "children": [], "durationId": "eb15ad2c-4871-4b1a-8170-714b3cd0e358", "parent": "1a034e7a-0577-4b45-86e9-57a2ec215be0"}}, {"head": {"id": "d1f755c0-0b40-41f9-992f-19fb61633e00", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147553768200, "endTime": 156147555766400}, "additional": {"logType": "info", "children": [], "durationId": "689a7650-7715-4603-a45f-2f9315fd4977", "parent": "1a034e7a-0577-4b45-86e9-57a2ec215be0"}}, {"head": {"id": "feab2760-96fb-4305-92da-974bdcff6c6e", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147555772000, "endTime": 156147555781600}, "additional": {"logType": "info", "children": [], "durationId": "8cf5ec09-a960-41b7-9ba0-9baf3ff7880d", "parent": "1a034e7a-0577-4b45-86e9-57a2ec215be0"}}, {"head": {"id": "1a034e7a-0577-4b45-86e9-57a2ec215be0", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147117008700, "endTime": 156147555785000}, "additional": {"logType": "info", "children": ["41396d10-2e95-44aa-9b45-43f53474787c", "18ac34fe-93c7-4176-8117-1aa904e41a6a", "d1f755c0-0b40-41f9-992f-19fb61633e00", "feab2760-96fb-4305-92da-974bdcff6c6e", "d6739033-5096-4206-918b-ead313cce70c", "22158d86-aeca-494d-b411-84b5f5c71ac2", "40349665-c854-4c08-88cb-27b7181af023"], "durationId": "5f720bd5-7ea7-47b6-943b-e9ce45f40a4d"}}, {"head": {"id": "d83cbb0d-5e5d-4bc7-b25a-55ecbb91b6d0", "name": "Configuration task cost before running: 444 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147556384100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e43bebcb-8eb1-4e68-a88b-e22c87f0c638", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147576915900, "endTime": 156147602171400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "eddcbb12-a8cc-4041-86fd-265d6192305b", "logId": "b62e5fe3-1230-47fa-a23f-455c6d650cce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eddcbb12-a8cc-4041-86fd-265d6192305b", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147559415400}, "additional": {"logType": "detail", "children": [], "durationId": "e43bebcb-8eb1-4e68-a88b-e22c87f0c638"}}, {"head": {"id": "f63d4ae5-fe4a-4662-9fa8-5c9e1be3f7d1", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147560607500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54060695-0178-4920-a075-61d4a2058915", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147560872600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "048aeaac-c0b2-4604-a760-6d4b3f79ff42", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147562647500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "658d2c9f-780e-405c-bf24-3873daee0c4e", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147564316100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "167cfba5-63ba-460c-bf64-0071b2897336", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147566521200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40fe4b76-dfbb-47f1-b49b-a61512b3f90e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147566693400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c10df2e-e1a3-43ad-bb87-f6e43cce896a", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147576940000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d7d04f1-9a2c-49e9-90fc-aa35d2c356a2", "name": "Incremental task entry:default@PreBuild pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147601803800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e77a8aed-514c-41d4-aa87-1701b34bbf08", "name": "entry : default@PreBuild cost memory 0.3159332275390625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147602028000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b62e5fe3-1230-47fa-a23f-455c6d650cce", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147576915900, "endTime": 156147602171400}, "additional": {"logType": "info", "children": [], "durationId": "e43bebcb-8eb1-4e68-a88b-e22c87f0c638"}}, {"head": {"id": "189dbd8f-780f-4883-a855-0283fc2a0ddd", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147614892300, "endTime": 156147618413600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d75c7d6f-5117-4417-b408-0074f48e9a0f", "logId": "437d028f-d6d3-484f-91d1-eba89ccb8f6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d75c7d6f-5117-4417-b408-0074f48e9a0f", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147611003000}, "additional": {"logType": "detail", "children": [], "durationId": "189dbd8f-780f-4883-a855-0283fc2a0ddd"}}, {"head": {"id": "7ef98680-77fd-47e2-afa7-ca4f7062ad1e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147613393800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c6700e4-d112-4e55-bd03-1343c23b6d8a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147613595600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e892eb4c-632d-46b3-8d35-ebf96f239cd5", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147614910100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f91d36d1-50d2-422b-b780-b6d69d22ebcb", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147616295200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a69a6cc-ff5c-4ac2-a715-640e2a27837f", "name": "entry : default@CreateModuleInfo cost memory 0.06034088134765625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147618072400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58ba1760-7980-44b6-8ace-dbcac87939af", "name": "runTaskFromQueue task cost before running: 506 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147618278600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "437d028f-d6d3-484f-91d1-eba89ccb8f6e", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147614892300, "endTime": 156147618413600, "totalTime": 3348000}, "additional": {"logType": "info", "children": [], "durationId": "189dbd8f-780f-4883-a855-0283fc2a0ddd"}}, {"head": {"id": "4f145ddf-a451-4f71-aaf8-06a13fc27f83", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147634055200, "endTime": 156147638854600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "71ab79fd-ba4d-4978-8473-82e411472526", "logId": "3a4fb424-9115-4a77-a2fb-252fc5ec37e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71ab79fd-ba4d-4978-8473-82e411472526", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147622773100}, "additional": {"logType": "detail", "children": [], "durationId": "4f145ddf-a451-4f71-aaf8-06a13fc27f83"}}, {"head": {"id": "093127eb-244c-443c-bf04-c8f769c6be94", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147624936500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "298aac64-a3b1-4c56-b2f2-984a39f9685d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147625118200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b3a3030-811d-45e3-90bb-b23a016aa1e1", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147634079200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c641a82f-e055-4299-bf17-7fbfb913d08a", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147636374300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da552c70-05ea-491a-b035-31f217f38996", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147638539400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb42a40a-08ee-4a6d-a574-b2bb347cef28", "name": "entry : default@GenerateMetadata cost memory 0.10208892822265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147638720700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a4fb424-9115-4a77-a2fb-252fc5ec37e9", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147634055200, "endTime": 156147638854600}, "additional": {"logType": "info", "children": [], "durationId": "4f145ddf-a451-4f71-aaf8-06a13fc27f83"}}, {"head": {"id": "441add5b-7d21-44d4-9c53-cb697d83c2e4", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147644641500, "endTime": 156147645343900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "06b88b45-3018-4540-b476-463053a7737a", "logId": "9214c920-5701-4017-97c5-a590372bb985"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06b88b45-3018-4540-b476-463053a7737a", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147641980500}, "additional": {"logType": "detail", "children": [], "durationId": "441add5b-7d21-44d4-9c53-cb697d83c2e4"}}, {"head": {"id": "84f77f2d-3756-469d-8677-02e37a410fd8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147644122500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5256e9e4-4e49-47d8-a158-67d47f675d62", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147644273400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "735d041d-362f-469f-93f5-f175f7b4ebfd", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147644656500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70078b0a-8656-4404-ac99-700dffc946fb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147644838700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55998049-90b2-4dfc-b42b-df540b957b7a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147644947100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f57ad27f-c89d-452c-a43f-12e6af7eb20f", "name": "entry : default@ConfigureCmake cost memory 0.037200927734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147645090800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c704bd2-ac0e-45bf-a4b7-864fa0cf701e", "name": "runTaskFromQueue task cost before running: 532 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147645234000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9214c920-5701-4017-97c5-a590372bb985", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147644641500, "endTime": 156147645343900, "totalTime": 564200}, "additional": {"logType": "info", "children": [], "durationId": "441add5b-7d21-44d4-9c53-cb697d83c2e4"}}, {"head": {"id": "565f873c-6bbf-4b4d-badc-365c4b1a792a", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147652847400, "endTime": 156147658143600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1c838912-d012-4455-b9d2-8a983dc1c339", "logId": "718dbaf6-0a3c-4796-897e-79140a60c6fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c838912-d012-4455-b9d2-8a983dc1c339", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147648978300}, "additional": {"logType": "detail", "children": [], "durationId": "565f873c-6bbf-4b4d-badc-365c4b1a792a"}}, {"head": {"id": "67d7875b-875a-4863-abc0-bb44f373cbbf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147651331000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87f67243-cd79-456e-a01f-0dae7ed33962", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147651510600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95a1629a-85a1-4958-94f9-e19604558ebb", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147652864000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8192aec-df49-49a5-a8af-e7e8041e6459", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147657795700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7eaa111-f6e6-4318-aaab-0ea1e5a34970", "name": "entry : default@MergeProfile cost memory 0.118133544921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147658004500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "718dbaf6-0a3c-4796-897e-79140a60c6fc", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147652847400, "endTime": 156147658143600}, "additional": {"logType": "info", "children": [], "durationId": "565f873c-6bbf-4b4d-badc-365c4b1a792a"}}, {"head": {"id": "4518b1d8-b37c-48b5-ac97-ba9239b391f7", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147665094000, "endTime": 156147671033500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "47647efa-b418-49b2-8a97-428e37d17384", "logId": "bec23bc3-ece1-483b-a2b2-91458ea87521"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47647efa-b418-49b2-8a97-428e37d17384", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147661354900}, "additional": {"logType": "detail", "children": [], "durationId": "4518b1d8-b37c-48b5-ac97-ba9239b391f7"}}, {"head": {"id": "0878a559-1378-406b-9f8b-b15df2a9cd31", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147663345900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbb104fb-baa7-46df-88cf-070b58a05793", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147663549400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df271bb3-b9cb-4c8a-a215-f0fc62226b04", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147665109900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b756794c-30cb-4ac1-a448-88969bd3ae06", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147667227700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be1f2d04-ca6f-4e14-8a95-4e843b2e6cba", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147670673800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2831244-4fed-4142-a410-5922a07326e0", "name": "entry : default@CreateBuildProfile cost memory 0.107452392578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147670904900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bec23bc3-ece1-483b-a2b2-91458ea87521", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147665094000, "endTime": 156147671033500}, "additional": {"logType": "info", "children": [], "durationId": "4518b1d8-b37c-48b5-ac97-ba9239b391f7"}}, {"head": {"id": "8e060704-580f-4315-99da-4c53ebc5e182", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147678112400, "endTime": 156147681493700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e903e0cb-be20-4c8c-92e6-acf5fe1ae19f", "logId": "fb4b2978-37da-4b12-a2ed-ec2b85a32e44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e903e0cb-be20-4c8c-92e6-acf5fe1ae19f", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147674215400}, "additional": {"logType": "detail", "children": [], "durationId": "8e060704-580f-4315-99da-4c53ebc5e182"}}, {"head": {"id": "c2d434c9-bab6-42c2-863f-522e471d79bc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147676404800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ded618a-023c-4ef7-b269-b3dab1855159", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147676584600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9796ce4-968e-444e-8e22-34a28654d326", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147678128400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e35abd39-8361-464b-a858-807349b4118f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147680508800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69d4233b-4413-4830-a23a-5c958aafceb3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147680638400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e951997-9c90-47b6-8b83-7fac3a2b7780", "name": "entry : default@PreCheckSyscap cost memory -4.70928955078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147681194400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b58109b7-500b-426f-b1d8-3019852ae4a2", "name": "runTaskFromQueue task cost before running: 569 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147681386700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb4b2978-37da-4b12-a2ed-ec2b85a32e44", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147678112400, "endTime": 156147681493700, "totalTime": 3240400}, "additional": {"logType": "info", "children": [], "durationId": "8e060704-580f-4315-99da-4c53ebc5e182"}}, {"head": {"id": "0d52ce47-73f9-49e0-933f-dac4b1a5c279", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147689706800, "endTime": 156147701486100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "851d52c3-3af1-42d7-a6ec-51d9ba97f2c6", "logId": "362fa085-74f3-49f6-80a6-131ec8d3e696"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "851d52c3-3af1-42d7-a6ec-51d9ba97f2c6", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147684752800}, "additional": {"logType": "detail", "children": [], "durationId": "0d52ce47-73f9-49e0-933f-dac4b1a5c279"}}, {"head": {"id": "5257e906-3cdb-4a58-bf17-7f6e7e5db92a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147686886500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0fb9435-eec4-4db9-bde8-bd1128a07f7e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147687065300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25c48da1-6d8c-4e24-b031-2d0821d27799", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147689721800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "023de4cf-8e47-407e-93e1-a040412e3399", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147699752000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c14b9dce-e6d1-429b-9398-957f49f02e6c", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147701166300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "911e7e40-a8a3-4122-bb9c-026b4c13006c", "name": "entry : default@GeneratePkgContextInfo cost memory 0.2537994384765625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147701358200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "362fa085-74f3-49f6-80a6-131ec8d3e696", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147689706800, "endTime": 156147701486100}, "additional": {"logType": "info", "children": [], "durationId": "0d52ce47-73f9-49e0-933f-dac4b1a5c279"}}, {"head": {"id": "daa8edd1-c066-4825-8ef5-3cbb526611b8", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147718159400, "endTime": 156147722575700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "85a1ad7d-ff2d-412f-b0ac-4a601ecdaea8", "logId": "eb51e118-df95-4926-8123-07dba46231fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85a1ad7d-ff2d-412f-b0ac-4a601ecdaea8", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147704757400}, "additional": {"logType": "detail", "children": [], "durationId": "daa8edd1-c066-4825-8ef5-3cbb526611b8"}}, {"head": {"id": "012d1a10-c65f-4254-8095-2dd445cfe292", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147707014800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f305fee4-1ff1-4837-9662-53f5e57240f1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147707194600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a22bda2f-5517-44f7-8207-d137fc63eb6c", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147718185900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9fecabb-57a8-45ee-bdb3-5a7e486711dd", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147721631500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1741bc59-453b-4dfd-b7cf-46518bdc4dc0", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147721870500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2961c5e0-28ab-4c10-a7f5-e8432ee351f3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147722039500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3210a156-7951-4b89-96e2-8b2b87c1857f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147722175600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14523f37-6566-4192-af96-a842c8cd3737", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1200103759765625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147722331100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "742992c5-c76b-4775-ae98-6c22773676a7", "name": "runTaskFromQueue task cost before running: 610 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147722471200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb51e118-df95-4926-8123-07dba46231fe", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147718159400, "endTime": 156147722575700, "totalTime": 4288700}, "additional": {"logType": "info", "children": [], "durationId": "daa8edd1-c066-4825-8ef5-3cbb526611b8"}}, {"head": {"id": "c9cb539a-f296-44be-8ef1-97ced4d10274", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147731039800, "endTime": 156147731705300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4ff312ee-89e8-496c-a1a2-b6ceb079aff6", "logId": "7b89c0fd-db85-4024-abdd-128137fd869f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ff312ee-89e8-496c-a1a2-b6ceb079aff6", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147726923900}, "additional": {"logType": "detail", "children": [], "durationId": "c9cb539a-f296-44be-8ef1-97ced4d10274"}}, {"head": {"id": "51b694c8-8ae0-4208-8fee-1c556cc4a9a9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147729174400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4990a24-4625-4899-9b26-5fe57f0bf5b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147729372200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1da762dd-a991-4e68-af5b-61f9cad2960a", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147731056500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77bc7697-c66e-45c4-b774-97a979e174c5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147731264900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "680ab724-8511-445d-824a-77c6d6e2a3bc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147731368300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0480834e-7292-40ad-804e-35104dbaac94", "name": "entry : default@BuildNativeWithCmake cost memory 0.03824615478515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147731483000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbd0c837-c498-4e14-aa3f-4b9880c4ab27", "name": "runTaskFromQueue task cost before running: 619 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147731611500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b89c0fd-db85-4024-abdd-128137fd869f", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147731039800, "endTime": 156147731705300, "totalTime": 545000}, "additional": {"logType": "info", "children": [], "durationId": "c9cb539a-f296-44be-8ef1-97ced4d10274"}}, {"head": {"id": "8d82afb9-9118-43db-a21e-531dc19afe5f", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147738520900, "endTime": 156147746455600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "276018f0-5e26-4d12-b4c4-76c886e9f014", "logId": "63bae33c-1bbc-4624-9793-1175af2c53f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "276018f0-5e26-4d12-b4c4-76c886e9f014", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147734759000}, "additional": {"logType": "detail", "children": [], "durationId": "8d82afb9-9118-43db-a21e-531dc19afe5f"}}, {"head": {"id": "63d74149-0b13-4913-b7c0-975563d114de", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147736981000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed3b4e66-53d8-45b7-a9f5-416b230a46ff", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147737138200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3c96f23-1c11-4bf9-ac4a-b3e38a92635c", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147738534000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6023ac5d-0943-417e-bf91-c1ad383fcfb4", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147746074100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d818a553-7c07-4922-b54b-6db946724cfc", "name": "entry : default@MakePackInfo cost memory 0.1631927490234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147746353300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63bae33c-1bbc-4624-9793-1175af2c53f2", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147738520900, "endTime": 156147746455600}, "additional": {"logType": "info", "children": [], "durationId": "8d82afb9-9118-43db-a21e-531dc19afe5f"}}, {"head": {"id": "ce6cf38d-2cea-41e4-939b-b3f8fa38bc0c", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147752944400, "endTime": 156147760096400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4652ed48-726e-44c2-b8f0-8b34f2b82e6b", "logId": "251fbb99-a85d-4443-be22-f7c82d7ff143"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4652ed48-726e-44c2-b8f0-8b34f2b82e6b", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147750271600}, "additional": {"logType": "detail", "children": [], "durationId": "ce6cf38d-2cea-41e4-939b-b3f8fa38bc0c"}}, {"head": {"id": "95b0c733-5857-4a78-9418-b9e01f92beab", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147751247000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "382b3d93-9f0f-4483-b839-9d089e854608", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147751342100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd710f7f-164c-4259-a0de-72fb125adfef", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147752982400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c16da394-d4cd-44f4-9067-07b7876e6102", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147753334000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67942323-3754-46a8-a816-d2bccb975aa8", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147755242600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9728e65b-357f-4841-8cf9-03dfcd2d1a94", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147759813300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5498303e-e2fd-4824-85cc-a0c98b8e6aca", "name": "entry : default@SyscapTransform cost memory 0.15024566650390625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147759977300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "251fbb99-a85d-4443-be22-f7c82d7ff143", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147752944400, "endTime": 156147760096400}, "additional": {"logType": "info", "children": [], "durationId": "ce6cf38d-2cea-41e4-939b-b3f8fa38bc0c"}}, {"head": {"id": "280a15ee-abae-434a-ae23-09870a40cea5", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147767193000, "endTime": 156147770596900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "123feb9f-9fb8-4db4-a2ee-8c70f673af7a", "logId": "6cf63f05-5194-44d4-b3d7-bc048cf25e1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "123feb9f-9fb8-4db4-a2ee-8c70f673af7a", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147762750800}, "additional": {"logType": "detail", "children": [], "durationId": "280a15ee-abae-434a-ae23-09870a40cea5"}}, {"head": {"id": "e7647981-43a1-4b1d-a0f0-ca643beb4897", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147764678200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fa7167d-2f4f-41a0-b490-e94fc95e9cd8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147764829900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15a6238b-b648-4bbd-a875-0b25ab11804c", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147767207000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea4d8817-b69e-4285-9557-e959705e1102", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147770358100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "886fbf76-6edb-4863-ae0d-9a7ded365a48", "name": "entry : default@ProcessProfile cost memory 0.12445831298828125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147770499300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cf63f05-5194-44d4-b3d7-bc048cf25e1a", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147767193000, "endTime": 156147770596900}, "additional": {"logType": "info", "children": [], "durationId": "280a15ee-abae-434a-ae23-09870a40cea5"}}, {"head": {"id": "8f612ac1-c995-4339-b638-1cfe4e49f6ac", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147777441200, "endTime": 156147788323700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a608963d-29fe-430a-afaf-ec277f114220", "logId": "f8049455-9562-43db-8ebe-0ab479829122"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a608963d-29fe-430a-afaf-ec277f114220", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147773035400}, "additional": {"logType": "detail", "children": [], "durationId": "8f612ac1-c995-4339-b638-1cfe4e49f6ac"}}, {"head": {"id": "a8af8e97-623f-4d7e-9577-394b6f964812", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147774708900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79732550-f1ac-48f3-ba74-5274e7ac2002", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147774861800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e3015aa-9836-4893-b035-d6db7f8eb829", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147777454200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "718797c7-5d60-4c87-94c1-a7748e7568aa", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147788024500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8579ac2a-a977-49a4-98c2-b3dacbbbc7f7", "name": "entry : default@ProcessRouterMap cost memory 0.23232269287109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147788215800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8049455-9562-43db-8ebe-0ab479829122", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147777441200, "endTime": 156147788323700}, "additional": {"logType": "info", "children": [], "durationId": "8f612ac1-c995-4339-b638-1cfe4e49f6ac"}}, {"head": {"id": "2dd146d7-21b8-4f0a-8bca-cac9e772d12d", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147794887500, "endTime": 156147803728000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "c9a25f9b-0820-4fd3-8568-0355ea157981", "logId": "8fecf7c8-0295-4268-b52b-a1442dbdfe70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9a25f9b-0820-4fd3-8568-0355ea157981", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147792860300}, "additional": {"logType": "detail", "children": [], "durationId": "2dd146d7-21b8-4f0a-8bca-cac9e772d12d"}}, {"head": {"id": "e89cb2a8-b556-4e9f-8bee-edad180b5062", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147794588600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37a8d219-d061-4e2a-8aab-22ad4b4c6182", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147794739600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfd66a04-1a5c-4ee1-a159-2445b3c32d33", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147794899000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f34eeeb1-fb9e-4533-b87d-9682d897e027", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147795114700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a0e94f4-9705-455d-a918-5fed258c1f45", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147801504600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d66824b6-8bc4-4be6-972b-2ad4d4c34d86", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147801707000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ecf2a13-f8dd-4793-9e2b-0722042487db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147801820900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d53bd3f-e8d8-4a84-89ee-441fdc5f9816", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147801887300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ffaa607-9489-44f7-9c0b-dc0bcb24edca", "name": "entry : default@ProcessStartupConfig cost memory 0.2569732666015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147803501200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faffeba7-3a0d-48eb-a4a2-9c20c08907c6", "name": "runTaskFromQueue task cost before running: 691 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147803648200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fecf7c8-0295-4268-b52b-a1442dbdfe70", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147794887500, "endTime": 156147803728000, "totalTime": 8735600}, "additional": {"logType": "info", "children": [], "durationId": "2dd146d7-21b8-4f0a-8bca-cac9e772d12d"}}, {"head": {"id": "a9716450-8163-4250-a995-ffdb65ad18d9", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147810156900, "endTime": 156147811493000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "b38f3f04-d83c-40b3-a9e7-c48085854e55", "logId": "3ddd83a5-d7d0-41da-9b14-088d2631a592"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b38f3f04-d83c-40b3-a9e7-c48085854e55", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147807489800}, "additional": {"logType": "detail", "children": [], "durationId": "a9716450-8163-4250-a995-ffdb65ad18d9"}}, {"head": {"id": "d5776703-2e68-4085-8e10-105a011e5314", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147808918500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb6555aa-8047-4314-a8ac-24fb18298ff5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147809061600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4628d36b-9206-4c83-84fe-31c262c476b0", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147810164000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1075cdce-23ae-4eef-9e27-159d3719008d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147810304000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2387766-643b-4842-81d0-66ad8d61c39c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147810359100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ada9c475-dcc8-4cbd-b693-c22bd4223463", "name": "entry : default@BuildNativeWithNinja cost memory 0.05850982666015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147811277900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b20ea73b-f391-4f05-820d-5d9abe8198cf", "name": "runTaskFromQueue task cost before running: 699 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147811414400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ddd83a5-d7d0-41da-9b14-088d2631a592", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147810156900, "endTime": 156147811493000, "totalTime": 1230000}, "additional": {"logType": "info", "children": [], "durationId": "a9716450-8163-4250-a995-ffdb65ad18d9"}}, {"head": {"id": "b6497c6d-9576-44d2-acbb-d8261631e2a7", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147820737900, "endTime": 156147828306300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "15d7e702-0e67-4181-ad8c-50607379778c", "logId": "269d2b9e-ef1a-48ba-9d9f-7ea3dad0e2b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15d7e702-0e67-4181-ad8c-50607379778c", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147815383200}, "additional": {"logType": "detail", "children": [], "durationId": "b6497c6d-9576-44d2-acbb-d8261631e2a7"}}, {"head": {"id": "5881bc65-14c8-4d23-a474-25e0a5334ce1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147817037500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6de8a2c9-0448-4b79-89ff-6b2d4e661db2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147817192300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a450ebe0-99dd-4167-859f-4afc5b5313a7", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147818838200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2a5f000-f8e8-4118-8ca9-d0ee64b64618", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147822102700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7afbca3d-ffc7-49f1-b3fc-3c1624947be9", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147825933300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7d6788b-dfae-4bca-baee-2e0632a8a54e", "name": "entry : default@ProcessResource cost memory 0.16132354736328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147826112100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "269d2b9e-ef1a-48ba-9d9f-7ea3dad0e2b5", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147820737900, "endTime": 156147828306300}, "additional": {"logType": "info", "children": [], "durationId": "b6497c6d-9576-44d2-acbb-d8261631e2a7"}}, {"head": {"id": "a85f27b4-b64c-4fdb-91a7-6b5df7339461", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147845107900, "endTime": 156147870655400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5429e76f-d2cc-42c8-87e9-96aca42ac5e3", "logId": "03d2f177-b9e4-45a6-8158-fa29316456f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5429e76f-d2cc-42c8-87e9-96aca42ac5e3", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147835646700}, "additional": {"logType": "detail", "children": [], "durationId": "a85f27b4-b64c-4fdb-91a7-6b5df7339461"}}, {"head": {"id": "bd168f30-4970-4c1b-9b5a-ff67bd3fa273", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147837777400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d42dca-f4e1-4e53-84e5-f132a4782774", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147837959000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2df6dc3e-c2f2-4a30-b9e8-fe6679f91be9", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147845118900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "697e0b4d-c044-4241-98ad-977d3b56253c", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147870383100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27513f21-9906-4ea4-a665-1272b5a85234", "name": "entry : default@GenerateLoaderJson cost memory 0.8750457763671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147870560100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03d2f177-b9e4-45a6-8158-fa29316456f4", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147845107900, "endTime": 156147870655400}, "additional": {"logType": "info", "children": [], "durationId": "a85f27b4-b64c-4fdb-91a7-6b5df7339461"}}, {"head": {"id": "902faf93-60fb-4b64-a4a2-519d59850d10", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147890475600, "endTime": 156147897702400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "8550573c-8bef-4b34-ae7d-27b6675b7baa", "logId": "fcbb518d-76f2-412d-8829-b2683bfd749c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8550573c-8bef-4b34-ae7d-27b6675b7baa", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147887862600}, "additional": {"logType": "detail", "children": [], "durationId": "902faf93-60fb-4b64-a4a2-519d59850d10"}}, {"head": {"id": "a41d4377-746f-4251-b640-e204e824879e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147889182300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "092790dc-63ec-44d1-8205-a0162a4697ab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147889344300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6da2ac7-b748-4f9d-bcdf-b8bea7e8f3ce", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147890488000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71b8f1c9-20d7-4663-b32b-cc8c02848631", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147897307500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c559904b-9db9-436f-9956-f7e6ee029e59", "name": "entry : default@ProcessLibs cost memory 0.14154052734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147897488200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcbb518d-76f2-412d-8829-b2683bfd749c", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147890475600, "endTime": 156147897702400}, "additional": {"logType": "info", "children": [], "durationId": "902faf93-60fb-4b64-a4a2-519d59850d10"}}, {"head": {"id": "c3674c60-199b-409b-b0a7-77bc742c7540", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147917925600, "endTime": 156147961490800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "f929d5ae-36a1-4e13-9b29-fab2469d941f", "logId": "6fbfa808-8d66-434e-8ea6-dcaec2518381"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f929d5ae-36a1-4e13-9b29-fab2469d941f", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147911485300}, "additional": {"logType": "detail", "children": [], "durationId": "c3674c60-199b-409b-b0a7-77bc742c7540"}}, {"head": {"id": "26eeccd5-03f7-4d7b-affc-2c0b973ba33c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147913911600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd6202bc-92b7-40d8-9b31-34beff6e4dfd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147914091700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34654dcd-219d-4aca-b9f3-71b0341a5d60", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147915326700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09023339-edc8-44be-baa7-f23b3f08d724", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147917964600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea8ff05a-6c26-4dc7-af85-18ae8b4220f3", "name": "Incremental task entry:default@CompileResource pre-execution cost: 42 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147961100200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c61b320-ef7c-440f-bc09-d2bc364f8d0d", "name": "entry : default@CompileResource cost memory 1.3207168579101562", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147961322900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fbfa808-8d66-434e-8ea6-dcaec2518381", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147917925600, "endTime": 156147961490800}, "additional": {"logType": "info", "children": [], "durationId": "c3674c60-199b-409b-b0a7-77bc742c7540"}}, {"head": {"id": "0aec8243-b35c-4c93-a988-4de71933a2d7", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147973894500, "endTime": 156147977291600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "800cc3ca-6c94-48c9-b750-46b3620845c5", "logId": "1ef412cb-2319-4d5f-83a1-b5ec54b3a246"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "800cc3ca-6c94-48c9-b750-46b3620845c5", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147966738700}, "additional": {"logType": "detail", "children": [], "durationId": "0aec8243-b35c-4c93-a988-4de71933a2d7"}}, {"head": {"id": "d17510e7-5cf4-4e38-a42b-f117f3fc73c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147968767000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f1e6302-5741-4771-8d95-e30220d7f1b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147968915700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81cb6323-0ab7-4fed-a8f7-0d6aec335b73", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147973907000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6d83344-a28d-46c4-87fd-ba4a429fedab", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147974758500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b431dfcd-a1de-4e7c-b302-d6fc3997d868", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147977151600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07ee5748-b101-4e0b-827d-bafe6fe89ed3", "name": "entry : default@DoNativeStrip cost memory 0.08438873291015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147977246100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ef412cb-2319-4d5f-83a1-b5ec54b3a246", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147973894500, "endTime": 156147977291600}, "additional": {"logType": "info", "children": [], "durationId": "0aec8243-b35c-4c93-a988-4de71933a2d7"}}, {"head": {"id": "a8fb1544-919b-4b26-aa0c-19f2a03f33de", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147985790200, "endTime": 156148032402000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "213841b4-6e4e-41bc-9fbc-068d4e80dcd2", "logId": "6e3ed694-1ca6-4829-97f7-1d2044a5d6f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "213841b4-6e4e-41bc-9fbc-068d4e80dcd2", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147978689000}, "additional": {"logType": "detail", "children": [], "durationId": "a8fb1544-919b-4b26-aa0c-19f2a03f33de"}}, {"head": {"id": "a4b8c29b-35f5-4c47-b5a9-71a1288de4cc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147979739500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b280615-77e2-4afd-b435-8f644f7abea9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147979820200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24ca10ec-3319-4c16-8f50-873c44809a45", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147985799700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb971b93-56b7-465a-921e-6a27674bec58", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147985966000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2175ed28-a016-4ef9-ab7d-ac13ace83831", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 38 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148032068200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "951949dc-873a-4217-ae70-44ec06f2b526", "name": "entry : default@CompileArkTS cost memory 1.2643890380859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148032270100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e3ed694-1ca6-4829-97f7-1d2044a5d6f1", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147985790200, "endTime": 156148032402000}, "additional": {"logType": "info", "children": [], "durationId": "a8fb1544-919b-4b26-aa0c-19f2a03f33de"}}, {"head": {"id": "7df4d910-7b86-4da5-a0f2-0578b3c43d1c", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148054998200, "endTime": 156148066511400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "9f837828-0eb1-4126-9e2b-1cff5d0ea688", "logId": "31c6f86a-eb01-4925-b055-1994fd6974a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f837828-0eb1-4126-9e2b-1cff5d0ea688", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148045440800}, "additional": {"logType": "detail", "children": [], "durationId": "7df4d910-7b86-4da5-a0f2-0578b3c43d1c"}}, {"head": {"id": "4f4ca600-ec3f-4a55-8e58-e821343d424d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148047450100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1f9eaab-aabf-4cb0-a761-b1bf518f02ce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148047619800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d4a3af-4bb7-4736-b346-a14beefc73f6", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148055013400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b778b61-2252-4009-b4bd-cc58111a6596", "name": "entry : default@BuildJS cost memory 0.34767913818359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148066188300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0f7ab26-e6eb-41a5-90a1-962d7773a24a", "name": "runTaskFromQueue task cost before running: 954 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148066409300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31c6f86a-eb01-4925-b055-1994fd6974a3", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148054998200, "endTime": 156148066511400, "totalTime": 11376400}, "additional": {"logType": "info", "children": [], "durationId": "7df4d910-7b86-4da5-a0f2-0578b3c43d1c"}}, {"head": {"id": "f1376540-230a-429d-b0e7-6c90f3f17d7b", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148075533100, "endTime": 156148078257800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "346c1bcc-d51e-4339-8b77-609f2bc97c0f", "logId": "034b72bb-8dd9-49ae-919d-f0355cfc184f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "346c1bcc-d51e-4339-8b77-609f2bc97c0f", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148069254700}, "additional": {"logType": "detail", "children": [], "durationId": "f1376540-230a-429d-b0e7-6c90f3f17d7b"}}, {"head": {"id": "52b72a77-1fc6-419c-bd96-048fbff429cd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148071132600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6bd58ff-38b5-4b36-84eb-ba4a17039227", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148071263000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c92f5344-ecb0-47ea-8283-22df81e1745f", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148075543500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0219b9c-2afb-47be-9dc1-10fce42e1aca", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148076383700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2369167-d826-4f6b-8371-eb14183ea203", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148078105800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00aa7088-b2cf-43a8-bf80-706351526167", "name": "entry : default@CacheNativeLibs cost memory 0.09587860107421875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148078209100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "034b72bb-8dd9-49ae-919d-f0355cfc184f", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148075533100, "endTime": 156148078257800}, "additional": {"logType": "info", "children": [], "durationId": "f1376540-230a-429d-b0e7-6c90f3f17d7b"}}, {"head": {"id": "89fe670d-55e6-471d-80ae-7969d8d1358e", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148084662700, "endTime": 156148087058100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "08a8ecc0-b38b-4056-86ec-b998e9bb242d", "logId": "4966d40f-2a3b-4383-b2d5-69757a4ddb9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08a8ecc0-b38b-4056-86ec-b998e9bb242d", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148080653500}, "additional": {"logType": "detail", "children": [], "durationId": "89fe670d-55e6-471d-80ae-7969d8d1358e"}}, {"head": {"id": "61eea3ff-752f-4aec-a6a1-9bae6f5abbf0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148082659300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c798663-f06a-47c4-906b-e762de270f86", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148082821800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a49cade-b8a4-41a2-8c70-dcd18967a9a6", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148084675600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c7c6a0e-d8e1-40e9-98fc-084cb41f4043", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148085197300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b482bb4b-d631-4af3-9fa9-862c3381ff5c", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148086821900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6748e08-308a-4231-8e00-542824a6b3c5", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07540130615234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148086958500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4966d40f-2a3b-4383-b2d5-69757a4ddb9e", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148084662700, "endTime": 156148087058100}, "additional": {"logType": "info", "children": [], "durationId": "89fe670d-55e6-471d-80ae-7969d8d1358e"}}, {"head": {"id": "85fe847d-2876-459e-bc2a-857cd338ca82", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148106109800, "endTime": 156148140884300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "4a371783-1ddf-4cfc-b614-84c64a199def", "logId": "bd4d18c9-a4c3-4975-8eb3-1dd58fc1440f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a371783-1ddf-4cfc-b614-84c64a199def", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148090510400}, "additional": {"logType": "detail", "children": [], "durationId": "85fe847d-2876-459e-bc2a-857cd338ca82"}}, {"head": {"id": "12e68544-83af-4d6f-b22d-45553ad980d8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148092250700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bb6e613-9cf6-4443-8b01-0310745c771a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148092384100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "701ffad2-a70d-4aa5-b186-5b3a169dd169", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148106127200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c288e19-f723-4735-b5b8-db8e87e6107d", "name": "Incremental task entry:default@PackageHap pre-execution cost: 31 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148140598600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46a15dad-f906-42ee-95e0-52f26aee03a0", "name": "entry : default@PackageHap cost memory 0.942840576171875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148140783700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd4d18c9-a4c3-4975-8eb3-1dd58fc1440f", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148106109800, "endTime": 156148140884300}, "additional": {"logType": "info", "children": [], "durationId": "85fe847d-2876-459e-bc2a-857cd338ca82"}}, {"head": {"id": "0fee5f28-f1cc-4ddb-8827-24c48f1e315f", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148151249700, "endTime": 156148155241100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": [], "detailId": "9230ae6d-9f1d-4fa7-b90b-afa8c1f8ddb4", "logId": "308684fb-0da4-433a-8040-c96936dfbefe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9230ae6d-9f1d-4fa7-b90b-afa8c1f8ddb4", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148146464000}, "additional": {"logType": "detail", "children": [], "durationId": "0fee5f28-f1cc-4ddb-8827-24c48f1e315f"}}, {"head": {"id": "b62fdb4e-b629-499e-9f36-a38c450db88b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148147897200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a5f6bb1-9c59-47ea-bd90-a7fc45a844d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148148018200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63cb1a0e-16a2-456a-b7e4-6da1bc6bb45e", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148151260700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62822a25-3c78-4332-b79a-9bccc584c6c3", "name": "Incremental task entry:default@SignHap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148155033600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc25e038-b447-4124-ade4-7c291d947214", "name": "entry : default@SignHap cost memory 0.105804443359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148155156000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "308684fb-0da4-433a-8040-c96936dfbefe", "name": "UP-TO-DATE :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148151249700, "endTime": 156148155241100}, "additional": {"logType": "info", "children": [], "durationId": "0fee5f28-f1cc-4ddb-8827-24c48f1e315f"}}, {"head": {"id": "00a4c09b-79d7-4808-a2bb-804e57d83a94", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148158758400, "endTime": 156148169774300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a86c7e22-db36-45c5-8594-ebae36d9ca39", "logId": "f218d02a-4766-4833-980e-f774e1e77985"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a86c7e22-db36-45c5-8594-ebae36d9ca39", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148157448400}, "additional": {"logType": "detail", "children": [], "durationId": "00a4c09b-79d7-4808-a2bb-804e57d83a94"}}, {"head": {"id": "60148d24-d267-435f-9452-8da7196522e3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148158143700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14891fc1-d541-4c12-a8fe-b73f9245072c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148158217300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9cf945c-bf0e-405f-812a-c808d08e4ab9", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148158764300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2f6578f-cc7f-4a13-bcc8-b811d23d58b1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148169489100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04fd0070-21f7-4166-9003-10fd94361fd1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148169609200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaa9033e-a862-4793-a0bd-9f114c15e7de", "name": "entry : default@CollectDebugSymbol cost memory -7.27752685546875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148169679800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e541109-c5c3-4653-b31f-4e8008ba8201", "name": "runTaskFromQueue task cost before running: 1 s 57 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148169742900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f218d02a-4766-4833-980e-f774e1e77985", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148158758400, "endTime": 156148169774300, "totalTime": 10965100}, "additional": {"logType": "info", "children": [], "durationId": "00a4c09b-79d7-4808-a2bb-804e57d83a94"}}, {"head": {"id": "5933f019-74b7-4580-beab-315f8ae7d91a", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148171087900, "endTime": 156148171283800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d9ae4095-a39d-478d-935f-3032bd52b8d9", "logId": "02184007-52dd-4b03-950e-a4244f6f8abd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9ae4095-a39d-478d-935f-3032bd52b8d9", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148171053100}, "additional": {"logType": "detail", "children": [], "durationId": "5933f019-74b7-4580-beab-315f8ae7d91a"}}, {"head": {"id": "44484cfe-2b54-4bb6-b00c-22c2ed18db2c", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148171093900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e30561ac-3f72-41e6-9cfc-4189cec7e90f", "name": "entry : assembleHap cost memory 0.01165771484375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148171185600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf12f40a-ddbb-4fcb-82a8-b571dd45d6f7", "name": "runTaskFromQueue task cost before running: 1 s 59 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148171250600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02184007-52dd-4b03-950e-a4244f6f8abd", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148171087900, "endTime": 156148171283800, "totalTime": 149500}, "additional": {"logType": "info", "children": [], "durationId": "5933f019-74b7-4580-beab-315f8ae7d91a"}}, {"head": {"id": "4736f8a1-c594-45d2-9092-96b0987973b3", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148186786000, "endTime": 156148186824400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8b90b8ac-0998-4cb7-bcdf-b56f560efc4b", "logId": "125e61cc-da52-464c-91a8-61c3aa83238c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "125e61cc-da52-464c-91a8-61c3aa83238c", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148186786000, "endTime": 156148186824400}, "additional": {"logType": "info", "children": [], "durationId": "4736f8a1-c594-45d2-9092-96b0987973b3"}}, {"head": {"id": "67ac99f5-a2bd-44e0-8737-eb933d605f60", "name": "BUILD SUCCESSFUL in 1 s 74 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148186881700}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "346f8940-f3dd-4495-86ad-baf37c3feba6", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156147113228700, "endTime": 156148187235200}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 19, "second": 37}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "9a5bf57a-8cd0-475d-bdf7-b3162ba3ee93", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148187272200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d82de4cd-28e7-4b8c-bfd3-092af04441a3", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148187440400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5098846-6a76-461a-845a-80c9f6d91ce4", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148188048600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a780d32-e756-4580-8967-1cec466e493c", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148188170900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43a2f4c7-efc2-4989-b622-e6fe70c478e9", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148188302400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14c06e04-6429-4a72-b296-6dfb7391d760", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148188448000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb06dbae-282b-41c6-9427-2a441040a8c1", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148188489900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee12a85b-9352-4ccc-b976-7f46a0332000", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148189258000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee077569-e1d4-4ce6-9daf-4033bbed6115", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148189451100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9b0b14d-eaea-438f-b14e-4767ad2b1229", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148189499300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab5251a5-802b-4bbb-9b97-0bd1f1eb4af3", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148189526100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "914a2db7-b77a-44d4-ae6e-c7a2390dd268", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148189549800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e371a801-d4e2-4001-bf13-50ef143136f9", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148189572100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "873d5257-56c7-45a4-bd79-1973d181e1e0", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148190466500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34c5e229-8d23-409d-97cf-4eade9d19ce8", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148190711500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f545713-e2b5-4877-b677-91327e4fe140", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148190972600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9823db5-e4eb-47ef-b893-b6705706e002", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148191023600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59e60546-9ec2-406e-a2b5-ac267d5b5314", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148191052300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ffb6837-7ae7-4e8e-81ff-06526d27cfb7", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148191079100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fafaf9ec-da13-482c-93bc-8e3be659e296", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148191104300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22c94500-8817-4e94-bc6e-533d80d7a6a3", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148191126300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee3b0594-1c97-4784-9725-89b4962344e5", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148191147300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5eff2750-fa77-495f-a994-6d74bd3a726d", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148192784600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe345799-a9cc-49d4-9fc3-e8c16697f9a0", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148193422500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5fcf78a-b5f2-43c0-96c2-db039d92f2d7", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148193757200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "328e61de-0d52-47ab-aad4-412399587ad6", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148193980600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c185423-3367-4363-8f31-51dd93e79593", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148194170900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a5d3b24-a095-4b6a-91b8-ede9313a20e7", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148194781100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af763b02-3511-4b40-8964-094a40b5908d", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148195572900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d29f5436-5008-4418-8577-c2746ae79cb4", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148195824900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "730367cc-6eef-419c-8dcc-bb202660671a", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148195966100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "204f3445-4784-4689-a84b-19560b54e81c", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148196008400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efc47bdb-3822-4937-81a0-09457d1344c7", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148196039900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0636b3d3-7dc3-44a3-8786-84523d61cea0", "name": "There is no need to refresh cache, since the incremental task entry:default@SignHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148196064000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9e202b1-cd6d-4150-9dd9-3ab0e10fceed", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148201879100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5155440-c3a3-4108-9335-474dc695358e", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148202456800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad89ff4a-b607-4b55-ad22-8a5638d8b2a9", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148203399400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff4f977e-2385-4d51-ae72-0eff2f57c0fd", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148203832200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}