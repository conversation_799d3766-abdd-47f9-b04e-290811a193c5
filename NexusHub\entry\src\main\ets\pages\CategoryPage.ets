import { AppModel, AppSearchParams, AppListResponse } from '../models/App';
import { CategoryModel } from '../models/Category';
import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { HttpClient } from '../services/HttpClient';
import { AppCard } from '../components/AppCard';
import { LoadingView, LoadingState, LoadMoreView } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { preferences } from '@kit.ArkData';
// getContext is deprecated, use this.getUIContext().getHostContext() instead
import { hilog } from '@kit.PerformanceAnalysisKit';

interface CategoryPageParams {
  categoryId: string;
  categoryName: string;
}

interface SortOption {
  key: string;
  label: string;
  icon: Resource;
}

/**
 * 分类页面
 */
@Entry
@Component
struct CategoryPage {
  @State apps: AppModel[] = [];
  @State loadingState: LoadingState = LoadingState.LOADING;
  @State currentPage: number = 1;
  @State hasMore: boolean = true;
  @State isLoadingMore: boolean = false;
  @State sortBy: string = 'downloadCount';
  @State showSortMenu: boolean = false;
  @State refreshing: boolean = false;

  private categoryId: string = '';
  private categoryName: string = '';
  private deviceUtils = DeviceUtils.getInstance();
  private apiService = ApiService.getInstance();
  private httpClient = HttpClient.getInstance();
  private searchParams: AppSearchParams = {
    page: 1,
    page_size: 20
  };

  aboutToAppear() {
    const params = this.getUIContext().getRouter().getParams() as CategoryPageParams;
    this.categoryId = params?.categoryId || '';
    this.categoryName = params?.categoryName || '分类';
    
    if (this.categoryId) {
      this.loadApps();
    }
  }

  /**
   * 检查并设置认证token
   */
  private async checkAndSetAuthToken(): Promise<void> {
    try {
      const context = this.getUIContext().getHostContext();
      const options: preferences.Options = { name: 'user_data' };
      const dataPreferences = preferences.getPreferencesSync(context, options);
      const token = dataPreferences.getSync('token', '') as string;
      
      if (token) {
        this.apiService.setAuthToken(token);
      }
    } catch (error) {
      hilog.error(0x0000, 'CategoryPage', '检查登录状态失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 加载应用列表
   */
  async loadApps(loadMore: boolean = false) {
    if (this.isLoadingMore) {
      return;
    }

    this.isLoadingMore = true;
    
    // 只有在非加载更多的情况下才显示加载状态
    if (!loadMore) {
      this.loadingState = LoadingState.LOADING;
    }

    try {
      await this.checkAndSetAuthToken();
      
      if (!loadMore) {
        this.currentPage = 1;
        this.apps = [];
      }

      const searchParams: AppSearchParams = {
        category: this.categoryId,
        page: this.currentPage,
        page_size: Constants.PAGE_SIZE,
        sort: this.sortBy
      };

      hilog.info(0x0000, 'CategoryPage', 'Loading apps with params: %{public}s', JSON.stringify(searchParams));
      
      // 构建查询字符串
        const queryString = Object.entries(searchParams)
          .filter((entry: [string, string | number | undefined]) => entry[1] !== undefined && entry[1] !== null)
          .map((entry: [string, string | number]) => `${encodeURIComponent(entry[0])}=${encodeURIComponent(String(entry[1]))}`)
          .join('&');
        
        // 使用重试机制获取应用列表
        const response: AppListResponse = await this.httpClient.getWithRetry(
          `/public/apps?${queryString}`
        );

      hilog.info(0x0000, 'CategoryPage', `API Response: ${JSON.stringify(response)}`);

      if (response && response.code === 200) {
        // 检查响应数据结构 - 使用后端返回的list字段
        if (response.data && response.data.list && Array.isArray(response.data.list)) {
          const newApps = response.data.list;
          
          if (loadMore) {
            this.apps = [...this.apps, ...newApps];
          } else {
            this.apps = newApps;
          }
          
          this.currentPage++;
          this.hasMore = response.data.pagination ? response.data.pagination.hasNext === true : newApps.length >= Constants.PAGE_SIZE;
          
          // 根据是否有数据设置状态
          this.loadingState = this.apps.length > 0 ? LoadingState.SUCCESS : LoadingState.EMPTY;
        } else {
          // 数据结构不正确，设置为空状态
          hilog.warn(0x0000, 'CategoryPage', 'Invalid response data structure');
          this.loadingState = LoadingState.EMPTY;
          if (!loadMore) {
            this.apps = [];
          }
        }
      } else {
        // 非200响应码，记录错误并设置错误状态
        hilog.error(0x0000, 'CategoryPage', `API Error: ${response?.code} - ${response?.message}`);
        this.loadingState = LoadingState.ERROR;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      hilog.error(0x0000, 'CategoryPage', 'Error loading apps: %{public}s', errorMessage);
      
      // 根据错误类型设置不同的状态
      if (errorMessage.includes('超时') || errorMessage.includes('网络')) {
        this.loadingState = LoadingState.ERROR;
      } else {
        this.loadingState = LoadingState.ERROR;
      }
    } finally {
      this.isLoadingMore = false;
    }
  }

  /**
   * 刷新数据
   */
  private async refreshData() {
    this.refreshing = true;
    await this.loadApps();
    this.refreshing = false;
  }

  /**
   * 跳转到应用详情
   */
  private navigateToAppDetail(appId: string) {
    this.getUIContext().getRouter().pushUrl({
      url: 'pages/AppDetailPage',
      params: { appId }
    });
  }

  /**
   * 排序菜单
   */
  @Builder
  private SortMenu() {
    if (this.showSortMenu) {
      Column() {
        ForEach([
          { key: 'downloadCount', label: '下载量', icon: $r('app.media.ic_download') },
          { key: 'rating', label: '评分', icon: $r('app.media.ic_star') },
          { key: 'updatedAt', label: '更新时间', icon: $r('app.media.ic_refresh') },
          { key: 'createdAt', label: '发布时间', icon: $r('app.media.ic_notification') },
          { key: 'name', label: '名称', icon: $r('app.media.ic_menu') }
        ], (sort: SortOption) => {
          Row({ space: 12 }) {
            Image(sort.icon)
              .width(20)
              .height(20)
              .fillColor(this.sortBy === sort.key ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_SECONDARY)

            Text(sort.label)
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
              .fontColor(this.sortBy === sort.key ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_PRIMARY)
              .layoutWeight(1)

            if (this.sortBy === sort.key) {
              Image($r('app.media.ic_check'))
              .width(16)
              .height(16)
              .fillColor(Constants.COLORS.PRIMARY)
              .onError(() => {
                console.error('CategoryPage SortMenu: Failed to load check icon');
              })
            }
          }
          .width('100%')
          .height(48)
          .padding({ left: '16vp', right: '16vp' })
          .onClick(() => {
            if (this.sortBy !== sort.key) {
              this.sortBy = sort.key;
              this.loadApps();
            }
            this.showSortMenu = false;
          })
        })
      }
      .width('100%')
      .backgroundColor(Constants.COLORS.WHITE)
      .borderRadius({ topLeft: Constants.BORDER_RADIUS.LARGE, topRight: Constants.BORDER_RADIUS.LARGE })
      .padding({ top: '8vp', bottom: '8vp' })
      .animation({ duration: 300, curve: Curve.EaseInOut })
    }
  }

  /**
   * 应用网格（平板设备）
   */
  @Builder
  private AppGrid() {
    Grid() {
      ForEach(this.apps, (app: AppModel) => {
        GridItem() {
          AppCard({
            app: app,
            cardType: 'grid',
            showDownloadButton: true
          })
            .onClick(() => this.navigateToAppDetail(app.id.toString()))
        }
      })

      // 加载更多
      if (this.hasMore || this.isLoadingMore) {
        GridItem() {
          LoadMoreView({
            isLoading: this.isLoadingMore,
            hasMore: this.hasMore,
            onLoadMore: () => {
              if (!this.isLoadingMore && this.hasMore) {
                this.loadApps(true);
              }
            }
          })
        }
        .columnStart(0)
        .columnEnd(2)
      }
    }
    .columnsTemplate('1fr 1fr 1fr')
    .rowsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .columnsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .scrollBar(BarState.Auto)
  }

  /**
   * 应用列表（手机设备）
   */
  @Builder
  private AppList() {
    List({ space: 8 }) {
      ForEach(this.apps, (app: AppModel) => {
        ListItem() {
          AppCard({
            app: app,
            cardType: 'list',
            showDownloadButton: true
          })
            .margin({ left: 16, right: 16 })
            .onClick(() => this.navigateToAppDetail(app.id.toString()))
        }
      })

      // 加载更多
      if (this.hasMore || this.isLoadingMore) {
        ListItem() {
          LoadMoreView({
            isLoading: this.isLoadingMore,
            hasMore: this.hasMore,
            onLoadMore: () => {
              if (!this.isLoadingMore && this.hasMore) {
                this.loadApps(true);
              }
            }
          })
        }
      }
    }
    .scrollBar(BarState.Auto)
  }

  /**
   * 顶部统计信息
   */
  @Builder
  private CategoryStats() {
    Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) }) {
      Column({ space: 4 }) {
        Text((this.apps?.length || 0).toString())
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.PRIMARY)
        Text('应用总数')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
      }
      .alignItems(HorizontalAlign.Center)

      Column({ space: 4 }) {
        Text(this.getAverageRating().toFixed(1))
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.WARNING)
        Text('平均评分')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
      }
      .alignItems(HorizontalAlign.Center)

      Column({ space: 4 }) {
        Text(this.getTotalDownloads())
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.SUCCESS)
        Text('总下载量')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
      }
      .alignItems(HorizontalAlign.Center)
    }
    .width('100%')
    .justifyContent(FlexAlign.SpaceAround)
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .margin({ left: 16, right: 16, top: 8, bottom: 8 })
  }

  /**
   * 获取平均评分
   */
  private getAverageRating(): number {
    if (!this.apps || this.apps.length === 0) return 0;
    const totalRating = this.apps.reduce((sum, app) => sum + (app.rating || 0), 0);
    return totalRating / this.apps.length;
  }

  /**
   * 获取总下载量
   */
  private getTotalDownloads(): string {
    const total = this.apps.reduce((sum, app) => sum + (app.download_count || 0), 0);
    if (total >= 1000000) {
      return (total / 1000000).toFixed(1) + 'M';
    } else if (total >= 1000) {
      return (total / 1000).toFixed(1) + 'K';
    }
    return total.toString();
  }

  /**
   * 获取排序标签
   */
  private getSortLabel(): string {
    switch (this.sortBy) {
      case 'downloadCount':
        return '下载量';
      case 'rating':
        return '评分';
      case 'updatedAt':
        return '更新时间';
      case 'createdAt':
        return '发布时间';
      case 'name':
        return '名称';
      default:
        return '排序';
    }
  }

  build() {
    Stack({ alignContent: Alignment.Bottom }) {
      Column() {
        // 顶部导航栏
        Row() {
          Image($r('app.media.ic_back'))
            .width(24)
            .height(24)
            .fillColor(Constants.COLORS.TEXT_PRIMARY)
            .onError(() => {
              console.error('CategoryPage: Failed to load back icon');
            })
            .onClick(() => {
              this.getUIContext().getRouter().back();
            })

          Text(this.categoryName)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontWeight(FontWeight.Medium)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .layoutWeight(1)
            .textAlign(TextAlign.Center)

          Row({ space: 8 }) {
            Text(this.getSortLabel())
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
              .fontColor(Constants.COLORS.TEXT_PRIMARY)

            Image($r('app.media.ic_sort'))
              .width(20)
              .height(20)
              .fillColor(Constants.COLORS.TEXT_PRIMARY)
              .onError(() => {
                console.error('CategoryPage: Failed to load sort icon');
              })
          }
          .onClick(() => {
            this.showSortMenu = !this.showSortMenu;
          })
        }
        .width('100%')
        .height(56)
        .padding({ left: '16vp', right: '16vp' })
        .justifyContent(FlexAlign.SpaceBetween)
        .alignItems(VerticalAlign.Center)
        .backgroundColor(Constants.COLORS.WHITE)

        if (this.loadingState === LoadingState.LOADING) {
          LoadingView({ state: LoadingState.LOADING })
            .layoutWeight(1)
        } else if (this.loadingState === LoadingState.ERROR) {
          LoadingView({ 
            state: LoadingState.ERROR,
            onRetry: (): void => { this.loadApps(); }
          })
            .layoutWeight(1)
        } else if (this.loadingState === LoadingState.EMPTY) {
          LoadingView({ 
            state: LoadingState.EMPTY,
            message: '该分类下暂无应用'
          })
            .layoutWeight(1)
        } else {
          Column() {
            // 分类统计信息
            this.CategoryStats()

            // 应用列表
            Refresh({ refreshing: this.refreshing, offset: 64, friction: 100 }) {
              if (this.deviceUtils.isTablet()) {
                this.AppGrid()
              } else {
                this.AppList()
              }
            }
            .onStateChange((refreshStatus: RefreshStatus) => {
              if (refreshStatus === RefreshStatus.Refresh) {
                this.refreshData();
              }
            })
            .layoutWeight(1)
          }
        }
      }
      .width('100%')
      .height('100%')
      .backgroundColor(Constants.COLORS.BACKGROUND)

      // 排序菜单
      this.SortMenu()
    }
    .width('100%')
    .height('100%')
    .onClick(() => {
      if (this.showSortMenu) {
        this.showSortMenu = false;
      }
    })
  }
}