"use strict";
export default {
  "pages.layouts.userLayout.title": "Ant Design \xE9 a especifica\xE7\xE3o de web design mais influente no distrito de Xihu",
  "pages.login.accountLogin.tab": "Login da conta",
  "pages.login.accountLogin.errorMessage": "usu\xE1rio/senha incorreto(admin/ant.design)",
  "pages.login.failure": "Login falhou, por favor tente novamente!",
  "pages.login.success": "Login efetuado com sucesso!",
  "pages.login.username.placeholder": "Usu\xE1rio: admin or user",
  "pages.login.username.required": "Por favor insira seu usu\xE1rio!",
  "pages.login.password.placeholder": "Senha: ant.design",
  "pages.login.password.required": "Por favor insira sua senha!",
  "pages.login.phoneLogin.tab": "Login com Telefone",
  "pages.login.phoneLogin.errorMessage": "Erro de C\xF3digo de Verifica\xE7\xE3o",
  "pages.login.phoneNumber.placeholder": "Telefone",
  "pages.login.phoneNumber.required": "Por favor entre com seu telefone!",
  "pages.login.phoneNumber.invalid": "Telefone \xE9 inv\xE1lido!",
  "pages.login.captcha.placeholder": "C\xF3digo de Verifica\xE7\xE3o",
  "pages.login.captcha.required": "Por favor entre com o c\xF3digo de verifica\xE7\xE3o!",
  "pages.login.phoneLogin.getVerificationCode": "Obter C\xF3digo",
  "pages.getCaptchaSecondText": "seg(s)",
  "pages.login.rememberMe": "Lembre-me",
  "pages.login.forgotPassword": "Perdeu a Senha ?",
  "pages.login.submit": "Enviar",
  "pages.login.loginWith": "Login com :",
  "pages.login.registerAccount": "Registra Conta",
  "pages.welcome.link": "Bem-vindo",
  "pages.welcome.alertMessage": "Componentes pesados mais r\xE1pidos e mais fortes foram lan\xE7ados.",
  "pages.404.subTitle": "Desculpe, a p\xE1gina que voc\xEA visitou n\xE3o existe. ",
  "pages.404.buttonText": "Voltar \xE0 p\xE1gina inicial",
  "pages.admin.subPage.title": "Esta p\xE1gina s\xF3 pode ser vista pelo Admin",
  "pages.admin.subPage.alertMessage": "O Umi ui foi lan\xE7ado, bem-vindo ao usar o npm run ui para iniciar a experi\xEAncia.",
  "pages.searchTable.createForm.newRule": "Neva Regra",
  "pages.searchTable.updateForm.ruleConfig": "Configura\xE7\xE3o de Regra",
  "pages.searchTable.updateForm.basicConfig": "Informa\xE7\xE3o b\xE1sica",
  "pages.searchTable.updateForm.ruleName.nameLabel": "Nome da Regra",
  "pages.searchTable.updateForm.ruleName.nameRules": "Por favor entre com o nome da regra!",
  "pages.searchTable.updateForm.ruleDesc.descLabel": "Descri\xE7\xE3o da Regra",
  "pages.searchTable.updateForm.ruleDesc.descPlaceholder": "Por favor insira ao menos cinco caracteres",
  "pages.searchTable.updateForm.ruleDesc.descRules": "Insira uma descri\xE7\xE3o de regra de pelo menos cinco caracteres!",
  "pages.searchTable.updateForm.ruleProps.title": "Configurar Propriedades",
  "pages.searchTable.updateForm.object": "Objeto de Monitoramento",
  "pages.searchTable.updateForm.ruleProps.templateLabel": "Modelo de Regra",
  "pages.searchTable.updateForm.ruleProps.typeLabel": "Tipo de Regra",
  "pages.searchTable.updateForm.schedulingPeriod.title": "Definir Per\xEDodo de Agendamento",
  "pages.searchTable.updateForm.schedulingPeriod.timeLabel": "Hora de In\xEDcio",
  "pages.searchTable.updateForm.schedulingPeriod.timeRules": "Por favor selecione um hor\xE1riod e in\xEDcio!",
  "pages.searchTable.titleDesc": "Descri\xE7\xE3o",
  "pages.searchTable.ruleName": "O nome da regra \xE9 obrigat\xF3rio",
  "pages.searchTable.titleCallNo": "N\xFAmero de chamadas de servi\xE7o",
  "pages.searchTable.titleStatus": "Status",
  "pages.searchTable.nameStatus.default": "padr\xE3o",
  "pages.searchTable.nameStatus.running": "executando",
  "pages.searchTable.nameStatus.online": "online",
  "pages.searchTable.nameStatus.abnormal": "anormal",
  "pages.searchTable.titleUpdatedAt": "\xDAltima programa\xE7\xE3o em",
  "pages.searchTable.exception": "Por favor, indique o motivo da exce\xE7\xE3o!",
  "pages.searchTable.titleOption": "Op\xE7\xE3o",
  "pages.searchTable.config": "Configura\xE7\xE3o",
  "pages.searchTable.subscribeAlert": "Inscreva-se para receber alertas",
  "pages.searchTable.title": "Formul\xE1rio de Consulta",
  "pages.searchTable.new": "Novo",
  "pages.searchTable.chosen": "selecionado",
  "pages.searchTable.item": "item",
  "pages.searchTable.totalServiceCalls": "N\xFAmero total de chamadas de servi\xE7o",
  "pages.searchTable.tenThousand": "0000",
  "pages.searchTable.batchDeletion": "dele\xE7\xE3o em lote",
  "pages.searchTable.batchApproval": "aprova\xE7\xE3o em lote"
};
