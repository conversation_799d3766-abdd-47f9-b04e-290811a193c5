import { AppDetailModel, AppReviewModel, DownloadStatus, AppReviewListResponse, AppDetailResponse } from '../models/App';
import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { LoadingView, LoadingState } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { preferences } from '@kit.ArkData';
import { hilog } from '@kit.PerformanceAnalysisKit';
// getContext is deprecated, use this.getUIContext().getHostContext() instead

interface AppDetailPageParams {
  appId: string;
}

/**
 * 应用详情页面
 */
@Entry
@Component
struct AppDetailPage {
  @State appDetail: AppDetailModel | null = null;
  @State reviews: AppReviewModel[] = [];
  @State loadingState: LoadingState = LoadingState.LOADING;
  @State downloadStatus: DownloadStatus = DownloadStatus.PENDING;
  @State downloadProgress: number = 0;
  @State currentScreenshotIndex: number = 0;
  @State showFullDescription: boolean = false;
  @State reviewsPage: number = 1;
  @State hasMoreReviews: boolean = true;
  @State isLoadingReviews: boolean = false;

  private appId: string = '';
  private deviceUtils = DeviceUtils.getInstance();
  private apiService = ApiService.getInstance();
  private swiperController = new SwiperController();

  aboutToAppear() {
    const params = this.getUIContext().getRouter().getParams() as AppDetailPageParams;
    this.appId = params?.appId || '';
    if (this.appId) {
      this.loadAppDetail();
      this.loadReviews();
    }
  }

  /**
   * 检查并设置认证token
   */
  private async checkAndSetAuthToken(): Promise<void> {
    try {
      const context = this.getUIContext().getHostContext();
      const options: preferences.Options = { name: 'user_data' };
      const dataPreferences = preferences.getPreferencesSync(context, options);
      const token = dataPreferences.getSync('token', '') as string;
      
      if (token) {
        this.apiService.setAuthToken(token);
      }
    } catch (error) {
      hilog.error(0x0000, 'AppDetailPage', '检查登录状态失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 加载应用详情
   */
  private async loadAppDetail() {
    try {
      this.loadingState = LoadingState.LOADING;
      
      // 检查登录状态并设置token
      await this.checkAndSetAuthToken();
      
      // 优先使用匿名接口获取应用详情，无需登录
      let response: AppDetailResponse;
      try {
        response = await this.apiService.getAppDetailAnonymous(Number(this.appId)) as AppDetailResponse;
      } catch (anonymousError) {
        // 如果匿名接口失败，尝试使用需要认证的接口
        hilog.warn(0x0000, 'AppDetailPage', '匿名接口获取应用详情失败，尝试认证接口: %{public}s', JSON.stringify(anonymousError));
        response = await this.apiService.getAppDetail(Number(this.appId)) as AppDetailResponse;
      }
      
      if (response.code === 200 && response.data) {
        this.appDetail = response.data;
        this.loadingState = LoadingState.SUCCESS;
      } else {
        this.loadingState = LoadingState.ERROR;
      }
    } catch (error) {
      hilog.error(0x0000, 'AppDetailPage', '加载应用详情失败: %{public}s', JSON.stringify(error));
      this.loadingState = LoadingState.ERROR;
    }
  }

  /**
   * 加载评论
   */
  private async loadReviews(page: number = 1) {
    try {
      if (page === 1) {
        this.isLoadingReviews = true;
      }
      const response = await this.apiService.getAppReviews(Number(this.appId), page, 10);
      if (response.code === 200 && response.data) {
        if (page === 1) {
          this.reviews = response.data.list;
        } else {
          this.reviews = this.reviews.concat(response.data.list);
        }
        this.hasMoreReviews = response.data.pagination.hasNext ?? false;
        this.reviewsPage = page;
      }
    } catch (error) {
      hilog.error(0x0000, 'AppDetailPage', '加载评论失败: %{public}s', JSON.stringify(error));
    } finally {
      this.isLoadingReviews = false;
    }
  }

  /**
   * 下载应用
   */
  private async downloadApp() {
    if (!this.appDetail) return;
    
    try {
      // 记录下载（使用匿名API）
      if (this.appDetail.id && this.appDetail.versions?.length > 0) {
        await this.apiService.recordAppDownloadAnonymous(
          this.appDetail.id, 
          this.appDetail.versions[0].id
        );
      }
    } catch (error) {
      hilog.warn(0x0000, 'AppDetailPage', '记录下载失败: %{public}s', JSON.stringify(error));
      // 即使记录失败也继续下载流程
    }
    
    // 模拟下载过程
    this.downloadStatus = DownloadStatus.DOWNLOADING;
    this.downloadProgress = 0;
    
    const timer = setInterval(() => {
      this.downloadProgress += 10;
      if (this.downloadProgress >= 100) {
        clearInterval(timer);
        this.downloadStatus = DownloadStatus.COMPLETED;
        this.downloadProgress = 100;
      }
    }, 200);
  }

  /**
   * 安装应用
   */
  private installApp() {
    if (!this.appDetail) return;
    
    this.downloadStatus = DownloadStatus.INSTALLING;
    
    setTimeout(() => {
      this.downloadStatus = DownloadStatus.INSTALLED;
    }, 2000);
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 格式化评分
   */
  private formatRating(rating: number): string {
    return (rating || 0).toFixed(1);
  }

  /**
   * 应用头部信息
   */
  @Builder
  private AppHeader() {
    Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
      Image(this.appDetail?.icon || Constants.PLACEHOLDER_IMAGE)
        .width(this.deviceUtils.isTablet() ? 80 : 64)
        .height(this.deviceUtils.isTablet() ? 80 : 64)
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
        .objectFit(ImageFit.Cover)

      Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) }) {
        Text(this.appDetail?.name || '')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .maxLines(2)
          .textOverflow({ overflow: TextOverflow.Ellipsis })

        Text(this.appDetail?.developer_name || '')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })

        Row({ space: '8vp' }) {
          Row() {
            ForEach([1, 2, 3, 4, 5], (star: number) => {
              Text('★')
                .fontSize(14)
                .fontColor(star <= (this.appDetail?.rating || 0) ? Constants.COLORS.WARNING : Constants.COLORS.BORDER)
            })
          }
          .justifyContent(FlexAlign.Start)
          .alignItems(VerticalAlign.Center)

          Text(this.formatRating(this.appDetail?.rating || 0))
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
        }
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)
    }
    .width('100%')
    .alignItems(VerticalAlign.Top)
  }

  /**
   * 下载按钮
   */
  @Builder
  private DownloadButton() {
    Button() {
      if (this.downloadStatus === DownloadStatus.DOWNLOADING) {
        Row({ space: '8vp' }) {
          LoadingProgress()
            .width(16)
            .height(16)
            .color(Constants.COLORS.WHITE)
          Text(`${this.downloadProgress}%`)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.WHITE)
        }
      } else if (this.downloadStatus === DownloadStatus.INSTALLING) {
        Row({ space: '8vp' }) {
          LoadingProgress()
            .width(16)
            .height(16)
            .color(Constants.COLORS.WHITE)
          Text('安装中')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor(Constants.COLORS.WHITE)
        }
      } else {
        Text(this.getDownloadButtonText())
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(Constants.COLORS.WHITE)
      }
    }
    .width(this.deviceUtils.isTablet() ? 120 : 100)
    .height(40)
    .backgroundColor(this.getDownloadButtonColor())
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .enabled(this.downloadStatus !== DownloadStatus.DOWNLOADING && this.downloadStatus !== DownloadStatus.INSTALLING)
    .onClick(() => {
      if (this.downloadStatus === DownloadStatus.PENDING) {
        this.downloadApp();
      } else if (this.downloadStatus === DownloadStatus.COMPLETED) {
        this.installApp();
      }
    })
  }

  private getDownloadButtonText(): string {
    switch (this.downloadStatus) {
      case DownloadStatus.PENDING:
        return '下载';
      case DownloadStatus.COMPLETED:
        return '安装';
      case DownloadStatus.INSTALLED:
        return '打开';
      default:
        return '下载';
    }
  }

  private getDownloadButtonColor(): string {
    switch (this.downloadStatus) {
      case DownloadStatus.INSTALLED:
        return Constants.COLORS.SUCCESS;
      default:
        return Constants.COLORS.PRIMARY;
    }
  }

  /**
   * 应用信息
   */
  @Builder
  private AppInfo() {
    Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) }) {
      Column({ space: '4vp' }) {
        Text(this.formatFileSize(this.appDetail?.size || 0))
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
        Text('大小')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
      }
      .alignItems(HorizontalAlign.Center)

      Column({ space: '4vp' }) {
        Text(this.appDetail?.version || '')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
        Text('版本')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
      }
      .alignItems(HorizontalAlign.Center)

      Column({ space: '4vp' }) {
        Text(this.appDetail?.category_name || '')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
        Text('分类')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
      }
      .alignItems(HorizontalAlign.Center)

      Column({ space: '4vp' }) {
        Text((this.appDetail?.download_count || 0).toString())
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
        Text('下载量')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
      }
      .alignItems(HorizontalAlign.Center)
    }
    .width('100%')
    .justifyContent(FlexAlign.SpaceAround)
  }

  /**
   * 应用截图
   */
  @Builder
  private AppScreenshots() {
    if (this.appDetail?.screenshots && this.appDetail.screenshots.length > 0) {
      Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
        Text('应用截图')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .alignSelf(ItemAlign.Start)

        Swiper(this.swiperController) {
          ForEach(this.appDetail.screenshots, (screenshot: string, index: number) => {
            Image(screenshot)
              .width('100%')
              .height(this.deviceUtils.isTablet() ? 400 : 300)
              .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
              .objectFit(ImageFit.Cover)
          })
        }
        .width('100%')
        .height(this.deviceUtils.isTablet() ? 400 : 300)
        .autoPlay(false)
        .indicator(true)
        .loop(true)
        .onChange((index: number) => {
          this.currentScreenshotIndex = index;
        })
      }
    }
  }

  /**
   * 应用描述
   */
  @Builder
  private AppDescription() {
    Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
      Text('应用介绍')
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
        .fontWeight(FontWeight.Bold)
        .fontColor(Constants.COLORS.TEXT_PRIMARY)
        .alignSelf(ItemAlign.Start)

      Text(this.appDetail?.description || '')
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.TEXT_PRIMARY)
        .lineHeight(24)
        .maxLines(this.showFullDescription ? undefined : 3)
        .textOverflow({ overflow: TextOverflow.Ellipsis })

      if ((this.appDetail?.description || '').length > 100) {
        Text(this.showFullDescription ? '收起' : '展开')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(Constants.COLORS.PRIMARY)
          .onClick(() => {
            this.showFullDescription = !this.showFullDescription;
          })
      }
    }
    .alignItems(HorizontalAlign.Start)
  }

  /**
   * 用户评论
   */
  @Builder
  private AppReviews() {
    Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
      Row() {
        Text('用户评价')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .layoutWeight(1)

        Text('查看全部')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(Constants.COLORS.PRIMARY)
          .onClick(() => {
            // 跳转到评论页面
          })
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceBetween)

      if (this.reviews && this.reviews.length > 0) {
        ForEach(this.reviews.slice(0, 3), (review: AppReviewModel) => {
          this.ReviewItem(review)
        })
      } else {
        Text('暂无评价')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(Constants.COLORS.TEXT_HINT)
          .textAlign(TextAlign.Center)
          .width('100%')
          .padding('20vp')
      }
    }
    .alignItems(HorizontalAlign.Start)
  }

  /**
   * 评论项
   */
  @Builder
  private ReviewItem(review: AppReviewModel) {
    Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) }) {
      Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) }) {
        Image(review.user_avatar || Constants.PLACEHOLDER_IMAGE)
          .width(32)
          .height(32)
          .borderRadius(16)
          .objectFit(ImageFit.Cover)

        Column({ space: '4vp' }) {
          Text(review.username)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontWeight(FontWeight.Medium)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)

          Row() {
            ForEach([1, 2, 3, 4, 5], (star: number) => {
              Text('★')
                .fontSize(12)
                .fontColor(star <= review.rating ? Constants.COLORS.WARNING : Constants.COLORS.BORDER)
            })
          }
          .justifyContent(FlexAlign.Start)
          .alignItems(VerticalAlign.Center)

          Text(review.created_at)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_HINT)
            .margin({ left: '8vp' })
        }
        .alignItems(HorizontalAlign.Start)
        .justifyContent(FlexAlign.Start)
        .layoutWeight(1)
      }
      .alignItems(VerticalAlign.Top)

      Text(review.content)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.TEXT_PRIMARY)
        .lineHeight(20)
        .maxLines(3)
        .textOverflow({ overflow: TextOverflow.Ellipsis })
    }
    .width('100%')
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .alignItems(HorizontalAlign.Start)
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Text('←')
          .fontSize(24)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .onClick(() => {
            this.getUIContext().getRouter().back();
          })

        Text('应用详情')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontWeight(FontWeight.Medium)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('⋯')
          .fontSize(24)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .onClick(() => {
            // 分享功能
          })
      }
      .width('100%')
      .height(56)
      .padding({ left: '16vp', right: '16vp' })
      .justifyContent(FlexAlign.SpaceBetween)
      .alignItems(VerticalAlign.Center)
      .backgroundColor(Constants.COLORS.WHITE)

      if (this.loadingState === LoadingState.LOADING) {
        LoadingView({ state: LoadingState.LOADING })
          .layoutWeight(1)
      } else if (this.loadingState === LoadingState.ERROR) {
        LoadingView({ 
          state: LoadingState.ERROR,
          onRetry: () => {
            this.loadAppDetail();
          }
        })
          .layoutWeight(1)
      } else if (this.appDetail) {
        Scroll() {
          Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) }) {
            // 应用头部信息和下载按钮
            Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
              this.AppHeader()
              this.DownloadButton()
            }
            .alignItems(HorizontalAlign.Start)

            Divider()
              .color(Constants.COLORS.BORDER)

            // 应用信息
            this.AppInfo()

            Divider()
              .color(Constants.COLORS.BORDER)

            // 应用截图
            this.AppScreenshots()

            // 应用描述
            this.AppDescription()

            Divider()
              .color(Constants.COLORS.BORDER)

            // 用户评论
            this.AppReviews()
          }
          .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
        }
        .layoutWeight(1)
        .scrollable(ScrollDirection.Vertical)
        .scrollBar(BarState.Auto)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.COLORS.BACKGROUND)
  }
}