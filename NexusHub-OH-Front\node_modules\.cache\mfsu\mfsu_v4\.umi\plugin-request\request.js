"use strict";
import axios from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/axios@0.27.2/node_modules/axios";
import useUmiRequest, { UseRequestProvider } from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/@ahooksjs+use-request@2.8.15_react@18.3.1/node_modules/@ahooksjs/use-request";
import { ApplyPluginsType } from "umi";
import { getPluginManager } from "../core/plugin";
function useRequest(service, options = {}) {
  return useUmiRequest(service, {
    formatResult: (result) => result?.data,
    requestMethod: (requestOptions) => {
      if (typeof requestOptions === "string") {
        return request(requestOptions);
      }
      if (typeof requestOptions === "object") {
        const { url, ...rest } = requestOptions;
        return request(url, rest);
      }
      throw new Error("request options error");
    },
    ...options
  });
}
let requestInstance;
let config;
const getConfig = () => {
  if (config) return config;
  config = getPluginManager().applyPlugins({
    key: "request",
    type: ApplyPluginsType.modify,
    initialValue: {}
  });
  return config;
};
const getRequestInstance = () => {
  if (requestInstance) return requestInstance;
  const config2 = getConfig();
  requestInstance = axios.create(config2);
  config2?.requestInterceptors?.forEach((interceptor) => {
    if (interceptor instanceof Array) {
      requestInstance.interceptors.request.use(async (config3) => {
        const { url } = config3;
        if (interceptor[0].length === 2) {
          const { url: newUrl, options } = await interceptor[0](url, config3);
          return { ...options, url: newUrl };
        }
        return interceptor[0](config3);
      }, interceptor[1]);
    } else {
      requestInstance.interceptors.request.use(async (config3) => {
        const { url } = config3;
        if (interceptor.length === 2) {
          const { url: newUrl, options } = await interceptor(url, config3);
          return { ...options, url: newUrl };
        }
        return interceptor(config3);
      });
    }
  });
  config2?.responseInterceptors?.forEach((interceptor) => {
    interceptor instanceof Array ? requestInstance.interceptors.response.use(interceptor[0], interceptor[1]) : requestInstance.interceptors.response.use(interceptor);
  });
  requestInstance.interceptors.response.use((response) => {
    const { data } = response;
    if (data?.success === false && config2?.errorConfig?.errorThrower) {
      config2.errorConfig.errorThrower(data);
    }
    return response;
  });
  return requestInstance;
};
const request = (url, opts = { method: "GET" }) => {
  const requestInstance2 = getRequestInstance();
  const config2 = getConfig();
  const { getResponse = false, requestInterceptors, responseInterceptors } = opts;
  const requestInterceptorsToEject = requestInterceptors?.map((interceptor) => {
    if (interceptor instanceof Array) {
      return requestInstance2.interceptors.request.use(async (config3) => {
        const { url: url2 } = config3;
        if (interceptor[0].length === 2) {
          const { url: newUrl, options } = await interceptor[0](url2, config3);
          return { ...options, url: newUrl };
        }
        return interceptor[0](config3);
      }, interceptor[1]);
    } else {
      return requestInstance2.interceptors.request.use(async (config3) => {
        const { url: url2 } = config3;
        if (interceptor.length === 2) {
          const { url: newUrl, options } = await interceptor(url2, config3);
          return { ...options, url: newUrl };
        }
        return interceptor(config3);
      });
    }
  });
  const responseInterceptorsToEject = responseInterceptors?.map((interceptor) => {
    return interceptor instanceof Array ? requestInstance2.interceptors.response.use(interceptor[0], interceptor[1]) : requestInstance2.interceptors.response.use(interceptor);
  });
  return new Promise((resolve, reject) => {
    requestInstance2.request({ ...opts, url }).then((res) => {
      requestInterceptorsToEject?.forEach((interceptor) => {
        requestInstance2.interceptors.request.eject(interceptor);
      });
      responseInterceptorsToEject?.forEach((interceptor) => {
        requestInstance2.interceptors.response.eject(interceptor);
      });
      resolve(getResponse ? res : res.data);
    }).catch((error) => {
      requestInterceptorsToEject?.forEach((interceptor) => {
        requestInstance2.interceptors.request.eject(interceptor);
      });
      responseInterceptorsToEject?.forEach((interceptor) => {
        requestInstance2.interceptors.response.eject(interceptor);
      });
      try {
        const handler = config2?.errorConfig?.errorHandler;
        if (handler)
          handler(error, opts, config2);
      } catch (e) {
        reject(e);
      }
      reject(error);
    });
  });
};
export {
  useRequest,
  UseRequestProvider,
  request,
  getRequestInstance
};
