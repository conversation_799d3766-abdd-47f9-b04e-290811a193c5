package api

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// Response API响应结构体
type Response struct {
	Code    int         `json:"code" example:"200"`
	Message string      `json:"message" example:"操作成功"`
	Data    interface{} `json:"data,omitempty"`
}

// ErrorResponse 错误响应结构体
//	@Description	错误响应
type ErrorResponse struct {
	Code    int    `json:"code" example:"400"`
	Message string `json:"message" example:"请求参数错误"`
}

// SuccessResponse 成功响应结构体
//	@Description	成功响应
type SuccessResponse struct {
	Code    int         `json:"code" example:"200"`
	Message string      `json:"message" example:"操作成功"`
	Data    interface{} `json:"data,omitempty"`
}

// PageResponse 分页响应结构体
type PageResponse struct {
	Code     int         `json:"code" example:"200"`
	Message  string      `json:"message" example:"操作成功"`
	Data     interface{} `json:"data,omitempty"`
	Total    int64       `json:"total" example:"100"`
	Page     int         `json:"page" example:"1"`
	PageSize int         `json:"page_size" example:"20"`
}

// TagResponse 标签响应结构体
//	@Description	标签响应
type TagResponse struct {
	ID          uint       `json:"id"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty"`
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Color       string     `json:"color"`
	IsActive    bool       `json:"is_active"`
}

// TagAppCountResponse 标签及应用数量响应
//	@Description	标签及应用数量响应
type TagAppCountResponse struct {
	Tag      TagResponse `json:"tag"`
	AppCount int64       `json:"app_count"`
}

// AppResponse 应用响应结构体
//	@Description	应用响应
type AppResponse struct {
	ID              uint       `json:"id"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
	DeletedAt       *time.Time `json:"deleted_at,omitempty"`
	Name            string     `json:"name"`
	PackageName     string     `json:"package_name"`
	Description     string     `json:"description"`
	Icon            string     `json:"icon"`
	DeveloperID     uint       `json:"developer_id"`
	CategoryID      uint       `json:"category_id"`
	DownloadCount   int        `json:"download_count"`
	AverageRating   float64    `json:"average_rating"`
	ReviewCount     int        `json:"review_count"`
	MinAndroidSDK   int        `json:"min_android_sdk"`
	Status          string     `json:"status"`
	CurrentVersions string     `json:"current_versions"`
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: "操作成功",
		Data:    data,
	})
}

// SuccessWithMessage 带自定义消息的成功响应
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    200,
		Message: message,
		Data:    data,
	})
}

// SuccessWithPage 分页成功响应
func SuccessWithPage(c *gin.Context, data interface{}, total int64, page, pageSize int) {
	c.JSON(http.StatusOK, PageResponse{
		Code:     200,
		Message:  "操作成功",
		Data:     data,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	})
}

// Fail 失败响应
func Fail(c *gin.Context, code int, message string) {
	c.JSON(http.StatusOK, Response{
		Code:    code,
		Message: message,
	})
}

// ParamError 参数错误响应
func ParamError(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, Response{
		Code:    400,
		Message: message,
	})
}

// BadRequest 请求参数错误响应
func BadRequest(c *gin.Context, message string) {
	if message == "" {
		message = "请求参数错误"
	}
	c.JSON(http.StatusBadRequest, Response{
		Code:    400,
		Message: message,
	})
}

// Unauthorized 未授权响应
func Unauthorized(c *gin.Context, message string) {
	if message == "" {
		message = "未授权或授权已过期"
	}
	c.JSON(http.StatusUnauthorized, Response{
		Code:    401,
		Message: message,
	})
}

// Forbidden 禁止访问响应
func Forbidden(c *gin.Context, message string) {
	if message == "" {
		message = "无权限执行此操作"
	}
	c.JSON(http.StatusForbidden, Response{
		Code:    403,
		Message: message,
	})
}

// NotFound 资源不存在响应
func NotFound(c *gin.Context, message string) {
	if message == "" {
		message = "资源不存在"
	}
	c.JSON(http.StatusNotFound, Response{
		Code:    404,
		Message: message,
	})
}

// ServerError 服务器错误响应
func ServerError(c *gin.Context, message string, err error) {
	if message == "" {
		message = "服务器内部错误"
	}
	// 如果在非生产环境，可以将错误详情添加到响应中
	if err != nil && gin.Mode() != gin.ReleaseMode {
		message = message + ": " + err.Error()
	}
	c.JSON(http.StatusInternalServerError, Response{
		Code:    500,
		Message: message,
	})
}

// CustomError 自定义错误响应
func CustomError(c *gin.Context, httpStatus, code int, message string) {
	c.JSON(httpStatus, Response{
		Code:    code,
		Message: message,
	})
}
