"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { useState, useEffect } from "react";
import { Card, List, Button, Tag, Space, Pagination, Tabs, Empty, message, Popconfirm } from "antd";
import { BellOutlined, DeleteOutlined, CheckOutlined, EyeOutlined } from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-components";
import { createStyles } from "antd-style";
import {
  getNotifications,
  markAsRead,
  markAllAsRead,
  deleteNotification
} from "@/services/ant-design-pro/notifications";
const useStyles = createStyles(({ token }) => ({
  notificationItem: {
    padding: "16px 24px",
    borderBottom: `1px solid ${token.colorBorderSecondary}`,
    "&:hover": {
      backgroundColor: token.colorBgTextHover
    },
    "&.unread": {
      backgroundColor: token.colorBgContainer,
      borderLeft: `4px solid ${token.colorPrimary}`
    }
  },
  notificationHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: "8px"
  },
  notificationTitle: {
    fontSize: "16px",
    fontWeight: 500,
    color: token.colorText,
    marginBottom: "4px"
  },
  notificationDescription: {
    fontSize: "14px",
    color: token.colorTextSecondary,
    lineHeight: "20px",
    marginBottom: "8px"
  },
  notificationMeta: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center"
  },
  notificationTime: {
    fontSize: "12px",
    color: token.colorTextTertiary
  },
  actionButtons: {
    display: "flex",
    gap: "8px"
  },
  typeTag: {
    marginRight: "8px"
  },
  statusTag: {
    marginRight: "8px"
  },
  toolbarCard: {
    marginBottom: "16px"
  },
  toolbar: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center"
  },
  emptyContainer: {
    padding: "60px 0",
    textAlign: "center"
  }
}));
const NotificationsPage = () => {
  const { styles } = useStyles();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [activeTab, setActiveTab] = useState("all");
  const [selectedIds, setSelectedIds] = useState([]);
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const params = {
        page: current,
        pageSize,
        read: activeTab === "unread" ? false : void 0
      };
      const response = await getNotifications(params);
      if (response.success && response.data) {
        setNotifications(response.data.list);
        setTotal(response.data.total);
      }
    } catch (error) {
      message.error("\u83B7\u53D6\u901A\u77E5\u5217\u8868\u5931\u8D25");
    } finally {
      setLoading(false);
    }
  };
  const handleMarkAsRead = async (id) => {
    try {
      const response = await markAsRead({ id });
      if (response.success) {
        message.success("\u6807\u8BB0\u5DF2\u8BFB\u6210\u529F");
        setNotifications(
          (prev) => prev.map(
            (item) => item.id === id ? { ...item, read: true } : item
          )
        );
      }
    } catch (error) {
      message.error("\u6807\u8BB0\u5DF2\u8BFB\u5931\u8D25");
    }
  };
  const handleMarkAllAsRead = async () => {
    try {
      const response = await markAllAsRead();
      if (response.success) {
        message.success("\u5168\u90E8\u6807\u8BB0\u5DF2\u8BFB\u6210\u529F");
        setNotifications(
          (prev) => prev.map((item) => ({ ...item, read: true }))
        );
      }
    } catch (error) {
      message.error("\u6807\u8BB0\u5168\u90E8\u5DF2\u8BFB\u5931\u8D25");
    }
  };
  const handleDelete = async (id) => {
    try {
      const response = await deleteNotification({ id });
      if (response.success) {
        message.success("\u5220\u9664\u6210\u529F");
        setNotifications((prev) => prev.filter((item) => item.id !== id));
        setTotal((prev) => prev - 1);
      }
    } catch (error) {
      message.error("\u5220\u9664\u5931\u8D25");
    }
  };
  const handleBatchDelete = async () => {
    try {
      await Promise.all(selectedIds.map((id) => deleteNotification({ id })));
      message.success("\u6279\u91CF\u5220\u9664\u6210\u529F");
      setNotifications((prev) => prev.filter((item) => !selectedIds.includes(item.id)));
      setTotal((prev) => prev - selectedIds.length);
      setSelectedIds([]);
    } catch (error) {
      message.error("\u6279\u91CF\u5220\u9664\u5931\u8D25");
    }
  };
  const getTypeTag = (type) => {
    const typeMap = {
      system: { color: "blue", text: "\u7CFB\u7EDF\u901A\u77E5" },
      review: { color: "green", text: "\u5BA1\u6838\u901A\u77E5" },
      developer: { color: "orange", text: "\u5F00\u53D1\u8005\u901A\u77E5" },
      security: { color: "red", text: "\u5B89\u5168\u901A\u77E5" }
    };
    const config = typeMap[type] || { color: "default", text: type };
    return /* @__PURE__ */ jsx(Tag, { color: config.color, className: styles.typeTag, children: config.text });
  };
  const getStatusTag = (status) => {
    const statusMap = {
      info: { color: "blue", text: "\u4FE1\u606F" },
      success: { color: "green", text: "\u6210\u529F" },
      warning: { color: "orange", text: "\u8B66\u544A" },
      error: { color: "red", text: "\u9519\u8BEF" }
    };
    const config = statusMap[status] || { color: "default", text: status };
    return /* @__PURE__ */ jsx(Tag, { color: config.color, className: styles.statusTag, children: config.text });
  };
  useEffect(() => {
    fetchNotifications();
  }, [current, pageSize, activeTab]);
  const unreadCount = notifications.filter((item) => !item.read).length;
  return /* @__PURE__ */ jsxs(
    PageContainer,
    {
      title: "\u901A\u77E5\u4E2D\u5FC3",
      subTitle: "\u67E5\u770B\u548C\u7BA1\u7406\u60A8\u7684\u6240\u6709\u901A\u77E5",
      extra: [
        /* @__PURE__ */ jsx(Button, { onClick: fetchNotifications, children: "\u5237\u65B0" }, "refresh")
      ],
      children: [
        /* @__PURE__ */ jsx(Card, { className: styles.toolbarCard, children: /* @__PURE__ */ jsxs("div", { className: styles.toolbar, children: [
          /* @__PURE__ */ jsx(
            Tabs,
            {
              activeKey: activeTab,
              onChange: (key) => {
                setActiveTab(key);
                setCurrent(1);
              },
              items: [
                {
                  key: "all",
                  label: `\u5168\u90E8 (${total})`
                },
                {
                  key: "unread",
                  label: `\u672A\u8BFB (${unreadCount})`
                }
              ]
            }
          ),
          /* @__PURE__ */ jsxs(Space, { children: [
            unreadCount > 0 && /* @__PURE__ */ jsx(
              Button,
              {
                type: "primary",
                icon: /* @__PURE__ */ jsx(CheckOutlined, {}),
                onClick: handleMarkAllAsRead,
                children: "\u5168\u90E8\u5DF2\u8BFB"
              }
            ),
            selectedIds.length > 0 && /* @__PURE__ */ jsx(
              Popconfirm,
              {
                title: "\u786E\u8BA4\u5220\u9664",
                description: `\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684 ${selectedIds.length} \u6761\u901A\u77E5\u5417\uFF1F`,
                onConfirm: handleBatchDelete,
                okText: "\u786E\u5B9A",
                cancelText: "\u53D6\u6D88",
                children: /* @__PURE__ */ jsxs(
                  Button,
                  {
                    danger: true,
                    icon: /* @__PURE__ */ jsx(DeleteOutlined, {}),
                    children: [
                      "\u6279\u91CF\u5220\u9664 (",
                      selectedIds.length,
                      ")"
                    ]
                  }
                )
              }
            )
          ] })
        ] }) }),
        /* @__PURE__ */ jsx(Card, { children: notifications.length > 0 ? /* @__PURE__ */ jsxs(Fragment, { children: [
          /* @__PURE__ */ jsx(
            List,
            {
              loading,
              dataSource: notifications,
              renderItem: (item) => /* @__PURE__ */ jsx(
                "div",
                {
                  className: `${styles.notificationItem} ${!item.read ? "unread" : ""}`,
                  children: /* @__PURE__ */ jsx("div", { className: styles.notificationHeader, children: /* @__PURE__ */ jsxs("div", { style: { flex: 1 }, children: [
                    /* @__PURE__ */ jsxs("div", { className: styles.notificationTitle, children: [
                      /* @__PURE__ */ jsx(BellOutlined, { style: { marginRight: "8px" } }),
                      item.title,
                      !item.read && /* @__PURE__ */ jsx(Tag, { color: "red", size: "small", style: { marginLeft: "8px" }, children: "\u672A\u8BFB" })
                    ] }),
                    item.description && /* @__PURE__ */ jsx("div", { className: styles.notificationDescription, children: item.description }),
                    /* @__PURE__ */ jsxs("div", { className: styles.notificationMeta, children: [
                      /* @__PURE__ */ jsxs(Space, { children: [
                        getTypeTag(item.type),
                        getStatusTag(item.status),
                        /* @__PURE__ */ jsx("span", { className: styles.notificationTime, children: item.created_at })
                      ] }),
                      /* @__PURE__ */ jsxs("div", { className: styles.actionButtons, children: [
                        !item.read && /* @__PURE__ */ jsx(
                          Button,
                          {
                            size: "small",
                            icon: /* @__PURE__ */ jsx(EyeOutlined, {}),
                            onClick: () => handleMarkAsRead(item.id),
                            children: "\u6807\u8BB0\u5DF2\u8BFB"
                          }
                        ),
                        /* @__PURE__ */ jsx(
                          Popconfirm,
                          {
                            title: "\u786E\u8BA4\u5220\u9664",
                            description: "\u786E\u5B9A\u8981\u5220\u9664\u8FD9\u6761\u901A\u77E5\u5417\uFF1F",
                            onConfirm: () => handleDelete(item.id),
                            okText: "\u786E\u5B9A",
                            cancelText: "\u53D6\u6D88",
                            children: /* @__PURE__ */ jsx(
                              Button,
                              {
                                size: "small",
                                danger: true,
                                icon: /* @__PURE__ */ jsx(DeleteOutlined, {}),
                                children: "\u5220\u9664"
                              }
                            )
                          }
                        )
                      ] })
                    ] })
                  ] }) })
                },
                item.id
              )
            }
          ),
          /* @__PURE__ */ jsx("div", { style: { textAlign: "center", marginTop: "24px" }, children: /* @__PURE__ */ jsx(
            Pagination,
            {
              current,
              pageSize,
              total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total2, range) => `\u7B2C ${range[0]}-${range[1]} \u6761/\u5171 ${total2} \u6761`,
              onChange: (page, size) => {
                setCurrent(page);
                setPageSize(size);
              }
            }
          ) })
        ] }) : /* @__PURE__ */ jsx("div", { className: styles.emptyContainer, children: /* @__PURE__ */ jsx(
          Empty,
          {
            image: Empty.PRESENTED_IMAGE_SIMPLE,
            description: activeTab === "unread" ? "\u6682\u65E0\u672A\u8BFB\u901A\u77E5" : "\u6682\u65E0\u901A\u77E5"
          }
        ) }) })
      ]
    }
  );
};
export default NotificationsPage;
