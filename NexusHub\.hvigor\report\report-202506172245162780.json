{"version": "2.0", "ppid": 35300, "events": [{"head": {"id": "56eb1cce-f8c8-4cac-bfcf-b9e2304b872b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886494201500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63def34c-6af5-4f23-b7f7-866eca3ab63f", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886504179300, "endTime": 146888877875600}, "additional": {"children": ["3f9f72d4-c0b1-47f1-a082-1879087ebe0e", "791f7ae1-411b-4df8-b0cb-0d196de03847", "a2b8c8b3-c574-492a-ab95-7eaf03fd33b7", "986b2a4a-80f2-4512-82c1-e49744bb02f1", "d8c58ed0-a3e6-42d5-ace8-ff3aed2e5953", "a43d29f8-0850-4423-9f68-f2ea10811022", "dc9895c1-7b4d-43d6-9bbe-d26bd34583df"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "a8baa954-4fe4-49a3-bae6-6ead0b2ae392"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f9f72d4-c0b1-47f1-a082-1879087ebe0e", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886504182600, "endTime": 146886519225800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63def34c-6af5-4f23-b7f7-866eca3ab63f", "logId": "7a37ec22-b8f4-4f0e-91e1-311f7ff53dc5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "791f7ae1-411b-4df8-b0cb-0d196de03847", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886519240900, "endTime": 146888875175100}, "additional": {"children": ["177cbe91-1041-4f3b-af02-e195ec2dc0b5", "ada95784-9fa8-44ed-a04b-fe5d4fd5d609", "63eddf3c-ae32-4bca-a540-52cb2cd2f644", "af87866f-809c-462c-acbe-1c4332ad7754", "7c720fb5-dea6-433f-bf52-c58ee06d4823", "b56d2b5b-e67f-4712-b35d-60bd8c5b25f8", "a7239e4e-9a60-45a6-bf9b-9d40a5d9d420", "5b610d25-414d-4dd0-a3fd-1c528405cff9", "610e454c-786c-451c-ab7a-c50fa0f9d7e4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63def34c-6af5-4f23-b7f7-866eca3ab63f", "logId": "0937e45c-5fa0-4dc6-8cb6-ccfc245e4c0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2b8c8b3-c574-492a-ab95-7eaf03fd33b7", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888875205400, "endTime": 146888877829400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63def34c-6af5-4f23-b7f7-866eca3ab63f", "logId": "f69c3bfd-8f94-45ad-9535-9df362c6fa47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "986b2a4a-80f2-4512-82c1-e49744bb02f1", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888877838100, "endTime": 146888877864100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63def34c-6af5-4f23-b7f7-866eca3ab63f", "logId": "9700d1bd-dbc3-4dd5-94b7-66482d7c92a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8c58ed0-a3e6-42d5-ace8-ff3aed2e5953", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886509144100, "endTime": 146886509312200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63def34c-6af5-4f23-b7f7-866eca3ab63f", "logId": "94c040d1-386d-459f-8a44-69d842617e07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94c040d1-386d-459f-8a44-69d842617e07", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886509144100, "endTime": 146886509312200}, "additional": {"logType": "info", "children": [], "durationId": "d8c58ed0-a3e6-42d5-ace8-ff3aed2e5953", "parent": "a8baa954-4fe4-49a3-bae6-6ead0b2ae392"}}, {"head": {"id": "a43d29f8-0850-4423-9f68-f2ea10811022", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886513645000, "endTime": 146886513681900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63def34c-6af5-4f23-b7f7-866eca3ab63f", "logId": "135983b0-f0a1-45c0-9b63-edbf88ad87a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "135983b0-f0a1-45c0-9b63-edbf88ad87a9", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886513645000, "endTime": 146886513681900}, "additional": {"logType": "info", "children": [], "durationId": "a43d29f8-0850-4423-9f68-f2ea10811022", "parent": "a8baa954-4fe4-49a3-bae6-6ead0b2ae392"}}, {"head": {"id": "b8777529-1c6f-4c7a-b970-98d1dc0879b3", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886514055500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "287074a9-56ca-4824-9e30-c999e784b100", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886519120000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a37ec22-b8f4-4f0e-91e1-311f7ff53dc5", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886504182600, "endTime": 146886519225800}, "additional": {"logType": "info", "children": [], "durationId": "3f9f72d4-c0b1-47f1-a082-1879087ebe0e", "parent": "a8baa954-4fe4-49a3-bae6-6ead0b2ae392"}}, {"head": {"id": "177cbe91-1041-4f3b-af02-e195ec2dc0b5", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886523757000, "endTime": 146886523782300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "791f7ae1-411b-4df8-b0cb-0d196de03847", "logId": "26c7b42f-06d5-417c-909d-668824426176"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ada95784-9fa8-44ed-a04b-fe5d4fd5d609", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886523816100, "endTime": 146886527227000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "791f7ae1-411b-4df8-b0cb-0d196de03847", "logId": "87adc243-0b30-4d0e-8d26-4eb4d3650b3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63eddf3c-ae32-4bca-a540-52cb2cd2f644", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886527311300, "endTime": 146888711951500}, "additional": {"children": ["7e97679c-04a7-4f01-a520-9989121dc652", "96c8c170-eb82-48f7-91c0-87dd2dacb8aa", "80283a05-49f1-4c0b-8556-f6b6f589e104"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "791f7ae1-411b-4df8-b0cb-0d196de03847", "logId": "ef5b3db2-970c-4ed8-ae19-57b158323171"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af87866f-809c-462c-acbe-1c4332ad7754", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888712081800, "endTime": 146888756385600}, "additional": {"children": ["87ccef4e-6d6a-4f3d-a89b-bb0a72ab6cc0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "791f7ae1-411b-4df8-b0cb-0d196de03847", "logId": "fae1bc47-d6fc-4c6c-8ba4-14a2e84686d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c720fb5-dea6-433f-bf52-c58ee06d4823", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888756444100, "endTime": 146888829350300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "791f7ae1-411b-4df8-b0cb-0d196de03847", "logId": "c2fecebe-51f5-47eb-8a28-5ae757eccbf0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b56d2b5b-e67f-4712-b35d-60bd8c5b25f8", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888831495400, "endTime": 146888846274200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "791f7ae1-411b-4df8-b0cb-0d196de03847", "logId": "da77b7f0-c12e-488a-b25a-b76111b2a617"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7239e4e-9a60-45a6-bf9b-9d40a5d9d420", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888846300100, "endTime": 146888874848700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "791f7ae1-411b-4df8-b0cb-0d196de03847", "logId": "fc56b5a3-18c9-4305-b1c7-9aa208c2a3d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b610d25-414d-4dd0-a3fd-1c528405cff9", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888874872000, "endTime": 146888875158700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "791f7ae1-411b-4df8-b0cb-0d196de03847", "logId": "e795bb8f-8f81-4270-b43f-8510ffe67d71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26c7b42f-06d5-417c-909d-668824426176", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886523757000, "endTime": 146886523782300}, "additional": {"logType": "info", "children": [], "durationId": "177cbe91-1041-4f3b-af02-e195ec2dc0b5", "parent": "0937e45c-5fa0-4dc6-8cb6-ccfc245e4c0f"}}, {"head": {"id": "87adc243-0b30-4d0e-8d26-4eb4d3650b3f", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886523816100, "endTime": 146886527227000}, "additional": {"logType": "info", "children": [], "durationId": "ada95784-9fa8-44ed-a04b-fe5d4fd5d609", "parent": "0937e45c-5fa0-4dc6-8cb6-ccfc245e4c0f"}}, {"head": {"id": "7e97679c-04a7-4f01-a520-9989121dc652", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886527925800, "endTime": 146886527964700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63eddf3c-ae32-4bca-a540-52cb2cd2f644", "logId": "783618dd-a14d-4800-8521-f33501694512"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "783618dd-a14d-4800-8521-f33501694512", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886527925800, "endTime": 146886527964700}, "additional": {"logType": "info", "children": [], "durationId": "7e97679c-04a7-4f01-a520-9989121dc652", "parent": "ef5b3db2-970c-4ed8-ae19-57b158323171"}}, {"head": {"id": "96c8c170-eb82-48f7-91c0-87dd2dacb8aa", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886529581800, "endTime": 146888711096400}, "additional": {"children": ["27d3ea87-253f-4b76-96bf-0999200c9400", "9882d375-7e42-4f10-a876-2c2130a18bb8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63eddf3c-ae32-4bca-a540-52cb2cd2f644", "logId": "4f64cc3a-31aa-484b-a993-38bfb0c3f90b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27d3ea87-253f-4b76-96bf-0999200c9400", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886529583000, "endTime": 146888393150600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "96c8c170-eb82-48f7-91c0-87dd2dacb8aa", "logId": "0e175535-5994-4e30-baa2-6cc1a2ed7355"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9882d375-7e42-4f10-a876-2c2130a18bb8", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888393178400, "endTime": 146888711083700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "96c8c170-eb82-48f7-91c0-87dd2dacb8aa", "logId": "1f0a6666-f4bb-4ede-85b9-fde8bb3a42b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7612cc05-5a8d-4b00-b084-fcb444fac4a1", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886529588900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b46eee06-69b3-4e44-80d4-59bf5912086f", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888392972800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e175535-5994-4e30-baa2-6cc1a2ed7355", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886529583000, "endTime": 146888393150600}, "additional": {"logType": "info", "children": [], "durationId": "27d3ea87-253f-4b76-96bf-0999200c9400", "parent": "4f64cc3a-31aa-484b-a993-38bfb0c3f90b"}}, {"head": {"id": "731e063a-3a68-4e3c-9b62-1ece92e7a7ab", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888393308900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f6ea478-ae4f-40c5-9df3-dbf75a86f825", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888624587600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c58cdd24-973f-47f0-b650-257d9c27cf2b", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888624866200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2b2834d-5cd6-4c0d-afe8-b67b64a173cc", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888625606700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e5a6a0c-da0a-4451-9f3b-c24bd4f9a9fa", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888625768500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d138f6ed-ae88-4f50-b512-7e43c94b452f", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888630808100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "188dc3cb-e0cc-452e-aaf3-6f8a5577b5d2", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888657873800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f56f465-d0af-4612-871c-ec9cf86f689a", "name": "Sdk init in 38 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888680487600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ffbb6e8-2c2a-4a38-b193-3f27d7f6c40a", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888681005100}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 45, "second": 18}, "markType": "other"}}, {"head": {"id": "d38c091a-1898-49e4-a0bd-b091128dbd02", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888681098400}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 45, "second": 18}, "markType": "other"}}, {"head": {"id": "eaaeaabe-ffde-4e2f-a697-84654511b285", "name": "Project task initialization takes 23 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888710527500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6738bf65-715a-4b8b-a742-947741f79040", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888710883600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81ec4349-729e-413f-9e91-463a440c3482", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888710981800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d11cc1e8-f9f0-49a7-9a93-58871fea5e5e", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888711024900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f0a6666-f4bb-4ede-85b9-fde8bb3a42b2", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888393178400, "endTime": 146888711083700}, "additional": {"logType": "info", "children": [], "durationId": "9882d375-7e42-4f10-a876-2c2130a18bb8", "parent": "4f64cc3a-31aa-484b-a993-38bfb0c3f90b"}}, {"head": {"id": "4f64cc3a-31aa-484b-a993-38bfb0c3f90b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886529581800, "endTime": 146888711096400}, "additional": {"logType": "info", "children": ["0e175535-5994-4e30-baa2-6cc1a2ed7355", "1f0a6666-f4bb-4ede-85b9-fde8bb3a42b2"], "durationId": "96c8c170-eb82-48f7-91c0-87dd2dacb8aa", "parent": "ef5b3db2-970c-4ed8-ae19-57b158323171"}}, {"head": {"id": "80283a05-49f1-4c0b-8556-f6b6f589e104", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888711873900, "endTime": 146888711926500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63eddf3c-ae32-4bca-a540-52cb2cd2f644", "logId": "878efb46-87d3-4254-a521-c5c4bcddbe2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "878efb46-87d3-4254-a521-c5c4bcddbe2d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888711873900, "endTime": 146888711926500}, "additional": {"logType": "info", "children": [], "durationId": "80283a05-49f1-4c0b-8556-f6b6f589e104", "parent": "ef5b3db2-970c-4ed8-ae19-57b158323171"}}, {"head": {"id": "ef5b3db2-970c-4ed8-ae19-57b158323171", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886527311300, "endTime": 146888711951500}, "additional": {"logType": "info", "children": ["783618dd-a14d-4800-8521-f33501694512", "4f64cc3a-31aa-484b-a993-38bfb0c3f90b", "878efb46-87d3-4254-a521-c5c4bcddbe2d"], "durationId": "63eddf3c-ae32-4bca-a540-52cb2cd2f644", "parent": "0937e45c-5fa0-4dc6-8cb6-ccfc245e4c0f"}}, {"head": {"id": "87ccef4e-6d6a-4f3d-a89b-bb0a72ab6cc0", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888712634900, "endTime": 146888756356200}, "additional": {"children": ["eef740be-e691-48ae-9a00-a17044e60d01", "921caf9d-db7b-4f0f-b71f-767ba0ee493f", "ff8b19b3-b47e-4134-b187-7a21eab96c3d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af87866f-809c-462c-acbe-1c4332ad7754", "logId": "28ad6bf1-cb2e-4620-b044-6a676f2f0c7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eef740be-e691-48ae-9a00-a17044e60d01", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888715049400, "endTime": 146888715067500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87ccef4e-6d6a-4f3d-a89b-bb0a72ab6cc0", "logId": "722184c6-842e-4544-ba67-681f0cb9b167"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "722184c6-842e-4544-ba67-681f0cb9b167", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888715049400, "endTime": 146888715067500}, "additional": {"logType": "info", "children": [], "durationId": "eef740be-e691-48ae-9a00-a17044e60d01", "parent": "28ad6bf1-cb2e-4620-b044-6a676f2f0c7f"}}, {"head": {"id": "921caf9d-db7b-4f0f-b71f-767ba0ee493f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888718035800, "endTime": 146888754660100}, "additional": {"children": ["481dbb3c-e39e-4f20-8980-a5a9c0e946b2", "188c3d03-098b-4820-85ab-94fe63b052ef"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87ccef4e-6d6a-4f3d-a89b-bb0a72ab6cc0", "logId": "d0192177-f27d-48ea-83bd-aa441a651f6c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "481dbb3c-e39e-4f20-8980-a5a9c0e946b2", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888718038800, "endTime": 146888728305500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "921caf9d-db7b-4f0f-b71f-767ba0ee493f", "logId": "1fbb4b74-c3a0-4dea-8a82-b7defeff187a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "188c3d03-098b-4820-85ab-94fe63b052ef", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888728326700, "endTime": 146888754650200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "921caf9d-db7b-4f0f-b71f-767ba0ee493f", "logId": "afef351b-9c97-408d-8043-94be4f8c9fc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f52c62d6-8883-4a77-a5cf-1ca2e36557ae", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888718050500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5e9acf2-b71b-4727-af3c-fd45b87abd7c", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888728158600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fbb4b74-c3a0-4dea-8a82-b7defeff187a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888718038800, "endTime": 146888728305500}, "additional": {"logType": "info", "children": [], "durationId": "481dbb3c-e39e-4f20-8980-a5a9c0e946b2", "parent": "d0192177-f27d-48ea-83bd-aa441a651f6c"}}, {"head": {"id": "e707d480-33b0-4b4e-b772-69ea35ec6d19", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888728454200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd66135b-27c0-46ed-bd39-d9de4eb4007b", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888743121500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fb72644-e53c-472b-9ccf-2eefbb6dc881", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888743482300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf86b09e-8f04-46d2-b2a2-48b654a10a68", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888744157800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6c45b4b-6661-440e-9509-46bd0f5691a1", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888744525300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6825fa5e-1a7a-492b-a81f-c864192bf9d9", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888744612500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8714e250-8e0e-4899-892d-515e1082d43b", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888744671600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2e81073-bf4a-45ef-95d3-73892381a975", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888744757400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "442d7a9b-c42d-4f8b-8e0f-79001126fc40", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888744825200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5df4623-bab5-4a3c-ba62-634c5afdcb55", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888745298300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96c816b3-70e6-4e2a-8bbe-05ca972ea72a", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888745462500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7990e689-c77d-4993-a713-c074725db8ac", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888745529900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1fcbc9c-be82-415c-87ac-4bc2e1e2afbb", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888745570600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b36543e6-ff03-486c-a0c1-1a9d5fbd152f", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888745755300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f90c058-e719-4ca2-86c0-0d79462377c1", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888745826500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ce6648b-7d72-4bfd-99af-d1c8fb595d00", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888746052800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c12f1c7f-4f5e-4945-8345-51f838ac0e8e", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888746184900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dfecbad-2144-45aa-b756-589842600b95", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888746229100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d913027a-c2ba-4512-a36c-18fbf9f9876b", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888746348200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "832f2973-ff81-45ce-93e2-5defa73f7baf", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888746638500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "151bd3af-2bfe-4c97-98f6-89b5f49561ce", "name": "Module entry task initialization takes 5 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888754230000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76bd4157-af49-4ad6-b05a-c68baec3ce8c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888754495800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26b6727f-9758-499a-99a0-7343ce6b08b3", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888754570200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77b8190a-b91f-4dc5-865f-02c4ecda3319", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888754614200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afef351b-9c97-408d-8043-94be4f8c9fc2", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888728326700, "endTime": 146888754650200}, "additional": {"logType": "info", "children": [], "durationId": "188c3d03-098b-4820-85ab-94fe63b052ef", "parent": "d0192177-f27d-48ea-83bd-aa441a651f6c"}}, {"head": {"id": "d0192177-f27d-48ea-83bd-aa441a651f6c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888718035800, "endTime": 146888754660100}, "additional": {"logType": "info", "children": ["1fbb4b74-c3a0-4dea-8a82-b7defeff187a", "afef351b-9c97-408d-8043-94be4f8c9fc2"], "durationId": "921caf9d-db7b-4f0f-b71f-767ba0ee493f", "parent": "28ad6bf1-cb2e-4620-b044-6a676f2f0c7f"}}, {"head": {"id": "ff8b19b3-b47e-4134-b187-7a21eab96c3d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888756325200, "endTime": 146888756340400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87ccef4e-6d6a-4f3d-a89b-bb0a72ab6cc0", "logId": "1019c7f7-af42-4e26-a5cb-90850b629682"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1019c7f7-af42-4e26-a5cb-90850b629682", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888756325200, "endTime": 146888756340400}, "additional": {"logType": "info", "children": [], "durationId": "ff8b19b3-b47e-4134-b187-7a21eab96c3d", "parent": "28ad6bf1-cb2e-4620-b044-6a676f2f0c7f"}}, {"head": {"id": "28ad6bf1-cb2e-4620-b044-6a676f2f0c7f", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888712634900, "endTime": 146888756356200}, "additional": {"logType": "info", "children": ["722184c6-842e-4544-ba67-681f0cb9b167", "d0192177-f27d-48ea-83bd-aa441a651f6c", "1019c7f7-af42-4e26-a5cb-90850b629682"], "durationId": "87ccef4e-6d6a-4f3d-a89b-bb0a72ab6cc0", "parent": "fae1bc47-d6fc-4c6c-8ba4-14a2e84686d0"}}, {"head": {"id": "fae1bc47-d6fc-4c6c-8ba4-14a2e84686d0", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888712081800, "endTime": 146888756385600}, "additional": {"logType": "info", "children": ["28ad6bf1-cb2e-4620-b044-6a676f2f0c7f"], "durationId": "af87866f-809c-462c-acbe-1c4332ad7754", "parent": "0937e45c-5fa0-4dc6-8cb6-ccfc245e4c0f"}}, {"head": {"id": "17bbddac-dfae-4f8b-88b1-c7064aa07775", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888772760400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5119fa6e-d042-4769-a42e-c1bdc0296875", "name": "hvigorfile, resolve hvigorfile dependencies in 73 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888829168500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2fecebe-51f5-47eb-8a28-5ae757eccbf0", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888756444100, "endTime": 146888829350300}, "additional": {"logType": "info", "children": [], "durationId": "7c720fb5-dea6-433f-bf52-c58ee06d4823", "parent": "0937e45c-5fa0-4dc6-8cb6-ccfc245e4c0f"}}, {"head": {"id": "610e454c-786c-451c-ab7a-c50fa0f9d7e4", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888830687000, "endTime": 146888831428100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "791f7ae1-411b-4df8-b0cb-0d196de03847", "logId": "93caa4dc-f98b-43f8-ae51-be15a7a9630a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "461d125c-15e3-4a0a-bc4b-55288fb89688", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888830876500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93caa4dc-f98b-43f8-ae51-be15a7a9630a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888830687000, "endTime": 146888831428100}, "additional": {"logType": "info", "children": [], "durationId": "610e454c-786c-451c-ab7a-c50fa0f9d7e4", "parent": "0937e45c-5fa0-4dc6-8cb6-ccfc245e4c0f"}}, {"head": {"id": "89e16623-ea21-4bd7-a640-3fd139e295d7", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888834764300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dd31a3c-98cd-4d5f-9f20-a289548f510f", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888844870100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da77b7f0-c12e-488a-b25a-b76111b2a617", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888831495400, "endTime": 146888846274200}, "additional": {"logType": "info", "children": [], "durationId": "b56d2b5b-e67f-4712-b35d-60bd8c5b25f8", "parent": "0937e45c-5fa0-4dc6-8cb6-ccfc245e4c0f"}}, {"head": {"id": "abf5cfde-cc06-4133-81b1-d150149a17c6", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888846444100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a6253dd-9cfc-446e-9a3b-e8c7be8e8298", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888861613900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6a00746-9be3-4a7b-b605-ac35207b8d20", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888861770500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7971ea87-27e0-4c81-a8bb-68378ed1b1e0", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888862545500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b16f1988-da3e-4703-9f1d-b7f1bc9cd2c0", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888868295600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dcb776c-123b-402a-8d79-45804f63a339", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888868436500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc56b5a3-18c9-4305-b1c7-9aa208c2a3d1", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888846300100, "endTime": 146888874848700}, "additional": {"logType": "info", "children": [], "durationId": "a7239e4e-9a60-45a6-bf9b-9d40a5d9d420", "parent": "0937e45c-5fa0-4dc6-8cb6-ccfc245e4c0f"}}, {"head": {"id": "dfa39864-4c71-4a36-890a-592d9b560f15", "name": "Configuration phase cost:2 s 352 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888875038700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e795bb8f-8f81-4270-b43f-8510ffe67d71", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888874872000, "endTime": 146888875158700}, "additional": {"logType": "info", "children": [], "durationId": "5b610d25-414d-4dd0-a3fd-1c528405cff9", "parent": "0937e45c-5fa0-4dc6-8cb6-ccfc245e4c0f"}}, {"head": {"id": "0937e45c-5fa0-4dc6-8cb6-ccfc245e4c0f", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886519240900, "endTime": 146888875175100}, "additional": {"logType": "info", "children": ["26c7b42f-06d5-417c-909d-668824426176", "87adc243-0b30-4d0e-8d26-4eb4d3650b3f", "ef5b3db2-970c-4ed8-ae19-57b158323171", "fae1bc47-d6fc-4c6c-8ba4-14a2e84686d0", "c2fecebe-51f5-47eb-8a28-5ae757eccbf0", "da77b7f0-c12e-488a-b25a-b76111b2a617", "fc56b5a3-18c9-4305-b1c7-9aa208c2a3d1", "e795bb8f-8f81-4270-b43f-8510ffe67d71", "93caa4dc-f98b-43f8-ae51-be15a7a9630a"], "durationId": "791f7ae1-411b-4df8-b0cb-0d196de03847", "parent": "a8baa954-4fe4-49a3-bae6-6ead0b2ae392"}}, {"head": {"id": "dc9895c1-7b4d-43d6-9bbe-d26bd34583df", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888877738100, "endTime": 146888877810600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63def34c-6af5-4f23-b7f7-866eca3ab63f", "logId": "24d4d2ee-667f-40a5-96b4-c2bf27655f7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24d4d2ee-667f-40a5-96b4-c2bf27655f7a", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888877738100, "endTime": 146888877810600}, "additional": {"logType": "info", "children": [], "durationId": "dc9895c1-7b4d-43d6-9bbe-d26bd34583df", "parent": "a8baa954-4fe4-49a3-bae6-6ead0b2ae392"}}, {"head": {"id": "f69c3bfd-8f94-45ad-9535-9df362c6fa47", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888875205400, "endTime": 146888877829400}, "additional": {"logType": "info", "children": [], "durationId": "a2b8c8b3-c574-492a-ab95-7eaf03fd33b7", "parent": "a8baa954-4fe4-49a3-bae6-6ead0b2ae392"}}, {"head": {"id": "9700d1bd-dbc3-4dd5-94b7-66482d7c92a0", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888877838100, "endTime": 146888877864100}, "additional": {"logType": "info", "children": [], "durationId": "986b2a4a-80f2-4512-82c1-e49744bb02f1", "parent": "a8baa954-4fe4-49a3-bae6-6ead0b2ae392"}}, {"head": {"id": "a8baa954-4fe4-49a3-bae6-6ead0b2ae392", "name": "init", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886504179300, "endTime": 146888877875600}, "additional": {"logType": "info", "children": ["7a37ec22-b8f4-4f0e-91e1-311f7ff53dc5", "0937e45c-5fa0-4dc6-8cb6-ccfc245e4c0f", "f69c3bfd-8f94-45ad-9535-9df362c6fa47", "9700d1bd-dbc3-4dd5-94b7-66482d7c92a0", "94c040d1-386d-459f-8a44-69d842617e07", "135983b0-f0a1-45c0-9b63-edbf88ad87a9", "24d4d2ee-667f-40a5-96b4-c2bf27655f7a"], "durationId": "63def34c-6af5-4f23-b7f7-866eca3ab63f"}}, {"head": {"id": "6cd69e12-4415-40ae-97b0-664d60dae842", "name": "Configuration task cost before running: 2 s 379 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888878846400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "022b4664-afa0-4565-a80e-290b52d78134", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888901028900, "endTime": 146888928231900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "68254ab2-79d8-46b2-8d8c-4b80a537c5d7", "logId": "f63f6653-3d5d-4118-a590-1123a79b20a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68254ab2-79d8-46b2-8d8c-4b80a537c5d7", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888882054300}, "additional": {"logType": "detail", "children": [], "durationId": "022b4664-afa0-4565-a80e-290b52d78134"}}, {"head": {"id": "2ba7e0f6-1311-49c2-a7e9-8a9f6b88c6c3", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888884180600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "023edaa6-37bf-468f-b4e4-500075b5dc74", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888884532400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7624ca8-6ae2-4c3b-ad37-f664b4c815d7", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888886587200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac30e4d5-6cb8-4844-9da0-1ac90c0d5ca5", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888889153200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "935047df-2317-45e1-9cd1-c3a91b12bdc5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888892034600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e5b6ce7-5b03-4710-a530-ee1bbd2c833f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888892159900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b47c471-5f34-4275-a0f3-0a8c327bd79f", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888901094300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79d30dd9-bd77-4562-93bf-a4449a1d80a1", "name": "Incremental task entry:default@PreBuild pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888927665800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55190fc1-ce43-4514-91b5-bce94d1a1f96", "name": "entry : default@PreBuild cost memory 0.6735153198242188", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888927940300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f63f6653-3d5d-4118-a590-1123a79b20a4", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888901028900, "endTime": 146888928231900}, "additional": {"logType": "info", "children": [], "durationId": "022b4664-afa0-4565-a80e-290b52d78134"}}, {"head": {"id": "fa230454-2853-4abc-a076-1143c79e4410", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888939147200, "endTime": 146888942251500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "18a56696-9847-4843-87d1-3ea149928096", "logId": "7f96d86d-b66a-4097-8706-64952028282e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18a56696-9847-4843-87d1-3ea149928096", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888936690000}, "additional": {"logType": "detail", "children": [], "durationId": "fa230454-2853-4abc-a076-1143c79e4410"}}, {"head": {"id": "1910407e-edb2-4fbd-bec2-8f72435fa9af", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888938154700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9889631-88f6-4fe3-bc9a-9303784bfe32", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888938279800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fba0dac3-db86-47c7-ab75-47ed7ed98bef", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888939164300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c6d2a0b-a3f7-4a63-a9cb-cbbba27ac878", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888940536900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4268ce5-9997-4b93-ac29-86a6691a5a57", "name": "entry : default@CreateModuleInfo cost memory 0.06516265869140625", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888941918700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4310a3d1-d399-4067-af16-9f5be726ac72", "name": "runTaskFromQueue task cost before running: 2 s 442 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888942073500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f96d86d-b66a-4097-8706-64952028282e", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888939147200, "endTime": 146888942251500, "totalTime": 2907400}, "additional": {"logType": "info", "children": [], "durationId": "fa230454-2853-4abc-a076-1143c79e4410"}}, {"head": {"id": "3108fd30-afdd-4920-9e09-23619eff218c", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888952653200, "endTime": 146888956096700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "533d72e5-6b8d-49c9-96c9-1d5d292714c1", "logId": "13f6ba1d-bf52-4982-8b74-e2013ada6813"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "533d72e5-6b8d-49c9-96c9-1d5d292714c1", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888945208700}, "additional": {"logType": "detail", "children": [], "durationId": "3108fd30-afdd-4920-9e09-23619eff218c"}}, {"head": {"id": "80abebb3-b734-41b7-bbe3-cb5294b609d8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888946776200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71f38ba4-8039-4d2f-bac6-b39e4f68e344", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888946892000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7b2c7a8-9b19-4452-854e-889225bc61c0", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888952674100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83824881-1120-4124-8025-ab6c3b0f2847", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888954270800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fee3015-889c-4fd7-a04c-0eb7df9a714b", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888955860000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22f53dcf-ee09-4685-9023-32aeaf38679e", "name": "entry : default@GenerateMetadata cost memory 0.10796356201171875", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888956015100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13f6ba1d-bf52-4982-8b74-e2013ada6813", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888952653200, "endTime": 146888956096700}, "additional": {"logType": "info", "children": [], "durationId": "3108fd30-afdd-4920-9e09-23619eff218c"}}, {"head": {"id": "de99dc1f-a173-4d56-907e-9bdf14e43fc0", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888959950600, "endTime": 146888960619000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ff254ae1-2b47-4fed-9487-fc31994dd8b6", "logId": "dd05ad06-61a1-43b7-abeb-dec582c053ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff254ae1-2b47-4fed-9487-fc31994dd8b6", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888958179200}, "additional": {"logType": "detail", "children": [], "durationId": "de99dc1f-a173-4d56-907e-9bdf14e43fc0"}}, {"head": {"id": "0929c5be-4fd7-48ee-a8ec-c93068422ae7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888959450800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63e5526f-e1cb-4c1a-adcf-42f2ead4539d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888959572600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "281b9061-60d6-449e-9e16-30ba8e823701", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888959966200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "632c7170-71f2-4298-8524-142372834bc7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888960270700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c1f6cb4-8e1a-4134-befc-e2793c161e3c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888960347900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e804e9e-f963-4716-b104-fb755b55487f", "name": "entry : default@ConfigureCmake cost memory 0.037750244140625", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888960465800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32c2ebe6-23af-4b60-b0a9-eac8ee62370c", "name": "runTaskFromQueue task cost before running: 2 s 460 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888960567400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd05ad06-61a1-43b7-abeb-dec582c053ec", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888959950600, "endTime": 146888960619000, "totalTime": 587800}, "additional": {"logType": "info", "children": [], "durationId": "de99dc1f-a173-4d56-907e-9bdf14e43fc0"}}, {"head": {"id": "e8f47283-1c18-4880-a6fc-ccb5fc5d9f0b", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888965329300, "endTime": 146888968766900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "65e26381-40b4-40ad-b3fc-70a2062be58a", "logId": "3084f8c3-2993-4d69-bd51-bb51f85703a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65e26381-40b4-40ad-b3fc-70a2062be58a", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888962849800}, "additional": {"logType": "detail", "children": [], "durationId": "e8f47283-1c18-4880-a6fc-ccb5fc5d9f0b"}}, {"head": {"id": "7d9c863d-0939-48cc-a616-f7f85e16fcac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888964261600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "779fc02a-aff5-4be2-b37f-9c1a4fe83e1f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888964403300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a66de3b-d482-4b5a-a165-419b99ab465f", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888965345300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0725ffd1-c2a7-485f-8bb6-f32c6725cb7b", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888968525100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e092a174-cc12-4486-9b29-e2a32ef1e3eb", "name": "entry : default@MergeProfile cost memory 0.1244049072265625", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888968692600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3084f8c3-2993-4d69-bd51-bb51f85703a1", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888965329300, "endTime": 146888968766900}, "additional": {"logType": "info", "children": [], "durationId": "e8f47283-1c18-4880-a6fc-ccb5fc5d9f0b"}}, {"head": {"id": "04939df2-2885-4a5d-a593-89ad2adc2524", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888973139800, "endTime": 146888976586900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d3c7c40f-2dbb-4b8a-b5b3-c45161b24d09", "logId": "ab1d2ffa-c590-46dc-8f0d-8cbca9e67e30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3c7c40f-2dbb-4b8a-b5b3-c45161b24d09", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888970740800}, "additional": {"logType": "detail", "children": [], "durationId": "04939df2-2885-4a5d-a593-89ad2adc2524"}}, {"head": {"id": "c2a4e9e8-e903-444d-ad31-16bc130e39ed", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888971962300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79ebfa68-71ed-4d73-bf01-3c2e253642d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888972077400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38a14694-abfd-4f00-a8ec-95cb29adbc0e", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888973153600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32353e74-8f13-4d5b-bb5b-9b7042738ca2", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888974549000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "215b3838-a0fc-4cd6-bb34-0f63dba496fd", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888976376900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e920639-3371-49df-b607-83fc257bd0ee", "name": "entry : default@CreateBuildProfile cost memory 0.11480712890625", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888976509400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab1d2ffa-c590-46dc-8f0d-8cbca9e67e30", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888973139800, "endTime": 146888976586900}, "additional": {"logType": "info", "children": [], "durationId": "04939df2-2885-4a5d-a593-89ad2adc2524"}}, {"head": {"id": "48ddb4ca-f6a9-42bb-a218-c051a5dc7ed4", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888980761500, "endTime": 146888981485300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "2d55a557-1a04-43fb-913a-2b6a7de7e802", "logId": "d8dccd28-1a13-4267-8549-41110983cc16"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d55a557-1a04-43fb-913a-2b6a7de7e802", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888978421000}, "additional": {"logType": "detail", "children": [], "durationId": "48ddb4ca-f6a9-42bb-a218-c051a5dc7ed4"}}, {"head": {"id": "5717e68e-47b0-463d-97e5-e284418c752b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888979675900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "942dcac0-eca9-4c18-b4c7-e31567d93fd5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888979776100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "409b0665-eb36-404a-a0bb-5af8ea64cc19", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888980778300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0ad389-9d37-47bb-9696-dc23c9b02fbe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888980914100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9012a877-c05c-407b-bcb2-80813ac85ecb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888980961100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84bd7cbb-44d4-4a86-8730-f732eecf42f9", "name": "entry : default@PreCheckSyscap cost memory 0.04160308837890625", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888981290600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed1fa594-ba1e-4824-89f9-b38b3fa2190b", "name": "runTaskFromQueue task cost before running: 2 s 481 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888981417700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8dccd28-1a13-4267-8549-41110983cc16", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888980761500, "endTime": 146888981485300, "totalTime": 630000}, "additional": {"logType": "info", "children": [], "durationId": "48ddb4ca-f6a9-42bb-a218-c051a5dc7ed4"}}, {"head": {"id": "0df955b5-7146-4c0c-9976-1103b4d31415", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888986673600, "endTime": 146888995242300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6335e6f9-076c-42aa-b10a-bc8ff99b9ebe", "logId": "7354b85a-99e0-45eb-acea-9fc7a40eb630"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6335e6f9-076c-42aa-b10a-bc8ff99b9ebe", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888983503700}, "additional": {"logType": "detail", "children": [], "durationId": "0df955b5-7146-4c0c-9976-1103b4d31415"}}, {"head": {"id": "80a58686-25c1-4c1a-9e5b-b70cc2ecbad0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888984724300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0de8d72a-f776-41c2-bbc8-14eabba34431", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888984859200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c22307b-aa7b-4098-ba0a-c32868b4879b", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888986690800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fad12410-de7e-4b5a-905e-5598cc2f89f0", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888994133100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e50ee3f0-1aa0-45f5-a95e-4bbc5be5a69e", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888995049700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "719a96ff-097e-40c8-9401-5c72bfa759c7", "name": "entry : default@GeneratePkgContextInfo cost memory 0.24123382568359375", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888995178800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7354b85a-99e0-45eb-acea-9fc7a40eb630", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888986673600, "endTime": 146888995242300}, "additional": {"logType": "info", "children": [], "durationId": "0df955b5-7146-4c0c-9976-1103b4d31415"}}, {"head": {"id": "cd1d0765-603d-4039-888f-7db1762b57b0", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889004197200, "endTime": 146889006856200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "0a379305-c36d-4c3b-a578-ec4e034fea16", "logId": "d5b1b1e1-be74-4083-83bd-ce4361ec21d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a379305-c36d-4c3b-a578-ec4e034fea16", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888997180200}, "additional": {"logType": "detail", "children": [], "durationId": "cd1d0765-603d-4039-888f-7db1762b57b0"}}, {"head": {"id": "32964856-abce-42f5-9caf-2d1c8b2a746f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888998276900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e98a2bf-3b99-4856-ba66-5f6357fe1b9c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146888998380900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ce6cf44-4709-45d3-9463-c4fdeb563b2f", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889004216900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7f659fc-523f-434a-9e0b-16dfb7a25ead", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889006252400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db38adb7-a9af-428f-a871-68d757f57971", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889006424100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91657979-9ad1-434a-8432-cf81f3186fb4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889006540800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e66346b8-2aa4-40f5-9f8b-5c26e2c235d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889006590700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5876bd64-6c16-47c8-a004-6e18d7865942", "name": "entry : default@ProcessIntegratedHsp cost memory 0.123291015625", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889006737700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7d8331f-3d1e-4a1f-8d30-f4c66bd385b1", "name": "runTaskFromQueue task cost before running: 2 s 507 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889006815900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5b1b1e1-be74-4083-83bd-ce4361ec21d8", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889004197200, "endTime": 146889006856200, "totalTime": 2598400}, "additional": {"logType": "info", "children": [], "durationId": "cd1d0765-603d-4039-888f-7db1762b57b0"}}, {"head": {"id": "31317d5c-c5de-41f3-a91c-e02ed1197535", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889011383400, "endTime": 146889011771000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d2c89ba2-0210-4d3a-8ea1-853fd5580232", "logId": "1f993118-722f-40cb-b993-f5d9eac6b5c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2c89ba2-0210-4d3a-8ea1-853fd5580232", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889009408100}, "additional": {"logType": "detail", "children": [], "durationId": "31317d5c-c5de-41f3-a91c-e02ed1197535"}}, {"head": {"id": "75861695-b6e9-40bf-9ad7-09d01befebea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889010528400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fb0ec52-3349-445d-87c7-fcddeb243b63", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889010624800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ceeafee-6616-49f5-82a0-245a88861ca5", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889011394300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dc9a436-78dc-49ef-8d24-08a63950cb34", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889011518700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b9f5872-ac5d-4f60-ba59-d04eace3afc3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889011564700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "760c8558-6b72-4666-ba8b-7b55e928242c", "name": "entry : default@BuildNativeWithCmake cost memory 0.03899383544921875", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889011657700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86207b75-d33d-4a04-9968-e461a04529b1", "name": "runTaskFromQueue task cost before running: 2 s 512 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889011729800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f993118-722f-40cb-b993-f5d9eac6b5c9", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889011383400, "endTime": 146889011771000, "totalTime": 324700}, "additional": {"logType": "info", "children": [], "durationId": "31317d5c-c5de-41f3-a91c-e02ed1197535"}}, {"head": {"id": "c99dc17e-9ec2-4763-bb25-bcc1648027d0", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889015360800, "endTime": 146889020430900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "62c857a6-9bb5-42a9-a0df-4d1902edd9ac", "logId": "dd302e8a-c1a3-4e94-95cd-bdab11319fba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62c857a6-9bb5-42a9-a0df-4d1902edd9ac", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889013403200}, "additional": {"logType": "detail", "children": [], "durationId": "c99dc17e-9ec2-4763-bb25-bcc1648027d0"}}, {"head": {"id": "404b8548-0fb3-48a4-9d48-2dca8b0c57fd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889014486800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0499f25-4279-4b02-ab49-c5118c02286f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889014583400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2732d96b-8d5a-4079-ab69-540df5e0c72b", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889015371700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f10a7b08-4a5b-4834-9860-4648459738cb", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889020045900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fb8224e-15bc-4eb2-875f-e4fefde3d2c7", "name": "entry : default@MakePackInfo cost memory 0.17138671875", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889020197100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd302e8a-c1a3-4e94-95cd-bdab11319fba", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889015360800, "endTime": 146889020430900}, "additional": {"logType": "info", "children": [], "durationId": "c99dc17e-9ec2-4763-bb25-bcc1648027d0"}}, {"head": {"id": "3d4785c3-6814-4d6c-b85e-a7d82c452467", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889025662700, "endTime": 146889043182700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5dd095a4-e46b-45f0-abd1-8d5f80073792", "logId": "8a069614-383c-4d72-a43f-23e621c21ea7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5dd095a4-e46b-45f0-abd1-8d5f80073792", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889023084500}, "additional": {"logType": "detail", "children": [], "durationId": "3d4785c3-6814-4d6c-b85e-a7d82c452467"}}, {"head": {"id": "3dbb02c7-5214-4909-bf1c-2e0f4aebb00e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889024189900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dba57c3d-aa3e-479c-b289-26bb9e798f81", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889024318600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "489eb9bc-3b82-4df3-87b3-c72fd9b371e2", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889025677600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09e7d8cc-60b4-4d41-a5f6-08636834cff2", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889025881900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7eea7d0-8f2e-4e87-8a15-1d2e3ba9c8b1", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889026820000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "938e274e-fd50-402b-a6bb-4479aad59a75", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889042905200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a61efe55-4a37-442b-87ba-61b1457daa2a", "name": "entry : default@SyscapTransform cost memory 0.1604156494140625", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889043086700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a069614-383c-4d72-a43f-23e621c21ea7", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889025662700, "endTime": 146889043182700}, "additional": {"logType": "info", "children": [], "durationId": "3d4785c3-6814-4d6c-b85e-a7d82c452467"}}, {"head": {"id": "a4c25cb0-ff39-4b8e-9d67-56f7b2f3aaa1", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889048595300, "endTime": 146889052150400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "611454ef-919f-4708-8421-66cfe07aa790", "logId": "5898554b-f4b0-4049-951a-abe2385dee33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "611454ef-919f-4708-8421-66cfe07aa790", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889045288700}, "additional": {"logType": "detail", "children": [], "durationId": "a4c25cb0-ff39-4b8e-9d67-56f7b2f3aaa1"}}, {"head": {"id": "fce8387a-8cc5-4d09-8885-9d035b316e18", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889046572900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfe1bda9-4992-4be5-ab3a-5487e1075568", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889046680700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44142459-29a3-4f5e-a5cc-379d354e3f0c", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889048609000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aefd440-e338-48bc-86a7-513abba9a76b", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889051837600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b12521e-5a51-4628-be1a-d57d9de191b3", "name": "entry : default@ProcessProfile cost memory 0.12859344482421875", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889052081800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5898554b-f4b0-4049-951a-abe2385dee33", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889048595300, "endTime": 146889052150400}, "additional": {"logType": "info", "children": [], "durationId": "a4c25cb0-ff39-4b8e-9d67-56f7b2f3aaa1"}}, {"head": {"id": "0b1356a6-5e28-4aa2-b9a8-475c1ca59810", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889057542000, "endTime": 146889064323300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e5884a30-6590-40be-98e0-9bab8f60faea", "logId": "f4cff098-4ae8-4132-af64-c99feeba2352"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5884a30-6590-40be-98e0-9bab8f60faea", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889054076700}, "additional": {"logType": "detail", "children": [], "durationId": "0b1356a6-5e28-4aa2-b9a8-475c1ca59810"}}, {"head": {"id": "bc9a4060-13b2-45f9-a740-1a91733296e1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889055287500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e7c7543-5c4c-4735-b76f-ed972e923a44", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889055399900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "245763af-20e4-4921-8cd2-3375e5b67eb2", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889057557600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3577014-d7bc-4151-9382-b63026fe3dbb", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889064122500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea1230c0-0863-4a6b-9172-e8acf83e6a44", "name": "entry : default@ProcessRouterMap cost memory 0.24521636962890625", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889064261200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4cff098-4ae8-4132-af64-c99feeba2352", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889057542000, "endTime": 146889064323300}, "additional": {"logType": "info", "children": [], "durationId": "0b1356a6-5e28-4aa2-b9a8-475c1ca59810"}}, {"head": {"id": "5c6a0e95-da9f-4fb1-a59e-cae4b96e0de2", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889071937900, "endTime": 146889077773200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "d2e27942-e9f0-44c1-bbc4-b4147d15e3af", "logId": "1d67d490-8cb4-4890-b1ed-b4e36170e5c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2e27942-e9f0-44c1-bbc4-b4147d15e3af", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889070709900}, "additional": {"logType": "detail", "children": [], "durationId": "5c6a0e95-da9f-4fb1-a59e-cae4b96e0de2"}}, {"head": {"id": "ac42dead-875d-4e6b-8c72-7710705b2e54", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889071725300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7d51d89-3e29-4865-97f7-9ff63ba2e083", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889071839200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fb6a50b-1eb7-454f-8347-e58b9ab3954c", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889071947100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2bfe9b7-1abf-4d88-b621-37c851bd7aaf", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889072091000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc15d56c-4dec-4c83-b123-7912e62501a5", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889076124100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4f80843-5b9a-4853-92e2-b0875459a996", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889076254500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb3585e2-51f6-422f-a4c0-593cd1429439", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889076332600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa5b0308-d57e-4fcf-9a9d-c0e7ad6939e2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889076374500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4ae8d6f-128f-48b5-8e88-723fbac61264", "name": "entry : default@ProcessStartupConfig cost memory 0.2672882080078125", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889077586100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb7dc601-ed21-4cb9-9ae1-ea72df04e573", "name": "runTaskFromQueue task cost before running: 2 s 578 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889077713700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d67d490-8cb4-4890-b1ed-b4e36170e5c0", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889071937900, "endTime": 146889077773200, "totalTime": 5750500}, "additional": {"logType": "info", "children": [], "durationId": "5c6a0e95-da9f-4fb1-a59e-cae4b96e0de2"}}, {"head": {"id": "34877b34-e566-4a33-af3b-c92e0a08d418", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889082700200, "endTime": 146889084964900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "9300421d-4202-49fa-8fe5-49cf3c423706", "logId": "01f28fac-4494-4ab5-a59a-5d1a37f2fe4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9300421d-4202-49fa-8fe5-49cf3c423706", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889080660800}, "additional": {"logType": "detail", "children": [], "durationId": "34877b34-e566-4a33-af3b-c92e0a08d418"}}, {"head": {"id": "9152c48f-8b9c-4dc9-a456-f32dbb0c2fdd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889081711600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a91be662-b4ab-4003-b947-6b5d5ce52088", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889081811500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8648230-79cf-47e2-9ed3-793a2fa796c8", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889082720700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "633f7874-7851-4b86-907c-60f1d7489595", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889082883300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8a8f679-8859-402a-9c54-35d3c2bed918", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889082959600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e378bc4-6062-42d0-8a1d-6adf4836822e", "name": "entry : default@BuildNativeWithNinja cost memory 0.05895233154296875", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889084694000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f946e2a0-d1d4-4c9e-9430-6e4001226925", "name": "runTaskFromQueue task cost before running: 2 s 585 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889084896600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01f28fac-4494-4ab5-a59a-5d1a37f2fe4a", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889082700200, "endTime": 146889084964900, "totalTime": 2161500}, "additional": {"logType": "info", "children": [], "durationId": "34877b34-e566-4a33-af3b-c92e0a08d418"}}, {"head": {"id": "3317dcba-e88e-4aac-bb56-05a0ef2fa837", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889091888100, "endTime": 146889098303900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "80e8717e-855e-4e80-bf1e-393dcf0609fc", "logId": "5664e7ee-7cb3-4812-aa21-24aa94f5ea29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80e8717e-855e-4e80-bf1e-393dcf0609fc", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889087833500}, "additional": {"logType": "detail", "children": [], "durationId": "3317dcba-e88e-4aac-bb56-05a0ef2fa837"}}, {"head": {"id": "79e14b0f-2089-48fb-b86e-9902a9fcb1ef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889088873700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "614cfd83-8778-4672-8dcb-c81c47ac0017", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889088970000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7230e9b1-7746-4e7d-9a46-01a8aba1f95f", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889090484500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "533ba51e-6538-4d0a-b5b6-405f9b5b0438", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889093872900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad01656a-34cd-42bc-b69b-6e6756f8f16c", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889096547400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b82c9743-31a8-45d2-93e0-fa1f6094e8b4", "name": "entry : default@ProcessResource cost memory 0.1693115234375", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889096666500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5664e7ee-7cb3-4812-aa21-24aa94f5ea29", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889091888100, "endTime": 146889098303900}, "additional": {"logType": "info", "children": [], "durationId": "3317dcba-e88e-4aac-bb56-05a0ef2fa837"}}, {"head": {"id": "d878f674-9a53-48c3-b65c-a26c397deefe", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889107966800, "endTime": 146889133324400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "33c007d2-392b-4574-b92e-8a170afde60c", "logId": "c30824f3-ae7c-4fcf-bfa4-31c35b3df96e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33c007d2-392b-4574-b92e-8a170afde60c", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889102629900}, "additional": {"logType": "detail", "children": [], "durationId": "d878f674-9a53-48c3-b65c-a26c397deefe"}}, {"head": {"id": "2a480a6a-c1b9-4bba-8cbd-7dd877982e73", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889103724000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48aa7a81-4890-4001-ad65-8bdff3143c8e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889103830900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42288840-0696-426a-a562-68767991cdd0", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889107982100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "915d7fc6-9710-403a-99b4-730e3811a162", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889132941100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba981824-a9e0-4f1f-8143-9d4b167999b0", "name": "entry : default@GenerateLoaderJson cost memory 0.9026947021484375", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889133194400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c30824f3-ae7c-4fcf-bfa4-31c35b3df96e", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889107966800, "endTime": 146889133324400}, "additional": {"logType": "info", "children": [], "durationId": "d878f674-9a53-48c3-b65c-a26c397deefe"}}, {"head": {"id": "25679e48-a447-4f3e-adf8-17ffc1e027a0", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889144749100, "endTime": 146889151705200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "883e0255-1c08-48ff-97c1-f864ab5ed270", "logId": "15057569-4afc-4ae9-8bdb-a0f5d5a867a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "883e0255-1c08-48ff-97c1-f864ab5ed270", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889142642400}, "additional": {"logType": "detail", "children": [], "durationId": "25679e48-a447-4f3e-adf8-17ffc1e027a0"}}, {"head": {"id": "28fc777a-37a1-42e9-adfb-5027bc64dbd9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889143804200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f439de39-b5ce-4456-a308-cd361518ef8b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889143928100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bb18c10-382d-443e-8718-fdeded2664ef", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889144760400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f25364a-da4d-4120-ab6f-fdb872459ddf", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889151351600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abee93c8-ca54-4e20-9e42-415bb7ef1eb1", "name": "entry : default@ProcessLibs cost memory 0.148681640625", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889151589600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15057569-4afc-4ae9-8bdb-a0f5d5a867a5", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889144749100, "endTime": 146889151705200}, "additional": {"logType": "info", "children": [], "durationId": "25679e48-a447-4f3e-adf8-17ffc1e027a0"}}, {"head": {"id": "a35f96e5-f8d2-49dd-bcd2-aeb8faae74a6", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889160615400, "endTime": 146889193272300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "198aa8d1-276c-44e5-bac9-41f2add8df52", "logId": "476b3e85-c200-40d7-92c5-c8c34abe4f4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "198aa8d1-276c-44e5-bac9-41f2add8df52", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889154695500}, "additional": {"logType": "detail", "children": [], "durationId": "a35f96e5-f8d2-49dd-bcd2-aeb8faae74a6"}}, {"head": {"id": "b0d9f0b8-ee58-4a1f-bb53-bccecde8b58b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889156011100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b97bc77-531e-47fc-bc05-c4568919e840", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889156175800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f52f6ade-44c3-4bf1-bbcf-24ffff90820e", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889157882900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a63df565-6132-4df3-987d-b151e77f7da5", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889160703700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2110dbf5-4b9f-4b46-be1d-faea36f9c6df", "name": "Incremental task entry:default@CompileResource pre-execution cost: 31 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889192842800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f76eeb41-b664-4dda-a75b-cbcbdaa3c059", "name": "entry : default@CompileResource cost memory 1.4798507690429688", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889193147900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "476b3e85-c200-40d7-92c5-c8c34abe4f4e", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889160615400, "endTime": 146889193272300}, "additional": {"logType": "info", "children": [], "durationId": "a35f96e5-f8d2-49dd-bcd2-aeb8faae74a6"}}, {"head": {"id": "489a2f7f-789b-4589-aad7-6bf7678372fd", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889201448500, "endTime": 146889204307900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "489d7d54-40b8-4d54-b192-a7a24a11eb0d", "logId": "246c2f20-ca1e-47a8-9a1c-7dfabd085a51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "489d7d54-40b8-4d54-b192-a7a24a11eb0d", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889196200000}, "additional": {"logType": "detail", "children": [], "durationId": "489a2f7f-789b-4589-aad7-6bf7678372fd"}}, {"head": {"id": "37558619-fad5-4589-955f-7318f03177c3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889197474200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "491575e9-fb40-4a0f-8ce4-270fc859afd2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889197601600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36aaf4e9-5fb6-4e12-a0c0-e9bd7e8ab4c1", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889201464600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74e233ab-fde0-4c18-83f8-e8ac4ef618e9", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889202066100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbd5355f-24c3-464e-8a62-35a53d56e832", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889204066500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8c2f6fc-9388-46bf-9d55-ea14ffb437d6", "name": "entry : default@DoNativeStrip cost memory 0.08419036865234375", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889204228100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "246c2f20-ca1e-47a8-9a1c-7dfabd085a51", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889201448500, "endTime": 146889204307900}, "additional": {"logType": "info", "children": [], "durationId": "489a2f7f-789b-4589-aad7-6bf7678372fd"}}, {"head": {"id": "ea75c5ad-6cc4-4bf9-9825-79c51f91564c", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889212084600, "endTime": 146889249813200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "b3dc4702-cbab-4db5-82b8-73419b459190", "logId": "fc684ab5-de3c-40af-9d36-f0da31d85443"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3dc4702-cbab-4db5-82b8-73419b459190", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889206222200}, "additional": {"logType": "detail", "children": [], "durationId": "ea75c5ad-6cc4-4bf9-9825-79c51f91564c"}}, {"head": {"id": "47b68752-384e-490d-8773-5c2dc3fad164", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889207487800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e73632f-9ccf-4351-8cee-b0c0174dd49e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889207616900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb9bce52-c60c-4745-bba2-efffe1051cab", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889212099800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5965067a-0a00-4344-ae3e-7d7565404747", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889212532100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b1a1e88-e135-443c-86cf-36a16a4ec776", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 30 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889249543600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a06f6011-dd65-4fcd-867f-28a76f0182c0", "name": "entry : default@CompileArkTS cost memory 1.2548828125", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889249725900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc684ab5-de3c-40af-9d36-f0da31d85443", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889212084600, "endTime": 146889249813200}, "additional": {"logType": "info", "children": [], "durationId": "ea75c5ad-6cc4-4bf9-9825-79c51f91564c"}}, {"head": {"id": "3d366df5-9710-47e6-83e1-dfe7c3ab8b60", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889262895400, "endTime": 146889270974300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "a1af3a09-97ad-4c42-83a0-9775c75787ce", "logId": "974d7c72-466c-43f4-89ad-d26a1706f95a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1af3a09-97ad-4c42-83a0-9775c75787ce", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889257162000}, "additional": {"logType": "detail", "children": [], "durationId": "3d366df5-9710-47e6-83e1-dfe7c3ab8b60"}}, {"head": {"id": "b8e95e61-872f-473f-9dca-93acc3b4f3c8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889258326100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "437efe42-ee4a-4c8d-bbbb-fa4e3abc07ab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889258446700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eed4015-ae18-4114-a451-ede381054aa9", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889262909900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12d49faf-deaa-46ce-8288-ead68cb5113b", "name": "entry : default@BuildJS cost memory 0.35659027099609375", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889270739600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "908a2896-3768-4d2c-8ed5-5d4d3d77d316", "name": "runTaskFromQueue task cost before running: 2 s 771 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889270910400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "974d7c72-466c-43f4-89ad-d26a1706f95a", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889262895400, "endTime": 146889270974300, "totalTime": 7977300}, "additional": {"logType": "info", "children": [], "durationId": "3d366df5-9710-47e6-83e1-dfe7c3ab8b60"}}, {"head": {"id": "6fc81309-d8db-4a2c-a0bb-7df05c602c22", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889275739500, "endTime": 146889278416500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "92c2719f-862f-44c0-aba6-fbba182b3644", "logId": "04820114-473b-4aa8-a291-f608d63aafa9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92c2719f-862f-44c0-aba6-fbba182b3644", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889272505700}, "additional": {"logType": "detail", "children": [], "durationId": "6fc81309-d8db-4a2c-a0bb-7df05c602c22"}}, {"head": {"id": "74b90d75-4b3f-4d0f-b13d-5b08a491bbac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889273426100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "518930b4-b7a3-414a-a34e-5dc8e3bd3ed4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889273522900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f3341f8-196d-4529-9539-4fb69785c9fc", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889275749700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0be1d6b7-75f4-4559-9753-cfd6b6871fa9", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889276453100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7953c40-b0b1-4dbe-a789-2caac2eb4604", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889278266600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c229e89-722e-4b94-9cee-df8d2a6f9aff", "name": "entry : default@CacheNativeLibs cost memory 0.10028076171875", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889278362700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04820114-473b-4aa8-a291-f608d63aafa9", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889275739500, "endTime": 146889278416500}, "additional": {"logType": "info", "children": [], "durationId": "6fc81309-d8db-4a2c-a0bb-7df05c602c22"}}, {"head": {"id": "a32154c4-dd6c-4d16-a2fe-0da2270b6368", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889281574000, "endTime": 146889282666900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "e4642071-d6bd-4b91-8574-b0803eddf327", "logId": "fc150d07-148a-4858-8678-048b42e4db1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4642071-d6bd-4b91-8574-b0803eddf327", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889279754500}, "additional": {"logType": "detail", "children": [], "durationId": "a32154c4-dd6c-4d16-a2fe-0da2270b6368"}}, {"head": {"id": "33c98392-5fa4-4515-84a6-b6d49ec5265b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889280607900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9520ec4e-4067-4ed9-be5b-d712944b12c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889280686100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f39d70fe-3070-48ec-9562-43eb56e419aa", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889281582000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f72490f1-2873-490a-9eee-d24046d5ad1d", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889281831800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57de523b-11ac-4543-ab78-4a6bdad949a2", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889282524800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad7d9f9a-7ac0-40a8-95e1-cd1b1b41ad7b", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07714080810546875", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889282616700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc150d07-148a-4858-8678-048b42e4db1b", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889281574000, "endTime": 146889282666900}, "additional": {"logType": "info", "children": [], "durationId": "a32154c4-dd6c-4d16-a2fe-0da2270b6368"}}, {"head": {"id": "346946a9-a407-4d16-bbe4-5e5358b725ff", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889293189400, "endTime": 146889312722000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "f0e2941c-3876-47e8-afe2-84dfc0fbc005", "logId": "2fa43e31-a8f4-4940-a25d-3bd8076d75cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0e2941c-3876-47e8-afe2-84dfc0fbc005", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889285414200}, "additional": {"logType": "detail", "children": [], "durationId": "346946a9-a407-4d16-bbe4-5e5358b725ff"}}, {"head": {"id": "453ab12d-31d8-4d46-8b5c-a08bf0a77874", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889286371900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3238d7f-b06a-4f9d-b7b0-abb2baf89fda", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889286503900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cba871af-e8f7-473a-a041-9bdbf1bb0b7b", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889293199800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a71c7864-e524-42aa-9591-42a01186cd80", "name": "Incremental task entry:default@PackageHap pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889312515500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ad61675-e37c-4a01-a785-a1e5f7e3ed7a", "name": "entry : default@PackageHap cost memory 1.01849365234375", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889312658200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fa43e31-a8f4-4940-a25d-3bd8076d75cb", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889293189400, "endTime": 146889312722000}, "additional": {"logType": "info", "children": [], "durationId": "346946a9-a407-4d16-bbe4-5e5358b725ff"}}, {"head": {"id": "f66ecfc3-2910-429e-98ea-dc96b065838d", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889320171000, "endTime": 146889322756600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": [], "detailId": "4db095a2-e15a-42dd-bdc6-9493e40c4505", "logId": "2e2dc6a8-7267-45ef-951f-f098a3b81d04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4db095a2-e15a-42dd-bdc6-9493e40c4505", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889316084500}, "additional": {"logType": "detail", "children": [], "durationId": "f66ecfc3-2910-429e-98ea-dc96b065838d"}}, {"head": {"id": "e538ef68-3f28-4c64-9e62-fe1d09fa84a8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889317444300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a1954d0-7217-4794-8fb4-eae7cc9e03f8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889317567600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bdb718c-4f4c-4d25-a575-7d35ccbbe2d0", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889320184600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60b5c0d8-0c31-4f15-a98a-042b287b1c9b", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889322576000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "534ff55f-d9f7-4619-a874-bd5f158404f7", "name": "entry : default@SignHap cost memory 0.1746063232421875", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889322696600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e2dc6a8-7267-45ef-951f-f098a3b81d04", "name": "UP-TO-DATE :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889320171000, "endTime": 146889322756600}, "additional": {"logType": "info", "children": [], "durationId": "f66ecfc3-2910-429e-98ea-dc96b065838d"}}, {"head": {"id": "50553e6d-a609-4e2b-ba1a-aa95ba2684b2", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889326116100, "endTime": 146889331720000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1c3e0844-eed6-45d2-9395-8eb778356a80", "logId": "1b02d5c0-9752-4701-a7c1-f787d2fac1da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c3e0844-eed6-45d2-9395-8eb778356a80", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889324448000}, "additional": {"logType": "detail", "children": [], "durationId": "50553e6d-a609-4e2b-ba1a-aa95ba2684b2"}}, {"head": {"id": "c53d63d4-f6fc-4d3e-a6e1-649ced224314", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889325230800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec74380c-06fd-458d-b496-ee77bb1fc131", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889325323800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d019c8b-82bc-48dd-830e-1a534e7eabba", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889326126400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a340a560-3c41-4e10-a0f6-332614e3872d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889331367200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "245611e1-4cd8-4771-b259-27c49d7528ee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889331497100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3de184b-3056-4ed8-9d7a-edccec4fca98", "name": "entry : default@CollectDebugSymbol cost memory 0.248687744140625", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889331599100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed5849f2-9f34-493c-aff0-8491b7e0b36e", "name": "runTaskFromQueue task cost before running: 2 s 832 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889331671400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b02d5c0-9752-4701-a7c1-f787d2fac1da", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889326116100, "endTime": 146889331720000, "totalTime": 5529000}, "additional": {"logType": "info", "children": [], "durationId": "50553e6d-a609-4e2b-ba1a-aa95ba2684b2"}}, {"head": {"id": "52e86e7f-7253-481c-b44d-f45dc08c8588", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889333816700, "endTime": 146889334200900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "0e04afad-39ca-4460-936c-861f3bdf9d09", "logId": "1a108c9d-dcb6-4f11-88eb-ab34df098f23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e04afad-39ca-4460-936c-861f3bdf9d09", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889333745200}, "additional": {"logType": "detail", "children": [], "durationId": "52e86e7f-7253-481c-b44d-f45dc08c8588"}}, {"head": {"id": "2950a18e-3908-4d98-85f7-6035f4e27e13", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889333832000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73758dc1-c06a-4c5a-9a29-7944250cc0b2", "name": "entry : assembleHap cost memory 0.0118408203125", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889334040700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03f8b965-eaf5-4503-834d-465dde302f58", "name": "runTaskFromQueue task cost before running: 2 s 834 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889334143200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a108c9d-dcb6-4f11-88eb-ab34df098f23", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889333816700, "endTime": 146889334200900, "totalTime": 308500}, "additional": {"logType": "info", "children": [], "durationId": "52e86e7f-7253-481c-b44d-f45dc08c8588"}}, {"head": {"id": "27c4a8c7-8679-4b7a-9d5c-a03937f79fbe", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889346036700, "endTime": 146889346187500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c55455f7-53ce-472a-9a7c-cc2298328a8e", "logId": "c9c86eb2-10fb-49fc-b671-422d1b9dc905"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9c86eb2-10fb-49fc-b671-422d1b9dc905", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889346036700, "endTime": 146889346187500}, "additional": {"logType": "info", "children": [], "durationId": "27c4a8c7-8679-4b7a-9d5c-a03937f79fbe"}}, {"head": {"id": "371d0ee7-b81c-4520-b04f-5313c5043803", "name": "BUILD SUCCESSFUL in 2 s 846 ms ", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889346247800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "a3a48520-2eb5-4e2d-8f91-961d85e12301", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146886500559000, "endTime": 146889346724100}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 45, "second": 19}, "completeCommand": "{\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"mode\":\"module\",\"parallel\":true,\"incremental\":true,\"daemon\":true,\"_\":[\"assembleHap\"],\"analyze\":\"normal\"};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "50fb0aa8-7bcd-44c6-90cd-31afdd9d2a31", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889346842200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06e2d865-0bd3-4384-8717-a798eb65ba19", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889346981200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbb8e13f-d410-4fdd-9144-714b45ce0e26", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889347463300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94759e83-d4a5-41d8-a525-b952bec95147", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889347540800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b1a0f3d-ee3c-4ba8-8602-bf1113e8f6c6", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889347584400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6febade2-83da-4bea-bc36-ddf9c05ce4de", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889347613700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3e001f7-e85f-441d-b016-ef367123ce93", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889347647200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03376815-f157-468f-b515-11366af120b5", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889348207400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0bfb13a-a9da-4057-996c-f4242abd6173", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889348433900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ede82808-19e1-4ea8-aa00-d852b327b115", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889348489300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1df042c2-d90e-404a-b06a-3aa651dd8112", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889348520300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69711f92-d34f-4127-b2f7-6b3b02a753f6", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889348550000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8139d003-6f3e-43ca-a5d6-14981b5a7bc2", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889348584000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e6d9493-1f94-4de6-be6e-b6cf44e63325", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889349878400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "273875f7-212f-4f6a-9f01-7ab41e1cfa0d", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889350438900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e8653fa-ed98-47aa-b3b3-ffea6fc2b0cc", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889350773800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd2e7e59-c3e8-49bc-a9e4-832a56c03c78", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889350941400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8be0033d-d1c8-4336-8a57-44bc8afe893b", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889351003500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e92bf26-2a3d-445e-8cd8-2a07be526684", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889351038800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "230fa867-4ec7-46ba-99bf-23879425fc1e", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889351066700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10e98109-12f3-402b-be1a-56c2bf42f080", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889351105600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d4dcc32-58d1-417b-ace3-4f28f13b4300", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889351254400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e424ea9-8fd0-45d3-b778-bcd106510578", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889353273900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bee025e-a938-4eda-aa77-2bd9586504fa", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889353938100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62e97bc1-76f1-4a73-bb48-a97d0b1a5a90", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889354350800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbcd5231-0298-491f-a4e2-2832932f7a6b", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889354624400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0df0180-3876-469d-b414-ced8595c6124", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889354845200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "027f17c7-47c8-41cc-a433-3857a4a129db", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889355544700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae0ce3d6-8611-4c6c-ae31-b0b174851d1b", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889356461200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "427418f8-5379-4462-ab3d-89563244b875", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889356770300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "044ffa54-b905-43db-b6b2-2a81d919ff9f", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889356848200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c613205d-588c-4129-ad71-e39b873922c8", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889356885800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b199acc-51d2-41f1-b6a1-f84ebe311f43", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889356921700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5954bfa5-4552-4a14-95da-594780b323fc", "name": "There is no need to refresh cache, since the incremental task entry:default@SignHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889356951800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b0e5703-cbf8-4ed4-abdb-980c187884f0", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889359267300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c23f16d-44aa-43b1-be56-bc8b72174d8f", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889359544300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d7109a6-e0d1-41e1-8103-c2bfb3c3d616", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889360005500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39ea4285-d725-4dd4-8aea-25a3da865789", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 31680, "tid": "Main Thread", "startTime": 146889360356800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}