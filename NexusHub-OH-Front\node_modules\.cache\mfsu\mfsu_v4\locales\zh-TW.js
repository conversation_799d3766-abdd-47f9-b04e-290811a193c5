"use strict";
import component from "./zh-TW/component";
import globalHeader from "./zh-TW/globalHeader";
import menu from "./zh-TW/menu";
import pwa from "./zh-TW/pwa";
import settingDrawer from "./zh-TW/settingDrawer";
import settings from "./zh-TW/settings";
export default {
  "navBar.lang": "\u8A9E\u8A00",
  "layout.user.link.help": "\u5E6B\u52A9",
  "layout.user.link.privacy": "\u96B1\u79C1",
  "layout.user.link.terms": "\u689D\u6B3E",
  "app.preview.down.block": "\u4E0B\u8F09\u6B64\u9801\u9762\u5230\u672C\u5730\u9805\u76EE",
  ...globalHeader,
  ...menu,
  ...settingDrawer,
  ...settings,
  ...pwa,
  ...component
};
