package api

import (
	"strconv"
	"time"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/internal/services"
	"nexushub-oh-back/pkg/logger"
	"nexushub-oh-back/pkg/storage"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// AppController 应用控制器
type AppController struct {
	DB            *gorm.DB
	StorageClient storage.StorageProvider
	SearchService *services.SearchService
	Validate      *validator.Validate
}

// NewAppController 创建应用控制器
func NewAppController(db *gorm.DB, storageClient storage.StorageProvider, searchService *services.SearchService) *AppController {
	return &AppController{
		DB:            db,
		StorageClient: storageClient,
		SearchService: searchService,
		Validate:      validator.New(),
	}
}

// CreateAppRequest 创建应用请求
type CreateAppRequest struct {
	Name                string `json:"name" validate:"required,min=2,max=100"`
	Package             string `json:"package" validate:"required,min=3,max=100"`
	Description         string `json:"description"`
	ShortDesc           string `json:"short_desc" validate:"max=200"`
	Category            string `json:"category" validate:"required"`
	Icon                string `json:"icon" validate:"required"`
	BannerImage         string `json:"banner_image"`
	MinOpenHarmonyOSVer string `json:"min_open_harmony_os_ver" validate:"required"`
	Tags                string `json:"tags" validate:"max=255"`
	WebsiteURL          string `json:"website_url"`
	PrivacyURL          string `json:"privacy_url"`
}

// UpdateAppRequest 更新应用请求
type UpdateAppRequest struct {
	Name                string   `json:"name" validate:"omitempty,min=2,max=100"`
	Description         string   `json:"description"`
	ShortDesc           string   `json:"short_desc" validate:"max=200"`
	Category            string   `json:"category"`
	Icon                string   `json:"icon"`
	BannerImage         string   `json:"banner_image"`
	MinOpenHarmonyOSVer string   `json:"min_open_harmony_os_ver"`
	Tags                string   `json:"tags" validate:"max=255"`
	WebsiteURL          string   `json:"website_url"`
	PrivacyURL          string   `json:"privacy_url"`
	PackageURL          string   `json:"package_url"`
	Screenshots         []string `json:"screenshots"`
}

// AppVersionRequest 应用版本请求
type AppVersionRequest struct {
	VersionName         string  `json:"version_name" validate:"required,min=1,max=50"`
	VersionCode         float64 `json:"version_code" validate:"required,min=0.1"`
	ChangeLog           string  `json:"change_log"`
	PackageURL          string  `json:"package_url" validate:"required"`
	Size                int64   `json:"size" validate:"required,min=1"`
	MinOpenHarmonyOSVer string  `json:"min_open_harmony_os_ver" validate:"required"`
	IncrementalUpdate   bool    `json:"incremental_update"`
}

// UpdateVersionRequest 更新版本请求
type UpdateVersionRequest struct {
	VersionName         string `json:"version_name" validate:"omitempty,min=1,max=50"`
	ChangeLog           string `json:"change_log"`
	PackageURL          string `json:"package_url"`
	Size                int64  `json:"size" validate:"omitempty,min=1"`
	MinOpenHarmonyOSVer string `json:"min_open_harmony_os_ver"`
	IncrementalUpdate   bool   `json:"incremental_update"`
}

// VersionReviewRequest 版本审核请求
type VersionReviewRequest struct {
	Status string `json:"status" validate:"required,oneof=approved rejected"`
	Reason string `json:"reason"`
}

// AppScreenshotRequest 应用截图请求
type AppScreenshotRequest struct {
	ImageURL  string `json:"image_url" validate:"required"`
	SortOrder int    `json:"sort_order"`
}

// AnonymousDownloadRequest 匿名下载请求
type AnonymousDownloadRequest struct {
	DeviceType  string `json:"device_type"`
	DeviceOS    string `json:"device_os"`
	DeviceModel string `json:"device_model"`
}

// AppDetailsResponse 应用详情响应
type AppDetailsResponse struct {
	ID                  uint      `json:"id"`
	Name                string    `json:"name"`
	Package             string    `json:"package"`
	Description         string    `json:"description"`
	ShortDesc           string    `json:"short_desc"`
	Icon                string    `json:"icon"`
	BannerImage         string    `json:"banner_image"`
	Category            string    `json:"category"`
	DeveloperID         uint      `json:"developer_id"`
	DeveloperName       string    `json:"developer_name"`
	CompanyName         string    `json:"company_name"`
	Website             string    `json:"website"`
	CurrentVersion      string    `json:"current_version"`
	PackageURL          string    `json:"package_url"`
	Status              string    `json:"status"`
	ReleaseDate         time.Time `json:"release_date"`
	Size                int64     `json:"size"`
	DownloadCount       int64     `json:"download_count"`
	AverageRating       float64   `json:"average_rating"`
	RatingCount         int       `json:"rating_count"`
	MinOpenHarmonyOSVer string    `json:"min_open_harmony_os_ver"`
	Tags                string    `json:"tags"`
	WebsiteURL          string    `json:"website_url"`
	PrivacyURL          string    `json:"privacy_url"`
	IsVerified          bool      `json:"is_verified"`
	IsFeatured          bool      `json:"is_featured"`
	IsEditor            bool      `json:"is_editor"`
	IsTop               bool      `json:"is_top"`
	RejectReason        string    `json:"reject_reason"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
}

// AppVersionResponse 应用版本响应
type AppVersionResponse struct {
	ID                  uint      `json:"id"`
	ApplicationID       uint      `json:"application_id"`
	AppName             string    `json:"appName"`
	DeveloperName       string    `json:"developerName"`
	VersionName         string    `json:"versionName"`
	VersionCode         float64   `json:"versionCode"`
	ChangeLog           string    `json:"change_log"`
	UpdateDescription   string    `json:"updateDescription"`
	PackageURL          string    `json:"package_url"`
	Size                int64     `json:"size"`
	FileSize            int64     `json:"fileSize"`
	Status              string    `json:"status"`
	MinOpenHarmonyOSVer string    `json:"min_open_harmony_os_ver"`
	ReleasedAt          time.Time `json:"released_at"`
	DownloadCount       int64     `json:"download_count"`
	IncrementalUpdate   bool      `json:"incremental_update"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
}

// AppScreenshotResponse 应用截图响应
type AppScreenshotResponse struct {
	ID            uint      `json:"id"`
	ApplicationID uint      `json:"application_id"`
	ImageURL      string    `json:"image_url"`
	SortOrder     int       `json:"sort_order"`
	CreatedAt     time.Time `json:"created_at"`
}

// AppReviewRequest 应用审核请求
type AppReviewRequest struct {
	Status string `json:"status" validate:"required,oneof=approved rejected"`
	Reason string `json:"reason" validate:"required_if=Status rejected"`
}

// PagedResponse 分页响应
type PagedResponse struct {
	Total    int64       `json:"total"`     // 总记录数
	Page     int         `json:"page"`      // 当前页码
	PageSize int         `json:"page_size"` // 每页数量
	Data     interface{} `json:"data"`      // 数据
}

// CreateApp 创建应用
//
//	@Summary		创建应用
//	@Description	开发者创建新应用
//	@Tags			应用
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			data	body		CreateAppRequest					true	"应用信息"
//	@Success		200		{object}	Response{data=AppDetailsResponse}	"创建成功，返回应用详情"
//	@Failure		400		{object}	Response							"参数错误/包名已被使用"
//	@Failure		401		{object}	Response							"未授权"
//	@Failure		403		{object}	Response							"非开发者或开发者未通过审核"
//	@Failure		500		{object}	Response							"服务器错误"
//	@Router			/apps [post]
func (c *AppController) CreateApp(ctx *gin.Context) {
	var req CreateAppRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		ParamError(ctx, err.Error())
		return
	}

	// 获取开发者ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "")
		return
	}

	// 检查用户是否为开发者
	var user models.User
	if err := c.DB.First(&user, userID).Error; err != nil {
		ServerError(ctx, "获取用户信息失败", err)
		return
	}

	// 获取用户角色
	userRole, exists := ctx.Get("role")
	if !exists {
		ServerError(ctx, "获取用户角色失败", nil)
		return
	}

	// 管理员可以直接创建应用，开发者需要通过审核
	if userRole.(string) != string(models.UserRoleAdmin) {
		if !user.IsDeveloper || user.VerifyStatus != models.VerifyStatusApproved {
			Forbidden(ctx, "非开发者或开发者未通过审核，无法发布应用")
			return
		}
	}

	// 检查包名是否已存在
	var count int64
	c.DB.Model(&models.Application{}).Where("package = ?", req.Package).Count(&count)
	if count > 0 {
		Fail(ctx, 400, "包名已被使用")
		return
	}

	// 根据用户角色设置应用状态
	var appStatus models.ApplicationStatus
	if userRole.(string) == string(models.UserRoleAdmin) {
		// 管理员创建的应用直接审核通过
		appStatus = models.ApplicationStatusApproved
	} else {
		// 开发者创建的应用为草稿状态
		appStatus = models.ApplicationStatusDraft
	}

	// 创建应用
	app := &models.Application{
		Name:                req.Name,
		Package:             req.Package,
		Description:         req.Description,
		ShortDesc:           req.ShortDesc,
		Icon:                req.Icon,
		BannerImage:         req.BannerImage,
		Category:            req.Category,
		DeveloperID:         user.ID,
		Status:              appStatus,
		MinOpenHarmonyOSVer: req.MinOpenHarmonyOSVer,
		Tags:                req.Tags,
		WebsiteURL:          req.WebsiteURL,
		PrivacyURL:          req.PrivacyURL,
	}

	// 如果是管理员创建，设置审核信息
	if userRole.(string) == string(models.UserRoleAdmin) {
		now := time.Now()
		app.ApprovedAt = &now
		userIDValue := userID.(uint)
		app.ReviewerID = &userIDValue
	} else {
		// 开发者创建的应用，ReviewerID设置为nil
		app.ReviewerID = nil
	}

	if err := models.CreateApplication(c.DB, app); err != nil {
		logger.Error("创建应用失败", zap.Error(err))
		ServerError(ctx, "创建应用失败，请稍后重试", err)
		return
	}

	// 同步搜索索引（仅对已审核通过的应用）
	if c.SearchService != nil && app.Status == models.ApplicationStatusApproved {
		if err := c.SearchService.IndexApp(ctx, app); err != nil {
			logger.Warn("添加应用到搜索索引失败", zap.Error(err), zap.Uint("app_id", app.ID))
		}
	}

	SuccessWithMessage(ctx, "创建应用成功", AppDetailsResponse{
		ID:                  app.ID,
		Name:                app.Name,
		Package:             app.Package,
		Description:         app.Description,
		ShortDesc:           app.ShortDesc,
		Icon:                app.Icon,
		BannerImage:         app.BannerImage,
		Category:            app.Category,
		DeveloperID:         app.DeveloperID,
		Status:              string(app.Status),
		MinOpenHarmonyOSVer: app.MinOpenHarmonyOSVer,
		Tags:                app.Tags,
		WebsiteURL:          app.WebsiteURL,
		PrivacyURL:          app.PrivacyURL,
		CreatedAt:           app.CreatedAt,
		UpdatedAt:           app.UpdatedAt,
	})
}

// UpdateApp 更新应用
//
//	@Summary		更新应用
//	@Description	开发者更新应用信息
//	@Tags			应用
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id		path		int									true	"应用ID"
//	@Param			data	body		UpdateAppRequest					true	"更新的应用信息"
//	@Success		200		{object}	Response{data=AppDetailsResponse}	"更新成功，返回应用详情"
//	@Failure		400		{object}	Response							"参数错误"
//	@Failure		401		{object}	Response							"未授权"
//	@Failure		403		{object}	Response							"非应用的开发者"
//	@Failure		404		{object}	Response							"应用不存在"
//	@Failure		500		{object}	Response							"服务器错误"
//	@Router			/apps/{id} [put]
func (c *AppController) UpdateApp(ctx *gin.Context) {
	appID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		ParamError(ctx, "无效的应用ID")
		return
	}

	var req UpdateAppRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		ParamError(ctx, err.Error())
		return
	}

	// 获取开发者ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "")
		return
	}

	// 获取应用
	var app models.Application
	if err := c.DB.First(&app, appID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "应用不存在")
		} else {
			ServerError(ctx, "获取应用信息失败", err)
		}
		return
	}

	// 检查是否为应用的开发者
	if app.DeveloperID != userID.(uint) {
		Forbidden(ctx, "你不是此应用的开发者")
		return
	}

	// 更新应用信息
	if req.Name != "" {
		app.Name = req.Name
	}
	if req.Description != "" {
		app.Description = req.Description
	}
	if req.ShortDesc != "" {
		app.ShortDesc = req.ShortDesc
	}
	if req.Category != "" {
		app.Category = req.Category
	}
	if req.Icon != "" {
		app.Icon = req.Icon
	}
	if req.BannerImage != "" {
		app.BannerImage = req.BannerImage
	}
	if req.MinOpenHarmonyOSVer != "" {
		app.MinOpenHarmonyOSVer = req.MinOpenHarmonyOSVer
	}
	if req.Tags != "" {
		app.Tags = req.Tags
	}
	if req.WebsiteURL != "" {
		app.WebsiteURL = req.WebsiteURL
	}
	if req.PrivacyURL != "" {
		app.PrivacyURL = req.PrivacyURL
	}
	if req.PackageURL != "" {
		app.PackageURL = req.PackageURL

		// 当提供了新的安装包URL时，自动创建一个新版本
		version := models.AppVersion{
			ApplicationID:       app.ID,
			VersionName:         "1.0.0", // 默认版本名
			VersionCode:         1.0,     // 默认版本号
			ChangeLog:           "应用更新",
			PackageURL:          req.PackageURL,
			Size:                0,
			Status:              models.ApplicationStatusPending,
			MinOpenHarmonyOSVer: app.MinOpenHarmonyOSVer,
			IncrementalUpdate:   false,
		}

		// 检查是否已存在相同版本号的版本
		var existingVersion models.AppVersion
		if err := c.DB.Where("application_id = ? AND version_code = ?", app.ID, version.VersionCode).First(&existingVersion).Error; err != nil {
			// 如果不存在，则创建新版本
			if err == gorm.ErrRecordNotFound {
				if err := models.AddAppVersion(c.DB, &version); err != nil {
					logger.Error("创建应用版本失败", zap.Error(err))
				}
			}
		} else {
			// 如果已存在，更新现有版本的PackageURL
			existingVersion.PackageURL = req.PackageURL
			if err := c.DB.Save(&existingVersion).Error; err != nil {
				logger.Error("更新应用版本失败", zap.Error(err))
			}
		}
	}

	// 处理截图更新
	if len(req.Screenshots) > 0 {
		// 删除现有截图
		if err := c.DB.Where("application_id = ?", app.ID).Delete(&models.AppScreenshot{}).Error; err != nil {
			logger.Error("删除现有截图失败", zap.Error(err))
		}
		// 添加新截图
		for i, screenshotURL := range req.Screenshots {
			screenshot := models.AppScreenshot{
				ApplicationID: app.ID,
				ImageURL:      screenshotURL,
				SortOrder:     i + 1,
			}
			if err := c.DB.Create(&screenshot).Error; err != nil {
				logger.Error("创建截图记录失败", zap.Error(err))
			}
		}
	}

	if err := models.UpdateApplication(c.DB, &app); err != nil {
		logger.Error("更新应用失败", zap.Error(err))
		ServerError(ctx, "更新应用失败，请稍后重试", err)
		return
	}

	// 同步搜索索引（仅当应用已审核通过时）
	if c.SearchService != nil && app.Status == models.ApplicationStatusApproved {
		if err := c.SearchService.IndexApp(ctx, &app); err != nil {
			logger.Warn("更新搜索索引失败", zap.Error(err), zap.Uint("app_id", app.ID))
		}
	}

	SuccessWithMessage(ctx, "更新应用成功", AppDetailsResponse{
		ID:                  app.ID,
		Name:                app.Name,
		Package:             app.Package,
		Description:         app.Description,
		ShortDesc:           app.ShortDesc,
		Icon:                app.Icon,
		BannerImage:         app.BannerImage,
		Category:            app.Category,
		DeveloperID:         app.DeveloperID,
		CurrentVersion:      app.CurrentVersion,
		PackageURL:          app.PackageURL,
		Status:              string(app.Status),
		DownloadCount:       app.DownloadCount,
		AverageRating:       app.AverageRating,
		RatingCount:         app.RatingCount,
		MinOpenHarmonyOSVer: app.MinOpenHarmonyOSVer,
		Tags:                app.Tags,
		WebsiteURL:          app.WebsiteURL,
		PrivacyURL:          app.PrivacyURL,
		IsVerified:          app.IsVerified,
		IsFeatured:          app.IsFeatured,
		IsEditor:            app.IsEditor,
		IsTop:               app.IsTop,
		CreatedAt:           app.CreatedAt,
		UpdatedAt:           app.UpdatedAt,
	})
}

// GetApp 获取应用详情
//
//	@Summary		获取应用详情
//	@Description	获取应用的详细信息，包括截图和版本
//	@Tags			应用
//	@Accept			json
//	@Produce		json
//	@Param			id	path		int										true	"应用ID"
//	@Success		200	{object}	Response{data=map[string]interface{}}	"返回应用详情，包括应用基本信息、截图和版本"
//	@Failure		404	{object}	Response								"应用不存在"
//	@Failure		500	{object}	Response								"服务器错误"
//	@Router			/apps/{id} [get]
func (c *AppController) GetApp(ctx *gin.Context) {
	appID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		ParamError(ctx, "无效的应用ID")
		return
	}

	// 获取应用
	app, err := models.GetApplicationByID(c.DB, uint(appID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "应用不存在")
		} else {
			ServerError(ctx, "获取应用信息失败", err)
		}
		return
	}

	// 获取应用截图
	screenshots, err := models.GetAppScreenshots(c.DB, app.ID)
	if err != nil {
		logger.Error("获取应用截图失败", zap.Error(err))
		screenshots = []models.AppScreenshot{}
	}

	// 获取应用版本
	versions, err := models.GetAppVersions(c.DB, app.ID)
	if err != nil {
		logger.Error("获取应用版本失败", zap.Error(err))
		versions = []models.AppVersion{}
	}

	// 转换截图为响应格式
	screenshotResp := make([]AppScreenshotResponse, 0, len(screenshots))
	for _, s := range screenshots {
		screenshotResp = append(screenshotResp, AppScreenshotResponse{
			ID:            s.ID,
			ApplicationID: s.ApplicationID,
			ImageURL:      s.ImageURL,
			SortOrder:     s.SortOrder,
			CreatedAt:     s.CreatedAt,
		})
	}

	// 转换版本为响应格式
	versionResp := make([]AppVersionResponse, 0, len(versions))
	for _, v := range versions {
		var releasedAt time.Time
		if v.ReleasedAt != nil {
			releasedAt = *v.ReleasedAt
		}
		// 获取应用名称和开发者名称
		appName := ""
		developerName := ""
		if v.Application.ID != 0 {
			appName = v.Application.Name
			if v.Application.Developer.ID != 0 {
				developerName = v.Application.Developer.Username
			}
		}

		versionResp = append(versionResp, AppVersionResponse{
			ID:                  v.ID,
			ApplicationID:       v.ApplicationID,
			AppName:             appName,
			DeveloperName:       developerName,
			VersionName:         v.VersionName,
			VersionCode:         v.VersionCode,
			ChangeLog:           v.ChangeLog,
			UpdateDescription:   v.ChangeLog, // 使用ChangeLog作为UpdateDescription
			PackageURL:          v.PackageURL,
			Size:                v.Size,
			FileSize:            v.Size, // 使用Size作为FileSize
			Status:              string(v.Status),
			MinOpenHarmonyOSVer: v.MinOpenHarmonyOSVer,
			ReleasedAt:          releasedAt,
			DownloadCount:       v.DownloadCount,
			IncrementalUpdate:   v.IncrementalUpdate,
			CreatedAt:           v.CreatedAt,
			UpdatedAt:           v.UpdatedAt,
		})
	}

	var releaseDate time.Time
	if app.ReleaseDate != nil {
		releaseDate = *app.ReleaseDate
	}

	Success(ctx, gin.H{
		"app": AppDetailsResponse{
			ID:                  app.ID,
			Name:                app.Name,
			Package:             app.Package,
			Description:         app.Description,
			ShortDesc:           app.ShortDesc,
			Icon:                app.Icon,
			BannerImage:         app.BannerImage,
			Category:            app.Category,
			DeveloperID:         app.DeveloperID,
			DeveloperName:       app.Developer.DeveloperName,
			CompanyName:         app.Developer.CompanyName,
			Website:             app.Developer.Website,
			CurrentVersion:      app.CurrentVersion,
			PackageURL:          app.PackageURL,
			Status:              string(app.Status),
			ReleaseDate:         releaseDate,
			Size:                app.Size,
			DownloadCount:       app.DownloadCount,
			AverageRating:       app.AverageRating,
			RatingCount:         app.RatingCount,
			MinOpenHarmonyOSVer: app.MinOpenHarmonyOSVer,
			Tags:                app.Tags,
			WebsiteURL:          app.WebsiteURL,
			PrivacyURL:          app.PrivacyURL,
			IsVerified:          app.IsVerified,
			IsFeatured:          app.IsFeatured,
			IsEditor:            app.IsEditor,
			IsTop:               app.IsTop,
			CreatedAt:           app.CreatedAt,
			UpdatedAt:           app.UpdatedAt,
		},
		"screenshots": screenshotResp,
		"versions":    versionResp,
	})
}

// ListApps 获取应用列表
//
//	@Summary		获取应用列表
//	@Description	获取应用列表，支持分页和筛选。普通用户只能看到已审核通过的应用，管理员可以看到所有状态的应用
//	@Tags			应用
//	@Accept			json
//	@Produce		json
//	@Param			page		query		int										false	"页码，默认1"	default(1)
//	@Param			page_size	query		int										false	"每页数量，默认20"	default(20)
//	@Param			category	query		string									false	"分类筛选"
//	@Param			keyword		query		string									false	"搜索关键字"
//	@Param			status		query		string									false	"状态筛选（仅管理员可用）"
//	@Success		200			{object}	PageResponse{data=[]AppDetailsResponse}	"返回应用列表"
//	@Failure		400			{object}	Response								"参数错误"
//	@Failure		500			{object}	Response								"服务器错误"
//	@Router			/apps [get]
func (c *AppController) ListApps(ctx *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	category := ctx.Query("category")
	keyword := ctx.Query("keyword")
	status := ctx.Query("status")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	// 检查用户角色
	userRole, _ := ctx.Get("role")
	isAdmin := userRole == "admin"

	// 添加调试日志
	logger.Info("应用列表查询", zap.Any("userRole", userRole), zap.Bool("isAdmin", isAdmin), zap.String("status", status))

	// 构建查询
	query := c.DB.Model(&models.Application{})

	// 如果不是管理员，只显示已审核通过的应用
	if !isAdmin {
		logger.Info("非管理员用户，只显示已审核通过的应用")
		query = query.Where("status = ?", models.ApplicationStatusApproved)
	} else if status != "" {
		// 管理员可以按状态筛选
		logger.Info("管理员按状态筛选", zap.String("status", status))
		query = query.Where("status = ?", status)
	} else {
		logger.Info("管理员查看所有状态的应用")
	}
	// 注意：如果是管理员且status为空，则不添加状态过滤，显示所有状态的应用
	if category != "" {
		query = query.Where("category = ?", category)
	}

	if keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Error("获取应用总数失败", zap.Error(err))
		ServerError(ctx, "获取应用列表失败", err)
		return
	}

	// 获取应用列表
	var apps []models.Application
	if err := query.Preload("Developer").Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&apps).Error; err != nil {
		logger.Error("获取应用列表失败", zap.Error(err))
		ServerError(ctx, "获取应用列表失败", err)
		return
	}

	// 构建返回结果
	result := make([]AppDetailsResponse, 0, len(apps))
	for _, app := range apps {
		var releaseDate time.Time
		if app.ReleaseDate != nil {
			releaseDate = *app.ReleaseDate
		}

		result = append(result, AppDetailsResponse{
			ID:                  app.ID,
			Name:                app.Name,
			Package:             app.Package,
			ShortDesc:           app.ShortDesc,
			Description:         app.Description,
			Icon:                app.Icon,
			Category:            app.Category,
			DeveloperID:         app.DeveloperID,
			DeveloperName:       app.Developer.DeveloperName,
			CurrentVersion:      app.CurrentVersion,
			ReleaseDate:         releaseDate,
			Size:                app.Size,
			DownloadCount:       app.DownloadCount,
			AverageRating:       app.AverageRating,
			RatingCount:         app.RatingCount,
			MinOpenHarmonyOSVer: app.MinOpenHarmonyOSVer,
			Tags:                app.Tags,
			IsVerified:          app.IsVerified,
			IsFeatured:          app.IsFeatured,
			IsEditor:            app.IsEditor,
			IsTop:               app.IsTop,
			RejectReason:        app.RejectReason,
			Status:              string(app.Status),
			CreatedAt:           app.CreatedAt,
			UpdatedAt:           app.UpdatedAt,
		})
	}

	// 返回分页结果
	SuccessWithPage(ctx, result, total, page, pageSize)
}

// ListDeveloperApps 获取开发者应用列表
//
//	@Summary		获取开发者应用列表
//	@Description	开发者获取自己提交的所有应用（包括各种状态）
//	@Tags			应用
//	@Accept			json
//	@Produce		json
//	@Param			page		query		int										false	"页码，默认1"	default(1)
//	@Param			page_size	query		int										false	"每页数量，默认20"	default(20)
//	@Param			status		query		string									false	"应用状态筛选"
//	@Param			keyword		query		string									false	"搜索关键字"
//	@Success		200			{object}	PageResponse{data=[]AppDetailsResponse}	"返回开发者应用列表"
//	@Failure		400			{object}	Response								"参数错误"
//	@Failure		500			{object}	Response								"服务器错误"
//	@Security		ApiKeyAuth
//	@Router			/developer/apps [get]
func (c *AppController) ListDeveloperApps(ctx *gin.Context) {
	// 获取当前用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "用户未登录")
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	status := ctx.Query("status")
	keyword := ctx.Query("keyword")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	// 构建查询 - 只查询当前开发者的应用
	query := c.DB.Model(&models.Application{}).Where("developer_id = ?", userID)

	if status != "" {
		query = query.Where("status = ?", status)
	}

	if keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Error("获取开发者应用总数失败", zap.Error(err))
		ServerError(ctx, "获取应用列表失败", err)
		return
	}

	// 获取应用列表
	var apps []models.Application
	if err := query.Preload("Developer").Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&apps).Error; err != nil {
		logger.Error("获取开发者应用列表失败", zap.Error(err))
		ServerError(ctx, "获取应用列表失败", err)
		return
	}

	// 构建返回结果
	result := make([]AppDetailsResponse, 0, len(apps))
	for _, app := range apps {
		var releaseDate time.Time
		if app.ReleaseDate != nil {
			releaseDate = *app.ReleaseDate
		}

		result = append(result, AppDetailsResponse{
			ID:                  app.ID,
			Name:                app.Name,
			Package:             app.Package,
			ShortDesc:           app.ShortDesc,
			Description:         app.Description,
			Icon:                app.Icon,
			Category:            app.Category,
			DeveloperID:         app.DeveloperID,
			DeveloperName:       app.Developer.DeveloperName,
			CurrentVersion:      app.CurrentVersion,
			ReleaseDate:         releaseDate,
			Size:                app.Size,
			DownloadCount:       app.DownloadCount,
			AverageRating:       app.AverageRating,
			RatingCount:         app.RatingCount,
			MinOpenHarmonyOSVer: app.MinOpenHarmonyOSVer,
			Tags:                app.Tags,
			IsVerified:          app.IsVerified,
			IsFeatured:          app.IsFeatured,
			IsEditor:            app.IsEditor,
			IsTop:               app.IsTop,
			Status:              string(app.Status),
			RejectReason:        app.RejectReason,
			CreatedAt:           app.CreatedAt,
			UpdatedAt:           app.UpdatedAt,
		})
	}

	// 返回分页结果
	SuccessWithPage(ctx, result, total, page, pageSize)
}

// ListPendingApps 获取待审核应用列表
//
//	@Summary		获取待审核应用列表
//	@Description	获取所有待审核的应用
//	@Tags			审核员
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			page	query		int								false	"页码，默认1"
//	@Param			limit	query		int								false	"每页数量，默认20"
//	@Success		200		{object}	Response{data=PagedResponse}	"返回待审核应用列表"
//	@Failure		401		{object}	Response						"未授权"
//	@Failure		403		{object}	Response						"权限不足"
//	@Failure		500		{object}	Response						"服务器错误"
//	@Router			/reviewer/apps/pending [get]
func (c *AppController) ListPendingApps(ctx *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// 获取待审核的应用
	var apps []models.Application
	var count int64

	// 构建查询条件：待审核的应用
	query := c.DB.Model(&models.Application{}).Where("status = ?", models.ApplicationStatusPending)

	// 计算总数
	if err := query.Count(&count).Error; err != nil {
		logger.Error("查询待审核应用数量失败", zap.Error(err))
		ServerError(ctx, "获取待审核应用列表失败", err)
		return
	}

	// 分页查询
	offset := (page - 1) * limit
	if err := query.Offset(offset).Limit(limit).Find(&apps).Error; err != nil {
		logger.Error("查询待审核应用失败", zap.Error(err))
		ServerError(ctx, "获取待审核应用列表失败", err)
		return
	}

	// 返回结果
	Success(ctx, PagedResponse{
		Total:    count,
		Page:     page,
		PageSize: limit,
		Data:     apps,
	})
}

// ReviewApp 审核应用
//
//	@Summary		审核应用
//	@Description	审核员审核应用
//	@Tags			审核员
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id		path		int					true	"应用ID"
//	@Param			data	body		AppReviewRequest	true	"审核信息"
//	@Success		200		{object}	Response			"审核成功"
//	@Failure		400		{object}	Response			"参数错误"
//	@Failure		401		{object}	Response			"未授权"
//	@Failure		403		{object}	Response			"权限不足"
//	@Failure		404		{object}	Response			"应用不存在"
//	@Failure		500		{object}	Response			"服务器错误"
//	@Router			/reviewer/apps/{id}/review [post]
func (c *AppController) ReviewApp(ctx *gin.Context) {
	// 获取应用ID
	appID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		ParamError(ctx, "无效的应用ID")
		return
	}

	var req AppReviewRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		ParamError(ctx, err.Error())
		return
	}

	// 查询应用
	var app models.Application
	if err := c.DB.First(&app, appID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "应用不存在")
		} else {
			logger.Error("查询应用失败", zap.Error(err))
			ServerError(ctx, "审核应用失败", err)
		}
		return
	}

	// 检查应用是否待审核状态
	if app.Status != models.ApplicationStatusPending {
		Fail(ctx, 400, "只能审核处于待审核状态的应用")
		return
	}

	// 获取审核员ID
	reviewerID, _ := ctx.Get("user_id")

	// 更新应用状态
	switch req.Status {
	case "approved":
		app.Status = models.ApplicationStatusApproved
		now := time.Now()
		app.ApprovedAt = &now
		reviewerIDValue := reviewerID.(uint)
		app.ReviewerID = &reviewerIDValue
	case "rejected":
		app.Status = models.ApplicationStatusRejected
		now := time.Now()
		app.RejectedAt = &now
		reviewerIDValue := reviewerID.(uint)
		app.ReviewerID = &reviewerIDValue
		app.RejectReason = req.Reason
	default:
		ParamError(ctx, "无效的审核状态")
		return
	}

	// 保存应用
	if err := c.DB.Save(&app).Error; err != nil {
		logger.Error("保存应用失败", zap.Error(err))
		ServerError(ctx, "审核应用失败", err)
		return
	}

	// 同步搜索索引
	if c.SearchService != nil {
		switch req.Status {
		case "approved":
			// 审核通过，添加到搜索索引
			if err := c.SearchService.IndexApp(ctx, &app); err != nil {
				logger.Warn("添加应用到搜索索引失败", zap.Error(err), zap.Uint("app_id", app.ID))
			}
		case "rejected":
			// 审核拒绝，从搜索索引中删除
			if err := c.SearchService.DeleteApp(ctx, app.ID); err != nil {
				logger.Warn("从搜索索引删除应用失败", zap.Error(err), zap.Uint("app_id", app.ID))
			}
		}
	}

	SuccessWithMessage(ctx, "审核成功", nil)
}

// CreateAppVersion 创建应用版本
//
//	@Summary		创建应用版本
//	@Description	开发者创建新的应用版本
//	@Tags			应用版本
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id		path		int					true	"应用ID"
//	@Param			data	body		AppVersionRequest	true	"版本信息"
//	@Success		200		{object}	Response			"创建成功"
//	@Failure		400		{object}	Response			"参数错误"
//	@Failure		401		{object}	Response			"未授权"
//	@Failure		403		{object}	Response			"权限不足"
//	@Failure		404		{object}	Response			"应用不存在"
//	@Failure		500		{object}	Response			"服务器错误"
//	@Router			/apps/{id}/versions [post]
func (c *AppController) CreateAppVersion(ctx *gin.Context) {
	// 获取应用ID
	appID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		ParamError(ctx, "无效的应用ID")
		return
	}

	var req AppVersionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		ParamError(ctx, err.Error())
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "用户未登录")
		return
	}

	// 查询应用
	var app models.Application
	if err := c.DB.First(&app, appID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "应用不存在")
		} else {
			logger.Error("查询应用失败", zap.Error(err))
			ServerError(ctx, "创建版本失败", err)
		}
		return
	}

	// 检查权限（只有应用的开发者可以创建版本）
	if app.DeveloperID != userID.(uint) {
		Forbidden(ctx, "无权限操作此应用")
		return
	}

	// 检查版本号是否已存在
	var existingVersion models.AppVersion
	if err := c.DB.Where("application_id = ? AND version_code = ?", appID, req.VersionCode).First(&existingVersion).Error; err == nil {
		Fail(ctx, 400, "版本号已存在")
		return
	}

	// 创建版本
	version := models.AppVersion{
		ApplicationID:       uint(appID),
		VersionName:         req.VersionName,
		VersionCode:         req.VersionCode,
		ChangeLog:           req.ChangeLog,
		PackageURL:          req.PackageURL,
		Size:                req.Size,
		Status:              models.ApplicationStatusPending,
		MinOpenHarmonyOSVer: req.MinOpenHarmonyOSVer,
		IncrementalUpdate:   req.IncrementalUpdate,
	}

	if err := models.AddAppVersion(c.DB, &version); err != nil {
		logger.Error("创建应用版本失败", zap.Error(err))
		ServerError(ctx, "创建版本失败", err)
		return
	}

	SuccessWithMessage(ctx, "版本创建成功，等待审核", gin.H{"version_id": version.ID})
}

// GetAppVersions 获取应用版本列表
//
//	@Summary		获取应用版本列表
//	@Description	获取指定应用的版本列表
//	@Tags			应用版本
//	@Accept			json
//	@Produce		json
//	@Param			id		path		int			true	"应用ID"
//	@Param			status	query		string		false	"版本状态筛选"
//	@Success		200		{object}	Response	"获取成功"
//	@Failure		400		{object}	Response	"参数错误"
//	@Failure		404		{object}	Response	"应用不存在"
//	@Failure		500		{object}	Response	"服务器错误"
//	@Router			/apps/{id}/versions [get]
func (c *AppController) GetAppVersions(ctx *gin.Context) {
	// 获取应用ID
	appID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		ParamError(ctx, "无效的应用ID")
		return
	}

	// 查询应用是否存在
	var app models.Application
	if err := c.DB.First(&app, appID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "应用不存在")
		} else {
			logger.Error("查询应用失败", zap.Error(err))
			ServerError(ctx, "获取版本列表失败", err)
		}
		return
	}

	// 获取状态筛选参数
	status := ctx.Query("status")
	var versions []models.AppVersion

	if status != "" {
		// 根据状态筛选
		appStatus := models.ApplicationStatus(status)
		versions, err = models.GetAppVersionsByStatus(c.DB, uint(appID), appStatus)
	} else {
		// 获取所有版本
		versions, err = models.GetAppVersions(c.DB, uint(appID))
	}

	if err != nil {
		logger.Error("获取应用版本失败", zap.Error(err))
		ServerError(ctx, "获取版本列表失败", err)
		return
	}

	// 转换为响应格式
	versionResp := make([]AppVersionResponse, 0, len(versions))
	for _, v := range versions {
		var releasedAt time.Time
		if v.ReleasedAt != nil {
			releasedAt = *v.ReleasedAt
		}

		// 获取应用名称和开发者名称
		appName := ""
		developerName := ""
		if v.Application.Name != "" {
			appName = v.Application.Name
		}
		if v.Application.Developer.Username != "" {
			developerName = v.Application.Developer.Username
		}

		versionResp = append(versionResp, AppVersionResponse{
			ID:                  v.ID,
			ApplicationID:       v.ApplicationID,
			AppName:             appName,
			DeveloperName:       developerName,
			VersionName:         v.VersionName,
			VersionCode:         v.VersionCode,
			ChangeLog:           v.ChangeLog,
			UpdateDescription:   v.ChangeLog,
			PackageURL:          v.PackageURL,
			Size:                v.Size,
			FileSize:            v.Size,
			Status:              string(v.Status),
			MinOpenHarmonyOSVer: v.MinOpenHarmonyOSVer,
			ReleasedAt:          releasedAt,
			DownloadCount:       v.DownloadCount,
			IncrementalUpdate:   v.IncrementalUpdate,
			CreatedAt:           v.CreatedAt,
			UpdatedAt:           v.UpdatedAt,
		})
	}

	Success(ctx, gin.H{"versions": versionResp})
}

// GetAppVersion 获取应用版本详情
//
//	@Summary		获取应用版本详情
//	@Description	获取指定应用版本的详细信息
//	@Tags			应用版本
//	@Accept			json
//	@Produce		json
//	@Param			id			path		int			true	"应用ID"
//	@Param			version_id	path		int			true	"版本ID"
//	@Success		200			{object}	Response	"获取成功"
//	@Failure		400			{object}	Response	"参数错误"
//	@Failure		404			{object}	Response	"版本不存在"
//	@Failure		500			{object}	Response	"服务器错误"
//	@Router			/apps/{id}/versions/{version_id} [get]
func (c *AppController) GetAppVersion(ctx *gin.Context) {
	// 获取版本ID
	versionID, err := strconv.ParseUint(ctx.Param("version_id"), 10, 64)
	if err != nil {
		ParamError(ctx, "无效的版本ID")
		return
	}

	// 查询版本
	version, err := models.GetAppVersionByID(c.DB, uint(versionID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "版本不存在")
		} else {
			logger.Error("查询版本失败", zap.Error(err))
			ServerError(ctx, "获取版本详情失败", err)
		}
		return
	}

	// 转换为响应格式
	var releasedAt time.Time
	if version.ReleasedAt != nil {
		releasedAt = *version.ReleasedAt
	}

	versionResp := AppVersionResponse{
		ID:                  version.ID,
		ApplicationID:       version.ApplicationID,
		VersionName:         version.VersionName,
		VersionCode:         version.VersionCode,
		ChangeLog:           version.ChangeLog,
		PackageURL:          version.PackageURL,
		Size:                version.Size,
		Status:              string(version.Status),
		MinOpenHarmonyOSVer: version.MinOpenHarmonyOSVer,
		ReleasedAt:          releasedAt,
		DownloadCount:       version.DownloadCount,
		IncrementalUpdate:   version.IncrementalUpdate,
		CreatedAt:           version.CreatedAt,
		UpdatedAt:           version.UpdatedAt,
	}

	Success(ctx, gin.H{"version": versionResp})
}

// UpdateAppVersion 更新应用版本
//
//	@Summary		更新应用版本
//	@Description	开发者更新应用版本信息
//	@Tags			应用版本
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id			path		int						true	"应用ID"
//	@Param			version_id	path		int						true	"版本ID"
//	@Param			data		body		UpdateVersionRequest	true	"版本信息"
//	@Success		200			{object}	Response				"更新成功"
//	@Failure		400			{object}	Response				"参数错误"
//	@Failure		401			{object}	Response				"未授权"
//	@Failure		403			{object}	Response				"权限不足"
//	@Failure		404			{object}	Response				"版本不存在"
//	@Failure		500			{object}	Response				"服务器错误"
//	@Router			/apps/{id}/versions/{version_id} [put]
func (c *AppController) UpdateAppVersion(ctx *gin.Context) {
	// 获取版本ID
	versionID, err := strconv.ParseUint(ctx.Param("version_id"), 10, 64)
	if err != nil {
		ParamError(ctx, "无效的版本ID")
		return
	}

	var req UpdateVersionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		ParamError(ctx, err.Error())
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "用户未登录")
		return
	}

	// 查询版本
	version, err := models.GetAppVersionByID(c.DB, uint(versionID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "版本不存在")
		} else {
			logger.Error("查询版本失败", zap.Error(err))
			ServerError(ctx, "更新版本失败", err)
		}
		return
	}

	// 查询应用，检查权限
	var app models.Application
	if err := c.DB.First(&app, version.ApplicationID).Error; err != nil {
		logger.Error("查询应用失败", zap.Error(err))
		ServerError(ctx, "更新版本失败", err)
		return
	}

	// 检查权限
	if app.DeveloperID != userID.(uint) {
		Forbidden(ctx, "无权限操作此版本")
		return
	}

	// 检查版本状态（只能更新草稿或被拒绝的版本）
	if version.Status != models.ApplicationStatusDraft && version.Status != models.ApplicationStatusRejected {
		Fail(ctx, 400, "只能更新草稿或被拒绝的版本")
		return
	}

	// 更新版本信息
	if req.VersionName != "" {
		version.VersionName = req.VersionName
	}
	if req.ChangeLog != "" {
		version.ChangeLog = req.ChangeLog
	}
	if req.PackageURL != "" {
		version.PackageURL = req.PackageURL
	}
	if req.Size > 0 {
		version.Size = req.Size
	}
	if req.MinOpenHarmonyOSVer != "" {
		version.MinOpenHarmonyOSVer = req.MinOpenHarmonyOSVer
	}
	version.IncrementalUpdate = req.IncrementalUpdate

	if err := models.UpdateAppVersion(c.DB, version); err != nil {
		logger.Error("更新应用版本失败", zap.Error(err))
		ServerError(ctx, "更新版本失败", err)
		return
	}

	SuccessWithMessage(ctx, "版本更新成功", nil)
}

// DeleteAppVersion 删除应用版本
//
//	@Summary		删除应用版本
//	@Description	开发者删除应用版本
//	@Tags			应用版本
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id			path		int			true	"应用ID"
//	@Param			version_id	path		int			true	"版本ID"
//	@Success		200			{object}	Response	"删除成功"
//	@Failure		400			{object}	Response	"参数错误"
//	@Failure		401			{object}	Response	"未授权"
//	@Failure		403			{object}	Response	"权限不足"
//	@Failure		404			{object}	Response	"版本不存在"
//	@Failure		500			{object}	Response	"服务器错误"
//	@Router			/apps/{id}/versions/{version_id} [delete]
func (c *AppController) DeleteAppVersion(ctx *gin.Context) {
	// 获取版本ID
	versionID, err := strconv.ParseUint(ctx.Param("version_id"), 10, 64)
	if err != nil {
		ParamError(ctx, "无效的版本ID")
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "用户未登录")
		return
	}

	// 查询版本
	version, err := models.GetAppVersionByID(c.DB, uint(versionID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "版本不存在")
		} else {
			logger.Error("查询版本失败", zap.Error(err))
			ServerError(ctx, "删除版本失败", err)
		}
		return
	}

	// 查询应用，检查权限
	var app models.Application
	if err := c.DB.First(&app, version.ApplicationID).Error; err != nil {
		logger.Error("查询应用失败", zap.Error(err))
		ServerError(ctx, "删除版本失败", err)
		return
	}

	// 检查权限
	if app.DeveloperID != userID.(uint) {
		Forbidden(ctx, "无权限操作此版本")
		return
	}

	// 检查版本状态（不能删除已发布的版本）
	if version.Status == models.ApplicationStatusApproved {
		Fail(ctx, 400, "不能删除已发布的版本")
		return
	}

	if err := models.DeleteAppVersion(c.DB, uint(versionID)); err != nil {
		logger.Error("删除应用版本失败", zap.Error(err))
		ServerError(ctx, "删除版本失败", err)
		return
	}

	SuccessWithMessage(ctx, "版本删除成功", nil)
}

// PublishAppVersion 发布应用版本
//
//	@Summary		发布应用版本
//	@Description	开发者发布应用版本（提交审核）
//	@Tags			应用版本
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id			path		int			true	"应用ID"
//	@Param			version_id	path		int			true	"版本ID"
//	@Success		200			{object}	Response	"提交成功"
//	@Failure		400			{object}	Response	"参数错误"
//	@Failure		401			{object}	Response	"未授权"
//	@Failure		403			{object}	Response	"权限不足"
//	@Failure		404			{object}	Response	"版本不存在"
//	@Failure		500			{object}	Response	"服务器错误"
//	@Router			/apps/{id}/versions/{version_id}/publish [post]
func (c *AppController) PublishAppVersion(ctx *gin.Context) {
	// 获取版本ID
	versionID, err := strconv.ParseUint(ctx.Param("version_id"), 10, 64)
	if err != nil {
		ParamError(ctx, "无效的版本ID")
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "用户未登录")
		return
	}

	// 查询版本
	version, err := models.GetAppVersionByID(c.DB, uint(versionID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "版本不存在")
		} else {
			logger.Error("查询版本失败", zap.Error(err))
			ServerError(ctx, "发布版本失败", err)
		}
		return
	}

	// 查询应用，检查权限
	var app models.Application
	if err := c.DB.First(&app, version.ApplicationID).Error; err != nil {
		logger.Error("查询应用失败", zap.Error(err))
		ServerError(ctx, "发布版本失败", err)
		return
	}

	// 检查权限
	if app.DeveloperID != userID.(uint) {
		Forbidden(ctx, "无权限操作此版本")
		return
	}

	// 检查版本状态（只能发布草稿或被拒绝的版本）
	if version.Status != models.ApplicationStatusDraft && version.Status != models.ApplicationStatusRejected {
		Fail(ctx, 400, "只能发布草稿或被拒绝的版本")
		return
	}

	// 更新版本状态为待审核
	version.Status = models.ApplicationStatusPending
	if err := models.UpdateAppVersion(c.DB, version); err != nil {
		logger.Error("更新版本状态失败", zap.Error(err))
		ServerError(ctx, "发布版本失败", err)
		return
	}

	SuccessWithMessage(ctx, "版本已提交审核", nil)
}

// GetPendingVersions 获取待审核版本列表
//
//	@Summary		获取待审核版本列表
//	@Description	管理员获取待审核的版本列表
//	@Tags			版本审核
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			page		query		int			false	"页码，默认1"	default(1)
//	@Param			page_size	query		int			false	"每页数量，默认20"	default(20)
//	@Success		200			{object}	Response	"获取成功"
//	@Failure		401			{object}	Response	"未授权"
//	@Failure		403			{object}	Response	"权限不足"
//	@Failure		500			{object}	Response	"服务器错误"
//	@Router			/admin/versions/pending [get]
func (c *AppController) GetPendingVersions(ctx *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 获取待审核版本列表
	versions, total, err := models.GetPendingVersions(c.DB, page, pageSize)
	if err != nil {
		logger.Error("获取待审核版本列表失败", zap.Error(err))
		ServerError(ctx, "获取待审核版本列表失败", err)
		return
	}

	// 转换为响应格式
	versionResp := make([]AppVersionResponse, 0, len(versions))
	for _, v := range versions {
		var releasedAt time.Time
		if v.ReleasedAt != nil {
			releasedAt = *v.ReleasedAt
		}

		// 获取应用名称和开发者名称
		appName := ""
		developerName := ""
		if v.Application.Name != "" {
			appName = v.Application.Name
		}
		if v.Application.Developer.Username != "" {
			developerName = v.Application.Developer.Username
		}

		versionResp = append(versionResp, AppVersionResponse{
			ID:                  v.ID,
			ApplicationID:       v.ApplicationID,
			AppName:             appName,
			DeveloperName:       developerName,
			VersionName:         v.VersionName,
			VersionCode:         v.VersionCode,
			ChangeLog:           v.ChangeLog,
			UpdateDescription:   v.ChangeLog,
			PackageURL:          v.PackageURL,
			Size:                v.Size,
			FileSize:            v.Size,
			Status:              string(v.Status),
			MinOpenHarmonyOSVer: v.MinOpenHarmonyOSVer,
			ReleasedAt:          releasedAt,
			DownloadCount:       v.DownloadCount,
			IncrementalUpdate:   v.IncrementalUpdate,
			CreatedAt:           v.CreatedAt,
			UpdatedAt:           v.UpdatedAt,
		})
	}

	// 返回结果
	Success(ctx, PagedResponse{
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		Data:     versionResp,
	})
}

// ReviewAppVersion 审核应用版本
//
//	@Summary		审核应用版本
//	@Description	管理员审核应用版本
//	@Tags			版本审核
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			version_id	path		int						true	"版本ID"
//	@Param			data		body		VersionReviewRequest	true	"审核信息"
//	@Success		200			{object}	Response				"审核成功"
//	@Failure		400			{object}	Response				"参数错误"
//	@Failure		401			{object}	Response				"未授权"
//	@Failure		403			{object}	Response				"权限不足"
//	@Failure		404			{object}	Response				"版本不存在"
//	@Failure		500			{object}	Response				"服务器错误"
//	@Router			/admin/versions/{version_id}/review [post]
func (c *AppController) ReviewAppVersion(ctx *gin.Context) {
	// 获取版本ID
	versionID, err := strconv.ParseUint(ctx.Param("version_id"), 10, 64)
	if err != nil {
		ParamError(ctx, "无效的版本ID")
		return
	}

	var req VersionReviewRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		ParamError(ctx, err.Error())
		return
	}

	// 查询版本
	version, err := models.GetAppVersionByID(c.DB, uint(versionID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "版本不存在")
		} else {
			logger.Error("查询版本失败", zap.Error(err))
			ServerError(ctx, "审核版本失败", err)
		}
		return
	}

	// 检查版本状态
	if version.Status != models.ApplicationStatusPending {
		Fail(ctx, 400, "只能审核处于待审核状态的版本")
		return
	}

	// 更新版本状态
	switch req.Status {
	case "approved":
		if err := models.PublishVersion(c.DB, uint(versionID)); err != nil {
			logger.Error("发布版本失败", zap.Error(err))
			ServerError(ctx, "审核版本失败", err)
			return
		}
		// 更新应用的当前版本
		var app models.Application
		if err := c.DB.First(&app, version.ApplicationID).Error; err == nil {
			app.CurrentVersion = version.VersionName
			app.Size = version.Size
			c.DB.Save(&app)
		}
	case "rejected":
		version.Status = models.ApplicationStatusRejected
		// TODO: 添加拒绝原因字段
		if err := models.UpdateAppVersion(c.DB, version); err != nil {
			logger.Error("拒绝版本失败", zap.Error(err))
			ServerError(ctx, "审核版本失败", err)
			return
		}
	default:
		ParamError(ctx, "无效的审核状态")
		return
	}

	SuccessWithMessage(ctx, "审核成功", nil)
}

// @Summary		提交应用审核
// @Description	开发者提交应用进行审核（从草稿状态变为待审核状态）
// @Tags			应用管理
// @Accept			json
// @Produce		json
// @Security		Bearer
// @Param			id	path		int			true	"应用ID"
// @Success		200	{object}	Response	"提交成功"
// @Failure		400	{object}	Response	"参数错误"
// @Failure		401	{object}	Response	"未授权"
// @Failure		403	{object}	Response	"权限不足"
// @Failure		404	{object}	Response	"应用不存在"
// @Failure		500	{object}	Response	"服务器错误"
// @Router			/apps/{id}/submit [post]
func (c *AppController) SubmitAppForReview(ctx *gin.Context) {
	// 获取应用ID
	appID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		ParamError(ctx, "无效的应用ID")
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "用户未登录")
		return
	}

	// 查询应用
	app, err := models.GetApplicationByID(c.DB, uint(appID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "应用不存在")
		} else {
			logger.Error("查询应用失败", zap.Error(err))
			ServerError(ctx, "提交应用失败", err)
		}
		return
	}

	// 检查权限
	if app.DeveloperID != userID.(uint) {
		Forbidden(ctx, "无权限操作此应用")
		return
	}

	// 检查应用状态（只能提交草稿状态的应用）
	if app.Status != models.ApplicationStatusDraft {
		Fail(ctx, 400, "只能提交草稿状态的应用")
		return
	}

	// 更新应用状态为待审核
	app.Status = models.ApplicationStatusPending
	if err := models.UpdateApplication(c.DB, app); err != nil {
		logger.Error("更新应用状态失败", zap.Error(err))
		ServerError(ctx, "提交应用失败", err)
		return
	}

	SuccessWithMessage(ctx, "应用已提交审核", nil)
}

// GetAppAnonymous 匿名获取应用详情
//
//	@Summary		匿名获取应用详情
//	@Description	匿名用户获取应用的详细信息，包括截图和版本，只返回已审核通过的应用
//	@Tags			应用
//	@Accept			json
//	@Produce		json
//	@Param			id	path		int										true	"应用ID"
//	@Success		200	{object}	Response{data=map[string]interface{}}	"返回应用详情，包括应用基本信息、截图和版本"
//	@Failure		404	{object}	Response								"应用不存在或未审核通过"
//	@Failure		500	{object}	Response								"服务器错误"
//	@Router			/public/apps/{id} [get]
func (c *AppController) GetAppAnonymous(ctx *gin.Context) {
	appID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		ParamError(ctx, "无效的应用ID")
		return
	}

	// 获取应用
	app, err := models.GetApplicationByID(c.DB, uint(appID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "应用不存在")
		} else {
			ServerError(ctx, "获取应用信息失败", err)
		}
		return
	}

	// 只允许访问已审核通过的应用
	if app.Status != models.ApplicationStatusApproved {
		NotFound(ctx, "应用不存在或未审核通过")
		return
	}

	// 获取应用截图
	screenshots, err := models.GetAppScreenshots(c.DB, app.ID)
	if err != nil {
		logger.Error("获取应用截图失败", zap.Error(err))
		screenshots = []models.AppScreenshot{}
	}

	// 获取应用版本（只返回已发布的版本）
	versions, err := models.GetAppVersions(c.DB, app.ID)
	if err != nil {
		logger.Error("获取应用版本失败", zap.Error(err))
		versions = []models.AppVersion{}
	}

	// 过滤只返回已发布的版本
	publishedVersions := make([]models.AppVersion, 0)
	for _, v := range versions {
		if v.Status == models.ApplicationStatusApproved {
			publishedVersions = append(publishedVersions, v)
		}
	}

	// 转换截图为响应格式
	screenshotResp := make([]AppScreenshotResponse, 0, len(screenshots))
	for _, s := range screenshots {
		screenshotResp = append(screenshotResp, AppScreenshotResponse{
			ID:            s.ID,
			ApplicationID: s.ApplicationID,
			ImageURL:      s.ImageURL,
			SortOrder:     s.SortOrder,
			CreatedAt:     s.CreatedAt,
		})
	}

	// 转换版本为响应格式
	versionResp := make([]AppVersionResponse, 0, len(publishedVersions))
	for _, v := range publishedVersions {
		var releasedAt time.Time
		if v.ReleasedAt != nil {
			releasedAt = *v.ReleasedAt
		}
		// 获取应用名称和开发者名称
		appName := ""
		developerName := ""
		if v.Application.ID != 0 {
			appName = v.Application.Name
			if v.Application.Developer.ID != 0 {
				developerName = v.Application.Developer.Username
			}
		}

		versionResp = append(versionResp, AppVersionResponse{
			ID:                  v.ID,
			ApplicationID:       v.ApplicationID,
			AppName:             appName,
			DeveloperName:       developerName,
			VersionName:         v.VersionName,
			VersionCode:         v.VersionCode,
			ChangeLog:           v.ChangeLog,
			UpdateDescription:   v.ChangeLog, // 使用ChangeLog作为UpdateDescription
			PackageURL:          v.PackageURL,
			Size:                v.Size,
			FileSize:            v.Size, // 使用Size作为FileSize
			Status:              string(v.Status),
			MinOpenHarmonyOSVer: v.MinOpenHarmonyOSVer,
			ReleasedAt:          releasedAt,
			DownloadCount:       v.DownloadCount,
			IncrementalUpdate:   v.IncrementalUpdate,
			CreatedAt:           v.CreatedAt,
			UpdatedAt:           v.UpdatedAt,
		})
	}

	var releaseDate time.Time
	if app.ReleaseDate != nil {
		releaseDate = *app.ReleaseDate
	}

	Success(ctx, gin.H{
		"app": AppDetailsResponse{
			ID:                  app.ID,
			Name:                app.Name,
			Package:             app.Package,
			Description:         app.Description,
			ShortDesc:           app.ShortDesc,
			Icon:                app.Icon,
			BannerImage:         app.BannerImage,
			Category:            app.Category,
			DeveloperID:         app.DeveloperID,
			DeveloperName:       app.Developer.DeveloperName,
			CompanyName:         app.Developer.CompanyName,
			Website:             app.Developer.Website,
			CurrentVersion:      app.CurrentVersion,
			PackageURL:          app.PackageURL,
			Status:              string(app.Status),
			ReleaseDate:         releaseDate,
			Size:                app.Size,
			DownloadCount:       app.DownloadCount,
			AverageRating:       app.AverageRating,
			RatingCount:         app.RatingCount,
			MinOpenHarmonyOSVer: app.MinOpenHarmonyOSVer,
			Tags:                app.Tags,
			WebsiteURL:          app.WebsiteURL,
			PrivacyURL:          app.PrivacyURL,
			IsVerified:          app.IsVerified,
			IsFeatured:          app.IsFeatured,
			IsEditor:            app.IsEditor,
			IsTop:               app.IsTop,
			CreatedAt:           app.CreatedAt,
			UpdatedAt:           app.UpdatedAt,
		},
		"screenshots": screenshotResp,
		"versions":    versionResp,
	})
}

// RecordDownloadAnonymous 匿名下载记录
//
//	@Summary		匿名下载记录
//	@Description	匿名用户下载应用时记录下载信息
//	@Tags			应用
//	@Accept			json
//	@Produce		json
//	@Param			id			path		int							true	"应用ID"
//	@Param			version_id	path		int							true	"版本ID"
//	@Param			data		body		AnonymousDownloadRequest	false	"设备信息"
//	@Success		200			{object}	Response					"记录成功"
//	@Failure		400			{object}	Response					"参数错误"
//	@Failure		404			{object}	Response					"应用或版本不存在"
//	@Failure		500			{object}	Response					"服务器错误"
//	@Router			/public/apps/{id}/versions/{version_id}/download [post]
func (c *AppController) RecordDownloadAnonymous(ctx *gin.Context) {
	appID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		ParamError(ctx, "无效的应用ID")
		return
	}

	versionID, err := strconv.Atoi(ctx.Param("version_id"))
	if err != nil {
		ParamError(ctx, "无效的版本ID")
		return
	}

	// 解析请求体（可选）
	var req AnonymousDownloadRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		// 如果解析失败，使用默认值
		req.DeviceType = "unknown"
		req.DeviceOS = "unknown"
		req.DeviceModel = "unknown"
	}

	// 检查应用是否存在且已审核通过
	var app models.Application
	if err := c.DB.Where("id = ? AND status = ?", appID, models.ApplicationStatusApproved).First(&app).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "应用不存在或未审核通过")
		} else {
			ServerError(ctx, "获取应用信息失败", err)
		}
		return
	}

	// 检查版本是否存在且已发布
	var version models.AppVersion
	if err := c.DB.Where("id = ? AND application_id = ? AND status = ?", versionID, appID, models.ApplicationStatusApproved).First(&version).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "版本不存在或未发布")
		} else {
			ServerError(ctx, "获取版本信息失败", err)
		}
		return
	}

	// 获取客户端信息
	ip := ctx.ClientIP()
	userAgent := ctx.Request.UserAgent()

	// 使用请求中的设备信息，如果没有则使用默认值
	deviceType := req.DeviceType
	deviceOS := req.DeviceOS
	deviceModel := req.DeviceModel

	if deviceType == "" {
		deviceType = "unknown"
	}
	if deviceOS == "" {
		deviceOS = "unknown"
	}
	if deviceModel == "" {
		deviceModel = "unknown"
	}

	// 创建匿名下载记录（UserID设为0表示匿名用户）
	record := &models.DownloadRecord{
		UserID:        0, // 匿名用户
		ApplicationID: uint(appID),
		AppVersionID:  uint(versionID),
		VersionName:   version.VersionName,
		IP:            ip,
		UserAgent:     userAgent,
		DeviceType:    deviceType,
		DeviceOS:      deviceOS,
		DeviceModel:   deviceModel,
		Status:        "success",
	}

	if err := models.CreateDownloadRecord(c.DB, record); err != nil {
		logger.Error("创建匿名下载记录失败", zap.Error(err))
		ServerError(ctx, "记录下载失败，请稍后重试", err)
		return
	}

	// 更新应用和版本的下载计数
	go func() {
		// 更新应用下载计数
		c.DB.Model(&models.Application{}).Where("id = ?", appID).UpdateColumn("download_count", gorm.Expr("download_count + ?", 1))
		// 更新版本下载计数
		c.DB.Model(&models.AppVersion{}).Where("id = ?", versionID).UpdateColumn("download_count", gorm.Expr("download_count + ?", 1))
	}()

	SuccessWithMessage(ctx, "下载记录已保存", nil)
}

// GetRecommendedApps 获取推荐应用
//
//	@Summary		获取推荐应用
//	@Description	获取编辑推荐的应用列表，无需认证
//	@Tags			应用
//	@Accept			json
//	@Produce		json
//	@Param			limit	query		int									false	"返回数量，默认10个"	default(10)
//	@Success		200		{object}	Response{data=[]AppDetailsResponse}	"返回推荐应用列表"
//	@Failure		500		{object}	Response							"服务器错误"
//	@Router			/public/apps/recommended [get]
func (c *AppController) GetRecommendedApps(ctx *gin.Context) {
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "10"))
	if limit <= 0 || limit > 50 {
		limit = 10
	}

	// 获取编辑推荐的应用
	var apps []models.Application
	if err := c.DB.Where("status = ? AND is_editor = ?", models.ApplicationStatusApproved, true).
		Preload("Developer").
		Order("created_at DESC").
		Limit(limit).
		Find(&apps).Error; err != nil {
		logger.Error("获取推荐应用失败", zap.Error(err))
		ServerError(ctx, "获取推荐应用失败", err)
		return
	}

	// 转换为响应格式
	result := make([]AppDetailsResponse, 0, len(apps))
	for _, app := range apps {
		var releaseDate time.Time
		if app.ReleaseDate != nil {
			releaseDate = *app.ReleaseDate
		}

		result = append(result, AppDetailsResponse{
			ID:                  app.ID,
			Name:                app.Name,
			Package:             app.Package,
			ShortDesc:           app.ShortDesc,
			Description:         app.Description,
			Icon:                app.Icon,
			Category:            app.Category,
			DeveloperID:         app.DeveloperID,
			DeveloperName:       app.Developer.DeveloperName,
			CurrentVersion:      app.CurrentVersion,
			ReleaseDate:         releaseDate,
			Size:                app.Size,
			DownloadCount:       app.DownloadCount,
			AverageRating:       app.AverageRating,
			RatingCount:         app.RatingCount,
			MinOpenHarmonyOSVer: app.MinOpenHarmonyOSVer,
			Tags:                app.Tags,
			WebsiteURL:          app.WebsiteURL,
			PrivacyURL:          app.PrivacyURL,
			IsVerified:          app.IsVerified,
			IsFeatured:          app.IsFeatured,
			IsEditor:            app.IsEditor,
			IsTop:               app.IsTop,
			Status:              string(app.Status),
			CreatedAt:           app.CreatedAt,
			UpdatedAt:           app.UpdatedAt,
		})
	}

	Success(ctx, result)
}

// GetPopularApps 获取热门应用
//
//	@Summary		获取热门应用
//	@Description	获取下载量和评分最高的应用列表，无需认证
//	@Tags			应用
//	@Accept			json
//	@Produce		json
//	@Param			limit	query		int									false	"返回数量，默认10个"	default(10)
//	@Success		200		{object}	Response{data=[]AppDetailsResponse}	"返回热门应用列表"
//	@Failure		500		{object}	Response							"服务器错误"
//	@Router			/public/apps/popular [get]
func (c *AppController) GetPopularApps(ctx *gin.Context) {
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "10"))
	if limit <= 0 || limit > 50 {
		limit = 10
	}

	// 获取热门应用（按下载量和评分排序）
	var apps []models.Application
	if err := c.DB.Where("status = ?", models.ApplicationStatusApproved).
		Preload("Developer").
		Order("download_count DESC, average_rating DESC").
		Limit(limit).
		Find(&apps).Error; err != nil {
		logger.Error("获取热门应用失败", zap.Error(err))
		ServerError(ctx, "获取热门应用失败", err)
		return
	}

	// 转换为响应格式
	result := make([]AppDetailsResponse, 0, len(apps))
	for _, app := range apps {
		var releaseDate time.Time
		if app.ReleaseDate != nil {
			releaseDate = *app.ReleaseDate
		}

		result = append(result, AppDetailsResponse{
			ID:                  app.ID,
			Name:                app.Name,
			Package:             app.Package,
			ShortDesc:           app.ShortDesc,
			Description:         app.Description,
			Icon:                app.Icon,
			Category:            app.Category,
			DeveloperID:         app.DeveloperID,
			DeveloperName:       app.Developer.DeveloperName,
			CurrentVersion:      app.CurrentVersion,
			ReleaseDate:         releaseDate,
			Size:                app.Size,
			DownloadCount:       app.DownloadCount,
			AverageRating:       app.AverageRating,
			RatingCount:         app.RatingCount,
			MinOpenHarmonyOSVer: app.MinOpenHarmonyOSVer,
			Tags:                app.Tags,
			WebsiteURL:          app.WebsiteURL,
			PrivacyURL:          app.PrivacyURL,
			IsVerified:          app.IsVerified,
			IsFeatured:          app.IsFeatured,
			IsEditor:            app.IsEditor,
			IsTop:               app.IsTop,
			Status:              string(app.Status),
			CreatedAt:           app.CreatedAt,
			UpdatedAt:           app.UpdatedAt,
		})
	}

	Success(ctx, result)
}

// GetLatestApps 获取最新应用
//
//	@Summary		获取最新应用
//	@Description	获取最新发布的应用列表，无需认证
//	@Tags			应用
//	@Accept			json
//	@Produce		json
//	@Param			limit	query		int									false	"返回数量，默认10个"	default(10)
//	@Success		200		{object}	Response{data=[]AppDetailsResponse}	"返回最新应用列表"
//	@Failure		500		{object}	Response							"服务器错误"
//	@Router			/public/apps/latest [get]
func (c *AppController) GetLatestApps(ctx *gin.Context) {
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "10"))
	if limit <= 0 || limit > 50 {
		limit = 10
	}

	// 获取最新应用（按创建时间排序）
	var apps []models.Application
	if err := c.DB.Where("status = ?", models.ApplicationStatusApproved).
		Preload("Developer").
		Order("created_at DESC").
		Limit(limit).
		Find(&apps).Error; err != nil {
		logger.Error("获取最新应用失败", zap.Error(err))
		ServerError(ctx, "获取最新应用失败", err)
		return
	}

	// 转换为响应格式
	result := make([]AppDetailsResponse, 0, len(apps))
	for _, app := range apps {
		var releaseDate time.Time
		if app.ReleaseDate != nil {
			releaseDate = *app.ReleaseDate
		}

		result = append(result, AppDetailsResponse{
			ID:                  app.ID,
			Name:                app.Name,
			Package:             app.Package,
			ShortDesc:           app.ShortDesc,
			Description:         app.Description,
			Icon:                app.Icon,
			Category:            app.Category,
			DeveloperID:         app.DeveloperID,
			DeveloperName:       app.Developer.DeveloperName,
			CurrentVersion:      app.CurrentVersion,
			ReleaseDate:         releaseDate,
			Size:                app.Size,
			DownloadCount:       app.DownloadCount,
			AverageRating:       app.AverageRating,
			RatingCount:         app.RatingCount,
			MinOpenHarmonyOSVer: app.MinOpenHarmonyOSVer,
			Tags:                app.Tags,
			WebsiteURL:          app.WebsiteURL,
			PrivacyURL:          app.PrivacyURL,
			IsVerified:          app.IsVerified,
			IsFeatured:          app.IsFeatured,
			IsEditor:            app.IsEditor,
			IsTop:               app.IsTop,
			Status:              string(app.Status),
			CreatedAt:           app.CreatedAt,
			UpdatedAt:           app.UpdatedAt,
		})
	}

	Success(ctx, result)
}

// GetFeaturedApps 获取排行应用
//
//	@Summary		获取排行应用
//	@Description	获取综合排行榜应用列表，按照评分、下载量等综合指标排序，无需认证
//	@Tags			应用
//	@Accept			json
//	@Produce		json
//	@Param			limit	query		int									false	"返回数量，默认10个"	default(10)
//	@Success		200		{object}	Response{data=[]AppDetailsResponse}	"返回排行应用列表"
//	@Failure		500		{object}	Response							"服务器错误"
//	@Router			/public/apps/featured [get]
func (c *AppController) GetFeaturedApps(ctx *gin.Context) {
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "10"))
	if limit <= 0 || limit > 50 {
		limit = 10
	}

	// 获取排行应用（按综合排行规则排序：置顶 > 评分权重 > 下载量 > 评分数量）
	var apps []models.Application
	if err := c.DB.Where("status = ?", models.ApplicationStatusApproved).
		Preload("Developer").
		Order("is_top DESC, (average_rating * 0.7 + LEAST(download_count/1000.0, 10) * 0.3) DESC, rating_count DESC").
		Limit(limit).
		Find(&apps).Error; err != nil {
		logger.Error("获取排行应用失败", zap.Error(err))
		ServerError(ctx, "获取排行应用失败", err)
		return
	}

	// 转换为响应格式
	result := make([]AppDetailsResponse, 0, len(apps))
	for _, app := range apps {
		var releaseDate time.Time
		if app.ReleaseDate != nil {
			releaseDate = *app.ReleaseDate
		}

		result = append(result, AppDetailsResponse{
			ID:                  app.ID,
			Name:                app.Name,
			Package:             app.Package,
			ShortDesc:           app.ShortDesc,
			Description:         app.Description,
			Icon:                app.Icon,
			Category:            app.Category,
			DeveloperID:         app.DeveloperID,
			DeveloperName:       app.Developer.DeveloperName,
			CurrentVersion:      app.CurrentVersion,
			ReleaseDate:         releaseDate,
			Size:                app.Size,
			DownloadCount:       app.DownloadCount,
			AverageRating:       app.AverageRating,
			RatingCount:         app.RatingCount,
			MinOpenHarmonyOSVer: app.MinOpenHarmonyOSVer,
			Tags:                app.Tags,
			WebsiteURL:          app.WebsiteURL,
			PrivacyURL:          app.PrivacyURL,
			IsVerified:          app.IsVerified,
			IsFeatured:          app.IsFeatured,
			IsEditor:            app.IsEditor,
			IsTop:               app.IsTop,
			Status:              string(app.Status),
			CreatedAt:           app.CreatedAt,
			UpdatedAt:           app.UpdatedAt,
		})
	}

	Success(ctx, result)
}

// GetPublicApps 获取公开应用列表
//
//	@Summary		获取公开应用列表
//	@Description	获取已审核通过的应用列表，支持分页和筛选，无需认证
//	@Tags			应用
//	@Accept			json
//	@Produce		json
//	@Param			page		query		int										false	"页码，默认1"	default(1)
//	@Param			page_size	query		int										false	"每页数量，默认20"	default(20)
//	@Param			category	query		string									false	"分类筛选"
//	@Param			keyword		query		string									false	"搜索关键字"
//	@Param			sort		query		string									false	"排序方式：latest(最新)、popular(热门)、rating(评分)"	default(latest)
//	@Success		200			{object}	PageResponse{data=[]AppDetailsResponse}	"返回应用列表"
//	@Failure		400			{object}	Response								"参数错误"
//	@Failure		500			{object}	Response								"服务器错误"
//	@Router			/public/apps [get]
func (c *AppController) GetPublicApps(ctx *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	category := ctx.Query("category")
	keyword := ctx.Query("keyword")
	sort := ctx.DefaultQuery("sort", "latest")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	// 构建查询 - 只显示已审核通过的应用
	query := c.DB.Model(&models.Application{}).Where("status = ?", models.ApplicationStatusApproved)

	if category != "" {
		query = query.Where("category = ?", category)
	}

	if keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Error("获取应用总数失败", zap.Error(err))
		ServerError(ctx, "获取应用列表失败", err)
		return
	}

	// 根据排序方式设置排序
	var orderBy string
	switch sort {
	case "popular":
		orderBy = "download_count DESC, average_rating DESC"
	case "rating":
		orderBy = "average_rating DESC, rating_count DESC"
	case "latest":
		fallthrough
	default:
		orderBy = "created_at DESC"
	}

	// 获取应用列表
	var apps []models.Application
	if err := query.Preload("Developer").Order(orderBy).Offset(offset).Limit(pageSize).Find(&apps).Error; err != nil {
		logger.Error("获取应用列表失败", zap.Error(err))
		ServerError(ctx, "获取应用列表失败", err)
		return
	}

	// 构建返回结果
	result := make([]AppDetailsResponse, 0, len(apps))
	for _, app := range apps {
		var releaseDate time.Time
		if app.ReleaseDate != nil {
			releaseDate = *app.ReleaseDate
		}

		result = append(result, AppDetailsResponse{
			ID:                  app.ID,
			Name:                app.Name,
			Package:             app.Package,
			ShortDesc:           app.ShortDesc,
			Description:         app.Description,
			Icon:                app.Icon,
			Category:            app.Category,
			DeveloperID:         app.DeveloperID,
			DeveloperName:       app.Developer.DeveloperName,
			CurrentVersion:      app.CurrentVersion,
			ReleaseDate:         releaseDate,
			Size:                app.Size,
			DownloadCount:       app.DownloadCount,
			AverageRating:       app.AverageRating,
			RatingCount:         app.RatingCount,
			MinOpenHarmonyOSVer: app.MinOpenHarmonyOSVer,
			Tags:                app.Tags,
			WebsiteURL:          app.WebsiteURL,
			PrivacyURL:          app.PrivacyURL,
			IsVerified:          app.IsVerified,
			IsFeatured:          app.IsFeatured,
			IsEditor:            app.IsEditor,
			IsTop:               app.IsTop,
			Status:              string(app.Status),
			CreatedAt:           app.CreatedAt,
			UpdatedAt:           app.UpdatedAt,
		})
	}

	// 返回分页结果
	Success(ctx, PagedResponse{
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		Data:     result,
	})
}
