"use strict";
import { jsx } from "react/jsx-runtime";
import {
  Link,
  useLocation,
  useNavigate,
  Outlet,
  useAppData,
  matchRoutes
} from "@umijs/max";
import { useMemo } from "react";
import {
  ProLayout
} from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/@ant-design/pro-components";
import "./Layout.css";
import Logo from "./Logo";
import Exception from "./Exception";
import { getRightRenderContent } from "./rightRender";
import { useModel } from "@@/plugin-model";
import { useAccessMarkedRoutes } from "@@/plugin-access";
import { useIntl } from "@@/plugin-locale";
const filterRoutes = (routes, filterFn) => {
  if (routes.length === 0) {
    return [];
  }
  let newRoutes = [];
  for (const route of routes) {
    const newRoute = { ...route };
    if (filterFn(route)) {
      if (Array.isArray(newRoute.routes)) {
        newRoutes.push(...filterRoutes(newRoute.routes, filterFn));
      }
    } else {
      if (Array.isArray(newRoute.children)) {
        newRoute.children = filterRoutes(newRoute.children, filterFn);
        newRoute.routes = newRoute.children;
      }
      newRoutes.push(newRoute);
    }
  }
  return newRoutes;
};
const mapRoutes = (routes) => {
  if (routes.length === 0) {
    return [];
  }
  return routes.map((route) => {
    const newRoute = { ...route };
    if (route.originPath) {
      newRoute.path = route.originPath;
    }
    if (Array.isArray(route.routes)) {
      newRoute.routes = mapRoutes(route.routes);
    }
    if (Array.isArray(route.children)) {
      newRoute.children = mapRoutes(route.children);
    }
    return newRoute;
  });
};
export default (props) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { clientRoutes, pluginManager } = useAppData();
  const initialInfo = useModel && useModel("@@initialState") || {
    initialState: void 0,
    loading: false,
    setInitialState: null
  };
  const { initialState, loading, setInitialState } = initialInfo;
  const userConfig = {
    "locale": true,
    "navTheme": "light",
    "colorPrimary": "#1890ff",
    "layout": "mix",
    "contentWidth": "Fluid",
    "fixedHeader": false,
    "fixSiderbar": true,
    "colorWeak": false,
    "title": "NexusHub \u7BA1\u7406\u540E\u53F0",
    "pwa": true,
    "logo": "/logo.svg",
    "iconfontUrl": "",
    "token": {}
  };
  const { formatMessage } = useIntl();
  const runtimeConfig = pluginManager.applyPlugins({
    key: "layout",
    type: "modify",
    initialValue: {
      ...initialInfo
    }
  });
  const newRoutes = filterRoutes(clientRoutes.filter((route2) => route2.id === "ant-design-pro-layout"), (route2) => {
    return !!route2.isLayout && route2.id !== "ant-design-pro-layout" || !!route2.isWrapper;
  });
  const [route] = useAccessMarkedRoutes(mapRoutes(newRoutes));
  const matchedRoute = useMemo(() => matchRoutes(route.children, location.pathname)?.pop?.()?.route, [location.pathname]);
  return /* @__PURE__ */ jsx(
    ProLayout,
    {
      route,
      location,
      title: userConfig.title || "ant-design-pro",
      navTheme: "dark",
      siderWidth: 256,
      onMenuHeaderClick: (e) => {
        e.stopPropagation();
        e.preventDefault();
        navigate("/");
      },
      formatMessage: userConfig.formatMessage || formatMessage,
      menu: { locale: userConfig.locale },
      logo: Logo,
      menuItemRender: (menuItemProps, defaultDom) => {
        if (menuItemProps.isUrl || menuItemProps.children) {
          return defaultDom;
        }
        if (menuItemProps.path && location.pathname !== menuItemProps.path) {
          return (
            // handle wildcard route path, for example /slave/* from qiankun
            /* @__PURE__ */ jsx(Link, { to: menuItemProps.path.replace("/*", ""), target: menuItemProps.target, children: defaultDom })
          );
        }
        return defaultDom;
      },
      itemRender: (route2, _, routes) => {
        const { breadcrumbName, title, path } = route2;
        const label = title || breadcrumbName;
        const last = routes[routes.length - 1];
        if (last) {
          if (last.path === path || last.linkPath === path) {
            return /* @__PURE__ */ jsx("span", { children: label });
          }
        }
        return /* @__PURE__ */ jsx(Link, { to: path, children: label });
      },
      disableContentMargin: true,
      fixSiderbar: true,
      fixedHeader: true,
      ...runtimeConfig,
      rightContentRender: runtimeConfig.rightContentRender !== false && ((layoutProps) => {
        const dom = getRightRenderContent({
          runtimeConfig,
          loading,
          initialState,
          setInitialState
        });
        if (runtimeConfig.rightContentRender) {
          return runtimeConfig.rightContentRender(layoutProps, dom, {
            // BREAK CHANGE userConfig > runtimeConfig
            userConfig,
            runtimeConfig,
            loading,
            initialState,
            setInitialState
          });
        }
        return dom;
      }),
      children: /* @__PURE__ */ jsx(
        Exception,
        {
          route: matchedRoute,
          noFound: runtimeConfig?.noFound,
          notFound: runtimeConfig?.notFound,
          unAccessible: runtimeConfig?.unAccessible,
          noAccessible: runtimeConfig?.noAccessible,
          children: runtimeConfig.childrenRender ? runtimeConfig.childrenRender(/* @__PURE__ */ jsx(Outlet, {}), props) : /* @__PURE__ */ jsx(Outlet, {})
        }
      )
    }
  );
};
