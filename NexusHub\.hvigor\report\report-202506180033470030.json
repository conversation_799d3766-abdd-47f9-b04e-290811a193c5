{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "aa1fdd8f-77e9-4e38-b0c7-d517f4afab17", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385686405600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87d96ec9-a6c6-4ed9-9a8b-13beca33d1c3", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385686636900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ee462cd-8f3e-4919-b1bb-9a0ce45b745a", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385686863000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29efdfae-821a-48af-90ca-1d323844fa12", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385690156100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74ee8b33-94c7-4bd2-9517-af56dd8d634c", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385690595100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acd19694-ab45-4e05-bfa8-0e254430c6a9", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385691281400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0556325-1064-4797-afce-540456adc878", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385691495000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a0bd06c-7952-41a5-8af8-e408a992ad36", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385691805000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e16065e-4006-40d8-9a84-47b173f7eb37", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385727506200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a50587ce-55fe-4a39-b5fb-c2e9a33e7027", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397219798800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02fc766d-88f5-4739-a5a8-0d4d147ff271", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397226506700, "endTime": 153397444775000}, "additional": {"children": ["10d1baf1-d7c2-442d-b745-5708273137e7", "22ccd8fe-646e-4850-b070-38d5921e49d5", "064f2aaa-8c4f-46df-ad2f-24b73c6b1657", "19e906c5-0700-4d64-8933-aa7184b82d66", "da857e25-ca9d-429b-a3fd-7ea431ac5df0", "33575da8-5dad-4191-bb6a-7b01d9f4fa42", "021e7d96-40cb-4e23-ab34-62b65a599046"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "1999c07f-7e6d-4692-a859-01fcf2410c2a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10d1baf1-d7c2-442d-b745-5708273137e7", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397226507900, "endTime": 153397238924500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "02fc766d-88f5-4739-a5a8-0d4d147ff271", "logId": "bec5a8ca-1481-464f-95fa-8fd1885e555b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22ccd8fe-646e-4850-b070-38d5921e49d5", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397238942100, "endTime": 153397443462200}, "additional": {"children": ["63643281-42a7-46e2-9367-5aed2fd40950", "3bad98d0-2e69-4214-8117-91f5fb4a273b", "b37d1a07-fc9a-4dd3-a315-51abd7f9b30d", "11a5ad90-4a48-4732-84f5-cf6afec1232a", "58c64f97-03b6-4005-8808-c66a890cb218", "d326a279-d709-496a-b390-939c144ecbed", "91a5d27b-0f7b-49b4-9785-9ce75327b192", "94706089-ae0c-4d56-8f5f-d0ef6546ed4d", "8ee6efa2-ecbb-4822-b62f-1140ae90ce41"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "02fc766d-88f5-4739-a5a8-0d4d147ff271", "logId": "4dc9d78e-86e9-494d-b516-9ba8712f4bb7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "064f2aaa-8c4f-46df-ad2f-24b73c6b1657", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397443486200, "endTime": 153397444742600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "02fc766d-88f5-4739-a5a8-0d4d147ff271", "logId": "ded48b9e-f484-4836-8c02-a97e65abdfa1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19e906c5-0700-4d64-8933-aa7184b82d66", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397444746900, "endTime": 153397444771500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "02fc766d-88f5-4739-a5a8-0d4d147ff271", "logId": "f0c18bda-56ad-4a5f-b150-dce9769e3f6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da857e25-ca9d-429b-a3fd-7ea431ac5df0", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397229912200, "endTime": 153397229954200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "02fc766d-88f5-4739-a5a8-0d4d147ff271", "logId": "21c33724-613b-4867-9c28-a6d0f56437ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21c33724-613b-4867-9c28-a6d0f56437ec", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397229912200, "endTime": 153397229954200}, "additional": {"logType": "info", "children": [], "durationId": "da857e25-ca9d-429b-a3fd-7ea431ac5df0", "parent": "1999c07f-7e6d-4692-a859-01fcf2410c2a"}}, {"head": {"id": "33575da8-5dad-4191-bb6a-7b01d9f4fa42", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397234269000, "endTime": 153397234292000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "02fc766d-88f5-4739-a5a8-0d4d147ff271", "logId": "c8477c61-752b-43f8-a86e-aad49b4bcb7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8477c61-752b-43f8-a86e-aad49b4bcb7f", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397234269000, "endTime": 153397234292000}, "additional": {"logType": "info", "children": [], "durationId": "33575da8-5dad-4191-bb6a-7b01d9f4fa42", "parent": "1999c07f-7e6d-4692-a859-01fcf2410c2a"}}, {"head": {"id": "4b80fd04-026b-4218-998a-1e9248829919", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397234343300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8efe123-d749-4e9f-9c1c-a27183ab04e7", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397238692800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bec5a8ca-1481-464f-95fa-8fd1885e555b", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397226507900, "endTime": 153397238924500}, "additional": {"logType": "info", "children": [], "durationId": "10d1baf1-d7c2-442d-b745-5708273137e7", "parent": "1999c07f-7e6d-4692-a859-01fcf2410c2a"}}, {"head": {"id": "63643281-42a7-46e2-9367-5aed2fd40950", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397243914700, "endTime": 153397243924900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "22ccd8fe-646e-4850-b070-38d5921e49d5", "logId": "bfa631fb-de75-4de3-9b74-6f9db2300318"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3bad98d0-2e69-4214-8117-91f5fb4a273b", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397243937800, "endTime": 153397247995000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "22ccd8fe-646e-4850-b070-38d5921e49d5", "logId": "4252f0d7-1fca-41af-9704-7ef13a658ad8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b37d1a07-fc9a-4dd3-a315-51abd7f9b30d", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397248009500, "endTime": 153397330232600}, "additional": {"children": ["20024e1f-5962-4060-892f-ea4f7daa3570", "f2b44b7c-1fd8-4511-825e-5118865b9512", "29cde0fa-0cc2-4740-95bc-f7b01b0ac63d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "22ccd8fe-646e-4850-b070-38d5921e49d5", "logId": "0cd54deb-89d7-4bd6-a6e6-8c46c656a6b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11a5ad90-4a48-4732-84f5-cf6afec1232a", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397330254800, "endTime": 153397367758100}, "additional": {"children": ["80ed0fe2-7dd2-43f5-9f0c-1620affec774"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "22ccd8fe-646e-4850-b070-38d5921e49d5", "logId": "308fd2b8-9c1b-4563-8d47-1719d39ec42c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58c64f97-03b6-4005-8808-c66a890cb218", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397367765300, "endTime": 153397414496100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "22ccd8fe-646e-4850-b070-38d5921e49d5", "logId": "e6772d74-3c36-47b7-bd3a-9f1bf48972b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d326a279-d709-496a-b390-939c144ecbed", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397415591000, "endTime": 153397428125100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "22ccd8fe-646e-4850-b070-38d5921e49d5", "logId": "bd7e30c4-fa3f-487d-b6ac-224074ceeffa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91a5d27b-0f7b-49b4-9785-9ce75327b192", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397428148900, "endTime": 153397443275100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "22ccd8fe-646e-4850-b070-38d5921e49d5", "logId": "3f813773-cd73-469e-81af-c165a88ec00f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94706089-ae0c-4d56-8f5f-d0ef6546ed4d", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397443298400, "endTime": 153397443450500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "22ccd8fe-646e-4850-b070-38d5921e49d5", "logId": "23708649-5b51-4c4e-87cb-096d93224952"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bfa631fb-de75-4de3-9b74-6f9db2300318", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397243914700, "endTime": 153397243924900}, "additional": {"logType": "info", "children": [], "durationId": "63643281-42a7-46e2-9367-5aed2fd40950", "parent": "4dc9d78e-86e9-494d-b516-9ba8712f4bb7"}}, {"head": {"id": "4252f0d7-1fca-41af-9704-7ef13a658ad8", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397243937800, "endTime": 153397247995000}, "additional": {"logType": "info", "children": [], "durationId": "3bad98d0-2e69-4214-8117-91f5fb4a273b", "parent": "4dc9d78e-86e9-494d-b516-9ba8712f4bb7"}}, {"head": {"id": "20024e1f-5962-4060-892f-ea4f7daa3570", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397248737200, "endTime": 153397248758000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b37d1a07-fc9a-4dd3-a315-51abd7f9b30d", "logId": "3273a4f4-2306-46cb-bf23-9d39b392acd3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3273a4f4-2306-46cb-bf23-9d39b392acd3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397248737200, "endTime": 153397248758000}, "additional": {"logType": "info", "children": [], "durationId": "20024e1f-5962-4060-892f-ea4f7daa3570", "parent": "0cd54deb-89d7-4bd6-a6e6-8c46c656a6b8"}}, {"head": {"id": "f2b44b7c-1fd8-4511-825e-5118865b9512", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397250453600, "endTime": 153397328122900}, "additional": {"children": ["1eb3821b-4b05-4c18-8b14-ee32004c6b6f", "76915c98-f201-4733-a539-4b1b1fe10a19"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b37d1a07-fc9a-4dd3-a315-51abd7f9b30d", "logId": "ce01964a-60e6-47cd-b1f6-fcadbdcdaeaf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1eb3821b-4b05-4c18-8b14-ee32004c6b6f", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397250454400, "endTime": 153397255873300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2b44b7c-1fd8-4511-825e-5118865b9512", "logId": "40033169-447b-471f-809a-8e60414caf5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76915c98-f201-4733-a539-4b1b1fe10a19", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397255889300, "endTime": 153397328103500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2b44b7c-1fd8-4511-825e-5118865b9512", "logId": "f6c9013b-9472-48e6-a662-5d0e8be2b1dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba100997-1c62-405c-bbe7-0ca5b2aa82c0", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397250458800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d09e5b4c-8117-4c26-a7b6-2547f0185728", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397255735300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40033169-447b-471f-809a-8e60414caf5d", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397250454400, "endTime": 153397255873300}, "additional": {"logType": "info", "children": [], "durationId": "1eb3821b-4b05-4c18-8b14-ee32004c6b6f", "parent": "ce01964a-60e6-47cd-b1f6-fcadbdcdaeaf"}}, {"head": {"id": "44a6fa40-8272-4f5e-8a3c-add1f15d41b6", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397255901800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50186b83-7b7e-44b2-9ef9-4ec69c05856c", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397262207200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d670ff9-a7e2-4dff-b151-5d059f74f150", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397262338500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ea6153d-c782-434c-9781-3f723651c38e", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397262444300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6f7ccac-c17f-4d05-a9d4-e1d9adb7bc49", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397262594900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cca889ed-0143-418c-926e-9d76faff90e0", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397265171600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ba35bf4-d7c5-44b8-b629-4c98838cf0e2", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397276405500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cca05c0e-3c28-4542-859c-cb99236578e4", "name": "Sdk init in 26 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397295735100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c27a82d1-6c1a-4b9b-a0ba-476fd0b7c6e5", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397296321300}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 33, "second": 47}, "markType": "other"}}, {"head": {"id": "b85a1811-4a7a-48a1-a7be-2ce6bd47ed9c", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397296477200}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 33, "second": 47}, "markType": "other"}}, {"head": {"id": "df778290-d7e1-4fcf-a1f3-312feacf39c3", "name": "Project task initialization takes 29 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397327604900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37cc5710-8ac1-4e36-affd-ff14c11bc820", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397327806000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2855a930-d392-4bca-bed2-b7edbcde4a1e", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397327926700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76f70ebf-f19b-42c2-886d-0b74f32a0f1b", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397328009300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6c9013b-9472-48e6-a662-5d0e8be2b1dd", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397255889300, "endTime": 153397328103500}, "additional": {"logType": "info", "children": [], "durationId": "76915c98-f201-4733-a539-4b1b1fe10a19", "parent": "ce01964a-60e6-47cd-b1f6-fcadbdcdaeaf"}}, {"head": {"id": "ce01964a-60e6-47cd-b1f6-fcadbdcdaeaf", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397250453600, "endTime": 153397328122900}, "additional": {"logType": "info", "children": ["40033169-447b-471f-809a-8e60414caf5d", "f6c9013b-9472-48e6-a662-5d0e8be2b1dd"], "durationId": "f2b44b7c-1fd8-4511-825e-5118865b9512", "parent": "0cd54deb-89d7-4bd6-a6e6-8c46c656a6b8"}}, {"head": {"id": "29cde0fa-0cc2-4740-95bc-f7b01b0ac63d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397330141300, "endTime": 153397330191800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b37d1a07-fc9a-4dd3-a315-51abd7f9b30d", "logId": "39e3cfa5-4d69-4748-bf62-d5ce229ed129"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39e3cfa5-4d69-4748-bf62-d5ce229ed129", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397330141300, "endTime": 153397330191800}, "additional": {"logType": "info", "children": [], "durationId": "29cde0fa-0cc2-4740-95bc-f7b01b0ac63d", "parent": "0cd54deb-89d7-4bd6-a6e6-8c46c656a6b8"}}, {"head": {"id": "0cd54deb-89d7-4bd6-a6e6-8c46c656a6b8", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397248009500, "endTime": 153397330232600}, "additional": {"logType": "info", "children": ["3273a4f4-2306-46cb-bf23-9d39b392acd3", "ce01964a-60e6-47cd-b1f6-fcadbdcdaeaf", "39e3cfa5-4d69-4748-bf62-d5ce229ed129"], "durationId": "b37d1a07-fc9a-4dd3-a315-51abd7f9b30d", "parent": "4dc9d78e-86e9-494d-b516-9ba8712f4bb7"}}, {"head": {"id": "80ed0fe2-7dd2-43f5-9f0c-1620affec774", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397332017900, "endTime": 153397367745400}, "additional": {"children": ["4ab7437a-8f72-40d9-a01e-9403770a49fa", "b81c68a6-1768-4105-9fcc-a9e41069098f", "57dd0f5f-00b5-4e1e-bb30-acdbd1ee0988"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "11a5ad90-4a48-4732-84f5-cf6afec1232a", "logId": "b13061db-10a9-4787-9b92-0e68ce670e18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ab7437a-8f72-40d9-a01e-9403770a49fa", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397337744500, "endTime": 153397337765700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "80ed0fe2-7dd2-43f5-9f0c-1620affec774", "logId": "52217b2c-e827-4344-b9a5-3257f076c450"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52217b2c-e827-4344-b9a5-3257f076c450", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397337744500, "endTime": 153397337765700}, "additional": {"logType": "info", "children": [], "durationId": "4ab7437a-8f72-40d9-a01e-9403770a49fa", "parent": "b13061db-10a9-4787-9b92-0e68ce670e18"}}, {"head": {"id": "b81c68a6-1768-4105-9fcc-a9e41069098f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397340400100, "endTime": 153397365990100}, "additional": {"children": ["f42e98e7-f352-4dbf-9a59-dd77feae2980", "40058cf2-da16-4919-874c-865735297b86"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "80ed0fe2-7dd2-43f5-9f0c-1620affec774", "logId": "095fbb2a-fb11-42f8-922b-1e9d30fc6e62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f42e98e7-f352-4dbf-9a59-dd77feae2980", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397340402200, "endTime": 153397347531900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b81c68a6-1768-4105-9fcc-a9e41069098f", "logId": "6bba5499-3247-479d-908d-4a6286890e98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40058cf2-da16-4919-874c-865735297b86", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397347560500, "endTime": 153397365977700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b81c68a6-1768-4105-9fcc-a9e41069098f", "logId": "431722a2-4c56-456a-93d3-490f52cc66ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44573ad2-0c19-4be0-b986-a205ffff2e71", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397340406800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "067fb33a-7b80-43e2-8aa2-df7e10c2031d", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397347309200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bba5499-3247-479d-908d-4a6286890e98", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397340402200, "endTime": 153397347531900}, "additional": {"logType": "info", "children": [], "durationId": "f42e98e7-f352-4dbf-9a59-dd77feae2980", "parent": "095fbb2a-fb11-42f8-922b-1e9d30fc6e62"}}, {"head": {"id": "e124bb55-59ba-41a7-84b5-0cd859a0e928", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397347585900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6785b564-6bd8-4227-bcfa-ed31d579eb09", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397357397100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ae335ad-7192-4848-a367-2161854a0ea6", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397357560800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ffe312f-2df6-4cb9-baed-a2e21be843bd", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397357832600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ca34a15-715a-49ed-a7a8-658c1a831e62", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397358053600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6b3e15d-4247-4c5c-9c48-353dc0b3485c", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397358147000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4a0a171-e118-43b3-9772-da45094c0942", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397358250200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c8c50e1-5aaf-4775-99a2-4299e9a83a0f", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397358605400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d068299-d3c0-45f1-9f5f-9924c7e4acd5", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397358750800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd20db0a-3ca6-4197-b94e-346eb05eafbc", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397359074700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf78ca19-5d49-4288-be7f-38626020da56", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397359269200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90ae5d9c-61a0-4681-ae13-f9136cdb6b72", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397359357100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ad9765b-4498-4711-bda5-7ebdc2741fa4", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397359414000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adbd3943-f670-454f-917a-f5d57d427c3e", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397359660300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c6dbfb9-314c-4aea-a3ef-c626ce7079e0", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397359760300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2afdd2ad-50f4-49e7-981f-4aa4cea72183", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397359974800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a2d271d-4286-475b-b69f-a2b46fd98dee", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397360550300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b4a4dd7-2fa5-44e6-a94b-567944598e8e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397360845900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ec2a120-7f8e-4a89-8cd1-6cfa918e0536", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397360950600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17680516-7fc9-419c-b2c7-f80b6835cf9f", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397361223500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14326df2-6dfa-4782-818e-2a977c0b3142", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397365621000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cb575ec-e4b1-43ab-8ca9-75e4a91ced68", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397365811200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c46bd9bb-fa1a-4808-ac25-2032684f4403", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397365899400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08d836b4-ff16-43f0-9572-6d6dc3da62b3", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397365938600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "431722a2-4c56-456a-93d3-490f52cc66ca", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397347560500, "endTime": 153397365977700}, "additional": {"logType": "info", "children": [], "durationId": "40058cf2-da16-4919-874c-865735297b86", "parent": "095fbb2a-fb11-42f8-922b-1e9d30fc6e62"}}, {"head": {"id": "095fbb2a-fb11-42f8-922b-1e9d30fc6e62", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397340400100, "endTime": 153397365990100}, "additional": {"logType": "info", "children": ["6bba5499-3247-479d-908d-4a6286890e98", "431722a2-4c56-456a-93d3-490f52cc66ca"], "durationId": "b81c68a6-1768-4105-9fcc-a9e41069098f", "parent": "b13061db-10a9-4787-9b92-0e68ce670e18"}}, {"head": {"id": "57dd0f5f-00b5-4e1e-bb30-acdbd1ee0988", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397367710000, "endTime": 153397367728000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "80ed0fe2-7dd2-43f5-9f0c-1620affec774", "logId": "78401978-0131-459a-a055-f46b2ab2965f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78401978-0131-459a-a055-f46b2ab2965f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397367710000, "endTime": 153397367728000}, "additional": {"logType": "info", "children": [], "durationId": "57dd0f5f-00b5-4e1e-bb30-acdbd1ee0988", "parent": "b13061db-10a9-4787-9b92-0e68ce670e18"}}, {"head": {"id": "b13061db-10a9-4787-9b92-0e68ce670e18", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397332017900, "endTime": 153397367745400}, "additional": {"logType": "info", "children": ["52217b2c-e827-4344-b9a5-3257f076c450", "095fbb2a-fb11-42f8-922b-1e9d30fc6e62", "78401978-0131-459a-a055-f46b2ab2965f"], "durationId": "80ed0fe2-7dd2-43f5-9f0c-1620affec774", "parent": "308fd2b8-9c1b-4563-8d47-1719d39ec42c"}}, {"head": {"id": "308fd2b8-9c1b-4563-8d47-1719d39ec42c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397330254800, "endTime": 153397367758100}, "additional": {"logType": "info", "children": ["b13061db-10a9-4787-9b92-0e68ce670e18"], "durationId": "11a5ad90-4a48-4732-84f5-cf6afec1232a", "parent": "4dc9d78e-86e9-494d-b516-9ba8712f4bb7"}}, {"head": {"id": "5229f6bf-2485-4ed5-8744-cdc5177c910c", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397382646500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e416d624-9c1d-446f-ad80-a071b0afa08f", "name": "hvigorfile, resolve hvigorfile dependencies in 47 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397414335600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6772d74-3c36-47b7-bd3a-9f1bf48972b1", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397367765300, "endTime": 153397414496100}, "additional": {"logType": "info", "children": [], "durationId": "58c64f97-03b6-4005-8808-c66a890cb218", "parent": "4dc9d78e-86e9-494d-b516-9ba8712f4bb7"}}, {"head": {"id": "8ee6efa2-ecbb-4822-b62f-1140ae90ce41", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397415382700, "endTime": 153397415579100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "22ccd8fe-646e-4850-b070-38d5921e49d5", "logId": "c68946a0-4e41-4640-9027-ca391d583178"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e9df74d-f1f3-4817-9bb3-a020b5a0ce97", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397415417300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c68946a0-4e41-4640-9027-ca391d583178", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397415382700, "endTime": 153397415579100}, "additional": {"logType": "info", "children": [], "durationId": "8ee6efa2-ecbb-4822-b62f-1140ae90ce41", "parent": "4dc9d78e-86e9-494d-b516-9ba8712f4bb7"}}, {"head": {"id": "9ea2fba2-9fab-4cb7-9089-68825541fa3e", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397417149300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7db3d8f-910d-43ea-b2f1-cd7cc685dc6d", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397427167700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd7e30c4-fa3f-487d-b6ac-224074ceeffa", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397415591000, "endTime": 153397428125100}, "additional": {"logType": "info", "children": [], "durationId": "d326a279-d709-496a-b390-939c144ecbed", "parent": "4dc9d78e-86e9-494d-b516-9ba8712f4bb7"}}, {"head": {"id": "b1eaf968-34f5-47d9-a455-8e7d03214d45", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397428170300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b27ab208-f7b9-49ac-b77c-f3d0d57fd939", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397435972400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ea6a700-da94-4c55-bb8c-7ac29be4f12e", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397436111200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80af1ce4-3aa2-4a0f-85d3-93a0c6a63f06", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397436267400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "035ad889-280a-4a67-b141-9c4352b54910", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397439633600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6546f1ad-f5dc-4963-96fa-50290ba8593e", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397439770600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f813773-cd73-469e-81af-c165a88ec00f", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397428148900, "endTime": 153397443275100}, "additional": {"logType": "info", "children": [], "durationId": "91a5d27b-0f7b-49b4-9785-9ce75327b192", "parent": "4dc9d78e-86e9-494d-b516-9ba8712f4bb7"}}, {"head": {"id": "55ff1efe-50c2-41f9-a91d-60ab3f4271ce", "name": "Configuration phase cost:200 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397443327600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23708649-5b51-4c4e-87cb-096d93224952", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397443298400, "endTime": 153397443450500}, "additional": {"logType": "info", "children": [], "durationId": "94706089-ae0c-4d56-8f5f-d0ef6546ed4d", "parent": "4dc9d78e-86e9-494d-b516-9ba8712f4bb7"}}, {"head": {"id": "4dc9d78e-86e9-494d-b516-9ba8712f4bb7", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397238942100, "endTime": 153397443462200}, "additional": {"logType": "info", "children": ["bfa631fb-de75-4de3-9b74-6f9db2300318", "4252f0d7-1fca-41af-9704-7ef13a658ad8", "0cd54deb-89d7-4bd6-a6e6-8c46c656a6b8", "308fd2b8-9c1b-4563-8d47-1719d39ec42c", "e6772d74-3c36-47b7-bd3a-9f1bf48972b1", "bd7e30c4-fa3f-487d-b6ac-224074ceeffa", "3f813773-cd73-469e-81af-c165a88ec00f", "23708649-5b51-4c4e-87cb-096d93224952", "c68946a0-4e41-4640-9027-ca391d583178"], "durationId": "22ccd8fe-646e-4850-b070-38d5921e49d5", "parent": "1999c07f-7e6d-4692-a859-01fcf2410c2a"}}, {"head": {"id": "021e7d96-40cb-4e23-ab34-62b65a599046", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397444709100, "endTime": 153397444729100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "02fc766d-88f5-4739-a5a8-0d4d147ff271", "logId": "b242cb3b-21a1-42d1-b9c2-a2ab287c452f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b242cb3b-21a1-42d1-b9c2-a2ab287c452f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397444709100, "endTime": 153397444729100}, "additional": {"logType": "info", "children": [], "durationId": "021e7d96-40cb-4e23-ab34-62b65a599046", "parent": "1999c07f-7e6d-4692-a859-01fcf2410c2a"}}, {"head": {"id": "ded48b9e-f484-4836-8c02-a97e65abdfa1", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397443486200, "endTime": 153397444742600}, "additional": {"logType": "info", "children": [], "durationId": "064f2aaa-8c4f-46df-ad2f-24b73c6b1657", "parent": "1999c07f-7e6d-4692-a859-01fcf2410c2a"}}, {"head": {"id": "f0c18bda-56ad-4a5f-b150-dce9769e3f6d", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397444746900, "endTime": 153397444771500}, "additional": {"logType": "info", "children": [], "durationId": "19e906c5-0700-4d64-8933-aa7184b82d66", "parent": "1999c07f-7e6d-4692-a859-01fcf2410c2a"}}, {"head": {"id": "1999c07f-7e6d-4692-a859-01fcf2410c2a", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397226506700, "endTime": 153397444775000}, "additional": {"logType": "info", "children": ["bec5a8ca-1481-464f-95fa-8fd1885e555b", "4dc9d78e-86e9-494d-b516-9ba8712f4bb7", "ded48b9e-f484-4836-8c02-a97e65abdfa1", "f0c18bda-56ad-4a5f-b150-dce9769e3f6d", "21c33724-613b-4867-9c28-a6d0f56437ec", "c8477c61-752b-43f8-a86e-aad49b4bcb7f", "b242cb3b-21a1-42d1-b9c2-a2ab287c452f"], "durationId": "02fc766d-88f5-4739-a5a8-0d4d147ff271"}}, {"head": {"id": "f3dc44c5-256f-4339-a620-bbc73ccacab2", "name": "Configuration task cost before running: 222 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397445037100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56d4877e-47e5-4f4f-ad3b-8fa8099e85ff", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397456406200, "endTime": 153397469346000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "bfb50225-e0a2-477e-9bd8-9b0c898b0f6d", "logId": "b327a3e4-fd48-4632-818c-53b1dcf97eb9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bfb50225-e0a2-477e-9bd8-9b0c898b0f6d", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397447076900}, "additional": {"logType": "detail", "children": [], "durationId": "56d4877e-47e5-4f4f-ad3b-8fa8099e85ff"}}, {"head": {"id": "2391a39e-f98d-448c-95fd-efded3dd1f79", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397447895500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58c32701-5afe-42aa-8d1d-2433622104a5", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397448047800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd5f0173-c9dd-4dd7-b3fe-1d902e05ec53", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397448824600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "197c392f-894b-488f-9b25-a1f2d53511c1", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397449632400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d4d4a03-5240-47a6-9f2e-f9c95bc94e5e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397450639300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfb62a76-1a8e-4ccb-8444-0bf14a58c8e4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397450726000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66cbf735-a4d2-4f89-bb81-21bc7b52dc30", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397456421100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9870313b-7bd6-420e-add4-c199309a5dd8", "name": "Incremental task entry:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397468795200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23b061c6-ff0c-4119-a4c4-764f9a65964b", "name": "entry : default@PreBuild cost memory 0.32178497314453125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397469134400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b327a3e4-fd48-4632-818c-53b1dcf97eb9", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397456406200, "endTime": 153397469346000}, "additional": {"logType": "info", "children": [], "durationId": "56d4877e-47e5-4f4f-ad3b-8fa8099e85ff"}}, {"head": {"id": "6392edf2-520f-4267-b9fd-954779904c24", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397477343100, "endTime": 153397479569200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ad8fad44-7243-402f-ab1a-e2b84ae8c9f9", "logId": "de8dc11d-1b52-4123-b0f8-1b5a9c6f84c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad8fad44-7243-402f-ab1a-e2b84ae8c9f9", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397475365300}, "additional": {"logType": "detail", "children": [], "durationId": "6392edf2-520f-4267-b9fd-954779904c24"}}, {"head": {"id": "a12e94fd-4162-47d0-a42c-d90110ff6d7d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397476556000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "319ed8f8-ac29-40ec-beb8-7b30538420c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397476683300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "177d0946-4831-48b0-8b88-883d4b2786bf", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397477358900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8479a930-eab5-493e-bebe-54dc3e51333a", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397478014600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97b22973-7011-46bc-bebd-798012c173ab", "name": "entry : default@CreateModuleInfo cost memory 0.06027984619140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397479148100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69adbe48-86b5-4709-b812-cf4624540f4b", "name": "runTaskFromQueue task cost before running: 256 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397479423400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de8dc11d-1b52-4123-b0f8-1b5a9c6f84c2", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397477343100, "endTime": 153397479569200, "totalTime": 2022700}, "additional": {"logType": "info", "children": [], "durationId": "6392edf2-520f-4267-b9fd-954779904c24"}}, {"head": {"id": "58805a2e-1fc2-4fb5-84d3-fbf2b282ed3a", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397489239500, "endTime": 153397492630200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ae09fdfe-dab0-432a-993e-26b5b383af79", "logId": "f0a165d8-2a35-404b-9157-b56c7c0bcada"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae09fdfe-dab0-432a-993e-26b5b383af79", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397482012200}, "additional": {"logType": "detail", "children": [], "durationId": "58805a2e-1fc2-4fb5-84d3-fbf2b282ed3a"}}, {"head": {"id": "09d4ddf6-e952-4fef-881f-9d2fee0a0220", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397483261100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6f859c4-e482-4cc3-b596-7adbaf1948b5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397483392200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e82d5b6a-4a30-4de1-87cf-5025077d4004", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397489255000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc92d530-d895-4108-9824-3b68bb29678c", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397490747500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f15f7eef-fb0a-4350-9826-7b11ba2ecad2", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397492438100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faae0be9-b24e-4b0c-b4e6-7f855ba06707", "name": "entry : default@GenerateMetadata cost memory 0.10276031494140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397492567200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0a165d8-2a35-404b-9157-b56c7c0bcada", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397489239500, "endTime": 153397492630200}, "additional": {"logType": "info", "children": [], "durationId": "58805a2e-1fc2-4fb5-84d3-fbf2b282ed3a"}}, {"head": {"id": "3b8b3ebf-56b3-4004-bbbe-63976db205c8", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397496528600, "endTime": 153397496822300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a06dc314-d8e8-4c24-838b-92f2c70491d2", "logId": "7155d862-7eae-4d1f-8964-92b5d2174603"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a06dc314-d8e8-4c24-838b-92f2c70491d2", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397494491100}, "additional": {"logType": "detail", "children": [], "durationId": "3b8b3ebf-56b3-4004-bbbe-63976db205c8"}}, {"head": {"id": "0a4815b6-4253-4580-8719-4243b5157253", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397496251400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c849350b-4575-4807-ae48-260d95434f29", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397496386300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "757c914d-b78d-4c0e-8253-27bc250c750c", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397496536200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a586d67-294c-4be9-a017-64d57214db0e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397496615100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fe7fc49-2e21-4ce4-bd45-f62cbf59f488", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397496652300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5d6be2e-c5e8-4a95-83e7-09d3bdc4fcd0", "name": "entry : default@ConfigureCmake cost memory 0.0374755859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397496711600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "619a383b-38a5-4115-a9d6-f42df3851125", "name": "runTaskFromQueue task cost before running: 274 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397496780200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7155d862-7eae-4d1f-8964-92b5d2174603", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397496528600, "endTime": 153397496822300, "totalTime": 232500}, "additional": {"logType": "info", "children": [], "durationId": "3b8b3ebf-56b3-4004-bbbe-63976db205c8"}}, {"head": {"id": "49cbbf80-38c4-49ef-b2ab-3d8217709f4e", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397501613700, "endTime": 153397505201500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "dd5976e4-8669-4137-bea6-f07d1d2d2e50", "logId": "3b741cad-b9e2-4557-b3ba-3132d6d2335a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd5976e4-8669-4137-bea6-f07d1d2d2e50", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397498699400}, "additional": {"logType": "detail", "children": [], "durationId": "49cbbf80-38c4-49ef-b2ab-3d8217709f4e"}}, {"head": {"id": "da4cc6ce-a1bb-4683-b001-ff90c398ebb2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397500255000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ded0254-690b-4a72-bf56-f13488265041", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397500460700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3983c7c-f40e-495a-a566-e8def195f7a3", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397501627500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0260279-b9fa-4f61-8a77-75385d231619", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397504991600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39d30f66-746d-4c7c-becb-3efece214107", "name": "entry : default@MergeProfile cost memory 0.1185302734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397505131000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b741cad-b9e2-4557-b3ba-3132d6d2335a", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397501613700, "endTime": 153397505201500}, "additional": {"logType": "info", "children": [], "durationId": "49cbbf80-38c4-49ef-b2ab-3d8217709f4e"}}, {"head": {"id": "f662429c-3576-4a6d-aa7d-2940b04aa533", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397508620700, "endTime": 153397511198800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a2de67c8-c894-4c52-ab07-56a95b317306", "logId": "b3d55e3e-6404-4c3f-bf24-3b384fd1a201"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2de67c8-c894-4c52-ab07-56a95b317306", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397506847900}, "additional": {"logType": "detail", "children": [], "durationId": "f662429c-3576-4a6d-aa7d-2940b04aa533"}}, {"head": {"id": "99b5a2fa-476c-49a0-9c27-ff27864bebf1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397507771300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2021aa59-aab9-4f89-adf5-1721c53c48a5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397507883100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e86a731-d877-4762-83d7-b97e524cb0e5", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397508630200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c0e857d-2294-4069-8ba4-b1664529fd32", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397509554400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e28b7279-3dd7-46f8-b955-d3af962d5c00", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397511011600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a902bda-20a8-4c46-9058-b0c7451b0c4b", "name": "entry : default@CreateBuildProfile cost memory 0.10778045654296875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397511135700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3d55e3e-6404-4c3f-bf24-3b384fd1a201", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397508620700, "endTime": 153397511198800}, "additional": {"logType": "info", "children": [], "durationId": "f662429c-3576-4a6d-aa7d-2940b04aa533"}}, {"head": {"id": "c404b804-8c7e-43ad-bdf8-00f5eb66b8ae", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397515762900, "endTime": 153397516448000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "dfa2e59d-3256-4e65-ac81-30416a1dacc2", "logId": "f1ad7619-c393-4a0f-97cb-8ab8edd88cff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfa2e59d-3256-4e65-ac81-30416a1dacc2", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397512974100}, "additional": {"logType": "detail", "children": [], "durationId": "c404b804-8c7e-43ad-bdf8-00f5eb66b8ae"}}, {"head": {"id": "2b23a820-74cb-4980-baf1-64719c1329af", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397514344000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b96920d-53ec-44a9-a074-cbf452a4b427", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397514522500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c282958c-dd4e-4fe7-a397-6f908bbc4aa7", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397515777500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52d7853f-b8dc-4059-acc5-a07e05c99ba2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397515997600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4cde003-4fcc-408d-bf18-ef6db5e48fd6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397516059900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f900387d-94e1-4712-af46-47f08ef74257", "name": "entry : default@PreCheckSyscap cost memory 0.04166412353515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397516287200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55073397-bf8b-43ea-be3e-7ffccea40210", "name": "runTaskFromQueue task cost before running: 293 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397516394300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1ad7619-c393-4a0f-97cb-8ab8edd88cff", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397515762900, "endTime": 153397516448000, "totalTime": 611800}, "additional": {"logType": "info", "children": [], "durationId": "c404b804-8c7e-43ad-bdf8-00f5eb66b8ae"}}, {"head": {"id": "34e9726c-d5c1-478f-a379-3dfe11be19f8", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397524852700, "endTime": 153397533190700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "16f30ee9-abcd-4cb0-b702-115b66a00296", "logId": "d3930fc1-f66b-4bff-b8d1-3cfab469e221"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16f30ee9-abcd-4cb0-b702-115b66a00296", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397522224500}, "additional": {"logType": "detail", "children": [], "durationId": "34e9726c-d5c1-478f-a379-3dfe11be19f8"}}, {"head": {"id": "40741cdb-03a8-437e-a120-aaac24ddfc0a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397523308600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0168f3fa-a655-4524-a087-c9a773929121", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397523445300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9d634f6-51e6-4fec-bbc3-adc2704c2869", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397524865500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da448cb3-b662-4d95-8e1d-2c67541e0dbb", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397532159700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62fd132f-7952-4a5e-8574-188b81827930", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397532974300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "870ff867-096a-4bf7-a5d4-6ae0e425e6da", "name": "entry : default@GeneratePkgContextInfo cost memory 0.37393951416015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397533119200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3930fc1-f66b-4bff-b8d1-3cfab469e221", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397524852700, "endTime": 153397533190700}, "additional": {"logType": "info", "children": [], "durationId": "34e9726c-d5c1-478f-a379-3dfe11be19f8"}}, {"head": {"id": "572914b2-6f77-42c5-a86e-57244713bb19", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397542140000, "endTime": 153397544490800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "87852ae4-1289-4241-8bee-099ec7e44d24", "logId": "b33e45b7-e333-4716-9a47-a7e811aced94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87852ae4-1289-4241-8bee-099ec7e44d24", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397535431500}, "additional": {"logType": "detail", "children": [], "durationId": "572914b2-6f77-42c5-a86e-57244713bb19"}}, {"head": {"id": "d12f3764-1380-4b2f-9038-f2d979468143", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397536800200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1907041-ac62-477d-a100-018721f4771e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397536942700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f16ddf82-771c-45e8-8912-9a266a5febc6", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397542153000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1d1a984-143d-4b6b-b4ad-f2776e35f54c", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397544066100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53eeba0f-40d9-4acb-8beb-a3ff6e69c7b0", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397544203600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fe3740e-e058-4375-90e1-363c97b43277", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397544287400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d4beecd-009f-4e19-8099-9793fb583b09", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397544328200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdfb0e0a-f7b5-4b49-99d3-f1f90cfd6922", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12093353271484375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397544394700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fad9d576-3e55-4477-8f0d-3d75d6af068e", "name": "runTaskFromQueue task cost before running: 321 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397544453100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b33e45b7-e333-4716-9a47-a7e811aced94", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397542140000, "endTime": 153397544490800, "totalTime": 2301800}, "additional": {"logType": "info", "children": [], "durationId": "572914b2-6f77-42c5-a86e-57244713bb19"}}, {"head": {"id": "41cfdbe8-264d-4d62-b3c5-40b42a3821fa", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397549444400, "endTime": 153397549800100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "b25db125-972a-441a-b298-6480ef597871", "logId": "023cc8c9-974c-4fe9-a857-c761225a9bff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b25db125-972a-441a-b298-6480ef597871", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397547440700}, "additional": {"logType": "detail", "children": [], "durationId": "41cfdbe8-264d-4d62-b3c5-40b42a3821fa"}}, {"head": {"id": "b38cd399-10a5-4803-aea1-52e0c3fd76e8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397548597700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d71ab15-11ed-42a4-aba2-1d3ebd7596a8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397548720300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b15f255f-f054-4cbb-baee-ad3ef6b2ba80", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397549454700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b199afcd-548a-41e5-adfd-33fe6d3b40a8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397549589400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b888ad45-c0f7-414c-aaed-6ec857d88c4e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397549637000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e5c23ac-2d52-4d0d-b90f-c80caa16bed2", "name": "entry : default@BuildNativeWithCmake cost memory 0.03855133056640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397549699000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4a4e951-aaf9-4110-b4ee-dc07a3619587", "name": "runTaskFromQueue task cost before running: 327 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397549762800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "023cc8c9-974c-4fe9-a857-c761225a9bff", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397549444400, "endTime": 153397549800100, "totalTime": 299800}, "additional": {"logType": "info", "children": [], "durationId": "41cfdbe8-264d-4d62-b3c5-40b42a3821fa"}}, {"head": {"id": "30ed6f10-d1a6-4514-b1ff-3e5bf8d432d8", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397553074800, "endTime": 153397557190300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5e594f00-9377-4cb0-a332-6a620122e7a8", "logId": "b314ac7e-98b0-4a5d-9077-7d644433aa29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e594f00-9377-4cb0-a332-6a620122e7a8", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397551243500}, "additional": {"logType": "detail", "children": [], "durationId": "30ed6f10-d1a6-4514-b1ff-3e5bf8d432d8"}}, {"head": {"id": "0f944d7a-a527-40ef-93cd-656f366ef97c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397552236000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ca36326-8677-4a82-b0ec-3cb4ffb64ecb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397552344300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4ee3478-0fe3-46ff-b5ef-6ec6eed584f6", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397553083800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6b9ffe3-c3f7-4c82-a36d-6f512348e13b", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397556986500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bedc777-09ee-478b-9a57-ca2c45604561", "name": "entry : default@MakePackInfo cost memory 0.163848876953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397557123600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b314ac7e-98b0-4a5d-9077-7d644433aa29", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397553074800, "endTime": 153397557190300}, "additional": {"logType": "info", "children": [], "durationId": "30ed6f10-d1a6-4514-b1ff-3e5bf8d432d8"}}, {"head": {"id": "2984b1d2-89b2-4801-a267-9ecd02c5beba", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397564162500, "endTime": 153397568944000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "512c667d-188a-4487-879c-8ad166635c01", "logId": "c375696a-59f1-499c-b37e-2d5d0e4e0bfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "512c667d-188a-4487-879c-8ad166635c01", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397560063000}, "additional": {"logType": "detail", "children": [], "durationId": "2984b1d2-89b2-4801-a267-9ecd02c5beba"}}, {"head": {"id": "44c24bde-3c5f-4ed3-a322-84626ad0adf6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397561397900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6a9468a-9849-4832-881a-9fbd6dee03ae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397561559200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e002325b-9209-4469-bb7b-d65d01fd220e", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397564179500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c8783a5-1b3a-454c-a73e-e8eaa4997a93", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397564544000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "361c7938-960f-4641-8d2a-c0b2907f00bb", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397565909200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f405edc4-e15a-446c-85a1-12686ce894bd", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397568715800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3945cf2-c61b-4710-9b3f-f75eb3769ef1", "name": "entry : default@SyscapTransform cost memory 0.15118408203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397568866600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c375696a-59f1-499c-b37e-2d5d0e4e0bfb", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397564162500, "endTime": 153397568944000}, "additional": {"logType": "info", "children": [], "durationId": "2984b1d2-89b2-4801-a267-9ecd02c5beba"}}, {"head": {"id": "23389377-a711-481b-93a5-4899e1bc2fe1", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397573259400, "endTime": 153397575217300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "15b7c831-c1c7-4d48-85ef-931a33f85a00", "logId": "815e95a1-888c-4765-bce5-a255d24ef58e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15b7c831-c1c7-4d48-85ef-931a33f85a00", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397570676300}, "additional": {"logType": "detail", "children": [], "durationId": "23389377-a711-481b-93a5-4899e1bc2fe1"}}, {"head": {"id": "9fa87331-5dd4-43b9-9e01-4f98123ad7fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397571850800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dfbec5e-bf0b-437f-b58f-1e7ceb91e727", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397571987500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5304e459-c106-455e-84f0-0a65403713d5", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397573271600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6273ca92-5acb-4446-b0c5-c2b422ef66ed", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397575024100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76a2d003-45a0-4074-be19-737763c56e61", "name": "entry : default@ProcessProfile cost memory 0.13010406494140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397575158300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "815e95a1-888c-4765-bce5-a255d24ef58e", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397573259400, "endTime": 153397575217300}, "additional": {"logType": "info", "children": [], "durationId": "23389377-a711-481b-93a5-4899e1bc2fe1"}}, {"head": {"id": "64c06beb-a656-421f-98ad-617dec8e00c6", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397580727000, "endTime": 153397589363200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8346a8c2-5510-41c8-bca9-093b423a3864", "logId": "c3ac5c9c-c6f9-411c-b4a4-545b640b7c02"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8346a8c2-5510-41c8-bca9-093b423a3864", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397577029600}, "additional": {"logType": "detail", "children": [], "durationId": "64c06beb-a656-421f-98ad-617dec8e00c6"}}, {"head": {"id": "19ae9ac2-c2e7-4d19-b791-843e5af2d304", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397578091500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f9a888e-fe00-4d4d-b111-3d8549fe2409", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397578222700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e45523a3-1566-4bf1-b60f-bd8fa4591e72", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397580749300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac933ac6-69ed-434d-bd22-05feb3f80567", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397589118200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f17c49c-b384-491f-81f1-cb31a58c9ada", "name": "entry : default@ProcessRouterMap cost memory 0.**********", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397589285000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3ac5c9c-c6f9-411c-b4a4-545b640b7c02", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397580727000, "endTime": 153397589363200}, "additional": {"logType": "info", "children": [], "durationId": "64c06beb-a656-421f-98ad-617dec8e00c6"}}, {"head": {"id": "3c77dd65-eb1f-4d2b-8beb-b4287b770384", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397594091400, "endTime": 153397602454300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "f7acdce4-b9ec-416f-ae28-22426fc75705", "logId": "4283ec3b-158b-433a-890a-e8009fb322ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7acdce4-b9ec-416f-ae28-22426fc75705", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397592489400}, "additional": {"logType": "detail", "children": [], "durationId": "3c77dd65-eb1f-4d2b-8beb-b4287b770384"}}, {"head": {"id": "47a59452-861e-4c39-8ba0-f670f3accb48", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397593842200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b93b345-eb67-420b-891e-c78942140d01", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397593989900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8650a5b3-282a-442f-a522-70f087dec13d", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397594099500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17b76940-0e6f-47e6-b141-a905ee89d729", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397594195900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b52e6e17-4b3e-4e69-89c7-4104cc3c18ab", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397600540300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "323c59d7-b0a4-492d-b8e0-96a23c5f61d8", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397600711000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99c41f4c-4122-4501-b08b-7fd564a3736a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397600805600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c832467-2ebc-4532-aae4-3ce35f78826c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397600845000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73cca0a0-efb3-4958-a65a-4ae6a75cc8c4", "name": "entry : default@ProcessStartupConfig cost memory 0.25910186767578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397602217500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1bf45ed-904e-4800-9316-0c3269b2cd86", "name": "runTaskFromQueue task cost before running: 379 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397602392100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4283ec3b-158b-433a-890a-e8009fb322ff", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397594091400, "endTime": 153397602454300, "totalTime": 8266200}, "additional": {"logType": "info", "children": [], "durationId": "3c77dd65-eb1f-4d2b-8beb-b4287b770384"}}, {"head": {"id": "adb8b5c3-51a7-4cfa-8083-bc7fab9377aa", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397608133300, "endTime": 153397609518400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "76f51e70-7640-4fc8-bd80-88fe37f690fb", "logId": "e9aba766-8019-4c06-97f8-3a3b795deb90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76f51e70-7640-4fc8-bd80-88fe37f690fb", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397605819600}, "additional": {"logType": "detail", "children": [], "durationId": "adb8b5c3-51a7-4cfa-8083-bc7fab9377aa"}}, {"head": {"id": "307587b5-07b7-45cb-a573-ff74b7fd9f5e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397607156700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b8303b0-65b8-4d9c-a56c-2eca5b0c2c30", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397607286800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16ec47b9-64cc-4cdc-84cb-b9bb99fb7fc9", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397608144500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c6a2f22-c8cf-4c4f-82a4-44b9e7819072", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397608287700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5002b617-31a2-4deb-a556-2bf6e34fe9db", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397608341200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b26b37d6-f729-483e-8741-c2fb9a33504d", "name": "entry : default@BuildNativeWithNinja cost memory 0.05841827392578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397609317300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e3c02c7-21df-4658-af4a-006464f43126", "name": "runTaskFromQueue task cost before running: 386 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397609464100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9aba766-8019-4c06-97f8-3a3b795deb90", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397608133300, "endTime": 153397609518400, "totalTime": 1306000}, "additional": {"logType": "info", "children": [], "durationId": "adb8b5c3-51a7-4cfa-8083-bc7fab9377aa"}}, {"head": {"id": "b2a8fb3c-e244-4a7f-b41f-73de4c8969f0", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397616279900, "endTime": 153397621940900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "cdcbd8c8-7b71-4ebd-8efb-94e67f2b0857", "logId": "91438b9f-94ac-4e81-b1cd-18f4b0c88468"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdcbd8c8-7b71-4ebd-8efb-94e67f2b0857", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397611972200}, "additional": {"logType": "detail", "children": [], "durationId": "b2a8fb3c-e244-4a7f-b41f-73de4c8969f0"}}, {"head": {"id": "b8baed31-2853-4fea-be57-1948dced67ce", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397613453300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f966313-b57d-40fa-970f-6d6cd76bfeab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397613641200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64e0f254-c157-4bdd-9d50-f17236e9f037", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397614870800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "542030c6-307f-418e-8e4a-25b931adbb97", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397617936100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba074ca1-b410-4a23-81fd-50a519c88c14", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397620041600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33561fe5-b1bb-4147-aa7e-429173c93ea8", "name": "entry : default@ProcessResource cost memory 0.1620025634765625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397620185800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91438b9f-94ac-4e81-b1cd-18f4b0c88468", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397616279900, "endTime": 153397621940900}, "additional": {"logType": "info", "children": [], "durationId": "b2a8fb3c-e244-4a7f-b41f-73de4c8969f0"}}, {"head": {"id": "265e7558-16e3-4251-be03-e3c0a2eae4e1", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397630868700, "endTime": 153397654090400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3d1c336c-d12d-4430-811b-a5b4158c8b2d", "logId": "3567e010-9160-4d4d-ac9b-69dba29e1a55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d1c336c-d12d-4430-811b-a5b4158c8b2d", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397626191300}, "additional": {"logType": "detail", "children": [], "durationId": "265e7558-16e3-4251-be03-e3c0a2eae4e1"}}, {"head": {"id": "d0e80b3a-77e1-46e1-80d7-01d622b85f78", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397627357200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5780cec6-a357-4d9f-af99-c419ccb0a4cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397627471800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b65f583a-2b22-4238-879e-46baa776d55b", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397630881000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6be81a92-9805-42b7-ad0d-fa8e8ad12107", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397653848600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e305b5a6-741b-4227-96cd-7f6cec377fea", "name": "entry : default@GenerateLoaderJson cost memory 0.8777999877929688", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397654021400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3567e010-9160-4d4d-ac9b-69dba29e1a55", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397630868700, "endTime": 153397654090400}, "additional": {"logType": "info", "children": [], "durationId": "265e7558-16e3-4251-be03-e3c0a2eae4e1"}}, {"head": {"id": "d4420920-d863-4cab-afb0-a809b1b155aa", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397665807800, "endTime": 153397671115700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "3108da72-58ab-4ee3-ac7f-c7dda9a3d831", "logId": "216e5c3c-83bc-4365-98bc-7614cd599041"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3108da72-58ab-4ee3-ac7f-c7dda9a3d831", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397663743700}, "additional": {"logType": "detail", "children": [], "durationId": "d4420920-d863-4cab-afb0-a809b1b155aa"}}, {"head": {"id": "42b85d1b-4e5d-4271-a143-57de5944fac3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397664912100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d6942a7-aa69-4b7e-a778-ec623c420cc5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397665042500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feec4961-0df0-4e58-ae67-08d5a9c02514", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397665818000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4286de2-f1bd-4f20-a340-218e14b3de94", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397670889800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fdd6b54-73b3-41bb-88b5-3784a52b3f25", "name": "entry : default@ProcessLibs cost memory 0.14200592041015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397671041700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "216e5c3c-83bc-4365-98bc-7614cd599041", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397665807800, "endTime": 153397671115700}, "additional": {"logType": "info", "children": [], "durationId": "d4420920-d863-4cab-afb0-a809b1b155aa"}}, {"head": {"id": "12cbd5cf-394e-40fb-863d-92c41cc39a6b", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397678159100, "endTime": 153397723963600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ab623658-5601-4061-a60c-744cb0cd7e22", "logId": "5d1d93ee-36c0-44fe-a64f-e6241344a8c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab623658-5601-4061-a60c-744cb0cd7e22", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397673178100}, "additional": {"logType": "detail", "children": [], "durationId": "12cbd5cf-394e-40fb-863d-92c41cc39a6b"}}, {"head": {"id": "c460ac3d-8f8c-4e5c-a4ef-18b4c9c00518", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397674237100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2b3a244-999b-46a5-b524-e3a615ae3a6e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397674367000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10e0fa90-54d0-46b7-8bfe-1c76f4628f3d", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397675441500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5b40154-541d-4e39-b06f-67874a03e0f9", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397678187600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5645388-8fad-4c05-9d88-c51db4a50055", "name": "Incremental task entry:default@CompileResource pre-execution cost: 44 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397723706900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a1fe1c9-a282-463c-a2ed-1e3473fc740a", "name": "entry : default@CompileResource cost memory 1.3154373168945312", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397723868100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d1d93ee-36c0-44fe-a64f-e6241344a8c3", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397678159100, "endTime": 153397723963600}, "additional": {"logType": "info", "children": [], "durationId": "12cbd5cf-394e-40fb-863d-92c41cc39a6b"}}, {"head": {"id": "1a2c5128-5159-471e-b664-cc95618518ec", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397731838500, "endTime": 153397734295400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "311aed48-3d44-4156-8f46-e9401cc30021", "logId": "2147341b-8a02-4f82-993a-9e926daed650"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "311aed48-3d44-4156-8f46-e9401cc30021", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397726883000}, "additional": {"logType": "detail", "children": [], "durationId": "1a2c5128-5159-471e-b664-cc95618518ec"}}, {"head": {"id": "6e4db8dc-8865-4051-bde6-2cde9d7eeab9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397728161300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12bac183-4726-4ded-adf8-046574003110", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397728295200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07ac4816-ebf3-4d4e-a676-ccd900f72cc5", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397731851700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80329334-0d23-4d90-a0b5-ec211dc1f943", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397732419800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e3c720e-3b69-4751-ae01-853c6084ebaf", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397734083100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a204b9cf-6525-46d6-af9a-388a71aec768", "name": "entry : default@DoNativeStrip cost memory 0.07973480224609375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397734224900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2147341b-8a02-4f82-993a-9e926daed650", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397731838500, "endTime": 153397734295400}, "additional": {"logType": "info", "children": [], "durationId": "1a2c5128-5159-471e-b664-cc95618518ec"}}, {"head": {"id": "3587ac36-01d2-44a2-b236-40ff8478cba7", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397742434300, "endTime": 153397779875900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "4e130f56-e891-4798-978e-6ed042feb120", "logId": "20bb6867-a720-497b-83d7-7a5ae47a9f22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e130f56-e891-4798-978e-6ed042feb120", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397735949000}, "additional": {"logType": "detail", "children": [], "durationId": "3587ac36-01d2-44a2-b236-40ff8478cba7"}}, {"head": {"id": "c7f056cb-0146-48c2-9021-3b2ecdaa7bf9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397737403700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d6ed4ad-3e7e-48c6-a285-ce18133e994d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397737545600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c571b88-ceb6-469c-b178-ec2755ab2dde", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397742445600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c37e7f0b-eda9-44d7-a3a7-d6db23d8d89a", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397742605900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79fc1ca4-7d15-4154-8b04-394cd602651c", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397779544800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eff7c392-97aa-485e-8cb0-84fbc6fe8751", "name": "entry : default@CompileArkTS cost memory -8.354751586914062", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397779786000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20bb6867-a720-497b-83d7-7a5ae47a9f22", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397742434300, "endTime": 153397779875900}, "additional": {"logType": "info", "children": [], "durationId": "3587ac36-01d2-44a2-b236-40ff8478cba7"}}, {"head": {"id": "669675f1-f858-4b40-a4c4-c90543acfc8f", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397793135000, "endTime": 153397800972900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "ed95f836-d526-46cf-9c92-5e2be21a9d87", "logId": "d086a564-db41-46a0-a517-3c559ed68d1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed95f836-d526-46cf-9c92-5e2be21a9d87", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397787086800}, "additional": {"logType": "detail", "children": [], "durationId": "669675f1-f858-4b40-a4c4-c90543acfc8f"}}, {"head": {"id": "3578ba7e-9ea0-4361-a23b-bbb9b9107f14", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397788171900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30e7815e-4f4b-4412-99fe-600b3d771067", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397788300000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1fd5178-f744-460e-90af-e6bbc6cfa880", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397793147500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4df0f72-3dc1-4ebb-b936-e5aab9fce43d", "name": "entry : default@BuildJS cost memory 0.3436431884765625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397800763100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f1c91d2-30f9-4684-9390-444aa01aecde", "name": "runTaskFromQueue task cost before running: 578 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397800915000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d086a564-db41-46a0-a517-3c559ed68d1d", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397793135000, "endTime": 153397800972900, "totalTime": 7757700}, "additional": {"logType": "info", "children": [], "durationId": "669675f1-f858-4b40-a4c4-c90543acfc8f"}}, {"head": {"id": "a6cc59c7-3c26-40ed-a82d-1dccef8495be", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397805790500, "endTime": 153397808670300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c7e03013-f1ae-49a6-98f9-f5659870df81", "logId": "3d7a755e-274c-407c-912b-188c39275847"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7e03013-f1ae-49a6-98f9-f5659870df81", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397802435400}, "additional": {"logType": "detail", "children": [], "durationId": "a6cc59c7-3c26-40ed-a82d-1dccef8495be"}}, {"head": {"id": "42b711dc-ce53-465e-90ba-a16140483ea4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397803400200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a3f678e-8552-47ab-86b3-7fc3f920e944", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397803520200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1c521ee-cce3-4583-bbd9-4bf42beb7cdd", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397805802900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c5268bd-c670-451b-a2e4-2ee4ac07c0b2", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397806550800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae0e01bd-56fe-4056-bdbe-1f380a822d65", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397808442800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c958dae-3662-4395-87d1-2e4970b4969f", "name": "entry : default@CacheNativeLibs cost memory 0.0946807861328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397808575800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d7a755e-274c-407c-912b-188c39275847", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397805790500, "endTime": 153397808670300}, "additional": {"logType": "info", "children": [], "durationId": "a6cc59c7-3c26-40ed-a82d-1dccef8495be"}}, {"head": {"id": "e068fd73-6f61-4574-9757-d4c67004f2bd", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397812355800, "endTime": 153397813703900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "7b6fdf67-3de1-4c32-9101-a5f0475718c3", "logId": "598b46f4-2cfa-42de-b803-687abd44b853"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b6fdf67-3de1-4c32-9101-a5f0475718c3", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397810433800}, "additional": {"logType": "detail", "children": [], "durationId": "e068fd73-6f61-4574-9757-d4c67004f2bd"}}, {"head": {"id": "03abb5f7-87e2-4d8f-a5ef-4e064589044f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397811318500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd32c2bd-f43a-4ef8-8455-b069a52256b2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397811435700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6859c57-ed9e-4152-a88c-dae7e0eb0810", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397812367300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8487d64b-ec20-4d02-b3f4-e1f2402f9dce", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397812731700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f47505b-ed05-4692-8b10-6f2636663dd6", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397813537400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c9655c6-8a20-464b-be4b-15d31e96b47c", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07462310791015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397813648500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "598b46f4-2cfa-42de-b803-687abd44b853", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397812355800, "endTime": 153397813703900}, "additional": {"logType": "info", "children": [], "durationId": "e068fd73-6f61-4574-9757-d4c67004f2bd"}}, {"head": {"id": "997423e4-2f4d-4d31-8d74-ec5b6448bc2f", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397823394300, "endTime": 153397842364100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "83cbf4d4-3d47-45af-90e6-65e5c3fcfa04", "logId": "a395e1bb-6242-4b2c-a464-e060958f9884"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83cbf4d4-3d47-45af-90e6-65e5c3fcfa04", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397815685400}, "additional": {"logType": "detail", "children": [], "durationId": "997423e4-2f4d-4d31-8d74-ec5b6448bc2f"}}, {"head": {"id": "7efbb575-e21c-4645-9e54-3ee06efa6f22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397816576900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bd9117e-fdbf-449b-8b94-5edcc5b8dfd8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397816691700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f56eb9d6-95c1-49db-a9af-519ac373a5b3", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397823407900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54b9db4a-229d-4861-acfc-c819c8daa59d", "name": "Incremental task entry:default@PackageHap pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397842130000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1a95904-bf32-474b-a52f-505c799ae2b0", "name": "entry : default@PackageHap cost memory 0.944061279296875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397842295700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a395e1bb-6242-4b2c-a464-e060958f9884", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397823394300, "endTime": 153397842364100}, "additional": {"logType": "info", "children": [], "durationId": "997423e4-2f4d-4d31-8d74-ec5b6448bc2f"}}, {"head": {"id": "f7f7dfbb-b733-4680-90cd-c757ddd39a96", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397849694800, "endTime": 153397852107900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": [], "detailId": "d3d75d2c-89dc-4491-a018-357b073f0ac8", "logId": "b663ab20-042f-416f-9fe3-802eb46715a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3d75d2c-89dc-4491-a018-357b073f0ac8", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397846315400}, "additional": {"logType": "detail", "children": [], "durationId": "f7f7dfbb-b733-4680-90cd-c757ddd39a96"}}, {"head": {"id": "4c867000-5203-46f7-a6be-6c88982767a6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397847453000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "456a63be-08a3-47c4-9e46-4d7d2a7d2d62", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397847588100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b06edc28-6c8e-416b-a895-336aff81175e", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397849706600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5a8c45c-242c-44b4-b333-a95bb272e82c", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397851926200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1937e4d2-528f-47de-89e6-c5348e8f2f85", "name": "entry : default@SignHap cost memory 0.1052093505859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397852046200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b663ab20-042f-416f-9fe3-802eb46715a9", "name": "UP-TO-DATE :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397849694800, "endTime": 153397852107900}, "additional": {"logType": "info", "children": [], "durationId": "f7f7dfbb-b733-4680-90cd-c757ddd39a96"}}, {"head": {"id": "c6675c6b-635e-4bda-b283-cc0c1d5d311a", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397855450700, "endTime": 153397860666100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9f394f3c-c665-46c6-a466-60a0ea4284a2", "logId": "92b56a1b-8e3f-435e-983c-481b38369a20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f394f3c-c665-46c6-a466-60a0ea4284a2", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397853745300}, "additional": {"logType": "detail", "children": [], "durationId": "c6675c6b-635e-4bda-b283-cc0c1d5d311a"}}, {"head": {"id": "148c496f-82a6-4556-9527-ac38e5512177", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397854632100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83446f7c-8c07-497f-8272-bf97f679cb3c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397854736500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e70a28b6-adce-46fb-99b5-a11aba439007", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397855459100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "430468fd-93e4-4a6b-8229-4d5240720942", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397860387900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc220ef3-fcc1-4200-b911-ce0fb5613078", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397860495200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2412e6f1-3e09-481e-b42a-839dc339a0fe", "name": "entry : default@CollectDebugSymbol cost memory 0.24228668212890625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397860562400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "048d0529-b808-4cf4-a638-de12b66b7d1d", "name": "runTaskFromQueue task cost before running: 638 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397860624300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92b56a1b-8e3f-435e-983c-481b38369a20", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397855450700, "endTime": 153397860666100, "totalTime": 5154900}, "additional": {"logType": "info", "children": [], "durationId": "c6675c6b-635e-4bda-b283-cc0c1d5d311a"}}, {"head": {"id": "5a0bcf50-43f4-4598-a3ff-41ac9bb30959", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397862108700, "endTime": 153397862346900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "f394d77d-8eaf-4122-9ea5-6e72464bd760", "logId": "767fda1b-7829-4f8a-bb1d-0594f1924843"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f394d77d-8eaf-4122-9ea5-6e72464bd760", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397862068200}, "additional": {"logType": "detail", "children": [], "durationId": "5a0bcf50-43f4-4598-a3ff-41ac9bb30959"}}, {"head": {"id": "b2d3e426-38e5-4e3b-9049-5628920aa020", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397862115300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab8f93c4-1d8b-457b-be53-0601a27fcc8a", "name": "entry : assembleHap cost memory 0.011749267578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397862227800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d94fed7-e556-4f60-bde3-5f2ff7b0311c", "name": "runTaskFromQueue task cost before running: 639 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397862304400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "767fda1b-7829-4f8a-bb1d-0594f1924843", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397862108700, "endTime": 153397862346900, "totalTime": 180700}, "additional": {"logType": "info", "children": [], "durationId": "5a0bcf50-43f4-4598-a3ff-41ac9bb30959"}}, {"head": {"id": "68f6ca3d-e2f5-414e-a869-206317af8a6c", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397874163200, "endTime": 153397874193300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b33386aa-7df6-4047-8a37-1533b47a7898", "logId": "6df30bee-c192-40f0-b55a-a4a397249241"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6df30bee-c192-40f0-b55a-a4a397249241", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397874163200, "endTime": 153397874193300}, "additional": {"logType": "info", "children": [], "durationId": "68f6ca3d-e2f5-414e-a869-206317af8a6c"}}, {"head": {"id": "495eb47c-32c9-4ccd-858a-64f67f6e232e", "name": "BUILD SUCCESSFUL in 651 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397874238400}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "b7364dec-e575-49c6-96d7-c8f767860803", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397223512100, "endTime": 153397874512900}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 33, "second": 47}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "39b8b15e-9e0c-4f70-8be5-ec65d70daf26", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397874540300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f2baefe-5f23-4de1-a329-588b0d1e8b34", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397874612500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dff02a84-4eb8-4358-a275-bda91aa0ccf8", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397875024500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d67fe979-4910-4ae9-9bf3-7bd51a125314", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397875214600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddffefb2-6547-426c-8241-a8180e9ee430", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397875297000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36d7b63b-278c-4810-a942-1c8599883144", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397875334500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca47e1c9-076d-40e7-af0d-2f8591886142", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397875373500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03f80725-bc56-4a56-9a60-d8fced88a067", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397876068300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f740c62d-5981-4f35-861b-2320570cbcfb", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397876309000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcceaa7f-12a8-4c06-a918-89854d78389e", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397876363100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bda186f7-f9ac-4bcf-a737-73b1b9e3e8da", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397876392800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d55e3be-bc55-450d-9582-314514a47c73", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397876416800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "961eabfc-5bb4-4d85-aa75-dc3ecb9bdd25", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397876441800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07a8f98c-e78a-4dd2-9ee8-cea56c730e3d", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397877493400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b92da681-5b74-4b57-9bc3-a521ed460a3b", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397877772900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeee55d9-9f15-4aa4-a561-2d196fe56cb7", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397877988800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f79ae043-05b5-4846-8915-07c0cec62e3e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397878043600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fab2b25-691f-4793-889c-f75813b081f5", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397878077100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd6380d1-95aa-4659-99ac-6209154f80ae", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397878105700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eb899d3-dfa4-451a-85b6-c7c71d2c28da", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397878129600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb1c4cc1-c14a-4b2d-bcdd-713946e2f231", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397878154400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9df56978-9139-4bed-94cd-b02b19d8b359", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397878178300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02c3b3b0-c8d6-44d3-9209-25a8a6c126da", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397880548100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff5e99dc-bb3d-4bad-94e7-b1180ae2ae22", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397881438600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e21e52c5-01b3-4c82-94da-d3e133b6d979", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397881914600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "639a5541-7b36-44a7-80fb-57226c8c1560", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397882185400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c905eef-60ae-4193-975e-aa027f6d4752", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397882411300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "510e8024-8bc0-458c-b432-fbf163328d9c", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397883078600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0791546b-5d63-41ad-984c-54364e4328d1", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397883982300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a439567-8fff-49ac-a1b7-a829786782a0", "name": "Incremental task entry:default@BuildJS post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397884253200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7e66674-1d71-4077-abc8-1481224a71e2", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397884318200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "909bf86c-1bcb-4838-994a-50620c37bae9", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397884351600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8aefa840-62e4-4ec9-ae77-199a<PERSON><PERSON><PERSON>de", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397884384900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "255c0929-32ad-4eaf-b8b0-b23248e34ebd", "name": "There is no need to refresh cache, since the incremental task entry:default@SignHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397884411000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75e8f247-94a9-4264-bf79-327fea0cd4b7", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397886785100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fdbc45b-2fa3-4d17-9437-40ddd455a5de", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397887147700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acb59433-032c-4508-8097-bedfa4cf1892", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397887644200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb3f529b-362d-4c9e-9ade-2f75d4f7eb12", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397888190500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}