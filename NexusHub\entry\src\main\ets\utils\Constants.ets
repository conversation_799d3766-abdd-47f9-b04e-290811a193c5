/**
 * 颜色配置接口
 */
interface ColorConfig {
  PRIMARY: string;
  PRIMARY_DARK: string;
  SECONDARY: string;
  BACKGROUND: Resource | string;
  BACKGROUND_LIGHT: Resource | string;
  WHITE: string;
  TEXT_PRIMARY: Resource | string;
  TEXT_SECONDARY: Resource | string;
  TEXT_TERTIARY: Resource | string;
  TEXT_HINT: Resource | string;
  BORDER: Resource | string;
  SUCCESS: string;
  WARNING: string;
  ERROR: string;
  INFO: string;
  SHADOW: Resource | string;
  TRANSPARENT: string;
  RATING: string;
}

/**
 * 字体大小配置接口
 */
interface FontSizeConfig {
  SMALL: number;
  NORMAL: number;
  MEDIUM: number;
  LARGE: number;
  XLARGE: number;
  XXLARGE: number;
  EXTRA_LARGE: number;
}

/**
 * 间距配置接口
 */
interface SpacingConfig {
  TINY: number;
  SMALL: number;
  MEDIUM: number;
  LARGE: number;
  XLARGE: number;
  XXLARGE: number;
  HUGE: number;
  EXTRA_LARGE: number;
}

/**
 * 圆角配置接口
 */
interface BorderRadiusConfig {
  SMALL: number;
  MEDIUM: number;
  LARGE: number;
  XLARGE: number;
}

/**
 * 设备类型配置接口
 */
interface DeviceTypeConfig {
  PHONE: string;
  TABLET: string;
  TWO_IN_ONE: string;
  WEARABLE: string;
  TV: string;
  CAR: string;
  SMART_VISION: string;
}

/**
 * 路由配置接口
 */
interface RoutesConfig {
  INDEX: string;
  HOME: string;
  CATEGORY: string;
  SEARCH: string;
  APP_DETAIL: string;
  DOWNLOAD: string;
  PROFILE: string;
}

/**
 * 应用状态配置接口
 */
interface AppStatusConfig {
  PUBLISHED: string;
  PENDING: string;
  REJECTED: string;
  DRAFT: string;
}

/**
 * 排序类型配置接口
 */
interface SortTypeConfig {
  NEWEST: string;
  POPULAR: string;
  RATING: string;
}

/**
 * 动画时长配置接口
 */
interface AnimationDurationConfig {
  FAST: number;
  NORMAL: number;
  SLOW: number;
}

/**
 * 本地存储键名配置接口
 */
interface StorageKeysConfig {
  TOKEN: string;
  USER_INFO: string;
  SEARCH_HISTORY: string;
  DOWNLOAD_HISTORY: string;
  DARK_MODE_FOLLOW_SYSTEM: string;
  DARK_MODE_MANUAL: string;
}

/**
 * 应用常量定义
 */
export class Constants {
  // API配置
  static readonly BASE_URL = 'http://172.20.10.3:8080';
  static readonly API_VERSION = '/api/v1';
  static readonly API_BASE_URL = `${Constants.BASE_URL}${Constants.API_VERSION}`;

  // 颜色主题 - 使用系统资源颜色支持深色模式
  static readonly COLORS: ColorConfig = {
    PRIMARY: '#007DFF',
    PRIMARY_DARK: '#0056CC',
    SECONDARY: '#36D1DC',
    BACKGROUND: $r('sys.color.ohos_id_color_background'),
    BACKGROUND_LIGHT: $r('app.color.background_secondary'),
    WHITE: '#FFFFFF',
    TEXT_PRIMARY: $r('sys.color.ohos_id_color_text_primary'),
    TEXT_SECONDARY: $r('sys.color.ohos_id_color_text_secondary'),
    TEXT_TERTIARY: $r('app.color.text_hint'),
    TEXT_HINT: $r('sys.color.ohos_id_color_text_hint'),
    BORDER: $r('sys.color.ohos_id_color_list_separator'),
    SUCCESS: '#52C41A',
    WARNING: '#FAAD14',
    ERROR: '#FF4D4F',
    INFO: '#1890FF',
    SHADOW: $r('sys.color.ohos_id_color_foreground_contrary'),
    TRANSPARENT: 'transparent',
    RATING: '#FFD700'
  };

  // 字体大小
  static readonly FONT_SIZE: FontSizeConfig = {
    SMALL: 12,
    NORMAL: 14,
    MEDIUM: 16,
    LARGE: 18,
    XLARGE: 20,
    XXLARGE: 24,
    EXTRA_LARGE: 28
  };

  // 间距
  static readonly SPACING: SpacingConfig = {
    TINY: 4,
    SMALL: 8,
    MEDIUM: 12,
    LARGE: 16,
    XLARGE: 20,
    XXLARGE: 24,
    HUGE: 32,
    EXTRA_LARGE: 40
  };

  // 圆角
  static readonly BORDER_RADIUS: BorderRadiusConfig = {
    SMALL: 4,
    MEDIUM: 8,
    LARGE: 12,
    XLARGE: 16
  };

  // 设备类型（与OpenHarmony官方API返回值匹配）
  static readonly DEVICE_TYPE: DeviceTypeConfig = {
    PHONE: 'phone',
    TABLET: 'tablet',
    TWO_IN_ONE: '2in1',
    WEARABLE: 'wearable',
    TV: 'tv',
    CAR: 'car',
    SMART_VISION: 'smartVision'
  };

  // 页面路由
  static readonly ROUTES: RoutesConfig = {
    INDEX: 'pages/Index',
    HOME: 'pages/Home',
    CATEGORY: 'pages/Category',
    SEARCH: 'pages/Search',
    APP_DETAIL: 'pages/AppDetail',
    DOWNLOAD: 'pages/Download',
    PROFILE: 'pages/Profile'
  };

  // 应用状态
  static readonly APP_STATUS: AppStatusConfig = {
    PUBLISHED: 'published',
    PENDING: 'pending',
    REJECTED: 'rejected',
    DRAFT: 'draft'
  };

  // 排序方式
  static readonly SORT_TYPE: SortTypeConfig = {
    NEWEST: 'newest',
    POPULAR: 'popular',
    RATING: 'rating'
  };

  // 分页配置
  static readonly PAGE_SIZE = 20;

  // 图片占位符
  static readonly PLACEHOLDER_IMAGE = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMDAgNzBMMTMwIDEwMEg3MEwxMDAgNzBaIiBmaWxsPSIjQ0NDQ0NDIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNDAiIHN0cm9rZT0iI0NDQ0NDQyIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LXNpemU9IjEyIj7lm77niYc8L3RleHQ+Cjwvc3ZnPgo=';

  // 动画时长
  static readonly ANIMATION_DURATION: AnimationDurationConfig = {
    FAST: 200,
    NORMAL: 300,
    SLOW: 500
  };

  // 网络请求超时时间
  static readonly TIMEOUT = 60000; // 60秒超时，符合OpenHarmony官方推荐

  // 本地存储键名
  static readonly STORAGE_KEYS: StorageKeysConfig = {
    TOKEN: 'auth_token',
    USER_INFO: 'user_info',
    SEARCH_HISTORY: 'search_history',
    DOWNLOAD_HISTORY: 'download_history',
    DARK_MODE_FOLLOW_SYSTEM: 'dark_mode_follow_system',
    DARK_MODE_MANUAL: 'dark_mode_manual'
  };
}