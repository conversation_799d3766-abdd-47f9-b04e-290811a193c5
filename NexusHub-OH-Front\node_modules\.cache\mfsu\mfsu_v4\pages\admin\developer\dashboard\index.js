"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Statistic,
  Tag,
  Button,
  Space,
  Typography,
  Progress,
  List,
  Avatar,
  Spin,
  message
} from "antd";
import {
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  TrophyOutlined,
  EyeOutlined
} from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-components";
import { Column } from "@ant-design/plots";
import dayjs from "dayjs";
import { history } from "umi";
import { getAdminDevelopersStats, getAdminDevelopersRecent } from "@/services/ant-design-pro/kaifazhe";
import styles from "./index.less";
const { Title, Text } = Typography;
const DeveloperDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(null);
  const [recentApplications, setRecentApplications] = useState([]);
  const [chartData, setChartData] = useState([]);
  const fetchStats = async () => {
    setLoading(true);
    try {
      const statsResponse = await getAdminDevelopersStats();
      console.log("API Response (getAdminDevelopersStats):", statsResponse);
      if (statsResponse && statsResponse.code === 200 && statsResponse.data) {
        console.log("Data to be set for stats:", statsResponse.data);
        setStats(statsResponse.data);
        const chartData2 = [
          { status: "\u5DF2\u901A\u8FC7", count: statsResponse.data.approved_count || 0, color: "#52c41a" },
          { status: "\u5F85\u5BA1\u6838", count: statsResponse.data.pending_count || 0, color: "#faad14" },
          { status: "\u5DF2\u62D2\u7EDD", count: statsResponse.data.rejected_count || 0, color: "#ff4d4f" }
        ];
        setChartData(chartData2);
      } else {
        console.warn("Failed to fetch stats or data is empty:", statsResponse);
      }
      const recentResponse = await getAdminDevelopersRecent({ limit: 5 });
      console.log("API Response (getAdminDevelopersRecent):", recentResponse);
      if (recentResponse && recentResponse.code === 200 && recentResponse.data) {
        setRecentApplications(recentResponse.data);
      } else {
        console.warn("Failed to fetch recent applications or data is empty:", recentResponse);
      }
    } catch (error) {
      console.error("\u83B7\u53D6\u7EDF\u8BA1\u6570\u636E\u5931\u8D25:", error);
      message.error("\u83B7\u53D6\u7EDF\u8BA1\u6570\u636E\u5931\u8D25");
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchStats();
  }, []);
  console.log("Current stats state before render:", stats);
  console.log("Current chartData state before render:", chartData);
  console.log("Current recentApplications state before render:", recentApplications);
  const renderStatusTag = (status) => {
    const statusMap = {
      pending: { color: "orange", text: "\u5F85\u5BA1\u6838" },
      approved: { color: "green", text: "\u5DF2\u901A\u8FC7" },
      rejected: { color: "red", text: "\u5DF2\u62D2\u7EDD" }
    };
    const config = statusMap[status];
    return config ? /* @__PURE__ */ jsx(Tag, { color: config.color, children: config.text }) : null;
  };
  const goToVerifyPage = () => {
    history.push("/user-management/developer-verify");
  };
  const chartConfig = {
    data: chartData,
    xField: "status",
    yField: "count",
    colorField: "status",
    color: ({ status }) => {
      const colorMap = {
        "\u5DF2\u901A\u8FC7": "#52c41a",
        "\u5F85\u5BA1\u6838": "#faad14",
        "\u5DF2\u62D2\u7EDD": "#ff4d4f"
      };
      return colorMap[status];
    },
    label: {
      position: "middle",
      style: {
        fill: "#FFFFFF",
        opacity: 0.6
      }
    },
    meta: {
      status: {
        alias: "\u72B6\u6001"
      },
      count: {
        alias: "\u6570\u91CF"
      }
    }
  };
  if (loading) {
    return /* @__PURE__ */ jsx(PageContainer, { title: "\u5F00\u53D1\u8005\u8BA4\u8BC1\u7BA1\u7406", children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs("div", { style: { textAlign: "center", padding: "50px 0" }, children: [
      /* @__PURE__ */ jsx(Spin, { size: "large" }),
      /* @__PURE__ */ jsx("div", { style: { marginTop: 16 }, children: "\u6B63\u5728\u52A0\u8F7D\u6570\u636E..." })
    ] }) }) });
  }
  return /* @__PURE__ */ jsx(
    PageContainer,
    {
      title: "\u5F00\u53D1\u8005\u8BA4\u8BC1\u6570\u636E\u7EDF\u8BA1",
      subTitle: "\u7EDF\u8BA1\u5F00\u53D1\u8005\u8BA4\u8BC1\u76F8\u5173\u6570\u636E",
      extra: [
        /* @__PURE__ */ jsx(Button, { type: "primary", onClick: goToVerifyPage, children: "\u5BA1\u6838\u7533\u8BF7" }, "verify")
      ],
      children: /* @__PURE__ */ jsxs("div", { className: styles.dashboard, children: [
        /* @__PURE__ */ jsxs(Row, { gutter: [16, 16], className: styles.statsRow, children: [
          /* @__PURE__ */ jsx(Col, { xs: 24, sm: 12, md: 6, children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx(
            Statistic,
            {
              title: "\u603B\u7533\u8BF7\u6570",
              value: stats?.total_applications || 0,
              prefix: /* @__PURE__ */ jsx(UserOutlined, {}),
              valueStyle: { color: "#1890ff" }
            }
          ) }) }),
          /* @__PURE__ */ jsx(Col, { xs: 24, sm: 12, md: 6, children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx(
            Statistic,
            {
              title: "\u5F85\u5BA1\u6838",
              value: stats?.pending_count || 0,
              prefix: /* @__PURE__ */ jsx(ClockCircleOutlined, {}),
              valueStyle: { color: "#faad14" }
            }
          ) }) }),
          /* @__PURE__ */ jsx(Col, { xs: 24, sm: 12, md: 6, children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx(
            Statistic,
            {
              title: "\u5DF2\u901A\u8FC7",
              value: stats?.approved_count || 0,
              prefix: /* @__PURE__ */ jsx(CheckCircleOutlined, {}),
              valueStyle: { color: "#52c41a" }
            }
          ) }) }),
          /* @__PURE__ */ jsx(Col, { xs: 24, sm: 12, md: 6, children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx(
            Statistic,
            {
              title: "\u4ECA\u65E5\u65B0\u7533\u8BF7",
              value: stats?.today_applications || 0,
              prefix: /* @__PURE__ */ jsx(TrophyOutlined, {}),
              valueStyle: { color: "#722ed1" }
            }
          ) }) })
        ] }),
        /* @__PURE__ */ jsxs(Row, { gutter: [16, 16], children: [
          /* @__PURE__ */ jsx(Col, { xs: 24, lg: 12, children: /* @__PURE__ */ jsxs(Card, { title: "\u5BA1\u6838\u72B6\u6001\u5206\u5E03", className: styles.chartCard, children: [
            /* @__PURE__ */ jsx(Column, { ...chartConfig, height: 300 }),
            /* @__PURE__ */ jsxs("div", { className: styles.approvalRate, children: [
              /* @__PURE__ */ jsx(Title, { level: 4, children: "\u901A\u8FC7\u7387" }),
              /* @__PURE__ */ jsx(
                Progress,
                {
                  type: "circle",
                  percent: parseFloat(stats?.approval_rate?.replace("%", "") || "0"),
                  format: (percent) => `${percent}%`,
                  strokeColor: {
                    "0%": "#108ee9",
                    "100%": "#87d068"
                  }
                }
              )
            ] })
          ] }) }),
          /* @__PURE__ */ jsx(Col, { xs: 24, lg: 12, children: /* @__PURE__ */ jsx(
            Card,
            {
              title: "\u6700\u8FD1\u7533\u8BF7",
              extra: /* @__PURE__ */ jsx(Button, { type: "link", onClick: goToVerifyPage, children: "\u67E5\u770B\u5168\u90E8" }),
              className: styles.recentCard,
              children: /* @__PURE__ */ jsx(
                List,
                {
                  itemLayout: "horizontal",
                  dataSource: recentApplications,
                  renderItem: (item) => /* @__PURE__ */ jsx(
                    List.Item,
                    {
                      actions: [
                        /* @__PURE__ */ jsx(
                          Button,
                          {
                            type: "link",
                            icon: /* @__PURE__ */ jsx(EyeOutlined, {}),
                            onClick: goToVerifyPage,
                            children: "\u67E5\u770B"
                          },
                          "view"
                        )
                      ],
                      children: /* @__PURE__ */ jsx(
                        List.Item.Meta,
                        {
                          avatar: /* @__PURE__ */ jsx(
                            Avatar,
                            {
                              src: item.developer_avatar,
                              icon: /* @__PURE__ */ jsx(UserOutlined, {})
                            }
                          ),
                          title: /* @__PURE__ */ jsxs(Space, { children: [
                            /* @__PURE__ */ jsx("span", { children: item.developer_name }),
                            renderStatusTag(item.verify_status)
                          ] }),
                          description: /* @__PURE__ */ jsxs("div", { children: [
                            /* @__PURE__ */ jsxs("div", { children: [
                              "\u7528\u6237\u540D: ",
                              item.username
                            ] }),
                            item.company_name && /* @__PURE__ */ jsxs("div", { children: [
                              "\u516C\u53F8: ",
                              item.company_name
                            ] }),
                            /* @__PURE__ */ jsxs(Text, { type: "secondary", children: [
                              "\u7533\u8BF7\u65F6\u95F4: ",
                              dayjs(item.submitted_at).format("MM-DD HH:mm")
                            ] })
                          ] })
                        }
                      )
                    }
                  )
                }
              )
            }
          ) })
        ] })
      ] })
    }
  );
};
export default DeveloperDashboard;
