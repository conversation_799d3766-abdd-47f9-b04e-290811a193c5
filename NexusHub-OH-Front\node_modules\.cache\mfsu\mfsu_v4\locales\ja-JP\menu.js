"use strict";
export default {
  "menu.welcome": "\u3088\u3046\u3053\u305D",
  "menu.more-blocks": "\u305D\u306E\u4ED6\u306E\u30D6\u30ED\u30C3\u30AF",
  "menu.home": "\u30DB\u30FC\u30E0",
  "menu.admin": "\u7BA1\u7406\u8005",
  "menu.admin.sub-page": "\u30B5\u30D6\u30DA\u30FC\u30B8",
  "menu.login": "\u30ED\u30B0\u30A4\u30F3",
  "menu.register": "\u767B\u9332",
  "menu.register-result": "\u767B\u9332\u7D50\u679C",
  "menu.dashboard": "\u30C0\u30C3\u30B7\u30E5\u30DC\u30FC\u30C9",
  "menu.dashboard.analysis": "\u5206\u6790",
  "menu.dashboard.monitor": "\u30E2\u30CB\u30BF\u30FC",
  "menu.dashboard.workplace": "\u8077\u5834",
  "menu.exception.403": "403",
  "menu.exception.404": "404",
  "menu.exception.500": "500",
  "menu.form": "\u30D5\u30A9\u30FC\u30E0",
  "menu.form.basic-form": "\u57FA\u672C\u30D5\u30A9\u30FC\u30E0",
  "menu.form.step-form": "\u30B9\u30C6\u30C3\u30D7\u30D5\u30A9\u30FC\u30E0",
  "menu.form.step-form.info": "\u30B9\u30C6\u30C3\u30D7\u30D5\u30A9\u30FC\u30E0\uFF08\u8EE2\u9001\u60C5\u5831\u306E\u66F8\u304D\u8FBC\u307F\uFF09",
  "menu.form.step-form.confirm": "\u30B9\u30C6\u30C3\u30D7\u30D5\u30A9\u30FC\u30E0\uFF08\u8EE2\u9001\u60C5\u5831\u306E\u78BA\u8A8D\uFF09",
  "menu.form.step-form.result": "\u30B9\u30C6\u30C3\u30D7\u30D5\u30A9\u30FC\u30E0\uFF08\u5B8C\u6210\uFF09",
  "menu.form.advanced-form": "\u9AD8\u5EA6\u306A\u30D5\u30A9\u30FC\u30E0",
  "menu.list": "\u30EA\u30B9\u30C8",
  "menu.list.table-list": "\u691C\u7D22\u30C6\u30FC\u30D6\u30EB",
  "menu.list.basic-list": "\u57FA\u672C\u30EA\u30B9\u30C8",
  "menu.list.card-list": "\u30AB\u30FC\u30C9\u30EA\u30B9\u30C8",
  "menu.list.search-list": "\u691C\u7D22\u30EA\u30B9\u30C8",
  "menu.list.search-list.articles": "\u691C\u7D22\u30EA\u30B9\u30C8(\u8A18\u4E8B)",
  "menu.list.search-list.projects": "\u691C\u7D22\u30EA\u30B9\u30C8(\u30D7\u30ED\u30B8\u30A7\u30AF\u30C8)",
  "menu.list.search-list.applications": "\u691C\u7D22\u30EA\u30B9\u30C8(\u30A2\u30D7\u30EA)",
  "menu.profile": "\u30D7\u30ED\u30D5\u30A3\u30FC\u30EB",
  "menu.profile.basic": "\u57FA\u672C\u30D7\u30ED\u30D5\u30A3\u30FC\u30EB",
  "menu.profile.advanced": "\u9AD8\u5EA6\u306A\u30D7\u30ED\u30D5\u30A3\u30FC\u30EB",
  "menu.result": "\u7D50\u679C",
  "menu.result.success": "\u6210\u529F",
  "menu.result.fail": "\u5931\u6557",
  "menu.exception": "\u4F8B\u5916",
  "menu.exception.not-permission": "403",
  "menu.exception.not-find": "404",
  "menu.exception.server-error": "500",
  "menu.exception.trigger": "\u30C8\u30EA\u30AC\u30FC",
  "menu.account": "\u30A2\u30AB\u30A6\u30F3\u30C8",
  "menu.account.settings": "\u30A2\u30AB\u30A6\u30F3\u30C8\u8A2D\u5B9A",
  "menu.account.trigger": "\u30C8\u30EA\u30AC\u30FC\u30A8\u30E9\u30FC",
  "menu.account.logout": "\u30ED\u30B0\u30A2\u30A6\u30C8",
  "menu.editor": "\u30B0\u30E9\u30D5\u30A3\u30C3\u30AF\u30A8\u30C7\u30A3\u30BF",
  "menu.editor.flow": "\u30D5\u30ED\u30FC\u30A8\u30C7\u30A3\u30BF",
  "menu.editor.mind": "\u30DE\u30A4\u30F3\u30C9\u30A8\u30C7\u30A3\u30BF\u30FC",
  "menu.editor.koni": "\u30B3\u30CB\u30A8\u30C7\u30A3\u30BF\u30FC"
};
