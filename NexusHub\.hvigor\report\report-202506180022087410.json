{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "3e198457-017c-4048-89d3-9d2287bad583", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520420597800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94588537-0fba-4bab-8f18-27f6e55893a3", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520420844200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b87a8feb-17e5-4979-a33b-55ac3c464a5a", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520421532500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c1f5f8b-6b28-41e4-accb-01ff0e5730f9", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520421728900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8653c7ed-632b-4b19-a5a2-0e1185a54a6d", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520422304600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5287bb23-8779-4611-9070-863bb300b509", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520422512600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccea05b3-829c-446d-b289-1cbd8d81002a", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520423870400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ac9877b-05e1-4b7e-8923-a107b7a57643", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152520456023200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad8b9a86-268a-477f-9628-5c4a36d881a2", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698956856500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4da2f206-29b1-4b11-9ecc-891fd97dda12", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698964409000, "endTime": 152699148384500}, "additional": {"children": ["27f1ea71-80b1-4bfc-b0b9-ffb9a9403e3d", "1036e513-c856-43c7-bf83-365ddef173c1", "cacd2909-e074-4375-b192-476d6ccb586f", "d7460f0f-2f17-453f-a04b-7c53b23cef5f", "746ba2fb-40d0-4202-824f-a983176579f5", "c33bcc32-f553-490d-a40c-9bf477fa1c4b", "f8c60cc3-4b1d-4eb3-9851-0cf87a07ec6f"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "0844edab-2d38-4322-a63c-0f1fc193552b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27f1ea71-80b1-4bfc-b0b9-ffb9a9403e3d", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698964410100, "endTime": 152698977096500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4da2f206-29b1-4b11-9ecc-891fd97dda12", "logId": "73a0ae33-a514-42f0-b769-59e690e23efa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1036e513-c856-43c7-bf83-365ddef173c1", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698977111800, "endTime": 152699147287800}, "additional": {"children": ["1ddc23b1-37ac-4c61-b35a-c0793975ecd4", "849b619e-6bfa-41e4-90e0-8668df257e1b", "2b78c8fd-5cfb-40cd-9918-6f5828f04ac1", "f0c272dc-dfbb-433e-9330-8b40c7cd89ef", "bdda93d8-28b1-43fd-bf20-945538e38ab6", "4962002c-3570-4c37-8f0f-0df8746ada5f", "d75b467c-32ea-41a4-adea-61331e13ead3", "78e3ef7c-1474-4698-b7da-3edc34c1fdb5", "13384d4e-00e9-4f9d-819f-800cb2961107"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4da2f206-29b1-4b11-9ecc-891fd97dda12", "logId": "1eed7f46-29d5-4971-821b-f03f7847eac1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cacd2909-e074-4375-b192-476d6ccb586f", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699147310100, "endTime": 152699148370600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4da2f206-29b1-4b11-9ecc-891fd97dda12", "logId": "64553978-1647-451a-afeb-325d0e046928"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7460f0f-2f17-453f-a04b-7c53b23cef5f", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699148374500, "endTime": 152699148379900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4da2f206-29b1-4b11-9ecc-891fd97dda12", "logId": "07d86804-5f49-47ec-89a4-6315dc1deebb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "746ba2fb-40d0-4202-824f-a983176579f5", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698967720500, "endTime": 152698967768000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4da2f206-29b1-4b11-9ecc-891fd97dda12", "logId": "b3ede5fb-b68b-4f34-ae7f-da986bc5788e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3ede5fb-b68b-4f34-ae7f-da986bc5788e", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698967720500, "endTime": 152698967768000}, "additional": {"logType": "info", "children": [], "durationId": "746ba2fb-40d0-4202-824f-a983176579f5", "parent": "0844edab-2d38-4322-a63c-0f1fc193552b"}}, {"head": {"id": "c33bcc32-f553-490d-a40c-9bf477fa1c4b", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698972130600, "endTime": 152698972145800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4da2f206-29b1-4b11-9ecc-891fd97dda12", "logId": "f9ff6ab6-7886-4cb9-be2b-c57ecae4bf58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9ff6ab6-7886-4cb9-be2b-c57ecae4bf58", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698972130600, "endTime": 152698972145800}, "additional": {"logType": "info", "children": [], "durationId": "c33bcc32-f553-490d-a40c-9bf477fa1c4b", "parent": "0844edab-2d38-4322-a63c-0f1fc193552b"}}, {"head": {"id": "0678b573-7349-4d33-b5e1-b8c86f539466", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698972195600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2724c1bf-e2d2-442c-80c1-8674fdf4b37f", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698976974900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73a0ae33-a514-42f0-b769-59e690e23efa", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698964410100, "endTime": 152698977096500}, "additional": {"logType": "info", "children": [], "durationId": "27f1ea71-80b1-4bfc-b0b9-ffb9a9403e3d", "parent": "0844edab-2d38-4322-a63c-0f1fc193552b"}}, {"head": {"id": "1ddc23b1-37ac-4c61-b35a-c0793975ecd4", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698982723400, "endTime": 152698982733200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1036e513-c856-43c7-bf83-365ddef173c1", "logId": "9b168a42-f7bb-4e58-8dd0-82dfbb1dafbc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "849b619e-6bfa-41e4-90e0-8668df257e1b", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698982751900, "endTime": 152698987259000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1036e513-c856-43c7-bf83-365ddef173c1", "logId": "bd497e37-80a9-4cd1-a781-0bc4b6c8fca7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b78c8fd-5cfb-40cd-9918-6f5828f04ac1", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698987273800, "endTime": 152699066586500}, "additional": {"children": ["773e8474-14be-419f-a268-f5c7df68db33", "0f5b2518-c57d-467f-8aaf-c312bb512343", "e0184861-d3c6-49ab-a84d-18d1ab367296"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1036e513-c856-43c7-bf83-365ddef173c1", "logId": "5bb14db3-abe6-4f52-a673-260b23ff91e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0c272dc-dfbb-433e-9330-8b40c7cd89ef", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699066600000, "endTime": 152699084055300}, "additional": {"children": ["6ede32b1-525d-490a-9234-d92cd8022746"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1036e513-c856-43c7-bf83-365ddef173c1", "logId": "f6107f4e-c253-4ea6-aee9-c22404e603bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdda93d8-28b1-43fd-bf20-945538e38ab6", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699084061700, "endTime": 152699123530500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1036e513-c856-43c7-bf83-365ddef173c1", "logId": "e260316c-e43d-49ea-a7ed-b9280011c6ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4962002c-3570-4c37-8f0f-0df8746ada5f", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699124492700, "endTime": 152699133944000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1036e513-c856-43c7-bf83-365ddef173c1", "logId": "d1e20517-fff3-4185-813a-fc0ca06e7856"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d75b467c-32ea-41a4-adea-61331e13ead3", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699133964300, "endTime": 152699147158100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1036e513-c856-43c7-bf83-365ddef173c1", "logId": "eeffa9a7-cd64-4576-8027-98c093a936bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78e3ef7c-1474-4698-b7da-3edc34c1fdb5", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699147174800, "endTime": 152699147277800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1036e513-c856-43c7-bf83-365ddef173c1", "logId": "512edbb6-ac72-46d7-934a-7cd271855751"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b168a42-f7bb-4e58-8dd0-82dfbb1dafbc", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698982723400, "endTime": 152698982733200}, "additional": {"logType": "info", "children": [], "durationId": "1ddc23b1-37ac-4c61-b35a-c0793975ecd4", "parent": "1eed7f46-29d5-4971-821b-f03f7847eac1"}}, {"head": {"id": "bd497e37-80a9-4cd1-a781-0bc4b6c8fca7", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698982751900, "endTime": 152698987259000}, "additional": {"logType": "info", "children": [], "durationId": "849b619e-6bfa-41e4-90e0-8668df257e1b", "parent": "1eed7f46-29d5-4971-821b-f03f7847eac1"}}, {"head": {"id": "773e8474-14be-419f-a268-f5c7df68db33", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698987931700, "endTime": 152698987979800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b78c8fd-5cfb-40cd-9918-6f5828f04ac1", "logId": "c9e0b0cb-55a5-4e73-a985-4748cbd18e35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9e0b0cb-55a5-4e73-a985-4748cbd18e35", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698987931700, "endTime": 152698987979800}, "additional": {"logType": "info", "children": [], "durationId": "773e8474-14be-419f-a268-f5c7df68db33", "parent": "5bb14db3-abe6-4f52-a673-260b23ff91e1"}}, {"head": {"id": "0f5b2518-c57d-467f-8aaf-c312bb512343", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698990644300, "endTime": 152699065870600}, "additional": {"children": ["54a34256-b7a8-47fd-b8d6-5df6957e36d0", "5f4ad409-6ab1-43c5-806c-d1ff528c190b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b78c8fd-5cfb-40cd-9918-6f5828f04ac1", "logId": "72896935-5ef5-4ed5-9215-6b0bf7f3b659"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54a34256-b7a8-47fd-b8d6-5df6957e36d0", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698990645300, "endTime": 152698997397900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f5b2518-c57d-467f-8aaf-c312bb512343", "logId": "5533324d-8bac-4830-812a-5b5c75eac58f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f4ad409-6ab1-43c5-806c-d1ff528c190b", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698997414400, "endTime": 152699065861100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f5b2518-c57d-467f-8aaf-c312bb512343", "logId": "d5a16cf4-18e7-412d-8a7f-1ae6d64a9107"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af638994-b39f-4cce-8cf5-0c8439a291db", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698990657600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f989024-7e28-409c-81e0-c19b0495ae33", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698997252900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5533324d-8bac-4830-812a-5b5c75eac58f", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698990645300, "endTime": 152698997397900}, "additional": {"logType": "info", "children": [], "durationId": "54a34256-b7a8-47fd-b8d6-5df6957e36d0", "parent": "72896935-5ef5-4ed5-9215-6b0bf7f3b659"}}, {"head": {"id": "e7b27001-9940-45e4-832e-84910262927a", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698997428000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56d33b11-4eee-44df-b81e-df86c0e0aa61", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699006331400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fb0689a-f0dd-448e-9322-19ad29d87189", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699006451500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e39607a-7cc8-40a5-b55b-b2188b249d90", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699006565500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "405bda1a-cab9-48e7-bb87-2cb46f7f6ed4", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699006634900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69b16381-bd47-4a30-b93e-ca8da0af4d42", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699008112000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "922af014-7588-4344-8a4f-ab3d2e0db877", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699021856200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0ea11a9-2586-4152-900b-cdf8479ab2d5", "name": "Sdk init in 29 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699042225900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebd1f870-2a3e-4514-b523-3480f6d8277c", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699042394300}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 22, "second": 8}, "markType": "other"}}, {"head": {"id": "e35e7716-b609-4fad-b1e4-611cd471593f", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699042413400}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 22, "second": 8}, "markType": "other"}}, {"head": {"id": "8c0ff555-3c3e-4f49-b189-2015087a4fc6", "name": "Project task initialization takes 22 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699065625000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1cd4f9a-61bc-42cb-a24c-1ab79907677c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699065751600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df1a2079-eaef-443f-8376-6d83703d199c", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699065794800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72181593-09ee-4ce3-8a76-6fb8b67af364", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699065830000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5a16cf4-18e7-412d-8a7f-1ae6d64a9107", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698997414400, "endTime": 152699065861100}, "additional": {"logType": "info", "children": [], "durationId": "5f4ad409-6ab1-43c5-806c-d1ff528c190b", "parent": "72896935-5ef5-4ed5-9215-6b0bf7f3b659"}}, {"head": {"id": "72896935-5ef5-4ed5-9215-6b0bf7f3b659", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698990644300, "endTime": 152699065870600}, "additional": {"logType": "info", "children": ["5533324d-8bac-4830-812a-5b5c75eac58f", "d5a16cf4-18e7-412d-8a7f-1ae6d64a9107"], "durationId": "0f5b2518-c57d-467f-8aaf-c312bb512343", "parent": "5bb14db3-abe6-4f52-a673-260b23ff91e1"}}, {"head": {"id": "e0184861-d3c6-49ab-a84d-18d1ab367296", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699066555700, "endTime": 152699066572300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2b78c8fd-5cfb-40cd-9918-6f5828f04ac1", "logId": "a67d6d4b-122d-4f2d-a3af-3a7c85bb17fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a67d6d4b-122d-4f2d-a3af-3a7c85bb17fb", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699066555700, "endTime": 152699066572300}, "additional": {"logType": "info", "children": [], "durationId": "e0184861-d3c6-49ab-a84d-18d1ab367296", "parent": "5bb14db3-abe6-4f52-a673-260b23ff91e1"}}, {"head": {"id": "5bb14db3-abe6-4f52-a673-260b23ff91e1", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698987273800, "endTime": 152699066586500}, "additional": {"logType": "info", "children": ["c9e0b0cb-55a5-4e73-a985-4748cbd18e35", "72896935-5ef5-4ed5-9215-6b0bf7f3b659", "a67d6d4b-122d-4f2d-a3af-3a7c85bb17fb"], "durationId": "2b78c8fd-5cfb-40cd-9918-6f5828f04ac1", "parent": "1eed7f46-29d5-4971-821b-f03f7847eac1"}}, {"head": {"id": "6ede32b1-525d-490a-9234-d92cd8022746", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699067375000, "endTime": 152699084046200}, "additional": {"children": ["190ac7a9-ad62-46c1-8dc2-93f55663da90", "6d2fac75-d451-41dd-8d62-aba6acd61695", "e89f8e75-27fb-4af2-b5ec-61cfbc64b36d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f0c272dc-dfbb-433e-9330-8b40c7cd89ef", "logId": "eb1f0a8e-5b7c-41b9-ad0d-458031760d9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "190ac7a9-ad62-46c1-8dc2-93f55663da90", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699070003200, "endTime": 152699070031000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ede32b1-525d-490a-9234-d92cd8022746", "logId": "2ab6494e-c6a4-40f5-948a-51a2c83a181c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ab6494e-c6a4-40f5-948a-51a2c83a181c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699070003200, "endTime": 152699070031000}, "additional": {"logType": "info", "children": [], "durationId": "190ac7a9-ad62-46c1-8dc2-93f55663da90", "parent": "eb1f0a8e-5b7c-41b9-ad0d-458031760d9f"}}, {"head": {"id": "6d2fac75-d451-41dd-8d62-aba6acd61695", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699071384600, "endTime": 152699082921900}, "additional": {"children": ["369ed580-3d47-4e76-9169-1fdc8c52308e", "10dded21-1e1d-4506-bf58-7ee611607cec"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ede32b1-525d-490a-9234-d92cd8022746", "logId": "5ebb5bec-767e-42a9-b39d-ba985d9ba04e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "369ed580-3d47-4e76-9169-1fdc8c52308e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699071385400, "endTime": 152699074925800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6d2fac75-d451-41dd-8d62-aba6acd61695", "logId": "6208ef00-f4a6-4dc9-9c21-14c84a9e241c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10dded21-1e1d-4506-bf58-7ee611607cec", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699074936000, "endTime": 152699082914700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6d2fac75-d451-41dd-8d62-aba6acd61695", "logId": "d463318f-3634-4534-8234-9cfb6764102b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa19c76c-6741-4078-a0fd-ca73bcc5038a", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699071389800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e8cc3fe-4420-44a0-92f9-32c3aede2f81", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699074840500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6208ef00-f4a6-4dc9-9c21-14c84a9e241c", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699071385400, "endTime": 152699074925800}, "additional": {"logType": "info", "children": [], "durationId": "369ed580-3d47-4e76-9169-1fdc8c52308e", "parent": "5ebb5bec-767e-42a9-b39d-ba985d9ba04e"}}, {"head": {"id": "5f844b3a-c7f2-4b5f-8a96-a916913eda0d", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699074944400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1465cfa-a3cc-4967-b8c3-d2cc06fd24b9", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699079523400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c45b8a6-b544-4a9d-9435-6e8636ba715b", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699079610800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b577d44b-3b11-4896-adc1-6cebb698a166", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699079742900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23109506-6d6b-40a8-a207-3c2e0fbfaaa9", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699079822100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04c85452-2fa4-4023-96d9-df04ecb1d3cc", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699079859400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f0889b7-b101-4322-b773-2cd42e6421e0", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699079887700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "649ba15b-3aa9-4b35-b880-bef99dab6d3c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699079927200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eb7537a-1af1-4ba3-bfe4-6bad76930457", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699079956400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab592bd8-9982-41ee-a20f-7e99921bb923", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699080098800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b0abda7-950e-4715-abd1-143650abce14", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699080170800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0551864d-1a5f-45e0-b5b3-bf52d49557e1", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699080205200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "963e4e6a-9571-4d6b-9472-fd4ff48a891c", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699080231600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f984e94-c61e-4710-96cb-2874a0dffa33", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699080276900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "973ca3f6-cbf0-448c-b86d-439a369f4f26", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699080303200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0aa35f7-b831-408f-8a35-61dadf398df3", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699080365800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e114f576-5dfc-476b-a74b-a4e235cc6320", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699080507100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3f2eedd-a5dd-47b7-9f19-aa5b1bbac2a6", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699080544100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c350b1d-5815-4109-8701-61fee7db9744", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699080573300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e8fadf0-8728-4512-9716-1c1ccedb3dfd", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699080603400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ecf9e03-0f4e-4dad-a64a-68a942fea33d", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699082724800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32232e16-fb42-4c5d-af75-d4d0f9db8a7a", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699082824000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8347b595-2047-4748-907a-5e02b08a41a3", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699082861800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9af32d3b-6aae-42b9-b68e-a073873a02c7", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699082886300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d463318f-3634-4534-8234-9cfb6764102b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699074936000, "endTime": 152699082914700}, "additional": {"logType": "info", "children": [], "durationId": "10dded21-1e1d-4506-bf58-7ee611607cec", "parent": "5ebb5bec-767e-42a9-b39d-ba985d9ba04e"}}, {"head": {"id": "5ebb5bec-767e-42a9-b39d-ba985d9ba04e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699071384600, "endTime": 152699082921900}, "additional": {"logType": "info", "children": ["6208ef00-f4a6-4dc9-9c21-14c84a9e241c", "d463318f-3634-4534-8234-9cfb6764102b"], "durationId": "6d2fac75-d451-41dd-8d62-aba6acd61695", "parent": "eb1f0a8e-5b7c-41b9-ad0d-458031760d9f"}}, {"head": {"id": "e89f8e75-27fb-4af2-b5ec-61cfbc64b36d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699084010500, "endTime": 152699084022700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ede32b1-525d-490a-9234-d92cd8022746", "logId": "35856ef2-b508-400b-ae19-6c28a50df99c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35856ef2-b508-400b-ae19-6c28a50df99c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699084010500, "endTime": 152699084022700}, "additional": {"logType": "info", "children": [], "durationId": "e89f8e75-27fb-4af2-b5ec-61cfbc64b36d", "parent": "eb1f0a8e-5b7c-41b9-ad0d-458031760d9f"}}, {"head": {"id": "eb1f0a8e-5b7c-41b9-ad0d-458031760d9f", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699067375000, "endTime": 152699084046200}, "additional": {"logType": "info", "children": ["2ab6494e-c6a4-40f5-948a-51a2c83a181c", "5ebb5bec-767e-42a9-b39d-ba985d9ba04e", "35856ef2-b508-400b-ae19-6c28a50df99c"], "durationId": "6ede32b1-525d-490a-9234-d92cd8022746", "parent": "f6107f4e-c253-4ea6-aee9-c22404e603bd"}}, {"head": {"id": "f6107f4e-c253-4ea6-aee9-c22404e603bd", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699066600000, "endTime": 152699084055300}, "additional": {"logType": "info", "children": ["eb1f0a8e-5b7c-41b9-ad0d-458031760d9f"], "durationId": "f0c272dc-dfbb-433e-9330-8b40c7cd89ef", "parent": "1eed7f46-29d5-4971-821b-f03f7847eac1"}}, {"head": {"id": "2c1a9057-f20d-4d38-b8fa-c2261b2da4f6", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699095335700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c73d483b-2d2c-463b-9062-fa4b3d5366b5", "name": "hvigorfile, resolve hvigorfile dependencies in 40 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699123398600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e260316c-e43d-49ea-a7ed-b9280011c6ed", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699084061700, "endTime": 152699123530500}, "additional": {"logType": "info", "children": [], "durationId": "bdda93d8-28b1-43fd-bf20-945538e38ab6", "parent": "1eed7f46-29d5-4971-821b-f03f7847eac1"}}, {"head": {"id": "13384d4e-00e9-4f9d-819f-800cb2961107", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699124291500, "endTime": 152699124481600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1036e513-c856-43c7-bf83-365ddef173c1", "logId": "7adb737f-784f-49a8-8a4a-5a1aa697e2c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "860da428-a46c-426d-ac80-b74292e2f766", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699124326600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7adb737f-784f-49a8-8a4a-5a1aa697e2c6", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699124291500, "endTime": 152699124481600}, "additional": {"logType": "info", "children": [], "durationId": "13384d4e-00e9-4f9d-819f-800cb2961107", "parent": "1eed7f46-29d5-4971-821b-f03f7847eac1"}}, {"head": {"id": "26d226ab-a9be-4820-87b3-bdc0613a38fb", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699125951200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74ca1a79-da78-4958-b43e-3fbe1a90fb59", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699133109900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1e20517-fff3-4185-813a-fc0ca06e7856", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699124492700, "endTime": 152699133944000}, "additional": {"logType": "info", "children": [], "durationId": "4962002c-3570-4c37-8f0f-0df8746ada5f", "parent": "1eed7f46-29d5-4971-821b-f03f7847eac1"}}, {"head": {"id": "2d0ab383-8c2a-48e0-a52e-753f1a578c05", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699133983500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78b99ef4-9f83-425d-824f-53bec5f6f2da", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699140728500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52efefd2-8def-41a2-a68d-71b7352ddae5", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699140835100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d71af25-793b-4e15-902d-cc79b94c8247", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699140986500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8b0f7ea-82d0-464e-a26e-f6866a2c241c", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699143879200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "698e4e7d-e3be-40ed-98cc-2f678d2d0caa", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699143970200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeffa9a7-cd64-4576-8027-98c093a936bf", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699133964300, "endTime": 152699147158100}, "additional": {"logType": "info", "children": [], "durationId": "d75b467c-32ea-41a4-adea-61331e13ead3", "parent": "1eed7f46-29d5-4971-821b-f03f7847eac1"}}, {"head": {"id": "bc6e121e-f85a-470c-a939-1ae3327a731f", "name": "Configuration phase cost:165 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699147198400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "512edbb6-ac72-46d7-934a-7cd271855751", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699147174800, "endTime": 152699147277800}, "additional": {"logType": "info", "children": [], "durationId": "78e3ef7c-1474-4698-b7da-3edc34c1fdb5", "parent": "1eed7f46-29d5-4971-821b-f03f7847eac1"}}, {"head": {"id": "1eed7f46-29d5-4971-821b-f03f7847eac1", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698977111800, "endTime": 152699147287800}, "additional": {"logType": "info", "children": ["9b168a42-f7bb-4e58-8dd0-82dfbb1dafbc", "bd497e37-80a9-4cd1-a781-0bc4b6c8fca7", "5bb14db3-abe6-4f52-a673-260b23ff91e1", "f6107f4e-c253-4ea6-aee9-c22404e603bd", "e260316c-e43d-49ea-a7ed-b9280011c6ed", "d1e20517-fff3-4185-813a-fc0ca06e7856", "eeffa9a7-cd64-4576-8027-98c093a936bf", "512edbb6-ac72-46d7-934a-7cd271855751", "7adb737f-784f-49a8-8a4a-5a1aa697e2c6"], "durationId": "1036e513-c856-43c7-bf83-365ddef173c1", "parent": "0844edab-2d38-4322-a63c-0f1fc193552b"}}, {"head": {"id": "f8c60cc3-4b1d-4eb3-9851-0cf87a07ec6f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699148345100, "endTime": 152699148360200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4da2f206-29b1-4b11-9ecc-891fd97dda12", "logId": "2ed190b0-b9b4-41d6-99aa-d58e21541ed5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ed190b0-b9b4-41d6-99aa-d58e21541ed5", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699148345100, "endTime": 152699148360200}, "additional": {"logType": "info", "children": [], "durationId": "f8c60cc3-4b1d-4eb3-9851-0cf87a07ec6f", "parent": "0844edab-2d38-4322-a63c-0f1fc193552b"}}, {"head": {"id": "64553978-1647-451a-afeb-325d0e046928", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699147310100, "endTime": 152699148370600}, "additional": {"logType": "info", "children": [], "durationId": "cacd2909-e074-4375-b192-476d6ccb586f", "parent": "0844edab-2d38-4322-a63c-0f1fc193552b"}}, {"head": {"id": "07d86804-5f49-47ec-89a4-6315dc1deebb", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699148374500, "endTime": 152699148379900}, "additional": {"logType": "info", "children": [], "durationId": "d7460f0f-2f17-453f-a04b-7c53b23cef5f", "parent": "0844edab-2d38-4322-a63c-0f1fc193552b"}}, {"head": {"id": "0844edab-2d38-4322-a63c-0f1fc193552b", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698964409000, "endTime": 152699148384500}, "additional": {"logType": "info", "children": ["73a0ae33-a514-42f0-b769-59e690e23efa", "1eed7f46-29d5-4971-821b-f03f7847eac1", "64553978-1647-451a-afeb-325d0e046928", "07d86804-5f49-47ec-89a4-6315dc1deebb", "b3ede5fb-b68b-4f34-ae7f-da986bc5788e", "f9ff6ab6-7886-4cb9-be2b-c57ecae4bf58", "2ed190b0-b9b4-41d6-99aa-d58e21541ed5"], "durationId": "4da2f206-29b1-4b11-9ecc-891fd97dda12"}}, {"head": {"id": "31a767c5-f210-4d45-bcdc-002f8edc4bed", "name": "Configuration task cost before running: 188 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699148519200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adb131ae-bb4c-416b-93a3-9f9129cbe126", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699156440700, "endTime": 152699167509200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "b8b2bdb4-c641-4648-b5ba-139618d04fa8", "logId": "8483c241-c424-42e5-92f1-4ba339f5a165"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8b2bdb4-c641-4648-b5ba-139618d04fa8", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699149782700}, "additional": {"logType": "detail", "children": [], "durationId": "adb131ae-bb4c-416b-93a3-9f9129cbe126"}}, {"head": {"id": "26a25b24-9a92-471a-ba89-3a122e4d6f2a", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699150384200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5d67e10-a0d0-4c28-aafb-550e4eaf77ce", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699150488100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7f7ea60-9d5e-43ca-9d9a-3a2223a8ba4c", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699151164200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d6746e8-7964-49fe-b369-28cde988b74c", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699152031000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e12c952e-8281-4183-860f-ed28c12d5d27", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699153167500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20178a21-9538-48a2-aa76-218b9364d36c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699153247400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "959d082a-41c0-4730-81b1-d56e11406dd1", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699156452600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c2a7908-ee8b-4c05-a238-d1bb23adb62d", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699167293700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ba57aaa-8689-4125-bcde-67e5b9cd9c24", "name": "entry : default@PreBuild cost memory 0.32033538818359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699167441100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8483c241-c424-42e5-92f1-4ba339f5a165", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699156440700, "endTime": 152699167509200}, "additional": {"logType": "info", "children": [], "durationId": "adb131ae-bb4c-416b-93a3-9f9129cbe126"}}, {"head": {"id": "e139483c-58b7-41cc-877d-95ce3c376c71", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699174645100, "endTime": 152699177490600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e106816b-96b5-4007-b926-d6aff2116d0c", "logId": "0cbc8d99-ef78-4216-b7d2-eba98246a8de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e106816b-96b5-4007-b926-d6aff2116d0c", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699172590700}, "additional": {"logType": "detail", "children": [], "durationId": "e139483c-58b7-41cc-877d-95ce3c376c71"}}, {"head": {"id": "46d158c4-778d-439f-9557-510729b1e2f3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699173914300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11b7d770-560d-4a3a-ad1e-a62209e3262b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699174023200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af5a64d5-7ca3-43bb-98a3-c6367dfa1620", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699174658000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "810adfe6-c249-4610-8212-092c79c8635c", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699175750500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ef2546d-f106-4b30-add8-cbf9820f716a", "name": "entry : default@CreateModuleInfo cost memory 0.0604248046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699177190800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e69430f-d2d8-44ab-bc1a-d6b4c7fb885a", "name": "runTaskFromQueue task cost before running: 217 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699177399600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cbc8d99-ef78-4216-b7d2-eba98246a8de", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699174645100, "endTime": 152699177490600, "totalTime": 2713600}, "additional": {"logType": "info", "children": [], "durationId": "e139483c-58b7-41cc-877d-95ce3c376c71"}}, {"head": {"id": "720f42bb-8132-477c-a732-8dae35d5e972", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699186626100, "endTime": 152699189055800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ecb6597a-126a-473b-809e-cf4c045743ea", "logId": "61bcae0a-4390-4ff7-8059-ca79d9f8cdad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ecb6597a-126a-473b-809e-cf4c045743ea", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699180440400}, "additional": {"logType": "detail", "children": [], "durationId": "720f42bb-8132-477c-a732-8dae35d5e972"}}, {"head": {"id": "6928dede-1a8c-47b0-96e7-dc36fd5bd802", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699181551100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1db3b98d-63b9-4b78-91ac-891d212e7eee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699181677800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b89444ee-86f5-4a27-b4e8-dd5d61fa02e4", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699186644300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed731267-f6c2-459b-8844-0120a7a5c88a", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699187849000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a4049b7-1b84-422b-8261-3f176c986d1b", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699188881900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c1e2e61-fa2b-452c-a0a6-73915d7f0bb0", "name": "entry : default@GenerateMetadata cost memory 0.102447509765625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699188996400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61bcae0a-4390-4ff7-8059-ca79d9f8cdad", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699186626100, "endTime": 152699189055800}, "additional": {"logType": "info", "children": [], "durationId": "720f42bb-8132-477c-a732-8dae35d5e972"}}, {"head": {"id": "512052cf-924e-4817-ae7b-3a0254ffca04", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699194158100, "endTime": 152699194458300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7b1a5063-04dc-457a-80ae-9f2765ec2239", "logId": "3c4a2c9e-e42d-4685-b7b2-ae8a3f2bb813"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b1a5063-04dc-457a-80ae-9f2765ec2239", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699192616400}, "additional": {"logType": "detail", "children": [], "durationId": "512052cf-924e-4817-ae7b-3a0254ffca04"}}, {"head": {"id": "1f20fb71-15b5-420d-a7c5-2d682f5f60c1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699193866600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7362d871-f9cf-482f-ba93-0390315edb45", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699193998800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f784759-778a-4fa0-b8f6-8ecd9504474c", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699194167300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a715de2e-c649-4fe1-af11-e9299a603862", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699194263000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbac94c7-340c-4c36-b3c6-a143c23cbd2f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699194300700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4af8926-d574-4d5e-a753-7c23ebd9a0a1", "name": "entry : default@ConfigureCmake cost memory 0.03809356689453125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699194360700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1ea8ee7-4091-4d38-8467-82ed75e1fcb0", "name": "runTaskFromQueue task cost before running: 234 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699194423000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c4a2c9e-e42d-4685-b7b2-ae8a3f2bb813", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699194158100, "endTime": 152699194458300, "totalTime": 259500}, "additional": {"logType": "info", "children": [], "durationId": "512052cf-924e-4817-ae7b-3a0254ffca04"}}, {"head": {"id": "22321a04-b8ca-4096-8a3d-09c1e01abfb4", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699197994600, "endTime": 152699201749300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3f371e30-4bdd-4ccc-8abd-9ab082de6d2c", "logId": "65e2660c-ef16-4037-9233-4fb6e1ff3b71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f371e30-4bdd-4ccc-8abd-9ab082de6d2c", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699196184200}, "additional": {"logType": "detail", "children": [], "durationId": "22321a04-b8ca-4096-8a3d-09c1e01abfb4"}}, {"head": {"id": "de210afb-6070-41a4-9a5e-22e5ccf0ed56", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699197210300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dee6bcbb-edbf-4e40-a8a1-61a78ac69d62", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699197319200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcf150c2-5915-40e3-8bc8-8f420be0c75e", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699198006800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "400e6068-2e97-487f-b0eb-22ea5cfc979e", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699201520400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32b75016-f10e-4b84-9f88-24891f58773b", "name": "entry : default@MergeProfile cost memory 0.1182861328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699201690500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65e2660c-ef16-4037-9233-4fb6e1ff3b71", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699197994600, "endTime": 152699201749300}, "additional": {"logType": "info", "children": [], "durationId": "22321a04-b8ca-4096-8a3d-09c1e01abfb4"}}, {"head": {"id": "73265ef0-bdb4-422e-9480-f5c03551a564", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699206168700, "endTime": 152699208855200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e6ac6c13-4ae0-44c0-b47f-5609a5d3859e", "logId": "c847389c-42ff-47e2-a8f5-7621bc567781"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6ac6c13-4ae0-44c0-b47f-5609a5d3859e", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699204165200}, "additional": {"logType": "detail", "children": [], "durationId": "73265ef0-bdb4-422e-9480-f5c03551a564"}}, {"head": {"id": "d07c8515-c2ae-4840-a6c2-f00e5e641892", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699205215500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "924d1cfe-3d86-4418-842e-c630593e429e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699205326500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49eb7255-b87c-48e2-9a0a-c8eb7b2ad9bc", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699206178700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa1c8fbf-fd90-43b9-91b7-5bb27181a834", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699207281600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40ae89dc-d04c-4cc3-ae6f-de2ecbee1f97", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699208682800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd23351b-2459-4c03-9bfc-353c7458d7c5", "name": "entry : default@CreateBuildProfile cost memory 0.10733795166015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699208797500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c847389c-42ff-47e2-a8f5-7621bc567781", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699206168700, "endTime": 152699208855200}, "additional": {"logType": "info", "children": [], "durationId": "73265ef0-bdb4-422e-9480-f5c03551a564"}}, {"head": {"id": "e966c9ca-a062-4439-aa2f-f7d634af9623", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699212004400, "endTime": 152699212511000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e13fc1a7-9a10-418f-8fa8-2d3e5c9ea23c", "logId": "44278cf9-03e3-4e4f-bb0a-90fdaa86f8a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e13fc1a7-9a10-418f-8fa8-2d3e5c9ea23c", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699210264400}, "additional": {"logType": "detail", "children": [], "durationId": "e966c9ca-a062-4439-aa2f-f7d634af9623"}}, {"head": {"id": "f4742c05-1d3c-41fb-b285-ae584ad23440", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699211226900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7a2d187-4666-4a41-8d05-3425f6f90de1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699211324800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a6a395a-5b92-49c5-b9ac-58458c87d2fb", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699212013200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b83bb7e9-575b-4875-980e-b7b1c0d7f69b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699212139400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9da6dbf4-5820-42b1-8eb2-37b022ffeec7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699212186200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9935715d-0d27-4c40-97d3-0ed9efc60d05", "name": "entry : default@PreCheckSyscap cost memory 0.041168212890625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699212380800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a136d50-ab47-40e3-9306-9516c52de8fd", "name": "runTaskFromQueue task cost before running: 252 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699212469900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44278cf9-03e3-4e4f-bb0a-90fdaa86f8a2", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699212004400, "endTime": 152699212511000, "totalTime": 445500}, "additional": {"logType": "info", "children": [], "durationId": "e966c9ca-a062-4439-aa2f-f7d634af9623"}}, {"head": {"id": "c990a30f-77e0-4307-bcb1-26e9d8bad3aa", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699216265600, "endTime": 152699222809800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "26f27e53-5823-4934-9d25-32457288ec86", "logId": "29229bed-1ca7-4e87-883b-492abf3a3527"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26f27e53-5823-4934-9d25-32457288ec86", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699214026600}, "additional": {"logType": "detail", "children": [], "durationId": "c990a30f-77e0-4307-bcb1-26e9d8bad3aa"}}, {"head": {"id": "9b590b41-a57a-4621-9732-a1add71912e7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699214965300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab0cc8e0-7d14-40b3-9250-f78cc06c757c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699215051400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abe844be-610c-4532-95d3-a1e8fed77c7f", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699216275600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11059bf0-3d85-44cb-97f4-a7867eb78b02", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699221568100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fa65068-59e8-49f0-8fa5-8a50c14fe3f3", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699222593900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3510b7e9-cc0d-4288-a721-7e4f2facb32a", "name": "entry : default@GeneratePkgContextInfo cost memory 0.245880126953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699222742100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29229bed-1ca7-4e87-883b-492abf3a3527", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699216265600, "endTime": 152699222809800}, "additional": {"logType": "info", "children": [], "durationId": "c990a30f-77e0-4307-bcb1-26e9d8bad3aa"}}, {"head": {"id": "7fa909c1-16a8-4cbd-a392-db1f5dc9f11d", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699229979400, "endTime": 152699232319700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "52d008b7-c22d-4c61-8ece-b99420d63167", "logId": "ae413e8f-278b-48da-b33b-64823104372e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52d008b7-c22d-4c61-8ece-b99420d63167", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699224434800}, "additional": {"logType": "detail", "children": [], "durationId": "7fa909c1-16a8-4cbd-a392-db1f5dc9f11d"}}, {"head": {"id": "157337f6-10ac-44c2-bfab-467481a3e971", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699225416000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7440e32a-b812-4f11-950d-c8ac79e9f63c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699225524500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf46325a-6585-4709-8e5e-e2701ce473c4", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699229997500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecc30ea1-c296-4326-bf9a-9b5d87f4df4d", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699231884000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "729316df-0397-40e9-975b-dc0bc2c4b1f9", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699232025300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12baa39d-7eb3-4615-a0f0-b3c17bad06f5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699232112800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "836ece74-2bb4-4c55-a742-a133f028ddeb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699232155400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a71150df-3971-4610-a1be-b1086b010f22", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12071990966796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699232218700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efc0eb1d-e21f-4c31-b6f5-99add158099f", "name": "runTaskFromQueue task cost before running: 272 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699232282400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae413e8f-278b-48da-b33b-64823104372e", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699229979400, "endTime": 152699232319700, "totalTime": 2288700}, "additional": {"logType": "info", "children": [], "durationId": "7fa909c1-16a8-4cbd-a392-db1f5dc9f11d"}}, {"head": {"id": "846316c7-b83f-478f-84df-549e11398044", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699237273700, "endTime": 152699237784800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8b8e2ed1-ab89-4634-a5ca-57c26510848e", "logId": "4e54e885-53a0-4835-8003-f4f889310518"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b8e2ed1-ab89-4634-a5ca-57c26510848e", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699234914900}, "additional": {"logType": "detail", "children": [], "durationId": "846316c7-b83f-478f-84df-549e11398044"}}, {"head": {"id": "3850c8c8-cd71-48f0-b0de-029e23ccdec2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699236289600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09401784-97c8-41ab-b630-47122d4d3749", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699236442200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da82202a-50e0-477d-8fb7-4d690918ad43", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699237284600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89ede21d-d40a-4196-9c1a-a1a3cfaeb78f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699237420300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a01c10a-b78a-499e-9b49-686d2e9aa3c4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699237468900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec28d394-1ced-4249-aeb0-c2fd00b30d09", "name": "entry : default@BuildNativeWithCmake cost memory 0.03852081298828125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699237541700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3890031-d533-4999-8f59-ed9d5b0c8e13", "name": "runTaskFromQueue task cost before running: 277 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699237719900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e54e885-53a0-4835-8003-f4f889310518", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699237273700, "endTime": 152699237784800, "totalTime": 402600}, "additional": {"logType": "info", "children": [], "durationId": "846316c7-b83f-478f-84df-549e11398044"}}, {"head": {"id": "37279d67-a224-45e8-98fc-d5821116df3b", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699241026900, "endTime": 152699244685100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "db44b9c7-3d71-4fc2-a001-c6525f8f0b0d", "logId": "d4872cd7-710b-4136-9585-8f48147eea84"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db44b9c7-3d71-4fc2-a001-c6525f8f0b0d", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699239282100}, "additional": {"logType": "detail", "children": [], "durationId": "37279d67-a224-45e8-98fc-d5821116df3b"}}, {"head": {"id": "769d762c-5b2e-48a5-bd64-7d48e0e199eb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699240208200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e08848a9-1140-4e7b-8569-c324b8642f12", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699240330500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9c6547e-0fa1-412c-8871-af5bb5c10e2b", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699241036900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc320705-25e2-414f-a937-88c3fc6e5abd", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699244499400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32e2d8d6-b461-402d-8548-8d30c2178765", "name": "entry : default@MakePackInfo cost memory 0.16375732421875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699244627600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4872cd7-710b-4136-9585-8f48147eea84", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699241026900, "endTime": 152699244685100}, "additional": {"logType": "info", "children": [], "durationId": "37279d67-a224-45e8-98fc-d5821116df3b"}}, {"head": {"id": "8b390c7a-b626-4081-a959-8221f77ba85b", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699248762100, "endTime": 152699252276000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3ed5617b-498b-47b2-b688-db1a2f04aa86", "logId": "9dae40cf-bc06-4e26-bb34-5460623ecd51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ed5617b-498b-47b2-b688-db1a2f04aa86", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699246634300}, "additional": {"logType": "detail", "children": [], "durationId": "8b390c7a-b626-4081-a959-8221f77ba85b"}}, {"head": {"id": "c8307561-eb5a-458a-b35e-caba36b354be", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699247539700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cbaa334-9eb2-415a-b135-5f8313ebb507", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699247650900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d08fbcca-211a-4d78-833e-9578a7ffae93", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699248770900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a77a779-4d48-4e89-ab2a-a079a637c2c8", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699248951600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6aa29a3d-98cb-4edb-b9c6-6caaeddd09b4", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699249650800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cee54a5-12c6-467b-bed4-61a27b2e2b25", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699252014400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed7b2752-605e-4f78-a236-9f25f187c7c1", "name": "entry : default@SyscapTransform cost memory 0.1507720947265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699252198200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dae40cf-bc06-4e26-bb34-5460623ecd51", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699248762100, "endTime": 152699252276000}, "additional": {"logType": "info", "children": [], "durationId": "8b390c7a-b626-4081-a959-8221f77ba85b"}}, {"head": {"id": "545ef61e-eb58-484a-b55f-c2fc4ac04c82", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699256404200, "endTime": 152699258244800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "96f4309e-4ea9-45a8-b10e-0920b2a96aea", "logId": "1464e3b4-29f2-4c75-8b41-0c304908b16b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96f4309e-4ea9-45a8-b10e-0920b2a96aea", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699254153400}, "additional": {"logType": "detail", "children": [], "durationId": "545ef61e-eb58-484a-b55f-c2fc4ac04c82"}}, {"head": {"id": "dcb725be-843d-4187-939c-bf58a70d47b3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699255193400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27c6b4d5-79c1-4a1f-a3b5-33af30ca3993", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699255303900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b23588d0-a819-4e70-99c6-0a6027cb34a5", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699256414700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "172f4c4e-1e85-48f9-ad00-4532e84104e4", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699258071000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74913b24-0582-430e-a429-e48f28498da2", "name": "entry : default@ProcessProfile cost memory 0.12639617919921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699258183200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1464e3b4-29f2-4c75-8b41-0c304908b16b", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699256404200, "endTime": 152699258244800}, "additional": {"logType": "info", "children": [], "durationId": "545ef61e-eb58-484a-b55f-c2fc4ac04c82"}}, {"head": {"id": "9873c356-a800-4cf1-b01c-3027e2b3c76c", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699262262700, "endTime": 152699269911600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "54aa81ef-c276-4cd5-b46f-0082282ccb3e", "logId": "ed57a9aa-be61-444a-9c22-deb93fefc0a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54aa81ef-c276-4cd5-b46f-0082282ccb3e", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699259702500}, "additional": {"logType": "detail", "children": [], "durationId": "9873c356-a800-4cf1-b01c-3027e2b3c76c"}}, {"head": {"id": "6bd0763d-4f18-4ba8-aa5a-0261c0fa27d0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699260617100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b21313c9-2a5e-4c63-8420-5c15c50ee8cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699260713700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8337b65-6b58-4f34-b6f9-b53394ebcbd3", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699262272100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ae50048-bb0f-45d1-8a68-6dc88f256c67", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699269688500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b424803-7764-4ed8-95aa-22954045c59e", "name": "entry : default@ProcessRouterMap cost memory 0.23326873779296875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699269848700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed57a9aa-be61-444a-9c22-deb93fefc0a8", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699262262700, "endTime": 152699269911600}, "additional": {"logType": "info", "children": [], "durationId": "9873c356-a800-4cf1-b01c-3027e2b3c76c"}}, {"head": {"id": "538a69f7-6d41-4590-9c67-92d8e6e9cdcc", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699273741200, "endTime": 152699278555000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "e294c043-ad86-4f22-9495-2a61da9294d5", "logId": "9e43a3e5-907c-4796-8e79-a522dc884acf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e294c043-ad86-4f22-9495-2a61da9294d5", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699272640200}, "additional": {"logType": "detail", "children": [], "durationId": "538a69f7-6d41-4590-9c67-92d8e6e9cdcc"}}, {"head": {"id": "02a721ec-b392-4347-a3fa-ae1e1c97218b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699273560400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "368dd8c3-448d-4550-a1ff-6ef33b4c7afb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699273662800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0e92f66-1126-4148-9c94-e493607f94f0", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699273748800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dac8a7d-a7ab-43ba-8d69-c10f9468a74c", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699273828900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58fec427-e39d-4e49-a58a-3f92d6371b87", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699277316100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "145f375a-3e69-442f-9436-6a52e34d7a39", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699277438900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4afda894-cfba-45a1-b49b-c674ffb7156e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699277506900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa281b74-307a-48de-b35b-883ac6f9934d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699277541900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fad4ae5f-0cfc-4353-83ec-837a5c2a88e6", "name": "entry : default@ProcessStartupConfig cost memory 0.25878143310546875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699278390500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09e05427-9047-4852-9525-19e3ad76870d", "name": "runTaskFromQueue task cost before running: 318 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699278502500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e43a3e5-907c-4796-8e79-a522dc884acf", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699273741200, "endTime": 152699278555000, "totalTime": 4736500}, "additional": {"logType": "info", "children": [], "durationId": "538a69f7-6d41-4590-9c67-92d8e6e9cdcc"}}, {"head": {"id": "748cf723-1c3e-4beb-a001-d62f6d536bb5", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699282569400, "endTime": 152699283839900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "56cc5c93-1555-4df5-b876-ab97d6ef1eea", "logId": "b44ac9a9-1775-437e-ae42-c656bbd9773f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56cc5c93-1555-4df5-b876-ab97d6ef1eea", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699281002100}, "additional": {"logType": "detail", "children": [], "durationId": "748cf723-1c3e-4beb-a001-d62f6d536bb5"}}, {"head": {"id": "8844bf92-bb48-42fe-ac2b-279fe2185aa0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699281863900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f180a2b1-346a-4b47-b587-e3c9fbd29692", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699281943300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a9104c0-ef09-49ee-a88a-dff43c06d8e7", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699282577600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf1f4343-eeb5-4fe1-921a-bc9263f04374", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699282778700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f57309f-54fe-4ff0-bc58-7b30f17483c1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699282833200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "000d77a1-0fbe-4b86-8172-96e822d962e5", "name": "entry : default@BuildNativeWithNinja cost memory 0.0582122802734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699283618100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc62ec04-770f-40b2-a9a7-2320a4c30aea", "name": "runTaskFromQueue task cost before running: 323 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699283757100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b44ac9a9-1775-437e-ae42-c656bbd9773f", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699282569400, "endTime": 152699283839900, "totalTime": 1151600}, "additional": {"logType": "info", "children": [], "durationId": "748cf723-1c3e-4beb-a001-d62f6d536bb5"}}, {"head": {"id": "51ebba1d-d3ac-436f-a897-3841f20b54af", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699291304400, "endTime": 152699296060400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "f89d8851-116b-4524-8e71-111e577cfc65", "logId": "a5003e8c-551b-474d-8ede-6e2a3c7c3095"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f89d8851-116b-4524-8e71-111e577cfc65", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699286755800}, "additional": {"logType": "detail", "children": [], "durationId": "51ebba1d-d3ac-436f-a897-3841f20b54af"}}, {"head": {"id": "0e929a96-bda8-46d7-b15e-8d0a9638edf3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699287824600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "479367fa-77c3-431e-9876-949f9fb7baa4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699287923800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff70c3fa-aa3a-4271-8e03-76a0e84954f4", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699290134600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da894d2e-c3b0-41d0-8bd0-81a9a3106fe5", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699292615100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21ca4ce8-1fef-4be8-8b5d-c6f5e3424236", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699294436600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3b119f6-d98f-4f54-927d-5e62297477c4", "name": "entry : default@ProcessResource cost memory 0.1618804931640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699294537000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5003e8c-551b-474d-8ede-6e2a3c7c3095", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699291304400, "endTime": 152699296060400}, "additional": {"logType": "info", "children": [], "durationId": "51ebba1d-d3ac-436f-a897-3841f20b54af"}}, {"head": {"id": "a808be1b-cb5c-40cc-b576-458bd08773db", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699303939200, "endTime": 152699321935800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "57b40df9-b407-4ccc-9c27-f5e2839b5dee", "logId": "48dcfa53-5cfc-4ab2-b2df-b904dcab0103"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57b40df9-b407-4ccc-9c27-f5e2839b5dee", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699299316600}, "additional": {"logType": "detail", "children": [], "durationId": "a808be1b-cb5c-40cc-b576-458bd08773db"}}, {"head": {"id": "0f60c1b2-fdee-49f3-979f-9d691a1c190c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699300474500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5116551c-abef-4785-9aed-613c8631933c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699300671400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ea06b89-0abe-48f4-8748-fbd4a344dcdc", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699303992900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d80cae8-314b-4632-adba-6c4c335fc49f", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699321742300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d97842e-04b4-4c76-9d8f-a3acecdd8fff", "name": "entry : default@GenerateLoaderJson cost memory 0.8829803466796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699321879800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48dcfa53-5cfc-4ab2-b2df-b904dcab0103", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699303939200, "endTime": 152699321935800}, "additional": {"logType": "info", "children": [], "durationId": "a808be1b-cb5c-40cc-b576-458bd08773db"}}, {"head": {"id": "e386d0ec-41d4-47c5-b0b0-b9ea9027f80c", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699331195300, "endTime": 152699335264100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "fb68f500-a923-403b-8c9e-9200f02c22b0", "logId": "c7bd244d-0209-41ba-a036-afe0ab9b0d0d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb68f500-a923-403b-8c9e-9200f02c22b0", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699329571700}, "additional": {"logType": "detail", "children": [], "durationId": "e386d0ec-41d4-47c5-b0b0-b9ea9027f80c"}}, {"head": {"id": "ebf5de4e-7584-4a3b-a7d4-e38022f0f23a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699330391000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bca386f-7742-4b33-bca6-701da61df754", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699330477600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a5514af-a70f-4c2f-bfef-b4485e39f281", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699331204900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b33cd9fb-80a8-46fa-b473-c39580af6b75", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699335043500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7f7253f-0760-4284-9b51-18c8dc366107", "name": "entry : default@ProcessLibs cost memory 0.1421966552734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699335201700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7bd244d-0209-41ba-a036-afe0ab9b0d0d", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699331195300, "endTime": 152699335264100}, "additional": {"logType": "info", "children": [], "durationId": "e386d0ec-41d4-47c5-b0b0-b9ea9027f80c"}}, {"head": {"id": "d8ae1162-58bc-427c-827b-039dd2207fb5", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699341218700, "endTime": 152699364385400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "29c69906-2be2-4494-88d6-49b8b9390f2c", "logId": "b3410904-f596-470b-8386-d6a8f6b18089"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29c69906-2be2-4494-88d6-49b8b9390f2c", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699337260400}, "additional": {"logType": "detail", "children": [], "durationId": "d8ae1162-58bc-427c-827b-039dd2207fb5"}}, {"head": {"id": "47b875ab-0f29-49b0-9c4f-af6ec4fac54c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699338090000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a284aa7-9084-4809-a205-4ae1b188daba", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699338181500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36d5aa4a-b210-46ff-8b64-447a9fb33aba", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699339024300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6798535-601e-465c-b879-0ca1ef02817d", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699341240900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f7ccdfa-7535-4b72-8d07-bc27c9c2c961", "name": "Incremental task entry:default@CompileResource pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699364155000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44accba7-564b-4885-bd5b-93c0f58c8457", "name": "entry : default@CompileResource cost memory 1.3225555419921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699364303300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3410904-f596-470b-8386-d6a8f6b18089", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699341218700, "endTime": 152699364385400}, "additional": {"logType": "info", "children": [], "durationId": "d8ae1162-58bc-427c-827b-039dd2207fb5"}}, {"head": {"id": "ede3d8a6-a7ba-409f-bf99-dda23f9d6f1c", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699370789200, "endTime": 152699373350700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "380c754c-3fcd-4a32-9a1d-2f0cf492a98d", "logId": "3e54346c-023a-4519-b60d-7970ae06d2fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "380c754c-3fcd-4a32-9a1d-2f0cf492a98d", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699366655600}, "additional": {"logType": "detail", "children": [], "durationId": "ede3d8a6-a7ba-409f-bf99-dda23f9d6f1c"}}, {"head": {"id": "342b9bc5-5903-4c2f-9a0e-0b415d25690d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699367891100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e99c87b8-94cd-4a6c-8dc6-ef8c58ec02d8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699368051500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fe5bb98-19fa-4968-b387-798bb6d745b8", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699370804500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "affe4a76-dd32-432c-9314-a2ee322cfe73", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699371443500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e137931-536e-4644-b0f3-b015fff3f0f1", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699373188600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e103bf04-5963-44db-aeb4-677a4ec1629c", "name": "entry : default@DoNativeStrip cost memory 0.08000946044921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699373296400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e54346c-023a-4519-b60d-7970ae06d2fc", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699370789200, "endTime": 152699373350700}, "additional": {"logType": "info", "children": [], "durationId": "ede3d8a6-a7ba-409f-bf99-dda23f9d6f1c"}}, {"head": {"id": "7f12f9cb-8e47-4de5-b03d-62c602553810", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699379446400, "endTime": 152710618544100}, "additional": {"children": ["e7211aee-f22c-46a8-8739-2c9014b67ba2", "5485d270-0a3f-47bf-8278-bf6cb6487f39"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "cfb0b306-d1e5-429c-9a32-142c14a056e5", "logId": "1d6446f1-4518-4df3-8de3-ebd87780e687"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfb0b306-d1e5-429c-9a32-142c14a056e5", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699374585900}, "additional": {"logType": "detail", "children": [], "durationId": "7f12f9cb-8e47-4de5-b03d-62c602553810"}}, {"head": {"id": "1cc2f8a3-6716-47e0-b2fb-e7515217d22a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699375360100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d0e77cb-62c5-427e-883a-5adf9915446e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699375478500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61bfe7e7-3ccd-433b-92bb-d55c4037fd70", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699379462400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e937ef67-1b8c-4746-bdcc-a5afa48cc876", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699379655900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3177a7d6-d94f-4dd7-9fc4-426a79f000ca", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699405402100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d24f7617-8805-4fea-bb97-40f2105d51b0", "name": "default@CompileArkTS work[4] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699406927400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7211aee-f22c-46a8-8739-2c9014b67ba2", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152702164059700, "endTime": 152710597220300}, "additional": {"children": ["8b768b41-2d45-49d9-a0a6-68addf655cdd", "6357a5d1-614a-4396-9867-350b8557bf78", "7a2326d6-d258-431d-94d8-a6c5aecaefb1", "f41df07a-61cc-40f1-9228-0b8d81bc18a5", "d2ad8fda-6231-4e1d-8ff2-2e498b3b90ab", "ce8b3ca4-79c9-4637-bdba-bd569f7d4b24", "f056ecdf-1ca7-432a-a789-592d93521b88"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "7f12f9cb-8e47-4de5-b03d-62c602553810", "logId": "06414f03-9516-45fd-a2d6-d3fb33096aed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2014cee-b6ab-4926-a745-723e86f08445", "name": "default@CompileArkTS work[4] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699407732200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44c6e437-a64d-4636-8136-113fb6e87c53", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699407828400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53e456d4-13df-4766-b6a4-3d20c2e6f93f", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699407868600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fe01771-dad5-47a5-8c17-b91b564c1b89", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699407901800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce98fb54-afb6-46c2-baad-ae5cdfb4686e", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699407927100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a75814e-1596-4a1e-847c-a29a6b373dc0", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699407958000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea5257d1-ed80-46f5-8dd2-86bf5b8e7f78", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699407983000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba7d8b65-03b1-4d99-89ac-3c44b7613031", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699408007500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c3af9a2-d16c-44f8-b3a8-b662b03072b7", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699408030200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b740aac4-e554-47b2-84ee-09e978220a81", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699408053300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ac2ce26-71eb-482a-9226-fcecc3e45135", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699408074900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2885db5f-d407-4fa5-9aaa-aeb82a0f0d8e", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699408098700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0be6db9f-e9dd-44b9-93e9-70ff9ecd0853", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699408121700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b0477eb-5f21-44ca-8bf7-26f495c97162", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699408144300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dd07a7a-a8ec-4463-85a4-5113abfcd9d7", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699408178200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd6cdfc5-5544-4414-b1bc-1464f2bbbe90", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699408202200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67a0b4b1-bbd6-4b6b-8376-f62e691c2a24", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699408247100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b835ce8-ec59-449c-b3b9-819851a514d6", "name": "default@CompileArkTS work[4] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699409266500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a35b87c1-bad0-46bc-8ea1-53cc7f3edc55", "name": "default@CompileArkTS work[4] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699409365700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d64e707-dc63-45f5-9344-323503c49311", "name": "CopyResources startTime: 152699409411200", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699409413900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1991aa68-87a9-41ac-80ba-7c2943b648df", "name": "default@CompileArkTS work[5] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699409472400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5485d270-0a3f-47bf-8278-bf6cb6487f39", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 152700491939400, "endTime": 152700511464100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "7f12f9cb-8e47-4de5-b03d-62c602553810", "logId": "1c9329b3-ebbb-4f24-843c-be328259b8c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91646e2f-6831-431e-8cd2-1dccbceec4e5", "name": "default@CompileArkTS work[5] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699410263600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93b5b04d-6154-4557-9dd1-832fee18fd18", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699410353000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8acaeb5b-545e-4ddf-8aff-5e495cf15bd1", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699410411900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18636636-2ea0-4fed-bef2-d95f948aefd9", "name": "default@CompileArkTS work[5] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699411280200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d704845-d70d-4791-ad57-47fe7e2abde2", "name": "default@CompileArkTS work[5] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699411390900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34d251fe-3e81-4fb1-9a71-34a07d3ee098", "name": "entry : default@CompileArkTS cost memory 1.7673873901367188", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699411604700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a9749bd-7163-4e47-a8bd-5054e8ad5baa", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699418457600, "endTime": 152699427280600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "1ab99cd9-1307-492b-9657-b983b55ba50d", "logId": "6a4a5b15-30fd-4eec-bb5c-f361fb72d515"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ab99cd9-1307-492b-9657-b983b55ba50d", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699413158200}, "additional": {"logType": "detail", "children": [], "durationId": "7a9749bd-7163-4e47-a8bd-5054e8ad5baa"}}, {"head": {"id": "73c04697-bdaf-46ef-8516-87e8804048b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699414238000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a89da95-5b50-4495-afb0-186a3f838897", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699414340100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a4b447f-2cef-484e-9ad9-55f1676a1ab9", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699418474400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b21312b-90ff-4a20-8c79-81387af50eda", "name": "entry : default@BuildJS cost memory 0.345367431640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699427057200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "925037bc-76b2-4e18-b4a2-e60860c96abf", "name": "runTaskFromQueue task cost before running: 466 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699427203600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a4a5b15-30fd-4eec-bb5c-f361fb72d515", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699418457600, "endTime": 152699427280600, "totalTime": 8721100}, "additional": {"logType": "info", "children": [], "durationId": "7a9749bd-7163-4e47-a8bd-5054e8ad5baa"}}, {"head": {"id": "2b74d685-353b-481b-aff8-08b65c0f3e27", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699434417400, "endTime": 152699438558800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "12b7e63c-594e-4ffc-9a2f-989d9275b48a", "logId": "5c6180f6-9bf7-4fb9-9a55-9506652afeef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12b7e63c-594e-4ffc-9a2f-989d9275b48a", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699430360400}, "additional": {"logType": "detail", "children": [], "durationId": "2b74d685-353b-481b-aff8-08b65c0f3e27"}}, {"head": {"id": "68e7c7b5-210d-45fb-a014-1a60533d3e4b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699431415900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4dd2021-9313-4ff2-b757-239392773018", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699431519400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c9061ab-79e9-4cab-97c6-55daa2bb06df", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699434449100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7780546-dd17-43f1-b69e-cfc0fdd1cdd8", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699435704700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fefda25c-e1b9-4d13-8669-92e0f7166936", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699438349800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "358ed1cf-40b8-4077-a230-cfd02e02934a", "name": "entry : default@CacheNativeLibs cost memory 0.0951385498046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699438490000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c6180f6-9bf7-4fb9-9a55-9506652afeef", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699434417400, "endTime": 152699438558800}, "additional": {"logType": "info", "children": [], "durationId": "2b74d685-353b-481b-aff8-08b65c0f3e27"}}, {"head": {"id": "80a85b7d-8df2-4cd5-a17f-98c569e188b5", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152700511921200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0be0f84e-0f3f-4a45-ac77-028852225015", "name": "CopyResources is end, endTime: 152700512104100", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152700512109900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fd96b68-a531-4884-8805-34540d9fe448", "name": "default@CompileArkTS work[5] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152700512211200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c9329b3-ebbb-4f24-843c-be328259b8c8", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 152700491939400, "endTime": 152700511464100}, "additional": {"logType": "info", "children": [], "durationId": "5485d270-0a3f-47bf-8278-bf6cb6487f39", "parent": "1d6446f1-4518-4df3-8de3-ebd87780e687"}}, {"head": {"id": "557c565f-bc0a-43f1-b033-d67cb998ac09", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152700512290800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "491abc75-fc8f-4e90-9588-e03ffb596a8d", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710598778500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b768b41-2d45-49d9-a0a6-68addf655cdd", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152702165160800, "endTime": 152703211024400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e7211aee-f22c-46a8-8739-2c9014b67ba2", "logId": "0ed09342-a666-4a6e-8f3f-bfefc2b909cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ed09342-a666-4a6e-8f3f-bfefc2b909cc", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152702165160800, "endTime": 152703211024400}, "additional": {"logType": "info", "children": [], "durationId": "8b768b41-2d45-49d9-a0a6-68addf655cdd", "parent": "06414f03-9516-45fd-a2d6-d3fb33096aed"}}, {"head": {"id": "6357a5d1-614a-4396-9867-350b8557bf78", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152703212457000, "endTime": 152703258569600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e7211aee-f22c-46a8-8739-2c9014b67ba2", "logId": "8b878b6b-5349-4c76-9dfa-45c32e48d32e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b878b6b-5349-4c76-9dfa-45c32e48d32e", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152703212457000, "endTime": 152703258569600}, "additional": {"logType": "info", "children": [], "durationId": "6357a5d1-614a-4396-9867-350b8557bf78", "parent": "06414f03-9516-45fd-a2d6-d3fb33096aed"}}, {"head": {"id": "7a2326d6-d258-431d-94d8-a6c5aecaefb1", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152703258669600, "endTime": 152703258775700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e7211aee-f22c-46a8-8739-2c9014b67ba2", "logId": "03e754ec-aaa0-41ee-8b24-cc1563949fcc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03e754ec-aaa0-41ee-8b24-cc1563949fcc", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152703258669600, "endTime": 152703258775700}, "additional": {"logType": "info", "children": [], "durationId": "7a2326d6-d258-431d-94d8-a6c5aecaefb1", "parent": "06414f03-9516-45fd-a2d6-d3fb33096aed"}}, {"head": {"id": "f41df07a-61cc-40f1-9228-0b8d81bc18a5", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152703258826800, "endTime": 152710325380100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e7211aee-f22c-46a8-8739-2c9014b67ba2", "logId": "99f7e899-0757-4270-8979-ba854aebb7c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99f7e899-0757-4270-8979-ba854aebb7c7", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152703258826800, "endTime": 152710325380100}, "additional": {"logType": "info", "children": [], "durationId": "f41df07a-61cc-40f1-9228-0b8d81bc18a5", "parent": "06414f03-9516-45fd-a2d6-d3fb33096aed"}}, {"head": {"id": "d2ad8fda-6231-4e1d-8ff2-2e498b3b90ab", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152710325649400, "endTime": 152710365935900}, "additional": {"children": ["13d04745-63c4-410a-b84a-128da52a59ce", "c1a4b8ed-7261-406d-b331-0888fb900e78", "7974461d-e5fa-4e5d-9f6c-9826361ae231"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e7211aee-f22c-46a8-8739-2c9014b67ba2", "logId": "94da0588-d590-44e3-ae5b-d68e8e1f8c30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94da0588-d590-44e3-ae5b-d68e8e1f8c30", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710325649400, "endTime": 152710365935900}, "additional": {"logType": "info", "children": ["049c7e4d-e8e3-407a-b37f-64d53167190a", "389b6ba9-ecb5-4ac8-a498-aac6aec4abfd", "0c8c4b28-9265-46dd-b92d-08c68ece40ce"], "durationId": "d2ad8fda-6231-4e1d-8ff2-2e498b3b90ab", "parent": "06414f03-9516-45fd-a2d6-d3fb33096aed"}}, {"head": {"id": "13d04745-63c4-410a-b84a-128da52a59ce", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152710325821100, "endTime": 152710325840500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d2ad8fda-6231-4e1d-8ff2-2e498b3b90ab", "logId": "049c7e4d-e8e3-407a-b37f-64d53167190a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "049c7e4d-e8e3-407a-b37f-64d53167190a", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710325821100, "endTime": 152710325840500}, "additional": {"logType": "info", "children": [], "durationId": "13d04745-63c4-410a-b84a-128da52a59ce", "parent": "94da0588-d590-44e3-ae5b-d68e8e1f8c30"}}, {"head": {"id": "c1a4b8ed-7261-406d-b331-0888fb900e78", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152710325850000, "endTime": 152710358371800}, "additional": {"children": ["adfa244d-167b-4bf4-9a80-7c03466e4064"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d2ad8fda-6231-4e1d-8ff2-2e498b3b90ab", "logId": "389b6ba9-ecb5-4ac8-a498-aac6aec4abfd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "389b6ba9-ecb5-4ac8-a498-aac6aec4abfd", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710325850000, "endTime": 152710358371800}, "additional": {"logType": "info", "children": ["28d962fa-6740-4946-b0ac-8e940c4c991c"], "durationId": "c1a4b8ed-7261-406d-b331-0888fb900e78", "parent": "94da0588-d590-44e3-ae5b-d68e8e1f8c30"}}, {"head": {"id": "adfa244d-167b-4bf4-9a80-7c03466e4064", "name": "module 'HomePage.ets' pack", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152710327872400, "endTime": 152710342264000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c1a4b8ed-7261-406d-b331-0888fb900e78", "logId": "28d962fa-6740-4946-b0ac-8e940c4c991c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28d962fa-6740-4946-b0ac-8e940c4c991c", "name": "module 'HomePage.ets' pack", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710327872400, "endTime": 152710342264000}, "additional": {"logType": "info", "children": [], "durationId": "adfa244d-167b-4bf4-9a80-7c03466e4064", "parent": "389b6ba9-ecb5-4ac8-a498-aac6aec4abfd"}}, {"head": {"id": "7974461d-e5fa-4e5d-9f6c-9826361ae231", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152710358378800, "endTime": 152710365916500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d2ad8fda-6231-4e1d-8ff2-2e498b3b90ab", "logId": "0c8c4b28-9265-46dd-b92d-08c68ece40ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c8c4b28-9265-46dd-b92d-08c68ece40ce", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710358378800, "endTime": 152710365916500}, "additional": {"logType": "info", "children": [], "durationId": "7974461d-e5fa-4e5d-9f6c-9826361ae231", "parent": "94da0588-d590-44e3-ae5b-d68e8e1f8c30"}}, {"head": {"id": "ce8b3ca4-79c9-4637-bdba-bd569f7d4b24", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152710365952400, "endTime": 152710574553400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e7211aee-f22c-46a8-8739-2c9014b67ba2", "logId": "f3366b52-ccc7-4532-9af6-a26434604f26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3366b52-ccc7-4532-9af6-a26434604f26", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710365952400, "endTime": 152710574553400}, "additional": {"logType": "info", "children": [], "durationId": "ce8b3ca4-79c9-4637-bdba-bd569f7d4b24", "parent": "06414f03-9516-45fd-a2d6-d3fb33096aed"}}, {"head": {"id": "f056ecdf-1ca7-432a-a789-592d93521b88", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152700378235600, "endTime": 152702162275000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e7211aee-f22c-46a8-8739-2c9014b67ba2", "logId": "258a69ec-ceaa-4b84-94a0-503546aa5365"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "258a69ec-ceaa-4b84-94a0-503546aa5365", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152700378235600, "endTime": 152702162275000}, "additional": {"logType": "info", "children": [], "durationId": "f056ecdf-1ca7-432a-a789-592d93521b88", "parent": "06414f03-9516-45fd-a2d6-d3fb33096aed"}}, {"head": {"id": "0cd95af5-99dd-406d-adda-358d7ae6d10f", "name": "default@CompileArkTS work[4] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710617326700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06414f03-9516-45fd-a2d6-d3fb33096aed", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 152702164059700, "endTime": 152710597220300}, "additional": {"logType": "info", "children": ["0ed09342-a666-4a6e-8f3f-bfefc2b909cc", "8b878b6b-5349-4c76-9dfa-45c32e48d32e", "03e754ec-aaa0-41ee-8b24-cc1563949fcc", "99f7e899-0757-4270-8979-ba854aebb7c7", "94da0588-d590-44e3-ae5b-d68e8e1f8c30", "f3366b52-ccc7-4532-9af6-a26434604f26", "258a69ec-ceaa-4b84-94a0-503546aa5365"], "durationId": "e7211aee-f22c-46a8-8739-2c9014b67ba2", "parent": "1d6446f1-4518-4df3-8de3-ebd87780e687"}}, {"head": {"id": "1855cc62-2e03-4667-bf2f-6f734272864e", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710618245600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d6446f1-4518-4df3-8de3-ebd87780e687", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152699379446400, "endTime": 152710618544100, "totalTime": 8484924900}, "additional": {"logType": "info", "children": ["06414f03-9516-45fd-a2d6-d3fb33096aed", "1c9329b3-ebbb-4f24-843c-be328259b8c8"], "durationId": "7f12f9cb-8e47-4de5-b03d-62c602553810"}}, {"head": {"id": "dbbbd969-6e94-480d-9ee1-ca053e65e584", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710641247800, "endTime": 152710644979400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "d5c88c18-41d1-4a59-a3a8-863495d70979", "logId": "354c902f-c065-4501-9fc5-29190b02b0e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5c88c18-41d1-4a59-a3a8-863495d70979", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710636178400}, "additional": {"logType": "detail", "children": [], "durationId": "dbbbd969-6e94-480d-9ee1-ca053e65e584"}}, {"head": {"id": "2a675dd4-45a4-4335-8817-8a7c170d7fd0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710638726800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "386f0d01-c8d5-47ab-8dd3-d418c73d4f8d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710638972400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "861740e1-d4e2-4214-8452-6d7277741909", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710641273000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45e15e3a-93ee-4e64-a792-a19697507b44", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710641812900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c6e5c64-2bb5-4aba-8b1d-194320cb29fd", "name": "entry:default@GeneratePkgModuleJson is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710642403000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d781f41-cf8d-4111-874f-014683ddfbb9", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710642549200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2096f744-6b8d-408c-83d4-09f47d06f317", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710642644500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51e68673-c1c5-4e90-a7e0-ca9a0f54539e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710642690300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76d0fe29-9241-4ed1-a1dc-ae695bb3a53b", "name": "entry : default@GeneratePkgModuleJson cost memory 0.12906646728515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710644780500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6b28940-4009-4061-a4d0-eb45dd649d40", "name": "runTaskFromQueue task cost before running: 11 s 684 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710644919300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "354c902f-c065-4501-9fc5-29190b02b0e7", "name": "Finished :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710641247800, "endTime": 152710644979400, "totalTime": 3645000}, "additional": {"logType": "info", "children": [], "durationId": "dbbbd969-6e94-480d-9ee1-ca053e65e584"}}, {"head": {"id": "b3f4068d-8be2-4188-b9e7-1b9f3a3a89a2", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710658581500, "endTime": 152710834306300}, "additional": {"children": ["23e83b87-1c05-4311-b4f9-a47bf2b699af", "92129430-189d-4172-8e40-28ebcef65786"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "39e8b203-fc86-46d9-8989-65cc3acbfb8b", "logId": "030b11e3-d7a8-4c5b-bbb4-a069dc0cacee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39e8b203-fc86-46d9-8989-65cc3acbfb8b", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710647712300}, "additional": {"logType": "detail", "children": [], "durationId": "b3f4068d-8be2-4188-b9e7-1b9f3a3a89a2"}}, {"head": {"id": "a7f97cd1-2c6f-418e-905b-a5c7e0661bfa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710648843000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e25efc7c-d831-4fe0-9a8c-ac28ed49581c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710649009600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68a23acb-16bf-415c-8586-ecfbdb203717", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710658599600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cd6f0e8-cc23-4212-9937-2e88386fba65", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710668168500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a940a679-439c-488a-b2ed-5ce1adfa3089", "name": "Incremental task entry:default@PackageHap pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710668376700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "701ba8be-7c0a-4f70-a31a-3c5377e56133", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710668496600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c35842ee-cc10-416c-aa65-3cbf74d216a4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710668544000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23e83b87-1c05-4311-b4f9-a47bf2b699af", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710670373800, "endTime": 152710672908500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b3f4068d-8be2-4188-b9e7-1b9f3a3a89a2", "logId": "05e1e51e-7952-4ca2-ac91-5dd64cfbe1a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c03d105e-1b22-476e-8e52-0f38b6892d2e", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710672715200}, "additional": {"logType": "debug", "children": [], "durationId": "b3f4068d-8be2-4188-b9e7-1b9f3a3a89a2"}}, {"head": {"id": "05e1e51e-7952-4ca2-ac91-5dd64cfbe1a8", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710670373800, "endTime": 152710672908500}, "additional": {"logType": "info", "children": [], "durationId": "23e83b87-1c05-4311-b4f9-a47bf2b699af", "parent": "030b11e3-d7a8-4c5b-bbb4-a069dc0cacee"}}, {"head": {"id": "92129430-189d-4172-8e40-28ebcef65786", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710673645700, "endTime": 152710827961000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b3f4068d-8be2-4188-b9e7-1b9f3a3a89a2", "logId": "ce1895cd-52b1-4b2f-a7cd-8b8dd00088da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f958afaf-1ec2-4eef-a1fb-63b2ab75bfd9", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710827048200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce1895cd-52b1-4b2f-a7cd-8b8dd00088da", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710673645700, "endTime": 152710827952300}, "additional": {"logType": "info", "children": [], "durationId": "92129430-189d-4172-8e40-28ebcef65786", "parent": "030b11e3-d7a8-4c5b-bbb4-a069dc0cacee"}}, {"head": {"id": "dc2edfdc-5d20-404d-abfd-964c2c8d9704", "name": "entry : default@PackageHap cost memory 0.2648162841796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710833631000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd9598d2-eb1f-4994-bedf-02a04dba3cba", "name": "runTaskFromQueue task cost before running: 11 s 873 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710833964600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "030b11e3-d7a8-4c5b-bbb4-a069dc0cacee", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710658581500, "endTime": 152710834306300, "totalTime": 175321600}, "additional": {"logType": "info", "children": ["05e1e51e-7952-4ca2-ac91-5dd64cfbe1a8", "ce1895cd-52b1-4b2f-a7cd-8b8dd00088da"], "durationId": "b3f4068d-8be2-4188-b9e7-1b9f3a3a89a2"}}, {"head": {"id": "613ba7e2-1aa1-497e-ae5e-8c1d7d1edd16", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710842751900, "endTime": 152711161631400}, "additional": {"children": ["5690d216-35e6-4497-82ec-dedf51190a7b", "946ceb65-f0b3-480e-b9d1-53d02f9cdbd4"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "8c3ef889-a5b7-490b-8a0b-71c3bf155e2f", "logId": "f74f0b95-3377-40a2-a52a-93025ef627df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c3ef889-a5b7-490b-8a0b-71c3bf155e2f", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710838578000}, "additional": {"logType": "detail", "children": [], "durationId": "613ba7e2-1aa1-497e-ae5e-8c1d7d1edd16"}}, {"head": {"id": "af13b05f-b728-4be4-a28b-d3b2ca6a6043", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710839585100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00760d98-2b64-4ef6-b282-eea0b35d78c7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710839699600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08566280-8492-4799-bae9-4c4bdfa8549a", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710842764500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfd93574-25f8-4805-90ca-3f3f71d98464", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710846024800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dde037c-2ca5-4b2e-81e4-7f770d3650fa", "name": "Incremental task entry:default@SignHap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710846175200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26211d61-00c7-4c1f-b5b4-def92645955a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710846265900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0438806d-5228-43ee-aa2b-2d0c3a89986d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710846307200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5690d216-35e6-4497-82ec-dedf51190a7b", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710848671300, "endTime": 152710955697600}, "additional": {"children": ["ec1b3b0e-4593-4765-9956-60db2295b24a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "613ba7e2-1aa1-497e-ae5e-8c1d7d1edd16", "logId": "3ed741ee-bee6-444d-b10c-c437f21857a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec1b3b0e-4593-4765-9956-60db2295b24a", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710871724400, "endTime": 152710954357000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5690d216-35e6-4497-82ec-dedf51190a7b", "logId": "61be5c80-ebb1-488f-bf97-4f26751eafbc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e9e9914-98ec-4576-8c02-8babb5269395", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710875864600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f3c1168-82fe-4d9e-adb0-9c59c86820a2", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710953905200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61be5c80-ebb1-488f-bf97-4f26751eafbc", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710871724400, "endTime": 152710954357000}, "additional": {"logType": "info", "children": [], "durationId": "ec1b3b0e-4593-4765-9956-60db2295b24a", "parent": "3ed741ee-bee6-444d-b10c-c437f21857a9"}}, {"head": {"id": "3ed741ee-bee6-444d-b10c-c437f21857a9", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710848671300, "endTime": 152710955697600}, "additional": {"logType": "info", "children": ["61be5c80-ebb1-488f-bf97-4f26751eafbc"], "durationId": "5690d216-35e6-4497-82ec-dedf51190a7b", "parent": "f74f0b95-3377-40a2-a52a-93025ef627df"}}, {"head": {"id": "946ceb65-f0b3-480e-b9d1-53d02f9cdbd4", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710956449100, "endTime": 152711161190600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "613ba7e2-1aa1-497e-ae5e-8c1d7d1edd16", "logId": "182384d8-eae8-4c4c-9b48-08e2023ffabe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82a9f3a6-ef01-4276-b980-85911549765e", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710958515800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65ba387a-b028-4186-8aad-9281cdecf2a6", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711160783500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "182384d8-eae8-4c4c-9b48-08e2023ffabe", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710956449100, "endTime": 152711161190600}, "additional": {"logType": "info", "children": [], "durationId": "946ceb65-f0b3-480e-b9d1-53d02f9cdbd4", "parent": "f74f0b95-3377-40a2-a52a-93025ef627df"}}, {"head": {"id": "566f2c20-d325-47f7-afda-5d342f0ff926", "name": "entry : default@SignHap cost memory 0.12244415283203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711161462700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7fd7aef-f5bd-422b-873a-9a83264c034e", "name": "runTaskFromQueue task cost before running: 12 s 201 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711161579000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f74f0b95-3377-40a2-a52a-93025ef627df", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152710842751900, "endTime": 152711161631400, "totalTime": 318803200}, "additional": {"logType": "info", "children": ["3ed741ee-bee6-444d-b10c-c437f21857a9", "182384d8-eae8-4c4c-9b48-08e2023ffabe"], "durationId": "613ba7e2-1aa1-497e-ae5e-8c1d7d1edd16"}}, {"head": {"id": "2d46d65a-ce0a-4c15-b486-cd32fc34f5aa", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711164875000, "endTime": 152711170259200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "918d8950-92fc-4f88-a1d4-2a0d3528cf86", "logId": "262ac7fb-4650-4666-87eb-02cf2ece80ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "918d8950-92fc-4f88-a1d4-2a0d3528cf86", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711163405300}, "additional": {"logType": "detail", "children": [], "durationId": "2d46d65a-ce0a-4c15-b486-cd32fc34f5aa"}}, {"head": {"id": "05ddb516-de5f-48e1-8d60-870b3de2e4c7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711164142300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "738acd89-fd6c-4123-afeb-bbcf803555e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711164221900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27f62b37-1169-4778-a357-e25ac0fae170", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711164882300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fb31fae-7cc6-40f0-b7a2-32473fc2a550", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711169945100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "408da0e7-05f3-44cb-b023-8a2a3bf3625f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711170045600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef37fcd8-1c0a-44a4-b2bd-538d6feaa081", "name": "entry : default@CollectDebugSymbol cost memory -0.5923995971679688", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711170144500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ebb5dbb-0000-4e13-b27f-443d8d33f60b", "name": "runTaskFromQueue task cost before running: 12 s 210 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711170219000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "262ac7fb-4650-4666-87eb-02cf2ece80ad", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711164875000, "endTime": 152711170259200, "totalTime": 5321200}, "additional": {"logType": "info", "children": [], "durationId": "2d46d65a-ce0a-4c15-b486-cd32fc34f5aa"}}, {"head": {"id": "17769d49-1feb-436a-96b5-1a77c42aedd4", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711171713500, "endTime": 152711172115400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "1e7aeb09-85a8-41e3-bb0b-1b027442c15a", "logId": "62ac9711-0eb3-40ba-b4f2-4635d55dc9f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e7aeb09-85a8-41e3-bb0b-1b027442c15a", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711171671000}, "additional": {"logType": "detail", "children": [], "durationId": "17769d49-1feb-436a-96b5-1a77c42aedd4"}}, {"head": {"id": "063d9eaa-8c09-486d-b2f4-419edae8909a", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711171719200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09279e0b-8a28-4542-97ac-15e3177246ce", "name": "entry : assembleHap cost memory 0.042449951171875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711171989000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "433a01d6-d0fb-4e3e-9396-46f031e9503e", "name": "runTaskFromQueue task cost before running: 12 s 211 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711172073300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62ac9711-0eb3-40ba-b4f2-4635d55dc9f9", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711171713500, "endTime": 152711172115400, "totalTime": 340000}, "additional": {"logType": "info", "children": [], "durationId": "17769d49-1feb-436a-96b5-1a77c42aedd4"}}, {"head": {"id": "1bf2ff01-8c4a-41e0-8646-f32e21e2e858", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711184200000, "endTime": 152711184231300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f22653d8-c0ac-45bd-9855-54e500d20ebf", "logId": "04b7838a-6538-4131-9cc7-8a30073170d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04b7838a-6538-4131-9cc7-8a30073170d6", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711184200000, "endTime": 152711184231300}, "additional": {"logType": "info", "children": [], "durationId": "1bf2ff01-8c4a-41e0-8646-f32e21e2e858"}}, {"head": {"id": "86d2e057-1cf8-4da0-b7d9-a05946c4764b", "name": "BUILD SUCCESSFUL in 12 s 224 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711184344400}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "061c7a57-a029-456c-b81b-f2760fb35927", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152698961191400, "endTime": 152711184994100}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 22, "second": 20}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "d814395b-918e-406a-8440-f921a7e6d73b", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711185102400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e3d77c0-5667-4d51-a8ca-8e4a337986b1", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711185413800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0228b66-12a2-4037-b607-d3d34b8c1164", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711185987400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6aa0c035-5b78-42c0-9913-bb648ec78f4a", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711186072700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46c280fa-2392-436f-9278-4bf4680ff36a", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711186127200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e57ee82-f28c-49e0-891f-b15392cf4ea5", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711186178600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a51cbc9a-2b32-4e45-abef-4fad10d80be7", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711186246000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76b75d44-d37d-4b96-855a-b0d8e98e6c35", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711186921900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26283d90-19c1-4f9e-af4e-14ca8793e466", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711187171300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec9445b9-7041-49af-a7ee-8e7233e733bf", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711187247400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e4f0fc9-5923-4362-a6e1-c0ce826daedf", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711187281800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44ef431d-c4c8-49fc-8ba3-6a76a8be2ec6", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711187309500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ff6c89c-660a-4ab9-845b-d9a2760b0def", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711187336800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21999180-c991-42cb-889a-85b9d5d6e8ff", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711188444400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9684d798-d5f8-4445-a06a-a2377702591d", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711189239800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a359c83a-707f-4219-b3e4-d52712ac14b1", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711189472000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d529c54-d2b1-4af7-9bed-8ff270fa2281", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711189530600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ad28a07-2893-4399-b4e0-f9d8a64da74a", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711189569300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abd63075-1345-4f68-a045-8c958b12aa7f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711189596600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ead0246-3c83-4ed0-af52-d76b67ebfffa", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711189624100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86ed810b-c073-4d06-98b8-04acd1e4ed00", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711189652300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5222477-b97f-4c19-bca3-5f91059c415b", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711192922600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55ec4b98-79b9-4c32-852d-d828a3553435", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711193557300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9a09764-6b04-4d87-a842-9545b6fbfdb1", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711193930200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "212fb996-1708-424c-9548-cb40c19c9da4", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711194170700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea6815ff-bbbc-4766-a9bf-b9861d5fa457", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711194370100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1204daed-520c-40aa-84b3-f8a03b5a2c14", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711195036700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "506c0741-5542-4310-9967-fd153e2c922d", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711200918000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5df489c4-140c-4c85-aa5e-41337201d7fa", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711201123500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24533b00-04a1-4b3d-a10c-87cbff3e9d54", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711201434300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93f231b2-313b-4b12-8880-e19296f4ffa8", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711202137800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3bc35fc-166a-4c9a-8f63-77ab5eaec7f7", "name": "Incremental task entry:default@CompileArkTS post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711202996300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9d556ba-bea3-43fe-a569-f0fad433386b", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711204533900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "717751d4-398b-4cb3-8403-8ee8f410dd67", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711205083800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d13cff7-8ac8-42a8-8d58-e61fd715e759", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711205401400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c032e1d0-50b6-4b08-826d-cd65c32d7d23", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711205600600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac39ce19-b59f-434a-bfb1-0fd1dc949f24", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711205760000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "263332d6-e8d8-4bf0-9ce0-06e0abd1048c", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711206293000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8f1509c-27ca-40bc-852b-9045e3bb01a2", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711207016600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "874e9f7c-0a6a-4edb-9337-654317b3207b", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711207218500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5f451d6-182a-481c-9e7e-250bb22c89d0", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711207271600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48ba7dbf-6561-45d4-b194-e70fb95fb3ca", "name": "Update task entry:default@GeneratePkgModuleJson input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711207312900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a9d3fdb-4bd7-4fdd-ad1b-2f5b03582b46", "name": "Update task entry:default@GeneratePkgModuleJson output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\package\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711207344100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e013617e-d8a1-42bb-9f79-bd10c153de51", "name": "Incremental task entry:default@GeneratePkgModuleJson post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711207547900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66181738-030a-4368-82d6-400c29275ce3", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711208592800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a41b850a-cf5a-4c72-a6ba-16f2c44dbba4", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711208994000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd579f11-d103-466a-8df9-6904c4a92d18", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711209059500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ecf4168-8c79-4dc4-9b4c-f054685043e7", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711215019700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f7598fb-235f-4123-962d-5b36d69edd76", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711215261800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1adcb416-46a2-4af3-99c4-ea8125762f5c", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711215458200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58cae35d-3d82-4a01-b3af-5fdc767968c0", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711215630400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9f740a6-5137-460e-89f9-2c0ee28416e9", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711216075800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9e3af19-ba96-4bfc-ae29-5b5bf7d93ebb", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711216248800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed2ff42d-c1a6-4526-b9d6-40966c15f7a5", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711216413800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "540b0eff-b24a-4026-8c0d-c04c302aceec", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711217213500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd3f6a98-7c87-4b14-808f-f6ac9ce80c36", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711217474100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "995b6d61-67f4-4109-b002-e427e0b7a12e", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711217683200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc7e8482-9e7f-45d3-bce4-dc39e6724a6b", "name": "Incremental task entry:default@PackageHap post-execution cost:11 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711217920900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e5e5f5a-ab76-45d4-bf2c-68f5d31d239a", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711218175400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4575138b-6d12-405d-b0ee-a94cc1ed747a", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711218380000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3499b1e-43c1-4778-8afc-d1ffaf39aa91", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711218575000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26559454-da40-45cd-805d-56bab5d75db6", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711218762400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5a6fedf-3c68-44c0-b4f5-ef1f917ec044", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711218822100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a22b9cd-ced8-40bf-a5e2-e5e641794445", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711219023800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4e533d2-c5ed-435b-afd4-a22c5c5952ea", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711221224700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a246f6d-85e1-4ba7-bf91-995aa1bd2b61", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711221449300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "447ce763-e9d6-48f9-b37c-6cfd845b2c7d", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711221922100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d88376db-d657-422f-bf88-3a59be541aad", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152711222297200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}