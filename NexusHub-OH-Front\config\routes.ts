/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */
export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        path: '/user/login',
        layout: false,
        name: 'login',
        component: './user/login',
      },
      {
        path: '/user',
        redirect: '/user/login',
      },
      {
        name: 'register-result',
        icon: 'smile',
        path: '/user/register-result',
        component: './user/register-result',
      },
      {
        name: 'register',
        icon: 'smile',
        path: '/user/register',
        component: './user/register',
      },
      {
        component: '404',
        
      },
    ],
  },
  {
    path: '/callback',
    layout: false,
    component: './callback',
  },
  {
    path: '/',
    redirect: '/dashboard',
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    icon: 'dashboard',
    routes: [
      {
        path: '/dashboard',
        redirect: '/dashboard/workplace',
      },
      {
        name: 'analysis',
        icon: 'BarChartOutlined',
        path: '/dashboard/analysis',
        component: './dashboard/analysis',
        access: 'isAdmin',
      },
      {
        name: 'monitor',
        icon: 'MonitorOutlined',
        path: '/dashboard/monitor',
        component: './dashboard/monitor',
        access: 'isAdmin',
      },
      {
        name: 'workplace',
        icon: 'DesktopOutlined',
        path: '/dashboard/workplace',
        component: './dashboard/workplace',
      },
    ],
  },
  {
    path: '/audit',
    name: 'audit',
    icon: 'SafetyOutlined',
    access: 'isAdmin',
    routes: [
      {
        path: '/audit',
        redirect: '/audit/app',
      },
      {
        name: 'app-audit',
        icon: 'AppstoreOutlined',
        path: '/audit/app',
        component: './App/Audit',
        access: 'isAdmin',
      },
      {
        name: 'version-review',
        icon: 'CheckCircleOutlined',
        path: '/audit/version',
        component: './admin/VersionReview',
        access: 'isAdmin',
      },
    ],
  },
  {
    path: '/statistics',
    name: 'statistics',
    icon: 'LineChartOutlined',
    access: 'canViewStatistics',
    routes: [
      {
        path: '/statistics',
        redirect: '/statistics/app',
      },
      {
        name: 'app-statistics',
        icon: 'AppstoreOutlined',
        path: '/statistics/app',
        component: './Statistics/App',
        access: 'canViewStatistics',
      },
      {
        name: 'developer-statistics',
        icon: 'DashboardOutlined',
        path: '/statistics/developer',
        component: './admin/developer/dashboard',
        access: 'canManageRole',
      },
    ],
  },
  {
    path: '/user-management',
    name: 'user-management',
    icon: 'TeamOutlined',
    access: 'canViewUserList',
    routes: [
      {
        path: '/user-management',
        redirect: '/user-management/list',
      },
      {
        name: 'user-list',
        icon: 'UnorderedListOutlined',
        path: '/user-management/list',
        component: './UserManagement/List',
        access: 'canViewUserList',
      },
      {
        name: 'user-detail',
        icon: 'UserOutlined',
        path: '/user-management/detail/:id',
        component: './UserManagement/Detail',
        hideInMenu: true,
        access: 'canViewUserList',
      },


      {
        name: 'developer-verify',
        icon: 'SafetyCertificateOutlined',
        path: '/user-management/developer-verify',
        component: './admin/developer/verify',
        access: 'canManageRole',
      },
    ],
  },
  {
    path: '/review',
    name: 'review',
    icon: 'CommentOutlined',
    access: 'canViewAudit',
    routes: [
      {
        path: '/review',
        redirect: '/review/list',
      },
      {
        name: 'review-list',
        icon: 'UnorderedListOutlined',
        path: '/review/list',
        component: './Review/List',
        access: 'canViewAudit',
      },
    ],
  },
  {
    path: '/app',
    name: 'app',
    icon: 'AppstoreOutlined',
    access: 'canViewApp',
    routes: [
      {
        path: '/app',
        redirect: '/app/list',
      },
      {
        name: 'app-list',
        icon: 'UnorderedListOutlined',
        path: '/app/list',
        component: './App/List',
        access: 'canViewApp',
      },
      {
        name: 'app-detail',
        icon: 'InfoCircleOutlined',
        path: '/app/detail/:id',
        component: './App/Detail',
        hideInMenu: true,
        access: 'canViewApp',
      },
      {
        name: 'app-create',
        icon: 'PlusOutlined',
        path: '/app/create',
        component: './App/Create',
        hideInMenu: true,
        access: 'canCreateApp',
      },
      {
        name: 'app-edit',
        icon: 'EditOutlined',
        path: '/app/edit/:id',
        component: './App/Edit',
        hideInMenu: true,
        access: 'canEditApp',
      },
      {
        name: 'featured-collection',
        icon: 'StarOutlined',
        path: '/app/featured-collection',
        component: './App/FeaturedCollection',
        access: 'isAdmin',
      },
    ],
  },
  {
    path: '/settings',
    name: 'settings',
    icon: 'SettingOutlined',
    access: 'canViewSettings',
    routes: [
      {
        path: '/settings',
        redirect: '/settings/tags',
      },
      {
        name: 'tag-management',
        icon: 'TagsOutlined',
        path: '/settings/tags',
        component: './Settings/Tags',
        access: 'canViewSettings',
      },
      {
        name: 'category-management',
        icon: 'FolderOutlined',
        path: '/settings/categories',
        component: './Settings/Categories',
        access: 'canViewSettings',
      },
      {
        name: 'openharmony-versions',
        icon: 'MobileOutlined',
        path: '/settings/openharmony-versions',
        component: './Settings/OpenHarmonyVersions',
        access: 'isAdmin',
      },
    ],
  },
  {
    name: 'account',
    icon: 'UserOutlined',
    path: '/account',
    routes: [
      {
        path: '/account',
        redirect: '/account/settings',
      },
      {
        name: 'settings',
        icon: 'SettingOutlined',
        path: '/account/settings',
        component: './account/settings',
      },
      {
        name: 'notifications',
        icon: 'BellOutlined',
        path: '/account/notifications',
        component: './account/notifications',
      },
    ],
  },
  {
    path: '/developer',
    name: 'developer',
    icon: 'CodeOutlined',
    access: 'canViewDeveloper',
    routes: [
      {
        path: '/developer',
        redirect: '/developer/verify',
      },
      {
        name: 'verify',
        icon: 'SafetyCertificateOutlined',
        path: '/developer/verify',
        component: './developer/verify',
        access: 'canViewDeveloper',
      },
    ],
  },
  {
    name: 'app-versions',
    icon: 'AppstoreOutlined',
    path: '/app/list/versions/:id',
    component: './App/Versions',
    hideInMenu: true,
    access: 'canViewDeveloper',
  },
  {
    name: 'create-version',
    icon: 'PlusOutlined',
    path: '/app/list/versions/:id/create',
    component: './App/Versions/CreateEdit',
    hideInMenu: true,
    access: 'canViewDeveloper',
  },
  {
    name: 'edit-version',
    icon: 'EditOutlined',
    path: '/app/list/versions/:id/edit/:versionId',
    component: './App/Versions/CreateEdit',
    hideInMenu: true,
    access: 'canViewDeveloper',
  },
  {
    path: '/',
    redirect: '/dashboard/workplace',
  },
  {
    component: '404',
    path: '/*',
  },
];
