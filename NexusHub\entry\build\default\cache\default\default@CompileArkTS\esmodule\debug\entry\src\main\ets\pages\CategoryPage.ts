if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface CategoryPage_Params {
    apps?: AppModel[];
    loadingState?: LoadingState;
    currentPage?: number;
    hasMore?: boolean;
    isLoadingMore?: boolean;
    sortBy?: string;
    showSortMenu?: boolean;
    refreshing?: boolean;
    categoryId?: string;
    categoryName?: string;
    deviceUtils?;
    apiService?;
    httpClient?;
    searchParams?: AppSearchParams;
}
import type { AppModel, AppSearchParams, AppListResponse } from '../models/App';
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { HttpClient } from "@normalized:N&&&entry/src/main/ets/services/HttpClient&";
import { AppCard } from "@normalized:N&&&entry/src/main/ets/components/AppCard&";
import { LoadingView, LoadingState, LoadMoreView } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import preferences from "@ohos:data.preferences";
import hilog from "@ohos:hilog";
interface CategoryPageParams {
    categoryId: string;
    categoryName: string;
}
interface SortOption {
    key: string;
    label: string;
    icon: Resource;
}
class CategoryPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__apps = new ObservedPropertyObjectPU([], this, "apps");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.LOADING, this, "loadingState");
        this.__currentPage = new ObservedPropertySimplePU(1, this, "currentPage");
        this.__hasMore = new ObservedPropertySimplePU(true, this, "hasMore");
        this.__isLoadingMore = new ObservedPropertySimplePU(false, this, "isLoadingMore");
        this.__sortBy = new ObservedPropertySimplePU('downloadCount', this, "sortBy");
        this.__showSortMenu = new ObservedPropertySimplePU(false, this, "showSortMenu");
        this.__refreshing = new ObservedPropertySimplePU(false, this, "refreshing");
        this.categoryId = '';
        this.categoryName = '';
        this.deviceUtils = DeviceUtils.getInstance();
        this.apiService = ApiService.getInstance();
        this.httpClient = HttpClient.getInstance();
        this.searchParams = {
            page: 1,
            page_size: 20
        };
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: CategoryPage_Params) {
        if (params.apps !== undefined) {
            this.apps = params.apps;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.currentPage !== undefined) {
            this.currentPage = params.currentPage;
        }
        if (params.hasMore !== undefined) {
            this.hasMore = params.hasMore;
        }
        if (params.isLoadingMore !== undefined) {
            this.isLoadingMore = params.isLoadingMore;
        }
        if (params.sortBy !== undefined) {
            this.sortBy = params.sortBy;
        }
        if (params.showSortMenu !== undefined) {
            this.showSortMenu = params.showSortMenu;
        }
        if (params.refreshing !== undefined) {
            this.refreshing = params.refreshing;
        }
        if (params.categoryId !== undefined) {
            this.categoryId = params.categoryId;
        }
        if (params.categoryName !== undefined) {
            this.categoryName = params.categoryName;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
        if (params.httpClient !== undefined) {
            this.httpClient = params.httpClient;
        }
        if (params.searchParams !== undefined) {
            this.searchParams = params.searchParams;
        }
    }
    updateStateVars(params: CategoryPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__apps.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__currentPage.purgeDependencyOnElmtId(rmElmtId);
        this.__hasMore.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoadingMore.purgeDependencyOnElmtId(rmElmtId);
        this.__sortBy.purgeDependencyOnElmtId(rmElmtId);
        this.__showSortMenu.purgeDependencyOnElmtId(rmElmtId);
        this.__refreshing.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__apps.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__currentPage.aboutToBeDeleted();
        this.__hasMore.aboutToBeDeleted();
        this.__isLoadingMore.aboutToBeDeleted();
        this.__sortBy.aboutToBeDeleted();
        this.__showSortMenu.aboutToBeDeleted();
        this.__refreshing.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __apps: ObservedPropertyObjectPU<AppModel[]>;
    get apps() {
        return this.__apps.get();
    }
    set apps(newValue: AppModel[]) {
        this.__apps.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __currentPage: ObservedPropertySimplePU<number>;
    get currentPage() {
        return this.__currentPage.get();
    }
    set currentPage(newValue: number) {
        this.__currentPage.set(newValue);
    }
    private __hasMore: ObservedPropertySimplePU<boolean>;
    get hasMore() {
        return this.__hasMore.get();
    }
    set hasMore(newValue: boolean) {
        this.__hasMore.set(newValue);
    }
    private __isLoadingMore: ObservedPropertySimplePU<boolean>;
    get isLoadingMore() {
        return this.__isLoadingMore.get();
    }
    set isLoadingMore(newValue: boolean) {
        this.__isLoadingMore.set(newValue);
    }
    private __sortBy: ObservedPropertySimplePU<string>;
    get sortBy() {
        return this.__sortBy.get();
    }
    set sortBy(newValue: string) {
        this.__sortBy.set(newValue);
    }
    private __showSortMenu: ObservedPropertySimplePU<boolean>;
    get showSortMenu() {
        return this.__showSortMenu.get();
    }
    set showSortMenu(newValue: boolean) {
        this.__showSortMenu.set(newValue);
    }
    private __refreshing: ObservedPropertySimplePU<boolean>;
    get refreshing() {
        return this.__refreshing.get();
    }
    set refreshing(newValue: boolean) {
        this.__refreshing.set(newValue);
    }
    private categoryId: string;
    private categoryName: string;
    private deviceUtils;
    private apiService;
    private httpClient;
    private searchParams: AppSearchParams;
    aboutToAppear() {
        const params = this.getUIContext().getRouter().getParams() as CategoryPageParams;
        this.categoryId = params?.categoryId || '';
        this.categoryName = params?.categoryName || '分类';
        if (this.categoryId) {
            this.loadApps();
        }
    }
    /**
     * 检查并设置认证token
     */
    private async checkAndSetAuthToken(): Promise<void> {
        try {
            const context = this.getUIContext().getHostContext();
            const options: preferences.Options = { name: 'user_data' };
            const dataPreferences = preferences.getPreferencesSync(context, options);
            const token = dataPreferences.getSync('token', '') as string;
            if (token) {
                this.apiService.setAuthToken(token);
            }
        }
        catch (error) {
            hilog.error(0x0000, 'CategoryPage', '检查登录状态失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 加载应用列表
     */
    async loadApps(loadMore: boolean = false) {
        if (this.isLoadingMore) {
            return;
        }
        this.isLoadingMore = true;
        // 只有在非加载更多的情况下才显示加载状态
        if (!loadMore) {
            this.loadingState = LoadingState.LOADING;
        }
        try {
            await this.checkAndSetAuthToken();
            if (!loadMore) {
                this.currentPage = 1;
                this.apps = [];
            }
            const searchParams: AppSearchParams = {
                category: this.categoryId,
                page: this.currentPage,
                page_size: Constants.PAGE_SIZE,
                sort: this.sortBy
            };
            hilog.info(0x0000, 'CategoryPage', 'Loading apps with params: %{public}s', JSON.stringify(searchParams));
            // 构建查询字符串
            const queryString = Object.entries(searchParams)
                .filter((entry: [
                string,
                string | number | undefined
            ]) => entry[1] !== undefined && entry[1] !== null)
                .map((entry: [
                string,
                string | number
            ]) => `${encodeURIComponent(entry[0])}=${encodeURIComponent(String(entry[1]))}`)
                .join('&');
            // 使用重试机制获取应用列表
            const response: AppListResponse = await this.httpClient.getWithRetry(`/public/apps?${queryString}`);
            hilog.info(0x0000, 'CategoryPage', `API Response: ${JSON.stringify(response)}`);
            if (response && response.code === 200) {
                // 检查响应数据结构 - 使用后端返回的list字段
                if (response.data && response.data.list && Array.isArray(response.data.list)) {
                    const newApps = response.data.list;
                    if (loadMore) {
                        this.apps = [...this.apps, ...newApps];
                    }
                    else {
                        this.apps = newApps;
                    }
                    this.currentPage++;
                    this.hasMore = response.data.pagination ? response.data.pagination.hasNext === true : newApps.length >= Constants.PAGE_SIZE;
                    // 根据是否有数据设置状态
                    this.loadingState = this.apps.length > 0 ? LoadingState.SUCCESS : LoadingState.EMPTY;
                }
                else {
                    // 数据结构不正确，设置为空状态
                    hilog.warn(0x0000, 'CategoryPage', 'Invalid response data structure');
                    this.loadingState = LoadingState.EMPTY;
                    if (!loadMore) {
                        this.apps = [];
                    }
                }
            }
            else {
                // 非200响应码，记录错误并设置错误状态
                hilog.error(0x0000, 'CategoryPage', `API Error: ${response?.code} - ${response?.message}`);
                this.loadingState = LoadingState.ERROR;
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            hilog.error(0x0000, 'CategoryPage', 'Error loading apps: %{public}s', errorMessage);
            // 根据错误类型设置不同的状态
            if (errorMessage.includes('超时') || errorMessage.includes('网络')) {
                this.loadingState = LoadingState.ERROR;
            }
            else {
                this.loadingState = LoadingState.ERROR;
            }
        }
        finally {
            this.isLoadingMore = false;
        }
    }
    /**
     * 刷新数据
     */
    private async refreshData() {
        this.refreshing = true;
        await this.loadApps();
        this.refreshing = false;
    }
    /**
     * 跳转到应用详情
     */
    private navigateToAppDetail(appId: string) {
        this.getUIContext().getRouter().pushUrl({
            url: 'pages/AppDetailPage',
            params: { appId }
        });
    }
    /**
     * 排序菜单
     */
    private SortMenu(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.showSortMenu) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Context.animation({ duration: 300, curve: Curve.EaseInOut });
                        Column.width('100%');
                        Column.backgroundColor(Constants.COLORS.WHITE);
                        Column.borderRadius({ topLeft: Constants.BORDER_RADIUS.LARGE, topRight: Constants.BORDER_RADIUS.LARGE });
                        Column.padding({ top: '8vp', bottom: '8vp' });
                        Context.animation(null);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const sort = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Row.create({ space: 12 });
                                Row.width('100%');
                                Row.height(48);
                                Row.padding({ left: '16vp', right: '16vp' });
                                Row.onClick(() => {
                                    if (this.sortBy !== sort.key) {
                                        this.sortBy = sort.key;
                                        this.loadApps();
                                    }
                                    this.showSortMenu = false;
                                });
                            }, Row);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Image.create(sort.icon);
                                Image.width(20);
                                Image.height(20);
                                Image.fillColor(this.sortBy === sort.key ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_SECONDARY);
                            }, Image);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(sort.label);
                                Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                                Text.fontColor(this.sortBy === sort.key ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_PRIMARY);
                                Text.layoutWeight(1);
                            }, Text);
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                If.create();
                                if (this.sortBy === sort.key) {
                                    this.ifElseBranchUpdateFunction(0, () => {
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Image.create({ "id": 16777248, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                                            Image.width(16);
                                            Image.height(16);
                                            Image.fillColor(Constants.COLORS.PRIMARY);
                                            Image.onError(() => {
                                                console.error('CategoryPage SortMenu: Failed to load check icon');
                                            });
                                        }, Image);
                                    });
                                }
                                else {
                                    this.ifElseBranchUpdateFunction(1, () => {
                                    });
                                }
                            }, If);
                            If.pop();
                            Row.pop();
                        };
                        this.forEachUpdateFunction(elmtId, [
                            { key: 'downloadCount', label: '下载量', icon: { "id": 16777252, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } },
                            { key: 'rating', label: '评分', icon: { "id": 16777274, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } },
                            { key: 'updatedAt', label: '更新时间', icon: { "id": 16777269, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } },
                            { key: 'createdAt', label: '发布时间', icon: { "id": 16777266, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } },
                            { key: 'name', label: '名称', icon: { "id": 16777264, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } }
                        ], forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                });
            }
            else /**
             * 应用网格（平板设备）
             */ {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    /**
     * 应用网格（平板设备）
     */
    private AppGrid(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Grid.create();
            Grid.columnsTemplate('1fr 1fr 1fr');
            Grid.rowsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Grid.columnsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Grid.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Grid.scrollBar(BarState.Auto);
        }, Grid);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const app = _item;
                {
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        GridItem.create(() => { }, false);
                    };
                    const observedDeepRender = () => {
                        this.observeComponentCreation2(itemCreation2, GridItem);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            __Common__.create();
                            __Common__.onClick(() => this.navigateToAppDetail(app.id.toString()));
                        }, __Common__);
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new AppCard(this, {
                                        app: app,
                                        cardType: 'grid',
                                        showDownloadButton: true
                                    }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/CategoryPage.ets", line: 249, col: 11 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {
                                            app: app,
                                            cardType: 'grid',
                                            showDownloadButton: true
                                        };
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {
                                        app: app,
                                        cardType: 'grid',
                                        showDownloadButton: true
                                    });
                                }
                            }, { name: "AppCard" });
                        }
                        __Common__.pop();
                        GridItem.pop();
                    };
                    observedDeepRender();
                }
            };
            this.forEachUpdateFunction(elmtId, this.apps, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 加载更多
            if (this.hasMore || this.isLoadingMore) {
                this.ifElseBranchUpdateFunction(0, () => {
                    {
                        const itemCreation2 = (elmtId, isInitialRender) => {
                            GridItem.create(() => { }, false);
                            GridItem.columnStart(0);
                            GridItem.columnEnd(2);
                        };
                        const observedDeepRender = () => {
                            this.observeComponentCreation2(itemCreation2, GridItem);
                            {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    if (isInitialRender) {
                                        let componentCall = new LoadMoreView(this, {
                                            isLoading: this.isLoadingMore,
                                            hasMore: this.hasMore,
                                            onLoadMore: () => {
                                                if (!this.isLoadingMore && this.hasMore) {
                                                    this.loadApps(true);
                                                }
                                            }
                                        }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/CategoryPage.ets", line: 261, col: 11 });
                                        ViewPU.create(componentCall);
                                        let paramsLambda = () => {
                                            return {
                                                isLoading: this.isLoadingMore,
                                                hasMore: this.hasMore,
                                                onLoadMore: () => {
                                                    if (!this.isLoadingMore && this.hasMore) {
                                                        this.loadApps(true);
                                                    }
                                                }
                                            };
                                        };
                                        componentCall.paramsGenerator_ = paramsLambda;
                                    }
                                    else {
                                        this.updateStateVarsOfChildByElmtId(elmtId, {
                                            isLoading: this.isLoadingMore,
                                            hasMore: this.hasMore
                                        });
                                    }
                                }, { name: "LoadMoreView" });
                            }
                            GridItem.pop();
                        };
                        observedDeepRender();
                    }
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Grid.pop();
    }
    /**
     * 应用列表（手机设备）
     */
    private AppList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            List.create({ space: 8 });
            List.scrollBar(BarState.Auto);
        }, List);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const app = _item;
                {
                    const itemCreation = (elmtId, isInitialRender) => {
                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                        itemCreation2(elmtId, isInitialRender);
                        if (!isInitialRender) {
                            ListItem.pop();
                        }
                        ViewStackProcessor.StopGetAccessRecording();
                    };
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        ListItem.create(deepRenderFunction, true);
                    };
                    const deepRenderFunction = (elmtId, isInitialRender) => {
                        itemCreation(elmtId, isInitialRender);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            __Common__.create();
                            __Common__.margin({ left: 16, right: 16 });
                            __Common__.onClick(() => this.navigateToAppDetail(app.id.toString()));
                        }, __Common__);
                        {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                if (isInitialRender) {
                                    let componentCall = new AppCard(this, {
                                        app: app,
                                        cardType: 'list',
                                        showDownloadButton: true
                                    }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/CategoryPage.ets", line: 290, col: 11 });
                                    ViewPU.create(componentCall);
                                    let paramsLambda = () => {
                                        return {
                                            app: app,
                                            cardType: 'list',
                                            showDownloadButton: true
                                        };
                                    };
                                    componentCall.paramsGenerator_ = paramsLambda;
                                }
                                else {
                                    this.updateStateVarsOfChildByElmtId(elmtId, {
                                        app: app,
                                        cardType: 'list',
                                        showDownloadButton: true
                                    });
                                }
                            }, { name: "AppCard" });
                        }
                        __Common__.pop();
                        ListItem.pop();
                    };
                    this.observeComponentCreation2(itemCreation2, ListItem);
                    ListItem.pop();
                }
            };
            this.forEachUpdateFunction(elmtId, this.apps, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 加载更多
            if (this.hasMore || this.isLoadingMore) {
                this.ifElseBranchUpdateFunction(0, () => {
                    {
                        const itemCreation = (elmtId, isInitialRender) => {
                            ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                            itemCreation2(elmtId, isInitialRender);
                            if (!isInitialRender) {
                                ListItem.pop();
                            }
                            ViewStackProcessor.StopGetAccessRecording();
                        };
                        const itemCreation2 = (elmtId, isInitialRender) => {
                            ListItem.create(deepRenderFunction, true);
                        };
                        const deepRenderFunction = (elmtId, isInitialRender) => {
                            itemCreation(elmtId, isInitialRender);
                            {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    if (isInitialRender) {
                                        let componentCall = new LoadMoreView(this, {
                                            isLoading: this.isLoadingMore,
                                            hasMore: this.hasMore,
                                            onLoadMore: () => {
                                                if (!this.isLoadingMore && this.hasMore) {
                                                    this.loadApps(true);
                                                }
                                            }
                                        }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/CategoryPage.ets", line: 303, col: 11 });
                                        ViewPU.create(componentCall);
                                        let paramsLambda = () => {
                                            return {
                                                isLoading: this.isLoadingMore,
                                                hasMore: this.hasMore,
                                                onLoadMore: () => {
                                                    if (!this.isLoadingMore && this.hasMore) {
                                                        this.loadApps(true);
                                                    }
                                                }
                                            };
                                        };
                                        componentCall.paramsGenerator_ = paramsLambda;
                                    }
                                    else {
                                        this.updateStateVarsOfChildByElmtId(elmtId, {
                                            isLoading: this.isLoadingMore,
                                            hasMore: this.hasMore
                                        });
                                    }
                                }, { name: "LoadMoreView" });
                            }
                            ListItem.pop();
                        };
                        this.observeComponentCreation2(itemCreation2, ListItem);
                        ListItem.pop();
                    }
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        List.pop();
    }
    /**
     * 顶部统计信息
     */
    private CategoryStats(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) });
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceAround);
            Row.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Row.backgroundColor(Constants.COLORS.WHITE);
            Row.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Row.margin({ left: 16, right: 16, top: 8, bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 4 });
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create((this.apps?.length || 0).toString());
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Constants.COLORS.PRIMARY);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('应用总数');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 4 });
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getAverageRating().toFixed(1));
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Constants.COLORS.WARNING);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('平均评分');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 4 });
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTotalDownloads());
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Constants.COLORS.SUCCESS);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('总下载量');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    /**
     * 获取平均评分
     */
    private getAverageRating(): number {
        if (!this.apps || this.apps.length === 0)
            return 0;
        const totalRating = this.apps.reduce((sum, app) => sum + (app.rating || 0), 0);
        return totalRating / this.apps.length;
    }
    /**
     * 获取总下载量
     */
    private getTotalDownloads(): string {
        const total = this.apps.reduce((sum, app) => sum + (app.download_count || 0), 0);
        if (total >= 1000000) {
            return (total / 1000000).toFixed(1) + 'M';
        }
        else if (total >= 1000) {
            return (total / 1000).toFixed(1) + 'K';
        }
        return total.toString();
    }
    /**
     * 获取排序标签
     */
    private getSortLabel(): string {
        switch (this.sortBy) {
            case 'downloadCount':
                return '下载量';
            case 'rating':
                return '评分';
            case 'updatedAt':
                return '更新时间';
            case 'createdAt':
                return '发布时间';
            case 'name':
                return '名称';
            default:
                return '排序';
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create({ alignContent: Alignment.Bottom });
            Stack.width('100%');
            Stack.height('100%');
            Stack.onClick(() => {
                if (this.showSortMenu) {
                    this.showSortMenu = false;
                }
            });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.COLORS.BACKGROUND);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: '16vp', right: '16vp' });
            // 顶部导航栏
            Row.justifyContent(FlexAlign.SpaceBetween);
            // 顶部导航栏
            Row.alignItems(VerticalAlign.Center);
            // 顶部导航栏
            Row.backgroundColor(Constants.COLORS.WHITE);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777246, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor(Constants.COLORS.TEXT_PRIMARY);
            Image.onError(() => {
                console.error('CategoryPage: Failed to load back icon');
            });
            Image.onClick(() => {
                this.getUIContext().getRouter().back();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.categoryName);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 8 });
            Row.onClick(() => {
                this.showSortMenu = !this.showSortMenu;
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getSortLabel());
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777273, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(20);
            Image.height(20);
            Image.fillColor(Constants.COLORS.TEXT_PRIMARY);
            Image.onError(() => {
                console.error('CategoryPage: Failed to load sort icon');
            });
        }, Image);
        Row.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, { state: LoadingState.LOADING }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/CategoryPage.ets", line: 455, col: 11 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.LOADING
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.LOADING
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.ERROR,
                                    onRetry: (): void => { this.loadApps(); }
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/CategoryPage.ets", line: 458, col: 11 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.ERROR,
                                        onRetry: (): void => { this.loadApps(); }
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.ERROR
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.EMPTY) {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.EMPTY,
                                    message: '该分类下暂无应用'
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/CategoryPage.ets", line: 464, col: 11 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.EMPTY,
                                        message: '该分类下暂无应用'
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.EMPTY,
                                    message: '该分类下暂无应用'
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(3, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                    }, Column);
                    // 分类统计信息
                    this.CategoryStats.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 应用列表
                        Refresh.create({ refreshing: this.refreshing, offset: 64, friction: 100 });
                        // 应用列表
                        Refresh.onStateChange((refreshStatus: RefreshStatus) => {
                            if (refreshStatus === RefreshStatus.Refresh) {
                                this.refreshData();
                            }
                        });
                        // 应用列表
                        Refresh.layoutWeight(1);
                    }, Refresh);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.deviceUtils.isTablet()) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.AppGrid.bind(this)();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                                this.AppList.bind(this)();
                            });
                        }
                    }, If);
                    If.pop();
                    // 应用列表
                    Refresh.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
        // 排序菜单
        this.SortMenu.bind(this)();
        Stack.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "CategoryPage";
    }
}
registerNamedRoute(() => new CategoryPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/CategoryPage", pageFullPath: "entry/src/main/ets/pages/CategoryPage", integratedHsp: "false", moduleType: "followWithHap" });
