{"version": "2.0", "ppid": 32468, "events": [{"head": {"id": "183f14b5-4563-444a-b024-5c858ab61913", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472361777800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5980cf6b-7b2b-4dc4-aab7-7fa0d8acd148", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472370158800, "endTime": 145474849608600}, "additional": {"children": ["688d6483-53dd-4176-b498-892df2f90bd2", "49811d92-eb6c-4073-a01c-0b2acbcf05c0", "605e95df-14fd-40c8-be7d-2baebfe2157e", "edff7b28-3a7b-4842-9afa-236cac38e9e2", "b8bbd4ca-c8d4-4e06-a56a-36c6f86cbecf", "94935bb5-a7c5-44e0-8531-b85860c8f08c", "a4beeea9-5db3-4bd3-af5a-9645c3cc0f13"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "da4ee9a9-ff6d-4c8a-8642-d95966553c23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "688d6483-53dd-4176-b498-892df2f90bd2", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472370162100, "endTime": 145472386496900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5980cf6b-7b2b-4dc4-aab7-7fa0d8acd148", "logId": "8fe90c99-6878-40b4-80d4-b7119a6e097a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49811d92-eb6c-4073-a01c-0b2acbcf05c0", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472386522600, "endTime": 145474847511300}, "additional": {"children": ["ae5dba19-a2d0-4cbe-939a-f8b801e2cd19", "4a45635a-320e-410d-b822-404e176743ca", "286ad84c-6e36-4a9d-be47-10e35f142fb2", "4999963a-cb98-4161-89ac-763414fcbecd", "a721cfee-6ab1-41ab-b65f-fd254277c3ab", "cbf15302-aacc-4fdf-8473-c726950dcc43", "35d2f8f6-7955-4c7d-9a9f-0c7b7708b71e", "b9a5c63a-7131-481f-96a4-92f37f79ecfe", "00f9f91c-f7eb-421a-a877-289312ccbe4a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5980cf6b-7b2b-4dc4-aab7-7fa0d8acd148", "logId": "a560463e-1f88-4a0b-847e-c7298261110b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "605e95df-14fd-40c8-be7d-2baebfe2157e", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474847536600, "endTime": 145474849581500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5980cf6b-7b2b-4dc4-aab7-7fa0d8acd148", "logId": "e0827cae-8ae8-40ab-ae14-83157f387f80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "edff7b28-3a7b-4842-9afa-236cac38e9e2", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474849586000, "endTime": 145474849601500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5980cf6b-7b2b-4dc4-aab7-7fa0d8acd148", "logId": "63a1a88c-ddd8-4725-bfb1-4eed2f41d955"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8bbd4ca-c8d4-4e06-a56a-36c6f86cbecf", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472374709900, "endTime": 145472374908500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5980cf6b-7b2b-4dc4-aab7-7fa0d8acd148", "logId": "3d4782a1-cb8c-4d0c-918c-3b405612ee81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d4782a1-cb8c-4d0c-918c-3b405612ee81", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472374709900, "endTime": 145472374908500}, "additional": {"logType": "info", "children": [], "durationId": "b8bbd4ca-c8d4-4e06-a56a-36c6f86cbecf", "parent": "da4ee9a9-ff6d-4c8a-8642-d95966553c23"}}, {"head": {"id": "94935bb5-a7c5-44e0-8531-b85860c8f08c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472380662700, "endTime": 145472380709300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5980cf6b-7b2b-4dc4-aab7-7fa0d8acd148", "logId": "b3b7951a-dbc6-4058-a467-d45487cb42ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3b7951a-dbc6-4058-a467-d45487cb42ea", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472380662700, "endTime": 145472380709300}, "additional": {"logType": "info", "children": [], "durationId": "94935bb5-a7c5-44e0-8531-b85860c8f08c", "parent": "da4ee9a9-ff6d-4c8a-8642-d95966553c23"}}, {"head": {"id": "51b42c26-59a0-4dc5-85db-0b168cd74204", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472381150400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7352d00-0548-499d-8391-359e467c3bc5", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472386373600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fe90c99-6878-40b4-80d4-b7119a6e097a", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472370162100, "endTime": 145472386496900}, "additional": {"logType": "info", "children": [], "durationId": "688d6483-53dd-4176-b498-892df2f90bd2", "parent": "da4ee9a9-ff6d-4c8a-8642-d95966553c23"}}, {"head": {"id": "ae5dba19-a2d0-4cbe-939a-f8b801e2cd19", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472391165500, "endTime": 145472391197700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49811d92-eb6c-4073-a01c-0b2acbcf05c0", "logId": "a8b0ec55-3641-4c6c-9aef-ea975743e9fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a45635a-320e-410d-b822-404e176743ca", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472391233500, "endTime": 145472396297200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49811d92-eb6c-4073-a01c-0b2acbcf05c0", "logId": "9cdeba82-a63b-4230-8d1a-e9be01a89bc4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "286ad84c-6e36-4a9d-be47-10e35f142fb2", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472396432700, "endTime": 145474705780900}, "additional": {"children": ["377b1a55-8891-4c7a-a798-e6f027b4e35c", "fd8d8262-ece9-43f6-994b-77a30b7430bf", "c6c14131-d702-4717-946d-d72ee1139429"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49811d92-eb6c-4073-a01c-0b2acbcf05c0", "logId": "56094e7c-3b98-4249-ac90-4dd2b3e116eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4999963a-cb98-4161-89ac-763414fcbecd", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474705979600, "endTime": 145474744874200}, "additional": {"children": ["12233b5f-5847-434c-a210-41525f4535b9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49811d92-eb6c-4073-a01c-0b2acbcf05c0", "logId": "5a5d0f06-5fa6-4f78-8c3a-18ac2bb1c31a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a721cfee-6ab1-41ab-b65f-fd254277c3ab", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474744927300, "endTime": 145474801891200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49811d92-eb6c-4073-a01c-0b2acbcf05c0", "logId": "7b85b9f2-3978-4821-839e-7df31d2ec9ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cbf15302-aacc-4fdf-8473-c726950dcc43", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474803489900, "endTime": 145474822700600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49811d92-eb6c-4073-a01c-0b2acbcf05c0", "logId": "b383363d-247c-4ac5-baf0-35e36dbc150a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35d2f8f6-7955-4c7d-9a9f-0c7b7708b71e", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474822724300, "endTime": 145474847180400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49811d92-eb6c-4073-a01c-0b2acbcf05c0", "logId": "ea48728e-809e-4ff2-b444-6465e6c90d00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9a5c63a-7131-481f-96a4-92f37f79ecfe", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474847205200, "endTime": 145474847496800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49811d92-eb6c-4073-a01c-0b2acbcf05c0", "logId": "2030fb0f-fe02-4c51-9e54-bdd26f827e7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8b0ec55-3641-4c6c-9aef-ea975743e9fe", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472391165500, "endTime": 145472391197700}, "additional": {"logType": "info", "children": [], "durationId": "ae5dba19-a2d0-4cbe-939a-f8b801e2cd19", "parent": "a560463e-1f88-4a0b-847e-c7298261110b"}}, {"head": {"id": "9cdeba82-a63b-4230-8d1a-e9be01a89bc4", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472391233500, "endTime": 145472396297200}, "additional": {"logType": "info", "children": [], "durationId": "4a45635a-320e-410d-b822-404e176743ca", "parent": "a560463e-1f88-4a0b-847e-c7298261110b"}}, {"head": {"id": "377b1a55-8891-4c7a-a798-e6f027b4e35c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472397166300, "endTime": 145472397220900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "286ad84c-6e36-4a9d-be47-10e35f142fb2", "logId": "b9733db1-063d-45d2-af56-9f91f55b8fdf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9733db1-063d-45d2-af56-9f91f55b8fdf", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472397166300, "endTime": 145472397220900}, "additional": {"logType": "info", "children": [], "durationId": "377b1a55-8891-4c7a-a798-e6f027b4e35c", "parent": "56094e7c-3b98-4249-ac90-4dd2b3e116eb"}}, {"head": {"id": "fd8d8262-ece9-43f6-994b-77a30b7430bf", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472399198800, "endTime": 145474704391700}, "additional": {"children": ["5e8c133d-e9d0-4ea3-abdf-5ba17ec47a47", "c136d650-8477-45bf-a036-44af30d8ae58"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "286ad84c-6e36-4a9d-be47-10e35f142fb2", "logId": "2d9a873b-1b88-40bf-ba47-2cda4705a23a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e8c133d-e9d0-4ea3-abdf-5ba17ec47a47", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472399200200, "endTime": 145474451348200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fd8d8262-ece9-43f6-994b-77a30b7430bf", "logId": "7f4d112c-68a7-43e3-9f2b-7de85dd350e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c136d650-8477-45bf-a036-44af30d8ae58", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474451371500, "endTime": 145474704367500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fd8d8262-ece9-43f6-994b-77a30b7430bf", "logId": "18de9e58-e79e-479e-bdbd-48b3ce5de71b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e197b15-766b-450e-ae9c-871fe2f7a50b", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472399206900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85db0d64-84a7-4cfd-932f-69be4a69ac26", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474451194800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f4d112c-68a7-43e3-9f2b-7de85dd350e5", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472399200200, "endTime": 145474451348200}, "additional": {"logType": "info", "children": [], "durationId": "5e8c133d-e9d0-4ea3-abdf-5ba17ec47a47", "parent": "2d9a873b-1b88-40bf-ba47-2cda4705a23a"}}, {"head": {"id": "3826f565-d481-4122-8c4e-978caf880521", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474451489400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04654248-b413-4b08-b02f-3a6f87fded36", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474624978800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7e48705-d367-42d8-b1b7-9b05f8aa4e0c", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474625182900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70425bd2-11be-470b-becc-db2bd8550c65", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474625587700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b28cd5c-e221-4b2f-ba57-52059ebf4597", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474625772900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30e622fe-73fd-4d79-bcab-ffec86e662b9", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474629852900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e42cd65b-4308-42c8-92e8-cb4c2578013b", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474648449400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91e7bfe9-254a-48bc-9938-1dc89dcba6d3", "name": "Sdk init in 37 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474674602200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8599c5b2-8c0b-4582-80b0-cc81c362a598", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474674886200}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 21, "second": 44}, "markType": "other"}}, {"head": {"id": "28514a32-d371-4203-8ae8-0be71f897fca", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474674967100}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 21, "second": 44}, "markType": "other"}}, {"head": {"id": "c2e2e529-91ce-4391-a74e-c6f76a82bb87", "name": "Project task initialization takes 22 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474703542900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee3e8e4b-bbef-4f2c-a13a-53d744c22870", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474704013100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19a02ce9-ee79-4266-bff3-83c27c82594d", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474704151100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4419b2dc-ca92-49f6-a78d-4bdf10fd4648", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474704231400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18de9e58-e79e-479e-bdbd-48b3ce5de71b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474451371500, "endTime": 145474704367500}, "additional": {"logType": "info", "children": [], "durationId": "c136d650-8477-45bf-a036-44af30d8ae58", "parent": "2d9a873b-1b88-40bf-ba47-2cda4705a23a"}}, {"head": {"id": "2d9a873b-1b88-40bf-ba47-2cda4705a23a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472399198800, "endTime": 145474704391700}, "additional": {"logType": "info", "children": ["7f4d112c-68a7-43e3-9f2b-7de85dd350e5", "18de9e58-e79e-479e-bdbd-48b3ce5de71b"], "durationId": "fd8d8262-ece9-43f6-994b-77a30b7430bf", "parent": "56094e7c-3b98-4249-ac90-4dd2b3e116eb"}}, {"head": {"id": "c6c14131-d702-4717-946d-d72ee1139429", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474705686900, "endTime": 145474705750100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "286ad84c-6e36-4a9d-be47-10e35f142fb2", "logId": "62a7c834-f528-4304-beac-bdadb45ea0fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62a7c834-f528-4304-beac-bdadb45ea0fd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474705686900, "endTime": 145474705750100}, "additional": {"logType": "info", "children": [], "durationId": "c6c14131-d702-4717-946d-d72ee1139429", "parent": "56094e7c-3b98-4249-ac90-4dd2b3e116eb"}}, {"head": {"id": "56094e7c-3b98-4249-ac90-4dd2b3e116eb", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472396432700, "endTime": 145474705780900}, "additional": {"logType": "info", "children": ["b9733db1-063d-45d2-af56-9f91f55b8fdf", "2d9a873b-1b88-40bf-ba47-2cda4705a23a", "62a7c834-f528-4304-beac-bdadb45ea0fd"], "durationId": "286ad84c-6e36-4a9d-be47-10e35f142fb2", "parent": "a560463e-1f88-4a0b-847e-c7298261110b"}}, {"head": {"id": "12233b5f-5847-434c-a210-41525f4535b9", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474706861900, "endTime": 145474744847000}, "additional": {"children": ["926f3885-a53f-48fc-90ab-d64597a2cab1", "64c37feb-f055-4936-a0ed-26bad4977aaa", "0ea12e60-1db8-4e5d-8521-2e0df506a6dc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4999963a-cb98-4161-89ac-763414fcbecd", "logId": "3bbc4c87-ef8f-413b-8329-5d1a49219930"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "926f3885-a53f-48fc-90ab-d64597a2cab1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474711745300, "endTime": 145474711778800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12233b5f-5847-434c-a210-41525f4535b9", "logId": "a283d15d-8e77-420f-bc32-7da019995008"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a283d15d-8e77-420f-bc32-7da019995008", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474711745300, "endTime": 145474711778800}, "additional": {"logType": "info", "children": [], "durationId": "926f3885-a53f-48fc-90ab-d64597a2cab1", "parent": "3bbc4c87-ef8f-413b-8329-5d1a49219930"}}, {"head": {"id": "64c37feb-f055-4936-a0ed-26bad4977aaa", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474714371100, "endTime": 145474743378400}, "additional": {"children": ["cdf501e0-e6cb-40eb-9e69-5f297845675e", "808be1a0-9247-4b1d-9fb4-fd22354fb087"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12233b5f-5847-434c-a210-41525f4535b9", "logId": "e56f0bc3-7044-4978-9505-3840be87b604"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdf501e0-e6cb-40eb-9e69-5f297845675e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474714372300, "endTime": 145474720957900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64c37feb-f055-4936-a0ed-26bad4977aaa", "logId": "f511c4cf-512f-4d86-8130-ec7a93d9d8e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "808be1a0-9247-4b1d-9fb4-fd22354fb087", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474720981200, "endTime": 145474743370600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64c37feb-f055-4936-a0ed-26bad4977aaa", "logId": "175558a3-65b0-4b82-b083-bf461c846e49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "967d8a04-84e6-48fd-9eac-70f0eaeebb4b", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474714380800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e71b8726-21b4-4675-ad75-57390fc12b84", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474720809500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f511c4cf-512f-4d86-8130-ec7a93d9d8e2", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474714372300, "endTime": 145474720957900}, "additional": {"logType": "info", "children": [], "durationId": "cdf501e0-e6cb-40eb-9e69-5f297845675e", "parent": "e56f0bc3-7044-4978-9505-3840be87b604"}}, {"head": {"id": "95f5ff99-4c92-42e6-954d-d2236b77d5d5", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474721092800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "013b1635-6a99-46af-9197-d2d5a41b157e", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474734801000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef806112-6b61-4ef0-a212-c712bb0298ff", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474735120200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "742db06f-ce1d-4616-958e-cb50185ab6b0", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474735574600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db7c05e5-03e5-45f7-9a6f-2985c92f716f", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474735835400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4abdbac4-511d-4a7a-9d5d-94afb1d890d3", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474735906600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "365d882d-7482-4c2e-8d38-293ecf0bbe18", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474735950200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54a89598-c956-4db3-b3c2-2a6fa06edf50", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474736017200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e1383db-1724-4987-be81-8b2bda518c6f", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474736073800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df45dfb3-d871-49cc-9004-2e4ca9f87ada", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474736475700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac6b8c38-2347-49e9-ab60-29df1c751437", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474736611900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54f6df34-63e4-4eaa-9641-f89958c40854", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474736667000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afb7ad8b-acc5-4453-ba6a-4b08fd8e1a31", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474736711900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41340e18-142c-4375-8c78-23dd1aaae2d1", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474736878500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc15c634-0f0a-4adf-989f-74855f84a640", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474736947100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f22f3941-72a3-4384-b735-02d0372a2dce", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474737141600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e835b2f-f8be-460a-9d8b-28dd0a7d8f18", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474737255400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51dee047-d962-4604-bfb5-7e93e9f1da3d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474737302600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de8ddf38-bca0-4ed0-b2b3-00022401e216", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474737424600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2add6dc-4500-4f68-b0b3-de925a779b03", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474737663500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0019cf1-9c29-431e-a31c-23d5a5b1de4b", "name": "Module entry task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474743095700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da1e82fe-ad8f-4334-b383-61ff25a302cc", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474743236400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50898520-13b8-46e1-b473-f9b8e10b538d", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474743290000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21d59b7e-5fbb-486e-b644-a44816c864d4", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474743327700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "175558a3-65b0-4b82-b083-bf461c846e49", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474720981200, "endTime": 145474743370600}, "additional": {"logType": "info", "children": [], "durationId": "808be1a0-9247-4b1d-9fb4-fd22354fb087", "parent": "e56f0bc3-7044-4978-9505-3840be87b604"}}, {"head": {"id": "e56f0bc3-7044-4978-9505-3840be87b604", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474714371100, "endTime": 145474743378400}, "additional": {"logType": "info", "children": ["f511c4cf-512f-4d86-8130-ec7a93d9d8e2", "175558a3-65b0-4b82-b083-bf461c846e49"], "durationId": "64c37feb-f055-4936-a0ed-26bad4977aaa", "parent": "3bbc4c87-ef8f-413b-8329-5d1a49219930"}}, {"head": {"id": "0ea12e60-1db8-4e5d-8521-2e0df506a6dc", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474744820700, "endTime": 145474744834300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12233b5f-5847-434c-a210-41525f4535b9", "logId": "9db52093-fdfd-4d5a-8e35-689c7635b84f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9db52093-fdfd-4d5a-8e35-689c7635b84f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474744820700, "endTime": 145474744834300}, "additional": {"logType": "info", "children": [], "durationId": "0ea12e60-1db8-4e5d-8521-2e0df506a6dc", "parent": "3bbc4c87-ef8f-413b-8329-5d1a49219930"}}, {"head": {"id": "3bbc4c87-ef8f-413b-8329-5d1a49219930", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474706861900, "endTime": 145474744847000}, "additional": {"logType": "info", "children": ["a283d15d-8e77-420f-bc32-7da019995008", "e56f0bc3-7044-4978-9505-3840be87b604", "9db52093-fdfd-4d5a-8e35-689c7635b84f"], "durationId": "12233b5f-5847-434c-a210-41525f4535b9", "parent": "5a5d0f06-5fa6-4f78-8c3a-18ac2bb1c31a"}}, {"head": {"id": "5a5d0f06-5fa6-4f78-8c3a-18ac2bb1c31a", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474705979600, "endTime": 145474744874200}, "additional": {"logType": "info", "children": ["3bbc4c87-ef8f-413b-8329-5d1a49219930"], "durationId": "4999963a-cb98-4161-89ac-763414fcbecd", "parent": "a560463e-1f88-4a0b-847e-c7298261110b"}}, {"head": {"id": "9d4416d4-7706-43b7-815f-4c37ec3e5a11", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474761179200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3c385a4-d95e-4873-92f8-0b2e9c8c6aef", "name": "hvigorfile, resolve hvigorfile dependencies in 57 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474801749800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b85b9f2-3978-4821-839e-7df31d2ec9ba", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474744927300, "endTime": 145474801891200}, "additional": {"logType": "info", "children": [], "durationId": "a721cfee-6ab1-41ab-b65f-fd254277c3ab", "parent": "a560463e-1f88-4a0b-847e-c7298261110b"}}, {"head": {"id": "00f9f91c-f7eb-421a-a877-289312ccbe4a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474802854000, "endTime": 145474803438000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49811d92-eb6c-4073-a01c-0b2acbcf05c0", "logId": "dba5ba4c-cc11-4685-93ca-9d1dff8ed56b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4dfe2a7-dd1c-47ba-be45-e4511367c816", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474803014000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dba5ba4c-cc11-4685-93ca-9d1dff8ed56b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474802854000, "endTime": 145474803438000}, "additional": {"logType": "info", "children": [], "durationId": "00f9f91c-f7eb-421a-a877-289312ccbe4a", "parent": "a560463e-1f88-4a0b-847e-c7298261110b"}}, {"head": {"id": "c2cf6fd3-edff-4fe2-875d-2f8206e0a0cd", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474805343000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6dd46e1-16da-45de-8c8f-0b9119a7e666", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474821448500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b383363d-247c-4ac5-baf0-35e36dbc150a", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474803489900, "endTime": 145474822700600}, "additional": {"logType": "info", "children": [], "durationId": "cbf15302-aacc-4fdf-8473-c726950dcc43", "parent": "a560463e-1f88-4a0b-847e-c7298261110b"}}, {"head": {"id": "d3fb899a-d1d3-4bfb-bc96-f41419350441", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474822838600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bca5e88b-20f6-40cf-b15b-996fa78533c3", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474835535600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7c5cf9f-918a-45f0-9436-ee554c44a36c", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474835654400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07a2ce28-fb12-40d4-8a79-c125cd910e2f", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474836304100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1442be78-3089-4a51-bbc1-a5d89c149cd6", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474841110900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c848bc88-f69c-4e62-85ca-f5e7250689eb", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474841232600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea48728e-809e-4ff2-b444-6465e6c90d00", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474822724300, "endTime": 145474847180400}, "additional": {"logType": "info", "children": [], "durationId": "35d2f8f6-7955-4c7d-9a9f-0c7b7708b71e", "parent": "a560463e-1f88-4a0b-847e-c7298261110b"}}, {"head": {"id": "a9549cb6-5587-4da7-97aa-8e5a762bcabb", "name": "Configuration phase cost:2 s 457 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474847376000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2030fb0f-fe02-4c51-9e54-bdd26f827e7c", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474847205200, "endTime": 145474847496800}, "additional": {"logType": "info", "children": [], "durationId": "b9a5c63a-7131-481f-96a4-92f37f79ecfe", "parent": "a560463e-1f88-4a0b-847e-c7298261110b"}}, {"head": {"id": "a560463e-1f88-4a0b-847e-c7298261110b", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472386522600, "endTime": 145474847511300}, "additional": {"logType": "info", "children": ["a8b0ec55-3641-4c6c-9aef-ea975743e9fe", "9cdeba82-a63b-4230-8d1a-e9be01a89bc4", "56094e7c-3b98-4249-ac90-4dd2b3e116eb", "5a5d0f06-5fa6-4f78-8c3a-18ac2bb1c31a", "7b85b9f2-3978-4821-839e-7df31d2ec9ba", "b383363d-247c-4ac5-baf0-35e36dbc150a", "ea48728e-809e-4ff2-b444-6465e6c90d00", "2030fb0f-fe02-4c51-9e54-bdd26f827e7c", "dba5ba4c-cc11-4685-93ca-9d1dff8ed56b"], "durationId": "49811d92-eb6c-4073-a01c-0b2acbcf05c0", "parent": "da4ee9a9-ff6d-4c8a-8642-d95966553c23"}}, {"head": {"id": "a4beeea9-5db3-4bd3-af5a-9645c3cc0f13", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474849519200, "endTime": 145474849568800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5980cf6b-7b2b-4dc4-aab7-7fa0d8acd148", "logId": "15dbdf2a-045b-476c-949d-9d323156a4a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15dbdf2a-045b-476c-949d-9d323156a4a8", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474849519200, "endTime": 145474849568800}, "additional": {"logType": "info", "children": [], "durationId": "a4beeea9-5db3-4bd3-af5a-9645c3cc0f13", "parent": "da4ee9a9-ff6d-4c8a-8642-d95966553c23"}}, {"head": {"id": "e0827cae-8ae8-40ab-ae14-83157f387f80", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474847536600, "endTime": 145474849581500}, "additional": {"logType": "info", "children": [], "durationId": "605e95df-14fd-40c8-be7d-2baebfe2157e", "parent": "da4ee9a9-ff6d-4c8a-8642-d95966553c23"}}, {"head": {"id": "63a1a88c-ddd8-4725-bfb1-4eed2f41d955", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474849586000, "endTime": 145474849601500}, "additional": {"logType": "info", "children": [], "durationId": "edff7b28-3a7b-4842-9afa-236cac38e9e2", "parent": "da4ee9a9-ff6d-4c8a-8642-d95966553c23"}}, {"head": {"id": "da4ee9a9-ff6d-4c8a-8642-d95966553c23", "name": "init", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472370158800, "endTime": 145474849608600}, "additional": {"logType": "info", "children": ["8fe90c99-6878-40b4-80d4-b7119a6e097a", "a560463e-1f88-4a0b-847e-c7298261110b", "e0827cae-8ae8-40ab-ae14-83157f387f80", "63a1a88c-ddd8-4725-bfb1-4eed2f41d955", "3d4782a1-cb8c-4d0c-918c-3b405612ee81", "b3b7951a-dbc6-4058-a467-d45487cb42ea", "15dbdf2a-045b-476c-949d-9d323156a4a8"], "durationId": "5980cf6b-7b2b-4dc4-aab7-7fa0d8acd148"}}, {"head": {"id": "cdde8e6d-bddb-4c53-b694-e34138e96268", "name": "Configuration task cost before running: 2 s 483 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474850183000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "737e7e64-6348-40a5-b3f6-2c562a1ffce7", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474865735100, "endTime": 145474881558100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "b8c25d83-3af9-4e3c-89bc-3cf03ea49156", "logId": "d49e02b9-c727-42ed-aa4f-31334a45f191"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8c25d83-3af9-4e3c-89bc-3cf03ea49156", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474852135600}, "additional": {"logType": "detail", "children": [], "durationId": "737e7e64-6348-40a5-b3f6-2c562a1ffce7"}}, {"head": {"id": "02ff9408-467e-4984-950e-a0e207bc25ed", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474853411200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff3f31c3-8244-4c85-b3ac-648bf10d7d01", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474853645700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3e1e120-d735-4574-b470-d34abac288d2", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474855113200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9dfe0b7-710f-4e62-b9ad-1dfa8ecb710d", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474857213900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e78eb957-0c02-4d3f-b13a-8217df81ada7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474859666700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0984f1e7-30b4-40d8-8b32-dc463e283824", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474859774300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cfd71b0-7ae9-461b-a7ca-be08139b4656", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474865775000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5a5b8fe-4f54-4405-aa72-9285e3e4aedc", "name": "Incremental task entry:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474881195600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a166af5-11da-480e-a743-ae77070a1489", "name": "entry : default@PreBuild cost memory 0.47048187255859375", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474881393700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d49e02b9-c727-42ed-aa4f-31334a45f191", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474865735100, "endTime": 145474881558100}, "additional": {"logType": "info", "children": [], "durationId": "737e7e64-6348-40a5-b3f6-2c562a1ffce7"}}, {"head": {"id": "ba3fa1be-8ba6-4e09-9414-8050c1ae5671", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474888461100, "endTime": 145474890956300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "65a1bb75-96d6-4f23-b970-50ea95896ad3", "logId": "a530ac97-9b61-4962-a3b4-38a8afc75e86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65a1bb75-96d6-4f23-b970-50ea95896ad3", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474886298700}, "additional": {"logType": "detail", "children": [], "durationId": "ba3fa1be-8ba6-4e09-9414-8050c1ae5671"}}, {"head": {"id": "2c493e44-aa67-4fb4-999d-58c01fc17452", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474887547800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e275e4e-3eb1-485b-b3ad-ff80d899b448", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474887702600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13f7eaa9-a70b-4ef9-b021-efb18bc5d9fb", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474888475600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "939ed077-d7ff-4965-a948-032d6b4f2d7f", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474889547600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3129c1bc-4672-48de-a323-c70175fdc207", "name": "entry : default@CreateModuleInfo cost memory 0.06377410888671875", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474890658600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "040583b4-8800-40f6-8879-1c1cf57882c7", "name": "runTaskFromQueue task cost before running: 2 s 524 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474890794400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a530ac97-9b61-4962-a3b4-38a8afc75e86", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474888461100, "endTime": 145474890956300, "totalTime": 2311700}, "additional": {"logType": "info", "children": [], "durationId": "ba3fa1be-8ba6-4e09-9414-8050c1ae5671"}}, {"head": {"id": "d06af4e3-8a5a-4610-861b-2e4fab60e582", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474903695200, "endTime": 145474906690100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "17bb9772-9b86-49c7-aaa4-d59d62ceebac", "logId": "6da63045-9d02-4cf3-a061-ab0afe7d69c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17bb9772-9b86-49c7-aaa4-d59d62ceebac", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474893580700}, "additional": {"logType": "detail", "children": [], "durationId": "d06af4e3-8a5a-4610-861b-2e4fab60e582"}}, {"head": {"id": "fe67efb7-8cd1-4ad6-9cd5-122e16d59342", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474896326100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "735b5b73-fbd9-431a-a300-1532a1f51ede", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474896514900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf81defa-2bc0-4686-bf99-d5e1caee1e4f", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474903715700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7017fae-73e7-44b3-8cf0-eef1c7ce3e12", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474905173300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b39514f-55cb-4d7f-88dd-f9e27e148bfa", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474906490300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c84809f0-bb41-405f-9d82-33499ac80af6", "name": "entry : default@GenerateMetadata cost memory 0.108062744140625", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474906621600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6da63045-9d02-4cf3-a061-ab0afe7d69c9", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474903695200, "endTime": 145474906690100}, "additional": {"logType": "info", "children": [], "durationId": "d06af4e3-8a5a-4610-861b-2e4fab60e582"}}, {"head": {"id": "8cb17e2e-1071-491c-a69e-acfe0751742d", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474910924700, "endTime": 145474911787600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d5c3e5c8-af38-4577-bf8f-a0bc2d185c15", "logId": "f414081a-e96a-4d61-8e48-4ad2711336ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5c3e5c8-af38-4577-bf8f-a0bc2d185c15", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474908526200}, "additional": {"logType": "detail", "children": [], "durationId": "8cb17e2e-1071-491c-a69e-acfe0751742d"}}, {"head": {"id": "9c2b41d1-2016-44d7-a5b0-3260423dc4c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474909910800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5a8ab20-e204-43d1-a4e7-3b253dc32440", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474910194200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2c4a3d0-63f2-4856-9f73-1e2fe7026b5f", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474910946500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0727d51-8068-4d78-8174-6f99b0f7a850", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474911413600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5082597d-3133-4cfc-a438-adce93abf735", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474911515100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f07fe2f6-0f7a-4bb9-826a-84701ccc8167", "name": "entry : default@ConfigureCmake cost memory 0.037750244140625", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474911627700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d78c3d8-491f-436e-b9d4-8c41fb0b2a2d", "name": "runTaskFromQueue task cost before running: 2 s 545 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474911723700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f414081a-e96a-4d61-8e48-4ad2711336ac", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474910924700, "endTime": 145474911787600, "totalTime": 769700}, "additional": {"logType": "info", "children": [], "durationId": "8cb17e2e-1071-491c-a69e-acfe0751742d"}}, {"head": {"id": "fbce177b-7390-4bd4-a1c0-f4d18e1cbb59", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474917033400, "endTime": 145474920532600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "cc2f2c84-506f-4505-a7dc-d9029c016763", "logId": "6ebfb1b0-03ab-4c21-a963-683fd0eb093a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc2f2c84-506f-4505-a7dc-d9029c016763", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474914298500}, "additional": {"logType": "detail", "children": [], "durationId": "fbce177b-7390-4bd4-a1c0-f4d18e1cbb59"}}, {"head": {"id": "47c0e530-0aff-4bd9-9f4c-4ec301f2d9c1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474915710200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a60bbb6-c454-4754-a1bb-418ec12892b2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474915976700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05478a73-bd56-4f1d-ba8f-b57c88b989bd", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474917051700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc27454c-631a-4347-8239-b8bacd53a13a", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474920299800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3f11e3a-012e-4088-9b4d-f748457c951f", "name": "entry : default@MergeProfile cost memory 0.3554534912109375", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474920458000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ebfb1b0-03ab-4c21-a963-683fd0eb093a", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474917033400, "endTime": 145474920532600}, "additional": {"logType": "info", "children": [], "durationId": "fbce177b-7390-4bd4-a1c0-f4d18e1cbb59"}}, {"head": {"id": "b9c4a001-d175-47d9-a5c6-1310c32ceab3", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474924645700, "endTime": 145474928404100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d9a13546-7a92-4d44-b3ee-706ddb6ffb48", "logId": "024962ba-d0d9-48cb-86f6-123439bec09b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9a13546-7a92-4d44-b3ee-706ddb6ffb48", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474922390900}, "additional": {"logType": "detail", "children": [], "durationId": "b9c4a001-d175-47d9-a5c6-1310c32ceab3"}}, {"head": {"id": "1f006e0f-1a5d-4e9d-a537-1922dfc1e18d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474923492100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f2fe5d1-3eaa-4890-8bb7-5ccf551a4160", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474923607800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deb51701-10c5-43e8-b778-1ee36106e81a", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474924659200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d13f02de-7df7-493b-992e-be9fba574a30", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474925933900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45781c26-6fd4-4313-898c-57ccffc57f79", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474928148500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "043e6f4f-8bcc-4851-a336-f99adcc69dfb", "name": "entry : default@CreateBuildProfile cost memory 0.11466217041015625", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474928317600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "024962ba-d0d9-48cb-86f6-123439bec09b", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474924645700, "endTime": 145474928404100}, "additional": {"logType": "info", "children": [], "durationId": "b9c4a001-d175-47d9-a5c6-1310c32ceab3"}}, {"head": {"id": "28033e91-8648-4399-9f6a-dcc1f57bf3cf", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474934003800, "endTime": 145474934891400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "95e9995e-26e5-4b3f-b09b-34059bad881d", "logId": "1a97c517-640e-4309-a11b-6b8faf5012d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95e9995e-26e5-4b3f-b09b-34059bad881d", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474930462200}, "additional": {"logType": "detail", "children": [], "durationId": "28033e91-8648-4399-9f6a-dcc1f57bf3cf"}}, {"head": {"id": "a2ba9914-3b1a-4ae3-a21c-6800c76fe4ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474931814400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00993dd9-01a7-4686-8c05-1ad280cd5626", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474932011300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53a321f9-678a-4282-8a57-2a3bdba89c46", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474934032200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f0d44e1-327d-4e43-996a-465b4609bc80", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474934276200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b31f5da-4acc-42e2-9eb6-12c35e12187e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474934364000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "876b87b9-0215-4292-b4bb-8fdd9a7c157d", "name": "entry : default@PreCheckSyscap cost memory 0.04160308837890625", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474934716900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd1333fe-3eef-4bc7-bdc4-2ee4d9443a14", "name": "runTaskFromQueue task cost before running: 2 s 568 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474934838700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a97c517-640e-4309-a11b-6b8faf5012d0", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474934003800, "endTime": 145474934891400, "totalTime": 811400}, "additional": {"logType": "info", "children": [], "durationId": "28033e91-8648-4399-9f6a-dcc1f57bf3cf"}}, {"head": {"id": "59e81d9a-fccb-4eed-b66d-829c4dc2b05b", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474939824200, "endTime": 145474949150000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6ebfda9d-c15f-48c3-b4d3-7eaa3ef559bd", "logId": "ccced6b7-cff4-43d0-ac8f-65b48fe6effd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ebfda9d-c15f-48c3-b4d3-7eaa3ef559bd", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474936743600}, "additional": {"logType": "detail", "children": [], "durationId": "59e81d9a-fccb-4eed-b66d-829c4dc2b05b"}}, {"head": {"id": "d85c93f2-a915-46ec-9ca2-3a5f01d5e107", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474938108600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f65b507-e3ae-4010-8412-9c8702c5989d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474938228100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f15db816-eca9-4014-958a-545ecf3e6943", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474939839900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf3ccf98-0dfe-4b95-9d28-7eed82d50ebe", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474948004600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4ca398b-877a-44cc-a07e-5474a5c5023e", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474948956700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72670541-50b0-4090-98a2-53f70d2dfaf9", "name": "entry : default@GeneratePkgContextInfo cost memory 0.249755859375", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474949083300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccced6b7-cff4-43d0-ac8f-65b48fe6effd", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474939824200, "endTime": 145474949150000}, "additional": {"logType": "info", "children": [], "durationId": "59e81d9a-fccb-4eed-b66d-829c4dc2b05b"}}, {"head": {"id": "409445ad-716b-407c-84e2-e56bc3261995", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474959211400, "endTime": 145474963092400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "4ec45942-df26-4497-a0ab-68ad7d7ce6e9", "logId": "6aa6fc25-a22e-465b-a0cd-7fdf3679b6fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ec45942-df26-4497-a0ab-68ad7d7ce6e9", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474952376600}, "additional": {"logType": "detail", "children": [], "durationId": "409445ad-716b-407c-84e2-e56bc3261995"}}, {"head": {"id": "f2150bd2-e023-427b-b99f-f748cb62b131", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474953968100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41d7023f-107f-4b0e-9f85-9dfa86905649", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474954080900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76eccc63-7c65-4db5-a9c1-da99617e65b6", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474959231200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "286f590e-d266-4895-b509-a71e1d9829a9", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474962407800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64d46982-8d16-40cc-af9e-8f0ea140ca69", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474962660200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fc470d8-968e-4ae1-9254-2246c84661b1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474962764900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f57179f-43e9-4dae-bf82-490581e04991", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474962805400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c46be5f6-de9f-43c5-a4d4-a94cd8bb9318", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12286376953125", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474962963700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "106d39b4-6306-4692-bd22-9485c5358ccb", "name": "runTaskFromQueue task cost before running: 2 s 596 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474963052000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6aa6fc25-a22e-465b-a0cd-7fdf3679b6fa", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474959211400, "endTime": 145474963092400, "totalTime": 3815700}, "additional": {"logType": "info", "children": [], "durationId": "409445ad-716b-407c-84e2-e56bc3261995"}}, {"head": {"id": "5da26739-f8f7-4ef5-a3e7-7c917536a99d", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474968768900, "endTime": 145474969623100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4cd9d579-98e5-44dd-92d1-673b42f6f70d", "logId": "46b1f4da-1dad-41d7-8d50-9e9dc14b4a82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cd9d579-98e5-44dd-92d1-673b42f6f70d", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474966433700}, "additional": {"logType": "detail", "children": [], "durationId": "5da26739-f8f7-4ef5-a3e7-7c917536a99d"}}, {"head": {"id": "b3575836-2aea-4eea-9b03-828ac430064a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474967679200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "784d81e4-3129-45a1-9a9f-6bfc5ee61b37", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474967823400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38df23d8-46f6-4577-8fd0-772ddac3e115", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474968795000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "193ec89b-8a82-465f-bf88-5f8d7176deaf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474969092000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8725845e-b0f0-49b8-9700-e05304277ea2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474969214600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "716f2c35-a20a-40b2-936c-66deeb640856", "name": "entry : default@BuildNativeWithCmake cost memory 0.03899383544921875", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474969398000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21d2ccdc-5bab-4d0e-9418-80cabdb17f36", "name": "runTaskFromQueue task cost before running: 2 s 603 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474969546600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46b1f4da-1dad-41d7-8d50-9e9dc14b4a82", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474968768900, "endTime": 145474969623100, "totalTime": 737700}, "additional": {"logType": "info", "children": [], "durationId": "5da26739-f8f7-4ef5-a3e7-7c917536a99d"}}, {"head": {"id": "9c1574f9-fae6-45e7-ad78-1e600932e938", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474974046700, "endTime": 145474981436100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f5931a10-350b-4888-91ae-e116d51413f5", "logId": "77693d12-a677-4d4f-9b97-bd48c1baed34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5931a10-350b-4888-91ae-e116d51413f5", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474971982400}, "additional": {"logType": "detail", "children": [], "durationId": "9c1574f9-fae6-45e7-ad78-1e600932e938"}}, {"head": {"id": "6f03aa6d-4c4b-48f3-a003-842c9bac4fab", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474973099800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50fe13ef-0a80-4d0f-8b4e-bca235800579", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474973225300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82103002-bb51-4ff0-b730-f5d730881930", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474974063600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "031214e6-49c0-4932-869d-cadc02fbb934", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474980792300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63b2f8c3-2204-457a-aead-1806f00bd51f", "name": "entry : default@MakePackInfo cost memory 0.170928955078125", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474981014100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77693d12-a677-4d4f-9b97-bd48c1baed34", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474974046700, "endTime": 145474981436100}, "additional": {"logType": "info", "children": [], "durationId": "9c1574f9-fae6-45e7-ad78-1e600932e938"}}, {"head": {"id": "094bc512-cf1a-47cb-b800-2f6947963231", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474989110400, "endTime": 145475013611900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c1116985-cf75-4c97-81b3-983b37fca4e7", "logId": "454c8778-e18c-431b-81ae-b01508b33f98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1116985-cf75-4c97-81b3-983b37fca4e7", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474985397700}, "additional": {"logType": "detail", "children": [], "durationId": "094bc512-cf1a-47cb-b800-2f6947963231"}}, {"head": {"id": "7e986cdb-e6cb-4bcb-9bc1-d83773ed6ce3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474987054200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46a0fa5b-d016-48fe-8ebc-a48389ce94f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474987248200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14928709-d672-4c26-9e0a-55f1e65adee8", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474989128800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce4ad36e-962e-4690-a107-e27fd165fa1b", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474989399700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbe441a8-dab5-4ac3-81f0-319cee072a0a", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474990947700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7f2a805-c3d6-46d1-a06e-4680e5f223e0", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475013181000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3de79e0d-2638-4096-bfd3-bbf6267cdc91", "name": "entry : default@SyscapTransform cost memory 0.16070556640625", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475013436200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "454c8778-e18c-431b-81ae-b01508b33f98", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145474989110400, "endTime": 145475013611900}, "additional": {"logType": "info", "children": [], "durationId": "094bc512-cf1a-47cb-b800-2f6947963231"}}, {"head": {"id": "8ba95125-3482-4d25-87d7-991b14fea4ea", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475020404900, "endTime": 145475024428000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "7e6293da-77a4-4979-b99a-29fa2abaffd1", "logId": "426b1abc-00c2-43fd-b793-2731eee56f0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e6293da-77a4-4979-b99a-29fa2abaffd1", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475016708100}, "additional": {"logType": "detail", "children": [], "durationId": "8ba95125-3482-4d25-87d7-991b14fea4ea"}}, {"head": {"id": "a0a8393e-1e75-4a54-a0dd-b7cc9c60c1d4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475018170800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96205f2d-e234-41ac-9591-00e7bf5a55b3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475018285800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d502e2b-6cbd-4127-95e7-bf41bd3cd1ab", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475020423500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a1c81de-c318-4a8f-b287-843a5c223ea1", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475024088200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81952128-e23b-45a3-962f-42f6580a10ae", "name": "entry : default@ProcessProfile cost memory 0.20377349853515625", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475024314600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "426b1abc-00c2-43fd-b793-2731eee56f0f", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475020404900, "endTime": 145475024428000}, "additional": {"logType": "info", "children": [], "durationId": "8ba95125-3482-4d25-87d7-991b14fea4ea"}}, {"head": {"id": "cdaa1e98-2580-47cd-8bc1-66ddfcf77019", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475035138300, "endTime": 145475045024400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "776a0fd2-0fe1-4d60-b1e0-589b6be39fb8", "logId": "2db556d9-7275-4413-96e4-c66b6724b7f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "776a0fd2-0fe1-4d60-b1e0-589b6be39fb8", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475027465500}, "additional": {"logType": "detail", "children": [], "durationId": "cdaa1e98-2580-47cd-8bc1-66ddfcf77019"}}, {"head": {"id": "83528da9-8749-4c58-b483-05c26b347c91", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475031251000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6479a14-ffb2-493a-9ca5-5898c22ab529", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475031455400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9124ea7-20aa-4d2a-b7e7-da92bad111fb", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475035160400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8149a7e-dde5-41c1-93ff-4bc64a61c3b0", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475044702400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "001f8e62-e5b8-4804-bf67-c94f712602a4", "name": "entry : default@ProcessRouterMap cost memory 0.24399566650390625", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475044918800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2db556d9-7275-4413-96e4-c66b6724b7f6", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475035138300, "endTime": 145475045024400}, "additional": {"logType": "info", "children": [], "durationId": "cdaa1e98-2580-47cd-8bc1-66ddfcf77019"}}, {"head": {"id": "6ca116b4-181c-4381-bd44-a954bf01c745", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475059517100, "endTime": 145475069907900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "b497d542-9606-43b7-a063-2f8a23cf95d6", "logId": "b829055f-75fb-41e3-ac3c-8d5864cf5674"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b497d542-9606-43b7-a063-2f8a23cf95d6", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475056637200}, "additional": {"logType": "detail", "children": [], "durationId": "6ca116b4-181c-4381-bd44-a954bf01c745"}}, {"head": {"id": "4473fde6-2dec-4877-9efc-ce62f138bbc3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475059053700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56817878-fd8b-4869-b672-a250ec65de87", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475059233700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b90de60-6a90-43d6-a8d0-206eb5a56bb4", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475059535900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b88f5871-2cd9-4195-b1e4-bf5660a6bdfd", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475059848700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0de3224-d6db-47e2-9158-0a9d0c34ac31", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475067209700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ff91e71-a5ca-4a36-a748-aa38423533ef", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475067399300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd26747b-963b-49ef-bd2b-23df0dca81c5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475067497500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88c92c52-6c05-4c44-b0d2-4c7677fd15de", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475067565300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d25c1bcf-c8d3-4fbc-a7ba-03ed57a557ba", "name": "entry : default@ProcessStartupConfig cost memory 0.26625823974609375", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475069666200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65748fb7-2ade-48ae-9363-d2febc1978da", "name": "runTaskFromQueue task cost before running: 2 s 703 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475069844900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b829055f-75fb-41e3-ac3c-8d5864cf5674", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475059517100, "endTime": 145475069907900, "totalTime": 10296000}, "additional": {"logType": "info", "children": [], "durationId": "6ca116b4-181c-4381-bd44-a954bf01c745"}}, {"head": {"id": "5aa7a376-a8a4-4aeb-b8a6-d2ee8e2730cb", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475075430400, "endTime": 145475076985100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c86b370f-7f54-4a74-bca8-73660028614f", "logId": "32ca2e06-19a8-4c18-935e-b7e1df35e342"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c86b370f-7f54-4a74-bca8-73660028614f", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475073269700}, "additional": {"logType": "detail", "children": [], "durationId": "5aa7a376-a8a4-4aeb-b8a6-d2ee8e2730cb"}}, {"head": {"id": "5ff53872-1cff-4644-8c17-1e9945c3deae", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475074477900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04c46779-f62b-4c88-b2c7-c529317cc255", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475074593900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1197fe4e-b70e-4354-a9b2-780061f2b28e", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475075446500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e0a6bbb-0ea6-440a-9403-ee0271e369ce", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475075598300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42e8674f-a174-4948-8d8a-5cc55e7729a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475075649500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ffa4f56-830f-4ea3-8bd4-8ca25fd82324", "name": "entry : default@BuildNativeWithNinja cost memory 0.0588836669921875", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475076778700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a73f0a5f-6e7a-4570-bbb5-156b0294dc1e", "name": "runTaskFromQueue task cost before running: 2 s 710 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475076928500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32ca2e06-19a8-4c18-935e-b7e1df35e342", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475075430400, "endTime": 145475076985100, "totalTime": 1463100}, "additional": {"logType": "info", "children": [], "durationId": "5aa7a376-a8a4-4aeb-b8a6-d2ee8e2730cb"}}, {"head": {"id": "ffc519ef-163c-4560-8512-27f07f99f6c4", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475084157600, "endTime": 145475091082700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "25a779f9-581e-4bc1-8d14-87f264943976", "logId": "af4df412-1c85-4a59-b4b2-a2648c64e20e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25a779f9-581e-4bc1-8d14-87f264943976", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475079669300}, "additional": {"logType": "detail", "children": [], "durationId": "ffc519ef-163c-4560-8512-27f07f99f6c4"}}, {"head": {"id": "dc511f08-bf45-4480-9ca6-6c61348bc703", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475080761300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1cd63fa-2a65-4f31-b3b9-baa4a4c1ee24", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475080866900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8123a745-535b-4b47-8bca-7ddb6edad443", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475082558400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6511bd0-51b3-438f-a360-73e4357c7cf1", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475086452300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc8834aa-e927-434e-85d0-378632c0d086", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475089157200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8716529e-84bd-46c1-8dc9-3438ad6e024d", "name": "entry : default@ProcessResource cost memory 0.1687164306640625", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475089292200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af4df412-1c85-4a59-b4b2-a2648c64e20e", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475084157600, "endTime": 145475091082700}, "additional": {"logType": "info", "children": [], "durationId": "ffc519ef-163c-4560-8512-27f07f99f6c4"}}, {"head": {"id": "9f1cf3aa-358e-4481-8f6a-520724340d6c", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475100929800, "endTime": 145475122381800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d618bf9e-391b-4eaf-9685-cb0096b0a8b0", "logId": "65e51c28-aacc-4795-b14c-6fefa3219288"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d618bf9e-391b-4eaf-9685-cb0096b0a8b0", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475095534000}, "additional": {"logType": "detail", "children": [], "durationId": "9f1cf3aa-358e-4481-8f6a-520724340d6c"}}, {"head": {"id": "e5b82496-d4c8-4ac1-8193-36b6b1d9bccf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475096771300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87fa5e1f-b8f4-42ad-b732-1c8b03add9d8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475096908300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b99e74c0-7c88-4c8d-b03c-a850f4fd5ba5", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475100944200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a554b4f4-b7bb-4458-a1bd-89ec37868698", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475122045000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9644110-6c69-4061-86f3-3583de669adb", "name": "entry : default@GenerateLoaderJson cost memory 1.0041580200195312", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475122249200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65e51c28-aacc-4795-b14c-6fefa3219288", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475100929800, "endTime": 145475122381800}, "additional": {"logType": "info", "children": [], "durationId": "9f1cf3aa-358e-4481-8f6a-520724340d6c"}}, {"head": {"id": "ad9c87fb-4525-40fe-8e53-fa458027522c", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475135653300, "endTime": 145475141846800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "295b9bdc-b87b-4809-98e8-fa7f342b45a1", "logId": "e8c8413e-fd14-456b-b80f-a0f523e2bbe0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "295b9bdc-b87b-4809-98e8-fa7f342b45a1", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475133877600}, "additional": {"logType": "detail", "children": [], "durationId": "ad9c87fb-4525-40fe-8e53-fa458027522c"}}, {"head": {"id": "c5660f9c-33ba-469a-8245-c4e8131a7964", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475134822200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "990e21a0-960c-4d24-8101-c5b5fd44c1f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475134934200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b7aee65-f1c6-40ab-8b1e-0121ae6b9dd7", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475135664900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24bf14bf-ec7d-44e1-af03-f6c915ca19d7", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475141588500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b87a754c-9894-43c4-9d6b-c89a00f3f37b", "name": "entry : default@ProcessLibs cost memory 0.14824676513671875", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475141750400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8c8413e-fd14-456b-b80f-a0f523e2bbe0", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475135653300, "endTime": 145475141846800}, "additional": {"logType": "info", "children": [], "durationId": "ad9c87fb-4525-40fe-8e53-fa458027522c"}}, {"head": {"id": "1ed6b4d9-c222-4ba9-8fbf-612a1d7bb72b", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475149015500, "endTime": 145475181641100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "1dbe4b2c-69d6-4c27-a961-257c427c90ff", "logId": "7f937427-ac4d-41ca-a40a-f327898c6e61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dbe4b2c-69d6-4c27-a961-257c427c90ff", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475144101700}, "additional": {"logType": "detail", "children": [], "durationId": "1ed6b4d9-c222-4ba9-8fbf-612a1d7bb72b"}}, {"head": {"id": "a0418f7d-ea73-4d22-aa07-513436cc8706", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475145136200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28b233eb-7344-4705-80a2-69fdfc1a2205", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475145249300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a2f4535-1df1-4058-a447-f12b74c29566", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475146354800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5c537b6-f03d-4fa0-bbbd-2a09fff65fcf", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475149102200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96483e27-2eea-454f-bf51-8e63a6dda9d2", "name": "Incremental task entry:default@CompileResource pre-execution cost: 31 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475181153400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8462a837-4de1-4959-97f1-7776311a18ac", "name": "entry : default@CompileResource cost memory 1.6812591552734375", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475181514400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f937427-ac4d-41ca-a40a-f327898c6e61", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475149015500, "endTime": 145475181641100}, "additional": {"logType": "info", "children": [], "durationId": "1ed6b4d9-c222-4ba9-8fbf-612a1d7bb72b"}}, {"head": {"id": "4965ea15-921a-4d03-92f6-9265738f4745", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475188651700, "endTime": 145475190982100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "b03d955c-798d-45b8-847b-d932a3a97223", "logId": "5c3fc1ba-a7ed-4e44-9ad3-5a0d8093e5b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b03d955c-798d-45b8-847b-d932a3a97223", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475185001100}, "additional": {"logType": "detail", "children": [], "durationId": "4965ea15-921a-4d03-92f6-9265738f4745"}}, {"head": {"id": "15d663a0-ae47-4a4f-a150-0e873e30b806", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475186091900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a959c076-6546-4848-ab60-a84feb8c9d5d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475186197500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cac827d4-da14-4aa8-81ca-548b389a6ec4", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475188665000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac72a0fd-3931-4476-afe1-b8bd0383cf12", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475189187300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b5c8a90-ca25-4ad6-a5d0-6f54f7fc1757", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475190803500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b772b36d-efff-4f36-a6ee-d76a5eff53d4", "name": "entry : default@DoNativeStrip cost memory 0.08390045166015625", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475190914200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c3fc1ba-a7ed-4e44-9ad3-5a0d8093e5b0", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475188651700, "endTime": 145475190982100}, "additional": {"logType": "info", "children": [], "durationId": "4965ea15-921a-4d03-92f6-9265738f4745"}}, {"head": {"id": "ee6f3cf6-8b88-4542-bf29-8827f1830d05", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475200337100, "endTime": 145486973801200}, "additional": {"children": ["8bc16a97-e0ff-4f6a-baf1-870f71a498e7", "ea1c8f8f-8949-456b-b298-ae5c187c184f"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "beb9d79f-8bf3-499d-8055-331ab0ebda67", "logId": "56053b12-6f8a-4eff-a1f5-72782ce6049e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "beb9d79f-8bf3-499d-8055-331ab0ebda67", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475193087500}, "additional": {"logType": "detail", "children": [], "durationId": "ee6f3cf6-8b88-4542-bf29-8827f1830d05"}}, {"head": {"id": "ef71bfff-fa7b-4849-bda3-e837c8ce3cce", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475194499800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "135d9c92-a9a7-4d5a-91a8-af5b4b320e30", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475194652600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f6eb8e5-c89c-4f45-8c91-dee43f962791", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475200352000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b78812f-0dd0-4a1d-8823-17014c0cbcb3", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475200782700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e9a474d-9f69-49de-8d70-742782d03788", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475236476000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d99b0ab4-6a09-48b2-a25a-863e1730163f", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 28 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475236649200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f55d5e5-a72a-4f94-90a3-b958e4907dbb", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475260114700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f09bc9b9-7d78-46e4-a3c1-99555878ccb6", "name": "default@CompileArkTS work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475264048400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bc16a97-e0ff-4f6a-baf1-870f71a498e7", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Worker4", "startTime": 145475267679700, "endTime": 145486973577600}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "ee6f3cf6-8b88-4542-bf29-8827f1830d05", "logId": "5e70886d-4197-4929-9ca5-3b95f9263ca6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b859213-7fbb-4d0d-baea-f9bd563ec1b2", "name": "default@CompileArkTS work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475265580500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "472b03ec-ae9b-4fd4-886c-e47f35b7dd69", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475265891100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1fdb843-f73d-47c2-9728-ac393d5d4402", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475265967000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0937a000-7506-4c41-aea2-8457673aec5f", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475266003200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1454debc-bdd1-4bd2-b34d-d1144eed456f", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475266035200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a559ad14-7cb5-422a-abad-41536d1cb7ca", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475266066400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f4e9916-9b80-465b-afa4-e2d9a6e40e5e", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475266097100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9763a24-c40b-4744-b7f5-077d8694a044", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475266128500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc1a91d7-c6cb-4fea-b446-17ff6ba5fc0d", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475266160900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7de4f19-0012-4c86-84d0-15c3ddae2b1f", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475266190500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bda565f4-21fd-44d2-880d-fd31c6cb8c11", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475266222100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46834aa2-c9f4-46d4-af4e-1ab8515f8dd9", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475266250800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edfb99e3-8c9c-4f2f-9046-8529ec4911fd", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475266279400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edb5e6af-911e-4c71-b293-943e0bd6386f", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475266309800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fdc1e6b-9bc3-4ea3-848b-406ec0c2ed3d", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475266342900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "342163ae-1049-4e46-93e8-0865428efa64", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475266396300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff371fef-6542-4a9b-956b-19a3f7267dd2", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475266554900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f8f6a27-8da3-4e65-b1af-87f1f2150283", "name": "default@CompileArkTS work[0] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475267712800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b07c0f20-1377-4344-9ee7-320ba45dcff2", "name": "default@CompileArkTS work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475267896300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95bd6582-aaaa-46a9-8d9b-7234ce9c93b9", "name": "CopyResources startTime: 145475268042600", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475268047400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0b67855-e1fd-4741-beec-a3c590772121", "name": "default@CompileArkTS work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475268134100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea1c8f8f-8949-456b-b298-ae5c187c184f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Worker18", "startTime": 145476440877700, "endTime": 145476453807100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "ee6f3cf6-8b88-4542-bf29-8827f1830d05", "logId": "65a0e4f7-b6f9-40f9-b57d-55e35067a37e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d23ad93-ffc3-4080-9262-31d4b149d6e8", "name": "default@CompileArkTS work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475269178600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e47512a-3bf3-4823-b1ad-05560dd0c87f", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475269274700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f26c5977-11d2-4b8a-9889-1a6ec88120f6", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475269339700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bd56532-122f-4dfb-8ea2-4cd67d938cd2", "name": "default@CompileArkTS work[1] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475270348700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccd07353-53dd-4892-9ee4-2d4ccaaca42d", "name": "default@CompileArkTS work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475270455200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "648e80d8-cef6-44ee-9c03-9f5efd80ab14", "name": "entry : default@CompileArkTS cost memory 2.6971359252929688", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475270679600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc8f8f4e-87e2-4bda-967e-49f21996ed74", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475278648700, "endTime": 145475287384100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "a994738d-08b4-452b-a7df-7eb2d474cf35", "logId": "36bec524-fde8-46b5-8a66-1aca7bffb8c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a994738d-08b4-452b-a7df-7eb2d474cf35", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475272735500}, "additional": {"logType": "detail", "children": [], "durationId": "dc8f8f4e-87e2-4bda-967e-49f21996ed74"}}, {"head": {"id": "6496635d-e36d-4536-b046-17664c94d34b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475273845200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0905bea1-2458-49fa-b92f-c6de2afaaf16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475273966800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f5020f8-23d3-4493-a28a-4b6bd2867b74", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475278665700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c5883d0-74ab-4b75-be74-3fcddbef5c34", "name": "entry : default@BuildJS cost memory 0.3550872802734375", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475287101000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "031e4790-9ada-4f80-b1f2-997d11e4fad6", "name": "runTaskFromQueue task cost before running: 2 s 921 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475287304100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36bec524-fde8-46b5-8a66-1aca7bffb8c6", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475278648700, "endTime": 145475287384100, "totalTime": 8596500}, "additional": {"logType": "info", "children": [], "durationId": "dc8f8f4e-87e2-4bda-967e-49f21996ed74"}}, {"head": {"id": "d71f9559-d786-4e9f-9a75-39c740d5e5e9", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475293421900, "endTime": 145475299362900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "86caae50-65d7-485f-928f-b098e53c2a73", "logId": "8c938129-b2a1-4867-80b0-4a845af7086e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86caae50-65d7-485f-928f-b098e53c2a73", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475289348500}, "additional": {"logType": "detail", "children": [], "durationId": "d71f9559-d786-4e9f-9a75-39c740d5e5e9"}}, {"head": {"id": "8f119ca7-8841-46b5-9274-6298fa10a334", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475290433300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c60c633-3476-44a5-8287-fa8b26e447df", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475290558500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50bf5149-82c5-4ab1-a364-8635cdc04019", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475293463400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb85101f-aed7-4ee0-a879-ab2594ae7c5d", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475294923300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cfa76d7-d103-4a2f-8121-2b472aebe02a", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475299083200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d77d7653-6073-4a41-8e1b-9eabf8007cec", "name": "entry : default@CacheNativeLibs cost memory 0.10100555419921875", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475299285100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c938129-b2a1-4867-80b0-4a845af7086e", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475293421900, "endTime": 145475299362900}, "additional": {"logType": "info", "children": [], "durationId": "d71f9559-d786-4e9f-9a75-39c740d5e5e9"}}, {"head": {"id": "86abf1d9-7c63-49e4-959b-4dd031336d3c", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145476454285000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fff017b2-d38a-473e-8cca-6e0d579b29bd", "name": "CopyResources is end, endTime: 145476454484500", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145476454490700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f971a6c1-ad89-4b74-8d4c-92d322c7b7ed", "name": "default@CompileArkTS work[1] done.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145476454636700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65a0e4f7-b6f9-40f9-b57d-55e35067a37e", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Worker18", "startTime": 145476440877700, "endTime": 145476453807100}, "additional": {"logType": "info", "children": [], "durationId": "ea1c8f8f-8949-456b-b298-ae5c187c184f", "parent": "56053b12-6f8a-4eff-a1f5-72782ce6049e"}}, {"head": {"id": "742cca82-7ebf-43b2-9806-37e816f7f6b9", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145476454798300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8c522df-ca05-4a59-bf78-d21426403dbd", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486973362100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a05b1861-dc91-4eec-8ea9-206a359a1bb4", "name": "default@CompileArkTS work[0] failed.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486973666500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e70886d-4197-4929-9ca5-3b95f9263ca6", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Worker4", "startTime": 145475267679700, "endTime": 145486973577600}, "additional": {"logType": "error", "children": [], "durationId": "8bc16a97-e0ff-4f6a-baf1-870f71a498e7", "parent": "56053b12-6f8a-4eff-a1f5-72782ce6049e"}}, {"head": {"id": "56053b12-6f8a-4eff-a1f5-72782ce6049e", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145475200337100, "endTime": 145486973801200}, "additional": {"logType": "error", "children": ["5e70886d-4197-4929-9ca5-3b95f9263ca6", "65a0e4f7-b6f9-40f9-b57d-55e35067a37e"], "durationId": "ee6f3cf6-8b88-4542-bf29-8827f1830d05"}}, {"head": {"id": "d5ceaf7e-fd39-40f7-8c0a-2c563909f85c", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486973939000}, "additional": {"logType": "debug", "children": [], "durationId": "ee6f3cf6-8b88-4542-bf29-8827f1830d05"}}, {"head": {"id": "9b31a731-91ee-48f6-8015-42f6983e0b4e", "name": "ERROR: stacktrace = Error: ArkTS Compiler Error\r\n\u001b[33m1 WARN: \u001b[33m\u001b[33mArkTS:WARN: For details about ArkTS syntax errors, see FAQs\u001b[39m\u001b[39m\r\n\u001b[31m1 ERROR: \u001b[31m10605040 ArkTS Compiler Error\r\nError Message: Object literals cannot be used as type declarations (arkts-no-obj-literals-as-types) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/models/FeaturedCollection.ets:36:13\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:2 WARN:1}\u001b[39m\n    at runArkPack (C:\\Program Files\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5479)", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486974919400}, "additional": {"logType": "debug", "children": [], "durationId": "ee6f3cf6-8b88-4542-bf29-8827f1830d05"}}, {"head": {"id": "ce8ed478-c8d1-4f4a-b983-6aa42c858637", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486986372000, "endTime": 145486986624200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa69274a-2fdc-495c-aff8-928c9e872747", "logId": "8b0729f4-d048-4262-afd8-b48c06adfb05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b0729f4-d048-4262-afd8-b48c06adfb05", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486986372000, "endTime": 145486986624200}, "additional": {"logType": "info", "children": [], "durationId": "ce8ed478-c8d1-4f4a-b983-6aa42c858637"}}, {"head": {"id": "1ac8a56f-48c1-409b-9610-6796c3909989", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145472367185800, "endTime": 145486986895200}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 21, "second": 56}, "completeCommand": "{\"prop\":[\"product=default\"],\"mode\":\"module\",\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.8", "category": "build", "state": "failed"}}, {"head": {"id": "ffe68d67-bec2-4eb9-8291-118a3d78fc5b", "name": "BUILD FAILED in 14 s 620 ms ", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486986931100}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "0e23f2f1-4c46-46ef-8140-d831a955623c", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486987152500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a507d44c-27e8-4923-a71c-94d737f772fa", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486987243800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c96e4224-fc32-4bdc-8a5b-ab18e2fb3bd0", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486987643900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0e41ef9-0fef-4711-8a40-1fc3be2da7e7", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486987707000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0d3b65a-d411-4372-b889-8ae2f30873e2", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486987741000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31c859a6-3167-43af-9d6b-ee43836409a6", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486987765600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b281f32-548c-43be-9516-ec51e93f9a7e", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486987788500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "424f2fe2-32b0-42f6-b2a3-43fc82e23108", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486988257900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6725aafa-f5d8-4797-9777-d1b2cf948f90", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486988440100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07a37f47-c700-42f2-986a-9db737a343d4", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486988496200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d207f815-c349-44b2-bf08-8b1b1b2d7f36", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486988527500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c58e35f-8687-426c-a7b3-9700e74a77d8", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486988560600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef6f7331-f7b3-473b-a98a-01b9b477289c", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486988584200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77986c30-287d-4c28-8a0e-a343b3d4701a", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486989442600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0773ec3f-8155-4e75-bb1c-67a8f13e0b98", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486989672700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13e46b89-a3d1-487e-bf08-d52b4ddfa9be", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486989880500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62abb66a-9613-44e1-961d-7f89b933fb86", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486989937700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76ff4520-2038-45e9-b084-85eb49772e61", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486989970600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "285b5988-23c1-420f-b6c3-562ef8da<PERSON>f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486989994700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e43bb58-40a4-4586-9bb2-e2e41b2e15e7", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486990017100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d8ffc83-e4e9-47f4-85bc-8972246ae450", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486990041700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7e1b531-d22e-4866-8116-06b4a2551b1d", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486992805800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97bf41d2-999e-4dc9-83fd-81192fd6bbe0", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486993424300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "264e7d3a-8f2f-4942-98ca-79de82f87d54", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486993766600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f441bc5e-c0c6-433f-a208-4fdb1babad52", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486993969900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "137ca439-d750-4ef8-b3f1-6db4aa84da04", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486994218300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca14f039-9453-4ee4-89e4-63ee6abb661c", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486994979000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39ed5396-a66d-4e52-92b6-b4177784a95e", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486995069900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e0caa49-5388-45ba-a676-3da65323ea40", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486995306600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d61ba8a-07d9-44c8-bc36-8991b1fc2003", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486995676100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ca62d53-dea6-4ba7-abee-e94a924453ea", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486996960800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ce701c7-da77-45a6-98fb-6cf8cec31f2c", "name": "Incremental task entry:default@CompileArkTS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145486997996700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1685ca8-5c4f-4854-9c7a-9483b5496ade", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145487001166200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be7e2268-2711-43b7-bfd0-63b863dba519", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145487002180100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38781c75-17d3-4f98-b518-ea415496f6d5", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145487003085600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ee15d7f-5854-4f12-bd5c-e4962ff1bb19", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145487003555000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff7e8436-03c5-4e48-806c-b5fd73caded9", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145487003876800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64033b51-8ae7-4ad2-ae9c-32c619e2f820", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145487004884200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75119b51-283f-4563-830a-cb32c3c63e64", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145487005868900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3a25fcf-d8ad-40ae-b912-782a71b2aa74", "name": "Incremental task entry:default@BuildJS post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145487006255400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ff74eb1-cdda-47be-a272-0e4227c481e5", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 20024, "tid": "Main Thread", "startTime": 145487006340400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}