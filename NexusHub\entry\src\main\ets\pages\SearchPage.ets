import { AppModel, AppListResponse, AppSearchParams, AppListData } from '../models/App';
import { CategoryModel, CategoryListResponse } from '../models/Category';
import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { AppCard } from '../components/AppCard';
import { SearchBar, SearchSuggestions } from '../components/SearchBar';
import { LoadingView, LoadingState, LoadMoreView } from '../components/LoadingView';
import { router, LengthMetrics } from '@kit.ArkUI';
import { preferences } from '@kit.ArkData';
// getContext is deprecated, use this.getUIContext().getHostContext() instead
import { hilog } from '@kit.PerformanceAnalysisKit';

interface SearchPageParams {
  keyword?: string;
}

interface FilterOptions {
  category?: string;
  minRating?: number;
  isFree?: boolean;
}

interface SortOption {
  key: string;
  label: string;
}

/**
 * 搜索页面
 */
@Entry
@Component
struct SearchPage {
  @State searchKeyword: string = '';
  @State searchResults: AppModel[] = [];
  @State categories: CategoryModel[] = [];
  @State hotKeywords: string[] = [];
  @State searchHistory: string[] = [];
  @State loadingState: LoadingState = LoadingState.SUCCESS;
  @State isSearching: boolean = false;
  @State showSuggestions: boolean = true;
  @State currentPage: number = 1;
  @State hasMore: boolean = true;
  @State isLoadingMore: boolean = false;
  @State selectedCategory: string = '';
  @State sortBy: string = 'relevance';
  @State showFilter: boolean = false;

  private deviceUtils = DeviceUtils.getInstance();
  private apiService = ApiService.getInstance();
  private searchParams: AppSearchParams = {
    page: 1,
    page_size: 20
  };

  aboutToAppear(): void {
    const params = this.getUIContext().getRouter().getParams() as SearchPageParams;
    if (params?.keyword) {
      this.searchKeyword = params.keyword;
      this.performSearch(params.keyword);
    } else {
      this.loadInitialData();
    }
    this.loadSearchHistory();
  }

  /**
/**
   * 检查并设置认证token
   */
  private async checkAndSetAuthToken(): Promise<void> {
    try {
      const context = this.getUIContext().getHostContext();
      const options: preferences.Options = { name: 'user_data' };
      const dataPreferences = preferences.getPreferencesSync(context, options);
      const token = dataPreferences.getSync('token', '') as string;
      
      if (token) {
        this.apiService.setAuthToken(token);
      }
    } catch (error) {
      hilog.error(0x0000, 'SearchPage', '检查登录状态失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 加载初始数据
   */
  private async loadInitialData() {
    try {
      // 检查登录状态并设置token
      await this.checkAndSetAuthToken();
      
      const responses: [CategoryListResponse, Record<string, Object>] = await Promise.all([
        this.apiService.getAppCategories(),
        this.apiService.getHotKeywords()
      ]);
      const categoriesResponse: CategoryListResponse = responses[0];
      const hotKeywordsResponse: Record<string, Object> = responses[1];

      if (categoriesResponse.code === 200 && categoriesResponse.data) {
        this.categories = categoriesResponse.data;
      }

      if (hotKeywordsResponse.code === 200 && hotKeywordsResponse.data) {
        this.hotKeywords = hotKeywordsResponse.data as string[];
      }
    } catch (error) {
      hilog.error(0x0000, 'SearchPage', '加载初始数据失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 执行搜索
   */
  private async performSearch(keyword: string, loadMore: boolean = false) {
    if (!keyword.trim()) {
      this.showSuggestions = true;
      return;
    }

    try {
      this.showSuggestions = false;
      
      if (!loadMore) {
        this.isSearching = true;
        this.currentPage = 1;
        this.searchResults = [];
      } else {
        this.isLoadingMore = true;
      }

      const page = loadMore ? this.currentPage + 1 : 1;

      // 优先使用公开搜索接口，无需登录
      let response: AppListResponse;
      try {
        response = await this.apiService.searchApps(keyword.trim(), page, 20);
      } catch (publicError) {
        // 如果公开接口失败，尝试使用需要认证的接口
        hilog.warn(0x0000, 'SearchPage', '公开搜索接口失败，尝试认证接口: %{public}s', JSON.stringify(publicError));
        await this.checkAndSetAuthToken();
        
        this.searchParams = {
          keyword: keyword.trim(),
          category: this.selectedCategory || undefined,
          sort: this.sortBy,
          page: page,
          page_size: 20
        };
        
        response = await this.apiService.searchApps(keyword.trim(), page, 20);
      }
      
      if (response.code === 200 && response.data) {
        const responseData = response.data as AppListData;
        if (loadMore) {
          this.searchResults = this.searchResults.concat(responseData.list);
          this.currentPage++;
        } else {
          this.searchResults = responseData.list;
          this.currentPage = 1;
        }
        
        this.hasMore = responseData.pagination?.hasNext ?? false;
        
        if (!this.searchResults || this.searchResults.length === 0) {
          this.loadingState = LoadingState.EMPTY;
        } else {
          this.loadingState = LoadingState.SUCCESS;
        }

        // 保存搜索历史（仅在用户已登录时）
        if (!loadMore) {
          this.saveSearchHistory(keyword.trim());
        }
      } else {
        this.loadingState = LoadingState.ERROR;
      }
    } catch (error) {
      hilog.error(0x0000, 'SearchPage', '搜索失败: %{public}s', JSON.stringify(error));
      this.loadingState = LoadingState.ERROR;
    } finally {
      this.isSearching = false;
      this.isLoadingMore = false;
    }
  }

  /**
   * 加载搜索历史
   */
  private async loadSearchHistory() {
    try {
      const context = this.getUIContext().getHostContext();
      const options: preferences.Options = { name: 'search_history' };
      const dataPreferences = preferences.getPreferencesSync(context, options);
      const history = dataPreferences.getSync('keywords', '[]') as string;
      this.searchHistory = JSON.parse(history);
    } catch (error) {
      hilog.error(0x0000, 'SearchPage', '加载搜索历史失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 保存搜索历史
   */
  private async saveSearchHistory(keyword: string) {
    try {
      // 移除重复项并添加到开头
      const newHistory = [keyword, ...this.searchHistory.filter(item => item !== keyword)];
      // 限制历史记录数量
      this.searchHistory = newHistory.slice(0, 10);
      
      const context = this.getUIContext().getHostContext();
      const options: preferences.Options = { name: 'search_history' };
      const dataPreferences = preferences.getPreferencesSync(context, options);
      dataPreferences.putSync('keywords', JSON.stringify(this.searchHistory));
      dataPreferences.flush();
    } catch (error) {
      hilog.error(0x0000, 'SearchPage', '保存搜索历史失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 清除搜索历史
   */
  private async clearSearchHistory() {
    try {
      this.searchHistory = [];
      const context = this.getUIContext().getHostContext();
      const options: preferences.Options = { name: 'search_history' };
      const dataPreferences = preferences.getPreferencesSync(context, options);
      dataPreferences.deleteSync('keywords');
      dataPreferences.flush();
    } catch (error) {
      hilog.error(0x0000, 'SearchPage', '清除搜索历史失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 跳转到应用详情
   */
  private navigateToAppDetail(appId: string) {
    this.getUIContext().getRouter().pushUrl({
      url: 'pages/AppDetailPage',
      params: { appId }
    });
  }

  /**
   * 搜索建议
   */
  @Builder
  private SearchSuggestionsView() {
    if (this.showSuggestions) {
      SearchSuggestions({
        hotKeywords: this.hotKeywords,
        searchHistory: this.searchHistory,
        onSuggestionClick: (keyword: string) => {
          this.searchKeyword = keyword;
          this.performSearch(keyword);
        },
        onClearHistory: () => {
          this.clearSearchHistory();
        }
      })
    }
  }

  /**
   * 筛选器
   */
  @Builder
  private FilterView() {
    if (this.showFilter) {
      Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
        // 分类筛选
        Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) }) {
          Text('分类')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontWeight(FontWeight.Medium)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .alignSelf(ItemAlign.Start)

          Flex({ wrap: FlexWrap.Wrap, space: { main: LengthMetrics.vp(8), cross: LengthMetrics.vp(8) } }) {
            // 全部分类
            Text('全部')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(this.selectedCategory === '' ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_PRIMARY)
              .backgroundColor(this.selectedCategory === '' ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT)
              .padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' })
              .borderRadius(Constants.BORDER_RADIUS.SMALL)
              .onClick(() => {
                this.selectedCategory = '';
                if (this.searchKeyword.trim()) {
                  this.performSearch(this.searchKeyword);
                }
              })

            ForEach(this.categories, (category: CategoryModel) => {
              Text(category.name)
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
                .fontColor(this.selectedCategory === category.id.toString() ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_PRIMARY)
                .backgroundColor(this.selectedCategory === category.id.toString() ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT)
                .padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' })
                .borderRadius(Constants.BORDER_RADIUS.SMALL)
                .onClick(() => {
                  this.selectedCategory = category.id.toString();
                  if (this.searchKeyword.trim()) {
                    this.performSearch(this.searchKeyword);
                  }
                })
            })
          }
        }

        Divider().color(Constants.COLORS.BORDER)

        // 排序方式
        Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) }) {
          Text('排序')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontWeight(FontWeight.Medium)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .alignSelf(ItemAlign.Start)

          Row({ space: 12 }) {
            ForEach([
              { key: 'relevance', label: '相关度' },
              { key: 'downloadCount', label: '下载量' },
              { key: 'rating', label: '评分' },
              { key: 'updatedAt', label: '更新时间' }
            ], (sort: SortOption) => {
              Text(sort.label)
                .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
                .fontColor(this.sortBy === sort.key ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_PRIMARY)
                .backgroundColor(this.sortBy === sort.key ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT)
                .padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' })
                .borderRadius(Constants.BORDER_RADIUS.SMALL)
                .onClick(() => {
                  this.sortBy = sort.key;
                  if (this.searchKeyword.trim()) {
                    this.performSearch(this.searchKeyword);
                  }
                })
            })
          }
        }
      }
      .width('100%')
      .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
      .backgroundColor($r('sys.color.ohos_id_color_background'))
      .borderRadius({ topLeft: Constants.BORDER_RADIUS.LARGE, topRight: Constants.BORDER_RADIUS.LARGE })
      .animation({ duration: 300, curve: Curve.EaseInOut })
    }
  }

  /**
   * 搜索结果
   */
  @Builder
  private SearchResults() {
    if (this.loadingState === LoadingState.LOADING || this.isSearching) {
      LoadingView({ state: LoadingState.LOADING, message: '搜索中...' })
        .layoutWeight(1)
    } else if (this.loadingState === LoadingState.ERROR) {
      LoadingView({ 
        state: LoadingState.ERROR,
        message: '搜索失败，请重试',
        onRetry: (): Promise<void> => this.performSearch(this.searchKeyword)
      })
        .layoutWeight(1)
    } else if (this.loadingState === LoadingState.EMPTY) {
      LoadingView({ 
        state: LoadingState.EMPTY,
        message: '未找到相关应用'
      })
        .layoutWeight(1)
    } else {
      Column() {
        // 搜索结果统计
        if (this.searchResults && this.searchResults.length > 0) {
          Text(`找到 ${this.searchResults.length} 个相关应用`)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor($r('sys.color.ohos_id_color_text_secondary'))
            .alignSelf(ItemAlign.Start)
            .margin({ left: 16, right: 16, top: 8, bottom: 8 })
        }

        // 应用列表
        List({ space: 8 }) {
          ForEach(this.searchResults, (app: AppModel) => {
            ListItem() {
              AppCard({
                app: app,
                cardType: this.deviceUtils.isTablet() ? 'grid' : 'list',
                showDownloadButton: true
              })
                .margin({ left: 16, right: 16 })
                .onClick(() => this.navigateToAppDetail(app.id.toString()))
            }
          })

          // 加载更多
          if (this.hasMore || this.isLoadingMore) {
            ListItem() {
              LoadMoreView({
                isLoading: this.isLoadingMore,
                hasMore: this.hasMore,
                onLoadMore: () => {
                  if (!this.isLoadingMore && this.hasMore) {
                    this.performSearch(this.searchKeyword, true);
                  }
                }
              })
            }
          }
        }
        .layoutWeight(1)
        .scrollBar(BarState.Auto)
      }
    }
  }

  build() {
    Stack({ alignContent: Alignment.Bottom }) {
      Column() {
        // 顶部搜索栏
        Row() {
          Image($r('app.media.icons'))
            .width(24)
            .height(24)
            .fillColor($r('sys.color.ohos_id_color_text_primary'))
            .onClick(() => {
          this.getUIContext().getRouter().back();
        })

          SearchBar({
            placeholder: '搜索应用',
            searchText: this.searchKeyword,
            showCancelButton: false,
            isLoading: this.isSearching,
            onSearch: (keyword: string) => {
              this.performSearch(keyword);
            },
            onTextChange: (value: string) => {
              this.searchKeyword = value;
              this.showSuggestions = value.trim().length === 0;
            },
            onSearchFocus: () => {
              if (this.searchKeyword.trim().length === 0) {
                this.showSuggestions = true;
              }
            }
          })
            .layoutWeight(1)
            .margin({ left: 12, right: 12 })

          Image($r('app.media.icons'))
            .width(24)
            .height(24)
            .fillColor(this.showFilter ? $r('sys.color.ohos_id_color_primary') : $r('sys.color.ohos_id_color_text_primary'))
            .onClick(() => {
              this.showFilter = !this.showFilter;
            })
        }
        .width('100%')
        .height(56)
        .padding({ left: '16vp', right: '16vp' })
        .backgroundColor($r('sys.color.ohos_id_color_background'))
        .justifyContent(FlexAlign.SpaceBetween)
        .alignItems(VerticalAlign.Center)

        // 内容区域
        if (this.showSuggestions) {
          this.SearchSuggestionsView()
        } else {
          this.SearchResults()
        }
      }
      .width('100%')
      .height('100%')
      .backgroundColor($r('sys.color.ohos_id_color_background'))

      // 筛选器面板
      this.FilterView()
    }
    .width('100%')
    .height('100%')
  }
}

export { SearchPage };