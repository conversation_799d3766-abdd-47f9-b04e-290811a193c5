"use strict";
import { message } from "antd";
import { history } from "@umijs/max";
export const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || "http://localhost:8080/api/v1";
const getAuthToken = () => {
  const useLogto = process.env.REACT_APP_USE_LOGTO === "true";
  if (useLogto) {
    const logtoToken = localStorage.getItem("logto_access_token");
    if (logtoToken) {
      return logtoToken;
    }
  }
  return localStorage.getItem("token");
};
export const errorConfig = {
  // 错误处理： umi@3 的错误处理方案。
  errorConfig: {
    // 错误抛出
    errorThrower: (res) => {
      const { success, data, errorCode, errorMessage, showType } = res;
      if (!success) {
        const error = new Error(errorMessage);
        error.name = "BizError";
        error.info = { errorCode, errorMessage, showType, data };
        throw error;
      }
    },
    // 错误接收及处理
    errorHandler: (error, opts) => {
      if (opts?.skipErrorHandler) throw error;
      if (error.response?.status === 401) {
        message.error("\u767B\u5F55\u5DF2\u8FC7\u671F\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55");
        localStorage.removeItem("token");
        localStorage.removeItem("logto_access_token");
        history.push("/user/login");
        return;
      }
      if (error.name === "BizError") {
        const errorInfo = error.info;
        if (errorInfo) {
          const { errorMessage, errorCode } = errorInfo;
          switch (errorInfo.showType) {
            case "silent":
              break;
            case "warn":
              message.warning(errorMessage);
              break;
            case "error":
              message.error(errorMessage);
              break;
            case "notification":
              break;
            case "redirect":
              break;
            default:
              message.error(errorMessage);
          }
        }
      } else if (error.response) {
        message.error(`Response status:${error.response.status}`);
      } else if (error.request) {
        message.error("None response! Please retry.");
      } else {
        message.error("Request error, please retry.");
      }
    }
  },
  // 请求拦截器
  requestInterceptors: [
    (config) => {
      const token = getAuthToken();
      if (token) {
        config.headers = {
          ...config.headers,
          Authorization: `Bearer ${token}`
        };
      }
      return config;
    }
  ],
  // 响应拦截器
  responseInterceptors: [
    (response) => {
      const { data } = response;
      if (data?.success === false) {
        message.error("\u8BF7\u6C42\u5931\u8D25\uFF01");
      }
      return response;
    }
  ]
};
export const requestConfig = {
  // 错误处理
  errorConfig: {
    errorHandler: errorConfig.errorConfig.errorHandler
  },
  // 请求拦截器
  requestInterceptors: errorConfig.requestInterceptors,
  // 响应拦截器
  responseInterceptors: errorConfig.responseInterceptors,
  // API前缀
  baseURL: API_BASE_URL
};
export default requestConfig;
