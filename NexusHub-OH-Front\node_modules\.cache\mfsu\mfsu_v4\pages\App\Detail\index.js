"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { PageContainer } from "@ant-design/pro-components";
import { Card, Descriptions, Button, Tabs, Table, Tag, Rate, Statistic, Avatar, Image, Typography, Divider, Space, message } from "antd";
import { ArrowLeftOutlined, EditOutlined, DeleteOutlined, CheckOutlined, StopOutlined, DownloadOutlined, ShareAltOutlined, StarOutlined, AppstoreOutlined } from "@ant-design/icons";
import { useState } from "react";
import { useRequest, history, useParams } from "@umijs/max";
const { Title, Paragraph, Text } = Typography;
import { getAppDetail } from "@/services/app";
const fetchAppDetail = async (id) => {
  console.log("Fetching app detail for ID:", id);
  try {
    const response = await getAppDetail(id);
    console.log("App detail response:", response);
    if (response && response.data) {
      const appData = response.data.list[0];
      const screenshots = response.data.screenshots || [];
      const versions = response.data.versions || [];
      const appDetail = {
        id: String(appData.id),
        name: appData.name || "",
        icon: appData.icon || "",
        screenshots: screenshots.map((s) => s.image_url || s),
        developer: appData.developer_name || "",
        developerEmail: "",
        // API可能没有提供
        category: appData.category || "",
        status: appData.status || "published",
        version: appData.current_version || "",
        size: appData.size ? `${Math.round(appData.size / 1024 / 1024)}MB` : "\u672A\u77E5",
        downloads: appData.download_count || 0,
        rating: appData.average_rating || 0,
        ratingCount: appData.rating_count || 0,
        price: "free",
        // 假设所有应用都是免费的
        publishDate: appData.release_date ? new Date(appData.release_date).toISOString().split("T")[0] : "",
        updateDate: appData.updated_at ? new Date(appData.updated_at).toISOString().split("T")[0] : "",
        description: appData.description || "",
        privacyPolicy: appData.privacy_url || "",
        permissions: [],
        // API可能没有提供
        systemRequirements: {
          os: `OpenHarmonyOS ${appData.min_open_harmony_os_ver || "\u672A\u77E5"}+`,
          cpu: "\u672A\u63D0\u4F9B",
          ram: "\u672A\u63D0\u4F9B",
          storage: "\u672A\u63D0\u4F9B"
        },
        releaseNotes: versions.length > 0 ? versions[0].change_log : "",
        reviews: [],
        // API可能没有提供
        versions: versions.map((v) => ({
          version: v.version_name || v.versionName,
          releaseDate: v.released_at ? new Date(v.released_at).toISOString().split("T")[0] : "",
          notes: v.change_log || v.updateDescription || ""
        }))
      };
      console.log("Transformed app detail:", appDetail);
      return appDetail;
    }
    throw new Error("\u83B7\u53D6\u5E94\u7528\u8BE6\u60C5\u5931\u8D25");
  } catch (error) {
    console.error("Error fetching app detail:", error);
    message.error("\u83B7\u53D6\u5E94\u7528\u8BE6\u60C5\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
    throw error;
  }
};
const AppDetail = () => {
  const { id } = useParams();
  const [activeTab, setActiveTab] = useState("basic");
  const { data, loading } = useRequest(
    async () => {
      if (!id) {
        throw new Error("\u5E94\u7528ID\u4E0D\u80FD\u4E3A\u7A7A");
      }
      console.log("Fetching app detail for ID:", id);
      try {
        const response = await getAppDetail(id);
        console.log("App detail response:", response);
        if (response && response.data) {
          const appData = response.data.list[0];
          const screenshots = response.data.screenshots || [];
          const versions = response.data.versions || [];
          const appDetail = {
            id: String(appData.id),
            name: appData.name || "",
            icon: appData.icon || "",
            screenshots: screenshots.map((s) => s.image_url || s),
            developer: appData.developer_name || "",
            developerEmail: "",
            // API可能没有提供
            category: appData.category || "",
            status: appData.status || "published",
            version: appData.current_version || "",
            size: appData.size ? `${Math.round(appData.size / 1024 / 1024)}MB` : "\u672A\u77E5",
            downloads: appData.download_count || 0,
            rating: appData.average_rating || 0,
            ratingCount: appData.rating_count || 0,
            price: "free",
            // 假设所有应用都是免费的
            publishDate: appData.release_date ? new Date(appData.release_date).toISOString().split("T")[0] : "",
            updateDate: appData.updated_at ? new Date(appData.updated_at).toISOString().split("T")[0] : "",
            description: appData.description || "",
            privacyPolicy: appData.privacy_url || "",
            permissions: [],
            // API可能没有提供
            systemRequirements: {
              os: `HarmonyOS ${appData.min_open_harmony_os_ver || "\u672A\u77E5"}+`,
              cpu: "\u672A\u63D0\u4F9B",
              ram: "\u672A\u63D0\u4F9B",
              storage: "\u672A\u63D0\u4F9B"
            },
            releaseNotes: versions.length > 0 ? versions[0].change_log : "",
            reviews: [],
            // API可能没有提供
            versions: versions.map((v) => ({
              version: v.version_name || v.versionName,
              releaseDate: v.released_at ? new Date(v.released_at).toISOString().split("T")[0] : "",
              notes: v.change_log || v.updateDescription || ""
            }))
          };
          console.log("Transformed app detail:", appDetail);
          return appDetail;
        }
        throw new Error("\u83B7\u53D6\u5E94\u7528\u8BE6\u60C5\u5931\u8D25");
      } catch (error) {
        console.error("Error fetching app detail:", error);
        message.error("\u83B7\u53D6\u5E94\u7528\u8BE6\u60C5\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
        throw error;
      }
    },
    {
      refreshDeps: [id],
      onSuccess: (result) => {
        console.log("App detail data loaded successfully:", result);
      },
      onError: (error) => {
        console.error("App detail loading error:", error);
      }
    }
  );
  const handleBack = () => {
    history.push("/app/list");
  };
  const handleEdit = () => {
    message.success(`\u7F16\u8F91\u5E94\u7528 ID: ${id}`);
  };
  const handleDelete = () => {
    message.success(`\u5220\u9664\u5E94\u7528 ID: ${id}`);
    history.push("/app/list");
  };
  const handleApprove = () => {
    message.success(`\u5DF2\u901A\u8FC7\u5E94\u7528 ID: ${id}`);
  };
  const handleReject = () => {
    message.success(`\u5DF2\u62D2\u7EDD\u5E94\u7528 ID: ${id}`);
  };
  const reviewColumns = [
    {
      title: "\u7528\u6237\u540D",
      dataIndex: "username",
      key: "username",
      width: 150
    },
    {
      title: "\u8BC4\u5206",
      dataIndex: "rating",
      key: "rating",
      width: 120,
      render: (rating) => /* @__PURE__ */ jsx(Rate, { disabled: true, defaultValue: rating })
    },
    {
      title: "\u8BC4\u8BBA\u5185\u5BB9",
      dataIndex: "content",
      key: "content"
    },
    {
      title: "\u65E5\u671F",
      dataIndex: "date",
      key: "date",
      width: 120
    }
  ];
  const versionColumns = [
    {
      title: "\u7248\u672C\u53F7",
      dataIndex: "version",
      key: "version",
      width: 120
    },
    {
      title: "\u53D1\u5E03\u65E5\u671F",
      dataIndex: "releaseDate",
      key: "releaseDate",
      width: 120
    },
    {
      title: "\u66F4\u65B0\u5185\u5BB9",
      dataIndex: "notes",
      key: "notes",
      render: (notes) => /* @__PURE__ */ jsx("div", { style: { whiteSpace: "pre-line" }, children: notes })
    }
  ];
  console.log("Rendering AppDetail, data:", data, "loading:", loading);
  return /* @__PURE__ */ jsx(
    PageContainer,
    {
      header: {
        title: data?.name || "\u5E94\u7528\u8BE6\u60C5",
        subTitle: `ID: ${id}`,
        backIcon: /* @__PURE__ */ jsx(ArrowLeftOutlined, {}),
        onBack: handleBack,
        extra: [
          /* @__PURE__ */ jsx(Button, { icon: /* @__PURE__ */ jsx(EditOutlined, {}), onClick: handleEdit, children: "\u7F16\u8F91" }, "edit"),
          /* @__PURE__ */ jsx(Button, { danger: true, icon: /* @__PURE__ */ jsx(DeleteOutlined, {}), onClick: handleDelete, children: "\u5220\u9664" }, "delete"),
          data?.status === "pending" && /* @__PURE__ */ jsx(Button, { type: "primary", icon: /* @__PURE__ */ jsx(CheckOutlined, {}), onClick: handleApprove, children: "\u901A\u8FC7" }, "approve"),
          data?.status === "pending" && /* @__PURE__ */ jsx(Button, { danger: true, icon: /* @__PURE__ */ jsx(StopOutlined, {}), onClick: handleReject, children: "\u62D2\u7EDD" }, "reject")
        ]
      },
      loading,
      children: /* @__PURE__ */ jsxs(Card, { loading, children: [
        loading ? /* @__PURE__ */ jsx("div", { children: "\u52A0\u8F7D\u4E2D..." }) : data ? /* @__PURE__ */ jsxs("div", { style: { display: "flex", marginBottom: 24 }, children: [
          /* @__PURE__ */ jsx("div", { style: { marginRight: 24 }, children: /* @__PURE__ */ jsx(
            Avatar,
            {
              src: data.icon,
              size: 100,
              shape: "square",
              icon: /* @__PURE__ */ jsx(AppstoreOutlined, {}),
              onError: () => {
                console.warn("\u5E94\u7528\u56FE\u6807\u52A0\u8F7D\u5931\u8D25:", data.icon);
                return false;
              }
            }
          ) }),
          /* @__PURE__ */ jsxs("div", { style: { flex: 1 }, children: [
            /* @__PURE__ */ jsx(Title, { level: 3, children: data.name }),
            /* @__PURE__ */ jsxs(Space, { size: "large", children: [
              /* @__PURE__ */ jsx(Text, { type: "secondary", children: data.developer }),
              /* @__PURE__ */ jsx(Tag, { color: "blue", children: data.category }),
              data.status === "published" && /* @__PURE__ */ jsx(Tag, { color: "green", children: "\u5DF2\u53D1\u5E03" }),
              data.status === "pending" && /* @__PURE__ */ jsx(Tag, { color: "blue", children: "\u5F85\u5BA1\u6838" }),
              data.status === "rejected" && /* @__PURE__ */ jsx(Tag, { color: "red", children: "\u5DF2\u62D2\u7EDD" }),
              data.status === "draft" && /* @__PURE__ */ jsx(Tag, { color: "gray", children: "\u8349\u7A3F" })
            ] }),
            /* @__PURE__ */ jsx("div", { style: { marginTop: 16 }, children: /* @__PURE__ */ jsxs(Space, { size: "large", children: [
              /* @__PURE__ */ jsx(Statistic, { title: "\u4E0B\u8F7D\u91CF", value: data.downloads, formatter: (value) => {
                if (value >= 1e9) {
                  return `${(value / 1e9).toFixed(1)}B`;
                } else if (value >= 1e6) {
                  return `${(value / 1e6).toFixed(1)}M`;
                } else if (value >= 1e3) {
                  return `${(value / 1e3).toFixed(1)}K`;
                }
                return value;
              } }),
              /* @__PURE__ */ jsx(Statistic, { title: "\u8BC4\u5206", value: data.rating, precision: 1, suffix: /* @__PURE__ */ jsxs(Text, { type: "secondary", children: [
                "(",
                data.ratingCount,
                ")"
              ] }) }),
              /* @__PURE__ */ jsx(Statistic, { title: "\u5927\u5C0F", value: data.size }),
              /* @__PURE__ */ jsx(Statistic, { title: "\u4EF7\u683C", value: data.price === "free" ? "\u514D\u8D39" : `\xA5${data.price}` })
            ] }) })
          ] }),
          /* @__PURE__ */ jsx("div", { children: /* @__PURE__ */ jsxs(Space, { children: [
            /* @__PURE__ */ jsx(Button, { type: "primary", icon: /* @__PURE__ */ jsx(DownloadOutlined, {}), children: "\u4E0B\u8F7D" }),
            /* @__PURE__ */ jsx(Button, { icon: /* @__PURE__ */ jsx(ShareAltOutlined, {}), children: "\u5206\u4EAB" }),
            /* @__PURE__ */ jsx(Button, { icon: /* @__PURE__ */ jsx(StarOutlined, {}), children: "\u6536\u85CF" })
          ] }) })
        ] }) : /* @__PURE__ */ jsxs("div", { style: { textAlign: "center", padding: "50px 0" }, children: [
          /* @__PURE__ */ jsx(Title, { level: 4, children: "\u5E94\u7528\u8BE6\u60C5\u52A0\u8F7D\u5931\u8D25" }),
          /* @__PURE__ */ jsx("p", { children: "\u65E0\u6CD5\u83B7\u53D6\u5E94\u7528\u8BE6\u60C5\u4FE1\u606F\uFF0C\u8BF7\u68C0\u67E5\u5E94\u7528ID\u662F\u5426\u6B63\u786E\u6216\u7A0D\u540E\u91CD\u8BD5\u3002" }),
          /* @__PURE__ */ jsx(Button, { type: "primary", onClick: () => window.location.reload(), children: "\u91CD\u65B0\u52A0\u8F7D" })
        ] }),
        /* @__PURE__ */ jsx(
          Tabs,
          {
            activeKey: activeTab,
            onChange: setActiveTab,
            items: [
              {
                key: "basic",
                label: "\u57FA\u672C\u4FE1\u606F",
                children: data && /* @__PURE__ */ jsxs(Fragment, { children: [
                  /* @__PURE__ */ jsx(Title, { level: 4, children: "\u5E94\u7528\u4ECB\u7ECD" }),
                  /* @__PURE__ */ jsx(Paragraph, { children: data.description }),
                  /* @__PURE__ */ jsx(Title, { level: 4, children: "\u5E94\u7528\u622A\u56FE" }),
                  /* @__PURE__ */ jsx("div", { style: { display: "flex", overflowX: "auto", marginBottom: 24 }, children: data.screenshots && data.screenshots.length > 0 ? data.screenshots.map((screenshot, index) => /* @__PURE__ */ jsx("div", { style: { marginRight: 16 }, children: /* @__PURE__ */ jsx(
                    Image,
                    {
                      src: screenshot,
                      width: 200,
                      height: 400,
                      style: { objectFit: "cover", borderRadius: 8 }
                    }
                  ) }, index)) : /* @__PURE__ */ jsx("div", { style: { padding: "20px", textAlign: "center", color: "#999" }, children: "\u6682\u65E0\u5E94\u7528\u622A\u56FE" }) }),
                  /* @__PURE__ */ jsx(Divider, {}),
                  /* @__PURE__ */ jsxs(Descriptions, { title: "\u8BE6\u7EC6\u4FE1\u606F", bordered: true, column: 2, children: [
                    /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u5F00\u53D1\u8005", children: data.developer }),
                    /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u5F00\u53D1\u8005\u90AE\u7BB1", children: data.developerEmail }),
                    /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u7248\u672C", children: data.version }),
                    /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u66F4\u65B0\u65E5\u671F", children: data.updateDate }),
                    /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u53D1\u5E03\u65E5\u671F", children: data.publishDate }),
                    /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u7C7B\u522B", children: data.category }),
                    /* @__PURE__ */ jsxs(Descriptions.Item, { label: "\u7CFB\u7EDF\u8981\u6C42", span: 2, children: [
                      /* @__PURE__ */ jsxs("div", { children: [
                        "\u64CD\u4F5C\u7CFB\u7EDF: ",
                        data.systemRequirements.os
                      ] }),
                      /* @__PURE__ */ jsxs("div", { children: [
                        "\u5904\u7406\u5668: ",
                        data.systemRequirements.cpu
                      ] }),
                      /* @__PURE__ */ jsxs("div", { children: [
                        "\u5185\u5B58: ",
                        data.systemRequirements.ram
                      ] }),
                      /* @__PURE__ */ jsxs("div", { children: [
                        "\u5B58\u50A8\u7A7A\u95F4: ",
                        data.systemRequirements.storage
                      ] })
                    ] }),
                    /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u6743\u9650", span: 2, children: data.permissions.map((permission) => /* @__PURE__ */ jsx(Tag, { children: permission }, permission)) })
                  ] })
                ] })
              },
              {
                key: "versions",
                label: "\u7248\u672C\u5386\u53F2",
                children: /* @__PURE__ */ jsx(
                  Table,
                  {
                    columns: versionColumns,
                    dataSource: data?.versions,
                    rowKey: "version",
                    pagination: false
                  }
                )
              },
              {
                key: "reviews",
                label: "\u7528\u6237\u8BC4\u8BBA",
                children: /* @__PURE__ */ jsx(
                  Table,
                  {
                    columns: reviewColumns,
                    dataSource: data?.reviews,
                    rowKey: "id",
                    pagination: false
                  }
                )
              },
              {
                key: "privacy",
                label: "\u9690\u79C1\u653F\u7B56",
                children: data && /* @__PURE__ */ jsx(Paragraph, { children: data.privacyPolicy })
              }
            ]
          }
        )
      ] })
    }
  );
};
export default AppDetail;
