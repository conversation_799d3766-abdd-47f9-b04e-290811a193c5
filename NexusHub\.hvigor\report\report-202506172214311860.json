{"version": "2.0", "ppid": 33740, "events": [{"head": {"id": "1fe4f1b0-9442-45e1-a99c-377d83c69a2c", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032752961300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6bf720b-75fb-4fb9-a2a6-6eb411627cbb", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032753162400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14970cf5-f3ef-4da8-ae2c-684fc727e601", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032776181400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef00fc9a-0287-4087-95c9-6a604441bf9d", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032776595700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4d5656e-a300-4395-bf4d-2275d2d62fcd", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032777953800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df1b14a6-5a26-4ece-87bb-822f3bde2c2a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145032778274100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd407196-553f-4982-8034-6199873fbfae", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041405109300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df417fbd-447a-479e-abf0-1c5ac84db113", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041409891900, "endTime": 145041579883400}, "additional": {"children": ["36828dee-d8a4-4467-adf6-43fafe2baf5f", "dabff795-c90c-48d2-9eee-d6bff711806d", "a6a1ae10-7925-416d-8103-c547e6698fb8", "7a63b239-e4fb-4769-b91b-eb93d42b0eb2", "8ce2e627-d986-42e2-ad5d-0d399ce61b28", "84d38a8c-d4f8-4b35-9843-57f6ec00a066", "53aaced8-4251-47f5-900e-11e45320e707"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "9e402514-19b9-4fdc-9d37-718ec60022c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36828dee-d8a4-4467-adf6-43fafe2baf5f", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041409892700, "endTime": 145041419770800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df417fbd-447a-479e-abf0-1c5ac84db113", "logId": "f487283c-0dba-4c34-bc39-a69a613c798e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dabff795-c90c-48d2-9eee-d6bff711806d", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041419785700, "endTime": 145041578744900}, "additional": {"children": ["d6518a04-b556-4bc0-b94b-98e556f2a989", "671b28f1-71b6-49c4-99ea-851550c80981", "e8fee9db-48f0-458d-9535-ed5faba2c3ec", "0c9761b0-24ea-4184-b13b-39308dca1fb9", "8142aa59-c1fb-4b99-a672-aaad7921295b", "11b956ad-b01d-4f52-96bc-d22ab91bccf3", "440ddbab-13d0-4de1-9e66-a07591f1dad9", "847ff002-849d-44d5-a1e4-b11f313d287d", "b62c5999-8048-47b4-8e1b-d88929f92920"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df417fbd-447a-479e-abf0-1c5ac84db113", "logId": "b9c0cfcd-c78a-4c54-8446-fd38839b3171"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6a1ae10-7925-416d-8103-c547e6698fb8", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041578767500, "endTime": 145041579869300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df417fbd-447a-479e-abf0-1c5ac84db113", "logId": "86df6080-689e-4b84-822b-f808e5f10362"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a63b239-e4fb-4769-b91b-eb93d42b0eb2", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041579874000, "endTime": 145041579880200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df417fbd-447a-479e-abf0-1c5ac84db113", "logId": "d5b3518c-4cc6-474f-b1e8-2247859d7dc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ce2e627-d986-42e2-ad5d-0d399ce61b28", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041412255300, "endTime": 145041412284700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df417fbd-447a-479e-abf0-1c5ac84db113", "logId": "a8ef6759-07f3-45b2-b5d0-f646a54779ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8ef6759-07f3-45b2-b5d0-f646a54779ee", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041412255300, "endTime": 145041412284700}, "additional": {"logType": "info", "children": [], "durationId": "8ce2e627-d986-42e2-ad5d-0d399ce61b28", "parent": "9e402514-19b9-4fdc-9d37-718ec60022c0"}}, {"head": {"id": "84d38a8c-d4f8-4b35-9843-57f6ec00a066", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041415887000, "endTime": 145041415903300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df417fbd-447a-479e-abf0-1c5ac84db113", "logId": "e5ecf3a6-e888-4e8f-9bf1-ff75ec423af2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5ecf3a6-e888-4e8f-9bf1-ff75ec423af2", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041415887000, "endTime": 145041415903300}, "additional": {"logType": "info", "children": [], "durationId": "84d38a8c-d4f8-4b35-9843-57f6ec00a066", "parent": "9e402514-19b9-4fdc-9d37-718ec60022c0"}}, {"head": {"id": "62174b96-6500-41b9-8892-da646e099ae3", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041415947100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caa8640e-0284-49b5-99c9-ec3f48ee48b7", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041419648000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f487283c-0dba-4c34-bc39-a69a613c798e", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041409892700, "endTime": 145041419770800}, "additional": {"logType": "info", "children": [], "durationId": "36828dee-d8a4-4467-adf6-43fafe2baf5f", "parent": "9e402514-19b9-4fdc-9d37-718ec60022c0"}}, {"head": {"id": "d6518a04-b556-4bc0-b94b-98e556f2a989", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041423647100, "endTime": 145041423657900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dabff795-c90c-48d2-9eee-d6bff711806d", "logId": "9afeb139-bcee-4e08-8f5b-09b61c8f7926"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "671b28f1-71b6-49c4-99ea-851550c80981", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041423672800, "endTime": 145041426404000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dabff795-c90c-48d2-9eee-d6bff711806d", "logId": "c4fb4d51-c811-4be4-b498-60c511c72ed6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8fee9db-48f0-458d-9535-ed5faba2c3ec", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041426413700, "endTime": 145041490391300}, "additional": {"children": ["48c03429-9602-4285-85e9-c77e35ebc34b", "b21f9def-b6e9-49d6-a71d-741d2f365e63", "304b3b26-c182-499d-8997-3dec95c99d9d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dabff795-c90c-48d2-9eee-d6bff711806d", "logId": "7f2e6b9c-614c-407c-a3a4-0c3ef5a01447"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c9761b0-24ea-4184-b13b-39308dca1fb9", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041490402200, "endTime": 145041507835300}, "additional": {"children": ["bb0f7b6c-3af9-4122-9999-dd3366a13a84"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dabff795-c90c-48d2-9eee-d6bff711806d", "logId": "49929849-ad9e-4aa7-b22c-6274e3ecd99a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8142aa59-c1fb-4b99-a672-aaad7921295b", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041507848500, "endTime": 145041553529500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dabff795-c90c-48d2-9eee-d6bff711806d", "logId": "9114ee9f-1a0b-4fb8-a0fa-d7f703529907"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11b956ad-b01d-4f52-96bc-d22ab91bccf3", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041554553600, "endTime": 145041563603100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dabff795-c90c-48d2-9eee-d6bff711806d", "logId": "29a78bb1-f5ef-40be-9588-0fe8cc839bce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "440ddbab-13d0-4de1-9e66-a07591f1dad9", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041563624800, "endTime": 145041578608800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dabff795-c90c-48d2-9eee-d6bff711806d", "logId": "9d151308-0f33-43ef-85ff-b560f2654fb5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "847ff002-849d-44d5-a1e4-b11f313d287d", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041578627000, "endTime": 145041578735500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dabff795-c90c-48d2-9eee-d6bff711806d", "logId": "8fc60b38-b543-43f2-9c5b-e40dbb67024b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9afeb139-bcee-4e08-8f5b-09b61c8f7926", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041423647100, "endTime": 145041423657900}, "additional": {"logType": "info", "children": [], "durationId": "d6518a04-b556-4bc0-b94b-98e556f2a989", "parent": "b9c0cfcd-c78a-4c54-8446-fd38839b3171"}}, {"head": {"id": "c4fb4d51-c811-4be4-b498-60c511c72ed6", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041423672800, "endTime": 145041426404000}, "additional": {"logType": "info", "children": [], "durationId": "671b28f1-71b6-49c4-99ea-851550c80981", "parent": "b9c0cfcd-c78a-4c54-8446-fd38839b3171"}}, {"head": {"id": "48c03429-9602-4285-85e9-c77e35ebc34b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041426875300, "endTime": 145041426891600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e8fee9db-48f0-458d-9535-ed5faba2c3ec", "logId": "1249f492-5cb1-467c-a3fb-483c3427007f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1249f492-5cb1-467c-a3fb-483c3427007f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041426875300, "endTime": 145041426891600}, "additional": {"logType": "info", "children": [], "durationId": "48c03429-9602-4285-85e9-c77e35ebc34b", "parent": "7f2e6b9c-614c-407c-a3a4-0c3ef5a01447"}}, {"head": {"id": "b21f9def-b6e9-49d6-a71d-741d2f365e63", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041428334000, "endTime": 145041489741600}, "additional": {"children": ["e405c933-84ca-4f83-bbdf-254164ca88d9", "68e540b6-0529-488a-8c78-7f76596943a9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e8fee9db-48f0-458d-9535-ed5faba2c3ec", "logId": "12086d1a-582c-434a-b768-b6f0b8864bbb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e405c933-84ca-4f83-bbdf-254164ca88d9", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041428334800, "endTime": 145041436557900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b21f9def-b6e9-49d6-a71d-741d2f365e63", "logId": "f87067ff-9600-4346-a4cb-fc5ccf5386b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68e540b6-0529-488a-8c78-7f76596943a9", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041436574800, "endTime": 145041489733100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b21f9def-b6e9-49d6-a71d-741d2f365e63", "logId": "281d1f2b-a701-43d2-9e66-388f91536b34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36018dcd-be2e-4899-aebf-ed57cb7962c3", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041428338500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45d21338-5ff8-43ce-b3aa-ed7951879326", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041436423300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f87067ff-9600-4346-a4cb-fc5ccf5386b5", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041428334800, "endTime": 145041436557900}, "additional": {"logType": "info", "children": [], "durationId": "e405c933-84ca-4f83-bbdf-254164ca88d9", "parent": "12086d1a-582c-434a-b768-b6f0b8864bbb"}}, {"head": {"id": "257aa96d-7855-42e4-8ec4-f4242289d319", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041436587000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "993112a5-8b3a-4e0b-933c-4169a0c86e85", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041442437500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dcc608f-e8cd-482d-95a8-81025d3abdf8", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041442536200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4071db3-c8c7-47fc-88d9-781dfec63a22", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041442625700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a60c6bd-c415-424e-b787-ae5c1ca29ca7", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041442696000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94a2aee8-814b-47b9-8ef6-8542f2ef20ba", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041443996200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "717356c8-254e-4ac8-9956-ef05f95298b0", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041453742100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebf26cd1-8bb0-4f65-ad32-71b56fa709c9", "name": "Sdk init in 24 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041472251800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5fb59ae-1325-4041-b84a-ada082b9b884", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041472420300}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 14, "second": 31}, "markType": "other"}}, {"head": {"id": "8a8903bc-ac58-4d04-a683-2213fee46faf", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041472435300}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 14, "second": 31}, "markType": "other"}}, {"head": {"id": "74926c94-347d-4173-a889-22e5fe237daf", "name": "Project task initialization takes 16 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041489535500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05ca09bb-71d3-4a9f-8a57-99052b709f64", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041489637500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2172e240-7c87-4875-9283-2ab53d037415", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041489679400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "026eb8e2-7b55-4d7b-93cf-c6823f27bbdf", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041489706400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "281d1f2b-a701-43d2-9e66-388f91536b34", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041436574800, "endTime": 145041489733100}, "additional": {"logType": "info", "children": [], "durationId": "68e540b6-0529-488a-8c78-7f76596943a9", "parent": "12086d1a-582c-434a-b768-b6f0b8864bbb"}}, {"head": {"id": "12086d1a-582c-434a-b768-b6f0b8864bbb", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041428334000, "endTime": 145041489741600}, "additional": {"logType": "info", "children": ["f87067ff-9600-4346-a4cb-fc5ccf5386b5", "281d1f2b-a701-43d2-9e66-388f91536b34"], "durationId": "b21f9def-b6e9-49d6-a71d-741d2f365e63", "parent": "7f2e6b9c-614c-407c-a3a4-0c3ef5a01447"}}, {"head": {"id": "304b3b26-c182-499d-8997-3dec95c99d9d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041490362100, "endTime": 145041490377000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e8fee9db-48f0-458d-9535-ed5faba2c3ec", "logId": "6c012935-9aaf-4e15-9e12-4b2e7278a618"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c012935-9aaf-4e15-9e12-4b2e7278a618", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041490362100, "endTime": 145041490377000}, "additional": {"logType": "info", "children": [], "durationId": "304b3b26-c182-499d-8997-3dec95c99d9d", "parent": "7f2e6b9c-614c-407c-a3a4-0c3ef5a01447"}}, {"head": {"id": "7f2e6b9c-614c-407c-a3a4-0c3ef5a01447", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041426413700, "endTime": 145041490391300}, "additional": {"logType": "info", "children": ["1249f492-5cb1-467c-a3fb-483c3427007f", "12086d1a-582c-434a-b768-b6f0b8864bbb", "6c012935-9aaf-4e15-9e12-4b2e7278a618"], "durationId": "e8fee9db-48f0-458d-9535-ed5faba2c3ec", "parent": "b9c0cfcd-c78a-4c54-8446-fd38839b3171"}}, {"head": {"id": "bb0f7b6c-3af9-4122-9999-dd3366a13a84", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041490817500, "endTime": 145041507825100}, "additional": {"children": ["5ac8a27b-669a-43a4-8a01-35cbe517e1da", "b4bacfae-cc7a-4e5e-a7c5-e2c2e6e6454d", "a30ff0ce-dd88-4a68-9d48-65f075a299ad"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c9761b0-24ea-4184-b13b-39308dca1fb9", "logId": "f7c9b5de-f5ff-44b0-ace1-38349bca22b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ac8a27b-669a-43a4-8a01-35cbe517e1da", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041492832700, "endTime": 145041492846000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb0f7b6c-3af9-4122-9999-dd3366a13a84", "logId": "21114fcb-a2db-4937-8aeb-9400db96495c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21114fcb-a2db-4937-8aeb-9400db96495c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041492832700, "endTime": 145041492846000}, "additional": {"logType": "info", "children": [], "durationId": "5ac8a27b-669a-43a4-8a01-35cbe517e1da", "parent": "f7c9b5de-f5ff-44b0-ace1-38349bca22b9"}}, {"head": {"id": "b4bacfae-cc7a-4e5e-a7c5-e2c2e6e6454d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041494129100, "endTime": 145041506750900}, "additional": {"children": ["986863e0-2dc5-4fb1-9005-efe7f6df8460", "96137789-3425-4e56-91e3-c66a6cfb0723"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb0f7b6c-3af9-4122-9999-dd3366a13a84", "logId": "f97beb8a-41f4-4160-a81a-2626759d218c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "986863e0-2dc5-4fb1-9005-efe7f6df8460", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041494129800, "endTime": 145041497470900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4bacfae-cc7a-4e5e-a7c5-e2c2e6e6454d", "logId": "f8400261-081d-4cc0-acaa-6070c097cd1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96137789-3425-4e56-91e3-c66a6cfb0723", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041497480500, "endTime": 145041506742200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b4bacfae-cc7a-4e5e-a7c5-e2c2e6e6454d", "logId": "f73d2128-e01f-4c65-8c55-08458eb972a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04a358a3-627f-4430-a4ad-50f160d972a2", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041494133300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4d9852e-5962-4adc-8780-15adb2ce1b78", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041497389500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8400261-081d-4cc0-acaa-6070c097cd1c", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041494129800, "endTime": 145041497470900}, "additional": {"logType": "info", "children": [], "durationId": "986863e0-2dc5-4fb1-9005-efe7f6df8460", "parent": "f97beb8a-41f4-4160-a81a-2626759d218c"}}, {"head": {"id": "e4e6159b-fe97-4063-a538-8c132e4abc7d", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041497488900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdf40942-ec85-49bd-848d-215ca34f5136", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041503162800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24e31c40-b634-47f8-84cb-ea240f9b9624", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041503268500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45054303-a26a-4fa0-8623-289325474575", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041503395700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4acb1dd-b187-4eca-beff-6c99d96b4c11", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041503466300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3e9bd52-d52b-4241-9bca-bf3946875d4d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041503495800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbc51881-348d-4582-a48a-f3688c068d32", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041503521900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35400450-ad35-4add-9e7a-b9c1c8e02e02", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041503554400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c51e796f-a0c2-464d-823a-11177804dd27", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041503581400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a1d15b6-abc8-496a-bff1-5159ce0533e4", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041503707900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7a1e4fc-e7ef-41ff-9e6a-c254dd4837c4", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041503771300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27c48f44-c4a6-4f9a-ac7b-ce644c21981e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041504021800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d066b9f-b098-4fc1-86ba-50b0c55a2e6d", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041504070600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4033535-42c9-4236-abfe-7ef686aa44e9", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041504109600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d65dce18-d4fb-4448-8266-457f1a6ef9e3", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041504138500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adf13687-f096-42a1-805d-24504edb11d0", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041504225200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7863ef0f-192f-4488-a420-bd6098692da2", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041504277600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4edc3f6f-a54f-4e57-89eb-9df5260c813a", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041504344600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4baad95-e11c-40fa-857a-a290e231f8d0", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041504367900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c202970-3633-49c6-959f-6c5e936a986d", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041504400600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c51b0d77-3706-4f59-86f1-2ce00329cfb7", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041506551700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32194df5-fa99-4009-b53f-d05fc5c31c34", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041506654800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b09947e9-9d06-4990-aab3-5fea5c12ecaf", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041506692300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a42636ba-6fad-40f7-9767-b74ab288c539", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041506716700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f73d2128-e01f-4c65-8c55-08458eb972a0", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041497480500, "endTime": 145041506742200}, "additional": {"logType": "info", "children": [], "durationId": "96137789-3425-4e56-91e3-c66a6cfb0723", "parent": "f97beb8a-41f4-4160-a81a-2626759d218c"}}, {"head": {"id": "f97beb8a-41f4-4160-a81a-2626759d218c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041494129100, "endTime": 145041506750900}, "additional": {"logType": "info", "children": ["f8400261-081d-4cc0-acaa-6070c097cd1c", "f73d2128-e01f-4c65-8c55-08458eb972a0"], "durationId": "b4bacfae-cc7a-4e5e-a7c5-e2c2e6e6454d", "parent": "f7c9b5de-f5ff-44b0-ace1-38349bca22b9"}}, {"head": {"id": "a30ff0ce-dd88-4a68-9d48-65f075a299ad", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041507787100, "endTime": 145041507798700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bb0f7b6c-3af9-4122-9999-dd3366a13a84", "logId": "ca9b4d39-3b53-44aa-9233-d82e85aa07c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca9b4d39-3b53-44aa-9233-d82e85aa07c9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041507787100, "endTime": 145041507798700}, "additional": {"logType": "info", "children": [], "durationId": "a30ff0ce-dd88-4a68-9d48-65f075a299ad", "parent": "f7c9b5de-f5ff-44b0-ace1-38349bca22b9"}}, {"head": {"id": "f7c9b5de-f5ff-44b0-ace1-38349bca22b9", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041490817500, "endTime": 145041507825100}, "additional": {"logType": "info", "children": ["21114fcb-a2db-4937-8aeb-9400db96495c", "f97beb8a-41f4-4160-a81a-2626759d218c", "ca9b4d39-3b53-44aa-9233-d82e85aa07c9"], "durationId": "bb0f7b6c-3af9-4122-9999-dd3366a13a84", "parent": "49929849-ad9e-4aa7-b22c-6274e3ecd99a"}}, {"head": {"id": "49929849-ad9e-4aa7-b22c-6274e3ecd99a", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041490402200, "endTime": 145041507835300}, "additional": {"logType": "info", "children": ["f7c9b5de-f5ff-44b0-ace1-38349bca22b9"], "durationId": "0c9761b0-24ea-4184-b13b-39308dca1fb9", "parent": "b9c0cfcd-c78a-4c54-8446-fd38839b3171"}}, {"head": {"id": "e11e916d-0e0c-4786-b1d4-d11c685b2664", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041522340400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d687252d-d3ca-4b05-9ed3-7cefeced296c", "name": "hvigorfile, resolve hvigorfile dependencies in 46 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041553368000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9114ee9f-1a0b-4fb8-a0fa-d7f703529907", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041507848500, "endTime": 145041553529500}, "additional": {"logType": "info", "children": [], "durationId": "8142aa59-c1fb-4b99-a672-aaad7921295b", "parent": "b9c0cfcd-c78a-4c54-8446-fd38839b3171"}}, {"head": {"id": "b62c5999-8048-47b4-8e1b-d88929f92920", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041554325400, "endTime": 145041554538300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dabff795-c90c-48d2-9eee-d6bff711806d", "logId": "2a9d2935-e15a-447b-8f1f-5d2cd33f33ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b510f985-9d84-4f30-ad4a-0cce91ea742b", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041554357500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a9d2935-e15a-447b-8f1f-5d2cd33f33ae", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041554325400, "endTime": 145041554538300}, "additional": {"logType": "info", "children": [], "durationId": "b62c5999-8048-47b4-8e1b-d88929f92920", "parent": "b9c0cfcd-c78a-4c54-8446-fd38839b3171"}}, {"head": {"id": "19f7b74e-e6d4-46f4-a04b-9be8a6a504ca", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041556068200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f6d5367-e506-443d-878d-bae4a176fac8", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041562627300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29a78bb1-f5ef-40be-9588-0fe8cc839bce", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041554553600, "endTime": 145041563603100}, "additional": {"logType": "info", "children": [], "durationId": "11b956ad-b01d-4f52-96bc-d22ab91bccf3", "parent": "b9c0cfcd-c78a-4c54-8446-fd38839b3171"}}, {"head": {"id": "29b1bdd1-5204-45d0-b77b-cd16bce84aa6", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041563649600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3dc7e44-223d-4be4-ac2e-598bc098c337", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041571318200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dfd6f82-a6e3-4556-b29c-a11094e46f53", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041571449800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35629f33-06a4-4d80-8c0f-25daa29f9e22", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041571615800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39f3c83c-c2ff-472b-ab80-4dacc1c2208b", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041574785800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2089f116-c538-4c6b-bce6-53035e02ed6e", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041574932100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d151308-0f33-43ef-85ff-b560f2654fb5", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041563624800, "endTime": 145041578608800}, "additional": {"logType": "info", "children": [], "durationId": "440ddbab-13d0-4de1-9e66-a07591f1dad9", "parent": "b9c0cfcd-c78a-4c54-8446-fd38839b3171"}}, {"head": {"id": "d477252a-c6e7-40da-a794-975cb025d8e1", "name": "Configuration phase cost:155 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041578650000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fc60b38-b543-43f2-9c5b-e40dbb67024b", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041578627000, "endTime": 145041578735500}, "additional": {"logType": "info", "children": [], "durationId": "847ff002-849d-44d5-a1e4-b11f313d287d", "parent": "b9c0cfcd-c78a-4c54-8446-fd38839b3171"}}, {"head": {"id": "b9c0cfcd-c78a-4c54-8446-fd38839b3171", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041419785700, "endTime": 145041578744900}, "additional": {"logType": "info", "children": ["9afeb139-bcee-4e08-8f5b-09b61c8f7926", "c4fb4d51-c811-4be4-b498-60c511c72ed6", "7f2e6b9c-614c-407c-a3a4-0c3ef5a01447", "49929849-ad9e-4aa7-b22c-6274e3ecd99a", "9114ee9f-1a0b-4fb8-a0fa-d7f703529907", "29a78bb1-f5ef-40be-9588-0fe8cc839bce", "9d151308-0f33-43ef-85ff-b560f2654fb5", "8fc60b38-b543-43f2-9c5b-e40dbb67024b", "2a9d2935-e15a-447b-8f1f-5d2cd33f33ae"], "durationId": "dabff795-c90c-48d2-9eee-d6bff711806d", "parent": "9e402514-19b9-4fdc-9d37-718ec60022c0"}}, {"head": {"id": "53aaced8-4251-47f5-900e-11e45320e707", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041579843900, "endTime": 145041579858000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df417fbd-447a-479e-abf0-1c5ac84db113", "logId": "3775d9a5-5b36-4df1-8fc9-7840b7b2a8a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3775d9a5-5b36-4df1-8fc9-7840b7b2a8a4", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041579843900, "endTime": 145041579858000}, "additional": {"logType": "info", "children": [], "durationId": "53aaced8-4251-47f5-900e-11e45320e707", "parent": "9e402514-19b9-4fdc-9d37-718ec60022c0"}}, {"head": {"id": "86df6080-689e-4b84-822b-f808e5f10362", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041578767500, "endTime": 145041579869300}, "additional": {"logType": "info", "children": [], "durationId": "a6a1ae10-7925-416d-8103-c547e6698fb8", "parent": "9e402514-19b9-4fdc-9d37-718ec60022c0"}}, {"head": {"id": "d5b3518c-4cc6-474f-b1e8-2247859d7dc9", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041579874000, "endTime": 145041579880200}, "additional": {"logType": "info", "children": [], "durationId": "7a63b239-e4fb-4769-b91b-eb93d42b0eb2", "parent": "9e402514-19b9-4fdc-9d37-718ec60022c0"}}, {"head": {"id": "9e402514-19b9-4fdc-9d37-718ec60022c0", "name": "init", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041409891900, "endTime": 145041579883400}, "additional": {"logType": "info", "children": ["f487283c-0dba-4c34-bc39-a69a613c798e", "b9c0cfcd-c78a-4c54-8446-fd38839b3171", "86df6080-689e-4b84-822b-f808e5f10362", "d5b3518c-4cc6-474f-b1e8-2247859d7dc9", "a8ef6759-07f3-45b2-b5d0-f646a54779ee", "e5ecf3a6-e888-4e8f-9bf1-ff75ec423af2", "3775d9a5-5b36-4df1-8fc9-7840b7b2a8a4"], "durationId": "df417fbd-447a-479e-abf0-1c5ac84db113"}}, {"head": {"id": "69eb5e6d-ccac-41af-8eb9-8419f43f2bdd", "name": "Configuration task cost before running: 173 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041580008900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ba9b11f-61b9-485d-b05e-b6f6f07cf3e2", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041587515300, "endTime": 145041597218400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e9952b5f-e4f7-4f2d-8cd1-2ec2a4848c1f", "logId": "8985a13d-186b-4c87-ad82-67eda7c2c46d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9952b5f-e4f7-4f2d-8cd1-2ec2a4848c1f", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041581270500}, "additional": {"logType": "detail", "children": [], "durationId": "5ba9b11f-61b9-485d-b05e-b6f6f07cf3e2"}}, {"head": {"id": "cf5b88c2-03fa-431f-921f-d4ce9cd7bebd", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041581897000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b7c2359-93bf-4a87-a96b-39aee90eb170", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041581998800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8820d79c-e394-4637-9a82-4b86ff63aba7", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041582672500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee78f3d1-ccfe-4f8d-8fb1-4a37f5063215", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041583339100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6906c595-9f58-4ed7-a88e-bb97a64027d6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041584242800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae4861ed-40f6-43a7-8039-1ed3a15cbb42", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041584317400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f14004de-6175-472e-b732-0c4396f1ac36", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041587530200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c24f77f-0359-424e-8089-82a158545ea0", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041597026300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a027729-5963-4945-938d-6a27a305b453", "name": "entry : default@PreBuild cost memory 0.31926727294921875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041597162100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8985a13d-186b-4c87-ad82-67eda7c2c46d", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041587515300, "endTime": 145041597218400}, "additional": {"logType": "info", "children": [], "durationId": "5ba9b11f-61b9-485d-b05e-b6f6f07cf3e2"}}, {"head": {"id": "8c240d15-15f2-459a-a06e-ef0b69b0fe75", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041604767000, "endTime": 145041606396200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5e6b4deb-47b6-4197-946e-5a96103238d7", "logId": "ba354e60-26f6-4f66-8fee-082523c4d7eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e6b4deb-47b6-4197-946e-5a96103238d7", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041601363600}, "additional": {"logType": "detail", "children": [], "durationId": "8c240d15-15f2-459a-a06e-ef0b69b0fe75"}}, {"head": {"id": "d9a667dd-8342-4583-ad3a-d989a8d2a66d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041603342100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2132cb11-774a-41a9-933d-559e564ca53d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041603508700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e742d363-9680-4bb6-b9f2-e624e9b3b649", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041604778700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b25cd343-9eba-401c-b874-79cccb0fb584", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041605401500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0a7f972-f244-4910-882a-7abdbfe9f77d", "name": "entry : default@CreateModuleInfo cost memory 0.06114959716796875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041606237300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5e957e7-1a18-423b-b470-f3dfa91a467b", "name": "runTaskFromQueue task cost before running: 199 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041606346400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba354e60-26f6-4f66-8fee-082523c4d7eb", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041604767000, "endTime": 145041606396200, "totalTime": 1556900}, "additional": {"logType": "info", "children": [], "durationId": "8c240d15-15f2-459a-a06e-ef0b69b0fe75"}}, {"head": {"id": "daca59d3-5492-4149-8b5f-642c2a0c6703", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041613858100, "endTime": 145041616599500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bacc70b3-b5f8-4398-b6f3-639fe3489f12", "logId": "a6f45966-9256-4a09-89f5-6c6294cb4cb1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bacc70b3-b5f8-4398-b6f3-639fe3489f12", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041608341000}, "additional": {"logType": "detail", "children": [], "durationId": "daca59d3-5492-4149-8b5f-642c2a0c6703"}}, {"head": {"id": "e794dd4a-cc46-4d38-b3d1-e5531229c3fd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041609343000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42b89310-6ea2-4feb-821c-906084ada678", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041609429100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73b7a720-de87-4d8e-86f7-9328654523a6", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041613874900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4916d6c0-f786-4ed2-acd3-23decc15858b", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041615029800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "945f824a-06c3-4d20-a2f3-6811ca12ac89", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041616430200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2aba642-8573-4543-9c14-c0cff53367cc", "name": "entry : default@GenerateMetadata cost memory 0.103485107421875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041616539700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6f45966-9256-4a09-89f5-6c6294cb4cb1", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041613858100, "endTime": 145041616599500}, "additional": {"logType": "info", "children": [], "durationId": "daca59d3-5492-4149-8b5f-642c2a0c6703"}}, {"head": {"id": "d15fa9ee-431a-45c2-a8b1-17117cb7e414", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041619705700, "endTime": 145041620106900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3b2945f1-30eb-433e-afc8-7ee07b698d31", "logId": "64e3718e-72e2-44a0-b6e4-62b8e963a58b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b2945f1-30eb-433e-afc8-7ee07b698d31", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041618327600}, "additional": {"logType": "detail", "children": [], "durationId": "d15fa9ee-431a-45c2-a8b1-17117cb7e414"}}, {"head": {"id": "e7d7d2bf-9ef2-434b-9072-fb97f35e485d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041619472500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e16bc4bd-1bd2-402b-a8f7-fee2e80fbfbd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041619583600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36daed0e-a459-416c-9e9b-65f8d6a66c9a", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041619713600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "321b73de-26d6-467c-88eb-06de773a4199", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041619827600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b85510f0-67fe-4646-9b75-d49699908cc0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041619910000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5704f31-fa7e-4d4a-9d7d-c9ddbcce721a", "name": "entry : default@ConfigureCmake cost memory 0.0374755859375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041619995300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7cab3a9-d650-453b-bd32-45e8b4c2c16f", "name": "runTaskFromQueue task cost before running: 213 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041620069000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64e3718e-72e2-44a0-b6e4-62b8e963a58b", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041619705700, "endTime": 145041620106900, "totalTime": 343600}, "additional": {"logType": "info", "children": [], "durationId": "d15fa9ee-431a-45c2-a8b1-17117cb7e414"}}, {"head": {"id": "43a7027a-38d1-4ee2-bef7-53588abe1b12", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041623750700, "endTime": 145041626073100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "ed160dfb-9be2-4d19-8f2c-d8fbcfd4e3ac", "logId": "dfe33037-a360-4534-9f0f-f097dc4e278a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed160dfb-9be2-4d19-8f2c-d8fbcfd4e3ac", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041621912400}, "additional": {"logType": "detail", "children": [], "durationId": "43a7027a-38d1-4ee2-bef7-53588abe1b12"}}, {"head": {"id": "0de2b073-c629-41a2-8093-7ea1fffd9e83", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041623001200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d71470f5-35a9-4267-9c77-557bfa9ad638", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041623103100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20924440-0537-4c49-addd-a7d26878afbb", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041623763600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fbe14a0-f4d1-43d3-985d-0769b2b04463", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041625860900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e204d03c-13df-4b29-a240-2a5928509230", "name": "entry : default@MergeProfile cost memory 0.11850738525390625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041626009700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfe33037-a360-4534-9f0f-f097dc4e278a", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041623750700, "endTime": 145041626073100}, "additional": {"logType": "info", "children": [], "durationId": "43a7027a-38d1-4ee2-bef7-53588abe1b12"}}, {"head": {"id": "de721f0b-64be-4cf5-9fd4-86e16eb5793d", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041629776600, "endTime": 145041632493400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8e56179e-2db7-4db3-9c07-8c8410588693", "logId": "ae3e1113-3823-4bd1-9334-c186a70792f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e56179e-2db7-4db3-9c07-8c8410588693", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041627771400}, "additional": {"logType": "detail", "children": [], "durationId": "de721f0b-64be-4cf5-9fd4-86e16eb5793d"}}, {"head": {"id": "96b7625d-05cf-45d3-91d2-6d7203f1f424", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041628879700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "091779ca-e2e4-496d-a00c-4c6fe318a20c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041628988500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4584797-0b79-4898-a446-dc0a43e2876d", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041629786800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a11483b-f3e0-4910-ad36-3ea8f896e8c7", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041630753100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af6e6cdf-c000-4bc3-b4be-ab6e764f2434", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041632326800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f79a5271-8482-4533-b936-9685c807a56a", "name": "entry : default@CreateBuildProfile cost memory 0.10790252685546875", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041632437300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae3e1113-3823-4bd1-9334-c186a70792f1", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041629776600, "endTime": 145041632493400}, "additional": {"logType": "info", "children": [], "durationId": "de721f0b-64be-4cf5-9fd4-86e16eb5793d"}}, {"head": {"id": "1b537876-5964-4890-a366-d7a02bcf3343", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041636250100, "endTime": 145041636703100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "fcd54c46-384c-48bd-b0b9-a52564737429", "logId": "6dd79725-0e4e-4ce1-b006-a86d76f1f37f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcd54c46-384c-48bd-b0b9-a52564737429", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041634519900}, "additional": {"logType": "detail", "children": [], "durationId": "1b537876-5964-4890-a366-d7a02bcf3343"}}, {"head": {"id": "c121ebf9-b14f-4482-9449-12fd4cc7de0c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041635507300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "590cd3f2-ae42-48c5-8e7d-585e65afd983", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041635615000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e1c9712-89be-4457-826e-086640f02c57", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041636260000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7efbc1c0-191f-44b3-80e5-faf5613c8274", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041636371000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d36a4289-1e3f-40ce-b8fc-44f49bd61cd6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041636412300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfb99632-b1d0-456e-a005-678fafa4bd8e", "name": "entry : default@PreCheckSyscap cost memory 0.041107177734375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041636577700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab6cec5b-417f-4cb0-a2a4-10a92746addb", "name": "runTaskFromQueue task cost before running: 229 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041636665300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dd79725-0e4e-4ce1-b006-a86d76f1f37f", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041636250100, "endTime": 145041636703100, "totalTime": 396200}, "additional": {"logType": "info", "children": [], "durationId": "1b537876-5964-4890-a366-d7a02bcf3343"}}, {"head": {"id": "7a9433e6-36fe-4bad-81b3-46781241568e", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041640707900, "endTime": 145041648597600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "424b75a8-b29c-4a9a-b9f2-27c8eb6160d6", "logId": "f34ea97e-5944-4904-a06f-459a371daf66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "424b75a8-b29c-4a9a-b9f2-27c8eb6160d6", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041638022300}, "additional": {"logType": "detail", "children": [], "durationId": "7a9433e6-36fe-4bad-81b3-46781241568e"}}, {"head": {"id": "4a37d210-95e9-4228-8df2-92287af7cbb5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041639160000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cefc962-9af1-4a6d-a2ca-44f3d3ab8fec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041639253200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c68f7bce-f044-4991-bff6-5f677fcb20fd", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041640722100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c643494-854d-4d77-9b8c-4a8db8918685", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041647663800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60caf882-c29d-4444-a5b1-1919e81285f8", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041648395700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b40570ce-d6b5-4946-9330-f5206c682499", "name": "entry : default@GeneratePkgContextInfo cost memory -8.090538024902344", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041648533500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f34ea97e-5944-4904-a06f-459a371daf66", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041640707900, "endTime": 145041648597600}, "additional": {"logType": "info", "children": [], "durationId": "7a9433e6-36fe-4bad-81b3-46781241568e"}}, {"head": {"id": "6c2ec1ab-7b77-4d16-8527-27ac12f48156", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041655496800, "endTime": 145041657389400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "da139723-a4ef-474c-a3b4-89427559552f", "logId": "c5df5142-24ff-44c0-b256-b83da8e2aa53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da139723-a4ef-474c-a3b4-89427559552f", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041650411200}, "additional": {"logType": "detail", "children": [], "durationId": "6c2ec1ab-7b77-4d16-8527-27ac12f48156"}}, {"head": {"id": "f88e00b3-ffbd-4a6b-8c66-fb5c7f41f9ec", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041651357000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95ebe917-6176-4381-8892-054111162d6e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041651455200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7300c8a9-0bbd-4d7c-919a-6b0d11b2a92b", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041655510700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5bdb050-3662-4032-aff8-afa3a379fb72", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041657024600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9956cab9-9027-4d55-a388-005b7c29de79", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041657142900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "252cdd71-482b-4648-b9ab-2d94db39590e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041657213600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caed5f9a-d615-4430-b567-12433f7334f3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041657246400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a836b5b-4135-484e-a6b6-16e3a4edeefd", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1219329833984375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041657305000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5990fadc-bce1-4c38-962f-aee9488bb303", "name": "runTaskFromQueue task cost before running: 250 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041657354000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5df5142-24ff-44c0-b256-b83da8e2aa53", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041655496800, "endTime": 145041657389400, "totalTime": 1847700}, "additional": {"logType": "info", "children": [], "durationId": "6c2ec1ab-7b77-4d16-8527-27ac12f48156"}}, {"head": {"id": "dafc226a-f32a-436e-9eec-8dabb10725e9", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041661094700, "endTime": 145041661380000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "eecb4751-1bda-4bdd-9258-d275d3409617", "logId": "b06c5b83-b76b-4a7e-913c-b2c864beac9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eecb4751-1bda-4bdd-9258-d275d3409617", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041659530900}, "additional": {"logType": "detail", "children": [], "durationId": "dafc226a-f32a-436e-9eec-8dabb10725e9"}}, {"head": {"id": "47d09fc3-20c5-4bc4-8b5e-fc57d90ac216", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041660396000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ca72256-2619-4374-96eb-20d26fc273dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041660483600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "717f9932-40f9-4be5-9448-6ccb2569912b", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041661103400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7b8622c-f327-41ea-81de-5efd25741d1d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041661207800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59516535-e89b-43a7-a1f8-11d245902323", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041661243900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99f4f3c2-2540-4066-903a-9956a161d3a3", "name": "entry : default@BuildNativeWithCmake cost memory 0.03852081298828125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041661297200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a91747e-5120-4bbb-9611-9ee21037bdd3", "name": "runTaskFromQueue task cost before running: 254 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041661350300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b06c5b83-b76b-4a7e-913c-b2c864beac9a", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041661094700, "endTime": 145041661380000, "totalTime": 241400}, "additional": {"logType": "info", "children": [], "durationId": "dafc226a-f32a-436e-9eec-8dabb10725e9"}}, {"head": {"id": "407f69b8-8c42-4dde-b18c-810a8e3b9845", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041664616400, "endTime": 145041668349100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0b213f1f-0f8e-4e64-a71e-e26d2cd2f17c", "logId": "ad01b296-3f3a-4a89-bd60-6c60ba67b1a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b213f1f-0f8e-4e64-a71e-e26d2cd2f17c", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041662849300}, "additional": {"logType": "detail", "children": [], "durationId": "407f69b8-8c42-4dde-b18c-810a8e3b9845"}}, {"head": {"id": "8b4d011c-21c7-48ec-9b4c-dd5e1b7c7170", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041663818800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f047866c-acc8-422e-8afd-c5bc3891ae02", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041663938700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9cd3aea-5bf1-4090-9ef4-5ab567b634fa", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041664625500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6acb76f-9bd1-49f4-8d1b-ce432a8ed66a", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041668140500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9431c2c3-ef36-4ed7-8087-45d139c4b8ae", "name": "entry : default@MakePackInfo cost memory 0.16504669189453125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041668279000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad01b296-3f3a-4a89-bd60-6c60ba67b1a2", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041664616400, "endTime": 145041668349100}, "additional": {"logType": "info", "children": [], "durationId": "407f69b8-8c42-4dde-b18c-810a8e3b9845"}}, {"head": {"id": "48f900a4-240f-4b85-9408-1c9ab9432d4d", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041672710400, "endTime": 145041676015900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "af3c4df1-52bc-4c35-a5a5-d559c1d2ef25", "logId": "1391a294-8aeb-4891-bf9e-ffe856eaee5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af3c4df1-52bc-4c35-a5a5-d559c1d2ef25", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041670501000}, "additional": {"logType": "detail", "children": [], "durationId": "48f900a4-240f-4b85-9408-1c9ab9432d4d"}}, {"head": {"id": "999bbb88-ff9f-4aea-be19-56709220f062", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041671527200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f09d408-f6a8-4464-8376-829913704144", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041671617700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e17551e9-e7fc-4673-8b80-6a4e81924b15", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041672721100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56f681d3-8769-4cdf-8de0-813e433f1827", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041672895800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a82ae3e0-32d3-4891-8066-44fea19331e3", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041673755300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b53bc7f-33c9-40b6-adf4-289548b6d5ec", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041675865500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f76b1170-96ca-4409-beca-f04dc5188d97", "name": "entry : default@SyscapTransform cost memory 0.1504669189453125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041675961100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1391a294-8aeb-4891-bf9e-ffe856eaee5b", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041672710400, "endTime": 145041676015900}, "additional": {"logType": "info", "children": [], "durationId": "48f900a4-240f-4b85-9408-1c9ab9432d4d"}}, {"head": {"id": "5979949a-0910-466b-8982-ad15ee92fabb", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041680025900, "endTime": 145041682151800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "2a0b3940-13cf-4242-84cd-af4e3594e965", "logId": "5646ebae-fa9e-479b-8c2d-00ce71223426"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a0b3940-13cf-4242-84cd-af4e3594e965", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041677452700}, "additional": {"logType": "detail", "children": [], "durationId": "5979949a-0910-466b-8982-ad15ee92fabb"}}, {"head": {"id": "7be14f73-a611-41be-9d8f-0f3177a9677c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041678396800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a315446-b6b3-4439-b779-d6415d0c5794", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041678498200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1c7e8e7-e978-4920-8859-5c283dd947e3", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041680036400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "825f9d0c-3ad1-4d06-aae9-ff5393fa0974", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041681987400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84bcf723-7d24-486d-83d9-5bbb256000a6", "name": "entry : default@ProcessProfile cost memory 0.123321533203125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041682085200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5646ebae-fa9e-479b-8c2d-00ce71223426", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041680025900, "endTime": 145041682151800}, "additional": {"logType": "info", "children": [], "durationId": "5979949a-0910-466b-8982-ad15ee92fabb"}}, {"head": {"id": "01a20973-9a08-4445-8a3f-610568791ea0", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041686394800, "endTime": 145041692104900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1c0ca965-3196-4531-be2b-3e84327e0b92", "logId": "edde03fd-357a-487d-ac23-4baa5f7b5d3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c0ca965-3196-4531-be2b-3e84327e0b92", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041683641000}, "additional": {"logType": "detail", "children": [], "durationId": "01a20973-9a08-4445-8a3f-610568791ea0"}}, {"head": {"id": "5bb677b2-7743-4a8c-8c9f-7f47fe611fe7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041684663800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e07c9f0-aa06-40bc-bcd4-debf263af78f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041684850300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48ca3958-d8d0-462f-a31d-193066305701", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041686404900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4759b956-40dc-457c-94ad-f74f82830a32", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041691917500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b53ff5de-ae44-48d6-87ff-d30639e311d2", "name": "entry : default@ProcessRouterMap cost memory 0.23520660400390625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041692047700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edde03fd-357a-487d-ac23-4baa5f7b5d3a", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041686394800, "endTime": 145041692104900}, "additional": {"logType": "info", "children": [], "durationId": "01a20973-9a08-4445-8a3f-610568791ea0"}}, {"head": {"id": "b8fea714-56e7-4079-a2f9-012913905132", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041696444200, "endTime": 145041702714500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "2a55830b-c2f2-4cdc-9d03-a5da3941a78d", "logId": "165d7ae4-d0f4-40ab-a200-c8d12534269c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a55830b-c2f2-4cdc-9d03-a5da3941a78d", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041694903800}, "additional": {"logType": "detail", "children": [], "durationId": "b8fea714-56e7-4079-a2f9-012913905132"}}, {"head": {"id": "5d28056c-3d40-42dc-99b2-0394aba9cc1a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041696248400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10c3d0c7-97f1-4af3-8821-6652df5ec30e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041696361100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ed851be-3ce9-4d37-b4d7-3bec3573016c", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041696448800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad5049d6-4d0e-47d9-896b-ae3e5ca2d821", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041696533000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdfd5308-91ad-4952-b879-d8c5d96f21fb", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041700181300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9858f258-c5c3-41b7-90b4-58e40e14c0e7", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041700331600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78f304a8-56df-4cfb-b8ff-2585d85e3204", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041700459100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be5d0bab-f4b5-4d3c-aea2-98683a617ce3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041700555800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68bca53a-d390-4a7d-a0d2-49a516c2f525", "name": "entry : default@ProcessStartupConfig cost memory 0.26143646240234375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041702492400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64718a3b-5f06-4331-914e-2341823f111f", "name": "runTaskFromQueue task cost before running: 295 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041702652000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "165d7ae4-d0f4-40ab-a200-c8d12534269c", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041696444200, "endTime": 145041702714500, "totalTime": 6178900}, "additional": {"logType": "info", "children": [], "durationId": "b8fea714-56e7-4079-a2f9-012913905132"}}, {"head": {"id": "d546e049-dc41-4a4b-9d88-254271445ab5", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041707507700, "endTime": 145041708917000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "77e60e8d-8341-49eb-b745-f9a4b17404af", "logId": "a5b229a4-bd60-45ec-b2db-216dfda63a82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77e60e8d-8341-49eb-b745-f9a4b17404af", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041705554100}, "additional": {"logType": "detail", "children": [], "durationId": "d546e049-dc41-4a4b-9d88-254271445ab5"}}, {"head": {"id": "e3e541e2-326a-4bdd-a0e9-5018bdbef20e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041706690500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55cf7421-f7e9-485a-bdfc-9f0287440ccb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041706788400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da762877-5f11-4144-9e00-4138cfc4386f", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041707521000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1189ea4-7a0d-4dd6-b8f7-8cc43e65b63c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041707690000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b57170d5-d155-4a7c-bc8d-3c4f1035ce7e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041707823600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04056973-a1a2-4771-b443-1bf814c9e85b", "name": "entry : default@BuildNativeWithNinja cost memory 0.05853271484375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041708750300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf812bff-8bd5-48c6-94fe-555e7f12300e", "name": "runTaskFromQueue task cost before running: 302 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041708866800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5b229a4-bd60-45ec-b2db-216dfda63a82", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041707507700, "endTime": 145041708917000, "totalTime": 1338700}, "additional": {"logType": "info", "children": [], "durationId": "d546e049-dc41-4a4b-9d88-254271445ab5"}}, {"head": {"id": "0283bf93-2261-4140-979c-3246dded0b2b", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041715866200, "endTime": 145041721026700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c1de4ed2-71b6-483f-b8a4-d075561039af", "logId": "7e22dbd9-f2c0-4cee-be0a-bd85ec5492f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1de4ed2-71b6-483f-b8a4-d075561039af", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041711285700}, "additional": {"logType": "detail", "children": [], "durationId": "0283bf93-2261-4140-979c-3246dded0b2b"}}, {"head": {"id": "8b21f5b1-48ce-4cd1-8ac1-61f09596f8b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041713213400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c583ffd-43bd-460c-acfb-c885eaa71a2e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041713386400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b859e41-6e35-4305-bf64-b2e79be5977c", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041714747800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01aaf7ab-ec0d-4529-8f47-61ff146cf56e", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041717285300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7e12e92-d831-48e4-b4ba-9af39adf01b9", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041719368500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "887bff9e-8f91-4fe5-aac1-c2a35bec2410", "name": "entry : default@ProcessResource cost memory 0.1652679443359375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041719494900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e22dbd9-f2c0-4cee-be0a-bd85ec5492f0", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041715866200, "endTime": 145041721026700}, "additional": {"logType": "info", "children": [], "durationId": "0283bf93-2261-4140-979c-3246dded0b2b"}}, {"head": {"id": "c79d2b72-89aa-4db6-9fdd-df43c7502b8f", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041728247900, "endTime": 145041747885500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "56ec25e4-f14d-4d01-81d0-e00435ec8a05", "logId": "0cce6c04-147f-44dc-9a7a-899cd5c5d4e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56ec25e4-f14d-4d01-81d0-e00435ec8a05", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041724176700}, "additional": {"logType": "detail", "children": [], "durationId": "c79d2b72-89aa-4db6-9fdd-df43c7502b8f"}}, {"head": {"id": "38661534-e09e-491f-97e3-20a14e158083", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041725174500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdb307fe-d0ab-44a2-9627-367ad2c5c993", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041725278400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86d436d4-23f1-4a31-9a5e-12d2ab444e36", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041728260800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2db0eb15-ddac-4a0b-b12e-ec42fa199ad6", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041747666400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f43452ae-ceee-4f1d-8e01-8e362ba1e8fb", "name": "entry : default@GenerateLoaderJson cost memory 0.9102249145507812", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041747823000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cce6c04-147f-44dc-9a7a-899cd5c5d4e4", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041728247900, "endTime": 145041747885500}, "additional": {"logType": "info", "children": [], "durationId": "c79d2b72-89aa-4db6-9fdd-df43c7502b8f"}}, {"head": {"id": "19ed5e1a-6d4f-49e0-b03d-2ad54afce1b5", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041758504300, "endTime": 145041762483800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "227226c6-caac-4602-a57c-49100015c274", "logId": "7a7bf01b-a744-46c0-82ae-e4a0cdfbb06b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "227226c6-caac-4602-a57c-49100015c274", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041756310800}, "additional": {"logType": "detail", "children": [], "durationId": "19ed5e1a-6d4f-49e0-b03d-2ad54afce1b5"}}, {"head": {"id": "a92533e1-b124-4812-b0f9-946e3461b772", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041757692400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dc50ea0-419e-4218-9b18-c5c2f2c6f3a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041757789700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14125278-61f0-4f75-aeda-1e2e4120de59", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041758514100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcb3c19c-323d-49d5-994d-fa8b54a9b899", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041762297200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b10243a-45cf-475d-963d-43940e4166a2", "name": "entry : default@ProcessLibs cost memory 0.14306640625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041762416400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a7bf01b-a744-46c0-82ae-e4a0cdfbb06b", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041758504300, "endTime": 145041762483800}, "additional": {"logType": "info", "children": [], "durationId": "19ed5e1a-6d4f-49e0-b03d-2ad54afce1b5"}}, {"head": {"id": "2c3a8a1b-7c7b-4148-9166-d4ff074bacbb", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041769229300, "endTime": 145041795539900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5eb150ab-e578-4029-93c7-abee544a820e", "logId": "254e21e4-3427-4c94-b734-e60b54a8a8f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5eb150ab-e578-4029-93c7-abee544a820e", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041764301900}, "additional": {"logType": "detail", "children": [], "durationId": "2c3a8a1b-7c7b-4148-9166-d4ff074bacbb"}}, {"head": {"id": "3a12d950-fd98-4146-8767-0f679909ed20", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041765193900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "671453b6-fde9-43f5-b2b5-9e634dbbfccd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041765275600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7f043d5-08d9-4247-9cf6-caba04e6134f", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041766225500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2068c4b4-f2c5-4f8b-b5a1-3cfa7037f976", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041769260500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab837818-1f1a-4eea-a782-d8d645ec1e28", "name": "Incremental task entry:default@CompileResource pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041795236400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ee0b202-c59b-445d-8e29-8ef90e063448", "name": "entry : default@CompileResource cost memory 1.319488525390625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041795439700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "254e21e4-3427-4c94-b734-e60b54a8a8f7", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041769229300, "endTime": 145041795539900}, "additional": {"logType": "info", "children": [], "durationId": "2c3a8a1b-7c7b-4148-9166-d4ff074bacbb"}}, {"head": {"id": "da19cfd1-b08d-41bb-bad2-4b2c5b0cb0ab", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041803154600, "endTime": 145041805182100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "1c8ede3b-a07f-4511-a21b-fc6c3cfe9d0d", "logId": "fd7a02bc-27c6-479a-963b-d3027f2e5c80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c8ede3b-a07f-4511-a21b-fc6c3cfe9d0d", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041798860400}, "additional": {"logType": "detail", "children": [], "durationId": "da19cfd1-b08d-41bb-bad2-4b2c5b0cb0ab"}}, {"head": {"id": "f43285f5-6bf5-407f-9b5c-68bd1e5f9681", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041800206100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e97f229-9501-46b7-acba-f6ca8686fc5a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041800301400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca01c545-9d40-425b-ba50-ee7e9b37be51", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041803168900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a15dbf60-ff1e-4c70-8d38-519db6395b4f", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041803663000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49adec8b-c90d-44fc-89c1-c8c0592cbc2a", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041805029800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3070ea1-cdd7-4322-b068-451394258fc4", "name": "entry : default@DoNativeStrip cost memory 0.08003997802734375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041805128700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd7a02bc-27c6-479a-963b-d3027f2e5c80", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041803154600, "endTime": 145041805182100}, "additional": {"logType": "info", "children": [], "durationId": "da19cfd1-b08d-41bb-bad2-4b2c5b0cb0ab"}}, {"head": {"id": "93e5f1b8-d998-4c85-98de-94abd551ff13", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041810568200, "endTime": 145041838231300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "564148dd-7c5c-41a7-800d-14aea2df142c", "logId": "bf0215bf-413b-4530-8279-ef5dfc7d8a4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "564148dd-7c5c-41a7-800d-14aea2df142c", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041806512300}, "additional": {"logType": "detail", "children": [], "durationId": "93e5f1b8-d998-4c85-98de-94abd551ff13"}}, {"head": {"id": "5b25d5fc-1b83-4ed0-98b7-068b0dbce624", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041807357700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fec5be9-2f7a-445d-9b59-5716ea2213ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041807442300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "661d6285-3c0b-4992-9dbf-c48b7ef97d5e", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041810581700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc990bb9-9dfd-4a66-a2ea-05cc520d1ab9", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041810733700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c74998e0-e661-4832-ae37-499e5df97f68", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041837950700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be4eacab-166b-4fd7-8d56-6e6f64993ac5", "name": "entry : default@CompileArkTS cost memory 1.1752777099609375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041838149100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf0215bf-413b-4530-8279-ef5dfc7d8a4f", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041810568200, "endTime": 145041838231300}, "additional": {"logType": "info", "children": [], "durationId": "93e5f1b8-d998-4c85-98de-94abd551ff13"}}, {"head": {"id": "59639660-c6ac-435c-a7a4-1a4da7296fd0", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041850496100, "endTime": 145041862902400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "e9afabf0-712a-4c0a-be4b-31251d50c4b8", "logId": "c9afbe88-abf9-48b2-923d-7c3c85d3c148"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9afabf0-712a-4c0a-be4b-31251d50c4b8", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041844837300}, "additional": {"logType": "detail", "children": [], "durationId": "59639660-c6ac-435c-a7a4-1a4da7296fd0"}}, {"head": {"id": "17d6c63e-6092-4cde-aa95-c2fac9c7e59d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041845773700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f49827c-d6b1-4e50-925c-b0e1f0a25bd8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041845890200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adf05eab-508c-4591-ae6a-3c44b5bcd065", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041850509300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3466fb2-5187-49e8-ae85-dde379e42819", "name": "entry : default@BuildJS cost memory -10.37481689453125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041862674400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e85ebfc2-cf40-4958-b28b-996d8f4461e1", "name": "runTaskFromQueue task cost before running: 456 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041862844800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9afbe88-abf9-48b2-923d-7c3c85d3c148", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041850496100, "endTime": 145041862902400, "totalTime": 12319600}, "additional": {"logType": "info", "children": [], "durationId": "59639660-c6ac-435c-a7a4-1a4da7296fd0"}}, {"head": {"id": "d0096444-e5b1-4ce0-a190-4c0a11c5d6f5", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041869207800, "endTime": 145041872938900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7f2dc22d-b105-4611-ba39-6c0c5ff43d1a", "logId": "f50a2e5e-bd28-4596-bf59-5da1c3f7bc11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f2dc22d-b105-4611-ba39-6c0c5ff43d1a", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041864978800}, "additional": {"logType": "detail", "children": [], "durationId": "d0096444-e5b1-4ce0-a190-4c0a11c5d6f5"}}, {"head": {"id": "34922e83-f84f-4650-9b72-c37b18893071", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041866312500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2265b85-4145-4008-9ba6-922bed167a54", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041866571800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e85696aa-9d98-4f78-b681-c1c12aff7b86", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041869222200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "041be955-5fc5-4fce-8978-751e7767ff0a", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041869984500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9238ee1-b88f-4997-b5bc-aa90b4fe5b25", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041872686400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82861edf-1ba1-4017-8e94-d9787f5b35c6", "name": "entry : default@CacheNativeLibs cost memory 0.0950775146484375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041872859000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f50a2e5e-bd28-4596-bf59-5da1c3f7bc11", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041869207800, "endTime": 145041872938900}, "additional": {"logType": "info", "children": [], "durationId": "d0096444-e5b1-4ce0-a190-4c0a11c5d6f5"}}, {"head": {"id": "c07b10f0-ed1c-4472-a390-16307c3489f3", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041876941600, "endTime": 145041878218500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "22d3b348-acd2-415d-937d-7403606711fa", "logId": "abacfacf-eb41-48cb-97b6-628d6a7bbf9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22d3b348-acd2-415d-937d-7403606711fa", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041874726400}, "additional": {"logType": "detail", "children": [], "durationId": "c07b10f0-ed1c-4472-a390-16307c3489f3"}}, {"head": {"id": "7972d6bb-1bb6-452f-a527-311ec83cadb1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041875864800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6b21cd5-1877-44ac-9697-cab088688436", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041875970000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea1d544a-584b-459d-8107-fecbd9780fbf", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041876952500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "612de621-a25d-4fdf-9bb9-e1e3f7d09499", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041877235800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bd0abed-571b-4b0a-a59b-0b28bdeb3193", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041878069000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a1d9114-d178-4df6-abb3-b5d04ee8d727", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07520294189453125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041878167200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abacfacf-eb41-48cb-97b6-628d6a7bbf9e", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041876941600, "endTime": 145041878218500}, "additional": {"logType": "info", "children": [], "durationId": "c07b10f0-ed1c-4472-a390-16307c3489f3"}}, {"head": {"id": "39f4c891-7987-416c-b4f4-8c40b6e4e5fa", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041889514400, "endTime": 145041908284800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "0cd5a01d-9802-44d1-a9cd-0d68403d19a1", "logId": "fde4048e-d40e-472a-808b-e9a98d4822f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0cd5a01d-9802-44d1-a9cd-0d68403d19a1", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041880400800}, "additional": {"logType": "detail", "children": [], "durationId": "39f4c891-7987-416c-b4f4-8c40b6e4e5fa"}}, {"head": {"id": "861ed952-94a1-4621-a5ce-d4a0eecd8428", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041881761300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "046ecdc8-92b7-4402-99b9-ded45f4863d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041881898100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e670ddc7-262a-49c3-86fb-9aed2c1a8b5d", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041889530400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1768af67-e7d5-497f-8dfa-14f3d7604ba9", "name": "Incremental task entry:default@PackageHap pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041908069400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebd4cec4-4774-498e-ad37-323217290ad4", "name": "entry : default@PackageHap cost memory 1.1435089111328125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041908225600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fde4048e-d40e-472a-808b-e9a98d4822f3", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041889514400, "endTime": 145041908284800}, "additional": {"logType": "info", "children": [], "durationId": "39f4c891-7987-416c-b4f4-8c40b6e4e5fa"}}, {"head": {"id": "33ba4c43-a019-44bc-9c03-a6122f32c7ad", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041914681600, "endTime": 145041917022200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": [], "detailId": "8cf82b38-3334-45d4-9096-1f754eb2e0a7", "logId": "2669153a-dd37-4069-bbc5-d0755b91c09d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8cf82b38-3334-45d4-9096-1f754eb2e0a7", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041911634400}, "additional": {"logType": "detail", "children": [], "durationId": "33ba4c43-a019-44bc-9c03-a6122f32c7ad"}}, {"head": {"id": "8f5bdee7-3701-4b81-9cfc-183f67492f60", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041912653000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "106954df-14de-414f-8e6b-95724276c57f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041912744200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4b755ad-4d23-4061-9572-90fac8ce06af", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041914693000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a278b46-519d-422c-af03-c4309742c2a5", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041916862800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79b6ce8d-e352-450a-ac12-3cd5b65bdea9", "name": "entry : default@SignHap cost memory 0.10597991943359375", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041916967800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2669153a-dd37-4069-bbc5-d0755b91c09d", "name": "UP-TO-DATE :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041914681600, "endTime": 145041917022200}, "additional": {"logType": "info", "children": [], "durationId": "33ba4c43-a019-44bc-9c03-a6122f32c7ad"}}, {"head": {"id": "159f528a-1b4b-4e02-9de4-03b64affe890", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041920231700, "endTime": 145041925188700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3c28ba98-5a2e-4a21-9043-46ba444fdafa", "logId": "118f8a6c-7fde-4ab9-ac91-e0063092f0c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c28ba98-5a2e-4a21-9043-46ba444fdafa", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041918643000}, "additional": {"logType": "detail", "children": [], "durationId": "159f528a-1b4b-4e02-9de4-03b64affe890"}}, {"head": {"id": "c6a7eb53-56b5-48b2-aa84-4a3a2e86cb58", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041919495200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6ab2578-8e2b-4248-85cb-358598c21120", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041919586800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcf298f7-d758-4b0c-8b8f-fd899c76befe", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041920240100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab2a90ae-e329-49cd-b9a5-299bef1a0930", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041924972200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6662a4ea-6f7a-4f1b-bb9b-7e6d0a715709", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041925042900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "681a5178-5358-4b00-a183-1885fe9151a9", "name": "entry : default@CollectDebugSymbol cost memory 0.244140625", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041925099100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e52e1aaf-504c-4aee-8a86-81bc5249af96", "name": "runTaskFromQueue task cost before running: 518 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041925158100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "118f8a6c-7fde-4ab9-ac91-e0063092f0c9", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041920231700, "endTime": 145041925188700, "totalTime": 4910600}, "additional": {"logType": "info", "children": [], "durationId": "159f528a-1b4b-4e02-9de4-03b64affe890"}}, {"head": {"id": "93c5b755-08db-46c9-98c8-d34ac887480c", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041926352500, "endTime": 145041926533800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "fff3d6c8-f044-4de2-a471-31279ee1ae9e", "logId": "7c019126-9eec-4fae-83b9-f152621bb06b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fff3d6c8-f044-4de2-a471-31279ee1ae9e", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041926324800}, "additional": {"logType": "detail", "children": [], "durationId": "93c5b755-08db-46c9-98c8-d34ac887480c"}}, {"head": {"id": "c7db531c-cc2c-4b72-8fe6-114af1d7f161", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041926359000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fd87096-20ed-4522-80fa-b0d89b232636", "name": "entry : assembleHap cost memory 0.011749267578125", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041926443300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8bf9827-9dfc-4ef1-8ee9-04f3df13d29a", "name": "runTaskFromQueue task cost before running: 519 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041926498700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c019126-9eec-4fae-83b9-f152621bb06b", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041926352500, "endTime": 145041926533800, "totalTime": 131800}, "additional": {"logType": "info", "children": [], "durationId": "93c5b755-08db-46c9-98c8-d34ac887480c"}}, {"head": {"id": "87282b40-419c-410c-ad49-1da22aed679a", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041937744200, "endTime": 145041937768200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e201d055-9b6c-4ba1-bc0c-e760f4646aa4", "logId": "196573d7-09a0-474f-a89f-4f57e7c8280c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "196573d7-09a0-474f-a89f-4f57e7c8280c", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041937744200, "endTime": 145041937768200}, "additional": {"logType": "info", "children": [], "durationId": "87282b40-419c-410c-ad49-1da22aed679a"}}, {"head": {"id": "24cc0475-0db1-4dfb-8c18-3f934a669eee", "name": "BUILD SUCCESSFUL in 531 ms ", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041937810700}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "9598d26f-2ade-480f-9c46-df99d9af5ad6", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041407690400, "endTime": 145041938013200}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 22, "minute": 14, "second": 31}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "30b70bc5-02ae-49da-9dc6-8c0c8255c03a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041938032800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76a8d79a-c5e6-4e1d-98fa-75310dc25afe", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041938086000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66a302cc-d4c1-43ae-8d8c-798c67d06026", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041938424600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0bab6ac-e140-443b-9276-28f2ca9cf358", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041938479400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f42b183e-5d0d-4a52-b935-d2721d5cfa6d", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041938505500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74d9d6c2-0cb7-49c3-92d7-f371bd6fcbcf", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041938528100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40a79af9-01c5-4538-9037-283978acb193", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041938551500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0784b37d-43cc-46bb-9c69-aecea2e2c5ed", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041939002900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a44c357d-868a-4aab-b2ba-39dab6f751d8", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041939183800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d46705ce-1d53-4c98-b85c-f431e1a80a9d", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041939231000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a126a7bc-a5f0-4a57-9f7c-b1c6fd274ab0", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041939257200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c07915af-93b8-45b8-9af9-2573012ac72d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041939278400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3336b77-6d83-4e01-af41-8753c8ed5dbb", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041939299500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d5b9f57-6cba-4269-a4b8-68e70bcc52ba", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041940147400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec604175-8d6b-4d76-964b-886a5062a0b7", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041940403500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38fe9c8a-2e75-4968-8a18-3b5d71852f8a", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041940564900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46f91369-9c7e-4126-809b-4e3e546d29fb", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041940612200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fae48e8-016e-40cd-80bf-c658061fe6c9", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041940637600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68921b5d-af15-4de8-8af8-9f45a9b9d753", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041940659800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "056846cd-177d-4fe6-8269-359d5c1e0d52", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041940683600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6175a8d7-a3c2-4a9e-8c23-7c14db8fefe1", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041940705000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c5e31c8-e58b-420d-8ee4-4ea39c0dcd06", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041940725700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d14b609-5d7d-4999-836d-538a91290efe", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041942334600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6aab9bcd-1c6d-478f-b3f9-5a14a24a2262", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041942868000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a562ec87-c425-4807-bc8a-828e16a08ab1", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041943263200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1ca7f51-5994-4bfe-80f4-fcc52429ca35", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041943559600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6c5c868-b8b6-4372-b37e-ae93bc771aa3", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041943774500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea722620-81e4-4905-8b2c-c7fbd2f837c6", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041944420100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a84a01f9-6720-482f-b14c-9b0e2b5ff111", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041945155300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d74007c5-ab0b-44fd-9184-8736fb261e55", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041945373800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2787399f-ff51-49f4-b240-00f1f3ec0725", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041945428100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dc82003-4809-4c19-8237-9fcc6c88c1e6", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041945459600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52314632-f603-4185-a053-d8d080261014", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041945505400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78f5b828-c86f-4286-ac94-c309442f611f", "name": "There is no need to refresh cache, since the incremental task entry:default@SignHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041945531100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f1bd0c2-aca0-4189-a5fd-d076fe60984b", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041947686300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90913fc5-3ba2-450b-92ff-c806ac2a2986", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041947897300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57fbb45b-e79d-479f-8ee8-28cc54ade7aa", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041948293600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32440686-e9c5-48a5-b45f-8e004bcded32", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 37728, "tid": "Main Thread", "startTime": 145041948489400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}