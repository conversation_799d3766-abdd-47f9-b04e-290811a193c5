import React, { useState, useRef } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Modal,
  Form,
  message,
  Popconfirm,
  Tag,
  Select,
  DatePicker,
  Switch,
  Tooltip,
  Avatar,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  StarOutlined,
  AppstoreOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import moment from 'moment';
import {
  getFeaturedCollectionList,
  createFeaturedCollection,
  updateFeaturedCollection,
  deleteFeaturedCollection,
  type FeaturedCollection,
  type CreateFeaturedCollectionRequest,
} from '@/services/featuredCollection';
import { getAppList } from '@/services/app';

const { Option } = Select;
const { TextArea } = Input;

// 应用数据接口
interface AppItem {
  id: number;
  name: string;
  icon: string;
  package: string;
  category: string;
  status: string;
}

const FeaturedCollectionManagement: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<FeaturedCollection | null>(null);
  const [appSelectModalVisible, setAppSelectModalVisible] = useState(false);
  const [selectedApps, setSelectedApps] = useState<AppItem[]>([]);
  const [form] = Form.useForm();
  const actionRef = useRef<ActionType>();

  // 获取精选集列表的请求函数
  const fetchFeaturedCollections = async (params: any) => {
    try {
      const response = await getFeaturedCollectionList({
        page: params.current || 1,
        page_size: params.pageSize || 10,
        ...(params.keyword && { keyword: params.keyword }),
        ...(params.is_active !== undefined && { is_active: params.is_active }),
      });
      
      if (response && response.code === 200) {
        const responseData = response.data || {};
        return {
          data: responseData.items || [],
          total: responseData.total || 0,
          success: true,
        };
      }
      
      return {
        data: [],
        total: 0,
        success: false,
      };
    } catch (error) {
      message.error('获取精选集列表失败');
      return {
        data: [],
        total: 0,
        success: false,
      };
    }
  };

  // 获取应用列表的请求函数
  const fetchApps = async (params: any) => {
    try {
      const response = await getAppList({
        page: params.current || 1,
        pageSize: params.pageSize || 10,
        status: 'approved',
        ...(params.keyword && { keyword: params.keyword }),
      });
      
      if (response && response.code === 200) {
        return {
          data: response.data || [],
          total: response.total || 0,
          success: true,
        };
      }
      
      return {
        data: [],
        total: 0,
        success: false,
      };
    } catch (error) {
      message.error('获取应用列表失败');
      return {
        data: [],
        total: 0,
        success: false,
      };
    }
  };

  // 表格列定义
  const columns: ProColumns<FeaturedCollection>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '精选集名称',
      dataIndex: 'name',
      ellipsis: true,
      render: (text, record) => (
        <Space>
          <StarOutlined style={{ color: '#faad14' }} />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: true,
      search: false,
      width: 200,
    },
    {
      title: '应用数量',
      dataIndex: 'app_count',
      width: 100,
      search: false,
      render: (count) => (
        <Badge count={count} style={{ backgroundColor: '#52c41a' }} />
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      width: 100,
      valueType: 'select',
      valueEnum: {
        true: { text: '启用', status: 'Success' },
        false: { text: '禁用', status: 'Default' },
      },
      render: (_, record) => (
        <Tag color={record.is_active ? 'green' : 'default'}>
          {record.is_active ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '显示顺序',
      dataIndex: 'display_order',
      width: 100,
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      width: 180,
      search: false,
      render: (text) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      render: (_, record) => [
        <Tooltip key="view" title="查看应用">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewApps(record)}
          />
        </Tooltip>,
        <Tooltip key="edit" title="编辑">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
        </Tooltip>,
        <Popconfirm
          key="delete"
          title="确定要删除这个精选集吗？"
          onConfirm={() => handleDelete(record.id)}
          okText="确定"
          cancelText="取消"
        >
          <Tooltip title="删除">
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            />
          </Tooltip>
        </Popconfirm>,
      ],
    },
  ];

  // 应用选择表格列定义
  const appColumns: ColumnsType<AppItem> = [
    {
      title: '应用图标',
      dataIndex: 'icon',
      width: 80,
      render: (icon, record) => (
        <Avatar
          src={icon}
          icon={<AppstoreOutlined />}
          alt={record.name}
        />
      ),
    },
    {
      title: '应用名称',
      dataIndex: 'name',
      ellipsis: true,
    },
    {
      title: '包名',
      dataIndex: 'package',
      ellipsis: true,
    },
    {
      title: '分类',
      dataIndex: 'category',
      width: 120,
    },
  ];

  // 处理新建
  const handleCreate = () => {
    setEditingRecord(null);
    setSelectedApps([]);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑
  const handleEdit = (record: FeaturedCollection) => {
    setEditingRecord(record);
    setSelectedApps(record.apps || []);
    form.setFieldsValue({
      ...record,
      app_ids: record.apps?.map(app => app.id) || [],
    });
    setModalVisible(true);
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    try {
      const result = await deleteFeaturedCollection(id);
      if (result.code === 200) {
        message.success('删除成功');
        actionRef.current?.reload();
      } else {
        message.error(result.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败，请稍后重试');
    }
  };

  // 处理查看应用
  const handleViewApps = (record: FeaturedCollection) => {
    Modal.info({
      title: `${record.name} - 包含的应用`,
      width: 800,
      content: (
        <div style={{ marginTop: 16 }}>
          {record.apps && record.apps.length > 0 ? (
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))', gap: 16 }}>
              {record.apps.map(app => (
                <Card key={app.id} size="small">
                  <Card.Meta
                    avatar={<Avatar src={app.icon} icon={<AppstoreOutlined />} />}
                    title={app.name}
                    description={app.package}
                  />
                </Card>
              ))}
            </div>
          ) : (
            <div style={{ textAlign: 'center', color: '#999' }}>
              暂无应用
            </div>
          )}
        </div>
      ),
    });
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const submitData: CreateFeaturedCollectionRequest = {
        title: values.title,
        description: values.description,
        icon: values.icon || '',
        cover_image: values.cover_image || '',
        sort_order: parseInt(values.sort_order) || 0,
        is_active: values.is_active,
        is_public: values.is_public,
      };

      let result;
      if (editingRecord) {
        result = await updateFeaturedCollection(editingRecord.id, submitData);
      } else {
        result = await createFeaturedCollection(submitData);
      }

      if (result.code === 200) {
        message.success(editingRecord ? '更新成功' : '创建成功');
        setModalVisible(false);
        actionRef.current?.reload();
      } else {
        message.error(result.message || '操作失败');
      }
    } catch (error) {
      message.error('操作失败，请稍后重试');
    }
  };

  // 处理应用选择
  const handleAppSelect = () => {
    setAppSelectModalVisible(true);
  };

  // 应用选择确认
  const handleAppSelectConfirm = (apps: AppItem[]) => {
    setSelectedApps(apps);
    form.setFieldValue('app_ids', apps.map(app => app.id));
    setAppSelectModalVisible(false);
  };

  return (
    <div>
      <Card>
        <ProTable<FeaturedCollection>
          headerTitle="精选集管理"
          actionRef={actionRef}
          rowKey="id"
          search={{
            labelWidth: 'auto',
          }}
          toolBarRender={() => [
            <Button
              key="create"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
            >
              新建精选集
            </Button>,
          ]}
          request={fetchFeaturedCollections}
          columns={columns}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Card>

      {/* 新建/编辑精选集弹窗 */}
      <Modal
        title={editingRecord ? '编辑精选集' : '新建精选集'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            is_active: true,
            is_public: true,
            sort_order: 0,
          }}
        >
          <Form.Item
            name="title"
            label="精选集名称"
            rules={[{ required: true, message: '请输入精选集名称' }]}
          >
            <Input placeholder="请输入精选集名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <TextArea rows={3} placeholder="请输入精选集描述" />
          </Form.Item>

          <Form.Item label="选择应用">
            <div>
              <Button
                type="dashed"
                icon={<PlusOutlined />}
                onClick={handleAppSelect}
                style={{ marginBottom: 8 }}
              >
                选择应用 ({selectedApps.length})
              </Button>
              {selectedApps.length > 0 && (
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                  {selectedApps.map(app => (
                    <Tag
                      key={app.id}
                      closable
                      onClose={() => {
                        const newApps = selectedApps.filter(a => a.id !== app.id);
                        setSelectedApps(newApps);
                        form.setFieldValue('app_ids', newApps.map(a => a.id));
                      }}
                    >
                      <Avatar size="small" src={app.icon} style={{ marginRight: 4 }} />
                      {app.name}
                    </Tag>
                  ))}
                </div>
              )}
            </div>
          </Form.Item>

          <Form.Item
            name="sort_order"
            label="显示顺序"
            rules={[{ required: true, message: '请输入显示顺序' }]}
          >
            <Input type="number" placeholder="数字越小越靠前" />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item
            name="is_public"
            label="公开状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="公开" unCheckedChildren="私有" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 应用选择弹窗 */}
      <Modal
        title="选择应用"
        open={appSelectModalVisible}
        onCancel={() => setAppSelectModalVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setAppSelectModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="confirm"
            type="primary"
            onClick={() => {
              // 这里应该获取选中的应用并确认
              setAppSelectModalVisible(false);
            }}
          >
            确定
          </Button>,
        ]}
      >
        <ProTable<AppItem>
          rowKey="id"
          search={{
            labelWidth: 'auto',
          }}
          request={fetchApps}
          columns={[
            {
              title: '应用图标',
              dataIndex: 'icon',
              width: 80,
              search: false,
              render: (icon, record) => (
                <Avatar
                  src={icon}
                  icon={<AppstoreOutlined />}
                  alt={record.name}
                />
              ),
            },
            {
              title: '应用名称',
              dataIndex: 'name',
              ellipsis: true,
            },
            {
              title: '包名',
              dataIndex: 'package',
              ellipsis: true,
              search: false,
            },
            {
              title: '分类',
              dataIndex: 'category',
              width: 120,
              search: false,
            },
          ]}
          rowSelection={{
            selectedRowKeys: selectedApps.map(app => app.id),
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedApps(selectedRows);
            },
          }}
          pagination={{
            defaultPageSize: 5,
            showSizeChanger: false,
          }}
        />
      </Modal>
    </div>
  );
};

export default FeaturedCollectionManagement;