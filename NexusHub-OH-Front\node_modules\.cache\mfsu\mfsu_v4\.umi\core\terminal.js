"use strict";
let count = 0;
let groupLevel = 0;
function send(type, message) {
  if (false) {
    return;
  } else {
    const encodedMessage = message ? `&m=${encodeURI(message)}` : "";
    fetch(`/__umi/api/terminal?type=${type}&t=${Date.now()}&c=${count++}&g=${groupLevel}${encodedMessage}`, { mode: "no-cors" });
  }
}
function prettyPrint(obj) {
  return JSON.stringify(obj, null, 2);
}
function stringifyObjs(objs) {
  const obj = objs.length > 1 ? objs.map(stringify).join(" ") : objs[0];
  return typeof obj === "object" ? `${prettyPrint(obj)}` : obj.toString();
}
function stringify(obj) {
  return typeof obj === "object" ? `${JSON.stringify(obj)}` : obj.toString();
}
const terminal = {
  log(...objs) {
    send("log", stringifyObjs(objs));
  },
  info(...objs) {
    send("info", stringifyObjs(objs));
  },
  warn(...objs) {
    send("warn", stringifyObjs(objs));
  },
  error(...objs) {
    send("error", stringifyObjs(objs));
  },
  group() {
    groupLevel++;
  },
  groupCollapsed() {
    groupLevel++;
  },
  groupEnd() {
    groupLevel && --groupLevel;
  },
  clear() {
    send("clear");
  },
  trace(...args) {
    console.trace(...args);
  },
  profile(...args) {
    console.profile(...args);
  },
  profileEnd(...args) {
    console.profileEnd(...args);
  }
};
export { terminal };
