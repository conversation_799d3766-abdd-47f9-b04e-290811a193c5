"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { EllipsisOutlined } from "@ant-design/icons";
import { GridContent } from "@ant-design/pro-components";
import { Col, Dropdown, Row } from "antd";
import { Suspense, useState, useEffect } from "react";
import IntroduceRow from "./components/IntroduceRow";
import OfflineData from "./components/OfflineData";
import PageLoading from "./components/PageLoading";
import ProportionSales from "./components/ProportionSales";
import SalesCard from "./components/SalesCard";
import TopSearch from "./components/TopSearch";
import { getSummaryData, getTrendData, getCategoriesData, getPopularApps } from "./service";
import useStyles from "./style.style";
import { getTimeDistance } from "./utils/utils";
const Analysis = () => {
  const { styles } = useStyles();
  const [salesType, setSalesType] = useState("all");
  const [currentTabKey, setCurrentTabKey] = useState("");
  const [rangePickerValue, setRangePickerValue] = useState(
    getTimeDistance("year")
  );
  const [analyticsData, setAnalyticsData] = useState({});
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [summaryResult, trendResult, categoriesResult, appsResult] = await Promise.all([
          getSummaryData(),
          getTrendData(),
          getCategoriesData(),
          getPopularApps()
        ]);
        const categoryData = categoriesResult.data || [];
        const salesTypeData = categoryData.map((item) => ({
          x: item.category_name,
          y: item.app_count
        }));
        const downloadTypeData = categoryData.map((item) => ({
          x: item.category_name,
          y: item.download_count
        }));
        const combinedSummaryData = {
          ...summaryResult.data,
          ...trendResult.data
        };
        setAnalyticsData({
          // 摘要数据
          summaryData: combinedSummaryData,
          // 趋势数据
          visitData: trendResult.data?.user_trend || [],
          salesData: trendResult.data?.download_trend || [],
          // 热门应用数据 - 直接使用API返回的数据结构
          searchData: appsResult.data || [],
          // 分类数据
          offlineData: categoryData || [],
          salesTypeData: salesTypeData || [],
          downloadTypeData: downloadTypeData || []
        });
      } catch (error) {
        console.error("\u83B7\u53D6\u5206\u6790\u6570\u636E\u5931\u8D25:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);
  const selectDate = (type) => {
    setRangePickerValue(getTimeDistance(type));
  };
  const handleRangePickerChange = (value) => {
    setRangePickerValue(value);
  };
  const isActive = (type) => {
    if (!rangePickerValue) {
      return "";
    }
    const value = getTimeDistance(type);
    if (!value) {
      return "";
    }
    if (!rangePickerValue[0] || !rangePickerValue[1]) {
      return "";
    }
    if (rangePickerValue[0].isSame(value[0], "day") && rangePickerValue[1].isSame(value[1], "day")) {
      return styles.currentDate;
    }
    return "";
  };
  let salesPieData = salesType === "all" ? analyticsData.salesTypeData || [] : analyticsData.downloadTypeData || [];
  const dropdownGroup = /* @__PURE__ */ jsx("span", { className: styles.iconGroup, children: /* @__PURE__ */ jsx(
    Dropdown,
    {
      menu: {
        items: [
          {
            key: "1",
            label: "\u5BFC\u51FA\u6570\u636E"
          },
          {
            key: "2",
            label: "\u5237\u65B0"
          }
        ]
      },
      placement: "bottomRight",
      children: /* @__PURE__ */ jsx(EllipsisOutlined, {})
    }
  ) });
  const handleChangeSalesType = (e) => {
    setSalesType(e.target.value);
  };
  const handleTabChange = (key) => {
    setCurrentTabKey(key);
  };
  const activeKey = currentTabKey || analyticsData?.offlineData?.[0] && analyticsData?.offlineData[0].category_name || "";
  return /* @__PURE__ */ jsx(GridContent, { children: /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(Suspense, { fallback: /* @__PURE__ */ jsx(PageLoading, {}), children: /* @__PURE__ */ jsx(IntroduceRow, { loading, summaryData: analyticsData.summaryData }) }),
    /* @__PURE__ */ jsx(Suspense, { fallback: null, children: /* @__PURE__ */ jsx(
      SalesCard,
      {
        rangePickerValue,
        salesData: analyticsData?.salesData || [],
        userData: analyticsData?.visitData || [],
        isActive,
        handleRangePickerChange,
        loading,
        selectDate
      }
    ) }),
    /* @__PURE__ */ jsxs(
      Row,
      {
        gutter: 24,
        style: {
          marginTop: 24
        },
        children: [
          /* @__PURE__ */ jsx(Col, { xl: 12, lg: 24, md: 24, sm: 24, xs: 24, children: /* @__PURE__ */ jsx(Suspense, { fallback: null, children: /* @__PURE__ */ jsx(
            TopSearch,
            {
              loading,
              visitData2: analyticsData?.visitData || [],
              searchData: analyticsData?.searchData || [],
              dropdownGroup
            }
          ) }) }),
          /* @__PURE__ */ jsx(Col, { xl: 12, lg: 24, md: 24, sm: 24, xs: 24, children: /* @__PURE__ */ jsx(Suspense, { fallback: null, children: /* @__PURE__ */ jsx(
            ProportionSales,
            {
              dropdownGroup,
              salesType,
              loading,
              salesPieData: salesPieData || [],
              handleChangeSalesType
            }
          ) }) })
        ]
      }
    ),
    /* @__PURE__ */ jsx(Suspense, { fallback: null, children: /* @__PURE__ */ jsx(
      OfflineData,
      {
        activeKey,
        loading,
        offlineData: analyticsData?.offlineData || [],
        offlineChartData: analyticsData?.offlineChartData || [],
        handleTabChange
      }
    ) })
  ] }) });
};
export default Analysis;
