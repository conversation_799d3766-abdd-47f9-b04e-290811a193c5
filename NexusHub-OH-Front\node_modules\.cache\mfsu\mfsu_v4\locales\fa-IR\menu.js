"use strict";
export default {
  "menu.welcome": "\u062E\u0648\u0634 \u0622\u0645\u062F\u06CC\u062F",
  "menu.more-blocks": "\u0628\u0644\u0648\u06A9 \u0647\u0627\u06CC \u0628\u06CC\u0634\u062A\u0631",
  "menu.home": "\u062E\u0627\u0646\u0647",
  "menu.admin": "\u0645\u062F\u06CC\u0631",
  "menu.admin.sub-page": "\u0632\u06CC\u0631 \u0635\u0641\u062D\u0647",
  "menu.login": "\u0648\u0631\u0648\u062F",
  "menu.register": "\u062B\u0628\u062A \u0646\u0627\u0645",
  "menu.register-result": "\u062B\u0628\u062A \u0646\u0627\u0645 \u0646\u062A\u06CC\u062C\u0647",
  "menu.dashboard": "\u062F\u0627\u0634\u0628\u0648\u0631\u062F",
  "menu.dashboard.analysis": "\u062A\u062D\u0644\u06CC\u0644 \u0648 \u0628\u0631\u0631\u0633\u06CC",
  "menu.dashboard.monitor": "\u0646\u0638\u0627\u0631\u062A",
  "menu.dashboard.workplace": "\u0645\u062D\u0644 \u06A9\u0627\u0631",
  "menu.exception.403": "403",
  "menu.exception.404": "404",
  "menu.exception.500": "500",
  "menu.form": "\u0641\u0631\u0645",
  "menu.form.basic-form": "\u0641\u0631\u0645 \u0627\u0633\u0627\u0633\u06CC",
  "menu.form.step-form": "\u0641\u0631\u0645 \u0645\u0631\u062D\u0644\u0647",
  "menu.form.step-form.info": "\u0641\u0631\u0645 \u0645\u0631\u062D\u0644\u0647 (\u0646\u0648\u0634\u062A\u0646 \u0627\u0637\u0644\u0627\u0639\u0627\u062A \u0627\u0646\u062A\u0642\u0627\u0644)",
  "menu.form.step-form.confirm": "\u0641\u0631\u0645 \u0645\u0631\u062D\u0644\u0647 (\u062A\u0623\u06CC\u06CC\u062F \u0627\u0637\u0644\u0627\u0639\u0627\u062A \u0627\u0646\u062A\u0642\u0627\u0644)",
  "menu.form.step-form.result": "\u0641\u0631\u0645 \u0645\u0631\u062D\u0644\u0647 (\u062A\u0645\u0627\u0645 \u0634\u062F\u0647)",
  "menu.form.advanced-form": "\u0641\u0631\u0645 \u067E\u06CC\u0634\u0631\u0641\u062A\u0647",
  "menu.list": "\u0644\u06CC\u0633\u062A",
  "menu.list.table-list": "\u062C\u062F\u0648\u0644 \u062C\u0633\u062A\u062C\u0648",
  "menu.list.basic-list": "\u0644\u06CC\u0633\u062A \u0627\u0635\u0644\u06CC",
  "menu.list.card-list": "\u0644\u06CC\u0633\u062A \u06A9\u0627\u0631\u062A",
  "menu.list.search-list": "\u0644\u06CC\u0633\u062A \u062C\u0633\u062A\u062C\u0648",
  "menu.list.search-list.articles": "\u0644\u06CC\u0633\u062A \u062C\u0633\u062A\u062C\u0648 (\u0645\u0642\u0627\u0644\u0627\u062A)",
  "menu.list.search-list.projects": "\u0644\u06CC\u0633\u062A \u062C\u0633\u062A\u062C\u0648 (\u067E\u0631\u0648\u0698\u0647 \u0647\u0627)",
  "menu.list.search-list.applications": "\u0644\u06CC\u0633\u062A \u062C\u0633\u062A\u062C\u0648 (\u0628\u0631\u0646\u0627\u0645\u0647 \u0647\u0627)",
  "menu.profile": "\u0645\u0634\u062E\u0635\u0627\u062A",
  "menu.profile.basic": "\u0645\u0634\u062E\u0635\u0627\u062A \u0639\u0645\u0648\u0645\u06CC",
  "menu.profile.advanced": "\u0645\u0634\u062E\u0635\u0627\u062A \u067E\u06CC\u0634\u0631\u0641\u062A\u0647",
  "menu.result": "\u0646\u062A\u06CC\u062C\u0647",
  "menu.result.success": "\u0645\u0648\u0641\u0642",
  "menu.result.fail": "\u0646\u0627\u0645\u0648\u0641\u0642",
  "menu.exception": "\u0627\u0633\u062A\u062B\u0646\u0627",
  "menu.exception.not-permission": "403",
  "menu.exception.not-find": "404",
  "menu.exception.server-error": "500",
  "menu.exception.trigger": "\u0631\u0627\u0647 \u0627\u0646\u062F\u0627\u0632\u06CC",
  "menu.account": "\u062D\u0633\u0627\u0628",
  "menu.account.settings": "\u062A\u0646\u0638\u06CC\u0645\u0627\u062A \u062D\u0633\u0627\u0628",
  "menu.account.trigger": "\u062E\u0637\u0627\u06CC \u0631\u0627\u0647 \u0627\u0646\u062F\u0627\u0632\u06CC",
  "menu.account.logout": "\u062E\u0631\u0648\u062C",
  "menu.editor": "\u0648\u06CC\u0631\u0627\u06CC\u0634\u06AF\u0631 \u06AF\u0631\u0627\u0641\u06CC\u06A9",
  "menu.editor.flow": "\u0648\u06CC\u0631\u0627\u06CC\u0634\u06AF\u0631 \u062C\u0631\u06CC\u0627\u0646",
  "menu.editor.mind": "\u0648\u06CC\u0631\u0627\u06CC\u0634\u06AF\u0631 \u0630\u0647\u0646",
  "menu.editor.koni": "\u0648\u06CC\u0631\u0627\u06CC\u0634\u06AF\u0631 Koni"
};
