"use strict";
import component from "./bn-BD/component";
import globalHeader from "./bn-BD/globalHeader";
import menu from "./bn-BD/menu";
import pages from "./bn-BD/pages";
import pwa from "./bn-BD/pwa";
import settingDrawer from "./bn-BD/settingDrawer";
import settings from "./bn-BD/settings";
export default {
  "navBar.lang": "\u09AD\u09BE\u09B7\u09BE",
  "layout.user.link.help": "\u09B8\u09B9\u09BE\u09AF\u09BC\u09A4\u09BE",
  "layout.user.link.privacy": "\u0997\u09CB\u09AA\u09A8\u09C0\u09AF\u09BC\u09A4\u09BE",
  "layout.user.link.terms": "\u09B6\u09B0\u09CD\u09A4\u09BE\u09A6\u09BF",
  "app.preview.down.block": "\u0986\u09AA\u09A8\u09BE\u09B0 \u09B8\u09CD\u09A5\u09BE\u09A8\u09C0\u09AF\u09BC \u09AA\u09CD\u09B0\u0995\u09B2\u09CD\u09AA\u09C7 \u098F\u0987 \u09AA\u09C3\u09B7\u09CD\u09A0\u09BE\u099F\u09BF \u09A1\u09BE\u0989\u09A8\u09B2\u09CB\u09A1 \u0995\u09B0\u09C1\u09A8",
  "app.welcome.link.fetch-blocks": "\u09B8\u09AE\u09B8\u09CD\u09A4 \u09AC\u09CD\u09B2\u0995 \u09AA\u09BE\u09A8",
  "app.welcome.link.block-list": "`block` \u09A1\u09C7\u09AD\u09C7\u09B2\u09AA\u09AE\u09C7\u09A8\u09CD\u099F \u098F\u09B0 \u0989\u09AA\u09B0 \u09AD\u09BF\u09A4\u09CD\u09A4\u09BF \u0995\u09B0\u09C7 \u09A6\u09CD\u09B0\u09C1\u09A4 \u09B8\u09CD\u099F\u09CD\u09AF\u09BE\u09A8\u09CD\u09A1\u09BE\u09B0\u09CD\u09A1, \u09AA\u09C3\u09B7\u09CD\u09A0\u09BE\u09B8\u09AE\u09C2\u09B9 \u09A4\u09C8\u09B0\u09BF \u0995\u09B0\u09C1\u09A8\u0964",
  ...globalHeader,
  ...menu,
  ...settingDrawer,
  ...settings,
  ...pwa,
  ...component,
  ...pages
};
