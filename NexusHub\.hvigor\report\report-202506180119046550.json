{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "c9ce5c96-5fbf-4ba5-a2d2-8d5e9a06e533", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023200365100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b91716f-5696-45c0-9753-0c4d090275c7", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023200600900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a185e08-55af-4a17-9e9c-d6ef9f2ed729", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023201701800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cef6446-7543-40f4-bc69-4d3171166b9b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023205592300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bd65f92-482f-4bce-86f1-1e6a7cca5535", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023206027100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "745783a9-a56f-462c-bda2-9973a78da6da", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023206581100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33077b0f-f8ee-43b2-adf7-6c6ab739e9eb", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023209826800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f2b02f8-0aa0-4c91-bc3f-676f219717f3", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156023265895600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44bccc5f-7f8e-4363-99ee-9fd4e66dacab", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114872744000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35215742-2ba9-46b0-bc64-9be5e0f48517", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114878893300, "endTime": 156115062790700}, "additional": {"children": ["dca1b2b7-0ccc-4fb4-9629-3f6ba797b7ba", "c8d492e9-a3d6-4849-86b5-b762d769545b", "eaf89fa4-015d-490a-b221-83d2032fbc10", "222bbd2c-8402-4f4a-800a-917c14caf65b", "74194388-6545-4b68-afe1-9173ad781b2c", "2f4ea3f5-bb7e-4657-add0-ea52bcee682e", "89c7fc8f-3b23-4e1c-9cae-e33e5552df6b"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "99230868-68f0-475e-949e-86ba1d825823"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dca1b2b7-0ccc-4fb4-9629-3f6ba797b7ba", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114878894900, "endTime": 156114891654100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35215742-2ba9-46b0-bc64-9be5e0f48517", "logId": "570a9ff6-add0-48aa-a096-6a037564657b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8d492e9-a3d6-4849-86b5-b762d769545b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114891669400, "endTime": 156115061616100}, "additional": {"children": ["d860acb4-5c7a-4428-8475-770fabade507", "a6429c2c-b1d3-43cb-b86c-b65cb64edbe2", "da94b2d8-49ac-40bf-afd6-f9763f01bb92", "151d2b27-c002-41a6-a319-0ef96aca8a82", "828542b6-0287-41b8-9a67-f83a358436f8", "d97c5dd1-d4ae-47f4-9989-b65993786834", "7a696ea3-9f71-41c4-b7ae-3c7f7cb4d915", "12d1a129-5776-456a-97b5-85541814ef03", "50ed77cb-3c1e-444d-b185-8b3052f037b0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35215742-2ba9-46b0-bc64-9be5e0f48517", "logId": "efeecf15-4cba-432d-9690-5c695eb5a469"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eaf89fa4-015d-490a-b221-83d2032fbc10", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115061642400, "endTime": 156115062752300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35215742-2ba9-46b0-bc64-9be5e0f48517", "logId": "bcbde4c5-ad11-4b9f-85bd-f22d27f2751d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "222bbd2c-8402-4f4a-800a-917c14caf65b", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115062761700, "endTime": 156115062762900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35215742-2ba9-46b0-bc64-9be5e0f48517", "logId": "57de3335-f5fe-4f47-a42f-9bb781219ad6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74194388-6545-4b68-afe1-9173ad781b2c", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114882770900, "endTime": 156114882882000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35215742-2ba9-46b0-bc64-9be5e0f48517", "logId": "2593aeed-cdf4-40da-ba8a-b26be9b67ff8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2593aeed-cdf4-40da-ba8a-b26be9b67ff8", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114882770900, "endTime": 156114882882000}, "additional": {"logType": "info", "children": [], "durationId": "74194388-6545-4b68-afe1-9173ad781b2c", "parent": "99230868-68f0-475e-949e-86ba1d825823"}}, {"head": {"id": "2f4ea3f5-bb7e-4657-add0-ea52bcee682e", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114886947900, "endTime": 156114886970200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35215742-2ba9-46b0-bc64-9be5e0f48517", "logId": "f6f0ebd5-7bc8-4503-886a-d5f82e26f55c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6f0ebd5-7bc8-4503-886a-d5f82e26f55c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114886947900, "endTime": 156114886970200}, "additional": {"logType": "info", "children": [], "durationId": "2f4ea3f5-bb7e-4657-add0-ea52bcee682e", "parent": "99230868-68f0-475e-949e-86ba1d825823"}}, {"head": {"id": "2a99bef7-fd3a-4d94-99b1-5f92e96304ef", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114887019500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dac5334a-8308-4b77-891b-3c912abda927", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114891541300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "570a9ff6-add0-48aa-a096-6a037564657b", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114878894900, "endTime": 156114891654100}, "additional": {"logType": "info", "children": [], "durationId": "dca1b2b7-0ccc-4fb4-9629-3f6ba797b7ba", "parent": "99230868-68f0-475e-949e-86ba1d825823"}}, {"head": {"id": "d860acb4-5c7a-4428-8475-770fabade507", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114896377300, "endTime": 156114896383400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8d492e9-a3d6-4849-86b5-b762d769545b", "logId": "3330c5e6-b76f-45ab-9405-ab9d15248cf1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6429c2c-b1d3-43cb-b86c-b65cb64edbe2", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114896397200, "endTime": 156114899472500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8d492e9-a3d6-4849-86b5-b762d769545b", "logId": "0482d954-bf00-445d-ba80-f39943fb0ed9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da94b2d8-49ac-40bf-afd6-f9763f01bb92", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114899482700, "endTime": 156114966623300}, "additional": {"children": ["478be2dc-3a08-483a-91ea-aa8baecb79cc", "8d585bda-66c3-45bb-bbf9-925eea8b4271", "75a39e0f-863f-4c37-bee3-bb3723c0fffb"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8d492e9-a3d6-4849-86b5-b762d769545b", "logId": "4006d821-be1f-4612-899a-96bd71341f54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "151d2b27-c002-41a6-a319-0ef96aca8a82", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114966637400, "endTime": 156114984940200}, "additional": {"children": ["75dad613-c2b2-4dd3-bc7e-8ea65968b76b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8d492e9-a3d6-4849-86b5-b762d769545b", "logId": "ee65df8d-2f17-47f9-961f-50e633dcee2a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "828542b6-0287-41b8-9a67-f83a358436f8", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114984947000, "endTime": 156115029801000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8d492e9-a3d6-4849-86b5-b762d769545b", "logId": "1e9d378c-4bbf-4b4a-a0aa-f43626249c07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d97c5dd1-d4ae-47f4-9989-b65993786834", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115030795800, "endTime": 156115047576900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8d492e9-a3d6-4849-86b5-b762d769545b", "logId": "301be2ba-b697-4093-8ee0-ce40e54f954e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a696ea3-9f71-41c4-b7ae-3c7f7cb4d915", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115047597600, "endTime": 156115061466200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8d492e9-a3d6-4849-86b5-b762d769545b", "logId": "0df847d6-55ff-4a3c-8bb7-2cdaecb92d34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12d1a129-5776-456a-97b5-85541814ef03", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115061484100, "endTime": 156115061604800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8d492e9-a3d6-4849-86b5-b762d769545b", "logId": "e9179dd1-0b2f-444a-a7f3-f449004a5073"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3330c5e6-b76f-45ab-9405-ab9d15248cf1", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114896377300, "endTime": 156114896383400}, "additional": {"logType": "info", "children": [], "durationId": "d860acb4-5c7a-4428-8475-770fabade507", "parent": "efeecf15-4cba-432d-9690-5c695eb5a469"}}, {"head": {"id": "0482d954-bf00-445d-ba80-f39943fb0ed9", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114896397200, "endTime": 156114899472500}, "additional": {"logType": "info", "children": [], "durationId": "a6429c2c-b1d3-43cb-b86c-b65cb64edbe2", "parent": "efeecf15-4cba-432d-9690-5c695eb5a469"}}, {"head": {"id": "478be2dc-3a08-483a-91ea-aa8baecb79cc", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114899980000, "endTime": 156114899996300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da94b2d8-49ac-40bf-afd6-f9763f01bb92", "logId": "e94f2cf7-03c4-438f-931c-7915cd6a9157"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e94f2cf7-03c4-438f-931c-7915cd6a9157", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114899980000, "endTime": 156114899996300}, "additional": {"logType": "info", "children": [], "durationId": "478be2dc-3a08-483a-91ea-aa8baecb79cc", "parent": "4006d821-be1f-4612-899a-96bd71341f54"}}, {"head": {"id": "8d585bda-66c3-45bb-bbf9-925eea8b4271", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114901466900, "endTime": 156114965924600}, "additional": {"children": ["2f3e8903-9b57-4bbc-8d48-09b510953dfd", "d7ec7b43-d05c-4a28-a071-dec90d0ea658"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da94b2d8-49ac-40bf-afd6-f9763f01bb92", "logId": "0068236e-bc97-4506-a00b-0d80f4c75ef3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f3e8903-9b57-4bbc-8d48-09b510953dfd", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114901467600, "endTime": 156114905446000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d585bda-66c3-45bb-bbf9-925eea8b4271", "logId": "ee0c4715-fbec-4d70-9010-a879c2540de6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7ec7b43-d05c-4a28-a071-dec90d0ea658", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114905455600, "endTime": 156114965914100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d585bda-66c3-45bb-bbf9-925eea8b4271", "logId": "9714901e-63c9-40d1-b9e0-8d291b4c22eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "021b5075-3c3e-4e2c-a260-cae6ebcb27b5", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114901470600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9516ff93-78f8-46a3-b679-d76893e33b98", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114905345000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee0c4715-fbec-4d70-9010-a879c2540de6", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114901467600, "endTime": 156114905446000}, "additional": {"logType": "info", "children": [], "durationId": "2f3e8903-9b57-4bbc-8d48-09b510953dfd", "parent": "0068236e-bc97-4506-a00b-0d80f4c75ef3"}}, {"head": {"id": "73ecd6df-9a53-41c3-89a8-d78fe605c8a7", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114905462800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c506c2d-f646-42e5-b82a-4f267f87ccd1", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114910733400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ce4e38f-35a9-4fdf-9b0b-b0b9ebd18fcb", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114910824000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20e138b6-fd4c-4522-a174-9d3a527792e8", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114911528300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a31bcdae-a26a-49cf-a345-ed432a3af9d7", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114911630100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5759f61-5534-4fe5-8976-a44960565f61", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114912840800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2af9adaf-8c79-408a-8caf-33ca51b802b9", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114924766300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81d1870e-6463-4017-919f-85d2899ab458", "name": "Sdk init in 29 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114947015200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2178cdb-4066-48bf-b5c0-eea8837e5f4e", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114947178900}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 19, "second": 4}, "markType": "other"}}, {"head": {"id": "6b48eb99-40eb-4565-88b9-fc6c2a2cfe9d", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114947191000}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 19, "second": 4}, "markType": "other"}}, {"head": {"id": "9b777e35-e517-48cb-9ac6-ccc66021a435", "name": "Project task initialization takes 17 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114965669700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d883927-f5e5-4532-85eb-1575447164a1", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114965786900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d43fb46a-6a60-412c-aaee-c2d29069abff", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114965840100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4ec1188-c76b-4a45-9bf1-98ab705c6ee5", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114965873000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9714901e-63c9-40d1-b9e0-8d291b4c22eb", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114905455600, "endTime": 156114965914100}, "additional": {"logType": "info", "children": [], "durationId": "d7ec7b43-d05c-4a28-a071-dec90d0ea658", "parent": "0068236e-bc97-4506-a00b-0d80f4c75ef3"}}, {"head": {"id": "0068236e-bc97-4506-a00b-0d80f4c75ef3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114901466900, "endTime": 156114965924600}, "additional": {"logType": "info", "children": ["ee0c4715-fbec-4d70-9010-a879c2540de6", "9714901e-63c9-40d1-b9e0-8d291b4c22eb"], "durationId": "8d585bda-66c3-45bb-bbf9-925eea8b4271", "parent": "4006d821-be1f-4612-899a-96bd71341f54"}}, {"head": {"id": "75a39e0f-863f-4c37-bee3-bb3723c0fffb", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114966484000, "endTime": 156114966609900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da94b2d8-49ac-40bf-afd6-f9763f01bb92", "logId": "b86d5021-0672-4274-930a-a52bdcb832bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b86d5021-0672-4274-930a-a52bdcb832bc", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114966484000, "endTime": 156114966609900}, "additional": {"logType": "info", "children": [], "durationId": "75a39e0f-863f-4c37-bee3-bb3723c0fffb", "parent": "4006d821-be1f-4612-899a-96bd71341f54"}}, {"head": {"id": "4006d821-be1f-4612-899a-96bd71341f54", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114899482700, "endTime": 156114966623300}, "additional": {"logType": "info", "children": ["e94f2cf7-03c4-438f-931c-7915cd6a9157", "0068236e-bc97-4506-a00b-0d80f4c75ef3", "b86d5021-0672-4274-930a-a52bdcb832bc"], "durationId": "da94b2d8-49ac-40bf-afd6-f9763f01bb92", "parent": "efeecf15-4cba-432d-9690-5c695eb5a469"}}, {"head": {"id": "75dad613-c2b2-4dd3-bc7e-8ea65968b76b", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114967154400, "endTime": 156114984931600}, "additional": {"children": ["92914f06-82fb-4d03-911b-ef64f206e174", "8e75808f-fa8b-4789-88e1-60d1f6b743b3", "859a1290-de56-4935-9cdb-33b1328dc865"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "151d2b27-c002-41a6-a319-0ef96aca8a82", "logId": "c8a17907-eac0-42ee-82a2-ee85c668c1d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92914f06-82fb-4d03-911b-ef64f206e174", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114969745700, "endTime": 156114969759900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "75dad613-c2b2-4dd3-bc7e-8ea65968b76b", "logId": "c971d188-a2bb-4b68-bdf0-af32d93ff5cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c971d188-a2bb-4b68-bdf0-af32d93ff5cb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114969745700, "endTime": 156114969759900}, "additional": {"logType": "info", "children": [], "durationId": "92914f06-82fb-4d03-911b-ef64f206e174", "parent": "c8a17907-eac0-42ee-82a2-ee85c668c1d4"}}, {"head": {"id": "8e75808f-fa8b-4789-88e1-60d1f6b743b3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114971266300, "endTime": 156114983788800}, "additional": {"children": ["c0a4950d-d237-4836-aac7-7dabb0f83c3c", "f732ead3-d0c7-415d-936b-4cc6273e1746"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "75dad613-c2b2-4dd3-bc7e-8ea65968b76b", "logId": "7354aaea-068a-4121-8a63-d31380fd74b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0a4950d-d237-4836-aac7-7dabb0f83c3c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114971267200, "endTime": 156114973860200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e75808f-fa8b-4789-88e1-60d1f6b743b3", "logId": "ed489704-1275-491f-8f65-35ee5b3f6a39"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f732ead3-d0c7-415d-936b-4cc6273e1746", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114973870300, "endTime": 156114983777000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e75808f-fa8b-4789-88e1-60d1f6b743b3", "logId": "cb724be2-4ffc-40f9-a9d9-8c4bf1e0e3bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b82d5365-24ca-4ccf-84d1-c9be1f951b83", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114971270000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7ef2974-8e1a-40f1-a971-4ee3cd8046d9", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114973754800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed489704-1275-491f-8f65-35ee5b3f6a39", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114971267200, "endTime": 156114973860200}, "additional": {"logType": "info", "children": [], "durationId": "c0a4950d-d237-4836-aac7-7dabb0f83c3c", "parent": "7354aaea-068a-4121-8a63-d31380fd74b5"}}, {"head": {"id": "47d72e72-9fec-4ede-b49b-6f07e74292f3", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114973876800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3524cb20-0149-44f6-bd21-972d058bfbe0", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114979785100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d7328c8-9f97-4eea-8eb9-bf6916aa3bb4", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114979907000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bdd4338-a4dd-4481-8e55-d7cc666f53fa", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980050700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4fb6ee1-ccce-4816-b912-a52d51d6c15e", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980125100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c339839-9056-431c-b92f-f181bbc21b4f", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980157400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f8f9e45-7c69-4db0-b5ce-afebed098b86", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980188200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca6ca701-cc96-4fe9-b948-9c925949835f", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980223900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a72cde8f-c3f3-4987-a629-0c7111b99a05", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980255200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9256a5f7-689d-4eb6-9425-4b519b9197ed", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980390600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a354b7b3-1a02-409b-abc6-05d8312eac5d", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980456200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ee700dc-d6c1-4270-8621-858874175972", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980495200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c079af93-1738-4728-bd5a-a201ea58c43b", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980521700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aad3fc08-a198-4657-9d6c-9de5ba068ac7", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980555900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d17bd0d-afc1-45f3-ad7c-24bdc0c6ab17", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980584200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af4ef3b3-d666-428b-ba90-51d63d8f0717", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980645600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5227da4-5e1f-474e-aeda-92d26844d9e9", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980700700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f905007a-02b1-4e59-87e0-53eba75b6a83", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980732300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9be63f5-ecbb-4357-a69a-0ad68f6fd2dd", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980758400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce45a18b-1de4-4849-ad07-b1e189747ede", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114980791900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f20e18d-8905-4ecd-9263-36c72289bd50", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114983504600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cfde888-80a4-4725-a3ac-6be9d9e97bd3", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114983672300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e055204-d8c1-41a0-a68e-7091c21f160f", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114983717500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfcd6020-39e9-44fd-b06e-2a315af33e7a", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114983746800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb724be2-4ffc-40f9-a9d9-8c4bf1e0e3bd", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114973870300, "endTime": 156114983777000}, "additional": {"logType": "info", "children": [], "durationId": "f732ead3-d0c7-415d-936b-4cc6273e1746", "parent": "7354aaea-068a-4121-8a63-d31380fd74b5"}}, {"head": {"id": "7354aaea-068a-4121-8a63-d31380fd74b5", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114971266300, "endTime": 156114983788800}, "additional": {"logType": "info", "children": ["ed489704-1275-491f-8f65-35ee5b3f6a39", "cb724be2-4ffc-40f9-a9d9-8c4bf1e0e3bd"], "durationId": "8e75808f-fa8b-4789-88e1-60d1f6b743b3", "parent": "c8a17907-eac0-42ee-82a2-ee85c668c1d4"}}, {"head": {"id": "859a1290-de56-4935-9cdb-33b1328dc865", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114984898400, "endTime": 156114984911400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "75dad613-c2b2-4dd3-bc7e-8ea65968b76b", "logId": "cd89b614-b8d9-4c82-aa29-d447e4786450"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd89b614-b8d9-4c82-aa29-d447e4786450", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114984898400, "endTime": 156114984911400}, "additional": {"logType": "info", "children": [], "durationId": "859a1290-de56-4935-9cdb-33b1328dc865", "parent": "c8a17907-eac0-42ee-82a2-ee85c668c1d4"}}, {"head": {"id": "c8a17907-eac0-42ee-82a2-ee85c668c1d4", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114967154400, "endTime": 156114984931600}, "additional": {"logType": "info", "children": ["c971d188-a2bb-4b68-bdf0-af32d93ff5cb", "7354aaea-068a-4121-8a63-d31380fd74b5", "cd89b614-b8d9-4c82-aa29-d447e4786450"], "durationId": "75dad613-c2b2-4dd3-bc7e-8ea65968b76b", "parent": "ee65df8d-2f17-47f9-961f-50e633dcee2a"}}, {"head": {"id": "ee65df8d-2f17-47f9-961f-50e633dcee2a", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114966637400, "endTime": 156114984940200}, "additional": {"logType": "info", "children": ["c8a17907-eac0-42ee-82a2-ee85c668c1d4"], "durationId": "151d2b27-c002-41a6-a319-0ef96aca8a82", "parent": "efeecf15-4cba-432d-9690-5c695eb5a469"}}, {"head": {"id": "510024ee-74ae-4255-835a-7afbbdd9ac95", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114997112800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebb16493-62b3-47a9-96f3-85cb89314db6", "name": "hvigorfile, resolve hvigorfile dependencies in 45 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115029665400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e9d378c-4bbf-4b4a-a0aa-f43626249c07", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114984947000, "endTime": 156115029801000}, "additional": {"logType": "info", "children": [], "durationId": "828542b6-0287-41b8-9a67-f83a358436f8", "parent": "efeecf15-4cba-432d-9690-5c695eb5a469"}}, {"head": {"id": "50ed77cb-3c1e-444d-b185-8b3052f037b0", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115030588900, "endTime": 156115030783700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8d492e9-a3d6-4849-86b5-b762d769545b", "logId": "a67193a8-9eff-4b5c-9f1a-2fa987fb8df9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92e412cf-c016-45f2-aa9d-ef81b10f15ab", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115030619900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a67193a8-9eff-4b5c-9f1a-2fa987fb8df9", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115030588900, "endTime": 156115030783700}, "additional": {"logType": "info", "children": [], "durationId": "50ed77cb-3c1e-444d-b185-8b3052f037b0", "parent": "efeecf15-4cba-432d-9690-5c695eb5a469"}}, {"head": {"id": "689af47d-ccf3-4a1c-b910-f60fc1b89b0f", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115032175100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36304267-03e8-483f-9604-80087e2c5949", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115046660500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "301be2ba-b697-4093-8ee0-ce40e54f954e", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115030795800, "endTime": 156115047576900}, "additional": {"logType": "info", "children": [], "durationId": "d97c5dd1-d4ae-47f4-9989-b65993786834", "parent": "efeecf15-4cba-432d-9690-5c695eb5a469"}}, {"head": {"id": "6eb5dc90-2a9e-4e19-b733-e3ff3acb94ff", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115047616700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6617277-715a-45a0-8bb9-aaf3b9ba4527", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115054986900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "378819b6-4d3e-4cf6-989d-6cabc3726664", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115055113800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41ab168a-2950-41b7-833b-14b13ae22a56", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115055265900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8cf86c8-b7c0-4259-b8d0-12dcc4c492e6", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115058218700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c7e2ba-b430-4bfa-b661-f4e85334f5c6", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115058316800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0df847d6-55ff-4a3c-8bb7-2cdaecb92d34", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115047597600, "endTime": 156115061466200}, "additional": {"logType": "info", "children": [], "durationId": "7a696ea3-9f71-41c4-b7ae-3c7f7cb4d915", "parent": "efeecf15-4cba-432d-9690-5c695eb5a469"}}, {"head": {"id": "e75ed4f9-8522-4e07-9bd8-22056087557f", "name": "Configuration phase cost:166 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115061512300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9179dd1-0b2f-444a-a7f3-f449004a5073", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115061484100, "endTime": 156115061604800}, "additional": {"logType": "info", "children": [], "durationId": "12d1a129-5776-456a-97b5-85541814ef03", "parent": "efeecf15-4cba-432d-9690-5c695eb5a469"}}, {"head": {"id": "efeecf15-4cba-432d-9690-5c695eb5a469", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114891669400, "endTime": 156115061616100}, "additional": {"logType": "info", "children": ["3330c5e6-b76f-45ab-9405-ab9d15248cf1", "0482d954-bf00-445d-ba80-f39943fb0ed9", "4006d821-be1f-4612-899a-96bd71341f54", "ee65df8d-2f17-47f9-961f-50e633dcee2a", "1e9d378c-4bbf-4b4a-a0aa-f43626249c07", "301be2ba-b697-4093-8ee0-ce40e54f954e", "0df847d6-55ff-4a3c-8bb7-2cdaecb92d34", "e9179dd1-0b2f-444a-a7f3-f449004a5073", "a67193a8-9eff-4b5c-9f1a-2fa987fb8df9"], "durationId": "c8d492e9-a3d6-4849-86b5-b762d769545b", "parent": "99230868-68f0-475e-949e-86ba1d825823"}}, {"head": {"id": "89c7fc8f-3b23-4e1c-9cae-e33e5552df6b", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115062729100, "endTime": 156115062743000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35215742-2ba9-46b0-bc64-9be5e0f48517", "logId": "d0feb083-b053-47dc-9533-3114c34a27d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0feb083-b053-47dc-9533-3114c34a27d6", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115062729100, "endTime": 156115062743000}, "additional": {"logType": "info", "children": [], "durationId": "89c7fc8f-3b23-4e1c-9cae-e33e5552df6b", "parent": "99230868-68f0-475e-949e-86ba1d825823"}}, {"head": {"id": "bcbde4c5-ad11-4b9f-85bd-f22d27f2751d", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115061642400, "endTime": 156115062752300}, "additional": {"logType": "info", "children": [], "durationId": "eaf89fa4-015d-490a-b221-83d2032fbc10", "parent": "99230868-68f0-475e-949e-86ba1d825823"}}, {"head": {"id": "57de3335-f5fe-4f47-a42f-9bb781219ad6", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115062761700, "endTime": 156115062762900}, "additional": {"logType": "info", "children": [], "durationId": "222bbd2c-8402-4f4a-800a-917c14caf65b", "parent": "99230868-68f0-475e-949e-86ba1d825823"}}, {"head": {"id": "99230868-68f0-475e-949e-86ba1d825823", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114878893300, "endTime": 156115062790700}, "additional": {"logType": "info", "children": ["570a9ff6-add0-48aa-a096-6a037564657b", "efeecf15-4cba-432d-9690-5c695eb5a469", "bcbde4c5-ad11-4b9f-85bd-f22d27f2751d", "57de3335-f5fe-4f47-a42f-9bb781219ad6", "2593aeed-cdf4-40da-ba8a-b26be9b67ff8", "f6f0ebd5-7bc8-4503-886a-d5f82e26f55c", "d0feb083-b053-47dc-9533-3114c34a27d6"], "durationId": "35215742-2ba9-46b0-bc64-9be5e0f48517"}}, {"head": {"id": "8a888581-de30-4df2-ab91-9c560e7c3f24", "name": "Configuration task cost before running: 188 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115062973600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2d3d6e3-2816-4c08-9175-5b14fd19f2fa", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115071149900, "endTime": 156115084496600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "5c7f82d6-02d3-4f7d-8003-d0787d6f3419", "logId": "5b9bd356-d0ed-4caf-aef4-a7e2e8755eac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c7f82d6-02d3-4f7d-8003-d0787d6f3419", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115064338400}, "additional": {"logType": "detail", "children": [], "durationId": "d2d3d6e3-2816-4c08-9175-5b14fd19f2fa"}}, {"head": {"id": "c2378154-0aca-411b-92a1-61da50f6bdf4", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115065001400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f23276bf-63c9-410c-8c41-14126fa643b2", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115065114500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef3328b9-42f1-48e5-86c1-be0055a97b8f", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115065771200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d26165ac-f601-4ee0-b0b8-7689435a16ad", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115066603600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04b637b5-a43e-4464-83c4-d0b2860e0809", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115067592600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dfd63d3-4772-4564-abbc-f7bc2cf9d712", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115067676700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f73af628-e1c6-4ded-b8a1-ef3d4e0cd342", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115071162600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03b36052-ce34-4cdc-bb9a-f2b23700996c", "name": "Incremental task entry:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115084210600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a08aec28-3f1e-49e3-bdea-b7e5e49c2627", "name": "entry : default@PreBuild cost memory 0.321533203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115084387700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b9bd356-d0ed-4caf-aef4-a7e2e8755eac", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115071149900, "endTime": 156115084496600}, "additional": {"logType": "info", "children": [], "durationId": "d2d3d6e3-2816-4c08-9175-5b14fd19f2fa"}}, {"head": {"id": "155842bf-6495-43d9-b33e-c4c250bd8019", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115092416400, "endTime": 156115094194400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4000c28e-cf95-4d44-b81e-fb63f6e3e38f", "logId": "99752209-0362-49ca-87fd-79e13cf66a4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4000c28e-cf95-4d44-b81e-fb63f6e3e38f", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115090599000}, "additional": {"logType": "detail", "children": [], "durationId": "155842bf-6495-43d9-b33e-c4c250bd8019"}}, {"head": {"id": "f8503f40-6062-4896-818e-e3f143a67ac2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115091671400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef0e241f-449f-4702-8628-19e00c393616", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115091791500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb840e97-f406-417c-a7f5-7b8e72a57118", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115092426100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88a83502-42be-4236-a7e8-a2702f13f32d", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115093096800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0bff282-454b-4449-b3ba-4069d65d5e71", "name": "entry : default@CreateModuleInfo cost memory 0.05980682373046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115094021000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a2957d7-185f-43bb-afa1-7d90358cfef7", "name": "runTaskFromQueue task cost before running: 219 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115094139200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99752209-0362-49ca-87fd-79e13cf66a4e", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115092416400, "endTime": 156115094194400, "totalTime": 1704400}, "additional": {"logType": "info", "children": [], "durationId": "155842bf-6495-43d9-b33e-c4c250bd8019"}}, {"head": {"id": "236c7279-e2b5-4ac3-9244-3380c5d18869", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115103773400, "endTime": 156115106657200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cde60257-715f-4fc9-aada-1e171ad74486", "logId": "f5eb4b4b-80f8-4446-993a-53f02bd01586"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cde60257-715f-4fc9-aada-1e171ad74486", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115096361900}, "additional": {"logType": "detail", "children": [], "durationId": "236c7279-e2b5-4ac3-9244-3380c5d18869"}}, {"head": {"id": "25995db1-0ed7-4050-a540-a1d33888d66e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115097500900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bb44f17-7192-4a3d-8e85-68c3480fd7b3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115097631300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dbf4bc2-9bd2-477d-b721-7177382154ac", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115103788800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31a3df62-8a4e-40f5-a2f7-2f5ef2c3b4ce", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115105150000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "512e1485-613d-438f-b0e5-2887a6eff106", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115106462400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10fcf92a-3db1-4a55-b9d7-8cf3e679adbe", "name": "entry : default@GenerateMetadata cost memory 0.10198211669921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115106581900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5eb4b4b-80f8-4446-993a-53f02bd01586", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115103773400, "endTime": 156115106657200}, "additional": {"logType": "info", "children": [], "durationId": "236c7279-e2b5-4ac3-9244-3380c5d18869"}}, {"head": {"id": "d86e46c0-9401-4e7e-a199-1c387002af17", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115109929000, "endTime": 156115110238900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "553d274d-9250-444a-8d7f-7457141b2749", "logId": "8ce5b49c-0fff-4da0-a93e-6ab6a6bb5613"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "553d274d-9250-444a-8d7f-7457141b2749", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115108452200}, "additional": {"logType": "detail", "children": [], "durationId": "d86e46c0-9401-4e7e-a199-1c387002af17"}}, {"head": {"id": "25bcc406-de1f-4680-8a72-6874443a4fe1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115109658600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45169ac9-a1d7-4bec-9e5a-8930d7a918df", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115109783500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f76bd1f9-ba16-484d-8dc0-dd0c7b704a1c", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115109936600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f28741b2-6c3e-4ee3-88f6-8abb7063b8f5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115110028300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce7a80dd-351d-41a8-865d-1f7a5694346c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115110066500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a59dff13-91ca-43bf-806c-210f70cba151", "name": "entry : default@ConfigureCmake cost memory 0.037200927734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115110124300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1460b282-9277-4ee6-8653-53f001de8e45", "name": "runTaskFromQueue task cost before running: 235 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115110197200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ce5b49c-0fff-4da0-a93e-6ab6a6bb5613", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115109929000, "endTime": 156115110238900, "totalTime": 252800}, "additional": {"logType": "info", "children": [], "durationId": "d86e46c0-9401-4e7e-a199-1c387002af17"}}, {"head": {"id": "4d181e8f-5391-4eee-9943-aa067bb934f1", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115114025400, "endTime": 156115117002700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "4e1219a1-5211-4a7d-bcd6-fa516d12344b", "logId": "a2b2265c-3354-49b5-8bd0-05ccc0883ce5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e1219a1-5211-4a7d-bcd6-fa516d12344b", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115111984700}, "additional": {"logType": "detail", "children": [], "durationId": "4d181e8f-5391-4eee-9943-aa067bb934f1"}}, {"head": {"id": "2b8e480e-6a30-46f7-a368-8bc19f0062d1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115113132700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "002e4aed-8979-40a7-be98-3c2669e2ca46", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115113278900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a648b635-8ff1-41ea-995b-d5be56b1b39f", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115114035600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5852706-07df-4603-9cba-2d6ded156a42", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115116760700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55f2a63c-c21e-492b-b516-5ba5cc10c664", "name": "entry : default@MergeProfile cost memory 0.1180572509765625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115116930800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2b2265c-3354-49b5-8bd0-05ccc0883ce5", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115114025400, "endTime": 156115117002700}, "additional": {"logType": "info", "children": [], "durationId": "4d181e8f-5391-4eee-9943-aa067bb934f1"}}, {"head": {"id": "92c51851-565c-4070-ab87-9a466d2e75dd", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115120968000, "endTime": 156115123780500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b3527ba6-b7a4-4020-a802-5b402f786073", "logId": "3714ec16-f8fb-4eef-8bf1-df903f46682c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3527ba6-b7a4-4020-a802-5b402f786073", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115118719100}, "additional": {"logType": "detail", "children": [], "durationId": "92c51851-565c-4070-ab87-9a466d2e75dd"}}, {"head": {"id": "b1ca0bf5-1dc3-49ee-8073-a9121bf8493b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115119658800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d869493-c9fd-4497-b06f-8d26eb7ac5c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115119797300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8598794-d171-4883-913d-d0249608125d", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115120979400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dd46b3b-0d42-4a87-a34d-b999634bf2a2", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115121994700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d62c9e7-8431-4b0f-9d2c-206f16a6bfb3", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115123575400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2436ef78-a557-4891-86a0-4ccc9a02695d", "name": "entry : default@CreateBuildProfile cost memory 0.10662841796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115123704900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3714ec16-f8fb-4eef-8bf1-df903f46682c", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115120968000, "endTime": 156115123780500}, "additional": {"logType": "info", "children": [], "durationId": "92c51851-565c-4070-ab87-9a466d2e75dd"}}, {"head": {"id": "23cfb3a6-dc7f-467c-886d-64d165df2b97", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115127259000, "endTime": 156115127794200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "803901f9-bdda-4da5-a99c-a76fd6455d9e", "logId": "480acbd2-f8b5-4c72-b355-420df2fd2fb1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "803901f9-bdda-4da5-a99c-a76fd6455d9e", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115125344800}, "additional": {"logType": "detail", "children": [], "durationId": "23cfb3a6-dc7f-467c-886d-64d165df2b97"}}, {"head": {"id": "f77d5296-77dc-40d5-9a90-9433cbf0c887", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115126435200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "475a8bc0-9407-4d6f-b5bc-008e97241f25", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115126546800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5a4bd9a-3271-4218-a451-4ddf96740f43", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115127267200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "079acefa-7f74-4dc1-b53c-4f5f28088090", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115127406900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d477ade-c1f4-429f-aeba-d7d540fa8228", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115127458400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "706e3a4c-b2b0-4b5d-a443-b7626e680170", "name": "entry : default@PreCheckSyscap cost memory 0.04083251953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115127652500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abb542b8-9e23-4bb2-836f-581b6b9c862e", "name": "runTaskFromQueue task cost before running: 252 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115127745300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "480acbd2-f8b5-4c72-b355-420df2fd2fb1", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115127259000, "endTime": 156115127794200, "totalTime": 469700}, "additional": {"logType": "info", "children": [], "durationId": "23cfb3a6-dc7f-467c-886d-64d165df2b97"}}, {"head": {"id": "7a25c37a-d7a1-45cf-8db5-67e672889a7a", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115132484400, "endTime": 156115137646600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2ebd8668-4de5-41e9-87ab-742604e9c176", "logId": "94b9889d-2dad-4ab0-9c72-5f230113291f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ebd8668-4de5-41e9-87ab-742604e9c176", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115129307600}, "additional": {"logType": "detail", "children": [], "durationId": "7a25c37a-d7a1-45cf-8db5-67e672889a7a"}}, {"head": {"id": "54b8e68c-0aa9-47fd-89bf-0a0e19330eee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115130924200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1acc9ae2-29b1-472e-996e-6cc61a9a53d4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115131054200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6ecfb5a-78a5-4c4d-8189-257897f46b8f", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115132496100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "285df706-5fd0-492f-bc7b-b8358146d99d", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115136701700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "970738e3-6014-406b-bf31-ace0d2c39cfb", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115137426500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0577068-ac32-4e93-8257-72e67dc5df69", "name": "entry : default@GeneratePkgContextInfo cost memory 0.24883270263671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115137542100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94b9889d-2dad-4ab0-9c72-5f230113291f", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115132484400, "endTime": 156115137646600}, "additional": {"logType": "info", "children": [], "durationId": "7a25c37a-d7a1-45cf-8db5-67e672889a7a"}}, {"head": {"id": "416d4f55-7d72-42d6-bbc7-2cf57e17d0ca", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115147581500, "endTime": 156115151443200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "8efaf34d-22d1-4f0f-830e-da0b374faffc", "logId": "05cc83bc-956a-4fcd-a7a8-63236ce967de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8efaf34d-22d1-4f0f-830e-da0b374faffc", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115141504600}, "additional": {"logType": "detail", "children": [], "durationId": "416d4f55-7d72-42d6-bbc7-2cf57e17d0ca"}}, {"head": {"id": "e4c38465-fc51-4e44-8574-d47ba56a0aaa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115142652400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f5ac039-2472-4eb6-a9d7-44212aeca1fa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115142776700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e0d5549-6b08-4a8e-99c2-4db751d203e5", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115147599700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6767cacc-fe90-47fc-a338-9c8f92cc5f52", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115150755700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99e72e56-7e50-40f3-9470-7cf9efc41e02", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115150987700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ce0abf8-4f78-4817-8115-0ac4d6e41935", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115151122200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfb2a8a5-91fb-416f-9fd8-12a0aa82977c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115151190300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64e0d2ff-2ae7-4a7c-bd14-930cccc9c47e", "name": "entry : default@ProcessIntegratedHsp cost memory 0.119781494140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115151289600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edcf436c-4723-4184-b3f0-a951915ed052", "name": "runTaskFromQueue task cost before running: 276 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115151376800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05cc83bc-956a-4fcd-a7a8-63236ce967de", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115147581500, "endTime": 156115151443200, "totalTime": 3778700}, "additional": {"logType": "info", "children": [], "durationId": "416d4f55-7d72-42d6-bbc7-2cf57e17d0ca"}}, {"head": {"id": "6d58d111-1abf-4969-8393-ee93c9ebe09d", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115157000000, "endTime": 156115157720500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5f8d2495-7a1d-4efe-8f81-0efa08316c0d", "logId": "b8e92c6a-f366-425e-aeb0-ca986cb87483"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f8d2495-7a1d-4efe-8f81-0efa08316c0d", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115154609500}, "additional": {"logType": "detail", "children": [], "durationId": "6d58d111-1abf-4969-8393-ee93c9ebe09d"}}, {"head": {"id": "99c4b128-c430-4bfe-a973-2dccc6ec8575", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115155925300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8c4ac0c-95d2-424c-8922-a25af1c8c3ed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115156064700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdcdd7cf-9f9b-4bd6-b4e7-a55032b1a081", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115157013600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e1b1bc3-43a6-43b9-977d-9c303143e119", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115157192600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a82d41-fdaf-4b1e-904d-32f7157a696d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115157266000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c98c5ab-6a72-434f-ad32-b11e81b8d124", "name": "entry : default@BuildNativeWithCmake cost memory 0.03824615478515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115157360300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59bf22ff-d107-49fd-a517-5a4c0c28d30a", "name": "runTaskFromQueue task cost before running: 282 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115157543700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8e92c6a-f366-425e-aeb0-ca986cb87483", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115157000000, "endTime": 156115157720500, "totalTime": 513200}, "additional": {"logType": "info", "children": [], "durationId": "6d58d111-1abf-4969-8393-ee93c9ebe09d"}}, {"head": {"id": "cad06568-240e-4180-9185-6bccde4f0f03", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115161771900, "endTime": 156115165553200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "47c423f2-38c6-4110-9242-35e107dfb521", "logId": "10795970-e76b-4407-bd3f-de60d105c711"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47c423f2-38c6-4110-9242-35e107dfb521", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115159610200}, "additional": {"logType": "detail", "children": [], "durationId": "cad06568-240e-4180-9185-6bccde4f0f03"}}, {"head": {"id": "c92fe76b-af5a-4bc9-8e0c-3ca271d2f56e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115160698500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a3dc8b4-1674-414c-b2cc-90e74c4231a1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115160917200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d58b119b-d611-4866-86a6-072dca83cc31", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115161780100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8e7b6ed-cb30-4302-95c0-440e818554df", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115165352700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86275bcc-3222-4821-b405-599c9d2312ed", "name": "entry : default@MakePackInfo cost memory 0.16297149658203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115165484300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10795970-e76b-4407-bd3f-de60d105c711", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115161771900, "endTime": 156115165553200}, "additional": {"logType": "info", "children": [], "durationId": "cad06568-240e-4180-9185-6bccde4f0f03"}}, {"head": {"id": "186cc038-2fe8-4565-8e10-c774a103826a", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115169833400, "endTime": 156115173118500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "275860f1-def7-412a-8120-5cb302a36b72", "logId": "e85c6628-c171-4ccc-9195-75f9c02e0363"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "275860f1-def7-412a-8120-5cb302a36b72", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115167557800}, "additional": {"logType": "detail", "children": [], "durationId": "186cc038-2fe8-4565-8e10-c774a103826a"}}, {"head": {"id": "9c1927a5-9df6-4ae7-9ec6-65a62110517d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115168507300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f770257-2b20-43d5-b513-ef40b5d64dfc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115168601700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbcc2f35-60b9-4041-b6fa-4a688f0a9a31", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115169842000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a957d070-3985-4918-b15a-63202552764d", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115170034700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9741c26-2847-42b3-8b07-6ee11e6b2244", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115170745300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e7cf782-f9ea-4712-a490-a4e672d10c08", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115172942700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e87c76d-c885-4f97-80e8-ec29a313dbf1", "name": "entry : default@SyscapTransform cost memory 0.14949798583984375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115173055400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e85c6628-c171-4ccc-9195-75f9c02e0363", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115169833400, "endTime": 156115173118500}, "additional": {"logType": "info", "children": [], "durationId": "186cc038-2fe8-4565-8e10-c774a103826a"}}, {"head": {"id": "bfb154b8-bf1e-4499-a98e-a2a0cdc0a835", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115176526300, "endTime": 156115178697400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "17936e61-c22b-44c2-9a92-70bbb3eac2de", "logId": "6fa54768-f67f-42ff-bfa8-6d66137d691d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17936e61-c22b-44c2-9a92-70bbb3eac2de", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115174585100}, "additional": {"logType": "detail", "children": [], "durationId": "bfb154b8-bf1e-4499-a98e-a2a0cdc0a835"}}, {"head": {"id": "b976a5b9-b55b-49c9-a31d-33a706b765fb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115175481600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38cd8a60-de03-403c-8b0e-52e3e81d2be1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115175570000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a1b7bd2-5de0-43e8-8f64-ebb96210822b", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115176533600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31e273da-0d6f-488f-b93c-9c119f53c036", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115178470300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e86d0ec-6474-4258-a2d6-22cff294ebce", "name": "entry : default@ProcessProfile cost memory 0.12488555908203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115178616500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fa54768-f67f-42ff-bfa8-6d66137d691d", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115176526300, "endTime": 156115178697400}, "additional": {"logType": "info", "children": [], "durationId": "bfb154b8-bf1e-4499-a98e-a2a0cdc0a835"}}, {"head": {"id": "9c165eb5-835d-4407-b3f9-5392f02f3579", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115183759300, "endTime": 156115190033700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "12e993c5-d79e-4389-81c3-86f2da8df5c7", "logId": "9dc3d1b0-7dfe-490b-b1b3-928bdebebfb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12e993c5-d79e-4389-81c3-86f2da8df5c7", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115180289700}, "additional": {"logType": "detail", "children": [], "durationId": "9c165eb5-835d-4407-b3f9-5392f02f3579"}}, {"head": {"id": "66945afd-23df-4ea4-8d59-86af599da2dc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115181376100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f54e5d49-b8b8-4072-a9ab-35cf519bdcdf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115181516300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4ac3172-268e-428a-a512-7124b19f7297", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115183773900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97b0d922-b17d-4c1f-a437-eed2877a6362", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115189795300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e70fab8d-3cd1-44a7-b410-19c06f42228f", "name": "entry : default@ProcessRouterMap cost memory 0.23200225830078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115189963900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dc3d1b0-7dfe-490b-b1b3-928bdebebfb3", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115183759300, "endTime": 156115190033700}, "additional": {"logType": "info", "children": [], "durationId": "9c165eb5-835d-4407-b3f9-5392f02f3579"}}, {"head": {"id": "fcd1fae9-a0b8-4f81-afe3-c137327d590e", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115194262600, "endTime": 156115200100400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "7030d4e8-d109-437f-b475-318f451b1eaf", "logId": "29c90bf8-1abd-4217-9017-8c013206203f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7030d4e8-d109-437f-b475-318f451b1eaf", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115192950700}, "additional": {"logType": "detail", "children": [], "durationId": "fcd1fae9-a0b8-4f81-afe3-c137327d590e"}}, {"head": {"id": "a4a0f1ec-497a-4b9f-af52-48888ae3a7f8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115194079500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1019a7be-3903-4407-8163-20c151619652", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115194181200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59f80399-2c0c-4f4f-8b5e-15d22e1b5240", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115194269400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bc6f7f1-8ab7-4185-81e9-f0a9046c0703", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115194359400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e3fa59d-172d-4999-9cae-993f90a7bcbb", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115197829300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1ced6c5-178f-4d92-b73f-1aa683116317", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115197962100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb247a98-ed84-4f9a-9f05-05ab8b5e7a83", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115198182000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0156c792-ed9c-45a9-bf29-b64860f4eafd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115198315200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bd83f65-09c8-4bd5-959d-a645603ca546", "name": "entry : default@ProcessStartupConfig cost memory 0.257110595703125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115199922200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7ffe6b1-dcec-4fe0-84b6-c245430ec5aa", "name": "runTaskFromQueue task cost before running: 325 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115200041100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29c90bf8-1abd-4217-9017-8c013206203f", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115194262600, "endTime": 156115200100400, "totalTime": 5757800}, "additional": {"logType": "info", "children": [], "durationId": "fcd1fae9-a0b8-4f81-afe3-c137327d590e"}}, {"head": {"id": "e2e0ceb4-d782-4adc-bbfa-231674ab5f8f", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115204163100, "endTime": 156115205380700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "fd115fee-69e1-4410-90e7-6ce442ce08f6", "logId": "5eec17d5-9a23-411a-a43a-6ceb5cd61b77"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd115fee-69e1-4410-90e7-6ce442ce08f6", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115202528600}, "additional": {"logType": "detail", "children": [], "durationId": "e2e0ceb4-d782-4adc-bbfa-231674ab5f8f"}}, {"head": {"id": "b4d8b772-0da9-44a3-9b9d-c1de2e12f30f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115203429300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10c8567f-c286-43fc-ac80-031ed017f1c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115203525100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f093032-3a7c-4627-b9d7-0816c5200503", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115204170200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cce65454-9774-4a3d-ad28-c2500f08c388", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115204284300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbd6167f-915b-4b95-875e-a5d3cd7bd8a8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115204333000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dd403f4-3674-4494-b8c0-3e66f710bddc", "name": "entry : default@BuildNativeWithNinja cost memory 0.0578460693359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115205196500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce5851af-266c-4fcb-8cdc-e7014cd58bea", "name": "runTaskFromQueue task cost before running: 330 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115205317900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5eec17d5-9a23-411a-a43a-6ceb5cd61b77", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115204163100, "endTime": 156115205380700, "totalTime": 1137700}, "additional": {"logType": "info", "children": [], "durationId": "e2e0ceb4-d782-4adc-bbfa-231674ab5f8f"}}, {"head": {"id": "94b96168-8418-4256-90da-500fc77dc9cd", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115210745300, "endTime": 156115216669400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "87e9fb32-9c10-4c58-b2fb-34cadc80bbdd", "logId": "599b7c28-1f9c-44f5-9a63-896cba60d9ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87e9fb32-9c10-4c58-b2fb-34cadc80bbdd", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115207680100}, "additional": {"logType": "detail", "children": [], "durationId": "94b96168-8418-4256-90da-500fc77dc9cd"}}, {"head": {"id": "ea5e0a98-5a24-49b1-a209-8d602af7a5c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115208581100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b72691e-52ea-493a-b91a-dafcb9a54ff1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115208670600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8e8de4d-ab84-4d56-8dc6-022af1cbe5be", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115209664100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "040e6d1b-ba55-4322-83c2-bb1439c735ea", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115212430600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "809771c9-c7b6-4217-bbcd-e7cbd5e6654e", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115214431500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f9c0346-3ba3-4935-af7f-8edd72c1754a", "name": "entry : default@ProcessResource cost memory 0.16095733642578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115214538900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "599b7c28-1f9c-44f5-9a63-896cba60d9ac", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115210745300, "endTime": 156115216669400}, "additional": {"logType": "info", "children": [], "durationId": "94b96168-8418-4256-90da-500fc77dc9cd"}}, {"head": {"id": "fee7b678-623d-4c26-854d-ae7da2dfafcc", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115224765600, "endTime": 156115249783500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "947fef16-e108-45c8-b4ca-033f046c11c3", "logId": "6a935418-8b3d-4f5e-8f9f-f27c145d47bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "947fef16-e108-45c8-b4ca-033f046c11c3", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115220494700}, "additional": {"logType": "detail", "children": [], "durationId": "fee7b678-623d-4c26-854d-ae7da2dfafcc"}}, {"head": {"id": "87936fa2-0f6a-41f5-8736-1b3e6ceb434d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115221584400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b032fd58-7021-4d67-bef5-001674924ad6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115221719400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83befac5-b0de-4347-bf2d-e95ff438e1db", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115224777700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "170058cb-a527-4547-b5ca-1ddb0cd4fd74", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115249236900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01e8bbe4-46fd-4be2-be7d-62a75ec37d1e", "name": "entry : default@GenerateLoaderJson cost memory 0.8755111694335938", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115249693700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a935418-8b3d-4f5e-8f9f-f27c145d47bd", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115224765600, "endTime": 156115249783500}, "additional": {"logType": "info", "children": [], "durationId": "fee7b678-623d-4c26-854d-ae7da2dfafcc"}}, {"head": {"id": "917fc43d-0976-4a28-9791-8f03b888a604", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115259748500, "endTime": 156115265514400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c55965c6-b844-43e3-ab14-3afd3d0b02ef", "logId": "eaf12205-eed3-41c8-9f04-146c7b957477"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c55965c6-b844-43e3-ab14-3afd3d0b02ef", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115257910500}, "additional": {"logType": "detail", "children": [], "durationId": "917fc43d-0976-4a28-9791-8f03b888a604"}}, {"head": {"id": "9c101fb4-5d45-4469-bdca-15943afec74c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115258903300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dc472f5-4ef6-45e5-b2b3-f083c3266386", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115259018500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06963ca3-53fa-4e68-9b90-6c44d3381bd7", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115259757900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b7bb5d4-cfc0-4160-a5c9-a02bdd257ced", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115265279500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b8dcb41-9b3f-452a-bd4a-5acc404bebb8", "name": "entry : default@ProcessLibs cost memory 0.141357421875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115265435600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaf12205-eed3-41c8-9f04-146c7b957477", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115259748500, "endTime": 156115265514400}, "additional": {"logType": "info", "children": [], "durationId": "917fc43d-0976-4a28-9791-8f03b888a604"}}, {"head": {"id": "546c49e8-c980-4c31-a253-ffb41151b469", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115274916100, "endTime": 156115309111200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ae7e458c-ba61-45d0-b789-7e40feb21bda", "logId": "54c633cb-cd09-4bda-b6ac-3ddf6c1a9dfa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae7e458c-ba61-45d0-b789-7e40feb21bda", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115267704700}, "additional": {"logType": "detail", "children": [], "durationId": "546c49e8-c980-4c31-a253-ffb41151b469"}}, {"head": {"id": "290e4f83-e018-4d92-b663-6ae04befc812", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115268741700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e0b7eab-4682-4de9-b5a5-2169faa51398", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115268840100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0370f744-8e79-4b8d-935f-f05c0d28bcd1", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115269869500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce8e4a8d-eb2f-4984-a205-bcc6beeee259", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115274943000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9310783b-ce89-4801-b24b-0e276a561c27", "name": "Incremental task entry:default@CompileResource pre-execution cost: 33 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115308691500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f912a82-f06c-4541-875e-49f817620bd2", "name": "entry : default@CompileResource cost memory 1.3144378662109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115308944900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54c633cb-cd09-4bda-b6ac-3ddf6c1a9dfa", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115274916100, "endTime": 156115309111200}, "additional": {"logType": "info", "children": [], "durationId": "546c49e8-c980-4c31-a253-ffb41151b469"}}, {"head": {"id": "ba01c73a-573a-4e4b-ad75-7a742754b3aa", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115322683100, "endTime": 156115329016900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f5401564-cfaa-404c-b131-5fc88dbf24d8", "logId": "866a757b-3823-4050-840e-313f5b968162"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5401564-cfaa-404c-b131-5fc88dbf24d8", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115314564800}, "additional": {"logType": "detail", "children": [], "durationId": "ba01c73a-573a-4e4b-ad75-7a742754b3aa"}}, {"head": {"id": "9d65989e-b199-48ca-b112-15277526f851", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115317706300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e9421a0-bbb0-4f46-814c-f7907b7e3115", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115317944200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f9e5147-3722-4bf3-a6ed-c1aaa48a09a4", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115322695600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddca49b8-6270-4dc5-ac05-f3a8d807528b", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 4 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115326262800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82d40abb-910f-4713-a4b0-bbbe2463712d", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115328742000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f343325f-c338-4702-95c2-fb9e1dee36bc", "name": "entry : default@DoNativeStrip cost memory 0.079986572265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115328895700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "866a757b-3823-4050-840e-313f5b968162", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115322683100, "endTime": 156115329016900}, "additional": {"logType": "info", "children": [], "durationId": "ba01c73a-573a-4e4b-ad75-7a742754b3aa"}}, {"head": {"id": "aa73d583-193a-41d9-b7fb-b1883e893190", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115335979600, "endTime": 156125789467700}, "additional": {"children": ["f4b83f7f-59a8-4943-9119-05adb402cad1", "55064940-24fd-4b9d-9879-ffb573ca6fb4"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "5f3432eb-c876-4c54-bb19-47e80e94d717", "logId": "f9775d87-35bf-4dce-a506-72bd20e97372"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f3432eb-c876-4c54-bb19-47e80e94d717", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115330572100}, "additional": {"logType": "detail", "children": [], "durationId": "aa73d583-193a-41d9-b7fb-b1883e893190"}}, {"head": {"id": "1bf6adb7-a69a-4ddd-b9de-ffefe6b2c37f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115331564100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65eac1ea-fd98-46c2-b660-09021382ecc1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115331671700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d515f0b-b9bd-4c7f-8b88-f899ef019f04", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115335991900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e18867ba-4acd-4dad-ae74-fabec54b7267", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115336174800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6d55ee9-fb8c-479c-9105-f4494982c627", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115368750800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca73c682-4f6b-4a0d-b0f7-2c99c556c0c5", "name": "default@CompileArkTS work[22] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115370519700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4b83f7f-59a8-4943-9119-05adb402cad1", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 156118151521100, "endTime": 156125783033600}, "additional": {"children": ["aa0feb5f-477c-4700-9096-83855b2807c4", "c70773f3-251a-4440-9b8d-b7b85695bb1b", "5630b945-0154-4b10-8557-33bc4f42ec3b", "185b28d7-4ef1-4b81-9c83-b56c71ae0e98", "e4118ca1-3302-41c3-a175-ba0dca309c6e", "02930b31-73aa-46a0-b4bd-c36233b81c98", "923e696f-0ded-438f-989e-94107260119c"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "aa73d583-193a-41d9-b7fb-b1883e893190", "logId": "95f69ee4-6746-48da-8787-deedbc3c8801"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3eb0a2e8-3a02-4225-9ad5-60b2f086330b", "name": "default@CompileArkTS work[22] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371419600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "234a4663-3050-4802-a12f-28e2c91e2c7a", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371511400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05567bf3-8381-465c-be5a-5e99ab01a6dd", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371552500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cdbc963-62a6-4be8-9706-9dc8c837b9e3", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371581200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2256dda2-050d-497f-9a33-57cb9a1214eb", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371607800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc0109de-7fc6-4649-9b7f-6330fe4b87d3", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371647400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0d73571-12b2-412d-9061-31a91dae17ab", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371676000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1ec0752-db54-4ea4-ae3b-26ea2ee3b5a2", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371701600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c629b603-4013-4657-90e5-f06103abf650", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371725000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94009046-12f4-45ba-81fb-916398774d9c", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371751700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8d244ec-3d6a-4dbe-9693-3344e85bc5f2", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371775900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f770ff3-0ca3-4115-9872-6cbdf21b7244", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371798700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ef24a2b-4f18-4a04-b4b1-8ca5d97df9e5", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371822000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adb64060-2e58-47c1-99b7-8dcb07df9d7a", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371847500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba47104e-2053-412f-9e3e-23a19e8e266b", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371870900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91c3400c-8b95-4aaa-8618-ccbb1293c46b", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371894400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "435b3c52-2ca6-4420-ad82-9f94df9a6e02", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115371953000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c05089a-b223-40c3-a6ad-2b718f7fb7a8", "name": "default@CompileArkTS work[22] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115372745800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33697814-b5ed-4d0f-8e2a-c60bb9e87f3e", "name": "default@CompileArkTS work[22] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115372838800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b208388-73c7-4ed1-a063-9f8eb7abaa88", "name": "CopyResources startTime: 156115372886600", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115372888500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "962e4270-a4e9-4f30-a5b8-08a7cff6b34e", "name": "default@CompileArkTS work[23] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115372951400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55064940-24fd-4b9d-9879-ffb573ca6fb4", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 156116509127800, "endTime": 156116522658500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "aa73d583-193a-41d9-b7fb-b1883e893190", "logId": "eb4d56ae-b87b-4731-ad78-1f8b2c08ab9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d834ad75-d03e-4b18-ac40-6271b51e7f98", "name": "default@CompileArkTS work[23] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115373817500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "714f9602-f438-46d2-ad79-bbba369289ce", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115373908200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a3b8331-a52c-43eb-b627-c64f079fe7a2", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115373974000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72fdbcd3-519e-4e8c-8614-d57bbcf0cc06", "name": "default@CompileArkTS work[23] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115374716000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82cb274a-5508-490b-8b50-a3e07ab22bd7", "name": "default@CompileArkTS work[23] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115374799600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95e7102b-3e72-4702-9ebc-5f5ff0d589d6", "name": "entry : default@CompileArkTS cost memory 1.8208770751953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115374872300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f8d79eb-1cd7-4e3f-a5fe-3985a56a5b10", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115382466300, "endTime": 156115389975200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "43f3b040-6318-463c-8cbb-d3df9f2d7771", "logId": "3498785c-0a98-410f-923e-e765273364c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43f3b040-6318-463c-8cbb-d3df9f2d7771", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115377146000}, "additional": {"logType": "detail", "children": [], "durationId": "8f8d79eb-1cd7-4e3f-a5fe-3985a56a5b10"}}, {"head": {"id": "3e3d2c44-56f7-4fa0-ae80-769e41077743", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115378171900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca4e18bd-303b-455f-9f07-24b2114d8011", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115378300400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa9f9f76-f315-4c6d-b1e6-26ff31ffc473", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115382477800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bb18102-d3b1-48d2-b4d7-5da8d9ee4121", "name": "entry : default@BuildJS cost memory 0.337982177734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115389751900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fb0ac22-7efa-443e-bafa-0a3043802d5e", "name": "runTaskFromQueue task cost before running: 515 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115389912100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3498785c-0a98-410f-923e-e765273364c4", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115382466300, "endTime": 156115389975200, "totalTime": 7422100}, "additional": {"logType": "info", "children": [], "durationId": "8f8d79eb-1cd7-4e3f-a5fe-3985a56a5b10"}}, {"head": {"id": "6627112d-b630-4864-a8bb-391bf2b134d4", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115395203000, "endTime": 156115399062100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "56290e81-e190-4a86-9d95-f2cd944bc591", "logId": "a3751fe5-bae9-4b7e-b898-9794312a0145"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56290e81-e190-4a86-9d95-f2cd944bc591", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115391576600}, "additional": {"logType": "detail", "children": [], "durationId": "6627112d-b630-4864-a8bb-391bf2b134d4"}}, {"head": {"id": "eb4cce75-3156-454c-a798-04132874fc87", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115392590200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3acccddc-3453-4140-a60e-e491f0218f42", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115392694700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621ddc14-0984-4f33-ace4-776e9af33b5d", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115395213400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bedeadeb-bbdf-4713-b39b-1cf1420ceda6", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115395989200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8143cf71-0394-4e3c-ae82-0430cc9b11c9", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115398813200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8c7cf8b-5a36-4871-97f5-9a5a575eee66", "name": "entry : default@CacheNativeLibs cost memory 0.094482421875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115398979500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3751fe5-bae9-4b7e-b898-9794312a0145", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115395203000, "endTime": 156115399062100}, "additional": {"logType": "info", "children": [], "durationId": "6627112d-b630-4864-a8bb-391bf2b134d4"}}, {"head": {"id": "c48c720a-05bc-4c6d-b6d9-785c34f81734", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156116522923900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18485af0-84cd-41b1-9055-74b10d24d69a", "name": "CopyResources is end, endTime: 156116523099800", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156116523130100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93ba9309-9233-4de0-a7e2-8a2b2c8bfa24", "name": "default@CompileArkTS work[23] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156116523223000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb4d56ae-b87b-4731-ad78-1f8b2c08ab9a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 156116509127800, "endTime": 156116522658500}, "additional": {"logType": "info", "children": [], "durationId": "55064940-24fd-4b9d-9879-ffb573ca6fb4", "parent": "f9775d87-35bf-4dce-a506-72bd20e97372"}}, {"head": {"id": "896e079c-898f-41b8-b05b-fc34573c4285", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156116523291400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53f9dddf-a3c3-4763-b7e9-c7a319c1b734", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125783510600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa0feb5f-477c-4700-9096-83855b2807c4", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 156118152514400, "endTime": 156119319908200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f4b83f7f-59a8-4943-9119-05adb402cad1", "logId": "8157ea94-ac20-4096-a93f-cbf887181e97"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8157ea94-ac20-4096-a93f-cbf887181e97", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156118152514400, "endTime": 156119319908200}, "additional": {"logType": "info", "children": [], "durationId": "aa0feb5f-477c-4700-9096-83855b2807c4", "parent": "95f69ee4-6746-48da-8787-deedbc3c8801"}}, {"head": {"id": "c70773f3-251a-4440-9b8d-b7b85695bb1b", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 156119321504100, "endTime": 156119382324200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f4b83f7f-59a8-4943-9119-05adb402cad1", "logId": "69697065-94cb-4173-b6d7-f877d2477638"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69697065-94cb-4173-b6d7-f877d2477638", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156119321504100, "endTime": 156119382324200}, "additional": {"logType": "info", "children": [], "durationId": "c70773f3-251a-4440-9b8d-b7b85695bb1b", "parent": "95f69ee4-6746-48da-8787-deedbc3c8801"}}, {"head": {"id": "5630b945-0154-4b10-8557-33bc4f42ec3b", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 156119382468800, "endTime": 156119382797200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f4b83f7f-59a8-4943-9119-05adb402cad1", "logId": "2651d03c-6990-4434-8fb0-e247d4b6a8c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2651d03c-6990-4434-8fb0-e247d4b6a8c1", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156119382468800, "endTime": 156119382797200}, "additional": {"logType": "info", "children": [], "durationId": "5630b945-0154-4b10-8557-33bc4f42ec3b", "parent": "95f69ee4-6746-48da-8787-deedbc3c8801"}}, {"head": {"id": "185b28d7-4ef1-4b81-9c83-b56c71ae0e98", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 156119382876900, "endTime": 156125542886700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f4b83f7f-59a8-4943-9119-05adb402cad1", "logId": "d4a7328f-8b97-4f37-a6f3-14afd1264443"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4a7328f-8b97-4f37-a6f3-14afd1264443", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156119382876900, "endTime": 156125542886700}, "additional": {"logType": "info", "children": [], "durationId": "185b28d7-4ef1-4b81-9c83-b56c71ae0e98", "parent": "95f69ee4-6746-48da-8787-deedbc3c8801"}}, {"head": {"id": "e4118ca1-3302-41c3-a175-ba0dca309c6e", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 156125543142600, "endTime": 156125558953700}, "additional": {"children": ["2e215d0d-17e9-48cc-a3b5-b54a018d960a", "c538d2c3-9c1a-41d9-9511-42754e575387", "aeff0f5f-3bfc-4624-9142-ef237480c668"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f4b83f7f-59a8-4943-9119-05adb402cad1", "logId": "53e0a500-688a-4165-89fc-5617cac825db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53e0a500-688a-4165-89fc-5617cac825db", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125543142600, "endTime": 156125558953700}, "additional": {"logType": "info", "children": ["4f97a6ec-cf63-4d98-806f-f23089516c2d", "dd53c503-85b5-4b98-adab-6d0b6a99833d", "b20e35b9-609d-4d2b-b40d-e7de1022cf8a"], "durationId": "e4118ca1-3302-41c3-a175-ba0dca309c6e", "parent": "95f69ee4-6746-48da-8787-deedbc3c8801"}}, {"head": {"id": "2e215d0d-17e9-48cc-a3b5-b54a018d960a", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 156125543309500, "endTime": 156125543327800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e4118ca1-3302-41c3-a175-ba0dca309c6e", "logId": "4f97a6ec-cf63-4d98-806f-f23089516c2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f97a6ec-cf63-4d98-806f-f23089516c2d", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125543309500, "endTime": 156125543327800}, "additional": {"logType": "info", "children": [], "durationId": "2e215d0d-17e9-48cc-a3b5-b54a018d960a", "parent": "53e0a500-688a-4165-89fc-5617cac825db"}}, {"head": {"id": "c538d2c3-9c1a-41d9-9511-42754e575387", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 156125543337000, "endTime": 156125551408300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e4118ca1-3302-41c3-a175-ba0dca309c6e", "logId": "dd53c503-85b5-4b98-adab-6d0b6a99833d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd53c503-85b5-4b98-adab-6d0b6a99833d", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125543337000, "endTime": 156125551408300}, "additional": {"logType": "info", "children": [], "durationId": "c538d2c3-9c1a-41d9-9511-42754e575387", "parent": "53e0a500-688a-4165-89fc-5617cac825db"}}, {"head": {"id": "aeff0f5f-3bfc-4624-9142-ef237480c668", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 156125551508800, "endTime": 156125558764700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e4118ca1-3302-41c3-a175-ba0dca309c6e", "logId": "b20e35b9-609d-4d2b-b40d-e7de1022cf8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b20e35b9-609d-4d2b-b40d-e7de1022cf8a", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125551508800, "endTime": 156125558764700}, "additional": {"logType": "info", "children": [], "durationId": "aeff0f5f-3bfc-4624-9142-ef237480c668", "parent": "53e0a500-688a-4165-89fc-5617cac825db"}}, {"head": {"id": "02930b31-73aa-46a0-b4bd-c36233b81c98", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 156125559010800, "endTime": 156125780256400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f4b83f7f-59a8-4943-9119-05adb402cad1", "logId": "b6e53af9-bd49-4de8-ada1-5bd7a7026271"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6e53af9-bd49-4de8-ada1-5bd7a7026271", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125559010800, "endTime": 156125780256400}, "additional": {"logType": "info", "children": [], "durationId": "02930b31-73aa-46a0-b4bd-c36233b81c98", "parent": "95f69ee4-6746-48da-8787-deedbc3c8801"}}, {"head": {"id": "923e696f-0ded-438f-989e-94107260119c", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 156116430919700, "endTime": 156118149791400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f4b83f7f-59a8-4943-9119-05adb402cad1", "logId": "8a6ce0fa-e59c-4c93-aca2-0d7b5042adf5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a6ce0fa-e59c-4c93-aca2-0d7b5042adf5", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156116430919700, "endTime": 156118149791400}, "additional": {"logType": "info", "children": [], "durationId": "923e696f-0ded-438f-989e-94107260119c", "parent": "95f69ee4-6746-48da-8787-deedbc3c8801"}}, {"head": {"id": "e4bdb0d4-005d-41f2-a97a-3a9e9b876ac3", "name": "default@CompileArkTS work[22] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125789088300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95f69ee4-6746-48da-8787-deedbc3c8801", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 156118151521100, "endTime": 156125783033600}, "additional": {"logType": "info", "children": ["8157ea94-ac20-4096-a93f-cbf887181e97", "69697065-94cb-4173-b6d7-f877d2477638", "2651d03c-6990-4434-8fb0-e247d4b6a8c1", "d4a7328f-8b97-4f37-a6f3-14afd1264443", "53e0a500-688a-4165-89fc-5617cac825db", "b6e53af9-bd49-4de8-ada1-5bd7a7026271", "8a6ce0fa-e59c-4c93-aca2-0d7b5042adf5"], "durationId": "f4b83f7f-59a8-4943-9119-05adb402cad1", "parent": "f9775d87-35bf-4dce-a506-72bd20e97372"}}, {"head": {"id": "6803d46b-ce40-44a4-934a-d02bbc3be461", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125789306800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9775d87-35bf-4dce-a506-72bd20e97372", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156115335979600, "endTime": 156125789467700, "totalTime": 7683986000}, "additional": {"logType": "info", "children": ["95f69ee4-6746-48da-8787-deedbc3c8801", "eb4d56ae-b87b-4731-ad78-1f8b2c08ab9a"], "durationId": "aa73d583-193a-41d9-b7fb-b1883e893190"}}, {"head": {"id": "72e99b16-d1f0-44f6-8831-0774218f559a", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125797361200, "endTime": 156125798536400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "2e72beab-bf84-48d0-8b9b-4d21aed48fdf", "logId": "f2697262-c08f-4cf0-81a7-40f1212681c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e72beab-bf84-48d0-8b9b-4d21aed48fdf", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125795498600}, "additional": {"logType": "detail", "children": [], "durationId": "72e99b16-d1f0-44f6-8831-0774218f559a"}}, {"head": {"id": "d5d4f563-4aae-49da-9835-23fd90908e06", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125796418700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75498e5d-9e97-4684-b518-c15bf079ad08", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125796502100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1714fc44-9fbd-4005-909c-1a26e69a2484", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125797369500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4250b8d3-85f6-4f17-b774-453e34a678b8", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125797636000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5793f5d-ec46-41ba-bd5f-d4000817a60c", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125798395000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "703b3394-0454-4e3d-aa7d-73298348402f", "name": "entry : default@GeneratePkgModuleJson cost memory 0.0775146484375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125798485100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2697262-c08f-4cf0-81a7-40f1212681c9", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125797361200, "endTime": 156125798536400}, "additional": {"logType": "info", "children": [], "durationId": "72e99b16-d1f0-44f6-8831-0774218f559a"}}, {"head": {"id": "3c568270-44a9-4efb-a7c2-eee1557fb659", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125807380000, "endTime": 156125947187900}, "additional": {"children": ["fc9bc678-247c-43dd-b368-2dbee0c282f2", "f9e5a899-5d30-4417-a5f3-b18baf288d23"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "85aa0f06-cdaa-472c-9bc2-ce24f560b50d", "logId": "3bc150f4-a6eb-4db2-a3c0-15414c920b74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85aa0f06-cdaa-472c-9bc2-ce24f560b50d", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125800466600}, "additional": {"logType": "detail", "children": [], "durationId": "3c568270-44a9-4efb-a7c2-eee1557fb659"}}, {"head": {"id": "8146eb0c-085b-4d94-a642-c56d55a16c21", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125801231600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c5b7282-5d7b-46c6-a57d-14b2e2be2c11", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125801316700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb458229-a8a2-492c-8481-0554808b61bb", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125807389500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "902c3d23-68de-48c6-9c9c-7f7ca04eabbd", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125823255300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4247a7e9-df86-4246-b7f4-a698be81516d", "name": "Incremental task entry:default@PackageHap pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125823428000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62981dd1-c6cd-4a8e-9e68-5dc1d98ce0d6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125823502100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04c16926-0cdf-461e-a81a-6e53126d5458", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125823533400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc9bc678-247c-43dd-b368-2dbee0c282f2", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125825308300, "endTime": 156125827443500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c568270-44a9-4efb-a7c2-eee1557fb659", "logId": "88767ef3-7ade-4a5e-85d8-fba442e34058"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c5fe219-6bba-4bc3-920d-c8bc9691f98b", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125827280600}, "additional": {"logType": "debug", "children": [], "durationId": "3c568270-44a9-4efb-a7c2-eee1557fb659"}}, {"head": {"id": "88767ef3-7ade-4a5e-85d8-fba442e34058", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125825308300, "endTime": 156125827443500}, "additional": {"logType": "info", "children": [], "durationId": "fc9bc678-247c-43dd-b368-2dbee0c282f2", "parent": "3bc150f4-a6eb-4db2-a3c0-15414c920b74"}}, {"head": {"id": "f9e5a899-5d30-4417-a5f3-b18baf288d23", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125827970700, "endTime": 156125941496600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c568270-44a9-4efb-a7c2-eee1557fb659", "logId": "6b4b1812-1c2a-46e7-8172-fba2fa09349f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce587291-6dad-4705-9014-f0a017cdc1ab", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125940819900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b4b1812-1c2a-46e7-8172-fba2fa09349f", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125827970700, "endTime": 156125941487200}, "additional": {"logType": "info", "children": [], "durationId": "f9e5a899-5d30-4417-a5f3-b18baf288d23", "parent": "3bc150f4-a6eb-4db2-a3c0-15414c920b74"}}, {"head": {"id": "a622b685-63d7-4589-b545-88f7b938c1bc", "name": "entry : default@PackageHap cost memory -0.050567626953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125946962700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59ba7a4c-ee36-4a64-9a1b-45606bb5e20e", "name": "runTaskFromQueue task cost before running: 11 s 72 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125947123400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bc150f4-a6eb-4db2-a3c0-15414c920b74", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125807380000, "endTime": 156125947187900, "totalTime": 139720500}, "additional": {"logType": "info", "children": ["88767ef3-7ade-4a5e-85d8-fba442e34058", "6b4b1812-1c2a-46e7-8172-fba2fa09349f"], "durationId": "3c568270-44a9-4efb-a7c2-eee1557fb659"}}, {"head": {"id": "7b1932a4-5f23-4652-a10c-9d452d64ea81", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125953720900, "endTime": 156126229590000}, "additional": {"children": ["31871f20-a8f8-45d1-9e1b-875f49fb4549", "5922a611-373a-4498-b11c-cb4d01157d0f"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "bef2fb41-1cf1-43f9-aae4-8d91120b62fe", "logId": "3402410e-bc98-4a46-99d2-f1715ca2e67a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bef2fb41-1cf1-43f9-aae4-8d91120b62fe", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125950311900}, "additional": {"logType": "detail", "children": [], "durationId": "7b1932a4-5f23-4652-a10c-9d452d64ea81"}}, {"head": {"id": "bd0fbfb5-8252-460e-9724-54dd2654a72d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125951080300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "806d2c67-6199-4d23-a4aa-c5cd6803ac76", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125951163300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a55a5562-2f68-47e5-8807-310c36206f5a", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125953730900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af669f55-e750-4854-a2b8-12007ccaa706", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125955200000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a943215-bd67-40e4-a69d-215defcdfeb2", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125955301700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2aa2b52-a163-4a04-b14b-6321837dff9a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125955362700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d5cc151-1fce-4efb-8932-8ad020d23c81", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125955391100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31871f20-a8f8-45d1-9e1b-875f49fb4549", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125956786900, "endTime": 156126045994600}, "additional": {"children": ["a5bc5e49-b2dc-443a-a10e-38f301af67d3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b1932a4-5f23-4652-a10c-9d452d64ea81", "logId": "6ec39a3d-0347-4fd2-81d9-c1695895611e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5bc5e49-b2dc-443a-a10e-38f301af67d3", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125974765700, "endTime": 156126044585500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "31871f20-a8f8-45d1-9e1b-875f49fb4549", "logId": "1b3e6834-e4e9-44b0-9e40-caf2578a3a6f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46415cc9-724d-4c00-86ab-7e362cd76c9b", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125977947500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0503f065-39ee-4a9c-a80a-c842491ec3c1", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126044098700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b3e6834-e4e9-44b0-9e40-caf2578a3a6f", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125974765700, "endTime": 156126044585500}, "additional": {"logType": "info", "children": [], "durationId": "a5bc5e49-b2dc-443a-a10e-38f301af67d3", "parent": "6ec39a3d-0347-4fd2-81d9-c1695895611e"}}, {"head": {"id": "6ec39a3d-0347-4fd2-81d9-c1695895611e", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125956786900, "endTime": 156126045994600}, "additional": {"logType": "info", "children": ["1b3e6834-e4e9-44b0-9e40-caf2578a3a6f"], "durationId": "31871f20-a8f8-45d1-9e1b-875f49fb4549", "parent": "3402410e-bc98-4a46-99d2-f1715ca2e67a"}}, {"head": {"id": "5922a611-373a-4498-b11c-cb4d01157d0f", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126046741900, "endTime": 156126229118700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7b1932a4-5f23-4652-a10c-9d452d64ea81", "logId": "730f38e2-c7e8-4eb4-80ab-d56af463eb51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9559b519-d690-4136-a3e4-8583f5290cc5", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126049037100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f678526a-722f-427c-84be-b2bb6ce99f27", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126228659800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "730f38e2-c7e8-4eb4-80ab-d56af463eb51", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126046741900, "endTime": 156126229118700}, "additional": {"logType": "info", "children": [], "durationId": "5922a611-373a-4498-b11c-cb4d01157d0f", "parent": "3402410e-bc98-4a46-99d2-f1715ca2e67a"}}, {"head": {"id": "befff29c-563a-4fd5-8789-30df4f0cf3dc", "name": "entry : default@SignHap cost memory 0.12950897216796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126229390500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eb04a11-f62f-4378-afff-c4e619f49997", "name": "runTaskFromQueue task cost before running: 11 s 354 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126229524500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3402410e-bc98-4a46-99d2-f1715ca2e67a", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156125953720900, "endTime": 156126229590000, "totalTime": 275778200}, "additional": {"logType": "info", "children": ["6ec39a3d-0347-4fd2-81d9-c1695895611e", "730f38e2-c7e8-4eb4-80ab-d56af463eb51"], "durationId": "7b1932a4-5f23-4652-a10c-9d452d64ea81"}}, {"head": {"id": "b893d0a7-c41a-43dc-9280-780686233826", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126233568700, "endTime": 156126238749300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0893884e-ec5c-4b6e-9eb8-30d6c10bbf9c", "logId": "65479e43-bd40-44dc-8364-0c55b20b43ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0893884e-ec5c-4b6e-9eb8-30d6c10bbf9c", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126231695400}, "additional": {"logType": "detail", "children": [], "durationId": "b893d0a7-c41a-43dc-9280-780686233826"}}, {"head": {"id": "7635f2d0-c3be-4d16-9515-820737074064", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126232667100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06627d59-7aad-46a0-bf5d-da27daa6f5ca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126232782800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e689ba1d-68db-4d51-b279-216e76ad9004", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126233578000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e50e6aa-e4c6-494d-aac0-f2a6354222fe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126238446000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "091e23d8-86c1-48c5-b249-a63bb94a842d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126238541100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40fc3166-a246-4024-aca5-cb380d329436", "name": "entry : default@CollectDebugSymbol cost memory 0.2402801513671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126238636300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "780772cb-063b-45a9-a0b2-72ec9797533b", "name": "runTaskFromQueue task cost before running: 11 s 363 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126238706400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65479e43-bd40-44dc-8364-0c55b20b43ce", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126233568700, "endTime": 156126238749300, "totalTime": 5121100}, "additional": {"logType": "info", "children": [], "durationId": "b893d0a7-c41a-43dc-9280-780686233826"}}, {"head": {"id": "eec00536-6729-4980-8d85-a8b030fbc822", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126240200200, "endTime": 156126240475400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "9b5a56a0-44f5-417e-a586-0de9c492f142", "logId": "b5f49b9e-1b6f-4553-93b9-d20d804aab2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b5a56a0-44f5-417e-a586-0de9c492f142", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126240160900}, "additional": {"logType": "detail", "children": [], "durationId": "eec00536-6729-4980-8d85-a8b030fbc822"}}, {"head": {"id": "1374bbde-6002-43b7-9ee5-b79b466210c0", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126240205300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e68d9cb-1838-4ea1-9be0-b72082e1b69b", "name": "entry : assembleHap cost memory 0.01165771484375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126240357400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83e3ecc6-1f51-4ed6-ac51-44558b38f78c", "name": "runTaskFromQueue task cost before running: 11 s 365 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126240433100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5f49b9e-1b6f-4553-93b9-d20d804aab2f", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126240200200, "endTime": 156126240475400, "totalTime": 216100}, "additional": {"logType": "info", "children": [], "durationId": "eec00536-6729-4980-8d85-a8b030fbc822"}}, {"head": {"id": "4109a41d-fc07-44f8-a450-e38150424040", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126254396000, "endTime": 156126254421900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5463ca82-265a-4548-9bc8-8171073abb46", "logId": "710d0f7a-63ec-4586-b776-05171272a0f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "710d0f7a-63ec-4586-b776-05171272a0f1", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126254396000, "endTime": 156126254421900}, "additional": {"logType": "info", "children": [], "durationId": "4109a41d-fc07-44f8-a450-e38150424040"}}, {"head": {"id": "638df22d-4df0-4365-9694-a4732e3cab4b", "name": "BUILD SUCCESSFUL in 11 s 379 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126254505400}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "8bfc6754-fbd3-4e07-a321-2daa0deeb30d", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156114875744000, "endTime": 156126254967700}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 19, "second": 16}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "ef886b27-46ad-4a4c-91ae-b97355339f60", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126255060400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27aff842-5ba7-4fb8-ada8-916353c00f7a", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126255156700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57e2f552-dabc-4f4e-89b8-8a2754fc88ca", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126255558500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09465961-0871-46ff-aa01-824173db1b94", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126255628600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a29fc0ee-a3e3-401b-bb87-79712fd5f483", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126255672900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2877a50-9e81-41ed-b638-8832e4930bab", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126255739100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8fff53f-b90f-416a-a940-0c59cd65a8a5", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126255768300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9befdb8f-c91b-46ef-bdb1-8595f15e0ff3", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126256325000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18c622f4-1425-424d-aac8-97c624e896bc", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126256539200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a68b66d9-5973-4667-ba7b-4c9f372c7cdb", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126256588200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1ad3893-c7d2-4c2e-a850-e9fbdda705e3", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126256627100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c85720f0-533e-4457-916e-a27b3942ac37", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126256663300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c0deb24-6c02-46e3-b9ab-a728759a5ded", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126256690500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a671f647-a413-4632-a5ce-22bccec6ea83", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126257663200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca95d6e3-1fdc-4c05-9e69-2fe4b704d03a", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126257921300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0e45452-74ea-4f3b-89a1-e6524c3c1f6d", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126258134000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b688baac-4b2c-49c9-8e37-11ab9684622d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126258223100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df7b31c6-dc33-4b2b-aaf9-014c352d9fcb", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126258269400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4c21e1a-ceae-4791-888d-e9c82e857ec9", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126258303300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebffaa5c-1093-41ac-ab62-79c83c9c68dd", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126258336000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d7f70c2-f626-4fb7-8134-7f6fed9fdd6c", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126258366800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5936322-c9c5-42b3-833c-b0ddffa22a4b", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126261648300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "195ed39d-90bd-4ccc-b59f-4f06fb4eb84a", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126262358100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fb22e46-2999-40a5-b843-736284e067c1", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126262800300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6650f1fb-9ba4-4ac3-9add-2f411d881f51", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126263045100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd6fea2b-bd17-49ea-8d77-b5a1cccada64", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126263306200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05534d53-ae13-40de-8b33-f7d997e8ec9c", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126264042200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3941f09f-202c-4f3e-8aeb-26c7558656d0", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126271935400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d463a7d-7663-4d5f-ab88-ac31bdbb5cdf", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126272235100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea300211-8cb9-4ff9-ac9d-5b2bc783a77c", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126272622500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13c3d127-6907-4e2d-8b26-74cfb5cd85a0", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126273502100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5046593d-da6f-4c1f-8586-613b74e75956", "name": "Incremental task entry:default@CompileArkTS post-execution cost:16 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126274354800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae7ba369-a976-4a0f-b7ea-c84d83e8ed19", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126276504900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f8baf59-8c42-49df-be71-50d34f6f72ea", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126277227500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61ea89cc-41af-45f2-89e3-d99e9b919d4d", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126277669300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e6aa8ec-c300-4251-a473-cd894820a13d", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126277979700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0625664-f256-46d4-b28e-dfb85c3a1d02", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126278220800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88b9d8c9-cf09-4ade-a16a-3e415e5a3cc6", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126278986100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6043b30-a5bf-4de4-b8e1-80b62a8b11b2", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126279866400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ce9d7e9-ab3f-4b3c-bc8c-bdffe59ec8db", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126280181500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13910ce2-4548-4629-aeaa-5ff51062c02c", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126280253300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14f90d5f-a32f-4c93-9efa-5023eeffeb2e", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126280300100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef05a332-9088-444a-aaf2-7000e37a8e00", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126282843700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c590accc-0d86-40ea-b53b-fd463bec9f71", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126283539500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d63663df-6fe0-449c-bd83-074870c2416e", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126283884000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af8fc9a0-8c6f-4efe-ad29-303c2fc90ff5", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126292553900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0adb5fa8-88d6-4d8d-972e-ef34b55882ac", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126292987600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2511516-2ded-4c71-b692-a33e3bc00a00", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126293343800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04ace7a0-f244-4509-be32-dfb090e4c90b", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126293647500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03cc03cf-dfde-48e0-b487-8f7e162d3c45", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126293726900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be2ee938-289e-471d-a71a-19fb986c37cd", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126293964100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5034902-fd35-454a-a1c2-9beb5637d366", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126294236100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cb4242b-4bf7-4362-a3a4-445e3a0bbb8e", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126295227200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d563a9e0-971c-468d-9cce-b39498cd50ae", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126295535500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0500a68-8fba-4f02-a2a3-1cc783450715", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126295796800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1257359b-aa78-4310-8e27-66b93049522d", "name": "Incremental task entry:default@PackageHap post-execution cost:16 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126296110400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a8454ee-7831-4b1f-be63-2567e68aecfa", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126296363800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5af436a8-ee05-4d32-b809-fd7bd6e300cc", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126296612600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55a55bfd-9d71-475f-a7b1-07d33b99ac2b", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126296967400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0d1a813-ca5c-4a06-8738-0ac7cc481f5d", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126297341800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3a840e7-adb2-4767-9ec4-8e94badab405", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126297436800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f795317-a5f2-4f89-9501-1ab0bad0f2d3", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126297816900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c04d5ee1-91f6-4c80-a8ba-b651761a3912", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126300874300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89948ea7-e39a-42c2-a990-711ea50af689", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126301235000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a856cc3d-5a2a-4430-ba5c-7e4cbe3afbb4", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126301772900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09fe0b84-347a-444e-9fa0-ff6ce6b4ef1b", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156126302049300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}