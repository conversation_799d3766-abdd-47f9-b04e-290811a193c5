package api

import (
	"fmt"
	"strconv"
	"time"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/internal/services"
	"nexushub-oh-back/pkg/logger"
	"nexushub-oh-back/pkg/storage"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// DeveloperController 开发者控制器
type DeveloperController struct {
	DB                  *gorm.DB
	StorageClient       storage.StorageProvider
	Validate            *validator.Validate
	MessageService      *services.MessageService
	NotificationService *services.NotificationService
}

// NewDeveloperController 创建开发者控制器
func NewDeveloperController(db *gorm.DB, storageClient storage.StorageProvider, messageService *services.MessageService, notificationService *services.NotificationService) *DeveloperController {
	return &DeveloperController{
		DB:                  db,
		StorageClient:       storageClient,
		Validate:            validator.New(),
		MessageService:      messageService,
		NotificationService: notificationService,
	}
}

// DeveloperVerifyRequest 开发者认证请求
type DeveloperVerifyRequest struct {
	DeveloperName    string `json:"developer_name" validate:"required,min=2,max=100"`
	CompanyName      string `json:"company_name" validate:"omitempty,max=100"`
	Website          string `json:"website" validate:"omitempty,url,max=255"`
	Description      string `json:"description" validate:"required,min=10,max=1000"`
	ContactEmail     string `json:"contact_email" validate:"required,email"`
	ContactPhone     string `json:"contact_phone" validate:"required,len=11"`
	BusinessLicense  string `json:"business_license" validate:"omitempty,max=500"`
	IdentityCard     string `json:"identity_card" validate:"required,max=500"`
	DeveloperAvatar  string `json:"developer_avatar" validate:"omitempty,max=500"`
	DeveloperAddress string `json:"developer_address" validate:"required,min=5,max=255"`
}

// DeveloperVerifyResponse 开发者认证响应
type DeveloperVerifyResponse struct {
	ID               uint      `json:"id"`
	Username         string    `json:"username"`
	DeveloperName    string    `json:"developer_name"`
	CompanyName      string    `json:"company_name"`
	Website          string    `json:"website"`
	Description      string    `json:"description"`
	ContactEmail     string    `json:"contact_email"`
	ContactPhone     string    `json:"contact_phone"`
	BusinessLicense  string    `json:"business_license"`
	IdentityCard     string    `json:"identity_card"`
	DeveloperAvatar  string    `json:"developer_avatar"`
	DeveloperAddress string    `json:"developer_address"`
	VerifyStatus     string    `json:"verify_status"`
	VerifyReason     string    `json:"verify_reason"`
	SubmittedAt      time.Time `json:"submitted_at"`
	VerifiedAt       time.Time `json:"verified_at"`
}

// DeveloperStatsResponse 开发者统计响应
type DeveloperStatsResponse struct {
	TotalApplications int    `json:"total_applications"`
	PendingCount      int    `json:"pending_count"`
	ApprovedCount     int    `json:"approved_count"`
	RejectedCount     int    `json:"rejected_count"`
	TodayApplications int    `json:"today_applications"`
	ApprovalRate      string `json:"approval_rate"`
}

// RecentApplicationResponse 最近申请响应
type RecentApplicationResponse struct {
	ID              uint      `json:"id"`
	Username        string    `json:"username"`
	DeveloperName   string    `json:"developer_name"`
	CompanyName     *string   `json:"company_name"`
	VerifyStatus    string    `json:"verify_status"`
	SubmittedAt     time.Time `json:"submitted_at"`
	DeveloperAvatar *string   `json:"developer_avatar"`
}

// AdminVerifyRequest 管理员审核请求
type AdminVerifyRequest struct {
	VerifyStatus string `json:"verify_status" validate:"required,oneof=pending approved rejected"`
	VerifyReason string `json:"verify_reason" validate:"required_if=VerifyStatus rejected,max=255"`
}

// UploadResult 文件上传结果
type UploadResult struct {
	FileURL string `json:"file_url"`
}

// SubmitVerify 提交开发者认证
//	@Summary		提交开发者认证
//	@Description	用户提交开发者认证申请
//	@Tags			开发者
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			data	body		DeveloperVerifyRequest					true	"开发者认证信息"
//	@Success		200		{object}	Response{data=DeveloperVerifyResponse}	"提交成功，返回认证信息"
//	@Failure		400		{object}	Response								"参数错误"
//	@Failure		401		{object}	Response								"未授权"
//	@Failure		403		{object}	Response								"已是开发者或认证正在处理中"
//	@Failure		500		{object}	Response								"服务器错误"
//	@Router			/developers/verify [post]
func (c *DeveloperController) SubmitVerify(ctx *gin.Context) {
	var req DeveloperVerifyRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		ParamError(ctx, err.Error())
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "")
		return
	}

	// 获取用户信息
	var user models.User
	if err := c.DB.First(&user, userID).Error; err != nil {
		ServerError(ctx, "获取用户信息失败", err)
		return
	}

	// 检查用户是否已经是开发者或者有认证正在处理中
	if user.IsDeveloper {
		Fail(ctx, 403, "您已经是开发者")
		return
	}

	if user.VerifyStatus == models.VerifyStatusPending {
		Fail(ctx, 403, "您的认证正在审核中，请耐心等待")
		return
	}

	// 更新用户信息
	user.DeveloperName = req.DeveloperName
	user.CompanyName = req.CompanyName
	user.Website = req.Website
	user.Description = req.Description
	user.ContactEmail = req.ContactEmail
	user.ContactPhone = req.ContactPhone
	user.BusinessLicense = req.BusinessLicense
	user.IdentityCard = req.IdentityCard
	user.DeveloperAvatar = req.DeveloperAvatar
	user.DeveloperAddress = req.DeveloperAddress
	user.VerifyStatus = models.VerifyStatusPending
	user.SubmittedAt = time.Now()

	// 保存用户信息
	if err := c.DB.Save(&user).Error; err != nil {
		logger.Error("保存用户信息失败", zap.Error(err))
		ServerError(ctx, "提交认证失败，请稍后重试", err)
		return
	}

	// 通知所有管理员有新的开发者认证申请
	go func() {
		if c.NotificationService != nil {
			if err := c.NotificationService.NotifyAdminsForDeveloperSubmission(
				ctx.Request.Context(),
				user.ID,
				user.DeveloperName,
				user.Username,
			); err != nil {
				logger.Error("通知管理员失败", zap.Error(err), zap.Uint("user_id", user.ID))
			}
		}
	}()

	SuccessWithMessage(ctx, "提交成功，请等待审核", DeveloperVerifyResponse{
		ID:               user.ID,
		Username:         user.Username,
		DeveloperName:    user.DeveloperName,
		CompanyName:      user.CompanyName,
		Website:          user.Website,
		Description:      user.Description,
		ContactEmail:     user.ContactEmail,
		ContactPhone:     user.ContactPhone,
		BusinessLicense:  user.BusinessLicense,
		IdentityCard:     user.IdentityCard,
		DeveloperAvatar:  user.DeveloperAvatar,
		DeveloperAddress: user.DeveloperAddress,
		VerifyStatus:     string(user.VerifyStatus),
		VerifyReason:     user.VerifyReason,
		SubmittedAt:      user.SubmittedAt,
	})
}

// GetVerifyStatus 获取认证状态
//	@Summary		获取开发者认证状态
//	@Description	获取当前用户的开发者认证状态
//	@Tags			开发者
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Success		200	{object}	Response{data=DeveloperVerifyResponse}	"返回认证状态"
//	@Failure		401	{object}	Response								"未授权"
//	@Failure		404	{object}	Response								"用户不存在"
//	@Failure		500	{object}	Response								"服务器错误"
//	@Router			/developers/verify/status [get]
func (c *DeveloperController) GetVerifyStatus(ctx *gin.Context) {
	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "")
		return
	}

	// 获取用户信息
	var user models.User
	if err := c.DB.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "用户不存在")
		} else {
			ServerError(ctx, "获取用户信息失败", err)
		}
		return
	}

	// 构建响应数据
	var verifiedAt time.Time
	if user.VerifiedAt != nil {
		verifiedAt = *user.VerifiedAt
	}

	response := DeveloperVerifyResponse{
		ID:               user.ID,
		Username:         user.Username,
		DeveloperName:    user.DeveloperName,
		CompanyName:      user.CompanyName,
		Website:          user.Website,
		Description:      user.Description,
		ContactEmail:     user.ContactEmail,
		ContactPhone:     user.ContactPhone,
		BusinessLicense:  user.BusinessLicense,
		IdentityCard:     user.IdentityCard,
		DeveloperAvatar:  user.DeveloperAvatar,
		DeveloperAddress: user.DeveloperAddress,
		VerifyStatus:     string(user.VerifyStatus),
		VerifyReason:     user.VerifyReason,
		SubmittedAt:      user.SubmittedAt,
		VerifiedAt:       verifiedAt,
	}

	Success(ctx, response)
}

// ListPendingVerifications 获取待审核的开发者
//	@Summary		获取待审核的开发者
//	@Description	管理员获取待审核的开发者列表
//	@Tags			开发者
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			page		query		int												false	"页码，默认1"							default(1)
//	@Param			page_size	query		int												false	"每页数量，默认20"							default(20)
//	@Param			status		query		string											false	"认证状态(pending/approved/rejected)"	default("pending")
//	@Success		200			{object}	PageResponse{data=[]DeveloperVerifyResponse}	"返回待审核的开发者列表"
//	@Failure		401			{object}	Response										"未授权"
//	@Failure		403			{object}	Response										"非管理员，无权访问"
//	@Failure		500			{object}	Response										"服务器错误"
//	@Router			/admin/developers/verify [get]
func (c *DeveloperController) ListPendingVerifications(ctx *gin.Context) {
	// 注意：权限验证已在路由中间件中完成，这里不需要再次验证

	// 解析分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	status := ctx.DefaultQuery("status", "pending")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 构建查询条件
	offset := (page - 1) * pageSize
	query := c.DB.Model(&models.User{}).Where("verify_status = ?", status)

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Error("获取待审核开发者总数失败", zap.Error(err))
		ServerError(ctx, "获取列表失败，请稍后重试", err)
		return
	}

	// 获取开发者列表
	var users []models.User
	if err := query.Offset(offset).Limit(pageSize).Find(&users).Error; err != nil {
		logger.Error("获取待审核开发者列表失败", zap.Error(err))
		ServerError(ctx, "获取列表失败，请稍后重试", err)
		return
	}

	// 构建响应数据
	result := make([]DeveloperVerifyResponse, 0, len(users))
	for _, user := range users {
		var verifiedAt time.Time
		if user.VerifiedAt != nil {
			verifiedAt = *user.VerifiedAt
		}

		result = append(result, DeveloperVerifyResponse{
			ID:               user.ID,
			Username:         user.Username,
			DeveloperName:    user.DeveloperName,
			CompanyName:      user.CompanyName,
			Website:          user.Website,
			Description:      user.Description,
			ContactEmail:     user.ContactEmail,
			ContactPhone:     user.ContactPhone,
			BusinessLicense:  user.BusinessLicense,
			IdentityCard:     user.IdentityCard,
			DeveloperAvatar:  user.DeveloperAvatar,
			DeveloperAddress: user.DeveloperAddress,
			VerifyStatus:     string(user.VerifyStatus),
			VerifyReason:     user.VerifyReason,
			SubmittedAt:      user.SubmittedAt,
			VerifiedAt:       verifiedAt,
		})
	}

	SuccessWithPage(ctx, result, total, page, pageSize)
}

// VerifyDeveloper 审核开发者
//	@Summary		审核开发者
//	@Description	管理员审核开发者认证申请
//	@Tags			开发者
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id		path		int										true	"用户ID"
//	@Param			data	body		AdminVerifyRequest						true	"审核结果"
//	@Success		200		{object}	Response{data=DeveloperVerifyResponse}	"审核成功，返回更新后的认证信息"
//	@Failure		400		{object}	Response								"参数错误"
//	@Failure		401		{object}	Response								"未授权"
//	@Failure		403		{object}	Response								"非管理员，无权操作"
//	@Failure		404		{object}	Response								"用户不存在"
//	@Failure		500		{object}	Response								"服务器错误"
//	@Router			/admin/developers/{id}/verify [post]
func (c *DeveloperController) VerifyDeveloper(ctx *gin.Context) {
	// 验证是否为管理员
	userRole, exists := ctx.Get("role")
	if !exists || userRole != string(models.UserRoleAdmin) {
		Forbidden(ctx, "非管理员，无权操作")
		return
	}

	// 获取用户ID
	userID, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		ParamError(ctx, "无效的用户ID")
		return
	}

	var req AdminVerifyRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		ParamError(ctx, err.Error())
		return
	}

	// 获取用户信息
	var user models.User
	if err := c.DB.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "用户不存在")
		} else {
			ServerError(ctx, "获取用户信息失败", err)
		}
		return
	}

	// 检查用户认证状态
	if user.VerifyStatus != models.VerifyStatusPending {
		Fail(ctx, 400, "该用户不在待审核状态")
		return
	}

	// 更新用户认证状态
	now := time.Now()
	user.VerifyStatus = models.VerifyStatus(req.VerifyStatus)
	user.VerifyReason = req.VerifyReason
	user.VerifiedAt = &now

	// 根据审核结果更新用户状态和角色
	if req.VerifyStatus == string(models.VerifyStatusApproved) {
		// 审核通过：设置为开发者并更新角色
		user.IsDeveloper = true
		// 自动将用户角色从user更新为developer
		user.Role = string(models.UserRoleDeveloper)
	} else if req.VerifyStatus == string(models.VerifyStatusRejected) {
		// 审核拒绝：重置开发者状态，角色保持为user
		user.IsDeveloper = false
		// 确保角色为普通用户
		if user.Role == string(models.UserRoleDeveloper) {
			user.Role = string(models.UserRoleUser)
		}
	}

	// 保存用户信息
	if err := c.DB.Save(&user).Error; err != nil {
		logger.Error("保存用户信息失败", zap.Error(err))
		ServerError(ctx, "审核失败，请稍后重试", err)
		return
	}

	// 发送审核结果通知
	go func() {
		var title, content string
		var notificationType models.NotificationType
		if req.VerifyStatus == string(models.VerifyStatusApproved) {
			title = "开发者认证审核通过"
			content = "恭喜！您的开发者认证申请已通过审核，现在您可以上传和管理应用了。"
			notificationType = models.NotificationTypeSuccess
		} else {
			title = "开发者认证审核未通过"
			content = "很抱歉，您的开发者认证申请未通过审核。原因：" + req.VerifyReason + "。您可以重新提交申请。"
			notificationType = models.NotificationTypeWarning
		}

		// 使用通知服务发送通知
		if c.NotificationService != nil {
			notificationReq := &services.NotificationRequest{
				UserID:    user.ID,
				Title:     title,
				Content:   content,
				Type:      notificationType,
				Category:  models.CategoryDeveloperVerify,
				Priority:  models.PriorityHigh,
				SendEmail: true,
			}
			if _, err := c.NotificationService.CreateNotification(
				ctx.Request.Context(),
				notificationReq,
			); err != nil {
				logger.Error("发送审核通知失败", zap.Error(err), zap.Uint("user_id", user.ID))
			}
		}

		// 发布消息到队列
		if c.MessageService != nil {
			if err := c.MessageService.PublishNotificationMessage(
				ctx.Request.Context(),
				user.ID,
				title,
				content,
				string(notificationType),
			); err != nil {
				logger.Error("发送消息队列通知失败", zap.Error(err), zap.Uint("user_id", user.ID))
			}
		}
	}()

	SuccessWithMessage(ctx, "审核成功", DeveloperVerifyResponse{
		ID:               user.ID,
		Username:         user.Username,
		DeveloperName:    user.DeveloperName,
		CompanyName:      user.CompanyName,
		Website:          user.Website,
		Description:      user.Description,
		ContactEmail:     user.ContactEmail,
		ContactPhone:     user.ContactPhone,
		BusinessLicense:  user.BusinessLicense,
		IdentityCard:     user.IdentityCard,
		DeveloperAvatar:  user.DeveloperAvatar,
		DeveloperAddress: user.DeveloperAddress,
		VerifyStatus:     string(user.VerifyStatus),
		VerifyReason:     user.VerifyReason,
		SubmittedAt:      user.SubmittedAt,
		VerifiedAt:       *user.VerifiedAt,
	})
}

// GetUploadToken 获取文件上传凭证
//	@Summary		获取文件上传凭证
//	@Description	获取上传到对象存储的预签名URL
//	@Tags			开发者
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			file_type	query		string						true	"文件类型(avatar/license/identity/screenshot/package)"
//	@Param			file_name	query		string						true	"文件名称"
//	@Success		200			{object}	Response{data=UploadResult}	"返回上传凭证"
//	@Failure		400			{object}	Response					"参数错误"
//	@Failure		401			{object}	Response					"未授权"
//	@Failure		500			{object}	Response					"服务器错误"
//	@Router			/api/v1/upload/token [get]
func (c *DeveloperController) GetUploadToken(ctx *gin.Context) {
	// 获取参数
	fileType := ctx.Query("file_type")
	fileName := ctx.Query("file_name")

	if fileType == "" || fileName == "" {
		ParamError(ctx, "文件类型和文件名不能为空")
		return
	}

	// 验证文件类型
	validTypes := map[string]bool{
		"avatar":     true,
		"license":    true,
		"identity":   true,
		"screenshot": true,
		"package":    true,
	}

	if !validTypes[fileType] {
		ParamError(ctx, "无效的文件类型")
		return
	}

	// 获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "")
		return
	}

	// 生成存储路径
	objectKey := c.generateObjectKey(fileType, fileName, userID.(uint))

	// 生成上传URL
	fileURL, err := c.StorageClient.GetFileURL(ctx, "", objectKey, 30*60*time.Second) // 30分钟有效期
	if err != nil {
		logger.Error("生成上传URL失败", zap.Error(err))
		ServerError(ctx, "获取上传凭证失败，请稍后重试", err)
		return
	}

	Success(ctx, UploadResult{
		FileURL: fileURL,
	})
}

// 生成对象存储键
func (c *DeveloperController) generateObjectKey(fileType, fileName string, userID uint) string {
	timestamp := time.Now().Unix()
	var prefix string

	switch fileType {
	case "avatar":
		prefix = "avatars"
	case "license":
		prefix = "licenses"
	case "identity":
		prefix = "identities"
	case "screenshot":
		prefix = "screenshots"
	case "package":
		prefix = "packages"
	default:
		prefix = "others"
	}

	return prefix + "/" + strconv.FormatUint(uint64(userID), 10) + "/" + strconv.FormatInt(timestamp, 10) + "_" + fileName
}

// GetDeveloperStats 获取开发者认证统计数据
func (c *DeveloperController) GetDeveloperStats(ctx *gin.Context) {
	// 检查管理员权限
	userRole, exists := ctx.Get("role")
	if !exists || userRole != "admin" {
		Forbidden(ctx, "需要管理员权限")
		return
	}

	var stats DeveloperStatsResponse

	// 获取总申请数（只统计真正提交了申请的用户）
	var totalCount int64
	if err := c.DB.Model(&models.User{}).Where("submitted_at IS NOT NULL AND submitted_at != '0001-01-01 00:00:00+00'").Count(&totalCount).Error; err != nil {
		logger.Error("获取总申请数失败", zap.Error(err))
		ServerError(ctx, "获取统计数据失败", err)
		return
	}
	stats.TotalApplications = int(totalCount)

	// 获取各状态数量（只统计真正提交了申请的用户）
	var pendingCount, approvedCount, rejectedCount int64
	c.DB.Model(&models.User{}).Where("submitted_at IS NOT NULL AND submitted_at != '0001-01-01 00:00:00+00' AND verify_status = ?", "pending").Count(&pendingCount)
	c.DB.Model(&models.User{}).Where("submitted_at IS NOT NULL AND submitted_at != '0001-01-01 00:00:00+00' AND verify_status = ?", "approved").Count(&approvedCount)
	c.DB.Model(&models.User{}).Where("submitted_at IS NOT NULL AND submitted_at != '0001-01-01 00:00:00+00' AND verify_status = ?", "rejected").Count(&rejectedCount)

	stats.PendingCount = int(pendingCount)
	stats.ApprovedCount = int(approvedCount)
	stats.RejectedCount = int(rejectedCount)

	// 获取今日新申请数（只统计真正提交了申请的用户）
	today := time.Now().Format("2006-01-02")
	var todayCount int64
	c.DB.Model(&models.User{}).Where("submitted_at IS NOT NULL AND submitted_at != '0001-01-01 00:00:00+00' AND DATE(submitted_at) = ?", today).Count(&todayCount)
	stats.TodayApplications = int(todayCount)

	// 计算通过率
	if totalCount > 0 {
		approvalRate := float64(approvedCount) / float64(totalCount) * 100
		stats.ApprovalRate = fmt.Sprintf("%.1f%%", approvalRate)
	} else {
		stats.ApprovalRate = "0.0%"
	}

	Success(ctx, stats)
}

// GetRecentApplications 获取最近的申请
func (c *DeveloperController) GetRecentApplications(ctx *gin.Context) {
	// 检查管理员权限
	userRole, exists := ctx.Get("role")
	if !exists || userRole != "admin" {
		Forbidden(ctx, "需要管理员权限")
		return
	}

	// 获取限制数量，默认10条
	limitStr := ctx.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 50 {
		limit = 10
	}

	var users []models.User
	if err := c.DB.Where("submitted_at IS NOT NULL AND submitted_at != '0001-01-01 00:00:00+00'").
		Order("submitted_at DESC").
		Limit(limit).
		Find(&users).Error; err != nil {
		logger.Error("获取最近申请失败", zap.Error(err))
		ServerError(ctx, "获取最近申请失败", err)
		return
	}

	// 转换为响应格式
	var applications []RecentApplicationResponse
	for _, user := range users {
		application := RecentApplicationResponse{
			ID:            user.ID,
			Username:      user.Username,
			DeveloperName: user.DeveloperName,
			VerifyStatus:  string(user.VerifyStatus),
			SubmittedAt:   user.SubmittedAt,
		}

		if user.CompanyName != "" {
			application.CompanyName = &user.CompanyName
		}

		if user.DeveloperAvatar != "" {
			application.DeveloperAvatar = &user.DeveloperAvatar
		}

		applications = append(applications, application)
	}

	Success(ctx, applications)
}
