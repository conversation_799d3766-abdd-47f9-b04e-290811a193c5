import { PageContainer } from '@ant-design/pro-components';
import { Card, Descriptions, Button, Tabs, Table, Tag, Rate, Statistic, Row, Col, Avatar, Image, Typography, Divider, Space, message } from 'antd';
import { ArrowLeftOutlined, EditOutlined, DeleteOutlined, CheckOutlined, StopOutlined, DownloadOutlined, ShareAltOutlined, StarOutlined, AppstoreOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest, history, useParams } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';

// const { TabPane } = Tabs; // 已废弃，使用items属性替代
const { Title, Paragraph, Text } = Typography;

interface AppDetailData {
  id: string;
  name: string;
  icon: string;
  screenshots: string[];
  developer: string;
  developerEmail: string;
  category: string;
  status: 'published' | 'draft' | 'rejected' | 'pending';
  version: string;
  size: string;
  downloads: number;
  rating: number;
  ratingCount: number;
  price: number | 'free';
  publishDate: string;
  updateDate: string;
  description: string;
  privacyPolicy: string;
  permissions: string[];
  systemRequirements: {
    os: string;
    cpu: string;
    ram: string;
    storage: string;
  };
  releaseNotes: string;
  reviews: {
    id: string;
    username: string;
    rating: number;
    content: string;
    date: string;
  }[];
  versions: {
    version: string;
    releaseDate: string;
    notes: string;
  }[];
}

import { getAppDetail } from '@/services/app';

// 获取应用详情数据
const fetchAppDetail = async (id: string) => {
  console.log('Fetching app detail for ID:', id);
  
  try {
    // 调用API获取应用详情
    const response = await getAppDetail(id);
    console.log('App detail response:', response);
    
    if (response && response.data) {
      const appData = response.data.list[0];
      const screenshots = response.data.screenshots || [];
      const versions = response.data.versions || [];
      
      // 转换API返回的数据为组件需要的格式
      const appDetail: AppDetailData = {
        id: String(appData.id),
        name: appData.name || '',
        icon: appData.icon || '',
        screenshots: screenshots.map((s: any) => s.image_url || s),
        developer: appData.developer_name || '',
        developerEmail: '',  // API可能没有提供
        category: appData.category || '',
        status: appData.status as any || 'published',
        version: appData.current_version || '',
        size: appData.size ? `${Math.round(appData.size / 1024 / 1024)}MB` : '未知',
        downloads: appData.download_count || 0,
        rating: appData.average_rating || 0,
        ratingCount: appData.rating_count || 0,
        price: 'free',  // 假设所有应用都是免费的
        publishDate: appData.release_date ? new Date(appData.release_date).toISOString().split('T')[0] : '',
        updateDate: appData.updated_at ? new Date(appData.updated_at).toISOString().split('T')[0] : '',
        description: appData.description || '',
        privacyPolicy: appData.privacy_url || '',
        permissions: [],  // API可能没有提供
        systemRequirements: {
          os: `OpenHarmonyOS ${appData.min_open_harmony_os_ver || '未知'}+`,
          cpu: '未提供',
          ram: '未提供',
          storage: '未提供',
        },
        releaseNotes: versions.length > 0 ? versions[0].change_log : '',
        reviews: [],  // API可能没有提供
        versions: versions.map((v: any) => ({
          version: v.version_name || v.versionName,
          releaseDate: v.released_at ? new Date(v.released_at).toISOString().split('T')[0] : '',
          notes: v.change_log || v.updateDescription || '',
        })),
      };
      
      console.log('Transformed app detail:', appDetail);
      return appDetail;
    }
    
    throw new Error('获取应用详情失败');
  } catch (error) {
    console.error('Error fetching app detail:', error);
    message.error('获取应用详情失败，请稍后重试');
    throw error;
  }
};

const AppDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [activeTab, setActiveTab] = useState('basic');

  const { data, loading } = useRequest(
    async () => {
      if (!id) {
        throw new Error('应用ID不能为空');
      }
      
      console.log('Fetching app detail for ID:', id);
      
      try {
        // 调用API获取应用详情
        const response = await getAppDetail(id);
        console.log('App detail response:', response);
        
        if (response && response.data) {
          const appData = response.data.list[0];
          const screenshots = response.data.screenshots || [];
          const versions = response.data.versions || [];
          
          // 转换API返回的数据为组件需要的格式
          const appDetail: AppDetailData = {
            id: String(appData.id),
            name: appData.name || '',
            icon: appData.icon || '',
            screenshots: screenshots.map((s: any) => s.image_url || s),
            developer: appData.developer_name || '',
            developerEmail: '',  // API可能没有提供
            category: appData.category || '',
            status: appData.status as any || 'published',
            version: appData.current_version || '',
            size: appData.size ? `${Math.round(appData.size / 1024 / 1024)}MB` : '未知',
            downloads: appData.download_count || 0,
            rating: appData.average_rating || 0,
            ratingCount: appData.rating_count || 0,
            price: 'free',  // 假设所有应用都是免费的
            publishDate: appData.release_date ? new Date(appData.release_date).toISOString().split('T')[0] : '',
            updateDate: appData.updated_at ? new Date(appData.updated_at).toISOString().split('T')[0] : '',
            description: appData.description || '',
            privacyPolicy: appData.privacy_url || '',
            permissions: [],  // API可能没有提供
            systemRequirements: {
              os: `HarmonyOS ${appData.min_open_harmony_os_ver || '未知'}+`,
              cpu: '未提供',
              ram: '未提供',
              storage: '未提供',
            },
            releaseNotes: versions.length > 0 ? versions[0].change_log : '',
            reviews: [],  // API可能没有提供
            versions: versions.map((v: any) => ({
              version: v.version_name || v.versionName,
              releaseDate: v.released_at ? new Date(v.released_at).toISOString().split('T')[0] : '',
              notes: v.change_log || v.updateDescription || '',
            })),
          };
          
          console.log('Transformed app detail:', appDetail);
          return appDetail;
        }
        
        throw new Error('获取应用详情失败');
      } catch (error) {
        console.error('Error fetching app detail:', error);
        message.error('获取应用详情失败，请稍后重试');
        throw error;
      }
    },
    {
      refreshDeps: [id],
      onSuccess: (result) => {
        console.log('App detail data loaded successfully:', result);
      },
      onError: (error) => {
        console.error('App detail loading error:', error);
      }
    }
  );

  const handleBack = () => {
    history.push('/app/list');
  };

  const handleEdit = () => {
    message.success(`编辑应用 ID: ${id}`);
  };

  const handleDelete = () => {
    message.success(`删除应用 ID: ${id}`);
    history.push('/app/list');
  };

  const handleApprove = () => {
    message.success(`已通过应用 ID: ${id}`);
  };

  const handleReject = () => {
    message.success(`已拒绝应用 ID: ${id}`);
  };

  const reviewColumns: ColumnsType<AppDetailData['reviews'][0]> = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 150,
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      width: 120,
      render: (rating: number) => <Rate disabled defaultValue={rating} />,
    },
    {
      title: '评论内容',
      dataIndex: 'content',
      key: 'content',
    },
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: 120,
    },
  ];

  const versionColumns: ColumnsType<AppDetailData['versions'][0]> = [
    {
      title: '版本号',
      dataIndex: 'version',
      key: 'version',
      width: 120,
    },
    {
      title: '发布日期',
      dataIndex: 'releaseDate',
      key: 'releaseDate',
      width: 120,
    },
    {
      title: '更新内容',
      dataIndex: 'notes',
      key: 'notes',
      render: (notes: string) => (
        <div style={{ whiteSpace: 'pre-line' }}>
          {notes}
        </div>
      ),
    },
  ];

  console.log('Rendering AppDetail, data:', data, 'loading:', loading);

  return (
    <PageContainer
      header={{
        title: data?.name || '应用详情',
        subTitle: `ID: ${id}`,
        backIcon: <ArrowLeftOutlined />,
        onBack: handleBack,
        extra: [
          <Button key="edit" icon={<EditOutlined />} onClick={handleEdit}>
            编辑
          </Button>,
          <Button key="delete" danger icon={<DeleteOutlined />} onClick={handleDelete}>
            删除
          </Button>,
          data?.status === 'pending' && (
            <Button key="approve" type="primary" icon={<CheckOutlined />} onClick={handleApprove}>
              通过
            </Button>
          ),
          data?.status === 'pending' && (
            <Button key="reject" danger icon={<StopOutlined />} onClick={handleReject}>
              拒绝
            </Button>
          ),
        ],
      }}
      loading={loading}
    >
      <Card loading={loading}>
        {loading ? (
          <div>加载中...</div>
        ) : data ? (
          <div style={{ display: 'flex', marginBottom: 24 }}>
            <div style={{ marginRight: 24 }}>
              <Avatar 
            src={data.icon} 
            size={100} 
            shape="square"
            icon={<AppstoreOutlined />}
            onError={() => {
              console.warn('应用图标加载失败:', data.icon);
              return false;
            }}
          />
            </div>
            <div style={{ flex: 1 }}>
              <Title level={3}>{data.name}</Title>
              <Space size="large">
                <Text type="secondary">{data.developer}</Text>
                <Tag color="blue">{data.category}</Tag>
                {data.status === 'published' && <Tag color="green">已发布</Tag>}
                {data.status === 'pending' && <Tag color="blue">待审核</Tag>}
                {data.status === 'rejected' && <Tag color="red">已拒绝</Tag>}
                {data.status === 'draft' && <Tag color="gray">草稿</Tag>}
              </Space>
              <div style={{ marginTop: 16 }}>
                <Space size="large">
                  <Statistic title="下载量" value={data.downloads} formatter={(value) => {
                    if (value >= 1000000000) {
                      return `${(value / 1000000000).toFixed(1)}B`;
                    } else if (value >= 1000000) {
                      return `${(value / 1000000).toFixed(1)}M`;
                    } else if (value >= 1000) {
                      return `${(value / 1000).toFixed(1)}K`;
                    }
                    return value;
                  }} />
                  <Statistic title="评分" value={data.rating} precision={1} suffix={<Text type="secondary">({data.ratingCount})</Text>} />
                  <Statistic title="大小" value={data.size} />
                  <Statistic title="价格" value={data.price === 'free' ? '免费' : `¥${data.price}`} />
                </Space>
              </div>
            </div>
            <div>
              <Space>
                <Button type="primary" icon={<DownloadOutlined />}>下载</Button>
                <Button icon={<ShareAltOutlined />}>分享</Button>
                <Button icon={<StarOutlined />}>收藏</Button>
              </Space>
            </div>
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Title level={4}>应用详情加载失败</Title>
            <p>无法获取应用详情信息，请检查应用ID是否正确或稍后重试。</p>
            <Button type="primary" onClick={() => window.location.reload()}>
              重新加载
            </Button>
          </div>
        )}

        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          items={[
            {
              key: 'basic',
              label: '基本信息',
              children: data && (
                <>
                  <Title level={4}>应用介绍</Title>
                  <Paragraph>{data.description}</Paragraph>

                  <Title level={4}>应用截图</Title>
                  <div style={{ display: 'flex', overflowX: 'auto', marginBottom: 24 }}>
                    {data.screenshots && data.screenshots.length > 0 ? (
                      data.screenshots.map((screenshot, index) => (
                        <div key={index} style={{ marginRight: 16 }}>
                          <Image
                            src={screenshot}
                            width={200}
                            height={400}
                            style={{ objectFit: 'cover', borderRadius: 8 }}
                          />
                        </div>
                      ))
                    ) : (
                      <div style={{ padding: '20px', textAlign: 'center', color: '#999' }}>
                        暂无应用截图
                      </div>
                    )}
                  </div>

                  <Divider />

                  <Descriptions title="详细信息" bordered column={2}>
                    <Descriptions.Item label="开发者">{data.developer}</Descriptions.Item>
                    <Descriptions.Item label="开发者邮箱">{data.developerEmail}</Descriptions.Item>
                    <Descriptions.Item label="版本">{data.version}</Descriptions.Item>
                    <Descriptions.Item label="更新日期">{data.updateDate}</Descriptions.Item>
                    <Descriptions.Item label="发布日期">{data.publishDate}</Descriptions.Item>
                    <Descriptions.Item label="类别">{data.category}</Descriptions.Item>
                    <Descriptions.Item label="系统要求" span={2}>
                      <div>操作系统: {data.systemRequirements.os}</div>
                      <div>处理器: {data.systemRequirements.cpu}</div>
                      <div>内存: {data.systemRequirements.ram}</div>
                      <div>存储空间: {data.systemRequirements.storage}</div>
                    </Descriptions.Item>
                    <Descriptions.Item label="权限" span={2}>
                      {data.permissions.map(permission => (
                        <Tag key={permission}>{permission}</Tag>
                      ))}
                    </Descriptions.Item>
                  </Descriptions>
                </>
              )
            },
            {
              key: 'versions',
              label: '版本历史',
              children: (
                <Table
                  columns={versionColumns}
                  dataSource={data?.versions}
                  rowKey="version"
                  pagination={false}
                />
              )
            },
            {
              key: 'reviews',
              label: '用户评论',
              children: (
                <Table
                  columns={reviewColumns}
                  dataSource={data?.reviews}
                  rowKey="id"
                  pagination={false}
                />
              )
            },
            {
              key: 'privacy',
              label: '隐私政策',
              children: data && (
                <Paragraph>
                  {data.privacyPolicy}
                </Paragraph>
              )
            }
          ]}
        />
      </Card>
    </PageContainer>
  );
};

export default AppDetail;