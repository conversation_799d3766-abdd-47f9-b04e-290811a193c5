"use strict";
import component from "./zh-CN/component";
import globalHeader from "./zh-CN/globalHeader";
import menu from "./zh-CN/menu";
import pages from "./zh-CN/pages";
import pwa from "./zh-CN/pwa";
import settingDrawer from "./zh-CN/settingDrawer";
import settings from "./zh-CN/settings";
export default {
  "navBar.lang": "\u8BED\u8A00",
  "layout.user.link.help": "\u5E2E\u52A9",
  "layout.user.link.privacy": "\u9690\u79C1",
  "layout.user.link.terms": "\u6761\u6B3E",
  "app.preview.down.block": "\u4E0B\u8F7D\u6B64\u9875\u9762\u5230\u672C\u5730\u9879\u76EE",
  "app.welcome.link.fetch-blocks": "\u83B7\u53D6\u5168\u90E8\u533A\u5757",
  "app.welcome.link.block-list": "\u57FA\u4E8E block \u5F00\u53D1\uFF0C\u5FEB\u901F\u6784\u5EFA\u6807\u51C6\u9875\u9762",
  ...pages,
  ...globalHeader,
  ...menu,
  ...settingDrawer,
  ...settings,
  ...pwa,
  ...component
};
