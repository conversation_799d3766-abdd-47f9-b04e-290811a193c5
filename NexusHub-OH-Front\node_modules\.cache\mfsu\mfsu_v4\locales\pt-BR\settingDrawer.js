"use strict";
export default {
  "app.setting.pagestyle": "Configura\xE7\xE3o de estilo da p\xE1gina",
  "app.setting.pagestyle.dark": "Dark style",
  "app.setting.pagestyle.light": "Light style",
  "app.setting.content-width": "Largura do conte\xFAdo",
  "app.setting.content-width.fixed": "Fixo",
  "app.setting.content-width.fluid": "Fluido",
  "app.setting.themecolor": "Cor do Tema",
  "app.setting.themecolor.dust": "Dust Red",
  "app.setting.themecolor.volcano": "Volcano",
  "app.setting.themecolor.sunset": "Sunset Orange",
  "app.setting.themecolor.cyan": "Cyan",
  "app.setting.themecolor.green": "Polar Green",
  "app.setting.themecolor.daybreak": "Daybreak Blue (default)",
  "app.setting.themecolor.geekblue": "Geek Glue",
  "app.setting.themecolor.purple": "Golden Purple",
  "app.setting.navigationmode": "Modo de Navega\xE7\xE3o",
  "app.setting.sidemenu": "Layout do Menu Lateral",
  "app.setting.topmenu": "Layout do Menu Superior",
  "app.setting.fixedheader": "Cabe\xE7alho fixo",
  "app.setting.fixedsidebar": "Barra lateral fixa",
  "app.setting.fixedsidebar.hint": "Funciona no layout do menu lateral",
  "app.setting.hideheader": "Esconder o cabe\xE7alho quando rolar",
  "app.setting.hideheader.hint": "Funciona quando o esconder cabe\xE7alho est\xE1 abilitado",
  "app.setting.othersettings": "Outras configura\xE7\xF5es",
  "app.setting.weakmode": "Weak Mode",
  "app.setting.copy": "Copiar Configura\xE7\xE3o",
  "app.setting.copyinfo": "copiado com sucesso, por favor trocar o defaultSettings em src/models/setting.js",
  "app.setting.production.hint": "O painel de configura\xE7\xE3o apenas \xE9 exibido no ambiente de desenvolvimento, por favor modifique manualmente o"
};
