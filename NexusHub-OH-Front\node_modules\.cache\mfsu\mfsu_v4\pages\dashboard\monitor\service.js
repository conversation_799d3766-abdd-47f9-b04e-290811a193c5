"use strict";
import { request } from "@umijs/max";
export async function queryTags() {
  return request("/tags");
}
export async function getMonitoringData() {
  return request("/dashboard/monitoring/data");
}
export async function getMonitoringLogs(params) {
  return request("/dashboard/monitoring/logs", {
    params: {
      page: 1,
      page_size: 20,
      ...params
    }
  });
}
export async function getMonitoringAlerts(params) {
  return request("/dashboard/monitoring/alerts", {
    params: {
      page: 1,
      page_size: 20,
      ...params
    }
  });
}
