"use strict";
import { request } from "@umijs/max";
export async function getOpenHarmonyVersions(params) {
  return request("/admin/harmony-versions", {
    method: "GET",
    params
  });
}
export async function getOpenHarmonyVersion(id) {
  return request(`/admin/harmony-versions/${id}`, {
    method: "GET"
  });
}
export async function createOpenHarmonyVersion(data) {
  return request("/admin/harmony-versions", {
    method: "POST",
    data
  });
}
export async function updateOpenHarmonyVersion(id, data) {
  return request(`/admin/harmony-versions/${id}`, {
    method: "PUT",
    data
  });
}
export async function deleteOpenHarmonyVersion(id) {
  return request(`/admin/harmony-versions/${id}`, {
    method: "DELETE"
  });
}
export async function checkVersionCodeExists(versionCode, excludeId) {
  return request("/admin/harmony-versions/check-version-code", {
    method: "POST",
    data: {
      version_code: versionCode,
      exclude_id: excludeId
    }
  });
}
export async function getVersionUsageStats(id) {
  return request(`/admin/harmony-versions/${id}/usage`, {
    method: "GET"
  });
}
