"use strict";
import { jsx } from "react/jsx-runtime";
import { useIntl } from "@umijs/max";
import { Button, message, notification } from "antd";
import defaultSettings from "../config/defaultSettings";
const { pwa } = defaultSettings;
const isHttps = document.location.protocol === "https:";
const clearCache = () => {
  if (window.caches) {
    caches.keys().then((keys) => {
      keys.forEach((key) => {
        caches.delete(key);
      });
    }).catch((e) => console.log(e));
  }
};
if (pwa) {
  window.addEventListener("sw.offline", () => {
    message.warning(useIntl().formatMessage({ id: "app.pwa.offline" }));
  });
  window.addEventListener("sw.updated", (event) => {
    const e = event;
    const reloadSW = async () => {
      const worker = e.detail && e.detail.waiting;
      if (!worker) {
        return true;
      }
      await new Promise((resolve, reject) => {
        const channel = new MessageChannel();
        channel.port1.onmessage = (msgEvent) => {
          if (msgEvent.data.error) {
            reject(msgEvent.data.error);
          } else {
            resolve(msgEvent.data);
          }
        };
        worker.postMessage({ type: "skip-waiting" }, [channel.port2]);
      });
      clearCache();
      window.location.reload();
      return true;
    };
    const key = `open${Date.now()}`;
    const btn = /* @__PURE__ */ jsx(
      Button,
      {
        type: "primary",
        onClick: () => {
          notification.destroy(key);
          reloadSW();
        },
        children: useIntl().formatMessage({ id: "app.pwa.serviceworker.updated.ok" })
      }
    );
    notification.open({
      message: useIntl().formatMessage({ id: "app.pwa.serviceworker.updated" }),
      description: useIntl().formatMessage({ id: "app.pwa.serviceworker.updated.hint" }),
      btn,
      key,
      onClose: async () => null
    });
  });
} else if ("serviceWorker" in navigator && isHttps) {
  const { serviceWorker } = navigator;
  if (serviceWorker.getRegistrations) {
    serviceWorker.getRegistrations().then((sws) => {
      sws.forEach((sw) => {
        sw.unregister();
      });
    });
  }
  serviceWorker.getRegistration().then((sw) => {
    if (sw) sw.unregister();
  });
  clearCache();
}
