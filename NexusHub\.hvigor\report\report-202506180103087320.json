{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "fbfd1fc3-ffb3-449c-9bf4-2e75438bfc56", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963333836000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "429bd80a-afae-4bb9-bb23-3628d9687338", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963333971100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed562311-d0a5-4479-b4ee-efe133eabfb8", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963388603100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad52162b-8d2f-4aa2-b945-e72ec2de3f39", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963388931000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f70a6610-915b-4f5b-a296-dacc4a76f7d2", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963391293300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fecf8ed-83d6-4e54-bef3-2f3a63d1d8b0", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154963391590600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7d7f248-bc96-48bb-852b-18775cdcc902", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158947922900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac4f4d1e-af7b-4bce-824f-32c529ea9b56", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158954154800, "endTime": 155159149359600}, "additional": {"children": ["f51b4761-ce7b-4f85-881c-37408a8d10a1", "c5ddd891-9932-4fea-954b-1ca04fb01f3d", "95d45581-8988-4c0e-b807-5f3bfb668909", "fdba0053-7081-45a7-969a-a07866a81531", "16c5c72c-99fa-4d58-82ef-c2e7cf6af6eb", "c2f23b6f-d7ba-48f4-95d0-dc8bf1bd20a3", "8b519762-76ba-4a65-8993-677feaf2c380"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "5200cd01-2805-435e-9679-cca565228dfe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f51b4761-ce7b-4f85-881c-37408a8d10a1", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158954162500, "endTime": 155158967404100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac4f4d1e-af7b-4bce-824f-32c529ea9b56", "logId": "49e56810-008e-4922-bc01-09b0de7bd368"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5ddd891-9932-4fea-954b-1ca04fb01f3d", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158967421100, "endTime": 155159147578800}, "additional": {"children": ["8e9b8ac2-8e26-4959-89ef-313a01bda1bd", "71c654f8-fef5-4e54-8c4f-3b823756f0e9", "b44bd8e5-3ef7-4ce7-9e3b-b72c14fd8959", "6e618032-3102-43e9-b248-4ea69a6861ce", "c3674ae3-c21c-428f-b4e0-de9b4a92c764", "80637779-cf04-492f-a9e3-e1b9ecd8d580", "6fd07297-966a-46b4-bca3-8868f325839f", "e9f50bad-3d23-4de6-ad23-ccb3fdb4bfeb", "9b6e9895-99c0-4468-898b-04c81400ccd1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac4f4d1e-af7b-4bce-824f-32c529ea9b56", "logId": "572d8b9c-4bad-4ef1-8586-bf1c3a9ab2e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95d45581-8988-4c0e-b807-5f3bfb668909", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159147602600, "endTime": 155159149333400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac4f4d1e-af7b-4bce-824f-32c529ea9b56", "logId": "46fb0961-0a32-4226-a46e-1187b666efac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fdba0053-7081-45a7-969a-a07866a81531", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159149342000, "endTime": 155159149352100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac4f4d1e-af7b-4bce-824f-32c529ea9b56", "logId": "2a608543-00e9-4e17-8649-6446de84c541"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16c5c72c-99fa-4d58-82ef-c2e7cf6af6eb", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158957224600, "endTime": 155158957279200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac4f4d1e-af7b-4bce-824f-32c529ea9b56", "logId": "8f014a8e-481a-49fa-8c2d-0eac213aa203"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f014a8e-481a-49fa-8c2d-0eac213aa203", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158957224600, "endTime": 155158957279200}, "additional": {"logType": "info", "children": [], "durationId": "16c5c72c-99fa-4d58-82ef-c2e7cf6af6eb", "parent": "5200cd01-2805-435e-9679-cca565228dfe"}}, {"head": {"id": "c2f23b6f-d7ba-48f4-95d0-dc8bf1bd20a3", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158962679700, "endTime": 155158962697300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac4f4d1e-af7b-4bce-824f-32c529ea9b56", "logId": "64b7c598-1eba-48e1-95f6-0b686196cc4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64b7c598-1eba-48e1-95f6-0b686196cc4e", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158962679700, "endTime": 155158962697300}, "additional": {"logType": "info", "children": [], "durationId": "c2f23b6f-d7ba-48f4-95d0-dc8bf1bd20a3", "parent": "5200cd01-2805-435e-9679-cca565228dfe"}}, {"head": {"id": "4205d48a-8940-42f7-81e1-16616b3ed717", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158962756000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "391d7c93-7f32-4656-927b-6df938a7f366", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158967246500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49e56810-008e-4922-bc01-09b0de7bd368", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158954162500, "endTime": 155158967404100}, "additional": {"logType": "info", "children": [], "durationId": "f51b4761-ce7b-4f85-881c-37408a8d10a1", "parent": "5200cd01-2805-435e-9679-cca565228dfe"}}, {"head": {"id": "8e9b8ac2-8e26-4959-89ef-313a01bda1bd", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158972776300, "endTime": 155158972782900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c5ddd891-9932-4fea-954b-1ca04fb01f3d", "logId": "3cb47f2e-056a-4511-a522-9f365bef0824"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71c654f8-fef5-4e54-8c4f-3b823756f0e9", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158972798200, "endTime": 155158976955700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c5ddd891-9932-4fea-954b-1ca04fb01f3d", "logId": "69a6c8ef-d5dc-445d-b06b-1b7fbab72549"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b44bd8e5-3ef7-4ce7-9e3b-b72c14fd8959", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158976968800, "endTime": 155159047463200}, "additional": {"children": ["fca599b2-1ddf-42e8-b301-d7adf9c8575a", "cd15ebb4-0a7a-4fa7-bac6-717f6d66a903", "adb67d13-a6e9-4098-acae-507dcfaf2200"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c5ddd891-9932-4fea-954b-1ca04fb01f3d", "logId": "18217da4-777f-47cc-9ccf-9db52186156f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e618032-3102-43e9-b248-4ea69a6861ce", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159047475600, "endTime": 155159069581700}, "additional": {"children": ["4bc41c38-b78e-422c-9ae1-aa44ae05a894"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c5ddd891-9932-4fea-954b-1ca04fb01f3d", "logId": "733a15aa-0b7c-4e2c-9077-2dd186794ce4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3674ae3-c21c-428f-b4e0-de9b4a92c764", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159069593900, "endTime": 155159121454000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c5ddd891-9932-4fea-954b-1ca04fb01f3d", "logId": "71ab190d-7095-4f88-b6ea-be37cc3e69c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80637779-cf04-492f-a9e3-e1b9ecd8d580", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159122560300, "endTime": 155159133160400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c5ddd891-9932-4fea-954b-1ca04fb01f3d", "logId": "63f7a270-39fd-45b3-8972-d01469d955ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6fd07297-966a-46b4-bca3-8868f325839f", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159133180100, "endTime": 155159147423100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c5ddd891-9932-4fea-954b-1ca04fb01f3d", "logId": "5b90c4c9-dcc6-41ba-8378-c30d8fa0bd14"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9f50bad-3d23-4de6-ad23-ccb3fdb4bfeb", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159147439100, "endTime": 155159147567900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c5ddd891-9932-4fea-954b-1ca04fb01f3d", "logId": "829eeef3-06c5-474d-b3b0-1eed596b7924"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cb47f2e-056a-4511-a522-9f365bef0824", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158972776300, "endTime": 155158972782900}, "additional": {"logType": "info", "children": [], "durationId": "8e9b8ac2-8e26-4959-89ef-313a01bda1bd", "parent": "572d8b9c-4bad-4ef1-8586-bf1c3a9ab2e9"}}, {"head": {"id": "69a6c8ef-d5dc-445d-b06b-1b7fbab72549", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158972798200, "endTime": 155158976955700}, "additional": {"logType": "info", "children": [], "durationId": "71c654f8-fef5-4e54-8c4f-3b823756f0e9", "parent": "572d8b9c-4bad-4ef1-8586-bf1c3a9ab2e9"}}, {"head": {"id": "fca599b2-1ddf-42e8-b301-d7adf9c8575a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158977628400, "endTime": 155158977648200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b44bd8e5-3ef7-4ce7-9e3b-b72c14fd8959", "logId": "e9e501a7-909a-45f7-9d38-9970f59e2ad8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9e501a7-909a-45f7-9d38-9970f59e2ad8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158977628400, "endTime": 155158977648200}, "additional": {"logType": "info", "children": [], "durationId": "fca599b2-1ddf-42e8-b301-d7adf9c8575a", "parent": "18217da4-777f-47cc-9ccf-9db52186156f"}}, {"head": {"id": "cd15ebb4-0a7a-4fa7-bac6-717f6d66a903", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158980142900, "endTime": 155159046810100}, "additional": {"children": ["bbb018ca-4003-4931-a7a5-879f3ba5cfa4", "75035109-0292-4701-b31d-414957074172"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b44bd8e5-3ef7-4ce7-9e3b-b72c14fd8959", "logId": "3c247ef5-9d29-4390-afa9-2903713d367d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbb018ca-4003-4931-a7a5-879f3ba5cfa4", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158980144000, "endTime": 155158984150600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cd15ebb4-0a7a-4fa7-bac6-717f6d66a903", "logId": "d0ad3629-90c0-4dab-b48a-9c39dcb9cfec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75035109-0292-4701-b31d-414957074172", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158984162100, "endTime": 155159046798700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cd15ebb4-0a7a-4fa7-bac6-717f6d66a903", "logId": "e8a1fc2e-66cb-43c7-8591-251faa3d9b61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "715a655d-b44d-4a12-b1b5-1ff6d27ed43f", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158980147800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86c17955-2172-4e52-a57e-75b6a2bff01f", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158984035800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0ad3629-90c0-4dab-b48a-9c39dcb9cfec", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158980144000, "endTime": 155158984150600}, "additional": {"logType": "info", "children": [], "durationId": "bbb018ca-4003-4931-a7a5-879f3ba5cfa4", "parent": "3c247ef5-9d29-4390-afa9-2903713d367d"}}, {"head": {"id": "fe72b656-7b8f-47c5-b145-2ec91fe4278f", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158984175300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91d1809f-aaea-42aa-b7c8-b3683a530ac7", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158991619000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b9e85de-c1e9-4fb7-a4bd-238564dd8e84", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158991723200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8391f0a0-de25-4952-9c62-730c2a85450b", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158991816200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "384c64cb-d5b7-4e40-a01b-95a1945be02b", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158991875500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a32047e-74c5-42cc-bd6c-cb943f44f43a", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158993150900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5e95098-760e-4d97-9fd3-6bf252ee1653", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159004831400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cf13133-1a0b-4d63-bec1-54733f3d2458", "name": "Sdk init in 26 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159024914900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8fd434d-ad5d-40c3-8e30-be030d969065", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159025079100}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 3, "second": 8}, "markType": "other"}}, {"head": {"id": "67894a16-4c84-41ea-8d76-e9c6ab379e74", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159025125600}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 3, "second": 8}, "markType": "other"}}, {"head": {"id": "de8343df-0840-4a22-afff-2c6f988459ba", "name": "Project task initialization takes 20 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159046546800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0e30385-5e36-4772-94d3-810ba1813cb4", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159046680400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2cef9d2-58cf-4905-a752-141632ad7f86", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159046731300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6cdc674-58e5-4a7d-90c1-13f2c2985811", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159046766100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8a1fc2e-66cb-43c7-8591-251faa3d9b61", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158984162100, "endTime": 155159046798700}, "additional": {"logType": "info", "children": [], "durationId": "75035109-0292-4701-b31d-414957074172", "parent": "3c247ef5-9d29-4390-afa9-2903713d367d"}}, {"head": {"id": "3c247ef5-9d29-4390-afa9-2903713d367d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158980142900, "endTime": 155159046810100}, "additional": {"logType": "info", "children": ["d0ad3629-90c0-4dab-b48a-9c39dcb9cfec", "e8a1fc2e-66cb-43c7-8591-251faa3d9b61"], "durationId": "cd15ebb4-0a7a-4fa7-bac6-717f6d66a903", "parent": "18217da4-777f-47cc-9ccf-9db52186156f"}}, {"head": {"id": "adb67d13-a6e9-4098-acae-507dcfaf2200", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159047440100, "endTime": 155159047453700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b44bd8e5-3ef7-4ce7-9e3b-b72c14fd8959", "logId": "794a60b1-31d0-483c-b556-4916ebfc6bf6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "794a60b1-31d0-483c-b556-4916ebfc6bf6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159047440100, "endTime": 155159047453700}, "additional": {"logType": "info", "children": [], "durationId": "adb67d13-a6e9-4098-acae-507dcfaf2200", "parent": "18217da4-777f-47cc-9ccf-9db52186156f"}}, {"head": {"id": "18217da4-777f-47cc-9ccf-9db52186156f", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158976968800, "endTime": 155159047463200}, "additional": {"logType": "info", "children": ["e9e501a7-909a-45f7-9d38-9970f59e2ad8", "3c247ef5-9d29-4390-afa9-2903713d367d", "794a60b1-31d0-483c-b556-4916ebfc6bf6"], "durationId": "b44bd8e5-3ef7-4ce7-9e3b-b72c14fd8959", "parent": "572d8b9c-4bad-4ef1-8586-bf1c3a9ab2e9"}}, {"head": {"id": "4bc41c38-b78e-422c-9ae1-aa44ae05a894", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159047981900, "endTime": 155159069569500}, "additional": {"children": ["55a5e1a4-860f-4d68-8493-fe155206acad", "2011204a-4544-44e9-a74b-cd3c82f4cc22", "1fc183ca-26ae-4f54-bef7-13002df703ea"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6e618032-3102-43e9-b248-4ea69a6861ce", "logId": "2a0ab816-2d9c-4f0c-ab48-be4f063f51e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55a5e1a4-860f-4d68-8493-fe155206acad", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159050589700, "endTime": 155159050609500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4bc41c38-b78e-422c-9ae1-aa44ae05a894", "logId": "1c5c1975-e2d0-45af-b033-3d05ce59bb4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c5c1975-e2d0-45af-b033-3d05ce59bb4c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159050589700, "endTime": 155159050609500}, "additional": {"logType": "info", "children": [], "durationId": "55a5e1a4-860f-4d68-8493-fe155206acad", "parent": "2a0ab816-2d9c-4f0c-ab48-be4f063f51e9"}}, {"head": {"id": "2011204a-4544-44e9-a74b-cd3c82f4cc22", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159054125100, "endTime": 155159067503200}, "additional": {"children": ["ec932371-f2ad-4283-831f-2c483dff8ad7", "22b8455d-287f-4be6-804e-85bbddd8faf9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4bc41c38-b78e-422c-9ae1-aa44ae05a894", "logId": "a442912b-32be-41ab-8070-0873bf7d83af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec932371-f2ad-4283-831f-2c483dff8ad7", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159054126100, "endTime": 155159056549500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2011204a-4544-44e9-a74b-cd3c82f4cc22", "logId": "026756bd-b216-4f20-90e3-b91782448d46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22b8455d-287f-4be6-804e-85bbddd8faf9", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159056561800, "endTime": 155159067485500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2011204a-4544-44e9-a74b-cd3c82f4cc22", "logId": "1cb12de4-738b-4c11-b8c5-1c59fdde32e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6b6df98-82c3-497e-ba66-f39eb87f09f3", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159054129800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "612680e2-627b-4d1d-82b4-86c06e10ed98", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159056434100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "026756bd-b216-4f20-90e3-b91782448d46", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159054126100, "endTime": 155159056549500}, "additional": {"logType": "info", "children": [], "durationId": "ec932371-f2ad-4283-831f-2c483dff8ad7", "parent": "a442912b-32be-41ab-8070-0873bf7d83af"}}, {"head": {"id": "41f4ca7a-ddda-4576-8efb-23dacc007d33", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159056578200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aea60e1-74ac-4dd7-b011-e6f3e699e388", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159063293200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a20f58f-9c54-4023-af93-dfe27962d7af", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159063417400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1814dc48-c4c4-4a21-8c43-f09e3a52a4d8", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159063589200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce3149f8-095f-44ec-91c9-ffde925a39bf", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159063683600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "595b769f-713e-444a-a50a-51d98cc033f4", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159063722200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e9b1827-63e9-409f-822b-fe34cd54deca", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159063756000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6115370-f633-4001-9beb-75ed78eeb4af", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159063795900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18ab40be-b34c-4b74-8313-4186b31f6629", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159063829400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33eb62d4-e061-4ac0-a1aa-fb0783966ad2", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159063980700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c705249-249c-4652-9b09-4303fde42691", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159064067000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aacf8ea-842b-43c2-8c7e-3cd237c5d8d6", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159064115000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "411cbe66-6c3b-47ae-beea-3331e8090bce", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159064150200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "926d8d43-8ca3-447e-9060-6224c999b6cc", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159064187700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "796a3f0a-a62c-44a5-b3a1-16189a73064e", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159064219900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c41ebe1f-6e65-493c-9b32-ec421af7110c", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159064298200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "440434a2-e871-48a4-92da-bbb7d0cc15a1", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159064360600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88b98022-106e-4681-86ee-b8b154cc41ba", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159064393000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55b749d4-0e6a-467a-9c0b-c35349e8cdfb", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159064419200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42019bc9-4737-4bc3-af3f-782a15011028", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159064457000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0472f32-0d29-43ea-a528-40a59febb2a0", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159067161800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9035fd78-75ba-43da-bff7-47ad4005a058", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159067350800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22502b25-9f74-48b8-a52a-7328083b2f83", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159067407400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98bdadda-2326-4e4d-90bc-e68869452d8d", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159067452800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cb12de4-738b-4c11-b8c5-1c59fdde32e7", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159056561800, "endTime": 155159067485500}, "additional": {"logType": "info", "children": [], "durationId": "22b8455d-287f-4be6-804e-85bbddd8faf9", "parent": "a442912b-32be-41ab-8070-0873bf7d83af"}}, {"head": {"id": "a442912b-32be-41ab-8070-0873bf7d83af", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159054125100, "endTime": 155159067503200}, "additional": {"logType": "info", "children": ["026756bd-b216-4f20-90e3-b91782448d46", "1cb12de4-738b-4c11-b8c5-1c59fdde32e7"], "durationId": "2011204a-4544-44e9-a74b-cd3c82f4cc22", "parent": "2a0ab816-2d9c-4f0c-ab48-be4f063f51e9"}}, {"head": {"id": "1fc183ca-26ae-4f54-bef7-13002df703ea", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159069541500, "endTime": 155159069556100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4bc41c38-b78e-422c-9ae1-aa44ae05a894", "logId": "bf614b8d-2e73-4d49-9077-624bd9edf888"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf614b8d-2e73-4d49-9077-624bd9edf888", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159069541500, "endTime": 155159069556100}, "additional": {"logType": "info", "children": [], "durationId": "1fc183ca-26ae-4f54-bef7-13002df703ea", "parent": "2a0ab816-2d9c-4f0c-ab48-be4f063f51e9"}}, {"head": {"id": "2a0ab816-2d9c-4f0c-ab48-be4f063f51e9", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159047981900, "endTime": 155159069569500}, "additional": {"logType": "info", "children": ["1c5c1975-e2d0-45af-b033-3d05ce59bb4c", "a442912b-32be-41ab-8070-0873bf7d83af", "bf614b8d-2e73-4d49-9077-624bd9edf888"], "durationId": "4bc41c38-b78e-422c-9ae1-aa44ae05a894", "parent": "733a15aa-0b7c-4e2c-9077-2dd186794ce4"}}, {"head": {"id": "733a15aa-0b7c-4e2c-9077-2dd186794ce4", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159047475600, "endTime": 155159069581700}, "additional": {"logType": "info", "children": ["2a0ab816-2d9c-4f0c-ab48-be4f063f51e9"], "durationId": "6e618032-3102-43e9-b248-4ea69a6861ce", "parent": "572d8b9c-4bad-4ef1-8586-bf1c3a9ab2e9"}}, {"head": {"id": "ed991a5e-b164-4176-bb8e-2b690c965e51", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159083167500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10e36d24-4b2b-46ae-8e6d-c5909c5b9ade", "name": "hvigorfile, resolve hvigorfile dependencies in 52 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159121300400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71ab190d-7095-4f88-b6ea-be37cc3e69c0", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159069593900, "endTime": 155159121454000}, "additional": {"logType": "info", "children": [], "durationId": "c3674ae3-c21c-428f-b4e0-de9b4a92c764", "parent": "572d8b9c-4bad-4ef1-8586-bf1c3a9ab2e9"}}, {"head": {"id": "9b6e9895-99c0-4468-898b-04c81400ccd1", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159122343000, "endTime": 155159122548600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c5ddd891-9932-4fea-954b-1ca04fb01f3d", "logId": "6735bce9-d522-4f07-8afc-2c38c1a81070"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ceb69536-7deb-40db-b801-6477407e987b", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159122377400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6735bce9-d522-4f07-8afc-2c38c1a81070", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159122343000, "endTime": 155159122548600}, "additional": {"logType": "info", "children": [], "durationId": "9b6e9895-99c0-4468-898b-04c81400ccd1", "parent": "572d8b9c-4bad-4ef1-8586-bf1c3a9ab2e9"}}, {"head": {"id": "027d528e-5822-41e1-8682-f3f1ac64b97b", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159124127900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6929b72-fee6-415a-9352-28d3723d513b", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159132262500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63f7a270-39fd-45b3-8972-d01469d955ed", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159122560300, "endTime": 155159133160400}, "additional": {"logType": "info", "children": [], "durationId": "80637779-cf04-492f-a9e3-e1b9ecd8d580", "parent": "572d8b9c-4bad-4ef1-8586-bf1c3a9ab2e9"}}, {"head": {"id": "c0c15752-d5f2-4cd1-b54e-43f4158f5603", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159133199100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "360d6f02-390f-4c8a-8d09-6623413315d2", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159140680900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fa448db-7457-41a9-a853-feb579aee096", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159140818700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67d211bc-9360-409a-b13e-277f8f71d424", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159140972700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7a11a74-e7f3-4994-bb89-195e57c40f46", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159143926800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e301c8b8-2165-4e35-b8d3-5d2545d0b827", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159144034000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b90c4c9-dcc6-41ba-8378-c30d8fa0bd14", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159133180100, "endTime": 155159147423100}, "additional": {"logType": "info", "children": [], "durationId": "6fd07297-966a-46b4-bca3-8868f325839f", "parent": "572d8b9c-4bad-4ef1-8586-bf1c3a9ab2e9"}}, {"head": {"id": "28e7c92a-3358-4a8e-bf1e-d72be1eb7cf1", "name": "Configuration phase cost:175 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159147459200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "829eeef3-06c5-474d-b3b0-1eed596b7924", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159147439100, "endTime": 155159147567900}, "additional": {"logType": "info", "children": [], "durationId": "e9f50bad-3d23-4de6-ad23-ccb3fdb4bfeb", "parent": "572d8b9c-4bad-4ef1-8586-bf1c3a9ab2e9"}}, {"head": {"id": "572d8b9c-4bad-4ef1-8586-bf1c3a9ab2e9", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158967421100, "endTime": 155159147578800}, "additional": {"logType": "info", "children": ["3cb47f2e-056a-4511-a522-9f365bef0824", "69a6c8ef-d5dc-445d-b06b-1b7fbab72549", "18217da4-777f-47cc-9ccf-9db52186156f", "733a15aa-0b7c-4e2c-9077-2dd186794ce4", "71ab190d-7095-4f88-b6ea-be37cc3e69c0", "63f7a270-39fd-45b3-8972-d01469d955ed", "5b90c4c9-dcc6-41ba-8378-c30d8fa0bd14", "829eeef3-06c5-474d-b3b0-1eed596b7924", "6735bce9-d522-4f07-8afc-2c38c1a81070"], "durationId": "c5ddd891-9932-4fea-954b-1ca04fb01f3d", "parent": "5200cd01-2805-435e-9679-cca565228dfe"}}, {"head": {"id": "8b519762-76ba-4a65-8993-677feaf2c380", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159149297600, "endTime": 155159149319200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac4f4d1e-af7b-4bce-824f-32c529ea9b56", "logId": "825ed470-7ff9-4f49-ae58-1ea4ae79b488"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "825ed470-7ff9-4f49-ae58-1ea4ae79b488", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159149297600, "endTime": 155159149319200}, "additional": {"logType": "info", "children": [], "durationId": "8b519762-76ba-4a65-8993-677feaf2c380", "parent": "5200cd01-2805-435e-9679-cca565228dfe"}}, {"head": {"id": "46fb0961-0a32-4226-a46e-1187b666efac", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159147602600, "endTime": 155159149333400}, "additional": {"logType": "info", "children": [], "durationId": "95d45581-8988-4c0e-b807-5f3bfb668909", "parent": "5200cd01-2805-435e-9679-cca565228dfe"}}, {"head": {"id": "2a608543-00e9-4e17-8649-6446de84c541", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159149342000, "endTime": 155159149352100}, "additional": {"logType": "info", "children": [], "durationId": "fdba0053-7081-45a7-969a-a07866a81531", "parent": "5200cd01-2805-435e-9679-cca565228dfe"}}, {"head": {"id": "5200cd01-2805-435e-9679-cca565228dfe", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158954154800, "endTime": 155159149359600}, "additional": {"logType": "info", "children": ["49e56810-008e-4922-bc01-09b0de7bd368", "572d8b9c-4bad-4ef1-8586-bf1c3a9ab2e9", "46fb0961-0a32-4226-a46e-1187b666efac", "2a608543-00e9-4e17-8649-6446de84c541", "8f014a8e-481a-49fa-8c2d-0eac213aa203", "64b7c598-1eba-48e1-95f6-0b686196cc4e", "825ed470-7ff9-4f49-ae58-1ea4ae79b488"], "durationId": "ac4f4d1e-af7b-4bce-824f-32c529ea9b56"}}, {"head": {"id": "38eb2e85-b28f-4751-a3f1-23c00eede5c6", "name": "Configuration task cost before running: 199 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159149841600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ba1ca32-d53b-465c-8e9b-b206a04e8348", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159158621500, "endTime": 155159171200700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d483e89a-71e3-42c2-92b5-cfed37c2648e", "logId": "1ab95e48-62dc-4f58-b94d-27f8caedbe99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d483e89a-71e3-42c2-92b5-cfed37c2648e", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159151472900}, "additional": {"logType": "detail", "children": [], "durationId": "8ba1ca32-d53b-465c-8e9b-b206a04e8348"}}, {"head": {"id": "f962764e-3b43-4885-a6e8-f22a9580385d", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159152186500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e56a38e-56bb-4884-a20a-394afbe5cbda", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159152299400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c46cdf1-be7d-4378-b913-454fb10c7569", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159153030800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eed92b70-ae97-44e7-a742-bb818f878cc6", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159153884500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee320b13-de28-43ff-8058-cc87fa3161c1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159154924100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83930896-6fd5-4477-a37d-06082a9c06ab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159155011100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79aaceca-324f-4f46-a54f-92d91d4c60b3", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159158633400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "119f8389-6a57-45f1-830d-e2e2ac13226e", "name": "Incremental task entry:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159170990200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b53bf3b-2f03-45de-a3b9-57560c2e610c", "name": "entry : default@PreBuild cost memory 0.3240966796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159171133800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ab95e48-62dc-4f58-b94d-27f8caedbe99", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159158621500, "endTime": 155159171200700}, "additional": {"logType": "info", "children": [], "durationId": "8ba1ca32-d53b-465c-8e9b-b206a04e8348"}}, {"head": {"id": "a79d0083-0850-4e99-830b-17626f342aae", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159177650400, "endTime": 155159180498900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7557d6a5-bbba-4854-814f-c317ecd77a26", "logId": "c1f0ae30-6a3b-4046-8603-b27b13aba9a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7557d6a5-bbba-4854-814f-c317ecd77a26", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159175573200}, "additional": {"logType": "detail", "children": [], "durationId": "a79d0083-0850-4e99-830b-17626f342aae"}}, {"head": {"id": "6157d8d2-bc0e-42a5-ba2f-c03613f6f4f8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159176910400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4b87f42-06b8-4eb7-a7ef-2a4f591690e0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159177018200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a4f587b-4129-49ef-95ff-6f5e2283963d", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159177660300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd1c8396-8303-4f49-8e82-864221428278", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159178977400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de9cbec5-ceb9-4925-a299-a12f6a1447d9", "name": "entry : default@CreateModuleInfo cost memory 0.060760498046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159180299200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b03b0a9e-72e7-41ea-8bf0-c1c2da5cda36", "name": "runTaskFromQueue task cost before running: 229 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159180441700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f0ae30-6a3b-4046-8603-b27b13aba9a3", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159177650400, "endTime": 155159180498900, "totalTime": 2768700}, "additional": {"logType": "info", "children": [], "durationId": "a79d0083-0850-4e99-830b-17626f342aae"}}, {"head": {"id": "ff63801b-5d18-4fe9-b035-ff63eb0042b0", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159191148800, "endTime": 155159194188400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "576d1470-3ce6-4f80-9634-27c0935879ce", "logId": "e8c8b31a-64fb-408e-add8-fd7e4793c03a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "576d1470-3ce6-4f80-9634-27c0935879ce", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159182856100}, "additional": {"logType": "detail", "children": [], "durationId": "ff63801b-5d18-4fe9-b035-ff63eb0042b0"}}, {"head": {"id": "1ba98645-5b21-4a1e-867c-cba76519914b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159184007900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc4668f6-1347-4a48-940b-32b90353b43c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159184127600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc8d9a9b-8557-45b6-a012-e4c00612cdca", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159191168200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daba353f-0911-458d-bf0a-0afc9363e98a", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159192677200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc3f3dc0-c8cd-4d5e-80bb-051e0f04a195", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159193917400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8900921-5f31-4d62-8b0c-c6bc251eb108", "name": "entry : default@GenerateMetadata cost memory 0.10205841064453125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159194085300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8c8b31a-64fb-408e-add8-fd7e4793c03a", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159191148800, "endTime": 155159194188400}, "additional": {"logType": "info", "children": [], "durationId": "ff63801b-5d18-4fe9-b035-ff63eb0042b0"}}, {"head": {"id": "dad29e22-61e2-4d53-a1cf-22b8f96674dd", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159197664700, "endTime": 155159197941100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "76d70f74-8987-4e82-8810-cecc05aec492", "logId": "2bd2ccb4-d089-4019-9a30-019387f4a4af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76d70f74-8987-4e82-8810-cecc05aec492", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159196249600}, "additional": {"logType": "detail", "children": [], "durationId": "dad29e22-61e2-4d53-a1cf-22b8f96674dd"}}, {"head": {"id": "a8c1872e-d330-4709-9271-c3be5eaa431d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159197373700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97468f3f-b2ee-4bfa-b84d-717ed3d104df", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159197504900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2890c7e-f389-4ea6-814b-cb63b0b71f43", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159197673400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d517fca-ca3d-4457-a08a-d8f19f72588e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159197752200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b978dc-ff5e-4dc3-9286-37d5e2ad38e4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159197789800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fba10b71-8e7f-4e81-a8b8-f0f091a8361a", "name": "entry : default@ConfigureCmake cost memory 0.0373382568359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159197840000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b01c7c44-6664-4cb4-87c1-b7829222b45c", "name": "runTaskFromQueue task cost before running: 247 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159197905800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bd2ccb4-d089-4019-9a30-019387f4a4af", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159197664700, "endTime": 155159197941100, "totalTime": 222100}, "additional": {"logType": "info", "children": [], "durationId": "dad29e22-61e2-4d53-a1cf-22b8f96674dd"}}, {"head": {"id": "dd1dc77d-51ef-4094-8124-274265d2cebb", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159203078800, "endTime": 155159205524800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "cc0b9f20-2c26-422f-84c3-e17a45244242", "logId": "faac560b-8c90-4d91-b4cc-1bde4a913ca8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc0b9f20-2c26-422f-84c3-e17a45244242", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159200226200}, "additional": {"logType": "detail", "children": [], "durationId": "dd1dc77d-51ef-4094-8124-274265d2cebb"}}, {"head": {"id": "b40bfc79-5373-4361-a729-f77b3c01eded", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159202123500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1681b79b-ae19-42d6-b275-86d878c0294c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159202283300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49b623b5-7a38-43cb-9473-0cc65ad45e8f", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159203092700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64c31562-f3f7-4dde-ad92-231aecae0afa", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159205292600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b2e9158-f2e9-4317-8e21-b1aea9153252", "name": "entry : default@MergeProfile cost memory 0.118133544921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159205443600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faac560b-8c90-4d91-b4cc-1bde4a913ca8", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159203078800, "endTime": 155159205524800}, "additional": {"logType": "info", "children": [], "durationId": "dd1dc77d-51ef-4094-8124-274265d2cebb"}}, {"head": {"id": "9ae49fb0-45ed-4810-9725-e0f1ccd93a79", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159210669400, "endTime": 155159214063100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9d606382-a077-4878-9845-b2017c482e7e", "logId": "ae945181-c85d-43fb-a45f-4a180407a340"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d606382-a077-4878-9845-b2017c482e7e", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159207345700}, "additional": {"logType": "detail", "children": [], "durationId": "9ae49fb0-45ed-4810-9725-e0f1ccd93a79"}}, {"head": {"id": "1de68022-8a7e-4b76-ac39-c84784ddccc9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159208846200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4013a0c-73a9-4766-b874-cb44f59ed81b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159209024000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89327df4-4f6c-4c4f-b551-6fa36a59e129", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159210684800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "934ee925-07b3-4a5e-8de4-9083dd871478", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159212295700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d403d82-3aed-4e8c-97eb-c81c51b7ef53", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159213890100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00788110-a0a9-473e-83ea-4311bd603910", "name": "entry : default@CreateBuildProfile cost memory 0.10747528076171875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159213998400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae945181-c85d-43fb-a45f-4a180407a340", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159210669400, "endTime": 155159214063100}, "additional": {"logType": "info", "children": [], "durationId": "9ae49fb0-45ed-4810-9725-e0f1ccd93a79"}}, {"head": {"id": "03224e08-ae4f-40a3-9580-380e3d35caaa", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159219426300, "endTime": 155159219975800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d6997ef7-35af-4552-800b-4c21ffdfb9e4", "logId": "6177a338-32fc-4d81-ab48-3531b616d1f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6997ef7-35af-4552-800b-4c21ffdfb9e4", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159217252400}, "additional": {"logType": "detail", "children": [], "durationId": "03224e08-ae4f-40a3-9580-380e3d35caaa"}}, {"head": {"id": "ed080e18-31ca-4519-bcbd-6875596b6a8a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159218450400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf08b3a7-0038-456e-aafd-075ef316afdd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159218587100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4767b640-95f0-4023-80a1-886d27904cd9", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159219435700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49ff846e-1fce-4056-bf9f-6a0e80f8d0cc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159219573600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d42cf8f-ec1f-4749-a6bd-1f828d96140e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159219625700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2df0432-ec78-4ce7-af49-280239bb9ce3", "name": "entry : default@PreCheckSyscap cost memory 0.0409698486328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159219823800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffd8e050-824a-4bed-8be7-ecc4152d8598", "name": "runTaskFromQueue task cost before running: 269 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159219930600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6177a338-32fc-4d81-ab48-3531b616d1f4", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159219426300, "endTime": 155159219975800, "totalTime": 485500}, "additional": {"logType": "info", "children": [], "durationId": "03224e08-ae4f-40a3-9580-380e3d35caaa"}}, {"head": {"id": "4af3f25b-01b6-444b-994a-7a9323e32a9e", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159225407800, "endTime": 155159231031300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e66e412a-e520-4818-93e5-273a29fecb09", "logId": "867a7731-81ca-43df-b860-5eddbf0f4963"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e66e412a-e520-4818-93e5-273a29fecb09", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159221611300}, "additional": {"logType": "detail", "children": [], "durationId": "4af3f25b-01b6-444b-994a-7a9323e32a9e"}}, {"head": {"id": "7cf48d3f-5e95-48a2-8930-0107e0f9de16", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159222774900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fe70f16-5a1a-4e81-bcf3-05b344f6bc99", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159222907200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4cd69b1-b236-4eba-889f-05e6154d7712", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159225418400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05179ffe-c9bf-4d94-8ea2-a4b046de077d", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159230063200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1afc6a69-27be-4f17-8fd5-fea5b113e9c7", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159230853200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33c8f0e3-f138-4920-9d52-8b36b5291e74", "name": "entry : default@GeneratePkgContextInfo cost memory 0.24869537353515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159230963300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "867a7731-81ca-43df-b860-5eddbf0f4963", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159225407800, "endTime": 155159231031300}, "additional": {"logType": "info", "children": [], "durationId": "4af3f25b-01b6-444b-994a-7a9323e32a9e"}}, {"head": {"id": "380fa321-81de-4803-bb69-e0b0f19deca2", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159238861400, "endTime": 155159241233800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "cacba3a5-e6a1-4366-b3b1-41351953fc99", "logId": "37f07664-35ad-41cf-bcce-8c5d3b1903e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cacba3a5-e6a1-4366-b3b1-41351953fc99", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159232598400}, "additional": {"logType": "detail", "children": [], "durationId": "380fa321-81de-4803-bb69-e0b0f19deca2"}}, {"head": {"id": "8c0ab887-199e-4e1e-8f04-794155aab74c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159233591000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8aedcca9-b4b0-4440-8a2d-3b6a69401e95", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159233704000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d39b743-faef-4173-93f6-fa6efbf6b0fa", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159238877700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f21a7cf8-d797-42f7-aaa9-e84e2eb38536", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159240811000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b47d7af0-8631-40aa-a3f8-3c19abeb05f2", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159240960900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bff8dc2-498d-4a92-b3b5-e2214c4d8370", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159241041300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e794e89-658e-4ccc-9019-2dc892831645", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159241078800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "186792ad-b8d2-425b-af23-b4d0dad35dd6", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1201629638671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159241141100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b48e31c-d131-487b-82c9-faccb2cf3f45", "name": "runTaskFromQueue task cost before running: 290 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159241197100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37f07664-35ad-41cf-bcce-8c5d3b1903e2", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159238861400, "endTime": 155159241233800, "totalTime": 2327900}, "additional": {"logType": "info", "children": [], "durationId": "380fa321-81de-4803-bb69-e0b0f19deca2"}}, {"head": {"id": "6afdd754-fe31-4d93-892f-b55f6d4cafb6", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159245385000, "endTime": 155159245956400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4bb45bbf-510b-4b18-96cc-64fed45909ef", "logId": "afac1b9d-9fb9-4b00-9bcd-ab757b4013f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bb45bbf-510b-4b18-96cc-64fed45909ef", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159243357900}, "additional": {"logType": "detail", "children": [], "durationId": "6afdd754-fe31-4d93-892f-b55f6d4cafb6"}}, {"head": {"id": "a1152a80-0cda-475e-8d55-3efcc5f25b0e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159244372500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05c6dd90-de78-4fb2-97af-4c8c3c33aa73", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159244508900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "362b7320-338c-4c8d-96bc-1b2403277f52", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159245401100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca0e0cb6-b1b2-4ec6-b790-d1922b01f2a1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159245601500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbf65dec-1433-4358-b64e-0251cb0dd7e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159245722200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53b56f8c-676a-477a-910f-ca5da18b3db9", "name": "entry : default@BuildNativeWithCmake cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159245842500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c573da5-1593-4452-97f4-3de8c5c2e430", "name": "runTaskFromQueue task cost before running: 295 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159245916300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afac1b9d-9fb9-4b00-9bcd-ab757b4013f1", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159245385000, "endTime": 155159245956400, "totalTime": 517700}, "additional": {"logType": "info", "children": [], "durationId": "6afdd754-fe31-4d93-892f-b55f6d4cafb6"}}, {"head": {"id": "3cfe4ce1-c656-4c31-b9d3-2028263493c3", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159249311400, "endTime": 155159253103200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "dac7448d-a61b-42ad-ac24-45a107353b49", "logId": "39bb16ad-4fca-43c6-af84-3a27d807c26e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dac7448d-a61b-42ad-ac24-45a107353b49", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159247528100}, "additional": {"logType": "detail", "children": [], "durationId": "3cfe4ce1-c656-4c31-b9d3-2028263493c3"}}, {"head": {"id": "37248c7b-12e3-484a-a2ae-6260c38934f8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159248509900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5ae9ddd-7d82-4479-81c7-c35ef141dbab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159248617600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "114f94ca-b68c-4470-8d71-f16287ef2433", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159249319200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ddbfc94-2063-4eab-b181-06119754f598", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159252885000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9f6f899-309f-49d5-a970-85980eb65c64", "name": "entry : default@MakePackInfo cost memory 0.20969390869140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159253024400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39bb16ad-4fca-43c6-af84-3a27d807c26e", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159249311400, "endTime": 155159253103200}, "additional": {"logType": "info", "children": [], "durationId": "3cfe4ce1-c656-4c31-b9d3-2028263493c3"}}, {"head": {"id": "cfe15d75-66dc-49ec-8323-92da2161302a", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159258240400, "endTime": 155159262212700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f49ec9db-8524-4511-9488-592e26c7d235", "logId": "b1a1ee02-036e-4cf7-8e1d-986208b56c35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f49ec9db-8524-4511-9488-592e26c7d235", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159255445400}, "additional": {"logType": "detail", "children": [], "durationId": "cfe15d75-66dc-49ec-8323-92da2161302a"}}, {"head": {"id": "7d3b9160-9016-4496-887c-3745a896bf44", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159256697400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd5b5af4-b9aa-4311-bb53-520a1e6d5fef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159256828100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29924845-6417-4220-b1dc-80bb383ca6ff", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159258250700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5738a3cf-ecc2-4da4-a7f0-da5b7bf50c87", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159258471700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dd9612d-e136-4dfb-8948-19a6100f010d", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159259378700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "937113a0-e276-4072-b99e-004b6cadd807", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159262011700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c66de395-2bfd-4403-966c-cac356a96076", "name": "entry : default@SyscapTransform cost memory 0.1498870849609375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159262142300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1a1ee02-036e-4cf7-8e1d-986208b56c35", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159258240400, "endTime": 155159262212700}, "additional": {"logType": "info", "children": [], "durationId": "cfe15d75-66dc-49ec-8323-92da2161302a"}}, {"head": {"id": "320a2f94-207f-44c1-b3ce-72c0fc9e95f7", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159266516500, "endTime": 155159268645200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "62b19e50-aa15-4061-b0a7-89b99bf596ce", "logId": "48927cc4-03f8-40af-80c7-998b11d6d1b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62b19e50-aa15-4061-b0a7-89b99bf596ce", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159263976900}, "additional": {"logType": "detail", "children": [], "durationId": "320a2f94-207f-44c1-b3ce-72c0fc9e95f7"}}, {"head": {"id": "9998e108-2cfc-46b1-8f79-91722525c1eb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159265181400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74d94072-4b97-417f-aa16-c7a2fa19099a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159265291800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ef9259b-1276-43ed-9a7c-649a49f29bf1", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159266526200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e485dea-b906-42eb-aa4c-078ea147fd19", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159268405600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6c396f8-156e-4b8a-bcea-929776992be9", "name": "entry : default@ProcessProfile cost memory 0.12371826171875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159268572100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48927cc4-03f8-40af-80c7-998b11d6d1b6", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159266516500, "endTime": 155159268645200}, "additional": {"logType": "info", "children": [], "durationId": "320a2f94-207f-44c1-b3ce-72c0fc9e95f7"}}, {"head": {"id": "963bf5a0-a029-48d1-8623-2383f26063b6", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159273168400, "endTime": 155159279612800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "420f2fa6-8b25-42fa-9993-8b4268a26ad0", "logId": "91659538-319f-4c6e-bf23-429e63da9b4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "420f2fa6-8b25-42fa-9993-8b4268a26ad0", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159270189800}, "additional": {"logType": "detail", "children": [], "durationId": "963bf5a0-a029-48d1-8623-2383f26063b6"}}, {"head": {"id": "9996eb2b-bbd9-4114-a488-ce6b153d3f33", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159271275800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca9a236b-6d88-4e8b-9344-4b122087c37c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159271412000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aff0a8a3-4e55-4d80-945f-ee777863de07", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159273205100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8b4030b-1cf0-400d-a9b1-628a0af6d100", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159279383800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87d1537e-1b00-4d4a-8baa-5c23c1a65349", "name": "entry : default@ProcessRouterMap cost memory 0.23193359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159279544400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91659538-319f-4c6e-bf23-429e63da9b4f", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159273168400, "endTime": 155159279612800}, "additional": {"logType": "info", "children": [], "durationId": "963bf5a0-a029-48d1-8623-2383f26063b6"}}, {"head": {"id": "360bbb10-b5da-4122-8c08-d0e7e04a242b", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159284644000, "endTime": 155159291570300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "9aa1ded9-9322-467c-8439-f027df21205a", "logId": "87f7c9ed-3974-4342-ae3c-e318c7d7c0cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9aa1ded9-9322-467c-8439-f027df21205a", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159282695300}, "additional": {"logType": "detail", "children": [], "durationId": "360bbb10-b5da-4122-8c08-d0e7e04a242b"}}, {"head": {"id": "0d8a93dc-d138-4c91-9d16-8b562f98fe59", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159284381200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4260227c-6f36-4467-9e68-95e4f82f9492", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159284524100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad8fa3b7-85a9-4d87-9cc0-2499674834e6", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159284651900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4166989f-6025-42e6-9168-f1033f3a21cc", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159284826100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2f0f3d6-cfab-458e-9781-96207a326e8d", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159289976200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18b02dc5-ee09-4dcd-87c9-72b9acbd4d6b", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159290142300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f32211cc-8408-422d-bcf7-cdd9f2747711", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159290241900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc797edd-5481-4395-9403-3d07f85442d0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159290282500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaeaee69-d8b1-40f8-9db8-1f5c6b998cd4", "name": "entry : default@ProcessStartupConfig cost memory 0.2572021484375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159291380700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1daf4abe-cda8-4c40-b09c-94070b214d44", "name": "runTaskFromQueue task cost before running: 340 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159291520300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87f7c9ed-3974-4342-ae3c-e318c7d7c0cf", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159284644000, "endTime": 155159291570300, "totalTime": 6853800}, "additional": {"logType": "info", "children": [], "durationId": "360bbb10-b5da-4122-8c08-d0e7e04a242b"}}, {"head": {"id": "85afe4db-f8e3-4464-b865-8cf33688befd", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159296688200, "endTime": 155159298037000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3d962229-ffbd-40af-98db-984b9e4221a8", "logId": "40019db9-70bf-40ae-a9d1-a964f048b9c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d962229-ffbd-40af-98db-984b9e4221a8", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159294560000}, "additional": {"logType": "detail", "children": [], "durationId": "85afe4db-f8e3-4464-b865-8cf33688befd"}}, {"head": {"id": "fb3c3f93-ceee-4879-a5f2-d6cc0b2994fa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159295794700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06a032a5-b954-490f-a092-046637645a4b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159295932300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cae66c6-2b88-4713-8774-6a1a115e20ab", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159296697500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42bc2c14-209c-461a-921e-fe4f9b183432", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159296835100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14e6bcb3-ec56-40f1-91b4-d32607a875ed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159296882100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90bfa4a6-1c25-4e9f-9a81-342839c7fea1", "name": "entry : default@BuildNativeWithNinja cost memory 0.05802154541015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159297827500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc4d04e4-e207-4efc-af9d-05c4d499aeee", "name": "runTaskFromQueue task cost before running: 347 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159297978500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40019db9-70bf-40ae-a9d1-a964f048b9c7", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159296688200, "endTime": 155159298037000, "totalTime": 1266100}, "additional": {"logType": "info", "children": [], "durationId": "85afe4db-f8e3-4464-b865-8cf33688befd"}}, {"head": {"id": "6f4c2e67-d0ae-4496-9f2d-be3a82f5aa9f", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159304862400, "endTime": 155159314001600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "df0e41dd-93c6-4e95-b095-c8da06ca2480", "logId": "e45d67af-9726-4aa7-a814-a8c50ca0ecf0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df0e41dd-93c6-4e95-b095-c8da06ca2480", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159300511800}, "additional": {"logType": "detail", "children": [], "durationId": "6f4c2e67-d0ae-4496-9f2d-be3a82f5aa9f"}}, {"head": {"id": "5ef6e918-9bf8-4901-9c18-78a569c247e6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159301674400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56a9af66-89b7-450b-b913-df129339d296", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159301824800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e52ff57a-2fb9-474c-9a62-26f0598795a7", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159303382400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08c03ba8-b810-4db6-8dba-70ab2137c7ed", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159306413100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c39b367f-a51d-4fde-a7c9-8e86866eb47b", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159310046600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c88e7a15-87b3-4f60-a76d-789f8906ebd1", "name": "entry : default@ProcessResource cost memory 0.16131591796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159310262900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e45d67af-9726-4aa7-a814-a8c50ca0ecf0", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159304862400, "endTime": 155159314001600}, "additional": {"logType": "info", "children": [], "durationId": "6f4c2e67-d0ae-4496-9f2d-be3a82f5aa9f"}}, {"head": {"id": "236dea95-d47e-429e-8ca6-3fcdd4d61501", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159322328500, "endTime": 155159346056400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ac16b6ee-fc24-40ea-8db2-4ed2926995c4", "logId": "3cc3b0f0-5161-4160-b873-d39b88a7ec32"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac16b6ee-fc24-40ea-8db2-4ed2926995c4", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159317885700}, "additional": {"logType": "detail", "children": [], "durationId": "236dea95-d47e-429e-8ca6-3fcdd4d61501"}}, {"head": {"id": "b2af0389-8f61-4c03-a7d7-7e218cb96d4a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159319000800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92598954-f76e-490b-9820-5975987c3cee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159319135200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae9f9951-9fb8-4438-bac7-1a9a86bf9614", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159322341500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d99b8fa-d7df-46d9-9421-6e6db80b68f3", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159345845400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7992b133-2443-497c-a72d-4c4138bffe86", "name": "entry : default@GenerateLoaderJson cost memory -4.831611633300781", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159345995600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cc3b0f0-5161-4160-b873-d39b88a7ec32", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159322328500, "endTime": 155159346056400}, "additional": {"logType": "info", "children": [], "durationId": "236dea95-d47e-429e-8ca6-3fcdd4d61501"}}, {"head": {"id": "6484fc21-8056-48a2-9e7d-7a2d02ae61ae", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159355830300, "endTime": 155159360078100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "91a21211-0adc-4974-a76e-392c60a9b96a", "logId": "303f238f-0a14-422e-a524-1761c99087f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91a21211-0adc-4974-a76e-392c60a9b96a", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159354150100}, "additional": {"logType": "detail", "children": [], "durationId": "6484fc21-8056-48a2-9e7d-7a2d02ae61ae"}}, {"head": {"id": "e4067a7a-3806-4696-b20e-e1b78080de45", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159355071100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cce2589-d9b1-4c14-b58c-49a1473562ed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159355174300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b7406f2-15e3-405d-951f-8df9c2e34a90", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159355839400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf3945c6-dc7b-474d-96a7-378d57a2f754", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159359875800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b56d0663-3164-413a-8806-6f8629eb5a9e", "name": "entry : default@ProcessLibs cost memory 0.14153289794921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159360014400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "303f238f-0a14-422e-a524-1761c99087f6", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159355830300, "endTime": 155159360078100}, "additional": {"logType": "info", "children": [], "durationId": "6484fc21-8056-48a2-9e7d-7a2d02ae61ae"}}, {"head": {"id": "54669d20-2091-4cf7-b211-9deab1f2a681", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159366171400, "endTime": 155159394034000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "f6fe31b0-3c85-4d95-96a0-cc6434d76e2e", "logId": "b495e624-195f-4ee4-b372-aba69f0a8ba2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6fe31b0-3c85-4d95-96a0-cc6434d76e2e", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159361861800}, "additional": {"logType": "detail", "children": [], "durationId": "54669d20-2091-4cf7-b211-9deab1f2a681"}}, {"head": {"id": "9b89e92a-f3ae-4889-b529-b55e662ca1dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159362849300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fac4cf7-a352-4c09-b57b-cdeef5432ab7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159362953900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46584490-b304-424f-9b02-d196d97d92e6", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159363774300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1b54f2e-e472-4fb8-9fdb-2c9469e83000", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159366197600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "656bfdb3-7b18-48e9-bf9d-42dfabe68d2d", "name": "Incremental task entry:default@CompileResource pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159393822800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3912d2de-64ff-4205-97b8-fee7edb2a2f8", "name": "entry : default@CompileResource cost memory 1.3146820068359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159393958200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b495e624-195f-4ee4-b372-aba69f0a8ba2", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159366171400, "endTime": 155159394034000}, "additional": {"logType": "info", "children": [], "durationId": "54669d20-2091-4cf7-b211-9deab1f2a681"}}, {"head": {"id": "73b47d81-d14d-410a-9f4d-eafefadc3482", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159399641300, "endTime": 155159402780900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "1cecfab4-2ce1-4ae7-b909-70d75190f4bc", "logId": "7f0ed77d-5826-4c3a-99a9-fd80067e895e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1cecfab4-2ce1-4ae7-b909-70d75190f4bc", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159396400300}, "additional": {"logType": "detail", "children": [], "durationId": "73b47d81-d14d-410a-9f4d-eafefadc3482"}}, {"head": {"id": "35b3957a-7ba6-4783-88bb-c791dc98a983", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159397469600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ce18fe3-ed15-460c-9e94-65829aee49e4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159397581800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad96cc09-e7c8-4a4a-a8d2-e9d39bd2d97b", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159399655600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9ae5ef3-502d-4dec-bb76-f21f55b2f032", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159400271600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "402d96b4-03c4-400f-8643-99a4ceb2edc9", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159402571400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4977c029-5112-4f2f-a459-9b2ef3ec39a5", "name": "entry : default@DoNativeStrip cost memory 0.0793304443359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159402714400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f0ed77d-5826-4c3a-99a9-fd80067e895e", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159399641300, "endTime": 155159402780900}, "additional": {"logType": "info", "children": [], "durationId": "73b47d81-d14d-410a-9f4d-eafefadc3482"}}, {"head": {"id": "a37d97ff-463b-4ae0-b5c3-0bbf4262e7e3", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159408698200, "endTime": 155169207393900}, "additional": {"children": ["c8551109-07c5-40bf-a269-3672f4af6e30", "500db36c-733d-4737-9038-7ff937833c4b"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "42697e36-669b-4290-9da2-c8c8bafce2e5", "logId": "6d0f0864-9783-4a06-838f-b126abd5de6c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42697e36-669b-4290-9da2-c8c8bafce2e5", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159404174400}, "additional": {"logType": "detail", "children": [], "durationId": "a37d97ff-463b-4ae0-b5c3-0bbf4262e7e3"}}, {"head": {"id": "fabd1ef4-0e07-40fd-901e-1fa610d15d3d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159405033100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e301f950-94c4-4dd0-9f55-5f4f2000e3c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159405148500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b389ec5-2849-4175-abb3-3c7c6cf20858", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159408711900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93a64b94-4e1e-4484-9441-dbc91ff86c90", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159408898000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a70b50f6-aa6a-41ee-a9c1-68a215c49eb1", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159435514000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1991133e-854c-46d1-bce6-e0b200fbd6dc", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 21 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159435697000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f38a8e36-ab40-435d-9f3b-b6437e8da1dd", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159453112200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7be35a84-ccdb-4992-a635-bb5c7c778893", "name": "default@CompileArkTS work[18] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159454649500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8551109-07c5-40bf-a269-3672f4af6e30", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 155162177925300, "endTime": 155169200670800}, "additional": {"children": ["a69598bd-380b-43c3-b0d0-f4cf6ff9dfb8", "bab1204b-30d8-4589-9859-a53c4e8a3cd3", "dad5d1ec-a74c-4c99-a8c9-beb14a0692ea", "c06d30e5-050d-496f-93ac-0dc85b1b48fa", "692631c5-d9b6-4c0b-90e5-8ed558311bef", "b2d2b107-5372-45f6-8073-c9b432fd1d12", "9c520645-54e2-486f-8620-6ab762a3a5c9"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "a37d97ff-463b-4ae0-b5c3-0bbf4262e7e3", "logId": "84ecf646-6804-44f3-a722-c19eb64224cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7f1b2cc-cf2d-41d1-8838-87f428c6aa94", "name": "default@CompileArkTS work[18] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455486200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af853758-ddda-4ec1-8a47-21691fd906cd", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455570000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c8979fa-c4f3-4620-8060-748703095881", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455605800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bcda7c9-da20-4af6-b173-911210c63472", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455629500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ad0a8e8-e0be-48ff-b69d-6fadeefd6983", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455652100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3564c0b6-989b-412b-bce4-8cdf268655c0", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455676500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a19bc891-543d-43df-9d61-0e5bd51bb30f", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455698700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "451e926b-b6e5-42c3-b4ee-3379ddcc7a1f", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455726400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa1de952-89c3-417e-b04f-8d6c0ff56f7b", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455749600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29ee3a6a-edc4-42f6-b9f1-b2f09d9ac86f", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455771500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1137ef7-d84c-4ac1-a063-21ddafd20dc8", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455792400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0db62509-f7b5-4143-a644-58ffb0f71457", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455814500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddd3d085-82de-47ea-a9e8-41d6e81e3da3", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455835500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63ff10de-83a8-465c-9756-bfc800130eca", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455857000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96973746-4fb5-4408-bafe-a8216ddfccc9", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455879500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7a84c94-ff8b-4b51-a1aa-c093e5aa092f", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455905900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a3622f1-efb7-43fb-8b16-33f00b597c0a", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159455955500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b30b019c-9467-40a1-b64b-6ff0d48b1f68", "name": "default@CompileArkTS work[18] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159456722000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3903c98f-8b72-44ce-9b39-46f46df9ffda", "name": "default@CompileArkTS work[18] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159456792900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31786dc2-5bad-4617-b94c-5c06775283b3", "name": "CopyResources startTime: 155159456840300", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159456842100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b3f010a-cf26-4d4f-b2a3-17751cd2b7d2", "name": "default@CompileArkTS work[19] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159456901300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "500db36c-733d-4737-9038-7ff937833c4b", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 155160582774000, "endTime": 155160597222700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "a37d97ff-463b-4ae0-b5c3-0bbf4262e7e3", "logId": "61f2c945-1457-4093-a462-d87a928adb93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81ecf924-4228-4fef-9b8e-0e6eb418be08", "name": "default@CompileArkTS work[19] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159457707800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50f72533-63f1-4c1d-9399-45e3b8a4f551", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159457799100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "826151e8-9377-4e35-8289-0535ef09b460", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159457851100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34ed1168-bd2c-47a5-8f5e-4582c9b926ef", "name": "default@CompileArkTS work[19] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159458663300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15bc3f52-1877-44ea-abe5-4dfa80e66bbc", "name": "default@CompileArkTS work[19] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159458754200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0254206-d14c-41e4-abaa-1dfacd6107fa", "name": "entry : default@CompileArkTS cost memory 2.4820785522460938", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159458847800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbc488c5-b4f8-46de-92a4-9c4c10f9db41", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159466028400, "endTime": 155159476022600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "7c552e1b-050f-4f12-bb24-bd936b31dde8", "logId": "402ae9b4-0301-4133-9cd9-17be909595bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c552e1b-050f-4f12-bb24-bd936b31dde8", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159460483400}, "additional": {"logType": "detail", "children": [], "durationId": "dbc488c5-b4f8-46de-92a4-9c4c10f9db41"}}, {"head": {"id": "d618fed9-b112-4935-819e-bced954c19d3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159461554500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d302973-1754-4b28-8b3e-7f33926d46ba", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159461667200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7f9133d-e94f-495e-9aaa-9d5861418c26", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159466040300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d979bd33-fda1-493e-ac8e-3b38780ec480", "name": "entry : default@BuildJS cost memory 0.341522216796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159475589400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2097e7a4-621e-4c2f-aec8-6fd564a84512", "name": "runTaskFromQueue task cost before running: 525 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159475873200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "402ae9b4-0301-4133-9cd9-17be909595bf", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159466028400, "endTime": 155159476022600, "totalTime": 9798400}, "additional": {"logType": "info", "children": [], "durationId": "dbc488c5-b4f8-46de-92a4-9c4c10f9db41"}}, {"head": {"id": "e36046a5-2282-4914-b311-9422719468af", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159482417600, "endTime": 155159485602500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8339cd5f-d323-471a-98c3-040ad3f9f5bb", "logId": "bdebcfeb-a48b-4f94-be27-6100c2349d4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8339cd5f-d323-471a-98c3-040ad3f9f5bb", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159478043600}, "additional": {"logType": "detail", "children": [], "durationId": "e36046a5-2282-4914-b311-9422719468af"}}, {"head": {"id": "9997643c-16c9-444d-83e8-c118a7fade9b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159479531300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffb0f1e8-b330-41ff-957c-8a886a5fac28", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159479688800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bfee669-ae50-4d71-89cc-8fd93d339fe0", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159482427500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d2a33f6-999a-4de5-9651-259de03c18ac", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159483287700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "473dd389-9456-46e9-a604-9ff1ac08ed2a", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159485401100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ece37a67-d90b-4ae8-9f9c-cf87eea9a14c", "name": "entry : default@CacheNativeLibs cost memory 0.09461212158203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159485530500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdebcfeb-a48b-4f94-be27-6100c2349d4e", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159482417600, "endTime": 155159485602500}, "additional": {"logType": "info", "children": [], "durationId": "e36046a5-2282-4914-b311-9422719468af"}}, {"head": {"id": "411bba02-a067-4cad-b4e3-894b4c11053d", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155160597785900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a497a9cf-070e-4c24-bf45-585f15d73348", "name": "CopyResources is end, endTime: 155160598033800", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155160598039500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "796b7536-f41b-4e9e-937e-1c4097c73f5b", "name": "default@CompileArkTS work[19] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155160598258200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61f2c945-1457-4093-a462-d87a928adb93", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 155160582774000, "endTime": 155160597222700}, "additional": {"logType": "info", "children": [], "durationId": "500db36c-733d-4737-9038-7ff937833c4b", "parent": "6d0f0864-9783-4a06-838f-b126abd5de6c"}}, {"head": {"id": "837f1c75-69cf-48fb-8987-ac5c418aa260", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155160598361900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2774aa28-b4f3-4e15-a39d-4b822a8e8c9a", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169201140000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a69598bd-380b-43c3-b0d0-f4cf6ff9dfb8", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 155162178895300, "endTime": 155163204423700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c8551109-07c5-40bf-a269-3672f4af6e30", "logId": "6f9516de-0983-41fa-a5fd-eca6ed985fba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f9516de-0983-41fa-a5fd-eca6ed985fba", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155162178895300, "endTime": 155163204423700}, "additional": {"logType": "info", "children": [], "durationId": "a69598bd-380b-43c3-b0d0-f4cf6ff9dfb8", "parent": "84ecf646-6804-44f3-a722-c19eb64224cd"}}, {"head": {"id": "bab1204b-30d8-4589-9859-a53c4e8a3cd3", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 155163205908200, "endTime": 155163276928100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c8551109-07c5-40bf-a269-3672f4af6e30", "logId": "82885b28-1fde-47d6-802d-50da3afd56be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82885b28-1fde-47d6-802d-50da3afd56be", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155163205908200, "endTime": 155163276928100}, "additional": {"logType": "info", "children": [], "durationId": "bab1204b-30d8-4589-9859-a53c4e8a3cd3", "parent": "84ecf646-6804-44f3-a722-c19eb64224cd"}}, {"head": {"id": "dad5d1ec-a74c-4c99-a8c9-beb14a0692ea", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 155163277026500, "endTime": 155163277251800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c8551109-07c5-40bf-a269-3672f4af6e30", "logId": "2f4a2be7-039e-4af2-8211-006d96dd0032"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f4a2be7-039e-4af2-8211-006d96dd0032", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155163277026500, "endTime": 155163277251800}, "additional": {"logType": "info", "children": [], "durationId": "dad5d1ec-a74c-4c99-a8c9-beb14a0692ea", "parent": "84ecf646-6804-44f3-a722-c19eb64224cd"}}, {"head": {"id": "c06d30e5-050d-496f-93ac-0dc85b1b48fa", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 155163277311400, "endTime": 155168959159700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c8551109-07c5-40bf-a269-3672f4af6e30", "logId": "cfa906e0-8341-4951-b99a-1762bfb6d3a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfa906e0-8341-4951-b99a-1762bfb6d3a9", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155163277311400, "endTime": 155168959159700}, "additional": {"logType": "info", "children": [], "durationId": "c06d30e5-050d-496f-93ac-0dc85b1b48fa", "parent": "84ecf646-6804-44f3-a722-c19eb64224cd"}}, {"head": {"id": "692631c5-d9b6-4c0b-90e5-8ed558311bef", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 155168959434400, "endTime": 155168975562800}, "additional": {"children": ["55680f00-9495-4154-9569-bc8cd3b9167b", "bec81709-f413-4470-b443-52adaa9ba892", "ed778b76-7dfc-4c3d-aa0e-31223a6c8f5c"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c8551109-07c5-40bf-a269-3672f4af6e30", "logId": "83a52bdc-75fc-4337-bc8c-eae6e5205c8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83a52bdc-75fc-4337-bc8c-eae6e5205c8a", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155168959434400, "endTime": 155168975562800}, "additional": {"logType": "info", "children": ["354e238a-d0d0-4b53-9ec2-2c8cb486cc30", "1fdc1604-3e46-4242-bc86-9368c3b0c551", "6cd8fa36-ca87-4f00-8839-e82fb8464d3c"], "durationId": "692631c5-d9b6-4c0b-90e5-8ed558311bef", "parent": "84ecf646-6804-44f3-a722-c19eb64224cd"}}, {"head": {"id": "55680f00-9495-4154-9569-bc8cd3b9167b", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 155168959604200, "endTime": 155168959623700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "692631c5-d9b6-4c0b-90e5-8ed558311bef", "logId": "354e238a-d0d0-4b53-9ec2-2c8cb486cc30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "354e238a-d0d0-4b53-9ec2-2c8cb486cc30", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155168959604200, "endTime": 155168959623700}, "additional": {"logType": "info", "children": [], "durationId": "55680f00-9495-4154-9569-bc8cd3b9167b", "parent": "83a52bdc-75fc-4337-bc8c-eae6e5205c8a"}}, {"head": {"id": "bec81709-f413-4470-b443-52adaa9ba892", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 155168959635600, "endTime": 155168968048200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "692631c5-d9b6-4c0b-90e5-8ed558311bef", "logId": "1fdc1604-3e46-4242-bc86-9368c3b0c551"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1fdc1604-3e46-4242-bc86-9368c3b0c551", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155168959635600, "endTime": 155168968048200}, "additional": {"logType": "info", "children": [], "durationId": "bec81709-f413-4470-b443-52adaa9ba892", "parent": "83a52bdc-75fc-4337-bc8c-eae6e5205c8a"}}, {"head": {"id": "ed778b76-7dfc-4c3d-aa0e-31223a6c8f5c", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 155168968056300, "endTime": 155168975412000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "692631c5-d9b6-4c0b-90e5-8ed558311bef", "logId": "6cd8fa36-ca87-4f00-8839-e82fb8464d3c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6cd8fa36-ca87-4f00-8839-e82fb8464d3c", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155168968056300, "endTime": 155168975412000}, "additional": {"logType": "info", "children": [], "durationId": "ed778b76-7dfc-4c3d-aa0e-31223a6c8f5c", "parent": "83a52bdc-75fc-4337-bc8c-eae6e5205c8a"}}, {"head": {"id": "b2d2b107-5372-45f6-8073-c9b432fd1d12", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 155168975606600, "endTime": 155169197750300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c8551109-07c5-40bf-a269-3672f4af6e30", "logId": "f1cad79b-c333-4242-a887-bb40dff7c919"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1cad79b-c333-4242-a887-bb40dff7c919", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155168975606600, "endTime": 155169197750300}, "additional": {"logType": "info", "children": [], "durationId": "b2d2b107-5372-45f6-8073-c9b432fd1d12", "parent": "84ecf646-6804-44f3-a722-c19eb64224cd"}}, {"head": {"id": "9c520645-54e2-486f-8620-6ab762a3a5c9", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 155160490672400, "endTime": 155162176126200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c8551109-07c5-40bf-a269-3672f4af6e30", "logId": "2701ad9d-904e-42df-809c-5e5b4989d09c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2701ad9d-904e-42df-809c-5e5b4989d09c", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155160490672400, "endTime": 155162176126200}, "additional": {"logType": "info", "children": [], "durationId": "9c520645-54e2-486f-8620-6ab762a3a5c9", "parent": "84ecf646-6804-44f3-a722-c19eb64224cd"}}, {"head": {"id": "d5add91e-75ec-455d-b990-5062f228b30b", "name": "default@CompileArkTS work[18] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169207156000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84ecf646-6804-44f3-a722-c19eb64224cd", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 155162177925300, "endTime": 155169200670800}, "additional": {"logType": "info", "children": ["6f9516de-0983-41fa-a5fd-eca6ed985fba", "82885b28-1fde-47d6-802d-50da3afd56be", "2f4a2be7-039e-4af2-8211-006d96dd0032", "cfa906e0-8341-4951-b99a-1762bfb6d3a9", "83a52bdc-75fc-4337-bc8c-eae6e5205c8a", "f1cad79b-c333-4242-a887-bb40dff7c919", "2701ad9d-904e-42df-809c-5e5b4989d09c"], "durationId": "c8551109-07c5-40bf-a269-3672f4af6e30", "parent": "6d0f0864-9783-4a06-838f-b126abd5de6c"}}, {"head": {"id": "3551ab93-57c0-4d20-9639-9d2f265bcab3", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169207332500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d0f0864-9783-4a06-838f-b126abd5de6c", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155159408698200, "endTime": 155169207393900, "totalTime": 7087413400}, "additional": {"logType": "info", "children": ["84ecf646-6804-44f3-a722-c19eb64224cd", "61f2c945-1457-4093-a462-d87a928adb93"], "durationId": "a37d97ff-463b-4ae0-b5c3-0bbf4262e7e3"}}, {"head": {"id": "d7e269c2-015e-4b31-bb69-0e8591e6c0c8", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169214427500, "endTime": 155169215613500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "5c3b5209-3f77-49dd-9b48-9a69fdd616d5", "logId": "9893b419-9e37-4b4b-9be3-adce6de9cff7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c3b5209-3f77-49dd-9b48-9a69fdd616d5", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169212627900}, "additional": {"logType": "detail", "children": [], "durationId": "d7e269c2-015e-4b31-bb69-0e8591e6c0c8"}}, {"head": {"id": "0d38c399-6d58-47f4-9032-6e2f8653393b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169213486500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcfb66df-602b-4cf5-9e69-9cee53d3d62d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169213572700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35e01e16-8ef7-499e-9289-5eea79809dfc", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169214435900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "541713ea-5193-415b-83e4-0e4e53dcf0ea", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169214699600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c3aa317-254c-4dbd-ae2e-cb908a4da6c7", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169215479100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53728e8d-f658-4026-8713-c16ecb9f25d8", "name": "entry : default@GeneratePkgModuleJson cost memory 0.077850341796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169215562600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9893b419-9e37-4b4b-9be3-adce6de9cff7", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169214427500, "endTime": 155169215613500}, "additional": {"logType": "info", "children": [], "durationId": "d7e269c2-015e-4b31-bb69-0e8591e6c0c8"}}, {"head": {"id": "35ec300f-84af-4160-aaf4-dc897453ce42", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169224624700, "endTime": 155169380641700}, "additional": {"children": ["0e34d668-5a1b-4b3a-af1c-65be78fe31d1", "38a0b947-b26a-4ad5-a46d-d73bda16518e"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "6e5657f8-65d4-4527-b5d5-ddcceccb7cf3", "logId": "26d04add-5bae-4d96-86c9-8a47627b2158"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e5657f8-65d4-4527-b5d5-ddcceccb7cf3", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169217534600}, "additional": {"logType": "detail", "children": [], "durationId": "35ec300f-84af-4160-aaf4-dc897453ce42"}}, {"head": {"id": "27dc6268-063d-4d6e-9be6-1b5feb41e2e8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169218325800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a41440c1-52d2-4a0d-920c-be13e4a869cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169218430300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cb0d210-2437-44ed-9fd3-e0ee3426329f", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169224637700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fe3e5f2-dca0-4fba-b1e0-56bbcee98bcc", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169241103800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dcf53bc-7265-460d-98b5-a8ef5203cf83", "name": "Incremental task entry:default@PackageHap pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169241297300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddc1064e-18ab-4b62-93af-3ed93542160f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169241380600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "266806d9-eaf0-4b6d-8b3e-2118977d1a46", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169241417400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e34d668-5a1b-4b3a-af1c-65be78fe31d1", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169242914700, "endTime": 155169245024000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35ec300f-84af-4160-aaf4-dc897453ce42", "logId": "af547659-49da-4cf6-b9b7-f23674de1d02"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d42f81c0-90b6-410c-8a19-4ed246c0ecaf", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169244868200}, "additional": {"logType": "debug", "children": [], "durationId": "35ec300f-84af-4160-aaf4-dc897453ce42"}}, {"head": {"id": "af547659-49da-4cf6-b9b7-f23674de1d02", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169242914700, "endTime": 155169245024000}, "additional": {"logType": "info", "children": [], "durationId": "0e34d668-5a1b-4b3a-af1c-65be78fe31d1", "parent": "26d04add-5bae-4d96-86c9-8a47627b2158"}}, {"head": {"id": "38a0b947-b26a-4ad5-a46d-d73bda16518e", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169245572300, "endTime": 155169375776600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35ec300f-84af-4160-aaf4-dc897453ce42", "logId": "d6da0907-25f1-4da6-8a59-5edd89931bcc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99a74738-c441-4729-bb38-f9b2d0a3f4ac", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169375048300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6da0907-25f1-4da6-8a59-5edd89931bcc", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169245572300, "endTime": 155169375766900}, "additional": {"logType": "info", "children": [], "durationId": "38a0b947-b26a-4ad5-a46d-d73bda16518e", "parent": "26d04add-5bae-4d96-86c9-8a47627b2158"}}, {"head": {"id": "8153e420-410b-428d-8904-0afce2630425", "name": "entry : default@PackageHap cost memory 1.5669097900390625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169380447100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bdf1831-75f1-4cf0-b3fe-e908598bc6da", "name": "runTaskFromQueue task cost before running: 10 s 430 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169380590000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26d04add-5bae-4d96-86c9-8a47627b2158", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169224624700, "endTime": 155169380641700, "totalTime": 155944200}, "additional": {"logType": "info", "children": ["af547659-49da-4cf6-b9b7-f23674de1d02", "d6da0907-25f1-4da6-8a59-5edd89931bcc"], "durationId": "35ec300f-84af-4160-aaf4-dc897453ce42"}}, {"head": {"id": "0c1f896e-0031-49ae-8f02-2ad44d8e7695", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169386500700, "endTime": 155169653360600}, "additional": {"children": ["22e47412-ed5d-4423-a8e8-9e185bdcb383", "df1ae79c-067e-4b9f-84dc-8ce1309f330e"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "4666ef9c-9a97-4b34-9405-14e23db2078c", "logId": "6e828b96-092a-4d44-af56-615cc729cbdb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4666ef9c-9a97-4b34-9405-14e23db2078c", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169383607700}, "additional": {"logType": "detail", "children": [], "durationId": "0c1f896e-0031-49ae-8f02-2ad44d8e7695"}}, {"head": {"id": "5edc3f6b-a7f7-423b-89ba-974ea7365096", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169384480200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d3fd9b8-eea3-42d2-a522-00ab1fbb7637", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169384560500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f542c229-7f7c-4e3f-b036-645051b40d4c", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169386508200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccf1f9ed-bfb2-47f6-8157-01c2f7b76824", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169387885300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dc771bc-ef05-4ecb-8cc5-c890fc3866c8", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169387974100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af3edf19-b0ef-4c26-b1de-756de04f4a90", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169388024700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be6cd76d-d5de-4825-82cb-27ee67433288", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169388061200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22e47412-ed5d-4423-a8e8-9e185bdcb383", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169389372700, "endTime": 155169462565300}, "additional": {"children": ["9f49fc44-1403-4d9b-b233-adad58a3294b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c1f896e-0031-49ae-8f02-2ad44d8e7695", "logId": "8a44b26a-52e1-432c-a17c-065cb579e8da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f49fc44-1403-4d9b-b233-adad58a3294b", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169403837600, "endTime": 155169461458200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "22e47412-ed5d-4423-a8e8-9e185bdcb383", "logId": "99a70be4-e83c-48ff-adb6-d0f13d08740c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5925f232-4319-461f-855b-d56f83bd037f", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169406519000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb2a90bc-70d0-490f-ab9b-c5aa2365e913", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169461080200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99a70be4-e83c-48ff-adb6-d0f13d08740c", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169403837600, "endTime": 155169461458200}, "additional": {"logType": "info", "children": [], "durationId": "9f49fc44-1403-4d9b-b233-adad58a3294b", "parent": "8a44b26a-52e1-432c-a17c-065cb579e8da"}}, {"head": {"id": "8a44b26a-52e1-432c-a17c-065cb579e8da", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169389372700, "endTime": 155169462565300}, "additional": {"logType": "info", "children": ["99a70be4-e83c-48ff-adb6-d0f13d08740c"], "durationId": "22e47412-ed5d-4423-a8e8-9e185bdcb383", "parent": "6e828b96-092a-4d44-af56-615cc729cbdb"}}, {"head": {"id": "df1ae79c-067e-4b9f-84dc-8ce1309f330e", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169463174000, "endTime": 155169652862600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c1f896e-0031-49ae-8f02-2ad44d8e7695", "logId": "06dc8e12-103f-461b-a0f2-73d44ac9efe8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f91f7d50-a9ba-467a-95a4-1ac8c9c96010", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169464944500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e88f3921-b189-4b9f-aca8-e429ca673e0a", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169652383500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06dc8e12-103f-461b-a0f2-73d44ac9efe8", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169463174000, "endTime": 155169652862600}, "additional": {"logType": "info", "children": [], "durationId": "df1ae79c-067e-4b9f-84dc-8ce1309f330e", "parent": "6e828b96-092a-4d44-af56-615cc729cbdb"}}, {"head": {"id": "9bd5f364-0a3b-4684-8f1c-e90f36b331ec", "name": "entry : default@SignHap cost memory -4.810943603515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169653191200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73ed1353-4d78-4c69-928c-652d34219b35", "name": "runTaskFromQueue task cost before running: 10 s 702 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169653309800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e828b96-092a-4d44-af56-615cc729cbdb", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169386500700, "endTime": 155169653360600, "totalTime": 266783200}, "additional": {"logType": "info", "children": ["8a44b26a-52e1-432c-a17c-065cb579e8da", "06dc8e12-103f-461b-a0f2-73d44ac9efe8"], "durationId": "0c1f896e-0031-49ae-8f02-2ad44d8e7695"}}, {"head": {"id": "c4831663-69e0-4ae2-b6ae-beabb180a39c", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169656633800, "endTime": 155169661152500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4ab5250b-19c8-442f-b9d1-f843cdba9bd0", "logId": "096d7286-f11e-4e0c-8030-665294c3ee20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ab5250b-19c8-442f-b9d1-f843cdba9bd0", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169655126300}, "additional": {"logType": "detail", "children": [], "durationId": "c4831663-69e0-4ae2-b6ae-beabb180a39c"}}, {"head": {"id": "b1fe1d30-3f21-4208-9be0-4f732fafe890", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169655918800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8485d70f-3db0-4410-975f-6f518f4bb65f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169656002100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55aa724c-3033-410e-bdc8-455cf7231572", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169656640600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "838d5f9e-508b-476c-ae41-895b3c1f120a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169660913900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e2f1393-dc18-4127-a645-a7e5600d2305", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169660984300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f22b91da-f3f3-4371-9062-6f616b8f2249", "name": "entry : default@CollectDebugSymbol cost memory 0.243408203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169661062500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c0a135c-9c68-4d9e-b843-ae2491c639e2", "name": "runTaskFromQueue task cost before running: 10 s 710 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169661121500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "096d7286-f11e-4e0c-8030-665294c3ee20", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169656633800, "endTime": 155169661152500, "totalTime": 4474100}, "additional": {"logType": "info", "children": [], "durationId": "c4831663-69e0-4ae2-b6ae-beabb180a39c"}}, {"head": {"id": "0f5cc988-4363-4006-bd6b-526c168f5030", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169662382400, "endTime": 155169662576000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "f6d32a34-bb83-4983-8926-0b2ac5021f85", "logId": "ac6b98e5-ac52-43a2-a27b-fba1d6c21f53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6d32a34-bb83-4983-8926-0b2ac5021f85", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169662352900}, "additional": {"logType": "detail", "children": [], "durationId": "0f5cc988-4363-4006-bd6b-526c168f5030"}}, {"head": {"id": "f8230087-33ee-4409-8af0-fdbd61acc7d9", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169662386800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c37460-ab19-4a32-886c-8e0896b391b7", "name": "entry : assembleHap cost memory 0.0117034912109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169662484400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b2ebb86-0141-4413-8c46-fc98547e92d6", "name": "runTaskFromQueue task cost before running: 10 s 712 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169662543000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac6b98e5-ac52-43a2-a27b-fba1d6c21f53", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169662382400, "endTime": 155169662576000, "totalTime": 147500}, "additional": {"logType": "info", "children": [], "durationId": "0f5cc988-4363-4006-bd6b-526c168f5030"}}, {"head": {"id": "ce83fd22-d532-4e49-8599-3d6b2d108490", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169675937200, "endTime": 155169675961700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "640f9569-b487-4a74-b573-10dc2b9ab66a", "logId": "76204e5a-2e06-4487-a358-e8d3ac51989a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76204e5a-2e06-4487-a358-e8d3ac51989a", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169675937200, "endTime": 155169675961700}, "additional": {"logType": "info", "children": [], "durationId": "ce83fd22-d532-4e49-8599-3d6b2d108490"}}, {"head": {"id": "b61bd0ff-f39e-48b8-ab4d-f2810c6cd197", "name": "BUILD SUCCESSFUL in 10 s 725 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169676039800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "55795d25-949f-4767-b35a-c4d474727f6b", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155158951504300, "endTime": 155169676460400}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 3, "second": 19}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "c4a32a8a-c150-4ea1-a2df-b886e57de7df", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169676551900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6982f40-7745-4a02-8541-b91ac6efdbdc", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169676630200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3271e30d-f145-4da2-abb9-8b87ab11fa6d", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169676982300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff2d77f9-659b-4041-ac6d-9d136f4c6a01", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169677049300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b817437e-7c26-42f5-be22-35f8e8bdc36c", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169677085200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cae8540-2a20-4777-9c87-bebac61b1f05", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169677134100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f66e4f8d-a850-44d1-abca-f0704931fbfb", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169677163400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9384c02e-7432-4c69-a4d3-942ad57bc366", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169677659200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f07968a6-0e46-4d6a-9372-3e22dadd0494", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169677842800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f721d7c-c21f-4f70-9110-dbd48d9ff777", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169677893500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "559b0988-cedc-456f-9368-360f4e3a8abd", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169677924200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dd60203-2fa9-477d-8c12-c24709b40eee", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169677950300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18abfecc-dcdb-48a9-8b3a-b55b6789c380", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169677977700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e400c85-acc8-4bb4-867f-b37ca8e32570", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169678828700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60842fd9-d577-45d5-942f-b3c20c4a4418", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169679076100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "250bcf1e-806e-487e-afbd-bb05d777cce4", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169679248300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cf6f7d1-4336-42b6-a8f5-26c0a3c1c96d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169679301900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4560b74e-11c6-4da8-9e26-59299f9385f6", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169679337600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d124086-fe59-4196-a97e-effdf1753540", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169679365700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c20e47c5-4bf3-4af9-9c71-160f6e2bea28", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169679391700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fcfc652-571a-412e-aae8-1f20d4b0f62d", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169679416200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "237d74df-c9e0-43c5-9b8b-1c584bfe0345", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169681979900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a366bf3f-b826-4dda-8fc3-89561966e56d", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169682653000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "362181a6-4913-428e-8a8e-57114f899cef", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169683122700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48f4e79d-5118-4513-8519-4564bebb5689", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169683353200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ce732fc-fa98-47ce-bda9-aebf0481571e", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169683672600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ff51826-f3e4-47dd-a831-85a64d198b75", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169684954700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14f2ae94-f2f2-464e-9a2a-4f278f241043", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169685065100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd588eb7-6387-4ed0-8f89-2f6a2b13aee0", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169685350400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1598dc5-1195-4b1c-a928-367aeea35a4f", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169685870500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b73da35-3ae0-4c52-9faf-7d2e5fdca6d6", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169686686100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "750a4186-2801-4389-be5b-6f74837a0b76", "name": "Incremental task entry:default@CompileArkTS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169687288800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11e0558d-043a-4389-a276-8e720e3593ef", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169688924800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c9aa3c8-f18f-4f9e-a68d-b6f1b607f7e2", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169689469700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8d6a2d3-7adc-4ab8-9cb5-e17c9eeb0bdf", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169689773000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73899c2d-07b7-4f36-9034-f1e4fbe2a57c", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169689975100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f968bc9e-d4b3-47e0-96e2-ee9e175011da", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169690146700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad0cb07f-c109-44ae-a8a7-a88d4cac1984", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169690726700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9cebe41-c485-4245-9f0b-24e6d14bd849", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169691374400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f30ef50b-90a1-4663-9e9c-99dfe5dace77", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169691600000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7dae898-b702-4763-819b-48011693c296", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169691674300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a15cfbdb-2f1e-4321-b0a4-51a09a1cc6f2", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169691707000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e018d1a-f052-4468-a9ed-b65ac9d54f1e", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169692806400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6a8cabf-c458-4589-9bc8-dcfff75c7444", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169693188000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "221d939c-6529-404c-8722-72d5a79723fe", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169693389000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfe24d25-c910-48fa-aa95-e237aa7b7e9b", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169698982300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f5922d1-c21d-47a2-b4b2-1c856a4122ec", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169699200200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de7ec88d-c39c-43e2-bb55-70108f67b20c", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169699387200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98eef3ed-2180-41b9-b2f1-a95723d7a566", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169699579100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9f22691-fc98-4187-9d09-8a0b813bfb59", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169699632800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f925459-700d-4253-b69f-71fa8450ca78", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169699983000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7859d6e9-bbda-4ad5-9a59-0fa7fdfdcc41", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169700234300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "035f2511-a73c-4ae2-a213-e8c17637c8e5", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169701031300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f8c4dd6-29b0-4655-9ae3-0570cd287d89", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169701271300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6837cc4a-2558-4c1a-8b90-e39c843055b0", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169701449900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "070e5830-3fca-4358-93dc-0e8348e4fabe", "name": "Incremental task entry:default@PackageHap post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169701675800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de1c428e-e7b2-4cff-a3e6-a8d70801bed7", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169701870400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a5b8d94-1382-4fd7-b9fa-df65879b278e", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169702050300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2723649-5b41-4d92-8739-c770530bd8da", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169702212400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f77a975-e8f6-4ac0-9b0f-2aa6001eeab1", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169702377400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1680feb-8b20-4197-ba98-0afc1ae65ef2", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169702418400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "431dc219-18b3-434c-807d-be87f0ede431", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169702594300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b36b2f25-1cc6-485d-a271-4bdad92f14fc", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169704747400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a11887f9-1832-4078-a8f5-cc7ab8b1a37f", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169704984900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77d77563-6e4a-4b7f-a90d-ad5a6135bb1d", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169705398300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2daacf0-18f6-49e0-95ce-fc1983eab274", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169705608200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}