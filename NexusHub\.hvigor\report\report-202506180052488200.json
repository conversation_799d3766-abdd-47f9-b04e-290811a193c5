{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "63aa6458-ac39-416b-af01-f299e903dc5c", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122798636600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1afaa246-5aec-4e2e-958c-107da3fa75c1", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122798933100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8de06d0f-ae92-4d54-841b-e602188f2316", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122799281800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d4cdffd-64d2-4bfb-b0a7-042838d18718", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122801377900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d39acb75-ca17-4626-b975-a1d6b5598888", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122801720900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9ccec5f-80be-43bd-90cb-093a19a36d63", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122809873100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0e51fdf-f1d8-47e1-82f6-2b03263407f0", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122810591300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d69e338b-3a2a-47c9-8a54-76564233daaa", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122818609800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b892507-ed57-481f-887d-640fb95a2a3d", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122865014300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c62f82bb-273a-46fb-9819-f5988e436aaf", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539035900400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b56d093-c3e5-4f55-a348-668a9a08418d", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539042900100, "endTime": 154539240567900}, "additional": {"children": ["8f6126c8-cf58-462d-b96a-ee0cf027b53b", "ae03e05c-cbba-4ac7-a023-d492c7629faf", "7cd38c80-11ad-4b46-85af-44bf1deba170", "f49794fd-35ab-479a-a303-dbdac38e5e3f", "259d12e4-a1b9-4420-9ec8-d87f0a40b2d6", "5fd8bd54-1eb0-4188-8459-2c9b491557ec", "6b7e05ee-c100-4e52-a585-aac29ed085ae"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "f0c4a241-619b-4301-a820-af3ba0ad59af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f6126c8-cf58-462d-b96a-ee0cf027b53b", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539042901100, "endTime": 154539059932700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1b56d093-c3e5-4f55-a348-668a9a08418d", "logId": "516bc249-71b6-4301-9661-7809c848148d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae03e05c-cbba-4ac7-a023-d492c7629faf", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539059945200, "endTime": 154539239083600}, "additional": {"children": ["1f87f993-1dae-4fba-9fc5-8ef5d60c9cd2", "fb960dcc-5737-4cb3-9210-f1ad7a2181c6", "46dd44df-d4c1-405a-a199-b1d60355a483", "1e1031c8-32d4-4441-a000-c5e738dbd187", "be997360-6075-41c1-8a2f-68faba30c4e3", "cf5e8ff0-55e1-4ab2-ad5c-92a1b8e11eba", "50090a00-b428-4bdc-ba83-889f24c3ac6c", "d8ac65fe-26de-4bb5-8a7c-8ad50c0e4120", "81f3740d-f7b0-4af4-addb-55685da644ed"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1b56d093-c3e5-4f55-a348-668a9a08418d", "logId": "9c381ca0-ea76-4733-88b8-f4b9ff9166b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cd38c80-11ad-4b46-85af-44bf1deba170", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539239118300, "endTime": 154539240560100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1b56d093-c3e5-4f55-a348-668a9a08418d", "logId": "26975e6c-43f0-4f22-8738-100dc59aa517"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f49794fd-35ab-479a-a303-dbdac38e5e3f", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539240563700, "endTime": 154539240565300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1b56d093-c3e5-4f55-a348-668a9a08418d", "logId": "f8108170-7c3b-4c0d-bbb5-44ce4fa00362"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "259d12e4-a1b9-4420-9ec8-d87f0a40b2d6", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539045848200, "endTime": 154539046037200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1b56d093-c3e5-4f55-a348-668a9a08418d", "logId": "ff2b6765-e6a5-4a7c-bbf4-6eee89a95bc5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff2b6765-e6a5-4a7c-bbf4-6eee89a95bc5", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539045848200, "endTime": 154539046037200}, "additional": {"logType": "info", "children": [], "durationId": "259d12e4-a1b9-4420-9ec8-d87f0a40b2d6", "parent": "f0c4a241-619b-4301-a820-af3ba0ad59af"}}, {"head": {"id": "5fd8bd54-1eb0-4188-8459-2c9b491557ec", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539052067200, "endTime": 154539052101400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1b56d093-c3e5-4f55-a348-668a9a08418d", "logId": "6fd3db6b-52c7-4c14-b8e6-7b0a2be35c81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6fd3db6b-52c7-4c14-b8e6-7b0a2be35c81", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539052067200, "endTime": 154539052101400}, "additional": {"logType": "info", "children": [], "durationId": "5fd8bd54-1eb0-4188-8459-2c9b491557ec", "parent": "f0c4a241-619b-4301-a820-af3ba0ad59af"}}, {"head": {"id": "d23fa405-147f-474f-9fa8-6ceab5e5394c", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539052163500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a063cb7c-e6f7-43d2-978d-3023fdc4126e", "name": "Cache service initialization finished in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539059821600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "516bc249-71b6-4301-9661-7809c848148d", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539042901100, "endTime": 154539059932700}, "additional": {"logType": "info", "children": [], "durationId": "8f6126c8-cf58-462d-b96a-ee0cf027b53b", "parent": "f0c4a241-619b-4301-a820-af3ba0ad59af"}}, {"head": {"id": "1f87f993-1dae-4fba-9fc5-8ef5d60c9cd2", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539064825300, "endTime": 154539064831900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ae03e05c-cbba-4ac7-a023-d492c7629faf", "logId": "aa046a53-ec9d-434c-ac0a-4967d0278696"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb960dcc-5737-4cb3-9210-f1ad7a2181c6", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539064898800, "endTime": 154539068973700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ae03e05c-cbba-4ac7-a023-d492c7629faf", "logId": "f108503b-362f-47a3-ace6-5e96b5c91ec3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46dd44df-d4c1-405a-a199-b1d60355a483", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539068989700, "endTime": 154539140719500}, "additional": {"children": ["5810c387-cfd4-4fd3-9271-549b189be954", "e5dce762-9775-497f-a125-5fbf976ec045", "69c2193a-22c7-4c1e-a47d-fbd90e65119b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ae03e05c-cbba-4ac7-a023-d492c7629faf", "logId": "8c9f8894-80cd-49cf-8425-df528e497fab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e1031c8-32d4-4441-a000-c5e738dbd187", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539140734100, "endTime": 154539160110700}, "additional": {"children": ["a563bc6b-bf63-489c-b1c4-1b79d37b536a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ae03e05c-cbba-4ac7-a023-d492c7629faf", "logId": "bb354833-4085-4ad7-add0-afa2bb9ce1e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be997360-6075-41c1-8a2f-68faba30c4e3", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539160116700, "endTime": 154539205447700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ae03e05c-cbba-4ac7-a023-d492c7629faf", "logId": "53f45852-2d04-410d-8070-f6310c6aadf6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf5e8ff0-55e1-4ab2-ad5c-92a1b8e11eba", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539206458100, "endTime": 154539223333000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ae03e05c-cbba-4ac7-a023-d492c7629faf", "logId": "e3ca38b9-321a-4316-b616-ebd2dc02a027"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50090a00-b428-4bdc-ba83-889f24c3ac6c", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539223357700, "endTime": 154539238893500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ae03e05c-cbba-4ac7-a023-d492c7629faf", "logId": "590ab5ae-fc9b-4d57-842f-ae409629ed5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8ac65fe-26de-4bb5-8a7c-8ad50c0e4120", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539238915000, "endTime": 154539239070700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ae03e05c-cbba-4ac7-a023-d492c7629faf", "logId": "e6189ed2-54bd-4ffa-a004-5cd9d730da14"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa046a53-ec9d-434c-ac0a-4967d0278696", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539064825300, "endTime": 154539064831900}, "additional": {"logType": "info", "children": [], "durationId": "1f87f993-1dae-4fba-9fc5-8ef5d60c9cd2", "parent": "9c381ca0-ea76-4733-88b8-f4b9ff9166b1"}}, {"head": {"id": "f108503b-362f-47a3-ace6-5e96b5c91ec3", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539064898800, "endTime": 154539068973700}, "additional": {"logType": "info", "children": [], "durationId": "fb960dcc-5737-4cb3-9210-f1ad7a2181c6", "parent": "9c381ca0-ea76-4733-88b8-f4b9ff9166b1"}}, {"head": {"id": "5810c387-cfd4-4fd3-9271-549b189be954", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539069670000, "endTime": 154539069694400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46dd44df-d4c1-405a-a199-b1d60355a483", "logId": "d2528eb3-35d4-4528-955d-ab0c4f69ea48"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2528eb3-35d4-4528-955d-ab0c4f69ea48", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539069670000, "endTime": 154539069694400}, "additional": {"logType": "info", "children": [], "durationId": "5810c387-cfd4-4fd3-9271-549b189be954", "parent": "8c9f8894-80cd-49cf-8425-df528e497fab"}}, {"head": {"id": "e5dce762-9775-497f-a125-5fbf976ec045", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539072513100, "endTime": 154539140063900}, "additional": {"children": ["6cc755aa-2e96-4253-9f6b-8d92f65c494b", "c93e77e9-030c-4f4a-a1a6-e15520107d51"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46dd44df-d4c1-405a-a199-b1d60355a483", "logId": "373fd4ee-5d31-4a56-9723-4a0fb0917129"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6cc755aa-2e96-4253-9f6b-8d92f65c494b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539072514400, "endTime": 154539077302600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e5dce762-9775-497f-a125-5fbf976ec045", "logId": "2569180d-e191-48e7-b0ba-785319ccbf55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c93e77e9-030c-4f4a-a1a6-e15520107d51", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539077317800, "endTime": 154539140053500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e5dce762-9775-497f-a125-5fbf976ec045", "logId": "40996f2c-1009-46b2-b860-3c129c3ac2b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "328d3f2f-a7ed-4f39-8114-bbe7a43c4b4d", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539072517800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "250d891c-19f6-417b-b574-fa12c1a3e54e", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539077174800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2569180d-e191-48e7-b0ba-785319ccbf55", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539072514400, "endTime": 154539077302600}, "additional": {"logType": "info", "children": [], "durationId": "6cc755aa-2e96-4253-9f6b-8d92f65c494b", "parent": "373fd4ee-5d31-4a56-9723-4a0fb0917129"}}, {"head": {"id": "05ba1a09-3b82-4661-aa04-85061c525f44", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539077334800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "202b7df1-d77e-4ffa-b4b8-24b812e66a94", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539085469600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bafc5a6-8f7a-449f-9a4c-a69ede2cedac", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539085604700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d9f78c9-6819-4f06-9e6c-7fff567fd5cd", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539085715900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b374b494-7801-49ed-a1c9-687b5435fbc9", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539085781400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36c5060d-4c49-49ec-a39b-d083829a89e2", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539087369800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c0be4e5-22d1-4965-b210-a2cf958e7788", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539099695100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8160eef9-f167-40da-9350-edc3e9496e1d", "name": "Sdk init in 28 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539120669800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e16dadcd-384b-48dc-b94b-a3578ce4f9b9", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539120834300}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 52, "second": 48}, "markType": "other"}}, {"head": {"id": "38793975-c168-47cc-82a4-b9a2f7300e9a", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539120848500}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 52, "second": 48}, "markType": "other"}}, {"head": {"id": "6e2ff468-8224-4554-a467-31915ea42bcb", "name": "Project task initialization takes 18 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539139789600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0810b6a4-2c12-4939-83ec-5be3a917f432", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539139920400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f600013f-47fb-4d7f-b4b5-c6782255f906", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539139971300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34628659-cebd-4361-8794-62639fd8f00f", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539140014800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40996f2c-1009-46b2-b860-3c129c3ac2b1", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539077317800, "endTime": 154539140053500}, "additional": {"logType": "info", "children": [], "durationId": "c93e77e9-030c-4f4a-a1a6-e15520107d51", "parent": "373fd4ee-5d31-4a56-9723-4a0fb0917129"}}, {"head": {"id": "373fd4ee-5d31-4a56-9723-4a0fb0917129", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539072513100, "endTime": 154539140063900}, "additional": {"logType": "info", "children": ["2569180d-e191-48e7-b0ba-785319ccbf55", "40996f2c-1009-46b2-b860-3c129c3ac2b1"], "durationId": "e5dce762-9775-497f-a125-5fbf976ec045", "parent": "8c9f8894-80cd-49cf-8425-df528e497fab"}}, {"head": {"id": "69c2193a-22c7-4c1e-a47d-fbd90e65119b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539140687600, "endTime": 154539140705200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "46dd44df-d4c1-405a-a199-b1d60355a483", "logId": "6ff9c991-279c-41ba-a783-0d591892d8a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ff9c991-279c-41ba-a783-0d591892d8a1", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539140687600, "endTime": 154539140705200}, "additional": {"logType": "info", "children": [], "durationId": "69c2193a-22c7-4c1e-a47d-fbd90e65119b", "parent": "8c9f8894-80cd-49cf-8425-df528e497fab"}}, {"head": {"id": "8c9f8894-80cd-49cf-8425-df528e497fab", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539068989700, "endTime": 154539140719500}, "additional": {"logType": "info", "children": ["d2528eb3-35d4-4528-955d-ab0c4f69ea48", "373fd4ee-5d31-4a56-9723-4a0fb0917129", "6ff9c991-279c-41ba-a783-0d591892d8a1"], "durationId": "46dd44df-d4c1-405a-a199-b1d60355a483", "parent": "9c381ca0-ea76-4733-88b8-f4b9ff9166b1"}}, {"head": {"id": "a563bc6b-bf63-489c-b1c4-1b79d37b536a", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539141279300, "endTime": 154539160100000}, "additional": {"children": ["d0631fa5-2c07-48f6-96c0-255e80e680de", "cbe1f1bc-e7ff-4df3-b03a-ba2e9f6348df", "a0126812-c599-4930-a8af-9870f4e5acb4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1e1031c8-32d4-4441-a000-c5e738dbd187", "logId": "6863e9ce-66a1-4676-93e2-373b570d7ccf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0631fa5-2c07-48f6-96c0-255e80e680de", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539143782200, "endTime": 154539143795400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a563bc6b-bf63-489c-b1c4-1b79d37b536a", "logId": "e9e1366d-fff6-4176-ab37-092567ff40ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9e1366d-fff6-4176-ab37-092567ff40ff", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539143782200, "endTime": 154539143795400}, "additional": {"logType": "info", "children": [], "durationId": "d0631fa5-2c07-48f6-96c0-255e80e680de", "parent": "6863e9ce-66a1-4676-93e2-373b570d7ccf"}}, {"head": {"id": "cbe1f1bc-e7ff-4df3-b03a-ba2e9f6348df", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539145486000, "endTime": 154539158933100}, "additional": {"children": ["735083e0-6156-4701-84cf-55fb9860f704", "e37a0f10-64fa-4289-9635-a457a83eac32"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a563bc6b-bf63-489c-b1c4-1b79d37b536a", "logId": "5676478f-7f0a-45cf-935b-228064c383ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "735083e0-6156-4701-84cf-55fb9860f704", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539145486900, "endTime": 154539148810100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cbe1f1bc-e7ff-4df3-b03a-ba2e9f6348df", "logId": "ac382d93-08c7-46fd-afdc-bc608ba3e73e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e37a0f10-64fa-4289-9635-a457a83eac32", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539148819000, "endTime": 154539158921800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cbe1f1bc-e7ff-4df3-b03a-ba2e9f6348df", "logId": "bca1cda8-13f6-404b-b2a8-53c3e2258496"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4929b8a8-7e29-466c-9cc8-182707f11964", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539145489600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5026d648-9900-49d9-8781-efe215e2ed24", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539148716100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac382d93-08c7-46fd-afdc-bc608ba3e73e", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539145486900, "endTime": 154539148810100}, "additional": {"logType": "info", "children": [], "durationId": "735083e0-6156-4701-84cf-55fb9860f704", "parent": "5676478f-7f0a-45cf-935b-228064c383ad"}}, {"head": {"id": "006e5ae3-b5a2-444b-9d41-6896b3e373e5", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539148828600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "743669e4-9207-4af1-8a04-ba8b8bff8a95", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539154904900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "378416e0-108e-48ab-898e-3312085844fb", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539155028400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63fc7865-5767-427c-bbed-70a55c3d5fb4", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539155182600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e75bc78-1e19-4fbd-a860-efbbe0163d28", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539155264000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b61faf9-c1ab-46bc-b307-8e9ebd236d35", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539155307700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3dfad80-ab71-49bf-b4cb-19c7cf105bd0", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539155340900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1be72d6-7c99-4f6b-ac55-3981354f189c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539155379700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "434e5df6-dad7-4028-93e6-20ee9e78e232", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539155412600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "059b1027-431f-4471-bcbf-daae4820dfd2", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539155561000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13f0f299-a3e7-466e-839e-85f511a518b3", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539155636200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9db7d91-df59-49c4-a225-c904cf7f8fe3", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539155675700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a525860-0483-45b2-a300-130b6b1c5607", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539155703400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "689ff4f1-81ef-4dd2-a8b9-1669d65bbc0a", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539155882300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f404f46-d412-42bd-9a5a-59971b66fde0", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539155927000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb490c31-bc0a-4e04-9d0d-4b00d4bb987d", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539156012800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b081c56-d4b5-4387-87b6-fbd3bb6e85f2", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539156078400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc2c58d6-237c-4f9a-a911-bcbc71ae5caa", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539156109700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0911bccf-b83e-45a9-87d6-b95b3a280746", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539156136800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c97ec1eb-074e-4708-8be3-37323df8bf6c", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539156173800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e211806d-9cbb-4a71-aeec-91c8335adcac", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539158666400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88f1fd85-f42b-4dfd-9c96-43a944152161", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539158811100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd2c0db2-2449-4893-9aab-a4bd9aaa9bef", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539158858500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f23da72-96b6-4881-a6e5-5a9f24cd3367", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539158889800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bca1cda8-13f6-404b-b2a8-53c3e2258496", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539148819000, "endTime": 154539158921800}, "additional": {"logType": "info", "children": [], "durationId": "e37a0f10-64fa-4289-9635-a457a83eac32", "parent": "5676478f-7f0a-45cf-935b-228064c383ad"}}, {"head": {"id": "5676478f-7f0a-45cf-935b-228064c383ad", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539145486000, "endTime": 154539158933100}, "additional": {"logType": "info", "children": ["ac382d93-08c7-46fd-afdc-bc608ba3e73e", "bca1cda8-13f6-404b-b2a8-53c3e2258496"], "durationId": "cbe1f1bc-e7ff-4df3-b03a-ba2e9f6348df", "parent": "6863e9ce-66a1-4676-93e2-373b570d7ccf"}}, {"head": {"id": "a0126812-c599-4930-a8af-9870f4e5acb4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539160071400, "endTime": 154539160084500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a563bc6b-bf63-489c-b1c4-1b79d37b536a", "logId": "5259c04d-ac93-4064-bd0f-f9457d8243d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5259c04d-ac93-4064-bd0f-f9457d8243d3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539160071400, "endTime": 154539160084500}, "additional": {"logType": "info", "children": [], "durationId": "a0126812-c599-4930-a8af-9870f4e5acb4", "parent": "6863e9ce-66a1-4676-93e2-373b570d7ccf"}}, {"head": {"id": "6863e9ce-66a1-4676-93e2-373b570d7ccf", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539141279300, "endTime": 154539160100000}, "additional": {"logType": "info", "children": ["e9e1366d-fff6-4176-ab37-092567ff40ff", "5676478f-7f0a-45cf-935b-228064c383ad", "5259c04d-ac93-4064-bd0f-f9457d8243d3"], "durationId": "a563bc6b-bf63-489c-b1c4-1b79d37b536a", "parent": "bb354833-4085-4ad7-add0-afa2bb9ce1e0"}}, {"head": {"id": "bb354833-4085-4ad7-add0-afa2bb9ce1e0", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539140734100, "endTime": 154539160110700}, "additional": {"logType": "info", "children": ["6863e9ce-66a1-4676-93e2-373b570d7ccf"], "durationId": "1e1031c8-32d4-4441-a000-c5e738dbd187", "parent": "9c381ca0-ea76-4733-88b8-f4b9ff9166b1"}}, {"head": {"id": "aa7d2efa-714e-4bf5-8e11-7b5ac3b8fc28", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539173820300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a94e3fad-ac46-494f-8604-403658d87bc4", "name": "hvigorfile, resolve hvigorfile dependencies in 46 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539205306100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53f45852-2d04-410d-8070-f6310c6aadf6", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539160116700, "endTime": 154539205447700}, "additional": {"logType": "info", "children": [], "durationId": "be997360-6075-41c1-8a2f-68faba30c4e3", "parent": "9c381ca0-ea76-4733-88b8-f4b9ff9166b1"}}, {"head": {"id": "81f3740d-f7b0-4af4-addb-55685da644ed", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539206240900, "endTime": 154539206447700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ae03e05c-cbba-4ac7-a023-d492c7629faf", "logId": "91166e46-b8dc-4f1f-ab85-ac9edd53ebcf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d1d8a8f-fc4c-4a59-beb3-f712cfcafbc2", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539206272900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91166e46-b8dc-4f1f-ab85-ac9edd53ebcf", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539206240900, "endTime": 154539206447700}, "additional": {"logType": "info", "children": [], "durationId": "81f3740d-f7b0-4af4-addb-55685da644ed", "parent": "9c381ca0-ea76-4733-88b8-f4b9ff9166b1"}}, {"head": {"id": "14c2f210-1960-438f-ba66-af142c6efc5d", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539207956600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e80a999b-d60b-4186-b856-895fed6ef137", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539221892300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3ca38b9-321a-4316-b616-ebd2dc02a027", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539206458100, "endTime": 154539223333000}, "additional": {"logType": "info", "children": [], "durationId": "cf5e8ff0-55e1-4ab2-ad5c-92a1b8e11eba", "parent": "9c381ca0-ea76-4733-88b8-f4b9ff9166b1"}}, {"head": {"id": "71ec1785-a08f-4444-adf8-7276611d60b7", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539223378300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c37b17e2-e833-4248-bd44-a9dcec37520e", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539230409900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3d8b19a-76c6-4c0e-acec-fb4b2b93cd7b", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539230528100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d722e920-511b-425c-af71-b5a6d09ca414", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539230712900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39d68393-3587-4735-8f5b-aa9069a75f88", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539233939900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71875670-f337-4076-b93e-9d971bdbb5e3", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539234133000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "590ab5ae-fc9b-4d57-842f-ae409629ed5c", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539223357700, "endTime": 154539238893500}, "additional": {"logType": "info", "children": [], "durationId": "50090a00-b428-4bdc-ba83-889f24c3ac6c", "parent": "9c381ca0-ea76-4733-88b8-f4b9ff9166b1"}}, {"head": {"id": "204fe8af-39ba-45ab-8721-35616e80bf3e", "name": "Configuration phase cost:175 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539238941900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6189ed2-54bd-4ffa-a004-5cd9d730da14", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539238915000, "endTime": 154539239070700}, "additional": {"logType": "info", "children": [], "durationId": "d8ac65fe-26de-4bb5-8a7c-8ad50c0e4120", "parent": "9c381ca0-ea76-4733-88b8-f4b9ff9166b1"}}, {"head": {"id": "9c381ca0-ea76-4733-88b8-f4b9ff9166b1", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539059945200, "endTime": 154539239083600}, "additional": {"logType": "info", "children": ["aa046a53-ec9d-434c-ac0a-4967d0278696", "f108503b-362f-47a3-ace6-5e96b5c91ec3", "8c9f8894-80cd-49cf-8425-df528e497fab", "bb354833-4085-4ad7-add0-afa2bb9ce1e0", "53f45852-2d04-410d-8070-f6310c6aadf6", "e3ca38b9-321a-4316-b616-ebd2dc02a027", "590ab5ae-fc9b-4d57-842f-ae409629ed5c", "e6189ed2-54bd-4ffa-a004-5cd9d730da14", "91166e46-b8dc-4f1f-ab85-ac9edd53ebcf"], "durationId": "ae03e05c-cbba-4ac7-a023-d492c7629faf", "parent": "f0c4a241-619b-4301-a820-af3ba0ad59af"}}, {"head": {"id": "6b7e05ee-c100-4e52-a585-aac29ed085ae", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539240527800, "endTime": 154539240551100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1b56d093-c3e5-4f55-a348-668a9a08418d", "logId": "b877c789-309d-4641-91ea-bc9360de27b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b877c789-309d-4641-91ea-bc9360de27b4", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539240527800, "endTime": 154539240551100}, "additional": {"logType": "info", "children": [], "durationId": "6b7e05ee-c100-4e52-a585-aac29ed085ae", "parent": "f0c4a241-619b-4301-a820-af3ba0ad59af"}}, {"head": {"id": "26975e6c-43f0-4f22-8738-100dc59aa517", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539239118300, "endTime": 154539240560100}, "additional": {"logType": "info", "children": [], "durationId": "7cd38c80-11ad-4b46-85af-44bf1deba170", "parent": "f0c4a241-619b-4301-a820-af3ba0ad59af"}}, {"head": {"id": "f8108170-7c3b-4c0d-bbb5-44ce4fa00362", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539240563700, "endTime": 154539240565300}, "additional": {"logType": "info", "children": [], "durationId": "f49794fd-35ab-479a-a303-dbdac38e5e3f", "parent": "f0c4a241-619b-4301-a820-af3ba0ad59af"}}, {"head": {"id": "f0c4a241-619b-4301-a820-af3ba0ad59af", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539042900100, "endTime": 154539240567900}, "additional": {"logType": "info", "children": ["516bc249-71b6-4301-9661-7809c848148d", "9c381ca0-ea76-4733-88b8-f4b9ff9166b1", "26975e6c-43f0-4f22-8738-100dc59aa517", "f8108170-7c3b-4c0d-bbb5-44ce4fa00362", "ff2b6765-e6a5-4a7c-bbf4-6eee89a95bc5", "6fd3db6b-52c7-4c14-b8e6-7b0a2be35c81", "b877c789-309d-4641-91ea-bc9360de27b4"], "durationId": "1b56d093-c3e5-4f55-a348-668a9a08418d"}}, {"head": {"id": "7c1c2a52-d887-46c3-bc78-e806e99591e2", "name": "Configuration task cost before running: 201 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539240781200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "980dc7ab-d18c-4478-98e2-935cb409590c", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539249567200, "endTime": 154539263953000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "af71ca05-195c-47cb-a32b-8950dffe25d5", "logId": "5d1f86a6-cd60-46c4-8a1e-309985661ddd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af71ca05-195c-47cb-a32b-8950dffe25d5", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539242311100}, "additional": {"logType": "detail", "children": [], "durationId": "980dc7ab-d18c-4478-98e2-935cb409590c"}}, {"head": {"id": "5cad9455-452c-4a00-8513-f4d4767f9e93", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539243005600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac53bc7f-85d1-4c05-9cfb-56092e094643", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539243114000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0b6513-05d5-4786-a05d-477759ac974f", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539243833200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22c78ace-469c-43e5-9be3-45ac4558ce58", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539244661500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39e02721-d105-4400-9771-2c50c99cbf59", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539245820800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a277b68f-e245-424e-86a5-e85307f1a64f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539245909200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2a51481-d15b-44e8-b0ae-97446ef2cbb4", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539249583200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "377e1b8e-c75b-4a91-84dc-7d3259ea751f", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539263609700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d303c91-7a72-43f8-8ace-3ff194ff9747", "name": "entry : default@PreBuild cost memory 0.316497802734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539263840200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d1f86a6-cd60-46c4-8a1e-309985661ddd", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539249567200, "endTime": 154539263953000}, "additional": {"logType": "info", "children": [], "durationId": "980dc7ab-d18c-4478-98e2-935cb409590c"}}, {"head": {"id": "7b3af744-682e-4f65-be09-52af3a94b695", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539272069700, "endTime": 154539273790500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "165d5d04-a9d5-4c40-a9ba-2ef36b061e42", "logId": "cff07f30-78bd-4d07-a8ce-26c5877024dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "165d5d04-a9d5-4c40-a9ba-2ef36b061e42", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539269815100}, "additional": {"logType": "detail", "children": [], "durationId": "7b3af744-682e-4f65-be09-52af3a94b695"}}, {"head": {"id": "cc767390-9edc-4329-8971-86b25e64138c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539271331700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea23c156-44f1-410a-936d-13f44dff24b2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539271457100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "117776be-0245-4442-9cb1-428bd9d7bec2", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539272080400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79547c05-b7b0-4860-9afd-762192186fb3", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539272725300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d93fb5a3-653f-4678-8c82-a2dd826edb43", "name": "entry : default@CreateModuleInfo cost memory 0.05979156494140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539273605200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be40c050-1a74-4846-ba23-4f0d25ac45d4", "name": "runTaskFromQueue task cost before running: 234 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539273731900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cff07f30-78bd-4d07-a8ce-26c5877024dd", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539272069700, "endTime": 154539273790500, "totalTime": 1641600}, "additional": {"logType": "info", "children": [], "durationId": "7b3af744-682e-4f65-be09-52af3a94b695"}}, {"head": {"id": "fc666e4f-ad52-416c-b80d-9ef32a8fb4a3", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539281823300, "endTime": 154539284801500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "66337b03-f1fc-4160-aea5-94d271142aee", "logId": "02a30df5-bb31-4adf-89b8-6e6dcde92824"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66337b03-f1fc-4160-aea5-94d271142aee", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539276043700}, "additional": {"logType": "detail", "children": [], "durationId": "fc666e4f-ad52-416c-b80d-9ef32a8fb4a3"}}, {"head": {"id": "3c8ffa18-aac6-478e-9297-9c6bd8159fa5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539277542800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79a57ad2-27da-41e0-9f50-d458b2592c5c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539277653700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e7bd883-9688-4fb9-bc85-327a5cec9238", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539281833900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebaf3e6d-ac1a-49f3-a8fe-214a2cdf87c2", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539282841200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b14acf48-f5dc-4285-8b4a-794380d68acc", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539284344400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9973bb91-9b50-44e3-ab18-2068bab2667c", "name": "entry : default@GenerateMetadata cost memory 0.10103607177734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539284700300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02a30df5-bb31-4adf-89b8-6e6dcde92824", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539281823300, "endTime": 154539284801500}, "additional": {"logType": "info", "children": [], "durationId": "fc666e4f-ad52-416c-b80d-9ef32a8fb4a3"}}, {"head": {"id": "7c562fdd-8f32-42bc-bb54-f9fa28a27e17", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539288525300, "endTime": 154539288824800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d098175c-ad7b-47b1-9d16-53cf8213ad14", "logId": "29963e05-fbdc-46d2-a80f-14a8c6410691"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d098175c-ad7b-47b1-9d16-53cf8213ad14", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539287051000}, "additional": {"logType": "detail", "children": [], "durationId": "7c562fdd-8f32-42bc-bb54-f9fa28a27e17"}}, {"head": {"id": "16c3994a-91bf-44cb-b9dd-62e4d55bd656", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539288280500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09fbb1da-8541-4983-af6f-08663d6227b1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539288393300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d4e25e3-6a9e-49fa-acc2-e88aa0996d85", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539288532800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68ee4a58-abbc-4e69-8fb5-b3b5fc439660", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539288619900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac940a34-1c08-44a2-90b0-a2217d009457", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539288663700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8a79a05-f8b1-4a8e-a9a9-8ff0ad1a9b1e", "name": "entry : default@ConfigureCmake cost memory 0.03722381591796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539288722200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c05a75c-95c9-4bd3-875b-b0130e29a93b", "name": "runTaskFromQueue task cost before running: 249 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539288786300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29963e05-fbdc-46d2-a80f-14a8c6410691", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539288525300, "endTime": 154539288824800, "totalTime": 246200}, "additional": {"logType": "info", "children": [], "durationId": "7c562fdd-8f32-42bc-bb54-f9fa28a27e17"}}, {"head": {"id": "dd6736ec-0d8a-410c-a92b-eb96b54ffca2", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539292705700, "endTime": 154539294760200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a9aee487-2584-4700-839c-1adeca744c1f", "logId": "1d75b9f0-1ae6-4214-bc6a-b0c7eab6106e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9aee487-2584-4700-839c-1adeca744c1f", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539290707200}, "additional": {"logType": "detail", "children": [], "durationId": "dd6736ec-0d8a-410c-a92b-eb96b54ffca2"}}, {"head": {"id": "48653045-cfd2-43f7-a583-69a89e685730", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539291926900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e422c2f-2e28-401b-a1c5-11d9f71a00c4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539292041200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee3cb08f-7166-499c-a617-75725827abc6", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539292717800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fe3a21b-598f-4bd0-babf-0bd2a62f719a", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539294591700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75bc37cf-d537-427f-bca8-1379425573b7", "name": "entry : default@MergeProfile cost memory 0.1180267333984375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539294700400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d75b9f0-1ae6-4214-bc6a-b0c7eab6106e", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539292705700, "endTime": 154539294760200}, "additional": {"logType": "info", "children": [], "durationId": "dd6736ec-0d8a-410c-a92b-eb96b54ffca2"}}, {"head": {"id": "8b866313-05dc-49d9-82cb-bea67749590f", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539298246900, "endTime": 154539301827900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "eb3fd264-3f25-47ed-858b-bac7815a9d9b", "logId": "ecfe72d7-d368-4df4-99bf-4cad0b5a09ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb3fd264-3f25-47ed-858b-bac7815a9d9b", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539296421300}, "additional": {"logType": "detail", "children": [], "durationId": "8b866313-05dc-49d9-82cb-bea67749590f"}}, {"head": {"id": "748794fd-3bca-409f-9f72-6c9dc9e291dc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539297418100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25d803fb-0702-40ea-a23c-bc5cca918837", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539297517300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a334738-b2e1-4f2c-b657-6a5799bfd130", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539298254700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a0609b5-84fb-4837-ad48-25070fe1d532", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539299164900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99e4f4a5-1335-458a-a453-2f00b7bfe8f8", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539301467900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc99d618-f17d-4549-884c-60e82347fd99", "name": "entry : default@CreateBuildProfile cost memory 0.10642242431640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539301701400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecfe72d7-d368-4df4-99bf-4cad0b5a09ea", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539298246900, "endTime": 154539301827900}, "additional": {"logType": "info", "children": [], "durationId": "8b866313-05dc-49d9-82cb-bea67749590f"}}, {"head": {"id": "5c24a779-fad5-4517-960a-3d62739866e3", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539306077200, "endTime": 154539306605500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "ad5ffdf0-6104-44ff-bef2-f7309103f79b", "logId": "1778bedd-1dfe-43fe-a4ec-cd426a4645b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad5ffdf0-6104-44ff-bef2-f7309103f79b", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539304052000}, "additional": {"logType": "detail", "children": [], "durationId": "5c24a779-fad5-4517-960a-3d62739866e3"}}, {"head": {"id": "e97312f1-13eb-47fe-a5bc-cd6ca50a80a0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539305135400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4facc9b5-ca9a-4208-a29d-bac33d7dc866", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539305273500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fb87740-77f2-4861-a3e9-486864b8544a", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539306087600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3670678-ff27-4f4a-bbcb-2ae3e0864a13", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539306222100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1899895d-5506-45be-9559-14e7bbe249fa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539306277400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "157df0dc-ea0a-42e2-ae91-20b4d66677a1", "name": "entry : default@PreCheckSyscap cost memory 0.04083251953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539306473200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "351a5067-37d8-465b-91ae-dad3efbc2255", "name": "runTaskFromQueue task cost before running: 267 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539306561700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1778bedd-1dfe-43fe-a4ec-cd426a4645b2", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539306077200, "endTime": 154539306605500, "totalTime": 468100}, "additional": {"logType": "info", "children": [], "durationId": "5c24a779-fad5-4517-960a-3d62739866e3"}}, {"head": {"id": "988115fb-9559-489d-9947-81c14561ae95", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539312377300, "endTime": 154539318111500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "db18c07b-7fbb-48b5-a926-eb2e2b6cc5f5", "logId": "796de779-6b42-4d5c-8923-b523425f930b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db18c07b-7fbb-48b5-a926-eb2e2b6cc5f5", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539308111900}, "additional": {"logType": "detail", "children": [], "durationId": "988115fb-9559-489d-9947-81c14561ae95"}}, {"head": {"id": "60c04b16-c073-479f-956e-c6b7bd21a74f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539310832800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30087bd2-7e69-400b-b9f9-cab7f9697514", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539310945000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45933879-4230-42cb-9e01-754c693a5090", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539312387600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6edd5593-1da5-440b-8168-152ce9e0be1c", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539317058900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ac28046-f355-49fc-b2f1-833ae85ce7a0", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539317909600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "296f22dd-358b-45ad-b359-03ba20796131", "name": "entry : default@GeneratePkgContextInfo cost memory 0.25269317626953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539318045500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "796de779-6b42-4d5c-8923-b523425f930b", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539312377300, "endTime": 154539318111500}, "additional": {"logType": "info", "children": [], "durationId": "988115fb-9559-489d-9947-81c14561ae95"}}, {"head": {"id": "04cc75f8-932f-48b9-8f05-3dc72374f6df", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539325581000, "endTime": 154539327599900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "3401124b-a1f1-434d-8d83-674ce7b1ae51", "logId": "849aa394-1cbc-4e94-9533-2be64c085c26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3401124b-a1f1-434d-8d83-674ce7b1ae51", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539319728400}, "additional": {"logType": "detail", "children": [], "durationId": "04cc75f8-932f-48b9-8f05-3dc72374f6df"}}, {"head": {"id": "18beb259-e64b-4f33-bd30-12d21193ae26", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539320696800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e7aaf1e-6029-4af9-94b9-4d05c3c79515", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539320798900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12bb89b4-4bd6-4a68-aeed-a753148e6cb7", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539325594700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7809e5e6-d903-42cd-8c78-0643fa6dbfa0", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539327210800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f48ba80-1661-488e-b8a5-9141d6f08db3", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539327334800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65315ddf-e346-4264-9217-476ccf589b8e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539327406000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c9ed825-e218-4bc8-882f-80929225af12", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539327445900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "689d2966-dfcf-4a35-94a1-e93c77d0dae3", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1197509765625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539327506100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab9f490b-c073-4443-98ca-e46ee4b7aaa0", "name": "runTaskFromQueue task cost before running: 288 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539327563100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "849aa394-1cbc-4e94-9533-2be64c085c26", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539325581000, "endTime": 154539327599900, "totalTime": 1973900}, "additional": {"logType": "info", "children": [], "durationId": "04cc75f8-932f-48b9-8f05-3dc72374f6df"}}, {"head": {"id": "2f05093c-8c5a-41f6-9d49-4bed855b3157", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539331589900, "endTime": 154539331914700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "915150cc-276e-4a62-8ebb-10b28a4fe935", "logId": "a2266a8e-d347-4776-af46-30f6a192cc7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "915150cc-276e-4a62-8ebb-10b28a4fe935", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539329500900}, "additional": {"logType": "detail", "children": [], "durationId": "2f05093c-8c5a-41f6-9d49-4bed855b3157"}}, {"head": {"id": "135cc317-ef15-4a1c-9bda-a56e3c4bf23e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539330702600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "602eb4ca-d451-4fa1-a84e-7bdc9d251c22", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539330800600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "665928ad-1a4a-4d57-a52c-76386566ab40", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539331598700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edc92a6f-b811-4ba3-bde1-6a1612f21b07", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539331721000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8c2c7ec-0eed-4f14-bc33-73d7b686b85b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539331766300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e035de8-d379-4934-86cf-f7cda5589e49", "name": "entry : default@BuildNativeWithCmake cost memory 0.03824615478515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539331824800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d6584f2-e8a9-4e28-8c82-e4bfb20fb36c", "name": "runTaskFromQueue task cost before running: 292 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539331881500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2266a8e-d347-4776-af46-30f6a192cc7c", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539331589900, "endTime": 154539331914700, "totalTime": 278300}, "additional": {"logType": "info", "children": [], "durationId": "2f05093c-8c5a-41f6-9d49-4bed855b3157"}}, {"head": {"id": "db5d8424-8e48-4dab-b4a4-81810a4681fe", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539335951800, "endTime": 154539339963800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "eb4daefe-e0c6-44bb-b590-38b986fc0566", "logId": "a2c93c99-f71c-4e09-99fc-a1fbb56e510c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb4daefe-e0c6-44bb-b590-38b986fc0566", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539333428700}, "additional": {"logType": "detail", "children": [], "durationId": "db5d8424-8e48-4dab-b4a4-81810a4681fe"}}, {"head": {"id": "f5fb72f1-33b6-4603-9228-6c10bb9066d0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539334875200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10e1a133-284e-4a75-aabf-223b2e045099", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539335027400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5132db93-f0fe-4c59-be54-5ee5f416a0a3", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539335962100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b17b10b-f971-4cbe-8b3c-a31e138832bf", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539339766200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ca82a95-cd11-440d-983d-67a3df8decb7", "name": "entry : default@MakePackInfo cost memory 0.16298675537109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539339899500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2c93c99-f71c-4e09-99fc-a1fbb56e510c", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539335951800, "endTime": 154539339963800}, "additional": {"logType": "info", "children": [], "durationId": "db5d8424-8e48-4dab-b4a4-81810a4681fe"}}, {"head": {"id": "8fccb043-bc20-4db9-9ee3-e2a99882ab35", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539344337900, "endTime": 154539348126300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1053f3b7-4a5e-4312-a1e5-87180b1051b7", "logId": "1e35b501-b01c-479d-bf2d-03e3c2aadb2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1053f3b7-4a5e-4312-a1e5-87180b1051b7", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539342068000}, "additional": {"logType": "detail", "children": [], "durationId": "8fccb043-bc20-4db9-9ee3-e2a99882ab35"}}, {"head": {"id": "287967b5-9cc2-4e5c-9532-46510deca802", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539343062600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e68e6043-3b0e-4a58-9f3f-7cbc9f3c9c5e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539343165600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7586b3ea-cd49-4b14-a0fb-114c534f7a00", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539344346700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d4a949-2ba4-4606-98a7-cd2d5a023b56", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539344544500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02b0c9f8-942a-4676-8d26-40e2743e1a2b", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539345304100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f620a50-6ccb-4f79-a853-2302ff533f73", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539347910400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b23893d7-5168-4ae1-ab81-dcc08f5ac82e", "name": "entry : default@SyscapTransform cost memory 0.14949798583984375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539348055200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e35b501-b01c-479d-bf2d-03e3c2aadb2f", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539344337900, "endTime": 154539348126300}, "additional": {"logType": "info", "children": [], "durationId": "8fccb043-bc20-4db9-9ee3-e2a99882ab35"}}, {"head": {"id": "ad8731b5-b90e-4833-8a18-91076ce3a292", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539353253800, "endTime": 154539355993300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d5f709f4-c234-4882-a690-e620ac03ef65", "logId": "6e9617cd-6e05-45b4-8711-b92e7cc8bb17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5f709f4-c234-4882-a690-e620ac03ef65", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539349742800}, "additional": {"logType": "detail", "children": [], "durationId": "ad8731b5-b90e-4833-8a18-91076ce3a292"}}, {"head": {"id": "c33dac38-54da-4777-bc74-37b1733ce3aa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539350902300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdc0eaef-b7c4-4ff8-af55-fb0d2afae539", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539351088400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7be63658-3fd1-4655-9ba3-3c71ac7a6a0b", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539353269200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8895f732-bb62-4338-bdfb-6cff49b62f7a", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539355746000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9134c5af-70de-4539-970f-52afabb6684b", "name": "entry : default@ProcessProfile cost memory 0.124542236328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539355912800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e9617cd-6e05-45b4-8711-b92e7cc8bb17", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539353253800, "endTime": 154539355993300}, "additional": {"logType": "info", "children": [], "durationId": "ad8731b5-b90e-4833-8a18-91076ce3a292"}}, {"head": {"id": "08d07bfb-a812-422b-95a3-2adfad155591", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539361344200, "endTime": 154539371104900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5becb1d3-c03e-4384-8de7-f45ab520559a", "logId": "7a0f40e5-40e2-41fe-9004-28757c96b6ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5becb1d3-c03e-4384-8de7-f45ab520559a", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539358207800}, "additional": {"logType": "detail", "children": [], "durationId": "08d07bfb-a812-422b-95a3-2adfad155591"}}, {"head": {"id": "a8bb442d-58aa-4521-8432-a352c3694029", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539359357800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf98b598-8b65-4e12-92f2-57fc2d6ad332", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539359482400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44def2b7-35c2-49b9-8ad4-1d2a28183869", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539361359000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9763f4bc-9dd0-4e4b-ac0d-8b8b733872b8", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539370847800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a210e19-af49-44eb-a46a-0bbba08c36c0", "name": "entry : default@ProcessRouterMap cost memory 0.2321624755859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539371007100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a0f40e5-40e2-41fe-9004-28757c96b6ca", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539361344200, "endTime": 154539371104900}, "additional": {"logType": "info", "children": [], "durationId": "08d07bfb-a812-422b-95a3-2adfad155591"}}, {"head": {"id": "d8788b94-d559-44db-b012-d498c24e759a", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539376087000, "endTime": 154539382791200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "fbbba9c3-eb17-4f09-9b54-a8478e8c2d48", "logId": "ac06db83-48a2-4ea5-977d-dffd010b65c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fbbba9c3-eb17-4f09-9b54-a8478e8c2d48", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539374656100}, "additional": {"logType": "detail", "children": [], "durationId": "d8788b94-d559-44db-b012-d498c24e759a"}}, {"head": {"id": "bb7455f2-eb86-4d0c-a968-0e4f607d378b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539375841100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30dcb037-4553-409b-9c4a-3649800023b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539375989000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "241c17f1-0849-43d6-af13-40e8261c7015", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539376097700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c07d8a91-e290-414d-bcd0-28009e0ccf3a", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539376252300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2d30d52-ea7d-4119-8338-72b1074240ce", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539380788600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06cf047c-054b-4be8-b160-0bf61c4bc8e1", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539380949100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51cc4c54-4184-4d84-b255-73ea58bd6c76", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539381040000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7cf92f3-ee64-4e46-956e-fe3d7238de58", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539381082300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d00b3272-7eb0-45a5-8a88-8a2c7bd6b65a", "name": "entry : default@ProcessStartupConfig cost memory 0.27152252197265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539382500600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91210e66-529e-406e-919b-632dbb7e8ef1", "name": "runTaskFromQueue task cost before running: 343 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539382694500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac06db83-48a2-4ea5-977d-dffd010b65c0", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539376087000, "endTime": 154539382791200, "totalTime": 6573100}, "additional": {"logType": "info", "children": [], "durationId": "d8788b94-d559-44db-b012-d498c24e759a"}}, {"head": {"id": "849fc152-b850-4ece-81ec-ba1aeb76433d", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539389180100, "endTime": 154539390484600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "cc488e43-9b21-48d5-8fce-825bc22ce9a7", "logId": "627e3173-6970-4dd4-a26b-3abf910df825"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc488e43-9b21-48d5-8fce-825bc22ce9a7", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539386999100}, "additional": {"logType": "detail", "children": [], "durationId": "849fc152-b850-4ece-81ec-ba1aeb76433d"}}, {"head": {"id": "a4557048-e995-40ec-a27c-92f043945002", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539388099300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8682eaf4-6850-4924-9593-d058e146e4e4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539388235900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2045af91-d7b1-4e7b-8b04-57d8d840c2cb", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539389190400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebd9ae6d-94ff-45c7-ab1c-78372b70ac53", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539389327300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e47d1a3f-fb0d-4204-8152-6f297d1cb988", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539389373800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94f4fbe6-6937-4c97-98d7-5cae94dc40ad", "name": "entry : default@BuildNativeWithNinja cost memory 0.057830810546875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539390287700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37953d39-d5b4-4a7a-a37c-96e03bb603ee", "name": "runTaskFromQueue task cost before running: 351 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539390426700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "627e3173-6970-4dd4-a26b-3abf910df825", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539389180100, "endTime": 154539390484600, "totalTime": 1225000}, "additional": {"logType": "info", "children": [], "durationId": "849fc152-b850-4ece-81ec-ba1aeb76433d"}}, {"head": {"id": "a70bebf7-10c7-433d-8049-db2057b08a68", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539396683500, "endTime": 154539403048100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "10a993da-4e7a-4af9-bc7f-90075b9d72dd", "logId": "b763a496-71fa-4197-a218-36f722d5a34d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10a993da-4e7a-4af9-bc7f-90075b9d72dd", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539392972100}, "additional": {"logType": "detail", "children": [], "durationId": "a70bebf7-10c7-433d-8049-db2057b08a68"}}, {"head": {"id": "6d8ead93-29af-49ea-946c-141b3546dde9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539394316900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e2e0d1c-9975-441e-9dab-e5db4fc96553", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539394422300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f54c4ea-6adc-4291-8b5a-0d789b75fff9", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539395519500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "643025fc-19dc-40b3-bb70-9036b77d4c8b", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539398181900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3528c96b-5d88-4bc1-a8c9-45e3c81b6c6a", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539400306100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2e4a7df-7736-4f20-a827-982f5cf76943", "name": "entry : default@ProcessResource cost memory 0.3605804443359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539400488400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b763a496-71fa-4197-a218-36f722d5a34d", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539396683500, "endTime": 154539403048100}, "additional": {"logType": "info", "children": [], "durationId": "a70bebf7-10c7-433d-8049-db2057b08a68"}}, {"head": {"id": "9521f125-803b-4ff3-bd85-095166f8500a", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539411761300, "endTime": 154539435909800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c8437aca-2a95-40bd-aeeb-006cf8e4710f", "logId": "04a566d3-740d-4420-84c3-20100534dfa4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8437aca-2a95-40bd-aeeb-006cf8e4710f", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539407375700}, "additional": {"logType": "detail", "children": [], "durationId": "9521f125-803b-4ff3-bd85-095166f8500a"}}, {"head": {"id": "a3c40d8c-de20-4822-81b1-0a634bfdc09b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539408443800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "355f307f-1e83-4411-aa09-bba6591a5670", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539408557000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bd9c749-d936-46e8-ac40-cd71dba360b3", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539411771600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4368bb4-7883-401a-b0c5-124438952ff0", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539435665900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72b30670-6d01-40b3-8780-fe97393a2f21", "name": "entry : default@GenerateLoaderJson cost memory 0.8736724853515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539435841300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04a566d3-740d-4420-84c3-20100534dfa4", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539411761300, "endTime": 154539435909800}, "additional": {"logType": "info", "children": [], "durationId": "9521f125-803b-4ff3-bd85-095166f8500a"}}, {"head": {"id": "9f76d37c-a404-4226-a8a6-c4c345c3a18b", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539448214300, "endTime": 154539453714400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c81124fd-c69d-49a6-8e8e-6b77fc032a9b", "logId": "de03f693-360e-4cf4-81c4-736408043991"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c81124fd-c69d-49a6-8e8e-6b77fc032a9b", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539446432900}, "additional": {"logType": "detail", "children": [], "durationId": "9f76d37c-a404-4226-a8a6-c4c345c3a18b"}}, {"head": {"id": "ad92fc49-9d8b-4729-89fd-dd7b4ecb075a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539447437800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14941570-b580-42b4-81f9-305cedd8ae5d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539447551300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d45f0c34-2798-477a-a2df-e68187a5c982", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539448223900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d0edee0-9e84-4c4c-885a-2cae0929a311", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539453405000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc068427-d045-40bc-8b41-42d4017bd6ae", "name": "entry : default@ProcessLibs cost memory 0.141387939453125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539453611400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de03f693-360e-4cf4-81c4-736408043991", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539448214300, "endTime": 154539453714400}, "additional": {"logType": "info", "children": [], "durationId": "9f76d37c-a404-4226-a8a6-c4c345c3a18b"}}, {"head": {"id": "05ef85fa-74f8-470f-8c1a-8377854d83ce", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539461784000, "endTime": 154539493220200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "1982e5e4-4954-4a05-a12a-61a2a0084080", "logId": "24f19f25-62c3-40c2-af4b-f797980ad0fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1982e5e4-4954-4a05-a12a-61a2a0084080", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539456560300}, "additional": {"logType": "detail", "children": [], "durationId": "05ef85fa-74f8-470f-8c1a-8377854d83ce"}}, {"head": {"id": "46adc92c-c699-4e66-b8e8-8e451991fbce", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539457931500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d465d039-f1a0-4111-9e66-ab1f0d579036", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539458038200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40cae1d6-e311-44c1-a8aa-ea2e009c69ad", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539459087600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7214d2e4-40df-4578-80e4-382f63da2e5b", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539461810700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a889e4-f1a0-46ec-b2cb-fdb36562be30", "name": "Incremental task entry:default@CompileResource pre-execution cost: 30 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539492960800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b4c833d-5f4f-4e8b-a22c-55827cb157c2", "name": "entry : default@CompileResource cost memory 1.3124771118164062", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539493131800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24f19f25-62c3-40c2-af4b-f797980ad0fc", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539461784000, "endTime": 154539493220200}, "additional": {"logType": "info", "children": [], "durationId": "05ef85fa-74f8-470f-8c1a-8377854d83ce"}}, {"head": {"id": "a9dc4bd7-4fc6-4f55-b4ac-320ff1b048f8", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539499897900, "endTime": 154539503895100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7c2b64ce-a508-4d93-a3fd-3a2b1cd373a2", "logId": "9fe7ecbf-b22c-47f3-afdc-1e20ffd21135"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c2b64ce-a508-4d93-a3fd-3a2b1cd373a2", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539496015000}, "additional": {"logType": "detail", "children": [], "durationId": "a9dc4bd7-4fc6-4f55-b4ac-320ff1b048f8"}}, {"head": {"id": "d44b736f-8e1b-4e30-9749-8a9337147688", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539496953100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "383d237a-64f2-4caa-8115-2467cc9be12d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539497061600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a0078e1-8da8-4106-9fad-ce0516ff6829", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539499918800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "131682e0-0cdb-456c-9a42-8f03ba0e5db6", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539500739200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c3c36de-c7c9-46cd-abc1-ddaa4623620f", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539503598200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b798fe5a-0d78-431f-bc3e-a55327bd1741", "name": "entry : default@DoNativeStrip cost memory 0.07837677001953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539503812300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fe7ecbf-b22c-47f3-afdc-1e20ffd21135", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539499897900, "endTime": 154539503895100}, "additional": {"logType": "info", "children": [], "durationId": "a9dc4bd7-4fc6-4f55-b4ac-320ff1b048f8"}}, {"head": {"id": "53adab3a-50ff-4546-a878-4dda9e64e88a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539511677100, "endTime": 154550704771000}, "additional": {"children": ["88e25c23-2592-4f0e-8853-b316b56c9e3f", "122e79e5-2b48-42ad-a0be-4b50483d18bc"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "150145b1-b22c-47a4-b5f7-ecb08b259951", "logId": "eb212cda-5c61-491c-b681-95886f16b0f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "150145b1-b22c-47a4-b5f7-ecb08b259951", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539505838300}, "additional": {"logType": "detail", "children": [], "durationId": "53adab3a-50ff-4546-a878-4dda9e64e88a"}}, {"head": {"id": "ac03554c-e279-4191-89be-03904cde4ed0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539506980800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4536b715-b47b-42e1-bf45-382c5c07b4d9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539507088000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9af0679f-2f6a-4610-a680-b896b3ce3af9", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539511687500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8ce9af8-6b7e-4785-8985-987f26618dd0", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539511848100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80520d0d-0dc5-4090-b344-03aaf60ae27e", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539546315600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f27f5945-503d-4dc1-993c-b7d86082965f", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 29 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539546479700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fd01bb6-b540-4212-a1b2-6d9c8f3cc69f", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539564251800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01ac191a-aa0b-4c9c-b81e-b5ea54791868", "name": "default@CompileArkTS work[14] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539565601100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88e25c23-2592-4f0e-8853-b316b56c9e3f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154539568268300, "endTime": 154550704246400}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "53adab3a-50ff-4546-a878-4dda9e64e88a", "logId": "5551f213-ad45-459b-9515-840a88b33f9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b079470-1ccc-44b0-935f-3b1b81d04ed9", "name": "default@CompileArkTS work[14] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539566497700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8821092-c108-4b92-982d-399ba780333b", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539566594200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "270e76d3-4515-4155-9622-0238df684d71", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539566635000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51d95a0f-ec4f-4a2d-88fa-4ba426167a2e", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539566663200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b6c9e49-46fa-4566-9a4e-035673a1ee61", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539566690700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "809dc232-9386-4eb8-b7b4-f49c73de2156", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539566771400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6f6ae59-04c1-4105-8b91-3976e4bc8d58", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539566854000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d23b7302-130f-41a7-a31c-b22424907e9c", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539566891000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67b0c39d-fd96-4fe6-98ce-84b64566f5a2", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539566918400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26ca46da-cdbd-4e96-9085-2de25b5901ae", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539566942300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12a18a0d-6fb6-4398-9eb3-5c0f60f1787b", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539566966600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc5ad40f-c370-460e-8942-f25e9bfb2699", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539566992200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d123b336-4800-4474-a4bb-ab4d1dbfb5b0", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539567019200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "470cfdd4-a0c9-4a06-9005-4d2b316f37bf", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539567061800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd4b2b74-7afe-45df-8bb2-043f3d6bb428", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539567088100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa228e76-02ae-42ae-92d9-dd903ef23dd1", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539567113600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae3fcbe0-2312-4c43-8405-befc9de48a15", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539567162700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73f860d7-1921-4ddd-8f58-2a21b09a58d4", "name": "default@CompileArkTS work[14] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539568286500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "666ca508-5d5b-42d7-87c3-52c8d0b1eb43", "name": "default@CompileArkTS work[14] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539568445400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48415706-5b17-40bf-9bb7-8fa749a94fea", "name": "CopyResources startTime: 154539568504100", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539568507700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44bef1c9-5a16-4c26-93c0-7896dcf21980", "name": "default@CompileArkTS work[15] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539568604800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "122e79e5-2b48-42ad-a0be-4b50483d18bc", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 154540569663500, "endTime": 154540581968800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "53adab3a-50ff-4546-a878-4dda9e64e88a", "logId": "2c3aad64-69d3-4113-8594-487e35ba5b24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "107e92e2-b24d-402a-a0e1-668a1f5417aa", "name": "default@CompileArkTS work[15] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539569671100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08277131-113a-4e24-9214-b5297fc92778", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539569795000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d342dae-e60d-4853-8272-46fea425cebc", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539569847700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1c61e0c-a12a-434a-b52f-576d68d6a9a4", "name": "default@CompileArkTS work[15] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539570708500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15b19490-0133-4d4a-b333-afa316aea901", "name": "default@CompileArkTS work[15] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539570807200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f01a6a3-6a02-4568-b3ea-6df6dff65312", "name": "entry : default@CompileArkTS cost memory 2.4119338989257812", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539570902700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03185a39-4b9c-4cc3-abd1-aaa53500ca36", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539578120100, "endTime": 154539589839900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "4b8f04a3-81f9-41be-88f6-a229508ceae0", "logId": "2ca621f0-63ea-4982-a229-5f43c959a629"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b8f04a3-81f9-41be-88f6-a229508ceae0", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539572510900}, "additional": {"logType": "detail", "children": [], "durationId": "03185a39-4b9c-4cc3-abd1-aaa53500ca36"}}, {"head": {"id": "36d37608-0833-4f73-9dc7-1739bb475e63", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539573541700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "973f60ea-3b54-4097-882a-95c6c1a5d81e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539573638100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fc9d4e6-e2d1-4c10-985a-aab012fe6eaa", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539578135200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcb6e3fe-20b0-4f2b-b978-1e3e972ce97a", "name": "entry : default@BuildJS cost memory 0.34134674072265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539589619200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45287799-1606-4029-83c9-fb76776e80df", "name": "runTaskFromQueue task cost before running: 550 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539589775100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ca621f0-63ea-4982-a229-5f43c959a629", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539578120100, "endTime": 154539589839900, "totalTime": 11629400}, "additional": {"logType": "info", "children": [], "durationId": "03185a39-4b9c-4cc3-abd1-aaa53500ca36"}}, {"head": {"id": "477b69cf-abbe-42d2-aba8-27990219b774", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539603100900, "endTime": 154539606705600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f64b2a4e-12f7-4b9f-9632-b48abc309e84", "logId": "ecbc80a1-bebc-4d55-aeca-14c6b1932fbf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f64b2a4e-12f7-4b9f-9632-b48abc309e84", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539593003400}, "additional": {"logType": "detail", "children": [], "durationId": "477b69cf-abbe-42d2-aba8-27990219b774"}}, {"head": {"id": "b5c7704b-65ef-4925-9315-75f548a70143", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539595410400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab1a6bb4-8bb0-417b-8a35-a317c266abd1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539595579400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b6f1b76-4747-48b6-a454-70f7daf65b3b", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539603117700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2d63801-441b-4964-b968-ff99668c115f", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539604105600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f06a9c05-d04c-40dc-8d95-1b5a6f585d3b", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539606524600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85cd145c-d8e5-4080-a116-c60b2947d192", "name": "entry : default@CacheNativeLibs cost memory 0.09325408935546875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539606641200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecbc80a1-bebc-4d55-aeca-14c6b1932fbf", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539603100900, "endTime": 154539606705600}, "additional": {"logType": "info", "children": [], "durationId": "477b69cf-abbe-42d2-aba8-27990219b774"}}, {"head": {"id": "1db9c1a4-837c-4a51-a8fd-f2a96d7169ae", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154540582199700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47de72be-40d0-4f11-8d78-b4b4e3f9cb49", "name": "CopyResources is end, endTime: 154540582344200", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154540582346800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a8cea72-b0da-41f4-8ad4-f3fce33e26d1", "name": "default@CompileArkTS work[15] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154540582427400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c3aad64-69d3-4113-8594-487e35ba5b24", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 154540569663500, "endTime": 154540581968800}, "additional": {"logType": "info", "children": [], "durationId": "122e79e5-2b48-42ad-a0be-4b50483d18bc", "parent": "eb212cda-5c61-491c-b681-95886f16b0f4"}}, {"head": {"id": "4bd4a6dc-3256-4731-990d-f4f562061855", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154540582530100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df7fa8d6-549e-489d-83ca-7ac405bd56b0", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550703873900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3000f5ae-2714-45f6-9b01-44179ce6e5c3", "name": "default@CompileArkTS work[14] failed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550704442300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5551f213-ad45-459b-9515-840a88b33f9e", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154539568268300, "endTime": 154550704246400}, "additional": {"logType": "error", "children": [], "durationId": "88e25c23-2592-4f0e-8853-b316b56c9e3f", "parent": "eb212cda-5c61-491c-b681-95886f16b0f4"}}, {"head": {"id": "eb212cda-5c61-491c-b681-95886f16b0f4", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539511677100, "endTime": 154550704771000}, "additional": {"logType": "error", "children": ["5551f213-ad45-459b-9515-840a88b33f9e", "2c3aad64-69d3-4113-8594-487e35ba5b24"], "durationId": "53adab3a-50ff-4546-a878-4dda9e64e88a"}}, {"head": {"id": "153ac417-f093-4a1f-8a92-af3b308ab4ec", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550705157400}, "additional": {"logType": "debug", "children": [], "durationId": "53adab3a-50ff-4546-a878-4dda9e64e88a"}}, {"head": {"id": "7c2f130c-2828-4efa-a385-3a545e421845", "name": "ERROR: stacktrace = Error: Could not resolve \"./AppListComponent\" from \"entry/src/main/ets/components/HomePageComponent.ets\"\r\n\u001b[33m1 WARN: \u001b[33m\u001b[33mArkTS:WARN: For details about ArkTS syntax errors, see FAQs\u001b[39m\u001b[39m\r\n\u001b[31m1 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:27:3\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m2 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:28:3\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m3 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:29:3\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m4 ERROR: \u001b[31m10605074 ArkTS Compiler Error\r\nError Message: Destructuring variable declarations are not supported (arkts-no-destruct-decls) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:44:13\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m5 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/FeaturedPageComponent.ets:19:3\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m6 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/FeaturedPageComponent.ets:33:13\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m7 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/FeaturedPageComponent.ets:62:13\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m8 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/CategoryListPageComponent.ets:18:3\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m9 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/CategoryListPageComponent.ets:32:13\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m10 ERROR: \u001b[31m10605090 ArkTS Compiler Error\r\nError Message: Function return type inference is limited (arkts-no-implicit-return-types) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/CategoryListPageComponent.ets:70:35\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m11 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/ProfilePageComponent.ets:17:3\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m12 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/ProfilePageComponent.ets:29:13\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m13 ERROR: \u001b[31m10605008 ArkTS Compiler Error\r\nError Message: Use explicit types instead of \"any\", \"unknown\" (arkts-no-any-unknown) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/ProfilePageComponent.ets:33:15\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m14 ERROR: \u001b[31m10605090 ArkTS Compiler Error\r\nError Message: Function return type inference is limited (arkts-no-implicit-return-types) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/ProfilePageComponent.ets:211:69\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m15 ERROR: \u001b[31m10605090 ArkTS Compiler Error\r\nError Message: Function return type inference is limited (arkts-no-implicit-return-types) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/ProfilePageComponent.ets:217:65\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m16 ERROR: \u001b[31m10605090 ArkTS Compiler Error\r\nError Message: Function return type inference is limited (arkts-no-implicit-return-types) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/ProfilePageComponent.ets:223:68\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m17 ERROR: \u001b[31m10605090 ArkTS Compiler Error\r\nError Message: Function return type inference is limited (arkts-no-implicit-return-types) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/ProfilePageComponent.ets:230:67\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m18 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module './AppListComponent' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:1:34\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m19 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module './CategoryGridComponent' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:2:39\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m20 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module './SearchBarComponent' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:3:36\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m21 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module './BannerComponent' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:4:33\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m22 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module '../models/AppModel' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:7:26\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m23 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module '../models/CategoryModel' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:8:31\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m24 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module '../models/BannerModel' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:9:29\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m25 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module '../services/AppService' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:10:28\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m26 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module '../services/CategoryService' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:11:33\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m27 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module '../services/BannerService' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:12:31\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m28 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module '../models/FeaturedCollectionModel' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/FeaturedPageComponent.ets:1:41\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m29 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module '../services/FeaturedCollectionService' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/FeaturedPageComponent.ets:2:43\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m30 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'TINY' does not exist on type 'FontSizeConfig'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/FeaturedPageComponent.ets:134:80\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m31 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'TINY' does not exist on type 'FontSizeConfig'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/FeaturedPageComponent.ets:139:82\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m32 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module '../models/CategoryModel' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/CategoryListPageComponent.ets:1:31\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m33 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module '../services/CategoryService' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/CategoryListPageComponent.ets:2:33\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m34 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'TINY' does not exist on type 'FontSizeConfig'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/CategoryListPageComponent.ets:125:78\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m35 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module '../services/UserService' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/ProfilePageComponent.ets:3:29\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m36 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Cannot find module '../models/UserModel' or its corresponding type declarations. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/ProfilePageComponent.ets:4:27\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m37 ERROR: \u001b[31m10905204 ArkTS Compiler Error\r\nError Message: 'SearchBarComponent({\n              onSearch: (keyword: string) => {\n                this.getUIContext().getRouter().pushUrl({\n                  url: 'pages/SearchPage',\n                  params: { keyword: keyword }\n                });\n              }\n            })\n            .margin({ top: 8, left: 16, right: 16, bottom: 8 })' does not meet UI component syntax. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:105:13\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m38 ERROR: \u001b[31m10905204 ArkTS Compiler Error\r\nError Message: 'BannerComponent({ banners: this.banners })\n                .margin({ bottom: 16 })' does not meet UI component syntax. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:117:15\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m39 ERROR: \u001b[31m10905204 ArkTS Compiler Error\r\nError Message: 'CategoryGridComponent({ \n                categories: this.categories,\n                maxDisplayCount: this.deviceUtils.isPhone() ? 8 : 12\n              })\n              .margin({ left: 16, right: 16, bottom: 24 })' does not meet UI component syntax. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:123:15\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m40 ERROR: \u001b[31m10905204 ArkTS Compiler Error\r\nError Message: 'AppListComponent({\n                  apps: this.featuredApps,\n                  layoutType: 'horizontal',\n                  showMoreButton: false\n                })' does not meet UI component syntax. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/HomePageComponent.ets:154:17\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:41 WARN:1}\u001b[39m\n    at runArkPack (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5479)\r\nRollupError: Could not resolve \"./AppListComponent\" from \"entry/src/main/ets/components/HomePageComponent.ets\"\n    at error (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-base\\node_modules\\rollup\\dist\\shared\\rollup.js:210:30)\n    at ModuleLoader.handleInvalidResolvedId (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-base\\node_modules\\rollup\\dist\\shared\\rollup.js:24211:24)\n    at C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-base\\node_modules\\rollup\\dist\\shared\\rollup.js:24173:28", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550706569200}, "additional": {"logType": "debug", "children": [], "durationId": "53adab3a-50ff-4546-a878-4dda9e64e88a"}}, {"head": {"id": "88300605-e1eb-4c9d-b39b-6bac8d3c6430", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550724936500, "endTime": 154550725179000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f56b273c-e76b-4c9b-944d-9c3551445160", "logId": "b250f999-f834-407b-8e65-d2e3abe56850"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b250f999-f834-407b-8e65-d2e3abe56850", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550724936500, "endTime": 154550725179000}, "additional": {"logType": "info", "children": [], "durationId": "88300605-e1eb-4c9d-b39b-6bac8d3c6430"}}, {"head": {"id": "c79083a3-be91-4a1f-a7c0-dea336368e6c", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154539039898600, "endTime": 154550725610800}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 53, "second": 0}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "30e5fac3-2aa3-4a1f-8a80-82b99d2e0c37", "name": "BUILD FAILED in 11 s 686 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550725661900}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "15f34e25-248c-41d3-a186-6fd239734cf8", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550725962800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "234ac629-a061-4cb1-a5f6-b42e67be593d", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550726040200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6570f15a-2d30-4a9f-8269-5f0dd35c3737", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550726518300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9af7652d-ba96-4ef1-baa0-f06431e70923", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550726645200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b84dc8f1-eee5-4a30-9f41-6bc637466934", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550726703900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04fb018a-b77c-4e75-aa39-0884ba60ed2f", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550726755700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b831dc5-fcce-48f9-9029-5a140670fcd8", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550726812800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a71a2478-7b9b-4892-8e01-ed525501c882", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550728314200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0489f9ea-636e-49b2-a761-3e7c3fd9bc4a", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550728760700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b4cf294-2bd1-4aeb-8d6c-da0d3ff5414f", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550728857400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f5c1df0-0030-417c-9d2f-298fe7f10de0", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550728907500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b809da9-d29a-4724-a0a9-2ca101321b71", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550728956200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7d9ef22-8ab0-4504-8ed5-c5b7f0e280c7", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550729004200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d80da1f-3143-4e99-a629-7015011698e4", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550730527700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c1ff97c-a21b-4155-a6b3-dd563adffc9b", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550730994000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab7d74cc-79b3-4412-a873-b13e73d989d4", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550731275300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efefd11d-bc7d-4e55-940b-511e1895ea58", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550731343200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cae5c43-64d7-49af-8430-31547028aab8", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550731418900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed5326a9-c6cd-4027-a5b3-203e33c7ee3c", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550731465200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "821296fd-7d64-433d-89db-2332f7da61eb", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550731502600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e04edd52-9921-497d-bcc5-c4cfbb70b2c8", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550731541000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51e981b3-da3b-46a5-b166-1e9ae7bac09a", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550735744300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "224f54fe-bed7-4e44-97cd-fe3ae9ed5fe3", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550736701300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "579ad6d8-ce60-4e38-8afc-7c0a42c13283", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550737222200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "786b88a6-76f7-4d8e-aa6f-6cd660c18b3d", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550737554100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23e78100-a02b-4e5d-8a82-bf0cc4797059", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550737843600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3a871a4-aebe-48b6-aadc-47f193bd3e80", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550738935500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28de1566-61cd-428c-9d51-cc5f829731de", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550739047500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a001c6e2-551d-445d-b827-f2347223f784", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550739326500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df068ef7-e8a2-4756-9275-39680cf2c9d8", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550739804100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b96e1648-e867-4f34-a0be-94bdb3258afb", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550741114000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8294d071-4c74-46dc-8b28-cb54f69b8be5", "name": "Incremental task entry:default@CompileArkTS post-execution cost:11 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550741885500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9a16609-20df-4f3b-9377-fe33a0899195", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550746621600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b4d2134-c7e3-47cc-91da-47b363d08cb4", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550748846900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2e0e37c-cd78-4677-aacd-57aeee992fe3", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550750056000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25230af7-3c65-464e-a1bd-1aa88c8db0e0", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550750842200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af1d13ab-ee06-4c95-a2ab-c8e35a3ef4b1", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550751563700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76af96f1-2582-4153-907f-9b26d09d07dc", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550754240600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b43d46a-113e-44d8-84d2-311faab3a91d", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550757305500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a9120a3-3ed2-4fc0-a26a-6f5146f06c55", "name": "Incremental task entry:default@BuildJS post-execution cost:16 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550757948700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c160ef8-cd76-4892-940c-1fe8da1dc6d8", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550758092900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}