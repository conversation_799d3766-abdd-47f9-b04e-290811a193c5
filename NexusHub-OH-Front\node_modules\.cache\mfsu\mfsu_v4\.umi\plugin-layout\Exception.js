"use strict";
import { jsx } from "react/jsx-runtime";
import { history } from "@umijs/max";
import { Result, Button } from "antd";
const Exception = (props) => (
  // render custom 404
  !props.route && (props.noFound || props.notFound) || // render custom 403
  props.route?.unaccessible && (props.unAccessible || props.noAccessible) || // render default exception
  (!props.route || props.route?.unaccessible) && /* @__PURE__ */ jsx(
    Result,
    {
      status: props.route ? "403" : "404",
      title: props.route ? "403" : "404",
      subTitle: props.route ? "\u62B1\u6B49\uFF0C\u4F60\u65E0\u6743\u8BBF\u95EE\u8BE5\u9875\u9762" : "\u62B1\u6B49\uFF0C\u4F60\u8BBF\u95EE\u7684\u9875\u9762\u4E0D\u5B58\u5728",
      extra: /* @__PURE__ */ jsx(But<PERSON>, { type: "primary", onClick: () => history.push("/"), children: "\u8FD4\u56DE\u9996\u9875" })
    }
  ) || // normal render
  props.children
);
export default Exception;
