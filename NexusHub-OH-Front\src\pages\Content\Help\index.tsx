import { PageContainer } from '@ant-design/pro-components';
import { Card, Table, Button, Tag, Badge, Space, Modal, Form, Input, Select, message, Tabs, Row, Col, Tree, Typography, Divider } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined, QuestionCircleOutlined, FileTextOutlined, BookOutlined, SearchOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';
import { TreeProps } from 'antd/es/tree';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

interface HelpItem {
  id: string;
  title: string;
  content: string;
  type: 'guide' | 'faq' | 'policy';
  category: 'user' | 'developer' | 'security' | 'other';
  order: number;
  status: 'draft' | 'published' | 'archived';
  createdTime: string;
  createdBy: string;
  updatedTime?: string;
  updatedBy?: string;
}

// 定义API返回数据类型
interface HelpListResponse {
  data: HelpItem[];
  total: number;
}

// 模拟数据获取函数
const fetchHelpList = async (params: any): Promise<HelpListResponse> => {
  // 实际项目中应该调用API
  console.log('Fetching help list with params:', params);
  
  // 模拟数据
  const mockData: HelpItem[] = [
    {
      id: '1',
      title: '如何下载应用',
      content: '<h3>下载步骤</h3><p>1. 在应用详情页点击下载按钮</p><p>2. 等待下载完成并安装</p>',
      type: 'guide',
      category: 'user',
      order: 1,
      status: 'published',
      createdTime: '2023-05-01 10:00:00',
      createdBy: 'admin',
      updatedTime: '2023-05-10 14:20:00',
      updatedBy: 'admin',
    },
    {
      id: '2',
      title: '如何评价应用',
      content: '<h3>评价步骤</h3><ol><li>进入已下载应用的详情页</li><li>点击"写评论"按钮</li><li>选择星级评分</li><li>填写评论内容</li><li>提交评论</li></ol>',
      type: 'faq',
      category: 'user',
      order: 1,
      status: 'published',
      createdTime: '2023-05-02 11:30:00',
      createdBy: 'admin',
    },
    {
      id: '3',
      title: '应用上架流程',
      content: '<h3>上架步骤</h3><ol><li>登录开发者账号</li><li>创建应用</li><li>上传应用包</li><li>填写应用信息</li><li>提交审核</li><li>等待审核结果</li><li>发布上线</li></ol>',
      type: 'guide',
      category: 'developer',
      order: 1,
      status: 'published',
      createdTime: '2023-05-04 14:20:00',
      createdBy: 'admin',
    },
    {
      id: '4',
      title: '开发者入驻指南',
      content: '<h3>申请条件</h3><p>1. 拥有合法的开发者资质</p><p>2. 提供真实有效的身份和企业信息</p><h3>申请流程</h3><ol><li>注册开发者账号</li><li>提交资质审核</li><li>签署开发者协议</li><li>完成账号设置</li></ol>',
      type: 'guide',
      category: 'developer',
      order: 2,
      status: 'draft',
      createdTime: '2023-05-05 15:30:00',
      createdBy: 'admin',
      updatedTime: '2023-05-12 16:40:00',
      updatedBy: 'admin',
    },
  ];

  // 根据类型过滤
  const typeFilteredData = params.type ? mockData.filter(item => item.type === params.type) : mockData;
  
  // 根据状态过滤
  const statusFilteredData = params.status ? typeFilteredData.filter(item => item.status === params.status) : typeFilteredData;
  
  // 根据分类过滤
  const categoryFilteredData = params.category ? statusFilteredData.filter(item => item.category === params.category) : statusFilteredData;
  
  // 根据关键词过滤
  const keywordFilteredData = params.keyword
    ? categoryFilteredData.filter(item => 
        item.title.toLowerCase().includes(params.keyword.toLowerCase()) ||
        item.content.toLowerCase().includes(params.keyword.toLowerCase())
      )
    : categoryFilteredData;

  return { data: keywordFilteredData, total: keywordFilteredData.length };
};

const HelpContentManagement: React.FC = () => {
  const [searchParams, setSearchParams] = useState({});
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [currentHelp, setCurrentHelp] = useState<HelpItem | null>(null);
  const [editForm] = Form.useForm();
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });
  const [activeTab, setActiveTab] = useState('all');

  const { data, loading, refresh } = useRequest(() => fetchHelpList({
    ...searchParams,
    ...pagination,
    type: activeTab !== 'all' ? activeTab : undefined,
  }), {
    refreshDeps: [searchParams, pagination, activeTab],
  });

  // 创建本地数据副本，确保data存在
  const helpData: HelpListResponse = data || { data: [], total: 0 };

  const handleSearch = (values: any) => {
    setSearchParams(values);
    setPagination({ ...pagination, current: 1 });
  };

  const handleTableChange = (newPagination: any) => {
    setPagination({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    });
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setPagination({ ...pagination, current: 1 });
  };

  const handleAdd = () => {
    setCurrentHelp(null);
    editForm.resetFields();
    setEditModalVisible(true);
  };

  const handleEdit = (record: HelpItem) => {
    setCurrentHelp(record);
    editForm.setFieldsValue({
      title: record.title,
      content: record.content,
      type: record.type,
      category: record.category,
      order: record.order,
      status: record.status,
    });
    setEditModalVisible(true);
  };

  const handlePreview = (record: HelpItem) => {
    setCurrentHelp(record);
    setPreviewModalVisible(true);
  };

  const handleDelete = (record: HelpItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除「${record.title}」吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        console.log(`Delete help item ${record.id}`);
        // 实际项目中应该调用API
        message.success('删除成功');
        refresh();
      },
    });
  };

  const handleEditSubmit = () => {
    editForm.validateFields().then(values => {
      console.log('Form values:', values);
      // 实际项目中应该调用API
      message.success(currentHelp ? '更新成功' : '添加成功');
      setEditModalVisible(false);
      refresh();
    });
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'draft':
        return <Tag color="default">草稿</Tag>;
      case 'published':
        return <Tag color="success">已发布</Tag>;
      case 'archived':
        return <Tag color="warning">已归档</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const getTypeTag = (type: string) => {
    switch (type) {
      case 'guide':
        return <Tag color="blue" icon={<BookOutlined />}>指南</Tag>;
      case 'faq':
        return <Tag color="purple" icon={<QuestionCircleOutlined />}>常见问题</Tag>;
      case 'policy':
        return <Tag color="orange" icon={<FileTextOutlined />}>政策</Tag>;
      default:
        return <Tag>{type}</Tag>;
    }
  };

  const getCategoryText = (category: string) => {
    switch (category) {
      case 'user':
        return '用户指南';
      case 'developer':
        return '开发者指南';
      case 'security':
        return '安全相关';
      case 'other':
        return '其他';
      default:
        return category;
    }
  };

  const columns: ColumnsType<HelpItem> = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <a onClick={() => handleEdit(record)}>{text}</a>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => getTypeTag(type),
      filters: [
        { text: '指南', value: 'guide' },
        { text: '常见问题', value: 'faq' },
        { text: '政策', value: 'policy' },
      ],
      onFilter: (value, record) => record.type === value,
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category) => getCategoryText(category),
      filters: [
        { text: '用户指南', value: 'user' },
        { text: '开发者指南', value: 'developer' },
        { text: '安全相关', value: 'security' },
      ],
      onFilter: (value, record) => record.category === value,
    },
    {
      title: '排序',
      dataIndex: 'order',
      key: 'order',
      sorter: (a, b) => a.order - b.order,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status),
      filters: [
        { text: '草稿', value: 'draft' },
        { text: '已发布', value: 'published' },
        { text: '已归档', value: 'archived' },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: '创建/更新信息',
      key: 'created',
      render: (_, record) => (
        <div>
          <div>创建人: {record.createdBy}</div>
          <div>创建时间: {record.createdTime}</div>
          {record.updatedTime && (
            <div>更新时间: {record.updatedTime}</div>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <a onClick={() => handleEdit(record)}><EditOutlined /> 编辑</a>
          <a onClick={() => handlePreview(record)}><SearchOutlined /> 预览</a>
          <a onClick={() => handleDelete(record)}><DeleteOutlined /> 删除</a>
        </Space>
      ),
    },
  ];

  // 帮助内容分类树数据
  const treeData = [
    {
      title: '用户指南',
      key: 'user',
      children: [
        { title: '应用下载', key: 'user-download' },
        { title: '账号管理', key: 'user-account' },
        { title: '评价反馈', key: 'user-feedback' },
      ],
    },
    {
      title: '开发者指南',
      key: 'developer',
      children: [
        { title: '入驻流程', key: 'developer-register' },
        { title: '应用上架', key: 'developer-publish' },
        { title: '收益结算', key: 'developer-payment' },
      ],
    },
    {
      title: '安全相关',
      key: 'security',
      children: [
        { title: '隐私政策', key: 'security-privacy' },
        { title: '账号安全', key: 'security-account' },
        { title: '内容安全', key: 'security-content' },
      ],
    },
  ];

  return (
    <PageContainer
      header={{
        title: '帮助内容管理',
      }}
    >
      <Row gutter={24}>
        <Col span={6}>
          <Card title="内容分类" bordered={false}>
            <Tree
              defaultExpandAll
              treeData={treeData}
              onSelect={(selectedKeys) => {
                if (selectedKeys.length > 0) {
                  const key = selectedKeys[0].toString();
                  // 根据选中的树节点筛选内容
                  if (key.includes('-')) {
                    // 子分类
                    const category = key.split('-')[0];
                    setSearchParams({ ...searchParams, category });
                  } else {
                    // 主分类
                    setSearchParams({ ...searchParams, category: key });
                  }
                  setPagination({ ...pagination, current: 1 });
                }
              }}
            />
          </Card>
        </Col>
        <Col span={18}>
          <Card bordered={false}>
            <Tabs 
              activeKey={activeTab} 
              onChange={handleTabChange}
              tabBarExtraContent={
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                  添加内容
                </Button>
              }
            >
              <Tabs.TabPane tab="全部" key="all" />
              <Tabs.TabPane tab="指南" key="guide" />
              <Tabs.TabPane tab="常见问题" key="faq" />
              <Tabs.TabPane tab="政策" key="policy" />
            </Tabs>

            <Form layout="inline" onFinish={handleSearch} style={{ marginBottom: 24 }}>
              <Form.Item name="keyword" label="关键词">
                <Input placeholder="标题/内容" style={{ width: 200 }} />
              </Form.Item>
              <Form.Item name="status" label="状态">
                <Select
                  placeholder="选择状态"
                  style={{ width: 120 }}
                  allowClear
                  options={[
                    { value: 'draft', label: '草稿' },
                    { value: 'published', label: '已发布' },
                    { value: 'archived', label: '已归档' },
                  ]}
                />
              </Form.Item>
              <Form.Item>
                <Button type="primary" htmlType="submit">
                  搜索
                </Button>
              </Form.Item>
            </Form>

            <Table
              columns={columns}
              dataSource={helpData.data}
              rowKey="id"
              loading={loading}
              pagination={{
                ...pagination,
                total: helpData.total,
                showSizeChanger: true,
                showQuickJumper: true,
              }}
              onChange={handleTableChange}
            />
          </Card>
        </Col>
      </Row>

      {/* 编辑弹窗 */}
      <Modal
        title={currentHelp ? '编辑帮助内容' : '添加帮助内容'}
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form form={editForm} layout="vertical" onFinish={handleEditSubmit}>
          <Row gutter={16}>
            <Col span={16}>
              <Form.Item
                name="title"
                label="标题"
                rules={[{ required: true, message: '请输入标题' }]}
              >
                <Input placeholder="请输入标题" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="type"
                label="类型"
                rules={[{ required: true, message: '请选择类型' }]}
              >
                <Select placeholder="请选择类型">
                  <Select.Option value="guide">指南</Select.Option>
                  <Select.Option value="faq">常见问题</Select.Option>
                  <Select.Option value="policy">政策</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={16}>
              <Form.Item
                name="category"
                label="分类"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select placeholder="请选择分类">
                  <Select.Option value="user">用户指南</Select.Option>
                  <Select.Option value="developer">开发者指南</Select.Option>
                  <Select.Option value="security">安全相关</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="order"
                label="排序"
                rules={[{ required: true, message: '请输入排序值' }]}
                initialValue={1}
              >
                <Input type="number" min={1} placeholder="数字越小越靠前" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="content"
            label="内容"
            rules={[{ required: true, message: '请输入内容' }]}
          >
            <ReactQuill 
              theme="snow" 
              style={{ height: 300, marginBottom: 50 }}
              modules={{
                toolbar: [
                  [{ 'header': [1, 2, 3, false] }],
                  ['bold', 'italic', 'underline', 'strike'],
                  [{'list': 'ordered'}, {'list': 'bullet'}],
                  ['link', 'image'],
                  ['clean']
                ],
              }}
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Select.Option value="draft">草稿</Select.Option>
              <Select.Option value="published">发布</Select.Option>
              <Select.Option value="archived">归档</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                提交
              </Button>
              <Button onClick={() => setEditModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 预览弹窗 */}
      <Modal
        title="内容预览"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        {currentHelp && (
          <div>
            <Typography.Title level={3}>{currentHelp.title}</Typography.Title>
            <Space style={{ marginBottom: 16 }}>
              {getTypeTag(currentHelp.type)}
              {getStatusTag(currentHelp.status)}
              <span>分类: {getCategoryText(currentHelp.category)}</span>
            </Space>
            <Divider />
            <div dangerouslySetInnerHTML={{ __html: currentHelp.content }} />
          </div>
        )}
      </Modal>
    </PageContainer>
  );
};

export default HelpContentManagement;