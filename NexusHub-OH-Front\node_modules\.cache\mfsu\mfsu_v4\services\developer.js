"use strict";
import { request } from "@umijs/max";
export async function getDeveloperApps(params) {
  return request("/developer/apps", {
    method: "GET",
    params
  });
}
export async function getDeveloperVerifyStatus() {
  return request("/api/v1/developers/verify/status", {
    method: "GET"
  });
}
export async function submitDeveloperVerify(body) {
  return request("/api/v1/developers/verify", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    data: body
  });
}
