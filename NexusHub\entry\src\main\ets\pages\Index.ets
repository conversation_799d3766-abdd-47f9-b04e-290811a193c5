import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 应用主入口页面
 * 使用Tabs组件实现底部导航栏和页面切换功能
 */
@Entry
@Component
struct Index {
  @State currentTabIndex: number = 0;
  private deviceUtils = DeviceUtils.getInstance();
  private tabsController: TabsController = new TabsController();
  private static readonly DOMAIN = 0x0000;
  private static readonly TAG = 'Index';

  aboutToAppear() {
    hilog.info(Index.DOMAIN, Index.TAG, '主应用界面初始化');
  }

  /**
   * 构建自定义TabBar
   */
  @Builder
  private TabBarBuilder(title: string, icon: Resource, activeIcon: Resource, index: number) {
    Column({ space: 4 }) {
      Image(this.currentTabIndex === index ? activeIcon : icon)
        .width(24)
        .height(24)
        .fillColor(this.currentTabIndex === index ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_HINT)
        .objectFit(ImageFit.Contain)

      Text(title)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
        .fontColor(this.currentTabIndex === index ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_HINT)
        .fontWeight(this.currentTabIndex === index ? FontWeight.Medium : FontWeight.Normal)
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  /**
   * 首页内容组件
   */
  @Builder
  private HomePageContent() {
    Scroll() {
      Column({ space: 24 }) {
        // 欢迎区域
        Column({ space: 16 }) {
          Text('欢迎使用 NexusHub')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.EXTRA_LARGE))
            .fontWeight(FontWeight.Bold)
            .fontColor($r('sys.color.ohos_id_color_text_primary'))

          Text('发现和下载优质应用')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM))
            .fontColor($r('sys.color.ohos_id_color_text_secondary'))
        }
        .width('100%')
        .padding(24)
        .backgroundColor($r('sys.color.ohos_id_color_foreground_contrary'))
        .borderRadius(16)
        .margin({ left: 16, right: 16, top: 16 })

        // 快捷操作
        Column({ space: 16 }) {
          Text('快捷操作')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontWeight(FontWeight.Medium)
            .fontColor($r('sys.color.ohos_id_color_text_primary'))
            .alignSelf(ItemAlign.Start)

          Row({ space: 16 }) {
            Button('搜索应用')
              .type(ButtonType.Normal)
              .borderRadius(8)
              .backgroundColor(Constants.COLORS.PRIMARY)
              .fontColor($r('sys.color.ohos_id_color_foreground_contrary'))
              .layoutWeight(1)
              .onClick(() => {
                this.getUIContext().getRouter().pushUrl({ url: 'pages/SearchPage' });
              })

            Button('浏览分类')
              .type(ButtonType.Normal)
              .borderRadius(8)
              .backgroundColor($r('sys.color.ohos_id_color_button_normal'))
              .fontColor($r('sys.color.ohos_id_color_text_primary'))
              .layoutWeight(1)
              .onClick(() => {
                this.currentTabIndex = 2; // 切换到分类页面
              })
          }
          .width('100%')
        }
        .width('100%')
        .padding(16)
        .backgroundColor($r('sys.color.ohos_id_color_foreground_contrary'))
        .borderRadius(16)
        .margin({ left: 16, right: 16, bottom: 16 })
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
    .scrollBar(BarState.Off)
    .edgeEffect(EdgeEffect.Spring)
  }

  /**
   * 精选页面内容组件
   */
  @Builder
  private FeaturedPageContent() {
    Column({ space: 24 }) {
      Text('精选集合')
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.EXTRA_LARGE))
        .fontWeight(FontWeight.Bold)
        .fontColor($r('sys.color.ohos_id_color_text_primary'))
        .alignSelf(ItemAlign.Start)
        .margin({ left: 16, right: 16, top: 16 })

      Column({ space: 16 }) {
        Text('发现精彩应用集合')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM))
          .fontColor($r('sys.color.ohos_id_color_text_secondary'))
          .textAlign(TextAlign.Center)

        Text('这里将展示各种主题的应用集合，帮助您快速找到感兴趣的应用。')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor($r('sys.color.ohos_id_color_text_secondary'))
          .textAlign(TextAlign.Center)
          .margin({ left: 32, right: 32 })

        Button('即将推出')
          .type(ButtonType.Normal)
          .borderRadius(8)
          .backgroundColor($r('sys.color.ohos_id_color_button_normal'))
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
          .enabled(false)
          .margin({ top: 16 })
      }
      .width('100%')
      .layoutWeight(1)
      .justifyContent(FlexAlign.Center)
      .padding(24)
      .backgroundColor($r('sys.color.ohos_id_color_foreground_contrary'))
      .borderRadius(16)
      .margin({ left: 16, right: 16, bottom: 16 })
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
  }

  /**
   * 分类页面内容组件
   */
  @Builder
  private CategoryPageContent() {
    Column({ space: 24 }) {
      Text('应用分类')
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.EXTRA_LARGE))
        .fontWeight(FontWeight.Bold)
        .fontColor($r('sys.color.ohos_id_color_text_primary'))
        .alignSelf(ItemAlign.Start)
        .margin({ left: 16, right: 16, top: 16 })

      // 搜索栏
      Row({ space: 12 }) {
        TextInput({ placeholder: '搜索分类...' })
          .layoutWeight(1)
          .height(40)
          .borderRadius(20)
          .backgroundColor($r('sys.color.ohos_id_color_button_normal'))
          .padding({ left: 16, right: 16 })

        Button({ type: ButtonType.Circle }) {
          Image($r('app.media.ic_search'))
            .width(20)
            .height(20)
            .fillColor($r('sys.color.ohos_id_color_foreground_contrary'))
        }
        .width(40)
        .height(40)
        .backgroundColor(Constants.COLORS.PRIMARY)
      }
      .width('100%')
      .padding({ left: 16, right: 16 })

      // 内容区域
      Column({ space: 16 }) {
        Text('浏览应用分类')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM))
          .fontColor($r('sys.color.ohos_id_color_text_secondary'))
          .textAlign(TextAlign.Center)

        Text('按照不同类别快速找到您需要的应用，包括游戏、工具、社交、教育等各种分类。')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor($r('sys.color.ohos_id_color_text_secondary'))
          .textAlign(TextAlign.Center)
          .margin({ left: 32, right: 32 })

        Button('即将推出')
          .type(ButtonType.Normal)
          .borderRadius(8)
          .backgroundColor($r('sys.color.ohos_id_color_button_normal'))
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
          .enabled(false)
          .margin({ top: 16 })
      }
      .width('100%')
      .layoutWeight(1)
      .justifyContent(FlexAlign.Center)
      .padding(24)
      .backgroundColor($r('sys.color.ohos_id_color_foreground_contrary'))
      .borderRadius(16)
      .margin({ left: 16, right: 16, bottom: 16 })
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
  }

  /**
   * 个人中心页面内容组件
   */
  @Builder
  private ProfilePageContent() {
    Scroll() {
      Column({ space: 24 }) {
        // 用户信息区域
        Row({ space: 16 }) {
          Image($r('app.media.ic_profile'))
            .width(64)
            .height(64)
            .borderRadius(32)
            .backgroundColor($r('sys.color.ohos_id_color_button_normal'))
            .fillColor($r('sys.color.ohos_id_color_text_secondary'))

          Column({ space: 4 }) {
            Text('未登录')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
              .fontWeight(FontWeight.Medium)
              .fontColor($r('sys.color.ohos_id_color_text_primary'))

            Text('点击登录账号')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor($r('sys.color.ohos_id_color_text_secondary'))
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1)

          Image($r('app.media.ic_arrow_right'))
            .width(20)
            .height(20)
            .fillColor($r('sys.color.ohos_id_color_text_secondary'))
        }
        .width('100%')
        .padding(20)
        .backgroundColor($r('sys.color.ohos_id_color_foreground_contrary'))
        .borderRadius(12)
        .margin({ left: 16, right: 16, top: 16 })
        .onClick(() => {
          this.getUIContext().getRouter().pushUrl({ url: 'pages/LoginPage' });
        })

        // 功能菜单
        Column({ space: 0 }) {
          this.buildMenuItem($r('app.media.ic_download'), '我的应用')
          Divider().color($r('sys.color.ohos_id_color_list_separator')).margin({ left: 60 })
          this.buildMenuItem($r('app.media.ic_star'), '我的收藏')
          Divider().color($r('sys.color.ohos_id_color_list_separator')).margin({ left: 60 })
          this.buildMenuItem($r('app.media.ic_history'), '浏览历史')
        }
        .borderRadius(12)
        .clip(true)
        .margin({ left: 16, right: 16 })

        // 设置菜单
        Column() {
          this.buildMenuItem($r('app.media.ic_settings'), '设置')
        }
        .borderRadius(12)
        .clip(true)
        .margin({ left: 16, right: 16, bottom: 16 })
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
    .scrollBar(BarState.Off)
    .edgeEffect(EdgeEffect.Spring)
  }

  /**
   * 构建菜单项
   */
  @Builder
  private buildMenuItem(icon: Resource, title: string) {
    Row({ space: 16 }) {
      Image(icon)
        .width(24)
        .height(24)
        .fillColor($r('sys.color.ohos_id_color_text_primary'))

      Text(title)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM))
        .fontColor($r('sys.color.ohos_id_color_text_primary'))
        .layoutWeight(1)

      Image($r('app.media.ic_arrow_right'))
        .width(20)
        .height(20)
        .fillColor($r('sys.color.ohos_id_color_text_secondary'))
    }
    .width('100%')
    .height(56)
    .padding({ left: 20, right: 20 })
    .backgroundColor($r('sys.color.ohos_id_color_foreground_contrary'))
  }

  build() {
    Column() {
      Tabs({
        barPosition: BarPosition.End,
        controller: this.tabsController,
        index: this.currentTabIndex
      }) {
        // 首页
        TabContent() {
          this.HomePageContent()
        }
        .tabBar(this.TabBarBuilder('首页', $r('app.media.ic_home'), $r('app.media.ic_home'), 0))

        // 精选页
        TabContent() {
          this.FeaturedPageContent()
        }
        .tabBar(this.TabBarBuilder('精选', $r('app.media.ic_featured'), $r('app.media.ic_featured'), 1))

        // 分类页
        TabContent() {
          this.CategoryPageContent()
        }
        .tabBar(this.TabBarBuilder('分类', $r('app.media.ic_category'), $r('app.media.ic_category'), 2))

        // 我的页面
        TabContent() {
          this.ProfilePageContent()
        }
        .tabBar(this.TabBarBuilder('我的', $r('app.media.ic_profile'), $r('app.media.ic_profile'), 3))
      }
      .width('100%')
      .height('100%')
      .barMode(BarMode.Fixed)
      .barHeight(this.deviceUtils.isTablet() ? 80 : 60)
      .animationDuration(300)
      .onChange((index: number) => {
        this.currentTabIndex = index;
      })
      .backgroundColor($r('sys.color.ohos_id_color_background'))
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
  }
}