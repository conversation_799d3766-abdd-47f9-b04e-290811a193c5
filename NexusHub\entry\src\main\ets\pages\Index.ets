import { HomePage } from './HomePage';
import { SearchPage } from './SearchPage';
import { FeaturedPage } from './FeaturedPage';
import { CategoryListPage } from './CategoryListPage';
import { ProfilePage } from './ProfilePage';
import { NavigationBar, NavItem } from '../components/NavigationBar';
import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';

/**
 * 应用主入口页面
 * 包含底部导航栏和页面切换功能
 */
@Entry
@Component
struct Index {
  @State @Watch('onTabIndexChange') currentTabIndex: number = 0;
  private deviceUtils = DeviceUtils.getInstance();

  // 导航项配置
  private navItems: NavItem[] = [
    {
      icon: $r('app.media.ic_home'),
      activeIcon: $r('app.media.ic_home'),
      label: '首页',
      route: 'pages/HomePage'
    },
    {
      icon: $r('app.media.ic_featured'),
      activeIcon: $r('app.media.ic_featured'),
      label: '精选',
      route: 'pages/FeaturedPage'
    },
    {
      icon: $r('app.media.ic_category'),
      activeIcon: $r('app.media.ic_category'),
      label: '分类',
      route: 'pages/CategoryListPage'
    },
    {
      icon: $r('app.media.ic_profile'),
      activeIcon: $r('app.media.ic_profile'),
      label: '我的',
      route: 'pages/ProfilePage'
    }
  ];

  /**
   * 标签切换事件处理
   */
  private onTabChange(index: number) {
    this.currentTabIndex = index;
  }

  /**
   * 监听标签索引变化
   */
  private onTabIndexChange() {
    // 当切换到个人中心页面时，强制刷新页面状态
    if (this.currentTabIndex === 3) {
      // 通过延迟执行确保页面组件已经创建
      setTimeout(() => {
        // 这里可以添加额外的刷新逻辑
      }, 100);
    }
  }

  /**
   * 根据当前标签索引渲染对应页面
   */
  @Builder
  private CurrentPage() {
    if (this.currentTabIndex === 0) {
      HomePage()
    } else if (this.currentTabIndex === 1) {
      FeaturedPage()
    } else if (this.currentTabIndex === 2) {
      CategoryListPage()
    } else if (this.currentTabIndex === 3) {
      ProfilePage()
    }
  }

  build() {
    if (this.deviceUtils.isPhone()) {
      // 手机设备：使用底部导航栏
      Column() {
        // 主要内容区域
        Column() {
          this.CurrentPage()
        }
        .layoutWeight(1)

        // 底部导航栏
        NavigationBar({
          navItems: this.navItems,
          currentIndex: this.currentTabIndex,
          onItemClick: (index: number): void => this.onTabChange(index),
          isBottomNav: true
        })
      }
      .width('100%')
      .height('100%')
      .backgroundColor($r('sys.color.ohos_id_color_background'))
    } else {
      // 平板和2in1设备：使用左侧导航栏
      Row() {
        // 左侧导航栏
        NavigationBar({
          navItems: this.navItems,
          currentIndex: this.currentTabIndex,
          onItemClick: (index: number): void => this.onTabChange(index),
          isBottomNav: false
        })

        // 主要内容区域
        Column() {
          this.CurrentPage()
        }
        .layoutWeight(1)
        .backgroundColor($r('sys.color.ohos_id_color_background'))
      }
      .width('100%')
      .height('100%')
      .backgroundColor($r('sys.color.ohos_id_color_background'))
    }
  }
}