"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { useState } from "react";
import { Card, Table, Button, Space, Input, Modal, Form, message, Popconfirm, Tag, Switch } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from "@ant-design/icons";
import { useRequest } from "@umijs/max";
import {
  getOpenHarmonyVersions,
  createOpenHarmonyVersion,
  updateOpenHarmonyVersion,
  deleteOpenHarmonyVersion
} from "@/services/openHarmonyVersion";
const OpenHarmonyVersions = () => {
  const [searchText, setSearchText] = useState("");
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingVersion, setEditingVersion] = useState(null);
  const [form] = Form.useForm();
  const { data: versionsData, loading, refresh } = useRequest(
    () => getOpenHarmonyVersions({ keyword: searchText }),
    {
      refreshDeps: [searchText]
    }
  );
  const versions = versionsData?.data || [];
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80
    },
    {
      title: "\u7248\u672C\u540D\u79F0",
      dataIndex: "version_name",
      key: "version_name",
      render: (text, record) => /* @__PURE__ */ jsxs(Space, { children: [
        /* @__PURE__ */ jsx("span", { style: { fontWeight: "bold" }, children: text }),
        record.is_active && /* @__PURE__ */ jsx(Tag, { color: "green", children: "\u6D3B\u8DC3" })
      ] })
    },
    {
      title: "\u7248\u672C\u4EE3\u7801",
      dataIndex: "version_code",
      key: "version_code",
      width: 120
    },
    {
      title: "\u63CF\u8FF0",
      dataIndex: "description",
      key: "description",
      ellipsis: true
    },
    {
      title: "\u4F7F\u7528\u6B21\u6570",
      dataIndex: "usage_count",
      key: "usage_count",
      width: 100,
      render: (count) => /* @__PURE__ */ jsx(Tag, { color: count > 100 ? "red" : count > 50 ? "orange" : "blue", children: count })
    },
    {
      title: "\u521B\u5EFA\u65F6\u95F4",
      dataIndex: "created_at",
      key: "created_at",
      width: 180,
      render: (date) => new Date(date).toLocaleString()
    },
    {
      title: "\u64CD\u4F5C",
      key: "action",
      width: 150,
      render: (_, record) => /* @__PURE__ */ jsxs(Space, { children: [
        /* @__PURE__ */ jsx(
          Button,
          {
            type: "link",
            icon: /* @__PURE__ */ jsx(EditOutlined, {}),
            onClick: () => handleEdit(record),
            children: "\u7F16\u8F91"
          }
        ),
        /* @__PURE__ */ jsx(
          Popconfirm,
          {
            title: "\u786E\u5B9A\u8981\u5220\u9664\u8FD9\u4E2A\u7248\u672C\u5417\uFF1F",
            description: "\u5220\u9664\u540E\u65E0\u6CD5\u6062\u590D\uFF0C\u8BF7\u8C28\u614E\u64CD\u4F5C\u3002",
            onConfirm: () => handleDelete(record.id),
            okText: "\u786E\u5B9A",
            cancelText: "\u53D6\u6D88",
            children: /* @__PURE__ */ jsx(
              Button,
              {
                type: "link",
                danger: true,
                icon: /* @__PURE__ */ jsx(DeleteOutlined, {}),
                children: "\u5220\u9664"
              }
            )
          }
        )
      ] })
    }
  ];
  const handleSearch = (value) => {
    setSearchText(value);
  };
  const handleAdd = () => {
    setEditingVersion(null);
    form.resetFields();
    setIsModalVisible(true);
  };
  const handleEdit = (version) => {
    setEditingVersion(version);
    form.setFieldsValue({
      version_name: version.version_name,
      version_code: version.version_code,
      description: version.description,
      is_active: version.is_active
    });
    setIsModalVisible(true);
  };
  const handleDelete = async (id) => {
    try {
      await deleteOpenHarmonyVersion(id);
      message.success("\u5220\u9664\u6210\u529F");
      refresh();
    } catch (error) {
      message.error("\u5220\u9664\u5931\u8D25");
    }
  };
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (editingVersion) {
        await updateOpenHarmonyVersion(editingVersion.id, values);
        message.success("\u66F4\u65B0\u6210\u529F");
      } else {
        await createOpenHarmonyVersion(values);
        message.success("\u521B\u5EFA\u6210\u529F");
      }
      setIsModalVisible(false);
      refresh();
    } catch (error) {
      message.error("\u64CD\u4F5C\u5931\u8D25");
    }
  };
  return /* @__PURE__ */ jsxs("div", { children: [
    /* @__PURE__ */ jsxs(Card, { children: [
      /* @__PURE__ */ jsx("div", { style: { marginBottom: 16 }, children: /* @__PURE__ */ jsxs(Space, { children: [
        /* @__PURE__ */ jsx(
          Input.Search,
          {
            placeholder: "\u641C\u7D22\u7248\u672C\u540D\u79F0",
            allowClear: true,
            style: { width: 300 },
            onSearch: handleSearch,
            prefix: /* @__PURE__ */ jsx(SearchOutlined, {})
          }
        ),
        /* @__PURE__ */ jsx(
          Button,
          {
            type: "primary",
            icon: /* @__PURE__ */ jsx(PlusOutlined, {}),
            onClick: handleAdd,
            children: "\u65B0\u589E\u7248\u672C"
          }
        )
      ] }) }),
      /* @__PURE__ */ jsx(
        Table,
        {
          columns,
          dataSource: versions,
          rowKey: "id",
          loading,
          pagination: {
            total: versionsData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `\u5171 ${total} \u6761\u8BB0\u5F55`
          }
        }
      )
    ] }),
    /* @__PURE__ */ jsx(
      Modal,
      {
        title: editingVersion ? "\u7F16\u8F91\u7248\u672C" : "\u65B0\u589E\u7248\u672C",
        open: isModalVisible,
        onOk: handleSubmit,
        onCancel: () => setIsModalVisible(false),
        width: 600,
        children: /* @__PURE__ */ jsxs(
          Form,
          {
            form,
            layout: "vertical",
            initialValues: {
              is_active: true
            },
            children: [
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "version_name",
                  label: "\u7248\u672C\u540D\u79F0",
                  rules: [
                    { required: true, message: "\u8BF7\u8F93\u5165\u7248\u672C\u540D\u79F0" },
                    { pattern: /^\d+\.\d+\.\d+$/, message: "\u7248\u672C\u540D\u79F0\u683C\u5F0F\u5E94\u4E3A x.y.z" }
                  ],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u4F8B\u5982\uFF1A4.0.0" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "version_code",
                  label: "\u7248\u672C\u4EE3\u7801",
                  rules: [
                    { required: true, message: "\u8BF7\u8F93\u5165\u7248\u672C\u4EE3\u7801" },
                    { pattern: /^[1-9]\d*$/, message: "\u7248\u672C\u4EE3\u7801\u5FC5\u987B\u662F\u6B63\u6574\u6570" }
                  ],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u4F8B\u5982\uFF1A40000" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "description",
                  label: "\u63CF\u8FF0",
                  children: /* @__PURE__ */ jsx(
                    Input.TextArea,
                    {
                      rows: 3,
                      placeholder: "\u8BF7\u8F93\u5165\u7248\u672C\u63CF\u8FF0",
                      maxLength: 500,
                      showCount: true
                    }
                  )
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "is_active",
                  label: "\u72B6\u6001",
                  valuePropName: "checked",
                  children: /* @__PURE__ */ jsx(Switch, { checkedChildren: "\u6FC0\u6D3B", unCheckedChildren: "\u7981\u7528" })
                }
              )
            ]
          }
        )
      }
    )
  ] });
};
export default OpenHarmonyVersions;
