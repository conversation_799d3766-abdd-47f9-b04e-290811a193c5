"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { Footer } from "@/components";
import { sendEmailVerifyCode as getFake<PERSON><PERSON>tcha } from "@/services/user";
import {
  LockOutlined,
  MailOutlined,
  UserOutlined,
  G<PERSON><PERSON>Outlined,
  WechatOutlined,
  QqOutlined
} from "@ant-design/icons";
import {
  LoginForm,
  ProFormCaptcha,
  ProFormCheckbox,
  ProFormText
} from "@ant-design/pro-components";
import { FormattedMessage, Helmet, SelectLang, useIntl, useModel, history } from "@umijs/max";
import { Alert, message, Tabs } from "antd";
import { createStyles } from "antd-style";
import { useState, useEffect } from "react";
import { flushSync } from "react-dom";
import Settings from "../../../../config/defaultSettings";
import { login as userLogin } from "@/services/user";
import HybridLogin from "@/components/HybridLogin";
import { useLogtoAuth } from "@/hooks/useLogtoAuth";
const useStyles = createStyles(({ token }) => {
  return {
    action: {
      marginLeft: "8px",
      color: "rgba(0, 0, 0, 0.2)",
      fontSize: "24px",
      verticalAlign: "middle",
      cursor: "pointer",
      transition: "color 0.3s",
      "&:hover": {
        color: token.colorPrimaryActive
      }
    },
    lang: {
      width: 42,
      height: 42,
      lineHeight: "42px",
      position: "fixed",
      right: 16,
      borderRadius: token.borderRadius,
      ":hover": {
        backgroundColor: token.colorBgTextHover
      }
    },
    container: {
      display: "flex",
      flexDirection: "column",
      height: "100vh",
      overflow: "auto",
      backgroundImage: "url('https://images.unsplash.com/photo-1579548122080-c35fd6820ecb?q=80&w=2070&auto=format&fit=crop')",
      backgroundSize: "cover",
      backgroundPosition: "center"
    },
    content: {
      flex: "1",
      padding: "32px 0",
      display: "flex",
      justifyContent: "center",
      alignItems: "center"
    },
    form: {
      padding: "32px 24px",
      backgroundColor: "rgba(255, 255, 255, 0.9)",
      borderRadius: "8px",
      boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)"
    },
    header: {
      display: "flex",
      alignItems: "center",
      marginBottom: "24px"
    },
    logo: {
      height: "44px",
      marginRight: "16px"
    },
    title: {
      fontSize: "33px",
      fontWeight: "bold",
      color: token.colorPrimary
    },
    desc: {
      fontSize: "14px",
      color: token.colorTextSecondary,
      marginTop: "12px"
    }
  };
});
const ActionIcons = () => {
  const { styles } = useStyles();
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(GithubOutlined, { className: styles.action, onClick: () => message.info("\u6682\u4E0D\u652F\u6301\u7B2C\u4E09\u65B9\u8D26\u53F7\u767B\u5F55") }, "GithubOutlined"),
    /* @__PURE__ */ jsx(WechatOutlined, { className: styles.action, onClick: () => message.info("\u6682\u4E0D\u652F\u6301\u7B2C\u4E09\u65B9\u8D26\u53F7\u767B\u5F55") }, "WechatOutlined"),
    /* @__PURE__ */ jsx(QqOutlined, { className: styles.action, onClick: () => message.info("\u6682\u4E0D\u652F\u6301\u7B2C\u4E09\u65B9\u8D26\u53F7\u767B\u5F55") }, "QqOutlined")
  ] });
};
const Lang = () => {
  const { styles } = useStyles();
  return /* @__PURE__ */ jsx("div", { className: styles.lang, "data-lang": true, children: SelectLang && /* @__PURE__ */ jsx(SelectLang, {}) });
};
const LoginMessage = ({ content }) => {
  return /* @__PURE__ */ jsx(
    Alert,
    {
      style: {
        marginBottom: 24
      },
      message: content,
      type: "error",
      showIcon: true
    }
  );
};
const Login = () => {
  const [userLoginState, setUserLoginState] = useState({});
  const [type, setType] = useState("account");
  const { initialState, setInitialState } = useModel("@@initialState");
  const { styles } = useStyles();
  const intl = useIntl();
  const logtoAuth = initialState?.useLogto ? useLogtoAuth() : { isAuthenticated: false, isLoading: false };
  const { isAuthenticated, isLoading: logtoLoading } = logtoAuth;
  useEffect(() => {
    if (initialState?.useLogto && isAuthenticated && !logtoLoading) {
      const urlParams = new URL(window.location.href).searchParams;
      history.push(urlParams.get("redirect") || "/");
    }
  }, [isAuthenticated, logtoLoading, initialState?.useLogto]);
  const fetchUserInfo = async () => {
    const userInfo = await initialState?.fetchUserInfo?.();
    if (userInfo) {
      flushSync(() => {
        setInitialState((s) => ({
          ...s,
          currentUser: userInfo
        }));
      });
    }
  };
  const handleSubmit = async (values) => {
    try {
      const loginParams = {
        username_or_email: values.username,
        password: values.password
      };
      const response = await userLogin(loginParams);
      console.log("\u767B\u5F55\u54CD\u5E94:", response);
      if (response) {
        const token = response.data?.token || response.token;
        console.log("\u5904\u7406\u540E\u7684token:", token);
        if (token) {
          localStorage.setItem("token", token);
          const defaultLoginSuccessMessage = "\u767B\u5F55\u6210\u529F\uFF01";
          message.success(defaultLoginSuccessMessage);
          await fetchUserInfo();
          const urlParams = new URL(window.location.href).searchParams;
          history.push(urlParams.get("redirect") || "/");
          return;
        } else {
          message.error("\u767B\u5F55\u54CD\u5E94\u4E2D\u672A\u5305\u542Btoken");
        }
      }
    } catch (error) {
      console.log(error);
      message.error("\u767B\u5F55\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5\uFF01");
    }
  };
  const { status, type: loginType } = userLoginState;
  return /* @__PURE__ */ jsxs("div", { className: styles.container, children: [
    /* @__PURE__ */ jsx(Helmet, { children: /* @__PURE__ */ jsxs("title", { children: [
      intl.formatMessage({
        id: "menu.login",
        defaultMessage: "\u767B\u5F55\u9875"
      }),
      "- ",
      Settings.title
    ] }) }),
    /* @__PURE__ */ jsx(Lang, {}),
    /* @__PURE__ */ jsx("div", { className: styles.content, children: /* @__PURE__ */ jsxs("div", { className: styles.form, children: [
      /* @__PURE__ */ jsxs("div", { className: styles.header, children: [
        /* @__PURE__ */ jsx("img", { alt: "logo", className: styles.logo, src: "/logo.svg" }),
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("h1", { className: styles.title, children: "NexusHub" }),
          /* @__PURE__ */ jsx("p", { className: styles.desc, children: /* @__PURE__ */ jsx(FormattedMessage, { id: "pages.login.appDesc", defaultMessage: "\u4E00\u7AD9\u5F0F\u5E94\u7528\u7BA1\u7406\u4E0E\u5206\u53D1\u5E73\u53F0" }) })
        ] })
      ] }),
      /* @__PURE__ */ jsxs(
        LoginForm,
        {
          contentStyle: {
            minWidth: 280,
            maxWidth: "75vw"
          },
          initialValues: {
            autoLogin: true
          },
          actions: [
            /* @__PURE__ */ jsx(
              FormattedMessage,
              {
                id: "pages.login.loginWith",
                defaultMessage: "\u5176\u4ED6\u767B\u5F55\u65B9\u5F0F"
              },
              "loginWith"
            ),
            /* @__PURE__ */ jsx(ActionIcons, {}, "icons")
          ],
          onFinish: async (values) => {
            await handleSubmit(values);
          },
          children: [
            /* @__PURE__ */ jsx(
              Tabs,
              {
                activeKey: type,
                onChange: setType,
                centered: true,
                items: [
                  {
                    key: "account",
                    label: intl.formatMessage({
                      id: "pages.login.accountLogin.tab",
                      defaultMessage: "\u8D26\u6237\u5BC6\u7801\u767B\u5F55"
                    })
                  },
                  {
                    key: "email",
                    label: intl.formatMessage({
                      id: "pages.login.emailLogin.tab",
                      defaultMessage: "\u90AE\u7BB1\u767B\u5F55"
                    })
                  }
                ]
              }
            ),
            status === "error" && loginType === "account" && /* @__PURE__ */ jsx(
              LoginMessage,
              {
                content: intl.formatMessage({
                  id: "pages.login.accountLogin.errorMessage",
                  defaultMessage: "\u8D26\u6237\u6216\u5BC6\u7801\u9519\u8BEF(admin/ant.design)"
                })
              }
            ),
            type === "account" && /* @__PURE__ */ jsxs(Fragment, { children: [
              /* @__PURE__ */ jsx(
                ProFormText,
                {
                  name: "username",
                  fieldProps: {
                    size: "large",
                    prefix: /* @__PURE__ */ jsx(UserOutlined, { className: "prefixIcon" })
                  },
                  placeholder: intl.formatMessage({
                    id: "pages.login.username.placeholder",
                    defaultMessage: "\u7528\u6237\u540D: admin \u6216 user"
                  }),
                  rules: [
                    {
                      required: true,
                      message: /* @__PURE__ */ jsx(
                        FormattedMessage,
                        {
                          id: "pages.login.username.required",
                          defaultMessage: "\u8BF7\u8F93\u5165\u7528\u6237\u540D!"
                        }
                      )
                    }
                  ]
                }
              ),
              /* @__PURE__ */ jsx(
                ProFormText.Password,
                {
                  name: "password",
                  fieldProps: {
                    size: "large",
                    prefix: /* @__PURE__ */ jsx(LockOutlined, { className: "prefixIcon" })
                  },
                  placeholder: intl.formatMessage({
                    id: "pages.login.password.placeholder",
                    defaultMessage: "\u5BC6\u7801: ant.design"
                  }),
                  rules: [
                    {
                      required: true,
                      message: /* @__PURE__ */ jsx(
                        FormattedMessage,
                        {
                          id: "pages.login.password.required",
                          defaultMessage: "\u8BF7\u8F93\u5165\u5BC6\u7801\uFF01"
                        }
                      )
                    }
                  ]
                }
              )
            ] }),
            status === "error" && loginType === "email" && /* @__PURE__ */ jsx(
              LoginMessage,
              {
                content: intl.formatMessage({
                  id: "pages.login.phoneLogin.errorMessage",
                  defaultMessage: "\u9A8C\u8BC1\u7801\u9519\u8BEF"
                })
              }
            ),
            type === "email" && /* @__PURE__ */ jsxs(Fragment, { children: [
              /* @__PURE__ */ jsx(
                ProFormText,
                {
                  fieldProps: {
                    size: "large",
                    prefix: /* @__PURE__ */ jsx(MailOutlined, { className: "prefixIcon" })
                  },
                  name: "email",
                  placeholder: intl.formatMessage({
                    id: "pages.login.email.placeholder",
                    defaultMessage: "\u90AE\u7BB1\u5730\u5740"
                  }),
                  rules: [
                    {
                      required: true,
                      message: /* @__PURE__ */ jsx(
                        FormattedMessage,
                        {
                          id: "pages.login.email.required",
                          defaultMessage: "\u8BF7\u8F93\u5165\u90AE\u7BB1\u5730\u5740\uFF01"
                        }
                      )
                    },
                    {
                      type: "email",
                      message: /* @__PURE__ */ jsx(
                        FormattedMessage,
                        {
                          id: "pages.login.email.invalid",
                          defaultMessage: "\u90AE\u7BB1\u5730\u5740\u683C\u5F0F\u9519\u8BEF\uFF01"
                        }
                      )
                    }
                  ]
                }
              ),
              /* @__PURE__ */ jsx(
                ProFormCaptcha,
                {
                  fieldProps: {
                    size: "large",
                    prefix: /* @__PURE__ */ jsx(LockOutlined, { className: "prefixIcon" })
                  },
                  captchaProps: {
                    size: "large"
                  },
                  placeholder: intl.formatMessage({
                    id: "pages.login.captcha.placeholder",
                    defaultMessage: "\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801"
                  }),
                  captchaTextRender: (timing, count) => {
                    if (timing) {
                      return `${count} ${intl.formatMessage({
                        id: "pages.getCaptchaSecondText",
                        defaultMessage: "\u79D2\u540E\u91CD\u65B0\u83B7\u53D6"
                      })}`;
                    }
                    return intl.formatMessage({
                      id: "pages.login.emailLogin.getVerificationCode",
                      defaultMessage: "\u83B7\u53D6\u9A8C\u8BC1\u7801"
                    });
                  },
                  name: "captcha",
                  rules: [
                    {
                      required: true,
                      message: /* @__PURE__ */ jsx(
                        FormattedMessage,
                        {
                          id: "pages.login.captcha.required",
                          defaultMessage: "\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801\uFF01"
                        }
                      )
                    }
                  ],
                  onGetCaptcha: async (email) => {
                    const result = await getFakeCaptcha({
                      email
                    });
                    if (!result) {
                      return;
                    }
                    message.success("\u83B7\u53D6\u9A8C\u8BC1\u7801\u6210\u529F\uFF01\u9A8C\u8BC1\u7801\u4E3A\uFF1A1234");
                  }
                }
              )
            ] }),
            /* @__PURE__ */ jsxs(
              "div",
              {
                style: {
                  marginBottom: 24
                },
                children: [
                  /* @__PURE__ */ jsx(ProFormCheckbox, { noStyle: true, name: "autoLogin", children: /* @__PURE__ */ jsx(FormattedMessage, { id: "pages.login.rememberMe", defaultMessage: "\u81EA\u52A8\u767B\u5F55" }) }),
                  /* @__PURE__ */ jsxs("div", { style: { float: "right" }, children: [
                    /* @__PURE__ */ jsx("a", { style: { marginRight: 12 }, children: /* @__PURE__ */ jsx(FormattedMessage, { id: "pages.login.forgotPassword", defaultMessage: "\u5FD8\u8BB0\u5BC6\u7801" }) }),
                    /* @__PURE__ */ jsx("a", { href: "/user/register", children: /* @__PURE__ */ jsx(FormattedMessage, { id: "pages.login.registerAccount", defaultMessage: "\u6CE8\u518C\u8D26\u53F7" }) })
                  ] })
                ]
              }
            )
          ]
        }
      ),
      /* @__PURE__ */ jsx(HybridLogin, {})
    ] }) }),
    /* @__PURE__ */ jsx(Footer, {})
  ] });
};
export default Login;
