"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { Card } from "antd";
import classNames from "classnames";
import useStyles from "./index.style";
const ChartCard = (props) => {
  const { styles } = useStyles();
  const renderTotal = (total2) => {
    if (!total2 && total2 !== 0) {
      return null;
    }
    let totalDom;
    switch (typeof total2) {
      case "undefined":
        totalDom = null;
        break;
      case "function":
        totalDom = /* @__PURE__ */ jsx("div", { className: styles.total, children: total2() });
        break;
      default:
        totalDom = /* @__PURE__ */ jsx("div", { className: styles.total, children: total2 });
    }
    return totalDom;
  };
  const renderContent = () => {
    const { contentHeight: contentHeight2, title: title2, avatar: avatar2, action: action2, total: total2, footer: footer2, children: children2, loading: loading2 } = props;
    if (loading2) {
      return false;
    }
    return /* @__PURE__ */ jsxs("div", { className: styles.chartCard, children: [
      /* @__PURE__ */ jsxs(
        "div",
        {
          className: classNames(styles.chartTop, {
            [styles.chartTopMargin]: !children2 && !footer2
          }),
          children: [
            /* @__PURE__ */ jsx("div", { className: styles.avatar, children: avatar2 }),
            /* @__PURE__ */ jsxs("div", { className: styles.metaWrap, children: [
              /* @__PURE__ */ jsxs("div", { className: styles.meta, children: [
                /* @__PURE__ */ jsx("span", { children: title2 }),
                /* @__PURE__ */ jsx("span", { className: styles.action, children: action2 })
              ] }),
              renderTotal(total2)
            ] })
          ]
        }
      ),
      children2 && /* @__PURE__ */ jsx(
        "div",
        {
          className: styles.content,
          style: {
            height: contentHeight2 || "auto"
          },
          children: /* @__PURE__ */ jsx("div", { className: contentHeight2 && styles.contentFixed, children: children2 })
        }
      ),
      footer2 && /* @__PURE__ */ jsx(
        "div",
        {
          className: classNames(styles.footer, {
            [styles.footerMargin]: !children2
          }),
          children: footer2
        }
      )
    ] });
  };
  const {
    loading = false,
    contentHeight,
    title,
    avatar,
    action,
    total,
    footer,
    children,
    ...rest
  } = props;
  return /* @__PURE__ */ jsx(
    Card,
    {
      loading,
      bodyStyle: {
        padding: "20px 24px 8px 24px"
      },
      ...rest,
      children: renderContent()
    }
  );
};
export default ChartCard;
