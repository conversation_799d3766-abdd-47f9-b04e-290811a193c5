if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface SearchPage_Params {
    searchKeyword?: string;
    searchResults?: AppModel[];
    categories?: CategoryModel[];
    hotKeywords?: string[];
    searchHistory?: string[];
    loadingState?: LoadingState;
    isSearching?: boolean;
    showSuggestions?: boolean;
    currentPage?: number;
    hasMore?: boolean;
    isLoadingMore?: boolean;
    selectedCategory?: string;
    sortBy?: string;
    showFilter?: boolean;
    deviceUtils?;
    apiService?;
    searchParams?: AppSearchParams;
}
import type { AppModel, AppListResponse, AppSearchParams, AppListData } from '../models/App';
import type { CategoryModel, CategoryListResponse } from '../models/Category';
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { AppCard } from "@normalized:N&&&entry/src/main/ets/components/AppCard&";
import { SearchBar, SearchSuggestions } from "@normalized:N&&&entry/src/main/ets/components/SearchBar&";
import { LoadingView, LoadingState, LoadMoreView } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import { LengthMetrics } from "@ohos:arkui.node";
import preferences from "@ohos:data.preferences";
import hilog from "@ohos:hilog";
interface SearchPageParams {
    keyword?: string;
}
interface FilterOptions {
    category?: string;
    minRating?: number;
    isFree?: boolean;
}
interface SortOption {
    key: string;
    label: string;
}
class SearchPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__searchKeyword = new ObservedPropertySimplePU('', this, "searchKeyword");
        this.__searchResults = new ObservedPropertyObjectPU([], this, "searchResults");
        this.__categories = new ObservedPropertyObjectPU([], this, "categories");
        this.__hotKeywords = new ObservedPropertyObjectPU([], this, "hotKeywords");
        this.__searchHistory = new ObservedPropertyObjectPU([], this, "searchHistory");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.SUCCESS, this, "loadingState");
        this.__isSearching = new ObservedPropertySimplePU(false, this, "isSearching");
        this.__showSuggestions = new ObservedPropertySimplePU(true, this, "showSuggestions");
        this.__currentPage = new ObservedPropertySimplePU(1, this, "currentPage");
        this.__hasMore = new ObservedPropertySimplePU(true, this, "hasMore");
        this.__isLoadingMore = new ObservedPropertySimplePU(false, this, "isLoadingMore");
        this.__selectedCategory = new ObservedPropertySimplePU('', this, "selectedCategory");
        this.__sortBy = new ObservedPropertySimplePU('relevance', this, "sortBy");
        this.__showFilter = new ObservedPropertySimplePU(false, this, "showFilter");
        this.deviceUtils = DeviceUtils.getInstance();
        this.apiService = ApiService.getInstance();
        this.searchParams = {
            page: 1,
            page_size: 20
        };
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: SearchPage_Params) {
        if (params.searchKeyword !== undefined) {
            this.searchKeyword = params.searchKeyword;
        }
        if (params.searchResults !== undefined) {
            this.searchResults = params.searchResults;
        }
        if (params.categories !== undefined) {
            this.categories = params.categories;
        }
        if (params.hotKeywords !== undefined) {
            this.hotKeywords = params.hotKeywords;
        }
        if (params.searchHistory !== undefined) {
            this.searchHistory = params.searchHistory;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.isSearching !== undefined) {
            this.isSearching = params.isSearching;
        }
        if (params.showSuggestions !== undefined) {
            this.showSuggestions = params.showSuggestions;
        }
        if (params.currentPage !== undefined) {
            this.currentPage = params.currentPage;
        }
        if (params.hasMore !== undefined) {
            this.hasMore = params.hasMore;
        }
        if (params.isLoadingMore !== undefined) {
            this.isLoadingMore = params.isLoadingMore;
        }
        if (params.selectedCategory !== undefined) {
            this.selectedCategory = params.selectedCategory;
        }
        if (params.sortBy !== undefined) {
            this.sortBy = params.sortBy;
        }
        if (params.showFilter !== undefined) {
            this.showFilter = params.showFilter;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
        if (params.searchParams !== undefined) {
            this.searchParams = params.searchParams;
        }
    }
    updateStateVars(params: SearchPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__searchKeyword.purgeDependencyOnElmtId(rmElmtId);
        this.__searchResults.purgeDependencyOnElmtId(rmElmtId);
        this.__categories.purgeDependencyOnElmtId(rmElmtId);
        this.__hotKeywords.purgeDependencyOnElmtId(rmElmtId);
        this.__searchHistory.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__isSearching.purgeDependencyOnElmtId(rmElmtId);
        this.__showSuggestions.purgeDependencyOnElmtId(rmElmtId);
        this.__currentPage.purgeDependencyOnElmtId(rmElmtId);
        this.__hasMore.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoadingMore.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCategory.purgeDependencyOnElmtId(rmElmtId);
        this.__sortBy.purgeDependencyOnElmtId(rmElmtId);
        this.__showFilter.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__searchKeyword.aboutToBeDeleted();
        this.__searchResults.aboutToBeDeleted();
        this.__categories.aboutToBeDeleted();
        this.__hotKeywords.aboutToBeDeleted();
        this.__searchHistory.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__isSearching.aboutToBeDeleted();
        this.__showSuggestions.aboutToBeDeleted();
        this.__currentPage.aboutToBeDeleted();
        this.__hasMore.aboutToBeDeleted();
        this.__isLoadingMore.aboutToBeDeleted();
        this.__selectedCategory.aboutToBeDeleted();
        this.__sortBy.aboutToBeDeleted();
        this.__showFilter.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __searchKeyword: ObservedPropertySimplePU<string>;
    get searchKeyword() {
        return this.__searchKeyword.get();
    }
    set searchKeyword(newValue: string) {
        this.__searchKeyword.set(newValue);
    }
    private __searchResults: ObservedPropertyObjectPU<AppModel[]>;
    get searchResults() {
        return this.__searchResults.get();
    }
    set searchResults(newValue: AppModel[]) {
        this.__searchResults.set(newValue);
    }
    private __categories: ObservedPropertyObjectPU<CategoryModel[]>;
    get categories() {
        return this.__categories.get();
    }
    set categories(newValue: CategoryModel[]) {
        this.__categories.set(newValue);
    }
    private __hotKeywords: ObservedPropertyObjectPU<string[]>;
    get hotKeywords() {
        return this.__hotKeywords.get();
    }
    set hotKeywords(newValue: string[]) {
        this.__hotKeywords.set(newValue);
    }
    private __searchHistory: ObservedPropertyObjectPU<string[]>;
    get searchHistory() {
        return this.__searchHistory.get();
    }
    set searchHistory(newValue: string[]) {
        this.__searchHistory.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __isSearching: ObservedPropertySimplePU<boolean>;
    get isSearching() {
        return this.__isSearching.get();
    }
    set isSearching(newValue: boolean) {
        this.__isSearching.set(newValue);
    }
    private __showSuggestions: ObservedPropertySimplePU<boolean>;
    get showSuggestions() {
        return this.__showSuggestions.get();
    }
    set showSuggestions(newValue: boolean) {
        this.__showSuggestions.set(newValue);
    }
    private __currentPage: ObservedPropertySimplePU<number>;
    get currentPage() {
        return this.__currentPage.get();
    }
    set currentPage(newValue: number) {
        this.__currentPage.set(newValue);
    }
    private __hasMore: ObservedPropertySimplePU<boolean>;
    get hasMore() {
        return this.__hasMore.get();
    }
    set hasMore(newValue: boolean) {
        this.__hasMore.set(newValue);
    }
    private __isLoadingMore: ObservedPropertySimplePU<boolean>;
    get isLoadingMore() {
        return this.__isLoadingMore.get();
    }
    set isLoadingMore(newValue: boolean) {
        this.__isLoadingMore.set(newValue);
    }
    private __selectedCategory: ObservedPropertySimplePU<string>;
    get selectedCategory() {
        return this.__selectedCategory.get();
    }
    set selectedCategory(newValue: string) {
        this.__selectedCategory.set(newValue);
    }
    private __sortBy: ObservedPropertySimplePU<string>;
    get sortBy() {
        return this.__sortBy.get();
    }
    set sortBy(newValue: string) {
        this.__sortBy.set(newValue);
    }
    private __showFilter: ObservedPropertySimplePU<boolean>;
    get showFilter() {
        return this.__showFilter.get();
    }
    set showFilter(newValue: boolean) {
        this.__showFilter.set(newValue);
    }
    private deviceUtils;
    private apiService;
    private searchParams: AppSearchParams;
    aboutToAppear(): void {
        const params = this.getUIContext().getRouter().getParams() as SearchPageParams;
        if (params?.keyword) {
            this.searchKeyword = params.keyword;
            this.performSearch(params.keyword);
        }
        else {
            this.loadInitialData();
        }
        this.loadSearchHistory();
    }
    /**
  /**
     * 检查并设置认证token
     */
    private async checkAndSetAuthToken(): Promise<void> {
        try {
            const context = this.getUIContext().getHostContext();
            const options: preferences.Options = { name: 'user_data' };
            const dataPreferences = preferences.getPreferencesSync(context, options);
            const token = dataPreferences.getSync('token', '') as string;
            if (token) {
                this.apiService.setAuthToken(token);
            }
        }
        catch (error) {
            hilog.error(0x0000, 'SearchPage', '检查登录状态失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 加载初始数据
     */
    private async loadInitialData() {
        try {
            // 检查登录状态并设置token
            await this.checkAndSetAuthToken();
            const responses: [
                CategoryListResponse,
                Record<string, Object>
            ] = await Promise.all([
                this.apiService.getAppCategories(),
                this.apiService.getHotKeywords()
            ]);
            const categoriesResponse: CategoryListResponse = responses[0];
            const hotKeywordsResponse: Record<string, Object> = responses[1];
            if (categoriesResponse.code === 200 && categoriesResponse.data) {
                this.categories = categoriesResponse.data;
            }
            if (hotKeywordsResponse.code === 200 && hotKeywordsResponse.data) {
                this.hotKeywords = hotKeywordsResponse.data as string[];
            }
        }
        catch (error) {
            hilog.error(0x0000, 'SearchPage', '加载初始数据失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 执行搜索
     */
    private async performSearch(keyword: string, loadMore: boolean = false) {
        if (!keyword.trim()) {
            this.showSuggestions = true;
            return;
        }
        try {
            this.showSuggestions = false;
            if (!loadMore) {
                this.isSearching = true;
                this.currentPage = 1;
                this.searchResults = [];
            }
            else {
                this.isLoadingMore = true;
            }
            const page = loadMore ? this.currentPage + 1 : 1;
            // 优先使用公开搜索接口，无需登录
            let response: AppListResponse;
            try {
                response = await this.apiService.searchApps(keyword.trim(), page, 20);
            }
            catch (publicError) {
                // 如果公开接口失败，尝试使用需要认证的接口
                hilog.warn(0x0000, 'SearchPage', '公开搜索接口失败，尝试认证接口: %{public}s', JSON.stringify(publicError));
                await this.checkAndSetAuthToken();
                this.searchParams = {
                    keyword: keyword.trim(),
                    category: this.selectedCategory || undefined,
                    sort: this.sortBy,
                    page: page,
                    page_size: 20
                };
                response = await this.apiService.searchApps(keyword.trim(), page, 20);
            }
            if (response.code === 200 && response.data) {
                const responseData = response.data as AppListData;
                if (loadMore) {
                    this.searchResults = this.searchResults.concat(responseData.list);
                    this.currentPage++;
                }
                else {
                    this.searchResults = responseData.list;
                    this.currentPage = 1;
                }
                this.hasMore = responseData.pagination?.hasNext ?? false;
                if (!this.searchResults || this.searchResults.length === 0) {
                    this.loadingState = LoadingState.EMPTY;
                }
                else {
                    this.loadingState = LoadingState.SUCCESS;
                }
                // 保存搜索历史（仅在用户已登录时）
                if (!loadMore) {
                    this.saveSearchHistory(keyword.trim());
                }
            }
            else {
                this.loadingState = LoadingState.ERROR;
            }
        }
        catch (error) {
            hilog.error(0x0000, 'SearchPage', '搜索失败: %{public}s', JSON.stringify(error));
            this.loadingState = LoadingState.ERROR;
        }
        finally {
            this.isSearching = false;
            this.isLoadingMore = false;
        }
    }
    /**
     * 加载搜索历史
     */
    private async loadSearchHistory() {
        try {
            const context = this.getUIContext().getHostContext();
            const options: preferences.Options = { name: 'search_history' };
            const dataPreferences = preferences.getPreferencesSync(context, options);
            const history = dataPreferences.getSync('keywords', '[]') as string;
            this.searchHistory = JSON.parse(history);
        }
        catch (error) {
            hilog.error(0x0000, 'SearchPage', '加载搜索历史失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 保存搜索历史
     */
    private async saveSearchHistory(keyword: string) {
        try {
            // 移除重复项并添加到开头
            const newHistory = [keyword, ...this.searchHistory.filter(item => item !== keyword)];
            // 限制历史记录数量
            this.searchHistory = newHistory.slice(0, 10);
            const context = this.getUIContext().getHostContext();
            const options: preferences.Options = { name: 'search_history' };
            const dataPreferences = preferences.getPreferencesSync(context, options);
            dataPreferences.putSync('keywords', JSON.stringify(this.searchHistory));
            dataPreferences.flush();
        }
        catch (error) {
            hilog.error(0x0000, 'SearchPage', '保存搜索历史失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 清除搜索历史
     */
    private async clearSearchHistory() {
        try {
            this.searchHistory = [];
            const context = this.getUIContext().getHostContext();
            const options: preferences.Options = { name: 'search_history' };
            const dataPreferences = preferences.getPreferencesSync(context, options);
            dataPreferences.deleteSync('keywords');
            dataPreferences.flush();
        }
        catch (error) {
            hilog.error(0x0000, 'SearchPage', '清除搜索历史失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 跳转到应用详情
     */
    private navigateToAppDetail(appId: string) {
        this.getUIContext().getRouter().pushUrl({
            url: 'pages/AppDetailPage',
            params: { appId }
        });
    }
    /**
     * 搜索建议
     */
    private SearchSuggestionsView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.showSuggestions) {
                this.ifElseBranchUpdateFunction(0, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new SearchSuggestions(this, {
                                    hotKeywords: this.hotKeywords,
                                    searchHistory: this.searchHistory,
                                    onSuggestionClick: (keyword: string) => {
                                        this.searchKeyword = keyword;
                                        this.performSearch(keyword);
                                    },
                                    onClearHistory: () => {
                                        this.clearSearchHistory();
                                    }
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/SearchPage.ets", line: 257, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        hotKeywords: this.hotKeywords,
                                        searchHistory: this.searchHistory,
                                        onSuggestionClick: (keyword: string) => {
                                            this.searchKeyword = keyword;
                                            this.performSearch(keyword);
                                        },
                                        onClearHistory: () => {
                                            this.clearSearchHistory();
                                        }
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    hotKeywords: this.hotKeywords,
                                    searchHistory: this.searchHistory
                                });
                            }
                        }, { name: "SearchSuggestions" });
                    }
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    /**
     * 筛选器
     */
    private FilterView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.showFilter) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) });
                        Context.animation({ duration: 300, curve: Curve.EaseInOut });
                        Column.width('100%');
                        Column.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
                        Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Column.borderRadius({ topLeft: Constants.BORDER_RADIUS.LARGE, topRight: Constants.BORDER_RADIUS.LARGE });
                        Context.animation(null);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 分类筛选
                        Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('分类');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontWeight(FontWeight.Medium);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Flex.create({ wrap: FlexWrap.Wrap, space: { main: LengthMetrics.vp(8), cross: LengthMetrics.vp(8) } });
                    }, Flex);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 全部分类
                        Text.create('全部');
                        // 全部分类
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                        // 全部分类
                        Text.fontColor(this.selectedCategory === '' ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_PRIMARY);
                        // 全部分类
                        Text.backgroundColor(this.selectedCategory === '' ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT);
                        // 全部分类
                        Text.padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' });
                        // 全部分类
                        Text.borderRadius(Constants.BORDER_RADIUS.SMALL);
                        // 全部分类
                        Text.onClick(() => {
                            this.selectedCategory = '';
                            if (this.searchKeyword.trim()) {
                                this.performSearch(this.searchKeyword);
                            }
                        });
                    }, Text);
                    // 全部分类
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const category = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(category.name);
                                Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                                Text.fontColor(this.selectedCategory === category.id.toString() ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_PRIMARY);
                                Text.backgroundColor(this.selectedCategory === category.id.toString() ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT);
                                Text.padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' });
                                Text.borderRadius(Constants.BORDER_RADIUS.SMALL);
                                Text.onClick(() => {
                                    this.selectedCategory = category.id.toString();
                                    if (this.searchKeyword.trim()) {
                                        this.performSearch(this.searchKeyword);
                                    }
                                });
                            }, Text);
                            Text.pop();
                        };
                        this.forEachUpdateFunction(elmtId, this.categories, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    Flex.pop();
                    // 分类筛选
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Divider.create();
                        Divider.color(Constants.COLORS.BORDER);
                    }, Divider);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 排序方式
                        Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('排序');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontWeight(FontWeight.Medium);
                        Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create({ space: 12 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const sort = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(sort.label);
                                Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                                Text.fontColor(this.sortBy === sort.key ? Constants.COLORS.WHITE : Constants.COLORS.TEXT_PRIMARY);
                                Text.backgroundColor(this.sortBy === sort.key ? Constants.COLORS.PRIMARY : Constants.COLORS.BACKGROUND_LIGHT);
                                Text.padding({ left: '12vp', right: '12vp', top: '6vp', bottom: '6vp' });
                                Text.borderRadius(Constants.BORDER_RADIUS.SMALL);
                                Text.onClick(() => {
                                    this.sortBy = sort.key;
                                    if (this.searchKeyword.trim()) {
                                        this.performSearch(this.searchKeyword);
                                    }
                                });
                            }, Text);
                            Text.pop();
                        };
                        this.forEachUpdateFunction(elmtId, [
                            { key: 'relevance', label: '相关度' },
                            { key: 'downloadCount', label: '下载量' },
                            { key: 'rating', label: '评分' },
                            { key: 'updatedAt', label: '更新时间' }
                        ], forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    Row.pop();
                    // 排序方式
                    Column.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    /**
     * 搜索结果
     */
    private SearchResults(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.loadingState === LoadingState.LOADING || this.isSearching) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, { state: LoadingState.LOADING, message: '搜索中...' }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/SearchPage.ets", line: 365, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.LOADING,
                                        message: '搜索中...'
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.LOADING, message: '搜索中...'
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.ERROR,
                                    message: '搜索失败，请重试',
                                    onRetry: (): Promise<void> => this.performSearch(this.searchKeyword)
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/SearchPage.ets", line: 368, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.ERROR,
                                        message: '搜索失败，请重试',
                                        onRetry: (): Promise<void> => this.performSearch(this.searchKeyword)
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.ERROR,
                                    message: '搜索失败，请重试'
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.EMPTY) {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.EMPTY,
                                    message: '未找到相关应用'
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/SearchPage.ets", line: 375, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.EMPTY,
                                        message: '未找到相关应用'
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.EMPTY,
                                    message: '未找到相关应用'
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(3, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 搜索结果统计
                        if (this.searchResults && this.searchResults.length > 0) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create(`找到 ${this.searchResults.length} 个相关应用`);
                                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                                    Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                                    Text.alignSelf(ItemAlign.Start);
                                    Text.margin({ left: 16, right: 16, top: 8, bottom: 8 });
                                }, Text);
                                Text.pop();
                            });
                        }
                        // 应用列表
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 应用列表
                        List.create({ space: 8 });
                        // 应用列表
                        List.layoutWeight(1);
                        // 应用列表
                        List.scrollBar(BarState.Auto);
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const app = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        __Common__.create();
                                        __Common__.margin({ left: 16, right: 16 });
                                        __Common__.onClick(() => this.navigateToAppDetail(app.id.toString()));
                                    }, __Common__);
                                    {
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            if (isInitialRender) {
                                                let componentCall = new AppCard(this, {
                                                    app: app,
                                                    cardType: this.deviceUtils.isTablet() ? 'grid' : 'list',
                                                    showDownloadButton: true
                                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/SearchPage.ets", line: 395, col: 15 });
                                                ViewPU.create(componentCall);
                                                let paramsLambda = () => {
                                                    return {
                                                        app: app,
                                                        cardType: this.deviceUtils.isTablet() ? 'grid' : 'list',
                                                        showDownloadButton: true
                                                    };
                                                };
                                                componentCall.paramsGenerator_ = paramsLambda;
                                            }
                                            else {
                                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                                    app: app,
                                                    cardType: this.deviceUtils.isTablet() ? 'grid' : 'list',
                                                    showDownloadButton: true
                                                });
                                            }
                                        }, { name: "AppCard" });
                                    }
                                    __Common__.pop();
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.searchResults, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 加载更多
                        if (this.hasMore || this.isLoadingMore) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                {
                                    const itemCreation = (elmtId, isInitialRender) => {
                                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                        itemCreation2(elmtId, isInitialRender);
                                        if (!isInitialRender) {
                                            ListItem.pop();
                                        }
                                        ViewStackProcessor.StopGetAccessRecording();
                                    };
                                    const itemCreation2 = (elmtId, isInitialRender) => {
                                        ListItem.create(deepRenderFunction, true);
                                    };
                                    const deepRenderFunction = (elmtId, isInitialRender) => {
                                        itemCreation(elmtId, isInitialRender);
                                        {
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                if (isInitialRender) {
                                                    let componentCall = new LoadMoreView(this, {
                                                        isLoading: this.isLoadingMore,
                                                        hasMore: this.hasMore,
                                                        onLoadMore: () => {
                                                            if (!this.isLoadingMore && this.hasMore) {
                                                                this.performSearch(this.searchKeyword, true);
                                                            }
                                                        }
                                                    }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/SearchPage.ets", line: 408, col: 15 });
                                                    ViewPU.create(componentCall);
                                                    let paramsLambda = () => {
                                                        return {
                                                            isLoading: this.isLoadingMore,
                                                            hasMore: this.hasMore,
                                                            onLoadMore: () => {
                                                                if (!this.isLoadingMore && this.hasMore) {
                                                                    this.performSearch(this.searchKeyword, true);
                                                                }
                                                            }
                                                        };
                                                    };
                                                    componentCall.paramsGenerator_ = paramsLambda;
                                                }
                                                else {
                                                    this.updateStateVarsOfChildByElmtId(elmtId, {
                                                        isLoading: this.isLoadingMore,
                                                        hasMore: this.hasMore
                                                    });
                                                }
                                            }, { name: "LoadMoreView" });
                                        }
                                        ListItem.pop();
                                    };
                                    this.observeComponentCreation2(itemCreation2, ListItem);
                                    ListItem.pop();
                                }
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    // 应用列表
                    List.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create({ alignContent: Alignment.Bottom });
            Stack.width('100%');
            Stack.height('100%');
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部搜索栏
            Row.create();
            // 顶部搜索栏
            Row.width('100%');
            // 顶部搜索栏
            Row.height(56);
            // 顶部搜索栏
            Row.padding({ left: '16vp', right: '16vp' });
            // 顶部搜索栏
            Row.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 顶部搜索栏
            Row.justifyContent(FlexAlign.SpaceBetween);
            // 顶部搜索栏
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.onClick(() => {
                this.getUIContext().getRouter().back();
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            __Common__.create();
            __Common__.layoutWeight(1);
            __Common__.margin({ left: 12, right: 12 });
        }, __Common__);
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new SearchBar(this, {
                        placeholder: '搜索应用',
                        searchText: this.searchKeyword,
                        showCancelButton: false,
                        isLoading: this.isSearching,
                        onSearch: (keyword: string) => {
                            this.performSearch(keyword);
                        },
                        onTextChange: (value: string) => {
                            this.searchKeyword = value;
                            this.showSuggestions = value.trim().length === 0;
                        },
                        onSearchFocus: () => {
                            if (this.searchKeyword.trim().length === 0) {
                                this.showSuggestions = true;
                            }
                        }
                    }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/SearchPage.ets", line: 439, col: 11 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {
                            placeholder: '搜索应用',
                            searchText: this.searchKeyword,
                            showCancelButton: false,
                            isLoading: this.isSearching,
                            onSearch: (keyword: string) => {
                                this.performSearch(keyword);
                            },
                            onTextChange: (value: string) => {
                                this.searchKeyword = value;
                                this.showSuggestions = value.trim().length === 0;
                            },
                            onSearchFocus: () => {
                                if (this.searchKeyword.trim().length === 0) {
                                    this.showSuggestions = true;
                                }
                            }
                        };
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {
                        placeholder: '搜索应用',
                        showCancelButton: false,
                        isLoading: this.isSearching
                    });
                }
            }, { name: "SearchBar" });
        }
        __Common__.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor(this.showFilter ? { "id": 125829186, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" } : { "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.onClick(() => {
                this.showFilter = !this.showFilter;
            });
        }, Image);
        // 顶部搜索栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 内容区域
            if (this.showSuggestions) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.SearchSuggestionsView.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.SearchResults.bind(this)();
                });
            }
        }, If);
        If.pop();
        Column.pop();
        // 筛选器面板
        this.FilterView.bind(this)();
        Stack.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "SearchPage";
    }
}
export { SearchPage };
registerNamedRoute(() => new SearchPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/SearchPage", pageFullPath: "entry/src/main/ets/pages/SearchPage", integratedHsp: "false", moduleType: "followWithHap" });
