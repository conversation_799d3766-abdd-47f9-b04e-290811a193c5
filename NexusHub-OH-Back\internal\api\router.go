package api

import (
	"net/http"
	"time"

	"nexushub-oh-back/config"
	"nexushub-oh-back/internal/middleware"
	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/internal/services"
	"nexushub-oh-back/pkg/auth"
	"nexushub-oh-back/pkg/database"
	"nexushub-oh-back/pkg/logger"
	"nexushub-oh-back/pkg/messaging"
	"nexushub-oh-back/pkg/storage"

	"github.com/gin-gonic/gin"

	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// SetupRouter 设置API路由
func SetupRouter(
	router *gin.Engine,
	db *database.PostgresDB,
	redis *database.RedisClient,
	storageClient storage.StorageProvider,
	jwtService *auth.JWTService,
	logtoService *auth.LogtoService,
	searchService *services.SearchService,
	userSearchService *services.UserSearchService,
	reviewSearchService *services.ReviewSearchService,
	tagSearchService *services.TagSearchService,
	rabbitMQClient *messaging.RabbitMQClient,
	cfg *config.Config,
) {
	// 全局中间件
	router.Use(middleware.Cors())
	router.Use(logger.GinLogger())

	// Swagger 文档路由
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 快速API测试路由
	router.GET("/config", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"server_port":  cfg.Server.Port,
			"server_mode":  cfg.Server.Mode,
			"db_host":      cfg.Database.Host,
			"redis_addr":   cfg.Redis.Addr,
			"storage_type": cfg.Storage.Type,
		})
	})

	// 限流器
	var rateLimiter *middleware.RateLimiter
	if cfg != nil && cfg.RateLimit.Enable {
		rateLimiter = middleware.NewRateLimiterWithConfig(&cfg.RateLimit)
	} else {
		// 使用默认配置
		rateLimiter = middleware.NewRateLimiter(time.Minute, 60) // 每分钟60个请求
		rateLimiter.StartCleanupTask(time.Minute * 10)           // 每10分钟清理一次过期记录
	}

	// 检查数据库连接
	if db == nil {
		router.GET("/", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"status":        "running",
				"message":       "服务器运行中，但未连接数据库",
				"config_loaded": cfg != nil,
			})
		})
		return
	}

	// 控制器
	var gormDB *gorm.DB
	if db != nil {
		gormDB = db.DB
	}

	userController := NewUserController(gormDB, jwtService)
	appController := NewAppController(gormDB, storageClient, searchService)
	reviewController := NewReviewController(gormDB)
	statsController := NewStatsController(gormDB)
	categoryController := NewCategoryController(gormDB)
	tagController := NewTagController(gormDB)
	featuredCollectionController := NewFeaturedCollectionController(gormDB)
	dashboardController := NewDashboardController(gormDB)
	geographicController := NewGeographicController(gormDB)
	harmonyVersionController := NewHarmonyVersionController(gormDB)

	// 消息队列控制器（仅在RabbitMQ客户端可用时初始化）
	var messageController *MessageController
	var messageService *services.MessageService
	if rabbitMQClient != nil {
		messageService = services.NewMessageService(rabbitMQClient)
		messageController = NewMessageController(messageService)
		// 初始化队列
		if err := messageService.InitializeQueues(); err != nil {
			logger.Warn("初始化消息队列失败", zap.Error(err))
		}
	}

	// 通知控制器
	var notificationController *NotificationController
	var notificationService *services.NotificationService
	if gormDB != nil {
		notificationService = services.NewNotificationService(gormDB, messageService, logger.Logger)
		notificationController = NewNotificationController(notificationService)
	}

	// 开发者控制器需要MessageService和NotificationService
	developerController := NewDeveloperController(gormDB, storageClient, messageService, notificationService)

	// 搜索控制器（仅在搜索服务可用时初始化）
	var searchController *SearchController
	if searchService != nil && userSearchService != nil && reviewSearchService != nil && tagSearchService != nil {
		searchController = NewSearchController(searchService, userSearchService, reviewSearchService, tagSearchService)
	}

	// API基础路径
	api := router.Group("/api/v1")
	{
		// 不需要认证的路由
		api.GET("/health", HealthCheck)

		// 地理位置相关（公共接口）
		geographic := api.Group("/geographic")
		{
			geographic.GET("/country", geographicController.GetCountries)
			geographic.GET("/province", geographicController.GetProvinces)
			geographic.GET("/city/:province", geographicController.GetCities)
			geographic.GET("/district/:city", geographicController.GetDistricts)
			geographic.GET("/street/:district", geographicController.GetStreets)
			geographic.GET("/level", geographicController.GetGeographicByLevel)
		}

		// 搜索相关（公共接口）
		if searchController != nil {
			search := api.Group("/search")
			{
				search.GET("/apps", searchController.SearchApps)
				search.GET("/suggestions", searchController.SearchSuggestions)
				search.GET("/reviews", searchController.SearchReviews)
				search.GET("/tags", searchController.SearchTags)
				search.GET("/tags/suggest", searchController.SuggestTags)
			}
		}

		// 公开API接口（无需认证）
		public := api.Group("/public")
		{
			// 获取应用详情
			public.GET("/apps/:id", appController.GetAppAnonymous)
			// 下载记录
			public.POST("/apps/:id/versions/:version_id/download", appController.RecordDownloadAnonymous)

			// 获取推荐应用
			public.GET("/apps/recommended", appController.GetRecommendedApps)
			// 获取热门应用
			public.GET("/apps/popular", appController.GetPopularApps)
			// 获取最新应用
			public.GET("/apps/latest", appController.GetLatestApps)
			// 获取应用列表（公开版本）
			public.GET("/apps", appController.GetPublicApps)
			// 获取精选应用
			public.GET("/apps/featured", appController.GetFeaturedApps)

			// 获取分类列表（公开接口）
			public.GET("/categories", categoryController.ListPublicCategories)
		}

		// 公共评论接口（公开API）
		if reviewController != nil {
			api.GET("/apps/:id/reviews", reviewController.GetAppReviews)
		}

		// 消息队列相关（需要认证）（公开API）
		if messageController != nil {
			messages := api.Group("/messages")
			messages.Use(middleware.HybridAuth(jwtService, logtoService))
			{
				messages.POST("/notification", messageController.SendNotification)
				messages.POST("/email", messageController.SendEmail)
				messages.POST("/activity", messageController.RecordUserActivity)
				messages.POST("/app-review/:app_id", messageController.TriggerAppReview)
				messages.GET("/status", messageController.GetQueueStatus)
			}
		}

		// 通知相关（需要认证）
		if notificationController != nil {
			notifications := api.Group("/notifications")
			notifications.Use(middleware.HybridAuth(jwtService, logtoService))
			{
				notifications.GET("", notificationController.GetNotifications)
				notifications.POST("/read", notificationController.MarkAsRead)
				notifications.POST("/read-all", notificationController.MarkAllAsRead)
				notifications.GET("/unread-count", notificationController.GetUnreadCount)
				notifications.GET("/settings", notificationController.GetNotificationSettings)
				notifications.PUT("/settings", notificationController.UpdateNotificationSettings)
				notifications.DELETE("/:id", notificationController.DeleteNotification)
			}
		}

		// 用户相关
		users := api.Group("/users")
		{
			users.POST("/register", rateLimiter.LimitByIP(), userController.Register)
			users.POST("/login", rateLimiter.LimitByIP(), userController.Login)

			// 需要认证的路由
			userAuth := users.Group("")
			userAuth.Use(middleware.HybridAuth(jwtService, logtoService))
			{
				userAuth.POST("/logout", userController.Logout)
				userAuth.GET("/profile", userController.GetProfile)
				userAuth.PUT("/profile", userController.UpdateProfile)
			}
		}

		// 开发者相关
		developers := api.Group("/developers")
		{
			// 需要认证的接口
			developersAuth := developers.Group("")
			developersAuth.Use(middleware.HybridAuth(jwtService, logtoService))
			{
				developersAuth.POST("/verify", developerController.SubmitVerify)
				developersAuth.GET("/verify/status", developerController.GetVerifyStatus)
			}
		}

		// 开发者应用管理
		developer := api.Group("/developer")
		developer.Use(middleware.HybridAuth(jwtService, logtoService))
		{
			developer.GET("/apps", appController.ListDeveloperApps)
		}

		// 文件上传
		upload := api.Group("/upload")
		upload.Use(middleware.HybridAuth(jwtService, logtoService))
		{
			upload.GET("/token", developerController.GetUploadToken)
		}

		// 分类相关（公开API）
		categories := api.Group("/categories")
		{
			// 公开接口
			categories.GET("", categoryController.ListCategories)
			categories.GET("/root", categoryController.ListRootCategories)
			categories.GET("/:id", categoryController.GetCategory)
			categories.GET("/:id/apps", categoryController.GetAppsByCategory)

			// 管理员接口
			categoriesAdmin := categories.Group("")
			categoriesAdmin.Use(middleware.HybridAuth(jwtService, logtoService))
			categoriesAdmin.Use(middleware.RoleAuth(string(models.UserRoleAdmin)))
			{
				categoriesAdmin.POST("", categoryController.CreateCategory)
				categoriesAdmin.PUT("/:id", categoryController.UpdateCategory)
				categoriesAdmin.DELETE("/:id", categoryController.DeleteCategory)
			}
		}

		// 标签相关（公开API）
		tags := api.Group("/tags")
		{
			// 公开接口
			tags.GET("", tagController.ListTags)
			tags.GET("/:id", tagController.GetTag)
			tags.GET("/:id/apps", tagController.GetAppsByTag)
		}

		// 精选集相关（公开接口）
		featuredCollectionsPublic := public.Group("/featured-collections")
		{
			featuredCollectionsPublic.GET("", featuredCollectionController.ListFeaturedCollections)
			featuredCollectionsPublic.GET("/:id", featuredCollectionController.GetFeaturedCollection)
			featuredCollectionsPublic.GET("/:id/apps", featuredCollectionController.GetFeaturedCollectionApps)
		}

		// 标签管理员接口
		tagsAdmin := tags.Group("")
		tagsAdmin.Use(middleware.HybridAuth(jwtService, logtoService))
		tagsAdmin.Use(middleware.RoleAuth(string(models.UserRoleAdmin)))
		{
			tagsAdmin.POST("", tagController.CreateTag)
			tagsAdmin.PUT("/:id", tagController.UpdateTag)
			tagsAdmin.DELETE("/:id", tagController.DeleteTag)
		}

		// 应用相关
		apps := api.Group("/apps")
		{
			// 公开接口
			apps.GET("/:id", appController.GetApp)

			// 需要认证的接口
			appAuth := apps.Group("")
			appAuth.Use(middleware.HybridAuth(jwtService, logtoService))
			{
				appAuth.GET("", appController.ListApps)
				appAuth.POST("", appController.CreateApp)
				appAuth.PUT("/:id", appController.UpdateApp)

				// 应用标签相关
				appAuth.GET("/:id/tags", tagController.GetAppTags)
				appAuth.POST("/:id/tags", tagController.AddAppTags)
				appAuth.DELETE("/:id/tags/:tag_id", tagController.RemoveAppTag)

				// 应用评论相关（发表评论需要认证）
				appAuth.POST("/:id/reviews", reviewController.CreateReview)
				appAuth.POST("/:id/reviews/:review_id/like", reviewController.LikeReview)
				appAuth.POST("/:id/reviews/:review_id/unlike", reviewController.UnlikeReview)
				appAuth.POST("/:id/reviews/:review_id/respond", reviewController.RespondToReview)

				// 应用版本管理
				appAuth.POST("/:id/versions", appController.CreateAppVersion)
				appAuth.GET("/:id/versions", appController.GetAppVersions)
				appAuth.GET("/:id/versions/:version_id", appController.GetAppVersion)
				appAuth.PUT("/:id/versions/:version_id", appController.UpdateAppVersion)
				appAuth.DELETE("/:id/versions/:version_id", appController.DeleteAppVersion)
				appAuth.POST("/:id/versions/:version_id/publish", appController.PublishAppVersion)

				// 应用提交审核
				appAuth.POST("/:id/submit", appController.SubmitAppForReview)

				// 应用下载记录
				appAuth.POST("/:id/versions/:version_id/download", statsController.RecordDownload)
			}

		}

		// 统计相关
		stats := api.Group("/stats")
		stats.Use(middleware.HybridAuth(jwtService, logtoService))
		{
			stats.GET("/downloads", statsController.GetUserDownloads)
			stats.GET("/apps/:id/downloads", statsController.GetAppDownloadStats)
			stats.GET("/apps/overall", statsController.GetAppOverallStats)
		}

		// 管理员相关
		admin := api.Group("/admin")
		admin.Use(middleware.HybridAuth(jwtService, logtoService))
		admin.Use(middleware.RoleAuth(string(models.UserRoleAdmin)))
		{
			// 开发者管理
			admin.GET("/developers/verify", developerController.ListPendingVerifications)
			admin.POST("/developers/:id/verify", developerController.VerifyDeveloper)
			admin.GET("/developers/stats", developerController.GetDeveloperStats)
			admin.GET("/developers/recent", developerController.GetRecentApplications)

			// 用户管理
			admin.GET("/users", userController.ListUsers)
			admin.POST("/users", userController.AdminCreateUser)
			admin.GET("/users/:id", userController.GetUserDetail)
			admin.GET("/users/:id/login-records", userController.GetUserLoginRecords)
			admin.GET("/users/:id/app-records", userController.GetUserAppRecords)
			admin.PUT("/users/:id/role", userController.UpdateUserRole)
			admin.PUT("/users/:id/status", userController.UpdateUserStatus)

			// 版本审核
			admin.GET("/versions/pending", appController.GetPendingVersions)
			admin.POST("/versions/:version_id/review", appController.ReviewAppVersion)

			// OpenHarmonyOS版本管理
			admin.POST("/harmony-versions", harmonyVersionController.CreateOpenHarmonyVersion)
			admin.GET("/harmony-versions", harmonyVersionController.GetOpenHarmonyVersions)
			admin.GET("/harmony-versions/:id", harmonyVersionController.GetOpenHarmonyVersion)
			admin.PUT("/harmony-versions/:id", harmonyVersionController.UpdateOpenHarmonyVersion)
			admin.DELETE("/harmony-versions/:id", harmonyVersionController.DeleteOpenHarmonyVersion)

			// 搜索索引管理
			if searchController != nil {
				admin.POST("/search/init", searchController.InitializeSearchIndex)
				admin.POST("/search/sync", searchController.SyncSearchIndex)
				admin.POST("/search/initialize-all", searchController.InitializeAllSearchIndexes)
				admin.GET("/search/users", searchController.SearchUsers)
				admin.GET("/search/tags/stats", searchController.GetTagStats)
			}

			// 精选集管理（管理员权限）
			featuredCollectionsAdmin := admin.Group("/featured-collections")
			{
				featuredCollectionsAdmin.POST("", featuredCollectionController.CreateFeaturedCollection)
				featuredCollectionsAdmin.GET("", featuredCollectionController.ListAllFeaturedCollections)
				featuredCollectionsAdmin.PUT("/:id", featuredCollectionController.UpdateFeaturedCollection)
				featuredCollectionsAdmin.DELETE("/:id", featuredCollectionController.DeleteFeaturedCollection)
				featuredCollectionsAdmin.POST("/:id/apps", featuredCollectionController.AddAppToFeaturedCollection)
				featuredCollectionsAdmin.DELETE("/:id/apps/:app_id", featuredCollectionController.RemoveAppFromFeaturedCollection)
			}
		}

		// 运营人员相关
		operator := api.Group("/operator")
		operator.Use(middleware.HybridAuth(jwtService, logtoService))
		operator.Use(middleware.RoleAuth(string(models.UserRoleOperator), string(models.UserRoleAdmin)))
		{
			// TODO: 添加运营人员专属接口
		}

		// 审核员相关
		reviewer := api.Group("/reviewer")
		reviewer.Use(middleware.HybridAuth(jwtService, logtoService))
		reviewer.Use(middleware.RoleAuth(string(models.UserRoleReviewer), string(models.UserRoleAdmin)))
		{
			// TODO: 添加审核员专属接口
			reviewer.GET("/apps/pending", appController.ListPendingApps)
			reviewer.POST("/apps/:id/review", appController.ReviewApp)
		}

		// 仪表盘相关
		dashboard := api.Group("/dashboard")
		dashboard.Use(middleware.HybridAuth(jwtService, logtoService))
		{
			// 分析页
			analytics := dashboard.Group("/analytics")
			{
				analytics.GET("/summary", dashboardController.GetAnalyticsSummary)
				analytics.GET("/trend", dashboardController.GetAnalyticsTrend)
				analytics.GET("/categories", dashboardController.GetCategoryStats)
				analytics.GET("/popular-apps", dashboardController.GetPopularApps)
			}

			// 监控页
			monitoring := dashboard.Group("/monitoring")
			{
				monitoring.GET("/data", dashboardController.GetMonitoringData)
				monitoring.GET("/logs", dashboardController.GetSystemLogs)
				monitoring.GET("/alerts", dashboardController.GetAlertEvents)
			}

			// 工作台
			workbench := dashboard.Group("/workbench")
			{
				workbench.GET("/summary", dashboardController.GetWorkbenchSummary)
				workbench.GET("/activities", dashboardController.GetRecentActivities)
				workbench.GET("/tasks", dashboardController.GetTasks)
				workbench.POST("/tasks", dashboardController.CreateTask)
				workbench.PUT("/tasks/:id", dashboardController.UpdateTask)
				workbench.DELETE("/tasks/:id", dashboardController.DeleteTask)
			}
		}
	}
}

// HealthCheck 健康检查
//
//	@Summary		健康检查接口
//	@Description	检查API服务是否正常运行
//	@Tags			系统
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	map[string]interface{}	"返回服务状态和时间"
//	@Router			/health [get]
func HealthCheck(c *gin.Context) {
	Success(c, gin.H{
		"status": "ok",
		"time":   time.Now().Format(time.RFC3339),
	})
}
