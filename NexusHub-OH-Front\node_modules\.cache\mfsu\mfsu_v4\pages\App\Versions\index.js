"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { PageContainer } from "@ant-design/pro-components";
import { Card, Table, Button, Space, Tag, message, Modal, Popconfirm } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ReloadOutlined } from "@ant-design/icons";
import { useState } from "react";
import { useRequest, history, useParams } from "@umijs/max";
import { getAppVersions, deleteAppVersion } from "@/services/version";
const DeveloperVersions = () => {
  const params = useParams();
  const appId = params.id;
  const [searchParams, setSearchParams] = useState({});
  const { data, loading, run } = useRequest(
    () => getAppVersions(Number(appId), {
      page: searchParams.page || 1,
      pageSize: searchParams.pageSize || 20
    }),
    {
      refreshDeps: [searchParams, appId]
    }
  );
  const handleDelete = async (versionId) => {
    try {
      await deleteAppVersion(versionId);
      message.success("\u7248\u672C\u5220\u9664\u6210\u529F");
      run();
    } catch (error) {
      console.error("\u5220\u9664\u7248\u672C\u5931\u8D25:", error);
      message.error("\u5220\u9664\u7248\u672C\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
    }
  };
  const handleViewDetail = (version) => {
    Modal.info({
      title: "\u7248\u672C\u8BE6\u60C5",
      width: 600,
      content: /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsx("strong", { children: "\u7248\u672C\u53F7:" }),
          " ",
          version.versionName
        ] }),
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsx("strong", { children: "\u7248\u672C\u4EE3\u7801:" }),
          " ",
          version.versionCode
        ] }),
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsx("strong", { children: "\u6587\u4EF6\u5927\u5C0F:" }),
          " ",
          version.fileSize ? `${(version.fileSize / 1024 / 1024).toFixed(2)} MB` : "\u672A\u77E5"
        ] }),
        /* @__PURE__ */ jsx("p", { children: /* @__PURE__ */ jsx("strong", { children: "\u66F4\u65B0\u8BF4\u660E:" }) }),
        /* @__PURE__ */ jsx("div", { style: { background: "#f5f5f5", padding: "8px", borderRadius: "4px", maxHeight: "200px", overflow: "auto" }, children: version.updateDescription || "\u65E0\u66F4\u65B0\u8BF4\u660E" }),
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsx("strong", { children: "\u521B\u5EFA\u65F6\u95F4:" }),
          " ",
          version.createdAt || version.created_at ? new Date(version.createdAt || version.created_at).toLocaleString() : "-"
        ] }),
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsx("strong", { children: "\u66F4\u65B0\u65F6\u95F4:" }),
          " ",
          version.updatedAt || version.updated_at ? new Date(version.updatedAt || version.updated_at).toLocaleString() : "-"
        ] })
      ] })
    });
  };
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80
    },
    {
      title: "\u7248\u672C\u53F7",
      dataIndex: "versionName",
      key: "versionName",
      width: 120
    },
    {
      title: "\u7248\u672C\u4EE3\u7801",
      dataIndex: "versionCode",
      key: "versionCode",
      width: 100
    },
    {
      title: "\u6587\u4EF6\u5927\u5C0F",
      dataIndex: "fileSize",
      key: "fileSize",
      width: 120,
      render: (size) => size ? `${(size / 1024 / 1024).toFixed(2)} MB` : "\u672A\u77E5"
    },
    {
      title: "\u72B6\u6001",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status) => {
        let color = "orange";
        let text = "\u5F85\u5BA1\u6838";
        if (status === "approved") {
          color = "green";
          text = "\u5DF2\u901A\u8FC7";
        } else if (status === "rejected") {
          color = "red";
          text = "\u5DF2\u62D2\u7EDD";
        } else if (status === "published") {
          color = "blue";
          text = "\u5DF2\u53D1\u5E03";
        } else if (status === "draft") {
          color = "gray";
          text = "\u8349\u7A3F";
        }
        return /* @__PURE__ */ jsx(Tag, { color, children: text });
      }
    },
    {
      title: "\u521B\u5EFA\u65F6\u95F4",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (time, record) => {
        const timeValue = time || record.created_at;
        return timeValue ? new Date(timeValue).toLocaleString() : "-";
      },
      sorter: true
    },
    {
      title: "\u66F4\u65B0\u65F6\u95F4",
      dataIndex: "updatedAt",
      key: "updatedAt",
      width: 150,
      render: (time, record) => {
        const timeValue = time || record.updated_at;
        return timeValue ? new Date(timeValue).toLocaleString() : "-";
      },
      sorter: true
    },
    {
      title: "\u64CD\u4F5C",
      key: "action",
      width: 250,
      render: (_, record) => /* @__PURE__ */ jsxs(Space, { size: "small", children: [
        /* @__PURE__ */ jsx(
          Button,
          {
            type: "link",
            icon: /* @__PURE__ */ jsx(EyeOutlined, {}),
            size: "small",
            onClick: () => handleViewDetail(record),
            children: "\u67E5\u770B"
          }
        ),
        (record.status === "draft" || record.status === "rejected") && /* @__PURE__ */ jsx(
          Button,
          {
            type: "link",
            icon: /* @__PURE__ */ jsx(EditOutlined, {}),
            size: "small",
            onClick: () => history.push(`/app/list/versions/${appId}/edit/${record.id}`),
            children: "\u7F16\u8F91"
          }
        ),
        record.status === "draft" && /* @__PURE__ */ jsx(
          Popconfirm,
          {
            title: "\u786E\u5B9A\u8981\u5220\u9664\u8FD9\u4E2A\u7248\u672C\u5417\uFF1F",
            onConfirm: () => handleDelete(record.id),
            okText: "\u786E\u5B9A",
            cancelText: "\u53D6\u6D88",
            children: /* @__PURE__ */ jsx(
              Button,
              {
                type: "link",
                danger: true,
                icon: /* @__PURE__ */ jsx(DeleteOutlined, {}),
                size: "small",
                children: "\u5220\u9664"
              }
            )
          }
        )
      ] })
    }
  ];
  return /* @__PURE__ */ jsx(PageContainer, { children: /* @__PURE__ */ jsxs(Card, { bordered: false, children: [
    /* @__PURE__ */ jsxs("div", { style: { marginBottom: 16, display: "flex", justifyContent: "space-between", alignItems: "center" }, children: [
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("span", { children: "\u7248\u672C\u7BA1\u7406" }),
        /* @__PURE__ */ jsxs(Tag, { color: "blue", style: { marginLeft: 8 }, children: [
          "\u5E94\u7528ID: ",
          appId
        ] }),
        /* @__PURE__ */ jsxs(Tag, { color: "green", style: { marginLeft: 8 }, children: [
          "\u603B\u7248\u672C\u6570: ",
          data?.versions?.length || 0
        ] })
      ] }),
      /* @__PURE__ */ jsxs(Space, { children: [
        /* @__PURE__ */ jsx(
          Button,
          {
            type: "primary",
            icon: /* @__PURE__ */ jsx(PlusOutlined, {}),
            onClick: () => history.push(`/app/list/versions/${appId}/create`),
            children: "\u521B\u5EFA\u65B0\u7248\u672C"
          }
        ),
        /* @__PURE__ */ jsx(
          Button,
          {
            icon: /* @__PURE__ */ jsx(ReloadOutlined, {}),
            onClick: () => run(),
            children: "\u5237\u65B0"
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ jsx(
      Table,
      {
        columns,
        dataSource: data?.versions,
        rowKey: "id",
        loading,
        scroll: { x: 1e3 },
        pagination: {
          current: data?.page || 1,
          pageSize: data?.page_size || 20,
          total: data?.versions?.length || 0,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `\u7B2C ${range[0]}-${range[1]} \u6761/\u5171 ${total} \u6761`,
          onChange: (page, pageSize) => {
            setSearchParams({
              ...searchParams,
              page,
              pageSize
            });
          }
        }
      }
    )
  ] }) });
};
export default DeveloperVersions;
