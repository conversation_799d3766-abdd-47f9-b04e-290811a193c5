"use strict";
import { request } from "@umijs/max";
export async function getUserList(params) {
  return request("admin/users", {
    method: "GET",
    params
  });
}
export async function updateUserStatus(id, status) {
  return request(`/admin/users/${id}/status`, {
    method: "PUT",
    data: { status }
  });
}
export async function getPendingApps(params) {
  return request("/admin/app/pending", {
    method: "GET",
    params
  });
}
export async function auditApp(id, data) {
  return request(`/admin/app/audit/${id}`, {
    method: "PUT",
    data
  });
}
export async function getPendingReviews(params) {
  return request("/admin/review/pending", {
    method: "GET",
    params
  });
}
export async function auditReview(id, data) {
  return request(`/admin/review/audit/${id}`, {
    method: "PUT",
    data
  });
}
export async function getDeveloperApplications(params) {
  return request("/admin/developer/applications", {
    method: "GET",
    params
  });
}
export async function auditDeveloperApplication(id, data) {
  return request(`/admin/developer/audit/${id}`, {
    method: "PUT",
    data
  });
}
