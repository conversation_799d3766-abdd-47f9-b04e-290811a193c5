"use strict";
import { request } from "@umijs/max";
export async function getNotifications(params = {}) {
  return request("/notifications", {
    method: "GET",
    params
  });
}
export async function getUnreadCount() {
  return request("/notifications/unread-count", {
    method: "GET"
  });
}
export async function markAsRead(params) {
  return request("/notifications/read", {
    method: "POST",
    data: params
  });
}
export async function markAllAsRead() {
  return request("/notifications/read-all", {
    method: "POST"
  });
}
export async function getNotificationSettings() {
  return request("/notifications/settings", {
    method: "GET"
  });
}
export async function updateNotificationSettings(params) {
  return request("/notifications/settings", {
    method: "PUT",
    data: params
  });
}
export async function deleteNotification(params) {
  return request(`/notifications/${params.id}`, {
    method: "DELETE"
  });
}
