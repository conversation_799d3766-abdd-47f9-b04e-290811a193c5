if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface HomePage_Params {
    recommendedApps?: AppModel[];
    bannerList?: BannerModel[];
    loadingState?: LoadingState;
    refreshing?: boolean;
    searchKeyword?: string;
    needLogin?: boolean;
    currentBannerIndex?: number;
    bannerTimer?: number;
    swiperController?: SwiperController;
    deviceUtils?;
    apiService?;
}
import type { AppModel, AppSearchParams } from '../models/App';
import type { CategoryModel } from '../models/Category';
import type { BannerModel } from '../models/Banner';
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { AppCard } from "@normalized:N&&&entry/src/main/ets/components/AppCard&";
import { SearchBar } from "@normalized:N&&&entry/src/main/ets/components/SearchBar&";
import { LoadingView, LoadingState } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import preferences from "@ohos:data.preferences";
import hilog from "@ohos:hilog";
export class HomePage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__recommendedApps = new ObservedPropertyObjectPU([], this, "recommendedApps");
        this.__bannerList = new ObservedPropertyObjectPU([], this, "bannerList");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.LOADING, this, "loadingState");
        this.__refreshing = new ObservedPropertySimplePU(false, this, "refreshing");
        this.__searchKeyword = new ObservedPropertySimplePU('', this, "searchKeyword");
        this.__needLogin = new ObservedPropertySimplePU(false, this, "needLogin");
        this.__currentBannerIndex = new ObservedPropertySimplePU(0, this, "currentBannerIndex");
        this.bannerTimer = -1;
        this.swiperController = new SwiperController();
        this.deviceUtils = DeviceUtils.getInstance();
        this.apiService = ApiService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: HomePage_Params) {
        if (params.recommendedApps !== undefined) {
            this.recommendedApps = params.recommendedApps;
        }
        if (params.bannerList !== undefined) {
            this.bannerList = params.bannerList;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.refreshing !== undefined) {
            this.refreshing = params.refreshing;
        }
        if (params.searchKeyword !== undefined) {
            this.searchKeyword = params.searchKeyword;
        }
        if (params.needLogin !== undefined) {
            this.needLogin = params.needLogin;
        }
        if (params.currentBannerIndex !== undefined) {
            this.currentBannerIndex = params.currentBannerIndex;
        }
        if (params.bannerTimer !== undefined) {
            this.bannerTimer = params.bannerTimer;
        }
        if (params.swiperController !== undefined) {
            this.swiperController = params.swiperController;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
    }
    updateStateVars(params: HomePage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__recommendedApps.purgeDependencyOnElmtId(rmElmtId);
        this.__bannerList.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__refreshing.purgeDependencyOnElmtId(rmElmtId);
        this.__searchKeyword.purgeDependencyOnElmtId(rmElmtId);
        this.__needLogin.purgeDependencyOnElmtId(rmElmtId);
        this.__currentBannerIndex.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__recommendedApps.aboutToBeDeleted();
        this.__bannerList.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__refreshing.aboutToBeDeleted();
        this.__searchKeyword.aboutToBeDeleted();
        this.__needLogin.aboutToBeDeleted();
        this.__currentBannerIndex.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __recommendedApps: ObservedPropertyObjectPU<AppModel[]>;
    get recommendedApps() {
        return this.__recommendedApps.get();
    }
    set recommendedApps(newValue: AppModel[]) {
        this.__recommendedApps.set(newValue);
    }
    private __bannerList: ObservedPropertyObjectPU<BannerModel[]>;
    get bannerList() {
        return this.__bannerList.get();
    }
    set bannerList(newValue: BannerModel[]) {
        this.__bannerList.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __refreshing: ObservedPropertySimplePU<boolean>;
    get refreshing() {
        return this.__refreshing.get();
    }
    set refreshing(newValue: boolean) {
        this.__refreshing.set(newValue);
    }
    private __searchKeyword: ObservedPropertySimplePU<string>;
    get searchKeyword() {
        return this.__searchKeyword.get();
    }
    set searchKeyword(newValue: string) {
        this.__searchKeyword.set(newValue);
    }
    private __needLogin: ObservedPropertySimplePU<boolean>;
    get needLogin() {
        return this.__needLogin.get();
    }
    set needLogin(newValue: boolean) {
        this.__needLogin.set(newValue);
    }
    private __currentBannerIndex: ObservedPropertySimplePU<number>;
    get currentBannerIndex() {
        return this.__currentBannerIndex.get();
    }
    set currentBannerIndex(newValue: number) {
        this.__currentBannerIndex.set(newValue);
    }
    private bannerTimer: number;
    private swiperController: SwiperController;
    private deviceUtils;
    private apiService;
    // 注释：移除导航项配置，统一由Index.ets管理
    aboutToAppear() {
        this.loadHomeData();
        this.startBannerAutoPlay();
    }
    /**
     * 页面显示时的回调
     */
    onPageShow() {
        this.checkAndSetAuthToken();
        this.refreshData();
    }
    aboutToDisappear() {
        this.stopBannerAutoPlay();
    }
    /**
     * 加载首页数据
     */
    private async loadHomeData(): Promise<void> {
        this.loadingState = LoadingState.LOADING;
        try {
            hilog.info(0x0000, 'HomePage', '开始加载首页数据...');
            // 检查并设置认证令牌（可选）
            try {
                await this.checkAndSetAuthToken();
                hilog.info(0x0000, 'HomePage', '认证令牌设置成功');
            }
            catch (authError) {
                hilog.warn(0x0000, 'HomePage', '认证令牌设置失败，将使用匿名访问: %{public}s', JSON.stringify(authError));
            }
            // 尝试从API加载数据
            const results = await Promise.allSettled([
                this.apiService.getBanners(),
                this.apiService.getRecommendedApps()
            ]);
            const bannersResult = results[0];
            const recommendedResult = results[1];
            hilog.info(0x0000, 'HomePage', 'API请求结果: banners=%{public}s, recommended=%{public}s', bannersResult.status, recommendedResult.status);
            let hasApiData = false;
            // 处理轮播图结果
            if (bannersResult.status === 'fulfilled' && bannersResult.value.code === 200) {
                this.bannerList = Array.isArray(bannersResult.value.data) ? bannersResult.value.data.filter(banner => banner.is_active) : [];
                hilog.info(0x0000, 'HomePage', '轮播图加载成功，数量: %{public}d', this.bannerList.length);
            }
            else {
                hilog.warn(0x0000, 'HomePage', '轮播图加载失败，使用模拟数据');
                this.loadMockBannerData();
            }
            // 处理推荐应用结果
            if (recommendedResult.status === 'fulfilled' && recommendedResult.value.code === 200) {
                // 后端返回的data包含list字段
                this.recommendedApps = Array.isArray(recommendedResult.value.data) ? recommendedResult.value.data : (recommendedResult.value.data?.list || []);
                hilog.info(0x0000, 'HomePage', '推荐应用加载成功，数量: %{public}d', this.recommendedApps.length);
                hasApiData = true;
            }
            // 如果API数据加载失败，使用模拟数据
            if (!hasApiData) {
                hilog.warn(0x0000, 'HomePage', 'API数据加载失败，使用模拟数据');
                this.loadMockData();
            }
            this.loadingState = LoadingState.SUCCESS;
            hilog.info(0x0000, 'HomePage', '首页数据加载完成');
        }
        catch (error) {
            hilog.error(0x0000, 'HomePage', '加载首页数据时发生异常: %{public}s', JSON.stringify(error));
            hilog.info(0x0000, 'HomePage', '使用模拟数据作为备选方案');
            this.loadMockData();
            this.loadMockBannerData();
            this.loadingState = LoadingState.SUCCESS;
        }
    }
    /**
     * 加载轮播图模拟数据
     */
    private loadMockBannerData() {
        this.bannerList = [
            {
                id: 1,
                title: '热门应用推荐',
                subtitle: '发现最受欢迎的应用',
                image_url: 'https://picsum.photos/800/300?random=1',
                link_type: 'category',
                link_value: '1',
                sort_order: 1,
                is_active: true,
                created_at: '2024-01-01T00:00:00Z',
                updated_at: '2024-01-01T00:00:00Z'
            },
            {
                id: 2,
                title: '新品上架',
                subtitle: '体验最新发布的应用',
                image_url: 'https://picsum.photos/800/300?random=2',
                link_type: 'app',
                link_value: '1',
                sort_order: 2,
                is_active: true,
                created_at: '2024-01-01T00:00:00Z',
                updated_at: '2024-01-01T00:00:00Z'
            },
            {
                id: 3,
                title: '编辑精选',
                subtitle: '编辑为您精心挑选的优质应用',
                image_url: 'https://picsum.photos/800/300?random=3',
                link_type: 'url',
                link_value: 'https://example.com',
                sort_order: 3,
                is_active: true,
                created_at: '2024-01-01T00:00:00Z',
                updated_at: '2024-01-01T00:00:00Z'
            }
        ];
        hilog.info(0x0000, 'HomePage', '轮播图模拟数据加载完成，数量: %{public}d', this.bannerList.length);
    }
    /**
     * 加载模拟数据
     */
    private loadMockData(): void {
        hilog.info(0x0000, 'HomePage', '正在加载模拟数据...');
        // 模拟推荐应用数据
        const mockRecommendedApps: AppModel[] = [
            {
                id: 1,
                created_at: '2024-01-15T00:00:00Z',
                updated_at: '2024-01-15T00:00:00Z',
                name: '微信',
                package_name: 'com.tencent.wechat',
                description: '一个为智能终端提供即时通讯服务的免费应用程序',
                short_description: '即时通讯服务',
                icon: Constants.PLACEHOLDER_IMAGE,
                category_id: 1,
                category_name: '社交',
                developer_id: 1,
                developer_name: '腾讯科技',
                version: '8.0.32',
                version_code: 80032,
                min_sdk_version: 21,
                target_sdk_version: 33,
                size: 245000000,
                download_url: '',
                download_count: 1000000,
                rating: 4.8,
                review_count: 50000,
                screenshots: [Constants.PLACEHOLDER_IMAGE],
                permissions: [],
                tags: [],
                changelog: '',
                privacy_policy: '',
                support_email: '',
                website: '',
                status: 'published',
                is_featured: false,
                is_editor_choice: true,
                is_top: false,
                published_at: '2024-01-15T00:00:00Z',
                review_status: 'approved',
                review_reason: '',
                reviewed_at: '2024-01-15T00:00:00Z',
                reviewer_id: 1
            },
            {
                id: 2,
                created_at: '2024-01-10T00:00:00Z',
                updated_at: '2024-01-10T00:00:00Z',
                name: '支付宝',
                package_name: 'com.alipay.android',
                description: '数字生活开放平台',
                short_description: '数字生活平台',
                icon: Constants.PLACEHOLDER_IMAGE,
                category_id: 2,
                category_name: '金融',
                developer_id: 2,
                developer_name: '蚂蚁集团',
                version: '10.3.20',
                version_code: 103020,
                min_sdk_version: 21,
                target_sdk_version: 33,
                size: 156000000,
                download_url: '',
                download_count: 800000,
                rating: 4.7,
                review_count: 40000,
                screenshots: [Constants.PLACEHOLDER_IMAGE],
                permissions: [],
                tags: [],
                changelog: '',
                privacy_policy: '',
                support_email: '',
                website: '',
                status: 'published',
                is_featured: false,
                is_editor_choice: true,
                is_top: false,
                published_at: '2024-01-10T00:00:00Z',
                review_status: 'approved',
                review_reason: '',
                reviewed_at: '2024-01-10T00:00:00Z',
                reviewer_id: 1
            },
            {
                id: 3,
                created_at: '2024-01-12T00:00:00Z',
                updated_at: '2024-01-12T00:00:00Z',
                name: '抖音',
                package_name: 'com.douyin.android',
                description: '记录美好生活',
                short_description: '短视频平台',
                icon: Constants.PLACEHOLDER_IMAGE,
                category_id: 3,
                category_name: '娱乐',
                developer_id: 3,
                developer_name: '北京微播视界科技有限公司',
                version: '28.5.0',
                version_code: 285000,
                min_sdk_version: 21,
                target_sdk_version: 33,
                size: 189000000,
                download_url: '',
                download_count: 1200000,
                rating: 4.6,
                review_count: 60000,
                screenshots: [Constants.PLACEHOLDER_IMAGE],
                permissions: [],
                tags: [],
                changelog: '',
                privacy_policy: '',
                support_email: '',
                website: '',
                status: 'published',
                is_featured: false,
                is_editor_choice: true,
                is_top: false,
                published_at: '2024-01-12T00:00:00Z',
                review_status: 'approved',
                review_reason: '',
                reviewed_at: '2024-01-12T00:00:00Z',
                reviewer_id: 1
            }
        ];
        this.recommendedApps = mockRecommendedApps;
        hilog.info(0x0000, 'HomePage', '模拟数据加载完成: recommendedApps=%{public}d', this.recommendedApps.length);
    }
    /**
     * 检查并设置认证token
     */
    private async checkAndSetAuthToken(): Promise<boolean> {
        try {
            const context = this.getUIContext().getHostContext();
            const options: preferences.Options = { name: 'user_data' };
            const dataPreferences = preferences.getPreferencesSync(context, options);
            const token = dataPreferences.getSync('token', '') as string;
            if (token) {
                this.apiService.setAuthToken(token);
                return true;
            }
            return false;
        }
        catch (error) {
            hilog.error(0x0000, 'HomePage', 'Failed to check auth token: %{public}s', JSON.stringify(error));
            return false;
        }
    }
    /**
     * 清除过期的token
     */
    private async clearExpiredToken(): Promise<void> {
        try {
            const context = this.getUIContext().getHostContext();
            const options: preferences.Options = { name: 'user_data' };
            const dataPreferences = preferences.getPreferencesSync(context, options);
            dataPreferences.deleteSync('token');
            dataPreferences.flush();
            this.apiService.setAuthToken('');
        }
        catch (error) {
            hilog.error(0x0000, 'HomePage', '清除token失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 跳转到登录页面
     */
    private navigateToLogin(): void {
        this.getUIContext().getRouter().pushUrl({ url: 'pages/LoginPage' });
    }
    /**
     * 刷新数据
     */
    private async refreshData() {
        this.refreshing = true;
        await this.loadHomeData();
        this.refreshing = false;
    }
    /**
     * 开始轮播图自动播放
     */
    private startBannerAutoPlay() {
        if (this.bannerTimer !== -1) {
            clearInterval(this.bannerTimer);
        }
        this.bannerTimer = setInterval(() => {
            if (this.bannerList && this.bannerList.length > 0) {
                this.currentBannerIndex = (this.currentBannerIndex + 1) % this.bannerList.length;
                this.swiperController.showNext();
            }
        }, 3000);
    }
    /**
     * 停止轮播图自动播放
     */
    private stopBannerAutoPlay() {
        if (this.bannerTimer !== -1) {
            clearInterval(this.bannerTimer);
            this.bannerTimer = -1;
        }
    }
    /**
     * 搜索应用
     */
    private searchApps(keyword: string) {
        if (keyword.trim()) {
            this.getUIContext().getRouter().pushUrl({
                url: 'pages/SearchPage',
                params: { keyword: keyword.trim() }
            });
        }
    }
    /**
     * 跳转到应用详情
     */
    private navigateToAppDetail(app: AppModel) {
        this.getUIContext().getRouter().pushUrl({
            url: 'pages/AppDetailPage',
            params: { appId: app.id.toString() }
        });
    }
    /**
     * 跳转到分类页面
     */
    private navigateToCategory(category: CategoryModel) {
        this.getUIContext().getRouter().pushUrl({
            url: 'pages/CategoryPage',
            params: { categoryId: category.id.toString(), categoryName: category.name }
        });
    }
    /**
     * 跳转到应用列表
     */
    private navigateToAppList(title: string, params: AppSearchParams) {
        this.getUIContext().getRouter().pushUrl({
            url: 'pages/AppListPage',
            params: { title, searchParams: JSON.stringify(params) }
        });
    }
    /**
     * 顶部搜索栏
     */
    private TopSearchBar(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
            Row.padding({ left: '16vp', right: '16vp', top: '8vp', bottom: '8vp' });
            Row.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            __Common__.create();
            __Common__.layoutWeight(1);
        }, __Common__);
        {
            this.observeComponentCreation2((elmtId, isInitialRender) => {
                if (isInitialRender) {
                    let componentCall = new SearchBar(this, {
                        placeholder: '搜索应用',
                        searchText: this.searchKeyword,
                        showCancelButton: false,
                        onSearch: (keyword: string): void => this.searchApps(keyword),
                        onTextChange: (text: string): void => {
                            this.searchKeyword = text;
                        },
                        onSearchClick: (): void => {
                            // 点击搜索栏时跳转到搜索页面
                            this.getUIContext().getRouter().pushUrl({
                                url: 'pages/SearchPage',
                                params: { keyword: this.searchKeyword || '' }
                            });
                        }
                    }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/HomePage.ets", line: 422, col: 7 });
                    ViewPU.create(componentCall);
                    let paramsLambda = () => {
                        return {
                            placeholder: '搜索应用',
                            searchText: this.searchKeyword,
                            showCancelButton: false,
                            onSearch: (keyword: string): void => this.searchApps(keyword),
                            onTextChange: (text: string): void => {
                                this.searchKeyword = text;
                            },
                            onSearchClick: (): void => {
                                // 点击搜索栏时跳转到搜索页面
                                this.getUIContext().getRouter().pushUrl({
                                    url: 'pages/SearchPage',
                                    params: { keyword: this.searchKeyword || '' }
                                });
                            }
                        };
                    };
                    componentCall.paramsGenerator_ = paramsLambda;
                }
                else {
                    this.updateStateVarsOfChildByElmtId(elmtId, {
                        placeholder: '搜索应用',
                        showCancelButton: false
                    });
                }
            }, { name: "SearchBar" });
        }
        __Common__.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('📷');
            Text.width(24);
            Text.height(24);
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.margin({ left: 12 });
            Text.onClick(() => {
                // 扫码功能
            });
        }, Text);
        Text.pop();
        Row.pop();
    }
    /**
     * 轮播图组件
     */
    private BannerSwiper(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.bannerList && this.bannerList.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Swiper.create(this.swiperController);
                        Swiper.width('100%');
                        Swiper.height(180);
                        Swiper.autoPlay(true);
                        Swiper.interval(3000);
                        Swiper.indicator(new DotIndicator()
                            .itemWidth(8)
                            .itemHeight(8)
                            .selectedItemWidth(16)
                            .selectedItemHeight(8)
                            .color('rgba(255,255,255,0.4)')
                            .selectedColor(Color.White));
                        Swiper.curve(Curve.Linear);
                        Swiper.onChange((index: number) => {
                            this.currentBannerIndex = index;
                        });
                        Swiper.margin({ left: 16, right: 16, top: 8, bottom: 8 });
                    }, Swiper);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const banner = _item;
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Stack.create({ alignContent: Alignment.BottomStart });
                                Stack.width('100%');
                                Stack.height(180);
                                Stack.onClick(() => this.handleBannerClick(banner));
                            }, Stack);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Image.create(banner.image_url);
                                Image.width('100%');
                                Image.height(180);
                                Image.objectFit(ImageFit.Cover);
                                Image.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                                Image.alt({ "id": 16777253, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                            }, Image);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                // 渐变遮罩
                                Column.create();
                                // 渐变遮罩
                                Column.width('100%');
                                // 渐变遮罩
                                Column.height(80);
                                // 渐变遮罩
                                Column.linearGradient({
                                    direction: GradientDirection.Bottom,
                                    colors: [['rgba(0,0,0,0)', 0], ['rgba(0,0,0,0.6)', 1]]
                                });
                                // 渐变遮罩
                                Column.borderRadius({ bottomLeft: Constants.BORDER_RADIUS.MEDIUM, bottomRight: Constants.BORDER_RADIUS.MEDIUM });
                            }, Column);
                            // 渐变遮罩
                            Column.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                // 文字信息
                                Column.create({ space: 4 });
                                // 文字信息
                                Column.alignItems(HorizontalAlign.Start);
                                // 文字信息
                                Column.padding({ left: 16, right: 16, bottom: 16 });
                            }, Column);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create(banner.title);
                                Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
                                Text.fontWeight(FontWeight.Bold);
                                Text.fontColor(Color.White);
                                Text.maxLines(1);
                                Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                            }, Text);
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                If.create();
                                if (banner.subtitle) {
                                    this.ifElseBranchUpdateFunction(0, () => {
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(banner.subtitle);
                                            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                                            Text.fontColor('rgba(255,255,255,0.8)');
                                            Text.maxLines(1);
                                            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                                        }, Text);
                                        Text.pop();
                                    });
                                }
                                else {
                                    this.ifElseBranchUpdateFunction(1, () => {
                                    });
                                }
                            }, If);
                            If.pop();
                            // 文字信息
                            Column.pop();
                            Stack.pop();
                        };
                        this.forEachUpdateFunction(elmtId, this.bannerList, forEachItemGenFunction, undefined, true, false);
                    }, ForEach);
                    ForEach.pop();
                    Swiper.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    /**
     * 处理轮播图点击事件
     */
    private handleBannerClick(banner: BannerModel) {
        switch (banner.link_type) {
            case 'app':
                if (banner.link_value) {
                    this.getUIContext().getRouter().pushUrl({
                        url: 'pages/AppDetailPage',
                        params: { appId: banner.link_value }
                    });
                }
                break;
            case 'category':
                if (banner.link_value) {
                    this.getUIContext().getRouter().pushUrl({
                        url: 'pages/CategoryPage',
                        params: { categoryId: banner.link_value, categoryName: banner.title }
                    });
                }
                break;
            case 'url':
                if (banner.link_value) {
                    // 这里可以打开外部链接或内部页面
                    hilog.info(0x0000, 'HomePage', '点击轮播图链接: %{public}s', banner.link_value);
                }
                break;
            default:
                hilog.info(0x0000, 'HomePage', '点击轮播图: %{public}s', banner.title);
                break;
        }
    }
    /**
     * 推荐应用列表
     */
    private RecommendedApps(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.recommendedApps && this.recommendedApps.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.width('100%');
                        Row.padding({ left: '16vp', right: '16vp' });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('推荐应用');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('查看更多');
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor(Constants.COLORS.PRIMARY);
                        Text.onClick((): void => {
                            const params: AppSearchParams = { page: 1, page_size: 20 };
                            this.navigateToAppList('推荐应用', params);
                        });
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.deviceUtils.isTablet()) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    // 平板设备使用网格布局
                                    Grid.create();
                                    // 平板设备使用网格布局
                                    Grid.columnsTemplate('1fr 1fr 1fr');
                                    // 平板设备使用网格布局
                                    Grid.rowsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
                                    // 平板设备使用网格布局
                                    Grid.columnsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
                                    // 平板设备使用网格布局
                                    Grid.padding({ left: '16vp', right: '16vp' });
                                }, Grid);
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    ForEach.create();
                                    const forEachItemGenFunction = _item => {
                                        const app = _item;
                                        {
                                            const itemCreation2 = (elmtId, isInitialRender) => {
                                                GridItem.create(() => { }, false);
                                            };
                                            const observedDeepRender = () => {
                                                this.observeComponentCreation2(itemCreation2, GridItem);
                                                {
                                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                        if (isInitialRender) {
                                                            let componentCall = new AppCard(this, {
                                                                app: app,
                                                                cardType: 'grid',
                                                                onAppClick: (app: AppModel): void => this.navigateToAppDetail(app)
                                                            }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/HomePage.ets", line: 593, col: 17 });
                                                            ViewPU.create(componentCall);
                                                            let paramsLambda = () => {
                                                                return {
                                                                    app: app,
                                                                    cardType: 'grid',
                                                                    onAppClick: (app: AppModel): void => this.navigateToAppDetail(app)
                                                                };
                                                            };
                                                            componentCall.paramsGenerator_ = paramsLambda;
                                                        }
                                                        else {
                                                            this.updateStateVarsOfChildByElmtId(elmtId, {
                                                                app: app,
                                                                cardType: 'grid'
                                                            });
                                                        }
                                                    }, { name: "AppCard" });
                                                }
                                                GridItem.pop();
                                            };
                                            observedDeepRender();
                                        }
                                    };
                                    this.forEachUpdateFunction(elmtId, this.recommendedApps, forEachItemGenFunction);
                                }, ForEach);
                                ForEach.pop();
                                // 平板设备使用网格布局
                                Grid.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    // 手机设备使用列表布局
                                    Column.create();
                                }, Column);
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    ForEach.create();
                                    const forEachItemGenFunction = _item => {
                                        const app = _item;
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            __Common__.create();
                                            __Common__.margin({ left: 16, right: 16, bottom: 8 });
                                        }, __Common__);
                                        {
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                if (isInitialRender) {
                                                    let componentCall = new AppCard(this, {
                                                        app: app,
                                                        cardType: 'list',
                                                        onAppClick: (app: AppModel): void => this.navigateToAppDetail(app)
                                                    }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/HomePage.ets", line: 609, col: 15 });
                                                    ViewPU.create(componentCall);
                                                    let paramsLambda = () => {
                                                        return {
                                                            app: app,
                                                            cardType: 'list',
                                                            onAppClick: (app: AppModel): void => this.navigateToAppDetail(app)
                                                        };
                                                    };
                                                    componentCall.paramsGenerator_ = paramsLambda;
                                                }
                                                else {
                                                    this.updateStateVarsOfChildByElmtId(elmtId, {
                                                        app: app,
                                                        cardType: 'list'
                                                    });
                                                }
                                            }, { name: "AppCard" });
                                        }
                                        __Common__.pop();
                                    };
                                    this.forEachUpdateFunction(elmtId, this.recommendedApps, forEachItemGenFunction);
                                }, ForEach);
                                ForEach.pop();
                                // 手机设备使用列表布局
                                Column.pop();
                            });
                        }
                    }, If);
                    If.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    /**
     * 快速入口
     */
    private QuickActions(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) });
            Row.width('100%');
            Row.padding({ left: '16vp', right: '16vp', top: '16vp', bottom: '16vp' });
            Row.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Row.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Row.margin({ left: 16, right: 16, top: 8, bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 8 });
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.layoutWeight(1);
            Column.onClick((): void => {
                const params: AppSearchParams = { sort: 'downloadCount', page: 1, page_size: 20 };
                this.navigateToAppList('应用排行榜', params);
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🏆');
            Text.width(32);
            Text.height(32);
            Text.fontColor(Constants.COLORS.PRIMARY);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('排行榜');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 8 });
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.layoutWeight(1);
            Column.onClick(() => {
                const params: AppSearchParams = { sort: 'createdAt', page: 1, page_size: 20 };
                this.navigateToAppList('最新上架', params);
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🆕');
            Text.width(32);
            Text.height(32);
            Text.fontColor(Constants.COLORS.SUCCESS);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('最新上架');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 8 });
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.layoutWeight(1);
            Column.onClick((): void => {
                const params: AppSearchParams = { sort: 'updatedAt', page: 1, page_size: 20 };
                this.navigateToAppList('最近更新', params);
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🔄');
            Text.width(32);
            Text.height(32);
            Text.fontColor(Constants.COLORS.WARNING);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('最近更新');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 8 });
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.layoutWeight(1);
            Column.onClick(() => {
                const params: AppSearchParams = { page: 1, page_size: 20 };
                this.navigateToAppList('免费应用', params);
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🆓');
            Text.width(32);
            Text.height(32);
            Text.fontColor(Constants.COLORS.PRIMARY);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('免费应用');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, { state: LoadingState.LOADING }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/HomePage.ets", line: 706, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.LOADING
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.LOADING
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.needLogin) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    // 需要登录的特殊错误状态
                                    Column.create({ space: 16 });
                                    // 需要登录的特殊错误状态
                                    Column.width('100%');
                                    // 需要登录的特殊错误状态
                                    Column.height('100%');
                                    // 需要登录的特殊错误状态
                                    Column.justifyContent(FlexAlign.Center);
                                    // 需要登录的特殊错误状态
                                    Column.alignItems(HorizontalAlign.Center);
                                    // 需要登录的特殊错误状态
                                    Column.layoutWeight(1);
                                }, Column);
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Image.create({ "id": 16777253, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                                    Image.width(64);
                                    Image.height(64);
                                    Image.objectFit(ImageFit.Contain);
                                    Image.fillColor({ "id": 125829240, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                                }, Image);
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('请先登录');
                                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
                                    Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                                    Text.textAlign(TextAlign.Center);
                                }, Text);
                                Text.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('登录后即可浏览应用商店的精彩内容');
                                    Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                                    Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                                    Text.textAlign(TextAlign.Center);
                                    Text.maxLines(2);
                                }, Text);
                                Text.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Button.createWithLabel('立即登录');
                                    Button.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                                    Button.backgroundColor(Constants.COLORS.PRIMARY);
                                    Button.fontColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                                    Button.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                                    Button.padding({ left: 32, right: 32, top: 12, bottom: 12 });
                                    Button.onClick(() => {
                                        this.navigateToLogin();
                                    });
                                }, Button);
                                Button.pop();
                                // 需要登录的特殊错误状态
                                Column.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    __Common__.create();
                                    __Common__.layoutWeight(1);
                                }, __Common__);
                                {
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        if (isInitialRender) {
                                            let componentCall = new LoadingView(this, {
                                                state: LoadingState.ERROR,
                                                onRetry: () => this.loadHomeData()
                                            }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/HomePage.ets", line: 745, col: 11 });
                                            ViewPU.create(componentCall);
                                            let paramsLambda = () => {
                                                return {
                                                    state: LoadingState.ERROR,
                                                    onRetry: () => this.loadHomeData()
                                                };
                                            };
                                            componentCall.paramsGenerator_ = paramsLambda;
                                        }
                                        else {
                                            this.updateStateVarsOfChildByElmtId(elmtId, {
                                                state: LoadingState.ERROR
                                            });
                                        }
                                    }, { name: "LoadingView" });
                                }
                                __Common__.pop();
                            });
                        }
                    }, If);
                    If.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                    }, Column);
                    // 顶部搜索栏
                    this.TopSearchBar.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 内容区域
                        Refresh.create({ refreshing: this.refreshing, offset: 64, friction: 100 });
                        // 内容区域
                        Refresh.onStateChange((refreshStatus: RefreshStatus) => {
                            if (refreshStatus === RefreshStatus.Refresh) {
                                this.refreshData();
                            }
                        });
                        // 内容区域
                        Refresh.layoutWeight(1);
                    }, Refresh);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Scroll.create();
                        Scroll.scrollable(ScrollDirection.Vertical);
                        Scroll.scrollBar(BarState.Auto);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) });
                    }, Column);
                    // 轮播图
                    this.BannerSwiper.bind(this)();
                    // 快速入口
                    this.QuickActions.bind(this)();
                    // 推荐应用
                    this.RecommendedApps.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 底部间距
                        Column.create();
                        // 底部间距
                        Column.height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE));
                    }, Column);
                    // 底部间距
                    Column.pop();
                    Column.pop();
                    Scroll.pop();
                    // 内容区域
                    Refresh.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "HomePage";
    }
}
registerNamedRoute(() => new HomePage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/HomePage", pageFullPath: "entry/src/main/ets/pages/HomePage", integratedHsp: "false", moduleType: "followWithHap" });
