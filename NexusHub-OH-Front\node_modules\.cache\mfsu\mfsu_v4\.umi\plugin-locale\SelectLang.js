"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { useState } from "react";
import { Menu, version, Dropdown } from "antd";
import { getLocale, getAllLocales, setLocale } from "./localeExports";
const HeaderDropdown = ({
  overlayClassName: cls,
  ...restProps
}) => /* @__PURE__ */ jsx(
  Dropdown,
  {
    overlayClassName: cls,
    ...restProps
  }
);
const transformArrayToObject = (allLangUIConfig) => {
  return allLangUIConfig.reduce((obj, item) => {
    if (!item.lang) {
      return obj;
    }
    return {
      ...obj,
      [item.lang]: item
    };
  }, {});
};
const defaultLangUConfigMap = {
  "ar-EG": {
    lang: "ar-EG",
    label: "\u0627\u0644\u0639\u0631\u0628\u064A\u0629",
    icon: "\u{1F1EA}\u{1F1EC}",
    title: "\u0644\u063A\u0629"
  },
  "az-AZ": {
    lang: "az-AZ",
    label: "Az\u0259rbaycan dili",
    icon: "\u{1F1E6}\u{1F1FF}",
    title: "Dil"
  },
  "bg-BG": {
    lang: "bg-BG",
    label: "\u0411\u044A\u043B\u0433\u0430\u0440\u0441\u043A\u0438 \u0435\u0437\u0438\u043A",
    icon: "\u{1F1E7}\u{1F1EC}",
    title: "\u0435\u0437\u0438\u043A"
  },
  "bn-BD": {
    lang: "bn-BD",
    label: "\u09AC\u09BE\u0982\u09B2\u09BE",
    icon: "\u{1F1E7}\u{1F1E9}",
    title: "\u09AD\u09BE\u09B7\u09BE"
  },
  "ca-ES": {
    lang: "ca-ES",
    label: "Catal\xE1",
    icon: "\u{1F1E8}\u{1F1E6}",
    title: "llengua"
  },
  "cs-CZ": {
    lang: "cs-CZ",
    label: "\u010Ce\u0161tina",
    icon: "\u{1F1E8}\u{1F1FF}",
    title: "Jazyk"
  },
  "da-DK": {
    lang: "da-DK",
    label: "Dansk",
    icon: "\u{1F1E9}\u{1F1F0}",
    title: "Sprog"
  },
  "de-DE": {
    lang: "de-DE",
    label: "Deutsch",
    icon: "\u{1F1E9}\u{1F1EA}",
    title: "Sprache"
  },
  "el-GR": {
    lang: "el-GR",
    label: "\u0395\u03BB\u03BB\u03B7\u03BD\u03B9\u03BA\u03AC",
    icon: "\u{1F1EC}\u{1F1F7}",
    title: "\u0393\u03BB\u03CE\u03C3\u03C3\u03B1"
  },
  "en-GB": {
    lang: "en-GB",
    label: "English",
    icon: "\u{1F1EC}\u{1F1E7}",
    title: "Language"
  },
  "en-US": {
    lang: "en-US",
    label: "English",
    icon: "\u{1F1FA}\u{1F1F8}",
    title: "Language"
  },
  "es-ES": {
    lang: "es-ES",
    label: "Espa\xF1ol",
    icon: "\u{1F1EA}\u{1F1F8}",
    title: "Idioma"
  },
  "et-EE": {
    lang: "et-EE",
    label: "Eesti",
    icon: "\u{1F1EA}\u{1F1EA}",
    title: "Keel"
  },
  "fa-IR": {
    lang: "fa-IR",
    label: "\u0641\u0627\u0631\u0633\u06CC",
    icon: "\u{1F1EE}\u{1F1F7}",
    title: "\u0632\u0628\u0627\u0646"
  },
  "fi-FI": {
    lang: "fi-FI",
    label: "Suomi",
    icon: "\u{1F1EB}\u{1F1EE}",
    title: "Kieli"
  },
  "fr-BE": {
    lang: "fr-BE",
    label: "Fran\xE7ais",
    icon: "\u{1F1E7}\u{1F1EA}",
    title: "Langue"
  },
  "fr-FR": {
    lang: "fr-FR",
    label: "Fran\xE7ais",
    icon: "\u{1F1EB}\u{1F1F7}",
    title: "Langue"
  },
  "ga-IE": {
    lang: "ga-IE",
    label: "Gaeilge",
    icon: "\u{1F1EE}\u{1F1EA}",
    title: "Teanga"
  },
  "he-IL": {
    lang: "he-IL",
    label: "\u05E2\u05D1\u05E8\u05D9\u05EA",
    icon: "\u{1F1EE}\u{1F1F1}",
    title: "\u05E9\u05E4\u05D4"
  },
  "hi-IN": {
    lang: "hi-IN",
    label: "\u0939\u093F\u0928\u094D\u0926\u0940, \u0939\u093F\u0902\u0926\u0940",
    icon: "\u{1F1EE}\u{1F1F3}",
    title: "\u092D\u093E\u0937\u093E: \u0939\u093F\u0928\u094D\u0926\u0940"
  },
  "hr-HR": {
    lang: "hr-HR",
    label: "Hrvatski jezik",
    icon: "\u{1F1ED}\u{1F1F7}",
    title: "Jezik"
  },
  "hu-HU": {
    lang: "hu-HU",
    label: "Magyar",
    icon: "\u{1F1ED}\u{1F1FA}",
    title: "Nyelv"
  },
  "hy-AM": {
    lang: "hu-HU",
    label: "\u0540\u0561\u0575\u0565\u0580\u0565\u0576",
    icon: "\u{1F1E6}\u{1F1F2}",
    title: "\u053C\u0565\u0566\u0578\u0582"
  },
  "id-ID": {
    lang: "id-ID",
    label: "Bahasa Indonesia",
    icon: "\u{1F1EE}\u{1F1E9}",
    title: "Bahasa"
  },
  "it-IT": {
    lang: "it-IT",
    label: "Italiano",
    icon: "\u{1F1EE}\u{1F1F9}",
    title: "Linguaggio"
  },
  "is-IS": {
    lang: "is-IS",
    label: "\xCDslenska",
    icon: "\u{1F1EE}\u{1F1F8}",
    title: "Tungum\xE1l"
  },
  "ja-JP": {
    lang: "ja-JP",
    label: "\u65E5\u672C\u8A9E",
    icon: "\u{1F1EF}\u{1F1F5}",
    title: "\u8A00\u8A9E"
  },
  "ku-IQ": {
    lang: "ku-IQ",
    label: "\u06A9\u0648\u0631\u062F\u06CC",
    icon: "\u{1F1EE}\u{1F1F6}",
    title: "Ziman"
  },
  "kn-IN": {
    lang: "kn-IN",
    label: "\u0C95\u0CA8\u0CCD\u0CA8\u0CA1",
    icon: "\u{1F1EE}\u{1F1F3}",
    title: "\u0CAD\u0CBE\u0CB7\u0CC6"
  },
  "ko-KR": {
    lang: "ko-KR",
    label: "\uD55C\uAD6D\uC5B4",
    icon: "\u{1F1F0}\u{1F1F7}",
    title: "\uC5B8\uC5B4"
  },
  "lv-LV": {
    lang: "lv-LV",
    label: "Latvie\u0161u valoda",
    icon: "\u{1F1F1}\u{1F1EE}",
    title: "Kalba"
  },
  "mk-MK": {
    lang: "mk-MK",
    label: "\u043C\u0430\u043A\u0435\u0434\u043E\u043D\u0441\u043A\u0438 \u0458\u0430\u0437\u0438\u043A",
    icon: "\u{1F1F2}\u{1F1F0}",
    title: "\u0408\u0430\u0437\u0438\u043A"
  },
  "mn-MN": {
    lang: "mn-MN",
    label: "\u041C\u043E\u043D\u0433\u043E\u043B \u0445\u044D\u043B",
    icon: "\u{1F1F2}\u{1F1F3}",
    title: "\u0425\u044D\u043B"
  },
  "ms-MY": {
    lang: "ms-MY",
    label: "\u0628\u0647\u0627\u0633 \u0645\u0644\u0627\u064A\u0648\u200E",
    icon: "\u{1F1F2}\u{1F1FE}",
    title: "Bahasa"
  },
  "nb-NO": {
    lang: "nb-NO",
    label: "Norsk",
    icon: "\u{1F1F3}\u{1F1F4}",
    title: "Spr\xE5k"
  },
  "ne-NP": {
    lang: "ne-NP",
    label: "\u0928\u0947\u092A\u093E\u0932\u0940",
    icon: "\u{1F1F3}\u{1F1F5}",
    title: "\u092D\u093E\u0937\u093E"
  },
  "nl-BE": {
    lang: "nl-BE",
    label: "Vlaams",
    icon: "\u{1F1E7}\u{1F1EA}",
    title: "Taal"
  },
  "nl-NL": {
    lang: "nl-NL",
    label: "Nederlands",
    icon: "\u{1F1F3}\u{1F1F1}",
    title: "Taal"
  },
  "pl-PL": {
    lang: "pl-PL",
    label: "Polski",
    icon: "\u{1F1F5}\u{1F1F1}",
    title: "J\u0119zyk"
  },
  "pt-BR": {
    lang: "pt-BR",
    label: "Portugu\xEAs",
    icon: "\u{1F1E7}\u{1F1F7}",
    title: "Idiomas"
  },
  "pt-PT": {
    lang: "pt-PT",
    label: "Portugu\xEAs",
    icon: "\u{1F1F5}\u{1F1F9}",
    title: "Idiomas"
  },
  "ro-RO": {
    lang: "ro-RO",
    label: "Rom\xE2n\u0103",
    icon: "\u{1F1F7}\u{1F1F4}",
    title: "Limba"
  },
  "ru-RU": {
    lang: "ru-RU",
    label: "\u0420\u0443\u0441\u0441\u043A\u0438\u0439",
    icon: "\u{1F1F7}\u{1F1FA}",
    title: "\u044F\u0437\u044B\u043A"
  },
  "sk-SK": {
    lang: "sk-SK",
    label: "Sloven\u010Dina",
    icon: "\u{1F1F8}\u{1F1F0}",
    title: "Jazyk"
  },
  "sr-RS": {
    lang: "sr-RS",
    label: "\u0441\u0440\u043F\u0441\u043A\u0438 \u0458\u0435\u0437\u0438\u043A",
    icon: "\u{1F1F8}\u{1F1F7}",
    title: "\u0408\u0435\u0437\u0438\u043A"
  },
  "sl-SI": {
    lang: "sl-SI",
    label: "Sloven\u0161\u010Dina",
    icon: "\u{1F1F8}\u{1F1F1}",
    title: "Jezik"
  },
  "sv-SE": {
    lang: "sv-SE",
    label: "Svenska",
    icon: "\u{1F1F8}\u{1F1EA}",
    title: "Spr\xE5k"
  },
  "ta-IN": {
    lang: "ta-IN",
    label: "\u0BA4\u0BAE\u0BBF\u0BB4\u0BCD",
    icon: "\u{1F1EE}\u{1F1F3}",
    title: "\u0BAE\u0BCA\u0BB4\u0BBF"
  },
  "th-TH": {
    lang: "th-TH",
    label: "\u0E44\u0E17\u0E22",
    icon: "\u{1F1F9}\u{1F1ED}",
    title: "\u0E20\u0E32\u0E29\u0E32"
  },
  "tr-TR": {
    lang: "tr-TR",
    label: "T\xFCrk\xE7e",
    icon: "\u{1F1F9}\u{1F1F7}",
    title: "Dil"
  },
  "uk-UA": {
    lang: "uk-UA",
    label: "\u0423\u043A\u0440\u0430\u0457\u043D\u0441\u044C\u043A\u0430",
    icon: "\u{1F1FA}\u{1F1F0}",
    title: "\u041C\u043E\u0432\u0430"
  },
  "vi-VN": {
    lang: "vi-VN",
    label: "Ti\u1EBFng Vi\u1EC7t",
    icon: "\u{1F1FB}\u{1F1F3}",
    title: "Ng\xF4n ng\u1EEF"
  },
  "zh-CN": {
    lang: "zh-CN",
    label: "\u7B80\u4F53\u4E2D\u6587",
    icon: "\u{1F1E8}\u{1F1F3}",
    title: "\u8BED\u8A00"
  },
  "zh-TW": {
    lang: "zh-TW",
    label: "\u7E41\u9AD4\u4E2D\u6587",
    icon: "\u{1F1ED}\u{1F1F0}",
    title: "\u8A9E\u8A00"
  }
};
export const SelectLang = (props) => {
  const {
    globalIconClassName,
    postLocalesData,
    onItemClick,
    icon,
    style,
    reload,
    ...restProps
  } = props;
  const [selectedLang, setSelectedLang] = useState(() => getLocale());
  const changeLang = ({ key }) => {
    setLocale(key, reload);
    setSelectedLang(getLocale());
  };
  const defaultLangUConfig = getAllLocales().map(
    (key) => defaultLangUConfigMap[key] || {
      lang: key,
      label: key,
      icon: "\u{1F310}",
      title: key
    }
  );
  const allLangUIConfig = postLocalesData?.(defaultLangUConfig) || defaultLangUConfig;
  const handleClick = onItemClick ? (params) => onItemClick(params) : changeLang;
  const menuItemStyle = { minWidth: "160px" };
  const menuItemIconStyle = { marginRight: "8px" };
  const langMenu = {
    selectedKeys: [selectedLang],
    onClick: handleClick,
    items: allLangUIConfig.map((localeObj) => ({
      key: localeObj.lang || localeObj.key,
      style: menuItemStyle,
      label: /* @__PURE__ */ jsxs(Fragment, { children: [
        /* @__PURE__ */ jsx("span", { role: "img", "aria-label": localeObj?.label || "en-US", style: menuItemIconStyle, children: localeObj?.icon || "\u{1F310}" }),
        localeObj?.label || "en-US"
      ] })
    }))
  };
  let dropdownProps;
  if (version.startsWith("5.") || version.startsWith("4.24.")) {
    dropdownProps = { menu: langMenu };
  } else if (version.startsWith("3.")) {
    dropdownProps = {
      overlay: /* @__PURE__ */ jsx(Menu, { children: langMenu.items.map((item) => /* @__PURE__ */ jsx(Menu.Item, { onClick: item.onClick, children: item.label }, item.key)) })
    };
  } else {
    dropdownProps = { overlay: /* @__PURE__ */ jsx(Menu, { ...langMenu }) };
  }
  const inlineStyle = {
    cursor: "pointer",
    padding: "12px",
    display: "inline-flex",
    alignItems: "center",
    justifyContent: "center",
    fontSize: 18,
    verticalAlign: "middle",
    ...style
  };
  return /* @__PURE__ */ jsx(HeaderDropdown, { ...dropdownProps, placement: "bottomRight", ...restProps, children: /* @__PURE__ */ jsx("span", { className: globalIconClassName, style: inlineStyle, children: /* @__PURE__ */ jsx("i", { className: "anticon", title: allLangUIConfig[selectedLang]?.title, children: icon ? icon : /* @__PURE__ */ jsxs(
    "svg",
    {
      viewBox: "0 0 24 24",
      focusable: "false",
      width: "1em",
      height: "1em",
      fill: "currentColor",
      "aria-hidden": "true",
      children: [
        /* @__PURE__ */ jsx("path", { d: "M0 0h24v24H0z", fill: "none" }),
        /* @__PURE__ */ jsx(
          "path",
          {
            d: "M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z ",
            className: "css-c4d79v"
          }
        )
      ]
    }
  ) }) }) });
  return /* @__PURE__ */ jsx(Fragment, {});
};
