"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { PlusOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { createElement } from "react";
import useStyles from "./index.style";
const EditableLinkGroup = ({
  links = [],
  onAdd = () => {
  },
  linkElement = "a"
}) => {
  const { styles } = useStyles();
  return /* @__PURE__ */ jsxs("div", { className: styles.linkGroup, children: [
    links.map(
      (link) => createElement(
        linkElement,
        {
          key: `linkGroup-item-${link.id || link.title}`,
          to: link.href,
          href: link.href
        },
        link.title
      )
    ),
    /* @__PURE__ */ jsxs(Button, { size: "small", type: "primary", ghost: true, onClick: onAdd, children: [
      /* @__PURE__ */ jsx(PlusOutlined, {}),
      " \u6DFB\u52A0"
    ] })
  ] });
};
export default EditableLinkGroup;
