{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "ea4f9ba1-919c-4d10-9412-3d5455705743", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159206094500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0801728-9488-4c2a-b8ca-f3407d753802", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159206375300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac4ebc94-7fd5-4cb4-b49b-6438572998c9", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159238704000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa93f871-35fa-467a-a0f3-c89d132dbf9b", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159239156200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e95e0af-cd9e-4203-b538-74d17d373844", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159240945800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24fbb52e-be1a-4d11-815a-19ef17d5f085", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159241272500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd7d2c57-2cf7-4fbf-a8ed-65e72a76adf7", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135721378600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e6e1e2d-6c90-455a-8e75-071be8b7a93c", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135735352700, "endTime": 157135982717000}, "additional": {"children": ["c31c4f85-d54d-48b1-9f52-5c5d0dc97608", "fda18780-2e76-4b5d-a0f1-293ece24e83b", "2ba23231-40cd-4ff9-9d8d-5a437f0952eb", "5538aafa-1a0b-45e5-b884-0274184d5968", "b6200866-c438-4e58-97ca-85e5465bc0d1", "f21f90f2-f228-476c-ac09-629a383c4232", "b4ae4d4b-002f-47bb-94ae-511e9c40ae73"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "a1fa89ed-69c8-4d9a-8088-907ced0ebe9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c31c4f85-d54d-48b1-9f52-5c5d0dc97608", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135735355800, "endTime": 157135760586100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6e6e1e2d-6c90-455a-8e75-071be8b7a93c", "logId": "1883054b-e7ac-4966-ad1f-40b9e3e6f1c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fda18780-2e76-4b5d-a0f1-293ece24e83b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135760607200, "endTime": 157135981014200}, "additional": {"children": ["cdb8a2ad-7edf-4f56-a15d-f27fe3bf8218", "eab9c8ad-6836-47fe-87c7-8902972b800c", "6702cd73-cfe0-4bee-b305-491bbb767ffb", "9c868b88-7e44-476e-bc4c-a5928596b517", "2c33d451-b806-4f5f-9b35-cd50b004b1cb", "b0b8c1b6-2b55-44cc-a343-be91cd7e7f58", "1bb213fe-2fe2-4876-96b5-ea434c6aa9ba", "f6727c07-7c37-4709-86b5-63ee94841535", "be95a858-10fd-40d1-b48a-311318f318b6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6e6e1e2d-6c90-455a-8e75-071be8b7a93c", "logId": "43fb307d-b692-4b46-8ac2-4ccd848945f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ba23231-40cd-4ff9-9d8d-5a437f0952eb", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135981045700, "endTime": 157135982691000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6e6e1e2d-6c90-455a-8e75-071be8b7a93c", "logId": "228ea7ec-439b-453f-9466-cfae27288ba4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5538aafa-1a0b-45e5-b884-0274184d5968", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135982696700, "endTime": 157135982710800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6e6e1e2d-6c90-455a-8e75-071be8b7a93c", "logId": "f96875fc-d2b6-45a2-9aec-0641d05ed18b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6200866-c438-4e58-97ca-85e5465bc0d1", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135743037300, "endTime": 157135743188600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6e6e1e2d-6c90-455a-8e75-071be8b7a93c", "logId": "469eff71-2a3d-40c9-9bc9-705eaea5c089"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "469eff71-2a3d-40c9-9bc9-705eaea5c089", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135743037300, "endTime": 157135743188600}, "additional": {"logType": "info", "children": [], "durationId": "b6200866-c438-4e58-97ca-85e5465bc0d1", "parent": "a1fa89ed-69c8-4d9a-8088-907ced0ebe9a"}}, {"head": {"id": "f21f90f2-f228-476c-ac09-629a383c4232", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135753326800, "endTime": 157135753442900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6e6e1e2d-6c90-455a-8e75-071be8b7a93c", "logId": "4cab2d71-422c-478b-a74b-20c8c3d18a5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cab2d71-422c-478b-a74b-20c8c3d18a5e", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135753326800, "endTime": 157135753442900}, "additional": {"logType": "info", "children": [], "durationId": "f21f90f2-f228-476c-ac09-629a383c4232", "parent": "a1fa89ed-69c8-4d9a-8088-907ced0ebe9a"}}, {"head": {"id": "a4fca7b5-681f-4316-a467-e13c0980e117", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135753612700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a5dbdd7-523e-4e7a-98e9-0bce51842a24", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135760415800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1883054b-e7ac-4966-ad1f-40b9e3e6f1c0", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135735355800, "endTime": 157135760586100}, "additional": {"logType": "info", "children": [], "durationId": "c31c4f85-d54d-48b1-9f52-5c5d0dc97608", "parent": "a1fa89ed-69c8-4d9a-8088-907ced0ebe9a"}}, {"head": {"id": "cdb8a2ad-7edf-4f56-a15d-f27fe3bf8218", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135768970800, "endTime": 157135768979400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fda18780-2e76-4b5d-a0f1-293ece24e83b", "logId": "b824ccbe-8002-435f-b2d4-7f02357b4943"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eab9c8ad-6836-47fe-87c7-8902972b800c", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135768999700, "endTime": 157135774275100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fda18780-2e76-4b5d-a0f1-293ece24e83b", "logId": "7fa10106-8eb8-44ed-abc2-a1e1670d9a61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6702cd73-cfe0-4bee-b305-491bbb767ffb", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135774289100, "endTime": 157135855448000}, "additional": {"children": ["bdbbc632-5fd7-4715-9715-660f5adb8b70", "dd7633dd-3084-4cba-ae0d-52ed1bcf4e92", "cb422e5e-1a56-4ccc-b3fe-f32270e40ad9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fda18780-2e76-4b5d-a0f1-293ece24e83b", "logId": "6af1805b-a3bc-43e6-8708-e318b81c7884"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c868b88-7e44-476e-bc4c-a5928596b517", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135855460000, "endTime": 157135879811000}, "additional": {"children": ["2e6a149a-8483-4606-946c-5241e8ccefb5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fda18780-2e76-4b5d-a0f1-293ece24e83b", "logId": "9235e776-feba-49f4-9b6d-058bb3862e85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c33d451-b806-4f5f-9b35-cd50b004b1cb", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135879831200, "endTime": 157135945175200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fda18780-2e76-4b5d-a0f1-293ece24e83b", "logId": "862a8567-5467-40f6-8cfc-fb5a54849d71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b0b8c1b6-2b55-44cc-a343-be91cd7e7f58", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135946073900, "endTime": 157135964521800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fda18780-2e76-4b5d-a0f1-293ece24e83b", "logId": "8bb68c59-561d-451f-8c54-cdd27540ca28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1bb213fe-2fe2-4876-96b5-ea434c6aa9ba", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135964542200, "endTime": 157135980841600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fda18780-2e76-4b5d-a0f1-293ece24e83b", "logId": "6f559d7c-a44e-4ce4-a847-22ef525ddfdf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6727c07-7c37-4709-86b5-63ee94841535", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135980862500, "endTime": 157135981000300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fda18780-2e76-4b5d-a0f1-293ece24e83b", "logId": "206346fe-48e7-4d7b-bce8-47fc473ee9cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b824ccbe-8002-435f-b2d4-7f02357b4943", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135768970800, "endTime": 157135768979400}, "additional": {"logType": "info", "children": [], "durationId": "cdb8a2ad-7edf-4f56-a15d-f27fe3bf8218", "parent": "43fb307d-b692-4b46-8ac2-4ccd848945f7"}}, {"head": {"id": "7fa10106-8eb8-44ed-abc2-a1e1670d9a61", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135768999700, "endTime": 157135774275100}, "additional": {"logType": "info", "children": [], "durationId": "eab9c8ad-6836-47fe-87c7-8902972b800c", "parent": "43fb307d-b692-4b46-8ac2-4ccd848945f7"}}, {"head": {"id": "bdbbc632-5fd7-4715-9715-660f5adb8b70", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135776074100, "endTime": 157135776103300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6702cd73-cfe0-4bee-b305-491bbb767ffb", "logId": "b86bc324-17cb-4558-84ce-7b0759f0529e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b86bc324-17cb-4558-84ce-7b0759f0529e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135776074100, "endTime": 157135776103300}, "additional": {"logType": "info", "children": [], "durationId": "bdbbc632-5fd7-4715-9715-660f5adb8b70", "parent": "6af1805b-a3bc-43e6-8708-e318b81c7884"}}, {"head": {"id": "dd7633dd-3084-4cba-ae0d-52ed1bcf4e92", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135779042300, "endTime": 157135854849700}, "additional": {"children": ["287ac78b-1cf8-4386-a544-4248b3f70759", "1d1d0608-5425-4fd7-9b76-6ff470a3ba07"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6702cd73-cfe0-4bee-b305-491bbb767ffb", "logId": "e2f22c5d-0f6c-484f-90b8-67475c6c2997"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "287ac78b-1cf8-4386-a544-4248b3f70759", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135779043600, "endTime": 157135784880900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dd7633dd-3084-4cba-ae0d-52ed1bcf4e92", "logId": "d7cd1882-447b-4cc1-88ec-c8d54d426aba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d1d0608-5425-4fd7-9b76-6ff470a3ba07", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135784899900, "endTime": 157135854836600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dd7633dd-3084-4cba-ae0d-52ed1bcf4e92", "logId": "9a1cf4aa-3b02-46b8-a0fe-529777328af1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b1abcd3-7719-4630-abc1-22a5de15dcd2", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135779047200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f84936b-3642-4e5a-b837-7842d6779f87", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135784713100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7cd1882-447b-4cc1-88ec-c8d54d426aba", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135779043600, "endTime": 157135784880900}, "additional": {"logType": "info", "children": [], "durationId": "287ac78b-1cf8-4386-a544-4248b3f70759", "parent": "e2f22c5d-0f6c-484f-90b8-67475c6c2997"}}, {"head": {"id": "0149a568-e05d-41c9-be77-b9408fb5cd6a", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135784913700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b2f1248-5a2b-47c7-b8c9-109b5851524e", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135794361400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e578f0b7-d775-414e-ad14-52e578e90dcd", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135794455500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5de4fa8f-957f-4f2b-80c1-c2ac9641254e", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135794538300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e65ebc85-8af8-4b4a-a64e-86dccf2afbd1", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135794588800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5a1b815-a226-4714-a431-b116f3071f09", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135795594000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "679998d5-7800-458d-9cb4-6f245e8afa92", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135808637400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcb99031-93f0-450e-a536-d06e61954dbc", "name": "Sdk init in 34 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135832577400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c54f66f2-4496-4173-a07a-c691b39a1801", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135832764800}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 36, "second": 5}, "markType": "other"}}, {"head": {"id": "84ed473d-598a-43d2-8a93-48c8f08a1058", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135832778800}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 36, "second": 5}, "markType": "other"}}, {"head": {"id": "ff3328c6-dfa9-40ef-8626-d4985a9ebc21", "name": "Project task initialization takes 20 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135853946200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79d5cc0a-9e08-4afc-9d1f-bae224ab6c28", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135854118400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35f3b429-906d-4f37-8080-4cfca0093a53", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135854197500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c164e1a4-9275-4536-8e8c-36f39975313d", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135854227700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a1cf4aa-3b02-46b8-a0fe-529777328af1", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135784899900, "endTime": 157135854836600}, "additional": {"logType": "info", "children": [], "durationId": "1d1d0608-5425-4fd7-9b76-6ff470a3ba07", "parent": "e2f22c5d-0f6c-484f-90b8-67475c6c2997"}}, {"head": {"id": "e2f22c5d-0f6c-484f-90b8-67475c6c2997", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135779042300, "endTime": 157135854849700}, "additional": {"logType": "info", "children": ["d7cd1882-447b-4cc1-88ec-c8d54d426aba", "9a1cf4aa-3b02-46b8-a0fe-529777328af1"], "durationId": "dd7633dd-3084-4cba-ae0d-52ed1bcf4e92", "parent": "6af1805b-a3bc-43e6-8708-e318b81c7884"}}, {"head": {"id": "cb422e5e-1a56-4ccc-b3fe-f32270e40ad9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135855424100, "endTime": 157135855438400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6702cd73-cfe0-4bee-b305-491bbb767ffb", "logId": "59c55806-c12c-43df-8b53-033234827943"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59c55806-c12c-43df-8b53-033234827943", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135855424100, "endTime": 157135855438400}, "additional": {"logType": "info", "children": [], "durationId": "cb422e5e-1a56-4ccc-b3fe-f32270e40ad9", "parent": "6af1805b-a3bc-43e6-8708-e318b81c7884"}}, {"head": {"id": "6af1805b-a3bc-43e6-8708-e318b81c7884", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135774289100, "endTime": 157135855448000}, "additional": {"logType": "info", "children": ["b86bc324-17cb-4558-84ce-7b0759f0529e", "e2f22c5d-0f6c-484f-90b8-67475c6c2997", "59c55806-c12c-43df-8b53-033234827943"], "durationId": "6702cd73-cfe0-4bee-b305-491bbb767ffb", "parent": "43fb307d-b692-4b46-8ac2-4ccd848945f7"}}, {"head": {"id": "2e6a149a-8483-4606-946c-5241e8ccefb5", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135855931200, "endTime": 157135879796900}, "additional": {"children": ["a8cfc7e9-d5a0-4d05-a8cf-359772265276", "32041122-4cb6-40f9-9bca-92444a67b067", "e1b6e2e9-f015-4dcd-8dea-838e2bf77f26"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c868b88-7e44-476e-bc4c-a5928596b517", "logId": "be182865-8bf2-49cc-a2a2-c027ceb6c6e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8cfc7e9-d5a0-4d05-a8cf-359772265276", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135859814400, "endTime": 157135859826800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2e6a149a-8483-4606-946c-5241e8ccefb5", "logId": "06c0f858-d205-448b-96e4-e3ebdddf04ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06c0f858-d205-448b-96e4-e3ebdddf04ff", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135859814400, "endTime": 157135859826800}, "additional": {"logType": "info", "children": [], "durationId": "a8cfc7e9-d5a0-4d05-a8cf-359772265276", "parent": "be182865-8bf2-49cc-a2a2-c027ceb6c6e4"}}, {"head": {"id": "32041122-4cb6-40f9-9bca-92444a67b067", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135861258100, "endTime": 157135877799600}, "additional": {"children": ["6c0fa701-e40a-48f7-9024-328fe0358bc0", "9f91671b-c53c-4af0-a085-7d8a3c89e3e7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2e6a149a-8483-4606-946c-5241e8ccefb5", "logId": "12adf63b-4897-4ff6-871c-048481d5a2fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c0fa701-e40a-48f7-9024-328fe0358bc0", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135861258900, "endTime": 157135864526800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "32041122-4cb6-40f9-9bca-92444a67b067", "logId": "b868f103-c33b-4849-9ebf-5b781021f67c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f91671b-c53c-4af0-a085-7d8a3c89e3e7", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135864543400, "endTime": 157135877784200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "32041122-4cb6-40f9-9bca-92444a67b067", "logId": "b6619222-a1e8-4540-a173-b1baecdf39cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17327368-4016-4de5-8676-43d245ffa3d8", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135861260700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62e5829c-ce8f-4dc1-a0ca-b21827ae652d", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135864412900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b868f103-c33b-4849-9ebf-5b781021f67c", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135861258900, "endTime": 157135864526800}, "additional": {"logType": "info", "children": [], "durationId": "6c0fa701-e40a-48f7-9024-328fe0358bc0", "parent": "12adf63b-4897-4ff6-871c-048481d5a2fe"}}, {"head": {"id": "736f7636-7afb-447e-a94b-2f309e501359", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135864550400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f293870-33cd-49f0-9348-3ea6caec9a1c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135871599900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b98989a1-1557-4f6f-afcf-9974509d59bb", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135871753000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "493f2722-042d-4398-bcf0-0151841bc7c5", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135872064200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2736a62-b541-4558-832a-3588ae029cdf", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135872267600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f54ab6f-ad19-4596-9f5f-a47a65dd55e7", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135872340100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6de46e1a-93e2-466d-8c81-74b4bd483893", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135872398800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20e84f31-2664-4b27-b3c2-d90c3fd3f0f2", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135872470000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ecf8495-183c-47e2-9a28-f9e05b4e59ff", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135872534700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f848ae7e-b14b-4523-b489-d59533d323ab", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135872815800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bc0f71f-f460-41c4-b980-484d3f44c17d", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135872966600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be4963f0-880d-4ff0-a965-b1df28049608", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135873037800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6cb86b9-4edc-4c63-913d-74751b795175", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135873103200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4460f8a-a538-4135-a99e-b138c2e3cd6a", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135873179700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66543df6-7704-4e75-956d-3f4256436bf0", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135873253900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70116215-5f28-48c6-9f2b-4117b5b0f3c0", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135873467000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b9a459d-97c1-4728-87cd-4cebf3b4d08d", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135873626500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50d53d79-eecd-4d71-b51e-627912bcf3ef", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135873695500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "723bf891-e39b-40d4-af16-756ae83d50e7", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135873745500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6da6ede4-9954-4e04-a6d9-48200edce6ae", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135873814700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b25dbc0d-ccda-4da1-a75a-59e882829fe6", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135877243300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bfc01be-333e-4728-b85b-4a2a83da5169", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135877576000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15c1d753-eae5-4bbc-a003-758135b3c88e", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135877676600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba94c513-0341-4d6f-b84e-59829f7f5915", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135877731000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6619222-a1e8-4540-a173-b1baecdf39cc", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135864543400, "endTime": 157135877784200}, "additional": {"logType": "info", "children": [], "durationId": "9f91671b-c53c-4af0-a085-7d8a3c89e3e7", "parent": "12adf63b-4897-4ff6-871c-048481d5a2fe"}}, {"head": {"id": "12adf63b-4897-4ff6-871c-048481d5a2fe", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135861258100, "endTime": 157135877799600}, "additional": {"logType": "info", "children": ["b868f103-c33b-4849-9ebf-5b781021f67c", "b6619222-a1e8-4540-a173-b1baecdf39cc"], "durationId": "32041122-4cb6-40f9-9bca-92444a67b067", "parent": "be182865-8bf2-49cc-a2a2-c027ceb6c6e4"}}, {"head": {"id": "e1b6e2e9-f015-4dcd-8dea-838e2bf77f26", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135879767700, "endTime": 157135879785100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2e6a149a-8483-4606-946c-5241e8ccefb5", "logId": "b3b496b9-a1a7-4624-9891-70826c6c60c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3b496b9-a1a7-4624-9891-70826c6c60c0", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135879767700, "endTime": 157135879785100}, "additional": {"logType": "info", "children": [], "durationId": "e1b6e2e9-f015-4dcd-8dea-838e2bf77f26", "parent": "be182865-8bf2-49cc-a2a2-c027ceb6c6e4"}}, {"head": {"id": "be182865-8bf2-49cc-a2a2-c027ceb6c6e4", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135855931200, "endTime": 157135879796900}, "additional": {"logType": "info", "children": ["06c0f858-d205-448b-96e4-e3ebdddf04ff", "12adf63b-4897-4ff6-871c-048481d5a2fe", "b3b496b9-a1a7-4624-9891-70826c6c60c0"], "durationId": "2e6a149a-8483-4606-946c-5241e8ccefb5", "parent": "9235e776-feba-49f4-9b6d-058bb3862e85"}}, {"head": {"id": "9235e776-feba-49f4-9b6d-058bb3862e85", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135855460000, "endTime": 157135879811000}, "additional": {"logType": "info", "children": ["be182865-8bf2-49cc-a2a2-c027ceb6c6e4"], "durationId": "9c868b88-7e44-476e-bc4c-a5928596b517", "parent": "43fb307d-b692-4b46-8ac2-4ccd848945f7"}}, {"head": {"id": "3e9b2467-1eb1-4d0e-a8ba-fe9560503579", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135911742800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a865142-8988-4b29-956e-7588de893204", "name": "hvigorfile, resolve hvigorfile dependencies in 66 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135945045000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "862a8567-5467-40f6-8cfc-fb5a54849d71", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135879831200, "endTime": 157135945175200}, "additional": {"logType": "info", "children": [], "durationId": "2c33d451-b806-4f5f-9b35-cd50b004b1cb", "parent": "43fb307d-b692-4b46-8ac2-4ccd848945f7"}}, {"head": {"id": "be95a858-10fd-40d1-b48a-311318f318b6", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135945886900, "endTime": 157135946063300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fda18780-2e76-4b5d-a0f1-293ece24e83b", "logId": "5c2ed353-33f9-4897-b1f2-dc500f8df9e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c12958c7-e0e5-4bd9-8988-10e82326f37e", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135945922300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c2ed353-33f9-4897-b1f2-dc500f8df9e9", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135945886900, "endTime": 157135946063300}, "additional": {"logType": "info", "children": [], "durationId": "be95a858-10fd-40d1-b48a-311318f318b6", "parent": "43fb307d-b692-4b46-8ac2-4ccd848945f7"}}, {"head": {"id": "e27875df-e8a6-431b-abc8-9ae96dd2a242", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135947472200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd13e23e-d13c-4878-a583-b7956ff327e4", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135963693700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bb68c59-561d-451f-8c54-cdd27540ca28", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135946073900, "endTime": 157135964521800}, "additional": {"logType": "info", "children": [], "durationId": "b0b8c1b6-2b55-44cc-a343-be91cd7e7f58", "parent": "43fb307d-b692-4b46-8ac2-4ccd848945f7"}}, {"head": {"id": "3891db8f-dacd-4287-8ba6-04955e881961", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135964564600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afcd3d67-8490-4eeb-89f9-8340b3c5baa3", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135972508800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "732c1637-7701-40f4-9651-2276f4d55523", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135972633100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edacdff6-3da2-46e3-b1da-673aebd5361c", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135972850200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0383c104-5170-412a-bb05-0410f30a3c21", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135976117500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd56a53b-4e2c-46e5-8618-9210f763289e", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135976200300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f559d7c-a44e-4ce4-a847-22ef525ddfdf", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135964542200, "endTime": 157135980841600}, "additional": {"logType": "info", "children": [], "durationId": "1bb213fe-2fe2-4876-96b5-ea434c6aa9ba", "parent": "43fb307d-b692-4b46-8ac2-4ccd848945f7"}}, {"head": {"id": "5819ea39-e8e4-413e-9f0e-6424efc59f14", "name": "Configuration phase cost:212 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135980889200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "206346fe-48e7-4d7b-bce8-47fc473ee9cc", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135980862500, "endTime": 157135981000300}, "additional": {"logType": "info", "children": [], "durationId": "f6727c07-7c37-4709-86b5-63ee94841535", "parent": "43fb307d-b692-4b46-8ac2-4ccd848945f7"}}, {"head": {"id": "43fb307d-b692-4b46-8ac2-4ccd848945f7", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135760607200, "endTime": 157135981014200}, "additional": {"logType": "info", "children": ["b824ccbe-8002-435f-b2d4-7f02357b4943", "7fa10106-8eb8-44ed-abc2-a1e1670d9a61", "6af1805b-a3bc-43e6-8708-e318b81c7884", "9235e776-feba-49f4-9b6d-058bb3862e85", "862a8567-5467-40f6-8cfc-fb5a54849d71", "8bb68c59-561d-451f-8c54-cdd27540ca28", "6f559d7c-a44e-4ce4-a847-22ef525ddfdf", "206346fe-48e7-4d7b-bce8-47fc473ee9cc", "5c2ed353-33f9-4897-b1f2-dc500f8df9e9"], "durationId": "fda18780-2e76-4b5d-a0f1-293ece24e83b", "parent": "a1fa89ed-69c8-4d9a-8088-907ced0ebe9a"}}, {"head": {"id": "b4ae4d4b-002f-47bb-94ae-511e9c40ae73", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135982659100, "endTime": 157135982680800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6e6e1e2d-6c90-455a-8e75-071be8b7a93c", "logId": "7fc49258-66a1-4f77-8793-375545c68c32"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7fc49258-66a1-4f77-8793-375545c68c32", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135982659100, "endTime": 157135982680800}, "additional": {"logType": "info", "children": [], "durationId": "b4ae4d4b-002f-47bb-94ae-511e9c40ae73", "parent": "a1fa89ed-69c8-4d9a-8088-907ced0ebe9a"}}, {"head": {"id": "228ea7ec-439b-453f-9466-cfae27288ba4", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135981045700, "endTime": 157135982691000}, "additional": {"logType": "info", "children": [], "durationId": "2ba23231-40cd-4ff9-9d8d-5a437f0952eb", "parent": "a1fa89ed-69c8-4d9a-8088-907ced0ebe9a"}}, {"head": {"id": "f96875fc-d2b6-45a2-9aec-0641d05ed18b", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135982696700, "endTime": 157135982710800}, "additional": {"logType": "info", "children": [], "durationId": "5538aafa-1a0b-45e5-b884-0274184d5968", "parent": "a1fa89ed-69c8-4d9a-8088-907ced0ebe9a"}}, {"head": {"id": "a1fa89ed-69c8-4d9a-8088-907ced0ebe9a", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135735352700, "endTime": 157135982717000}, "additional": {"logType": "info", "children": ["1883054b-e7ac-4966-ad1f-40b9e3e6f1c0", "43fb307d-b692-4b46-8ac2-4ccd848945f7", "228ea7ec-439b-453f-9466-cfae27288ba4", "f96875fc-d2b6-45a2-9aec-0641d05ed18b", "469eff71-2a3d-40c9-9bc9-705eaea5c089", "4cab2d71-422c-478b-a74b-20c8c3d18a5e", "7fc49258-66a1-4f77-8793-375545c68c32"], "durationId": "6e6e1e2d-6c90-455a-8e75-071be8b7a93c"}}, {"head": {"id": "62aa8773-7f48-4751-a7ed-79e8c807a68b", "name": "Configuration task cost before running: 256 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135983223900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fedb7b68-1fee-4126-9c1a-976945d97bac", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135992478200, "endTime": 157136003679700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "2ee4b52c-523f-454f-bfe9-6050ffaec750", "logId": "d9373d06-b88b-4596-81d8-e98ff2537139"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ee4b52c-523f-454f-bfe9-6050ffaec750", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135984639300}, "additional": {"logType": "detail", "children": [], "durationId": "fedb7b68-1fee-4126-9c1a-976945d97bac"}}, {"head": {"id": "08d5892e-3f1a-45a9-af57-26a22c770bf3", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135985193900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cba17376-bf32-47e4-bdd8-7326d51603d0", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135985284600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93d14085-85ae-4e78-8561-ef0c53fdbb48", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135986350400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b255aaba-c408-41f3-aa7e-8b63d4100705", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135987733900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcd1af32-8159-4825-9887-4d8a9fd58502", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135989159500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ed33e2a-8fa0-470f-bcd4-be2686f1a052", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135989288300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc7e11dc-5077-4168-baad-b37f400513fb", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135992488900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45667c2f-4098-489b-bbcd-69738c0d4255", "name": "Incremental task entry:default@PreBuild pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136003494900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f06792c-50ab-48dc-8f1c-b88d41d5a091", "name": "entry : default@PreBuild cost memory 0.3087005615234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136003619600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9373d06-b88b-4596-81d8-e98ff2537139", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135992478200, "endTime": 157136003679700}, "additional": {"logType": "info", "children": [], "durationId": "fedb7b68-1fee-4126-9c1a-976945d97bac"}}, {"head": {"id": "49d104fc-b061-4e90-90b1-8833fd75355a", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136013541000, "endTime": 157136014980800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bf753a2e-d7a6-4d5d-bfea-fb5bf1685cce", "logId": "f683004f-0443-44f8-b6ab-de9a265c8285"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf753a2e-d7a6-4d5d-bfea-fb5bf1685cce", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136010942700}, "additional": {"logType": "detail", "children": [], "durationId": "49d104fc-b061-4e90-90b1-8833fd75355a"}}, {"head": {"id": "9e3013f6-d957-4533-90e1-b61a9ccc30c3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136012792900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2594e90-e908-49b4-a3d4-f3485090a9db", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136012966600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa3b890d-5a3f-463b-8122-2f58b9574bc2", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136013546800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1d58a72-80f6-4de1-8301-1c17decc6a57", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136014098700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f99d77bd-03c7-4982-8714-b3af58ead515", "name": "entry : default@CreateModuleInfo cost memory 0.05960845947265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136014846200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "328363d2-1955-4228-8ab7-e9cce7f97420", "name": "runTaskFromQueue task cost before running: 287 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136014936000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f683004f-0443-44f8-b6ab-de9a265c8285", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136013541000, "endTime": 157136014980800, "totalTime": 1381800}, "additional": {"logType": "info", "children": [], "durationId": "49d104fc-b061-4e90-90b1-8833fd75355a"}}, {"head": {"id": "5bb62913-b1bc-42b2-b107-cf5c029549e5", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136023312700, "endTime": 157136025222600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "55b9db27-fbf9-44b5-90f0-81f34f2b9a48", "logId": "3943fb8e-6b6a-421a-b55b-b40b0733ec79"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55b9db27-fbf9-44b5-90f0-81f34f2b9a48", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136017655100}, "additional": {"logType": "detail", "children": [], "durationId": "5bb62913-b1bc-42b2-b107-cf5c029549e5"}}, {"head": {"id": "22c2d0ee-4c93-46b0-827e-8f1942870fa5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136019028500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a3dec4b-6ddf-4917-b049-2b44d350f46d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136019145000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c98a4b5a-2112-4a05-a89b-71ffa44cc29a", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136023324000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed1491a1-1ecb-4174-ae9a-c7074dde9219", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136024205400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61a043ee-a564-4a58-9fa7-7cdb1c191b42", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136025079800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "059ccd02-520a-4a2e-b52e-1c2ae055fd38", "name": "entry : default@GenerateMetadata cost memory 0.09967041015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136025171600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3943fb8e-6b6a-421a-b55b-b40b0733ec79", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136023312700, "endTime": 157136025222600}, "additional": {"logType": "info", "children": [], "durationId": "5bb62913-b1bc-42b2-b107-cf5c029549e5"}}, {"head": {"id": "1737b397-e9f2-485d-bee2-9cd5f2cd39a2", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136027983000, "endTime": 157136028228400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7513396a-2542-420e-a926-716a969c7859", "logId": "097b4671-09ba-4ee4-a0c9-1e73facc3681"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7513396a-2542-420e-a926-716a969c7859", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136026890500}, "additional": {"logType": "detail", "children": [], "durationId": "1737b397-e9f2-485d-bee2-9cd5f2cd39a2"}}, {"head": {"id": "ec36c47a-5b02-4ef0-8a84-d1b58807cf92", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136027775800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97e4fb22-05e4-4c93-bd0d-923b5ae18721", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136027870700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fd247b6-c888-4bab-b353-bbc3eac7fb45", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136027989900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d08f663-9c8d-47bc-b59a-7e7b847c96b1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136028058100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a179087-f5ec-4c9b-998f-be84aa5bf71a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136028087100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30dd78be-eb4f-4c4e-8df3-673053b7c92c", "name": "entry : default@ConfigureCmake cost memory 0.037109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136028133400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "447d4619-0a61-4860-b9b2-e7f8047254eb", "name": "runTaskFromQueue task cost before running: 301 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136028194900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "097b4671-09ba-4ee4-a0c9-1e73facc3681", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136027983000, "endTime": 157136028228400, "totalTime": 191700}, "additional": {"logType": "info", "children": [], "durationId": "1737b397-e9f2-485d-bee2-9cd5f2cd39a2"}}, {"head": {"id": "9b60e621-a4b6-4097-9d58-4c09592c8bcb", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136032084700, "endTime": 157136035354500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "53726b53-d729-4cc9-9b8a-521f8f25ed0b", "logId": "b16bc460-0682-4ead-a7c0-0a85beed2ce6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53726b53-d729-4cc9-9b8a-521f8f25ed0b", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136029432800}, "additional": {"logType": "detail", "children": [], "durationId": "9b60e621-a4b6-4097-9d58-4c09592c8bcb"}}, {"head": {"id": "67dd86e5-7372-4c19-a429-8c211bf6c4e3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136030845500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "796ae126-a60e-4d77-98fc-4784a882a3eb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136030968400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6e84166-e7ab-4211-a88e-499f3f20da2b", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136032101900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3c169ef-9027-487f-9375-2b3c9c1e81dc", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136035207100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40ba3d3b-6a38-4843-93c3-1fe55196950f", "name": "entry : default@MergeProfile cost memory 0.11724853515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136035301500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b16bc460-0682-4ead-a7c0-0a85beed2ce6", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136032084700, "endTime": 157136035354500}, "additional": {"logType": "info", "children": [], "durationId": "9b60e621-a4b6-4097-9d58-4c09592c8bcb"}}, {"head": {"id": "6631beed-e5ad-4e8c-9ae7-afa1700cd861", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136040250300, "endTime": 157136045170100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "06fb0ced-d79a-430b-a5ca-48d3ce3fca3d", "logId": "8107fff2-65d5-4659-96f8-59b7123ce47f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06fb0ced-d79a-430b-a5ca-48d3ce3fca3d", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136037103700}, "additional": {"logType": "detail", "children": [], "durationId": "6631beed-e5ad-4e8c-9ae7-afa1700cd861"}}, {"head": {"id": "446b3d9b-d114-48fc-9e32-02781efd6639", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136038905300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf755aca-b4f3-4b6b-829c-d6ee5d73d9dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136039055700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9369ba23-d077-4961-ac7e-983a5b475a6d", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136040260500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dc557bc-1a68-40bf-8dae-68adfa4a25e2", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136041929900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05ca7a1a-374a-4240-bfc6-f52da5394863", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136044889300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61154d90-1feb-4e14-93a3-0f88215004d8", "name": "entry : default@CreateBuildProfile cost memory 0.10691070556640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136045074600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8107fff2-65d5-4659-96f8-59b7123ce47f", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136040250300, "endTime": 157136045170100}, "additional": {"logType": "info", "children": [], "durationId": "6631beed-e5ad-4e8c-9ae7-afa1700cd861"}}, {"head": {"id": "012e1210-1f5a-484b-b6bf-c42e65b8e2ac", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136049134900, "endTime": 157136049564800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "aa0b38a5-0dc4-4c07-aa97-d022fa061693", "logId": "11c189d9-d1c2-400c-ac09-5babc1bbce03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa0b38a5-0dc4-4c07-aa97-d022fa061693", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136047370900}, "additional": {"logType": "detail", "children": [], "durationId": "012e1210-1f5a-484b-b6bf-c42e65b8e2ac"}}, {"head": {"id": "b7f3cb2e-d6e0-42e2-a198-72e28bd95157", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136048396400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96ecedf1-59ae-43d9-b8cf-39ac40586d7b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136048503500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57e12d0f-1bf6-4bae-8660-167239d0b127", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136049141800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3b888c1-e1d3-45cc-994f-1bcdf99f3bb2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136049273200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a7cfdf5-4f71-49b0-af85-553bb2ba3c6c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136049307400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4ebe873-ac36-4225-b9db-29915225bbc6", "name": "entry : default@PreCheckSyscap cost memory 0.0407257080078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136049458300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77e48fb0-0ed0-45b4-a886-dd5ddcedc334", "name": "runTaskFromQueue task cost before running: 322 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136049523900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11c189d9-d1c2-400c-ac09-5babc1bbce03", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136049134900, "endTime": 157136049564800, "totalTime": 376300}, "additional": {"logType": "info", "children": [], "durationId": "012e1210-1f5a-484b-b6bf-c42e65b8e2ac"}}, {"head": {"id": "cfae100a-dab6-4620-bc78-ae23580d7b63", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136052804400, "endTime": 157136059732800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "82533dce-c1fa-4bbf-a9c3-86fcf2f923d7", "logId": "e43c384b-97d0-4cba-8efc-05ff1ac19847"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82533dce-c1fa-4bbf-a9c3-86fcf2f923d7", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136050782500}, "additional": {"logType": "detail", "children": [], "durationId": "cfae100a-dab6-4620-bc78-ae23580d7b63"}}, {"head": {"id": "8dc40646-42f6-4577-8b2d-2ebab7be721c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136051626100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e51dc29-c0c4-4441-9470-e8d6a31a1807", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136051699600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dac9fb12-c7df-4b92-adc5-bcf30713cbcd", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136052811700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d78b2401-dc79-427e-b7bf-45bd8223ee99", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136058371900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4212c0b-11e6-4f55-9ab3-6426fc802f5b", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136059487200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca990b06-cfc3-432b-bd50-1eedb03ca<PERSON>e", "name": "entry : default@GeneratePkgContextInfo cost memory 0.23873138427734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136059623900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e43c384b-97d0-4cba-8efc-05ff1ac19847", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136052804400, "endTime": 157136059732800}, "additional": {"logType": "info", "children": [], "durationId": "cfae100a-dab6-4620-bc78-ae23580d7b63"}}, {"head": {"id": "91458d1d-f4ab-49e2-96a0-0e45134067ca", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136068038500, "endTime": 157136069762000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "a9b8e81e-3679-4e27-81de-207b91e60885", "logId": "0e5d4a72-972f-45dd-b274-134c10ea8de2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9b8e81e-3679-4e27-81de-207b91e60885", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136062096200}, "additional": {"logType": "detail", "children": [], "durationId": "91458d1d-f4ab-49e2-96a0-0e45134067ca"}}, {"head": {"id": "e6a30c98-1893-405f-9cfc-6e9995e90c41", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136063321100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fa4b56b-7b77-4e52-bb25-e28a66b31423", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136063399900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2c2b3e9-ddf0-407a-9af3-06291cf599f6", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136068046500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15b9c2d8-f8b1-4780-96f0-594060d0909a", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136069161200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d890c707-9485-45fb-a638-c5eb19470706", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136069247500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcfb6fdc-c4c4-494b-8a98-3f593247345e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136069302400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "834d8991-d81f-475c-be1c-0dd6b475fb6c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136069399300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5dc6472-bff2-4b17-aa16-62b9a4e3c131", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11871337890625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136069544200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e249b72-71bd-429d-9665-f95e4f39ddef", "name": "runTaskFromQueue task cost before running: 342 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136069667100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e5d4a72-972f-45dd-b274-134c10ea8de2", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136068038500, "endTime": 157136069762000, "totalTime": 1606600}, "additional": {"logType": "info", "children": [], "durationId": "91458d1d-f4ab-49e2-96a0-0e45134067ca"}}, {"head": {"id": "78f09682-00ef-44cd-baf5-e6cdce15c4d3", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136075225600, "endTime": 157136075664700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "da5fd758-c783-477d-bb0a-aeb69cb795ee", "logId": "c3bd882f-79c2-4296-a23a-0d81ffe2ef6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da5fd758-c783-477d-bb0a-aeb69cb795ee", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136072549600}, "additional": {"logType": "detail", "children": [], "durationId": "78f09682-00ef-44cd-baf5-e6cdce15c4d3"}}, {"head": {"id": "fc389f56-3b25-4af9-8c0b-5201249d0e41", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136074074100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14bce88e-979e-4cd1-ad8b-79ea4ced091b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136074207200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "279631e3-97c5-4974-a8fd-82706b388ce8", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136075234700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32d34be8-b2e1-4579-8d00-9da7ae1a8961", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136075367000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb7b59bb-9c59-4b8b-989a-93ec74e25b52", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136075428500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "793fa540-0007-43b0-b240-9507de0f90d5", "name": "entry : default@BuildNativeWithCmake cost memory 0.03815460205078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136075511600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acc7beb2-5607-457d-8525-06da3bb2d07d", "name": "runTaskFromQueue task cost before running: 348 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136075603800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3bd882f-79c2-4296-a23a-0d81ffe2ef6a", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136075225600, "endTime": 157136075664700, "totalTime": 358400}, "additional": {"logType": "info", "children": [], "durationId": "78f09682-00ef-44cd-baf5-e6cdce15c4d3"}}, {"head": {"id": "654f6299-652b-4b56-874c-4814b365218a", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136079289000, "endTime": 157136084138100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d4be5a9a-4414-41db-bd1f-310969b447c7", "logId": "08fcfcd5-64f8-466f-8cdd-1962eef2cd9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4be5a9a-4414-41db-bd1f-310969b447c7", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136077384500}, "additional": {"logType": "detail", "children": [], "durationId": "654f6299-652b-4b56-874c-4814b365218a"}}, {"head": {"id": "bf557796-4902-4e12-8685-95bb504b7d0e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136078159600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed9b2500-8177-4d22-a67e-1a3f85401f46", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136078277300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5acb7856-ec36-4831-bcc4-5d7787fd3b3e", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136079306700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2d5c72a-3532-4ca7-97f1-f0c6b38f26d0", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136083902900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b16056f-2909-48d0-b224-b27959ef84c4", "name": "entry : default@MakePackInfo cost memory 0.16240692138671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136084047800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08fcfcd5-64f8-466f-8cdd-1962eef2cd9c", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136079289000, "endTime": 157136084138100}, "additional": {"logType": "info", "children": [], "durationId": "654f6299-652b-4b56-874c-4814b365218a"}}, {"head": {"id": "071dfff7-deae-41d1-b9c6-c2df874000c3", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136090000700, "endTime": 157136095520700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "53331d54-edea-48e0-9159-4bf000952f16", "logId": "2fa94dec-efb5-4c2d-a153-a1a70931cfe7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53331d54-edea-48e0-9159-4bf000952f16", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136086827700}, "additional": {"logType": "detail", "children": [], "durationId": "071dfff7-deae-41d1-b9c6-c2df874000c3"}}, {"head": {"id": "2f36785a-ff3c-4c42-b33f-f0e455df6efa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136088251400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65ac9c32-2fcb-4648-9241-f1640e84e423", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136088363000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a660b63c-9c49-4fdb-b79b-d3ad05d2cc19", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136090010500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8e0af19-2aa8-449b-9a8c-3795c14b4599", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136090262800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43b54536-6f8e-488b-9686-de91891326a3", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136091435400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "287407f5-fc95-4486-98a5-a318bf76ad14", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136095283600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6841420-c148-4353-bb76-ac92eb4b6929", "name": "entry : default@SyscapTransform cost memory 0.14954376220703125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136095429500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fa94dec-efb5-4c2d-a153-a1a70931cfe7", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136090000700, "endTime": 157136095520700}, "additional": {"logType": "info", "children": [], "durationId": "071dfff7-deae-41d1-b9c6-c2df874000c3"}}, {"head": {"id": "ef63db70-3e08-466d-8a09-326f96c4b623", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136100797200, "endTime": 157136103862500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "fc7a344a-b644-4884-aa61-d29231024982", "logId": "707918d7-91e5-4e2f-bbd8-8858bbf31746"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc7a344a-b644-4884-aa61-d29231024982", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136097578400}, "additional": {"logType": "detail", "children": [], "durationId": "ef63db70-3e08-466d-8a09-326f96c4b623"}}, {"head": {"id": "7aa97e65-45c3-4d8b-9af0-4165b5c4980f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136099133800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b5b8b31-f28b-45da-8109-9aa1d8139933", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136099242400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a04c2524-3714-4345-8b1b-498368e4e7fc", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136100807700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99849266-0d3c-46ac-92a7-79f92c840ed3", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136103639400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fb3be75-1857-4365-9b7b-71a2329cbf62", "name": "entry : default@ProcessProfile cost memory 0.1214752197265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136103771500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "707918d7-91e5-4e2f-bbd8-8858bbf31746", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136100797200, "endTime": 157136103862500}, "additional": {"logType": "info", "children": [], "durationId": "ef63db70-3e08-466d-8a09-326f96c4b623"}}, {"head": {"id": "3ff9f58b-3c1d-4b02-b665-fe52c227f4d7", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136110159100, "endTime": 157136118001800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3151816c-7dfd-45ab-b3ca-2691806209e7", "logId": "85eb43e8-8856-463c-a893-a670799f502a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3151816c-7dfd-45ab-b3ca-2691806209e7", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136105818200}, "additional": {"logType": "detail", "children": [], "durationId": "3ff9f58b-3c1d-4b02-b665-fe52c227f4d7"}}, {"head": {"id": "3c82a88c-9999-4844-8cbe-45929f251f57", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136107308200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b555f9-f3b7-40fa-a74f-c8ef121531a0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136107419300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29be226b-148c-4934-b35a-24e2e51e04a5", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136110170800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e32140ac-c606-4e8f-8c01-4a0f34983eb9", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136117779700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ee54e39-0bb6-467a-a2fe-7e230bb0a12b", "name": "entry : default@ProcessRouterMap cost memory 0.23077392578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136117898400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85eb43e8-8856-463c-a893-a670799f502a", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136110159100, "endTime": 157136118001800}, "additional": {"logType": "info", "children": [], "durationId": "3ff9f58b-3c1d-4b02-b665-fe52c227f4d7"}}, {"head": {"id": "634293fa-9e86-4d95-991d-3c70aeaab0bc", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136120747800, "endTime": 157136125789300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "015c6837-70f3-4e6d-bc5f-55b610036ee7", "logId": "94032126-8a21-48c2-9db2-de7d808c0e39"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "015c6837-70f3-4e6d-bc5f-55b610036ee7", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136119852400}, "additional": {"logType": "detail", "children": [], "durationId": "634293fa-9e86-4d95-991d-3c70aeaab0bc"}}, {"head": {"id": "02b1d8be-0e0e-4d76-a17e-6499ce68b80a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136120611300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5d616a6-46cd-40f6-8bc6-79226a5ebb90", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136120683300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a14b1b64-6441-43f5-b478-974e635808d6", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136120752500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49ee3e7b-4b9c-49b6-997b-b32289a8591d", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136120837400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "913d10b6-aa8b-46cf-bc3e-3178b286ea80", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136123635900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed99a159-393c-484a-b7ef-aaec99c58d5c", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136123726400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f3c577a-bbe7-4f05-bf85-ac9388cc95e3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136123783700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d428ff37-c181-49fa-a682-e12142a03f2f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136123814200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0657df55-f31c-4767-acd0-4b7ebb982342", "name": "entry : default@ProcessStartupConfig cost memory 0.25559234619140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136125578700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "425b090a-b774-4364-b0f8-a1bca43dbbde", "name": "runTaskFromQueue task cost before running: 398 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136125737100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94032126-8a21-48c2-9db2-de7d808c0e39", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136120747800, "endTime": 157136125789300, "totalTime": 4969300}, "additional": {"logType": "info", "children": [], "durationId": "634293fa-9e86-4d95-991d-3c70aeaab0bc"}}, {"head": {"id": "3d5608e0-5191-4885-ba70-f570a3a02332", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136130164700, "endTime": 157136131674400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "13430cf6-4d4d-48bb-a953-6147630f1845", "logId": "833a676c-6254-4837-9e27-95eaa71994f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13430cf6-4d4d-48bb-a953-6147630f1845", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136127777300}, "additional": {"logType": "detail", "children": [], "durationId": "3d5608e0-5191-4885-ba70-f570a3a02332"}}, {"head": {"id": "1e07a0bc-6dea-4170-a295-22e807cdf5e8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136129129400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e694c5a1-09f2-4625-942e-94b36cf0d070", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136129230500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1edb9683-239b-4147-8db8-87157bc8c703", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136130173400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ee44e03-0b65-4ca6-ad7a-221316f536ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136130292200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62dac2b8-2344-4c14-bd7e-067d7cb6b133", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136130348400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75c8bccb-b7f7-43e1-a8b9-2f53d8965241", "name": "entry : default@BuildNativeWithNinja cost memory 0.05764007568359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136131454100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3f9bc4b-6631-44e4-b6f5-5bf64960ac73", "name": "runTaskFromQueue task cost before running: 404 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136131588300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "833a676c-6254-4837-9e27-95eaa71994f8", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136130164700, "endTime": 157136131674400, "totalTime": 1406000}, "additional": {"logType": "info", "children": [], "durationId": "3d5608e0-5191-4885-ba70-f570a3a02332"}}, {"head": {"id": "d807b6b4-4fc3-4eef-99e9-c94597b150a7", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136139298500, "endTime": 157136147260500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "2bed5455-a307-4c33-9525-75aea4f9e418", "logId": "762362c7-3abc-4adf-a9ca-3009a68fc3f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2bed5455-a307-4c33-9525-75aea4f9e418", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136134525400}, "additional": {"logType": "detail", "children": [], "durationId": "d807b6b4-4fc3-4eef-99e9-c94597b150a7"}}, {"head": {"id": "806f4cd7-7c92-44c4-833b-93fe2fbe9326", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136135879300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed279d3b-93d2-4f11-a933-50778680fdb0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136135994300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40258dc3-40fd-4898-86b0-99be71dd1f7f", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136137631100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e98c139-a73f-430e-adda-6c18046ef14d", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136141842500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31975d11-9582-440f-b264-2bbcd19e3bcb", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136144491400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b10ed468-c3d3-437b-83cc-15814472aabe", "name": "entry : default@ProcessResource cost memory 0.16011810302734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136144667300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "762362c7-3abc-4adf-a9ca-3009a68fc3f6", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136139298500, "endTime": 157136147260500}, "additional": {"logType": "info", "children": [], "durationId": "d807b6b4-4fc3-4eef-99e9-c94597b150a7"}}, {"head": {"id": "16073ef6-86e2-4bec-856b-9ca7b8a87a67", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136158463400, "endTime": 157136186021900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "47354a7d-9f6f-4f73-b2b2-1b11561f6439", "logId": "b697feae-dbca-4eaf-aa6a-55a652c629c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47354a7d-9f6f-4f73-b2b2-1b11561f6439", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136152094800}, "additional": {"logType": "detail", "children": [], "durationId": "16073ef6-86e2-4bec-856b-9ca7b8a87a67"}}, {"head": {"id": "706a3330-fa9b-4db4-a84e-07a750b850ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136153473600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5f480a8-a7ab-41bd-af0a-75653d7d8b1b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136153589500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e02cf09f-daa8-4568-b32e-41b1c2af8f09", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136158477100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "261df4c6-3bd9-4f7f-8761-a9c7913518fe", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136185727500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1729884-59a9-467f-8d78-7d708e096840", "name": "entry : default@GenerateLoaderJson cost memory 0.8638763427734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136185921700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b697feae-dbca-4eaf-aa6a-55a652c629c7", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136158463400, "endTime": 157136186021900}, "additional": {"logType": "info", "children": [], "durationId": "16073ef6-86e2-4bec-856b-9ca7b8a87a67"}}, {"head": {"id": "ccb14668-c27f-4900-91db-59f9acb3389f", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136204683800, "endTime": 157136211103000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "e9c8bc49-34a9-4c94-86c1-55705067effb", "logId": "0f65ad0c-c21d-4254-9216-2922fa262ec8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9c8bc49-34a9-4c94-86c1-55705067effb", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136201726700}, "additional": {"logType": "detail", "children": [], "durationId": "ccb14668-c27f-4900-91db-59f9acb3389f"}}, {"head": {"id": "3dd99265-c817-4519-b5dc-7c1af212d240", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136203490500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "708cac38-5a5b-4d24-9877-6bc1381b4282", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136203605300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6026eca9-a867-4f5c-b61e-626c5c9693ae", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136204693300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86abfecd-0451-4a77-9b1b-fcb4d14b198c", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136210880200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03bfa5b3-e790-4e30-897f-f475551e7163", "name": "entry : default@ProcessLibs cost memory 0.1405181884765625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136211005100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f65ad0c-c21d-4254-9216-2922fa262ec8", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136204683800, "endTime": 157136211103000}, "additional": {"logType": "info", "children": [], "durationId": "ccb14668-c27f-4900-91db-59f9acb3389f"}}, {"head": {"id": "ef8251b8-aba3-4745-9eb2-1ff0d5fb21ab", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136217210400, "endTime": 157136247675000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "29d80121-ab88-4331-885e-0a7bb7491dcc", "logId": "9fcf3da5-56eb-437f-9ebe-5ce82cb11500"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29d80121-ab88-4331-885e-0a7bb7491dcc", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136212825400}, "additional": {"logType": "detail", "children": [], "durationId": "ef8251b8-aba3-4745-9eb2-1ff0d5fb21ab"}}, {"head": {"id": "c3448316-1980-4a8c-ab0e-45761359f496", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136213673500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f01561c-b01d-448e-8926-342e91f38c12", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136213781000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89526bf3-a1ee-42fd-8a0d-5612194e6376", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136214763200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92637d26-d708-4851-9bb4-975076cf241c", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136217245600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ad528dd-181a-41e2-9639-4a1dca3e8d27", "name": "Incremental task entry:default@CompileResource pre-execution cost: 29 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136247181600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b60c33b-17aa-45ac-828f-42ac38b9a208", "name": "entry : default@CompileResource cost memory -4.7918243408203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136247486100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fcf3da5-56eb-437f-9ebe-5ce82cb11500", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136217210400, "endTime": 157136247675000}, "additional": {"logType": "info", "children": [], "durationId": "ef8251b8-aba3-4745-9eb2-1ff0d5fb21ab"}}, {"head": {"id": "5800ddd4-ab1a-4773-9e8a-81f85fb0a1a0", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136259048300, "endTime": 157136263423200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "00cfac46-fab0-4903-9196-73ae52669213", "logId": "f6a977a6-8f73-425c-ad2e-85ed3c1e1ece"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00cfac46-fab0-4903-9196-73ae52669213", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136252642400}, "additional": {"logType": "detail", "children": [], "durationId": "5800ddd4-ab1a-4773-9e8a-81f85fb0a1a0"}}, {"head": {"id": "934e0476-04c5-455e-92e9-da5d38a8e9a4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136254507700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ed1dd0b-20c2-4f78-bc26-7e4dfff965b0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136254639100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9840bb55-b79c-41df-b747-9f0d6d5f5627", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136259072500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75bc4dc0-1a0f-4b43-ac45-ef93815904d7", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136260016200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73e64aa2-1aa5-43ee-b073-97097b446598", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136263160100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d9428f9-88fc-4b93-9fe4-7341a1c53926", "name": "entry : default@DoNativeStrip cost memory 0.07895660400390625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136263310800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6a977a6-8f73-425c-ad2e-85ed3c1e1ece", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136259048300, "endTime": 157136263423200}, "additional": {"logType": "info", "children": [], "durationId": "5800ddd4-ab1a-4773-9e8a-81f85fb0a1a0"}}, {"head": {"id": "0cb08c03-41b9-424c-baec-e78b8687bdad", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136272089400, "endTime": 157153848610700}, "additional": {"children": ["12ac5274-76d1-45ca-94a6-18211c8c22df", "a175ac3a-7f0a-4f58-a151-6867e9f6831f"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "294c73ec-69a1-4116-9387-10374fc82a20", "logId": "55e3c096-7944-40c2-bd54-99ef60790adb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "294c73ec-69a1-4116-9387-10374fc82a20", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136265885800}, "additional": {"logType": "detail", "children": [], "durationId": "0cb08c03-41b9-424c-baec-e78b8687bdad"}}, {"head": {"id": "cebffb5b-d214-4988-af6d-eac65f33840a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136267798200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3a65ef7-e2d7-447f-817e-235ba01a0c4c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136267961000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1781794a-33bd-4f4c-af70-a0f6352e473d", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136272097800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd741bce-f3a0-4bc5-bd94-3751333f6dfa", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136272250900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38835831-0085-4df4-ae36-a5fe3375ba59", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136311439900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b2e941f-3835-4074-9440-6a8bc3e34562", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 32 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136311612000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f0639e8-4807-4651-ba42-6f26b1fe8c76", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136342550300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a7085f3-e659-43c2-a166-23e500955b55", "name": "default@CompileArkTS work[24] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136352148000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12ac5274-76d1-45ca-94a6-18211c8c22df", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 157136355918100, "endTime": 157153848212800}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "0cb08c03-41b9-424c-baec-e78b8687bdad", "logId": "f338cf59-f318-4b3f-a2ad-7497957949e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a49f2541-97b3-4f08-8053-c7ef3f833d47", "name": "default@CompileArkTS work[24] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136353476400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43e14ad3-6910-4f55-aa1c-bec4f215cdc4", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136353667300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a44281c-bb8a-4cab-8e2b-3e63ff789a1e", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136353734400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b729816-433d-490d-886e-957a4aa846f6", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136353788400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a4a363a-4208-4048-befa-2da88d8ced54", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136353834500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62ebaf71-82ea-4058-b9c6-3bd2a159b01a", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136353875800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fc52a8d-e332-4dcc-a42d-c8d7902c1f2f", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136353920000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6bc539d-f795-4c86-be31-21b2ff5cda06", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136353969300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f0b2d93-33c4-4b51-b2c7-42cd57aa32b0", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136354012600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6fa363f-4a00-497a-860a-1e9abc95635d", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136354052400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85bc8638-d8c2-4981-a9b1-b3b8536fee57", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136354092200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52e8c95f-c497-427b-9676-bc0f93d2b655", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136354137200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0e15bb7-24cd-4b0c-9341-69defcf9f28c", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136354178300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7539e4dd-8763-4026-94fa-30a79b6191e9", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136354218300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ed49145-3d6a-4925-b074-a3208383fc25", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136354257400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "235b2355-e574-47ab-b96b-264d09518987", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136354302000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2dfb2ef-5cf1-45e6-85a4-f6bf8cc3fa2a", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136354477500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "221063ba-3f41-4519-80c1-e8455bef3bed", "name": "default@CompileArkTS work[24] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136355981300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e535ed1-cd27-46f5-af4e-7305ba3b69a4", "name": "default@CompileArkTS work[24] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136356254900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab83e7a2-5520-4ca5-ba4b-9142f0cc0c54", "name": "CopyResources startTime: 157136356337000", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136356338900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4afa8ad-c1f9-4686-816c-ded9be112a8a", "name": "default@CompileArkTS work[25] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136356399200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a175ac3a-7f0a-4f58-a151-6867e9f6831f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 157137630857800, "endTime": 157137660727700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "0cb08c03-41b9-424c-baec-e78b8687bdad", "logId": "e0477d03-55fb-4af3-919e-73e48daed503"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be0a9499-22e4-4eb0-a9b6-ce3e95269240", "name": "default@CompileArkTS work[25] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136357018000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aac75d8-2abb-4084-9b5d-780c7f88f0e8", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136357163300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e3e8389-0b5a-43b5-9e2d-ec9ee32d9384", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136357246800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fe15979-d42b-4027-80b6-7bbfd629f415", "name": "default@CompileArkTS work[25] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136358270400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5adb7576-a25c-42c0-9389-24410d882cc7", "name": "default@CompileArkTS work[25] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136358418900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "364e1467-3dcb-4017-83e0-93acff1dd3df", "name": "entry : default@CompileArkTS cost memory 2.6531143188476562", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136358619600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbfb0587-e46c-4177-bea5-0b8e7ef2f4d7", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136368483900, "endTime": 157136376852300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "e1a6b0b5-5c0d-4baf-97ac-504ab0172b27", "logId": "c9d533bc-e396-4f80-8e8e-b865589b6132"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1a6b0b5-5c0d-4baf-97ac-504ab0172b27", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136360890300}, "additional": {"logType": "detail", "children": [], "durationId": "cbfb0587-e46c-4177-bea5-0b8e7ef2f4d7"}}, {"head": {"id": "474fd0e3-37d8-484c-96af-1c1cfd51e902", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136362486900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c8ba0e0-6751-4845-92ad-99956da1e0c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136362624000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90561927-f197-4ca2-99e4-54dbc75b67d6", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136368499100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "224142af-1864-49cd-a250-8a18211d471a", "name": "entry : default@BuildJS cost memory 0.33560943603515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136376639100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fdd3bcc-e3cb-4a2c-bcb9-967e2387ff74", "name": "runTaskFromQueue task cost before running: 649 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136376782200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9d533bc-e396-4f80-8e8e-b865589b6132", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136368483900, "endTime": 157136376852300, "totalTime": 8269600}, "additional": {"logType": "info", "children": [], "durationId": "cbfb0587-e46c-4177-bea5-0b8e7ef2f4d7"}}, {"head": {"id": "bd57cf3c-e6b9-46db-950b-94d2dfe85286", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136383149100, "endTime": 157136387926200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5786b55d-4b2a-41e4-9140-b901516fdb13", "logId": "4ba59841-0cce-429a-a5b1-fa2c319cb440"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5786b55d-4b2a-41e4-9140-b901516fdb13", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136378457800}, "additional": {"logType": "detail", "children": [], "durationId": "bd57cf3c-e6b9-46db-950b-94d2dfe85286"}}, {"head": {"id": "597781ac-dbd0-4336-8741-c86dbac9237d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136379338200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d20470a1-471b-4801-a9f2-4286b9382fbf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136379470400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96e4a6b2-0da2-4181-ab62-76f4ace9b4ce", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136383159900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5f854ee-f342-43e1-80a0-5e5725fc5b78", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136384189500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be31e681-fca3-457e-a55d-b1f3fdadd160", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136387711000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90fdba3c-bf5d-4ce3-927d-3b69df294423", "name": "entry : default@CacheNativeLibs cost memory 0.0938262939453125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136387838700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ba59841-0cce-429a-a5b1-fa2c319cb440", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136383149100, "endTime": 157136387926200}, "additional": {"logType": "info", "children": [], "durationId": "bd57cf3c-e6b9-46db-950b-94d2dfe85286"}}, {"head": {"id": "e14337cb-9cb2-4622-958a-06d620f9052e", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157137661187800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3f37398-d462-4d29-b6f3-70800bdd5fd3", "name": "CopyResources is end, endTime: 157137661531900", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157137661539100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44b0d67d-dbb1-441f-ac0e-5c5991fe670f", "name": "default@CompileArkTS work[25] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157137661835000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0477d03-55fb-4af3-919e-73e48daed503", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 157137630857800, "endTime": 157137660727700}, "additional": {"logType": "info", "children": [], "durationId": "a175ac3a-7f0a-4f58-a151-6867e9f6831f", "parent": "55e3c096-7944-40c2-bd54-99ef60790adb"}}, {"head": {"id": "a65e0306-331e-4a1c-972c-566f1bfa2cc7", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157137662026800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd66451f-4466-4771-bb2d-63c68716972c", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153847833700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca688a27-2b2c-4ca4-b7dd-e7728791d4cb", "name": "default@CompileArkTS work[24] failed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153848358900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f338cf59-f318-4b3f-a2ad-7497957949e5", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 157136355918100, "endTime": 157153848212800}, "additional": {"logType": "error", "children": [], "durationId": "12ac5274-76d1-45ca-94a6-18211c8c22df", "parent": "55e3c096-7944-40c2-bd54-99ef60790adb"}}, {"head": {"id": "55e3c096-7944-40c2-bd54-99ef60790adb", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157136272089400, "endTime": 157153848610700}, "additional": {"logType": "error", "children": ["f338cf59-f318-4b3f-a2ad-7497957949e5", "e0477d03-55fb-4af3-919e-73e48daed503"], "durationId": "0cb08c03-41b9-424c-baec-e78b8687bdad"}}, {"head": {"id": "7656ffa2-7d94-4984-9e50-2e450f43768a", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153848952800}, "additional": {"logType": "debug", "children": [], "durationId": "0cb08c03-41b9-424c-baec-e78b8687bdad"}}, {"head": {"id": "f08369f7-0df6-48f6-8eaf-c2c0a83c2f94", "name": "ERROR: stacktrace = Error: ArkTS Compiler Error\r\n\u001b[33m1 WARN: \u001b[33m\u001b[33mArkTS:WARN: For details about ArkTS syntax errors, see FAQs\u001b[39m\u001b[39m\r\n\u001b[33m2 WARN: \u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/HomePage.ets:20:1\n It's not a recommended way to export struct with '@Entry' decorator, which may cause ACE Engine error in component preview mode.\u001b[39m\u001b[39m\r\n\u001b[33m3 WARN: \u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/CategoryListPage.ets:16:1\n It's not a recommended way to export struct with '@Entry' decorator, which may cause ACE Engine error in component preview mode.\u001b[39m\u001b[39m\r\n\u001b[33m4 WARN: \u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/ProfilePage.ets:15:1\n It's not a recommended way to export struct with '@Entry' decorator, which may cause ACE Engine error in component preview mode.\u001b[39m\u001b[39m\r\n\u001b[33m5 WARN: \u001b[33mArkTS:WARN File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/FeaturedPage.ets:18:1\n It's not a recommended way to export struct with '@Entry' decorator, which may cause ACE Engine error in component preview mode.\u001b[39m\u001b[39m\r\n\u001b[31m1 ERROR: \u001b[31m10605066 ArkTS Compiler Error\r\nError Message: \"in\" operator is not supported (arkts-no-in) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/Index.ets:33:63\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m2 ERROR: \u001b[31m10605029 ArkTS Compiler Error\r\nError Message: Indexed access is not supported for fields (arkts-no-props-by-index) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/Index.ets:34:27\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m3 ERROR: \u001b[31m10903329 ArkTS Compiler Error\r\nError Message: Unknown resource name 'ic_arrow_left'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/components/BottomNavigationBar.ets:133:22\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:4 WARN:5}\u001b[39m\n    at runArkPack (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5479)", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153849669100}, "additional": {"logType": "debug", "children": [], "durationId": "0cb08c03-41b9-424c-baec-e78b8687bdad"}}, {"head": {"id": "08e8f0a0-8dd0-4439-a0dc-319cec0b9f03", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153866212600, "endTime": 157153866485100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "34aaa270-462f-4ce5-9d28-f09e19ffce08", "logId": "29943ddd-2702-42a0-9613-5413d5fbbe11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29943ddd-2702-42a0-9613-5413d5fbbe11", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153866212600, "endTime": 157153866485100}, "additional": {"logType": "info", "children": [], "durationId": "08e8f0a0-8dd0-4439-a0dc-319cec0b9f03"}}, {"head": {"id": "d45b89c5-e7cb-4b13-9191-2aa9e7f9e079", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157135728122500, "endTime": 157153866871900}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 36, "second": 23}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "d87f2c89-e629-4326-b340-34ab1044f3e7", "name": "BUILD FAILED in 18 s 139 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153866956900}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "38e50e6e-b331-4cad-bc0f-08c433a3fa4c", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153867205000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb2c0b6b-b5a8-4085-bbe9-ee885c9967a1", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153867925700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65e4c191-4b5f-4d33-add1-1c685e13ff5e", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153868718200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5059bd6-ad1b-4e14-b700-fbf3008cbcdc", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153869151900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bd8c1c4-6c2c-4883-a655-1373f17191d1", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153869374300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "093c8443-510b-4def-a7cd-6e15232bebc0", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153869489600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c01f7178-818b-4335-ac81-ad071dd96a8b", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153869843500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "958d760f-8a55-4fd1-8ba7-40c9932c61de", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153870969400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd21f156-a9c3-46e6-a835-f525cf50c680", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153871334200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8302e216-cbbc-4ad9-be7a-8cb6001b62f2", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153871427900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "915f45e3-3b33-45fa-bfb6-419f00cae92e", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153871487000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc6fe95c-0a84-498c-b295-c42e39fbef59", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153871533400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01c2bce6-268e-484a-b53c-54339d59d2c2", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153871681200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abeeac8c-ec48-46ad-abce-5537ae1bc2c4", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153872926600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec321cb5-4c48-42d2-97c5-565c9df6f587", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153873377600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd3abfdb-d393-4e49-b805-3f8d43edffce", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153873653300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04698acc-d440-4842-9a7b-614a4dee3b8b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153873717300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "339f31a8-98a4-4972-9836-289a13743a1a", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153873756100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ecc1740-e5fe-42ac-92ca-3041db0cd8fb", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153873785600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab514b63-ad68-451d-89d0-b0679bf245f4", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153873894700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "942305a2-a080-45fa-a40f-cd1858271cdf", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153873958200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99c04e4f-80bd-4f12-9f10-3bdd46296d9a", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153882188600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca6552e2-3e9b-428f-86a8-ccb3a1b2889f", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153884153800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d15b7601-477e-4a80-8f15-3985216fdc40", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153885373900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d74d62a1-b556-4e18-b0f6-5f4d9deba24d", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153885723100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78792a7b-0cf2-4df5-a261-c34d0f970a0e", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153885988800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9850077e-6a9d-41f1-a13b-67b0a2a34019", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153887603400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0bc06c6-e920-4b3d-853d-77e65df15f90", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153887749800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3614af1c-add5-4f3d-8269-3d299825ba67", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153888281200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6356815-cf69-4a2d-8c90-b3b204355945", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153889128600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e44195e2-76dc-49c2-b3aa-c17c149751b2", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153890890800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a1ff358-4a57-4ba4-beca-0df77f907ba0", "name": "Incremental task entry:default@CompileArkTS post-execution cost:18 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153891972000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5dd06ba-fb2d-41ee-9a07-b5f17da2b877", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153897373600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f11e1ecc-1bcc-4d57-b0c4-b3fbdeb3793f", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153899037300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70320abb-9d07-456f-84e4-ed7f215f6cda", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153900097600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5418874b-7a92-40d4-849b-460fb7cf8335", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153900735400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbc899ac-507a-4150-ac98-6654a46d529c", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153901234700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68263ec6-d1ec-418b-9770-f05f5184c70f", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153903783500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "678f89d3-a52c-4349-bbec-c667dfbf87e9", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153905578500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeb5a012-5c58-4638-a099-59bd93ce4b50", "name": "Incremental task entry:default@BuildJS post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153906204300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3079f363-d285-4c2c-a593-d40c55c18be1", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 157153906332600}, "additional": {"logType": "debug", "children": []}}], "workLog": []}