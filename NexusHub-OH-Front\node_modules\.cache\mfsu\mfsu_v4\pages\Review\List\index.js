"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { <PERSON>Container } from "@ant-design/pro-components";
import { Card, Table, Button, Tag, Space, Input, Select, DatePicker, Modal, Form, Rate, message } from "antd";
import { SearchOutlined, ReloadOutlined, EyeOutlined, DeleteOutlined, CheckOutlined, StopOutlined } from "@ant-design/icons";
import { useState } from "react";
import { useRequest } from "@umijs/max";
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;
const fetchReviewList = async (params) => {
  console.log("Fetching review list with params:", params);
  const mockData = [
    {
      id: "1",
      appId: "app001",
      appName: "\u5FAE\u4FE1",
      userId: "user001",
      username: "user12345",
      content: "\u975E\u5E38\u597D\u7528\u7684\u793E\u4EA4\u8F6F\u4EF6\uFF0C\u529F\u80FD\u4E30\u5BCC\uFF0C\u754C\u9762\u7B80\u6D01\uFF0C\u63A8\u8350\u4F7F\u7528\uFF01",
      rating: 5,
      status: "approved",
      createdAt: "2023-12-10 14:30:25",
      updatedAt: "2023-12-10 15:20:10",
      likeCount: 45,
      replyCount: 3
    },
    {
      id: "2",
      appId: "app002",
      appName: "\u652F\u4ED8\u5B9D",
      userId: "user002",
      username: "techfan",
      content: "\u652F\u4ED8\u529F\u80FD\u5F88\u65B9\u4FBF\uFF0C\u4F46\u6700\u8FD1\u66F4\u65B0\u540E\u5076\u5C14\u4F1A\u51FA\u73B0\u5361\u987F\u73B0\u8C61\u3002",
      rating: 4,
      status: "approved",
      createdAt: "2023-12-09 10:15:30",
      updatedAt: "2023-12-09 11:05:22",
      likeCount: 32,
      replyCount: 5
    },
    {
      id: "3",
      appId: "app003",
      appName: "\u6296\u97F3",
      userId: "user003",
      username: "videomaker",
      content: "\u77ED\u89C6\u9891\u5236\u4F5C\u529F\u80FD\u5F88\u5F3A\u5927\uFF0C\u4F46\u5E7F\u544A\u592A\u591A\u4E86\uFF0C\u5E0C\u671B\u80FD\u51CF\u5C11\u4E00\u4E9B\u3002",
      rating: 3,
      status: "approved",
      createdAt: "2023-12-08 16:45:12",
      updatedAt: "2023-12-08 17:30:05",
      likeCount: 28,
      replyCount: 7
    },
    {
      id: "4",
      appId: "app004",
      appName: "\u6DD8\u5B9D",
      userId: "user004",
      username: "shopper",
      content: "\u8D2D\u7269\u4F53\u9A8C\u5F88\u597D\uFF0C\u4F46\u5BA2\u670D\u54CD\u5E94\u6709\u70B9\u6162\u3002",
      rating: 4,
      status: "approved",
      createdAt: "2023-12-07 09:20:45",
      updatedAt: "2023-12-07 10:10:33",
      likeCount: 19,
      replyCount: 2
    },
    {
      id: "5",
      appId: "app005",
      appName: "\u7F51\u6613\u4E91\u97F3\u4E50",
      userId: "user005",
      username: "musiclover",
      content: "\u97F3\u4E50\u63A8\u8350\u7B97\u6CD5\u5F88\u51C6\u786E\uFF0C\u53D1\u73B0\u4E86\u5F88\u591A\u559C\u6B22\u7684\u6B4C\u66F2\uFF01",
      rating: 5,
      status: "approved",
      createdAt: "2023-12-06 20:30:18",
      updatedAt: "2023-12-06 21:15:27",
      likeCount: 56,
      replyCount: 4
    },
    {
      id: "6",
      appId: "app001",
      appName: "\u5FAE\u4FE1",
      userId: "user006",
      username: "chatmaster",
      content: "\u6700\u8FD1\u66F4\u65B0\u540E\uFF0C\u8BED\u97F3\u901A\u8BDD\u8D28\u91CF\u6709\u6240\u4E0B\u964D\uFF0C\u5E0C\u671B\u80FD\u6539\u8FDB\u3002",
      rating: 3,
      status: "pending",
      createdAt: "2023-12-05 15:40:22",
      updatedAt: "2023-12-05 15:40:22",
      likeCount: 8,
      replyCount: 0
    },
    {
      id: "7",
      appId: "app006",
      appName: "QQ",
      userId: "user007",
      username: "gamer2023",
      content: "\u804A\u5929\u529F\u80FD\u5F88\u7A33\u5B9A\uFF0C\u4F46\u6E38\u620F\u4E2D\u5FC3\u7684\u6E38\u620F\u8D28\u91CF\u53C2\u5DEE\u4E0D\u9F50\u3002",
      rating: 4,
      status: "approved",
      createdAt: "2023-12-04 18:25:36",
      updatedAt: "2023-12-04 19:10:42",
      likeCount: 23,
      replyCount: 3
    },
    {
      id: "8",
      appId: "app007",
      appName: "\u7F8E\u56E2",
      userId: "user008",
      username: "foodie",
      content: "\u5916\u5356\u9001\u8FBE\u901F\u5EA6\u5FEB\uFF0C\u4F46\u6709\u65F6\u5019\u5546\u5BB6\u51FA\u9910\u6162\u5F71\u54CD\u4F53\u9A8C\u3002",
      rating: 4,
      status: "approved",
      createdAt: "2023-12-03 12:50:15",
      updatedAt: "2023-12-03 13:35:28",
      likeCount: 17,
      replyCount: 2
    },
    {
      id: "9",
      appId: "app008",
      appName: "\u77E5\u4E4E",
      userId: "user009",
      username: "thinker",
      content: "\u5185\u5BB9\u8D28\u91CF\u9AD8\uFF0C\u4F46\u5E7F\u544A\u8D8A\u6765\u8D8A\u591A\uFF0C\u5F71\u54CD\u9605\u8BFB\u4F53\u9A8C\u3002",
      rating: 3,
      status: "rejected",
      createdAt: "2023-12-02 14:15:33",
      updatedAt: "2023-12-02 15:05:47",
      likeCount: 12,
      replyCount: 1
    },
    {
      id: "10",
      appId: "app009",
      appName: "Bilibili",
      userId: "user010",
      username: "animefan",
      content: "\u89C6\u9891\u5185\u5BB9\u4E30\u5BCC\uFF0C\u5F39\u5E55\u4E92\u52A8\u5F88\u6709\u8DA3\uFF0C\u4F46\u6709\u65F6\u5019\u7F13\u51B2\u901F\u5EA6\u6162\u3002",
      rating: 4,
      status: "approved",
      createdAt: "2023-12-01 19:30:27",
      updatedAt: "2023-12-01 20:20:38",
      likeCount: 41,
      replyCount: 6
    }
  ];
  const statusFilteredData = params.status ? mockData.filter((item) => item.status === params.status) : mockData;
  const ratingFilteredData = params.rating ? statusFilteredData.filter((item) => item.rating === params.rating) : statusFilteredData;
  const keywordFilteredData = params.keyword ? ratingFilteredData.filter(
    (item) => item.content.toLowerCase().includes(params.keyword.toLowerCase()) || item.appName.toLowerCase().includes(params.keyword.toLowerCase()) || item.username.toLowerCase().includes(params.keyword.toLowerCase())
  ) : ratingFilteredData;
  return { data: keywordFilteredData, total: keywordFilteredData.length };
};
const ReviewList = () => {
  const [searchParams, setSearchParams] = useState({});
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentReview, setCurrentReview] = useState(null);
  const { data, loading, refresh } = useRequest(() => fetchReviewList(searchParams), {
    refreshDeps: [searchParams]
  });
  const handleSearch = (values) => {
    setSearchParams(values);
  };
  const handleViewDetail = (record) => {
    setCurrentReview(record);
    setDetailModalVisible(true);
  };
  const handleDelete = (id) => {
    message.success(`\u5DF2\u5220\u9664\u8BC4\u8BBA ID: ${id}`);
    refresh();
  };
  const handleBatchDelete = () => {
    message.success(`\u5DF2\u6279\u91CF\u5220\u9664 ${selectedRowKeys.length} \u6761\u8BC4\u8BBA`);
    setSelectedRowKeys([]);
    refresh();
  };
  const handleApprove = (id) => {
    message.success(`\u5DF2\u901A\u8FC7\u8BC4\u8BBA ID: ${id}`);
    refresh();
  };
  const handleReject = (id) => {
    message.success(`\u5DF2\u62D2\u7EDD\u8BC4\u8BBA ID: ${id}`);
    refresh();
  };
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80
    },
    {
      title: "\u5E94\u7528\u540D\u79F0",
      dataIndex: "appName",
      key: "appName",
      width: 120
    },
    {
      title: "\u7528\u6237\u540D",
      dataIndex: "username",
      key: "username",
      width: 120
    },
    {
      title: "\u8BC4\u5206",
      dataIndex: "rating",
      key: "rating",
      width: 100,
      render: (rating) => /* @__PURE__ */ jsx(Rate, { disabled: true, defaultValue: rating })
    },
    {
      title: "\u8BC4\u8BBA\u5185\u5BB9",
      dataIndex: "content",
      key: "content",
      ellipsis: true,
      width: 300
    },
    {
      title: "\u72B6\u6001",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status) => {
        let color = "blue";
        let text = "\u5F85\u5BA1\u6838";
        if (status === "approved") {
          color = "green";
          text = "\u5DF2\u901A\u8FC7";
        } else if (status === "rejected") {
          color = "red";
          text = "\u5DF2\u62D2\u7EDD";
        }
        return /* @__PURE__ */ jsx(Tag, { color, children: text });
      }
    },
    {
      title: "\u70B9\u8D5E\u6570",
      dataIndex: "likeCount",
      key: "likeCount",
      width: 80
    },
    {
      title: "\u56DE\u590D\u6570",
      dataIndex: "replyCount",
      key: "replyCount",
      width: 80
    },
    {
      title: "\u521B\u5EFA\u65F6\u95F4",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150
    },
    {
      title: "\u64CD\u4F5C",
      key: "action",
      width: 200,
      render: (_, record) => /* @__PURE__ */ jsxs(Space, { size: "small", children: [
        /* @__PURE__ */ jsx(
          Button,
          {
            type: "text",
            icon: /* @__PURE__ */ jsx(EyeOutlined, {}),
            onClick: () => handleViewDetail(record),
            children: "\u67E5\u770B"
          }
        ),
        record.status === "pending" && /* @__PURE__ */ jsxs(Fragment, { children: [
          /* @__PURE__ */ jsx(
            Button,
            {
              type: "text",
              icon: /* @__PURE__ */ jsx(CheckOutlined, {}),
              style: { color: "green" },
              onClick: () => handleApprove(record.id),
              children: "\u901A\u8FC7"
            }
          ),
          /* @__PURE__ */ jsx(
            Button,
            {
              type: "text",
              icon: /* @__PURE__ */ jsx(StopOutlined, {}),
              style: { color: "red" },
              onClick: () => handleReject(record.id),
              children: "\u62D2\u7EDD"
            }
          )
        ] }),
        /* @__PURE__ */ jsx(
          Button,
          {
            type: "text",
            danger: true,
            icon: /* @__PURE__ */ jsx(DeleteOutlined, {}),
            onClick: () => handleDelete(record.id),
            children: "\u5220\u9664"
          }
        )
      ] })
    }
  ];
  return /* @__PURE__ */ jsxs(
    PageContainer,
    {
      header: {
        title: "\u8BC4\u8BBA\u5217\u8868",
        subTitle: "\u67E5\u770B\u548C\u7BA1\u7406\u6240\u6709\u5E94\u7528\u8BC4\u8BBA"
      },
      children: [
        /* @__PURE__ */ jsxs(Card, { children: [
          /* @__PURE__ */ jsxs(Form, { layout: "inline", onFinish: handleSearch, style: { marginBottom: 24 }, children: [
            /* @__PURE__ */ jsx(Form.Item, { name: "keyword", label: "\u5173\u952E\u8BCD", children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BC4\u8BBA\u5185\u5BB9/\u5E94\u7528\u540D\u79F0/\u7528\u6237\u540D", prefix: /* @__PURE__ */ jsx(SearchOutlined, {}) }) }),
            /* @__PURE__ */ jsx(Form.Item, { name: "status", label: "\u72B6\u6001", children: /* @__PURE__ */ jsxs(Select, { style: { width: 120 }, placeholder: "\u5168\u90E8\u72B6\u6001", allowClear: true, children: [
              /* @__PURE__ */ jsx(Option, { value: "approved", children: "\u5DF2\u901A\u8FC7" }),
              /* @__PURE__ */ jsx(Option, { value: "rejected", children: "\u5DF2\u62D2\u7EDD" }),
              /* @__PURE__ */ jsx(Option, { value: "pending", children: "\u5F85\u5BA1\u6838" })
            ] }) }),
            /* @__PURE__ */ jsx(Form.Item, { name: "rating", label: "\u8BC4\u5206", children: /* @__PURE__ */ jsxs(Select, { style: { width: 120 }, placeholder: "\u5168\u90E8\u8BC4\u5206", allowClear: true, children: [
              /* @__PURE__ */ jsx(Option, { value: 1, children: "1\u661F" }),
              /* @__PURE__ */ jsx(Option, { value: 2, children: "2\u661F" }),
              /* @__PURE__ */ jsx(Option, { value: 3, children: "3\u661F" }),
              /* @__PURE__ */ jsx(Option, { value: 4, children: "4\u661F" }),
              /* @__PURE__ */ jsx(Option, { value: 5, children: "5\u661F" })
            ] }) }),
            /* @__PURE__ */ jsx(Form.Item, { name: "dateRange", label: "\u65F6\u95F4\u8303\u56F4", children: /* @__PURE__ */ jsx(RangePicker, {}) }),
            /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsx(Button, { type: "primary", htmlType: "submit", children: "\u641C\u7D22" }) }),
            /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsx(Button, { onClick: () => setSearchParams({}), children: "\u91CD\u7F6E" }) })
          ] }),
          /* @__PURE__ */ jsxs("div", { style: { marginBottom: 16 }, children: [
            /* @__PURE__ */ jsx(
              Button,
              {
                danger: true,
                disabled: selectedRowKeys.length === 0,
                onClick: handleBatchDelete,
                children: "\u6279\u91CF\u5220\u9664"
              }
            ),
            /* @__PURE__ */ jsx(
              Button,
              {
                icon: /* @__PURE__ */ jsx(ReloadOutlined, {}),
                style: { marginLeft: 8 },
                onClick: refresh,
                children: "\u5237\u65B0"
              }
            ),
            /* @__PURE__ */ jsx("span", { style: { marginLeft: 8 }, children: selectedRowKeys.length > 0 ? `\u5DF2\u9009\u62E9 ${selectedRowKeys.length} \u9879` : "" })
          ] }),
          /* @__PURE__ */ jsx(
            Table,
            {
              rowSelection: {
                selectedRowKeys,
                onChange: setSelectedRowKeys
              },
              columns,
              dataSource: data?.data,
              rowKey: "id",
              loading,
              pagination: {
                total: data?.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `\u5171 ${total} \u6761`
              },
              scroll: { x: 1500 }
            }
          )
        ] }),
        /* @__PURE__ */ jsx(
          Modal,
          {
            title: "\u8BC4\u8BBA\u8BE6\u60C5",
            open: detailModalVisible,
            onCancel: () => setDetailModalVisible(false),
            footer: null,
            width: 600,
            children: currentReview && /* @__PURE__ */ jsxs("div", { children: [
              /* @__PURE__ */ jsxs("p", { children: [
                /* @__PURE__ */ jsx("strong", { children: "\u5E94\u7528\u540D\u79F0\uFF1A" }),
                " ",
                currentReview.appName
              ] }),
              /* @__PURE__ */ jsxs("p", { children: [
                /* @__PURE__ */ jsx("strong", { children: "\u7528\u6237\u540D\uFF1A" }),
                " ",
                currentReview.username
              ] }),
              /* @__PURE__ */ jsxs("p", { children: [
                /* @__PURE__ */ jsx("strong", { children: "\u8BC4\u5206\uFF1A" }),
                " ",
                /* @__PURE__ */ jsx(Rate, { disabled: true, defaultValue: currentReview.rating })
              ] }),
              /* @__PURE__ */ jsx("p", { children: /* @__PURE__ */ jsx("strong", { children: "\u8BC4\u8BBA\u5185\u5BB9\uFF1A" }) }),
              /* @__PURE__ */ jsx("p", { style: { background: "#f5f5f5", padding: 12, borderRadius: 4 }, children: currentReview.content }),
              /* @__PURE__ */ jsxs("p", { children: [
                /* @__PURE__ */ jsx("strong", { children: "\u521B\u5EFA\u65F6\u95F4\uFF1A" }),
                " ",
                currentReview.createdAt
              ] }),
              /* @__PURE__ */ jsxs("p", { children: [
                /* @__PURE__ */ jsx("strong", { children: "\u66F4\u65B0\u65F6\u95F4\uFF1A" }),
                " ",
                currentReview.updatedAt
              ] }),
              /* @__PURE__ */ jsxs("p", { children: [
                /* @__PURE__ */ jsx("strong", { children: "\u70B9\u8D5E\u6570\uFF1A" }),
                " ",
                currentReview.likeCount
              ] }),
              /* @__PURE__ */ jsxs("p", { children: [
                /* @__PURE__ */ jsx("strong", { children: "\u56DE\u590D\u6570\uFF1A" }),
                " ",
                currentReview.replyCount
              ] }),
              /* @__PURE__ */ jsxs("p", { children: [
                /* @__PURE__ */ jsx("strong", { children: "\u72B6\u6001\uFF1A" }),
                currentReview.status === "approved" && /* @__PURE__ */ jsx(Tag, { color: "green", children: "\u5DF2\u901A\u8FC7" }),
                currentReview.status === "rejected" && /* @__PURE__ */ jsx(Tag, { color: "red", children: "\u5DF2\u62D2\u7EDD" }),
                currentReview.status === "pending" && /* @__PURE__ */ jsx(Tag, { color: "blue", children: "\u5F85\u5BA1\u6838" })
              ] })
            ] })
          }
        )
      ]
    }
  );
};
export default ReviewList;
