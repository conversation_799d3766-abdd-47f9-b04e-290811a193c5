"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { AppstoreOutlined, CloudDownloadOutlined, TeamOutlined, CommentOutlined, InfoCircleOutlined } from "@ant-design/icons";
import { Area, Column } from "@ant-design/plots";
import { Col, Progress, Row, Tooltip } from "antd";
import numeral from "numeral";
import useStyles from "../style.style";
import { ChartCard, Field } from "./Charts";
import Trend from "./Trend";
const topColResponsiveProps = {
  xs: 24,
  sm: 12,
  md: 12,
  lg: 12,
  xl: 6,
  style: {
    marginBottom: 24
  }
};
const IntroduceRow = ({ loading, summaryData }) => {
  const { styles } = useStyles();
  const {
    total_apps = 0,
    total_downloads = 0,
    total_users = 0,
    total_reviews = 0,
    new_apps_today = 0,
    new_downloads_today = 0,
    new_users_today = 0,
    pending_apps_count = 0,
    pending_reviews_count = 0,
    total_developers = 0
  } = summaryData || {};
  const totalPending = pending_apps_count + pending_reviews_count;
  const totalItems = total_apps + total_reviews;
  const pendingPercent = totalItems > 0 ? Math.round(totalPending / totalItems * 100) : 0;
  return /* @__PURE__ */ jsxs(Row, { gutter: 24, children: [
    /* @__PURE__ */ jsx(Col, { ...topColResponsiveProps, children: /* @__PURE__ */ jsxs(
      ChartCard,
      {
        bordered: false,
        title: "\u5E94\u7528\u7EDF\u8BA1",
        action: /* @__PURE__ */ jsx(Tooltip, { title: "\u5E73\u53F0\u4E0A\u7684\u5E94\u7528\u603B\u6570\u548C\u4ECA\u65E5\u65B0\u589E\u6570\u91CF", children: /* @__PURE__ */ jsx(InfoCircleOutlined, {}) }),
        loading,
        total: () => numeral(total_apps).format("0,0"),
        footer: /* @__PURE__ */ jsx(Field, { label: "\u4ECA\u65E5\u65B0\u589E", value: numeral(new_apps_today).format("0,0") }),
        contentHeight: 46,
        children: [
          /* @__PURE__ */ jsx(
            AppstoreOutlined,
            {
              style: {
                position: "absolute",
                right: 16,
                top: 4,
                color: "#1890ff",
                fontSize: 32,
                opacity: 0.4
              }
            }
          ),
          /* @__PURE__ */ jsxs(
            Trend,
            {
              flag: "up",
              style: {
                marginRight: 16
              },
              children: [
                "\u5F00\u53D1\u8005",
                /* @__PURE__ */ jsx("span", { className: styles.trendText, children: numeral(total_developers).format("0,0") })
              ]
            }
          ),
          /* @__PURE__ */ jsxs(Trend, { flag: "up", children: [
            "\u7C7B\u522B",
            /* @__PURE__ */ jsx("span", { className: styles.trendText, children: "12" })
          ] })
        ]
      }
    ) }),
    /* @__PURE__ */ jsx(Col, { ...topColResponsiveProps, children: /* @__PURE__ */ jsxs(
      ChartCard,
      {
        bordered: false,
        loading,
        title: "\u4E0B\u8F7D\u7EDF\u8BA1",
        action: /* @__PURE__ */ jsx(Tooltip, { title: "\u5E94\u7528\u603B\u4E0B\u8F7D\u91CF\u548C\u4ECA\u65E5\u65B0\u589E\u4E0B\u8F7D\u91CF", children: /* @__PURE__ */ jsx(InfoCircleOutlined, {}) }),
        total: numeral(total_downloads).format("0,0"),
        footer: /* @__PURE__ */ jsx(Field, { label: "\u4ECA\u65E5\u4E0B\u8F7D", value: numeral(new_downloads_today).format("0,0") }),
        contentHeight: 46,
        children: [
          /* @__PURE__ */ jsx(
            CloudDownloadOutlined,
            {
              style: {
                position: "absolute",
                right: 16,
                top: 4,
                color: "#52c41a",
                fontSize: 32,
                opacity: 0.4
              }
            }
          ),
          /* @__PURE__ */ jsx(
            Area,
            {
              xField: "x",
              yField: "y",
              shapeField: "smooth",
              height: 46,
              axis: false,
              style: {
                fill: "linear-gradient(-90deg, white 0%, #52c41a 100%)",
                fillOpacity: 0.6,
                width: "100%"
              },
              padding: -20,
              data: Array.isArray(summaryData?.download_trend) ? summaryData.download_trend.map((item) => ({
                x: item.date,
                y: item.value
              })) : []
            }
          )
        ]
      }
    ) }),
    /* @__PURE__ */ jsx(Col, { ...topColResponsiveProps, children: /* @__PURE__ */ jsxs(
      ChartCard,
      {
        bordered: false,
        loading,
        title: "\u7528\u6237\u7EDF\u8BA1",
        action: /* @__PURE__ */ jsx(Tooltip, { title: "\u5E73\u53F0\u603B\u7528\u6237\u6570\u548C\u4ECA\u65E5\u65B0\u589E\u7528\u6237\u6570", children: /* @__PURE__ */ jsx(InfoCircleOutlined, {}) }),
        total: numeral(total_users).format("0,0"),
        footer: /* @__PURE__ */ jsx(Field, { label: "\u4ECA\u65E5\u65B0\u589E", value: numeral(new_users_today).format("0,0") }),
        contentHeight: 46,
        children: [
          /* @__PURE__ */ jsx(
            TeamOutlined,
            {
              style: {
                position: "absolute",
                right: 16,
                top: 4,
                color: "#722ed1",
                fontSize: 32,
                opacity: 0.4
              }
            }
          ),
          /* @__PURE__ */ jsx(
            Column,
            {
              xField: "x",
              yField: "y",
              padding: -20,
              axis: false,
              height: 46,
              data: Array.isArray(summaryData?.user_trend) ? summaryData.user_trend.map((item) => ({
                x: item.date,
                y: item.value
              })).slice(-7) : [],
              scale: { x: { paddingInner: 0.4 } }
            }
          )
        ]
      }
    ) }),
    /* @__PURE__ */ jsx(Col, { ...topColResponsiveProps, children: /* @__PURE__ */ jsxs(
      ChartCard,
      {
        loading,
        bordered: false,
        title: "\u5BA1\u6838\u5F85\u529E",
        action: /* @__PURE__ */ jsx(Tooltip, { title: "\u5F85\u5BA1\u6838\u7684\u5E94\u7528\u548C\u8BC4\u8BBA\u6570\u91CF", children: /* @__PURE__ */ jsx(InfoCircleOutlined, {}) }),
        total: numeral(totalPending).format("0,0"),
        footer: /* @__PURE__ */ jsxs(
          "div",
          {
            style: {
              whiteSpace: "nowrap",
              overflow: "hidden"
            },
            children: [
              /* @__PURE__ */ jsxs(
                Trend,
                {
                  flag: "up",
                  style: {
                    marginRight: 16
                  },
                  children: [
                    "\u5F85\u5BA1\u5E94\u7528",
                    /* @__PURE__ */ jsx("span", { className: styles.trendText, children: numeral(pending_apps_count).format("0,0") })
                  ]
                }
              ),
              /* @__PURE__ */ jsxs(Trend, { flag: "up", children: [
                "\u5F85\u5BA1\u8BC4\u8BBA",
                /* @__PURE__ */ jsx("span", { className: styles.trendText, children: numeral(pending_reviews_count).format("0,0") })
              ] })
            ]
          }
        ),
        contentHeight: 46,
        children: [
          /* @__PURE__ */ jsx(
            CommentOutlined,
            {
              style: {
                position: "absolute",
                right: 16,
                top: 4,
                color: "#fa8c16",
                fontSize: 32,
                opacity: 0.4
              }
            }
          ),
          /* @__PURE__ */ jsx(Progress, { percent: pendingPercent, strokeColor: { from: "#fa8c16", to: "#ffd666" }, status: "active" })
        ]
      }
    ) })
  ] });
};
export default IntroduceRow;
