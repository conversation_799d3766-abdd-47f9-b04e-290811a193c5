package api

import (
	"strconv"
	"time"

	"nexushub-oh-back/internal/models"
	"nexushub-oh-back/pkg/auth"
	"nexushub-oh-back/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UserController 用户控制器
type UserController struct {
	DB         *gorm.DB
	JWTService *auth.JWTService
	Validate   *validator.Validate
}

// NewUserController 创建用户控制器
func NewUserController(db *gorm.DB, jwtService *auth.JWTService) *UserController {
	return &UserController{
		DB:         db,
		JWTService: jwtService,
		Validate:   validator.New(),
	}
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Email    string `json:"email" validate:"required,email"`
	Phone    string `json:"phone" validate:"omitempty,len=11"`
	Password string `json:"password" validate:"required,min=6,max=50"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	UsernameOrEmail string `json:"username_or_email" validate:"required"`
	Password        string `json:"password" validate:"required"`
}

// UpdateProfileRequest 更新用户资料请求
type UpdateProfileRequest struct {
	Username      string `json:"username" validate:"omitempty,min=3,max=50"`
	Email         string `json:"email" validate:"omitempty,email"`
	Phone         string `json:"phone" validate:"omitempty,len=11"`
	Avatar        string `json:"avatar"`
	OldPassword   string `json:"old_password" validate:"omitempty"`
	NewPassword   string `json:"new_password" validate:"omitempty,min=6,max=50"`
	// 基本信息字段
	Description   string `json:"description"`
	CompanyName   string `json:"company_name"`
	Address       string `json:"address"`
	Province      string `json:"province"`
	City          string `json:"city"`
	District      string `json:"district"`
	Street        string `json:"street"`
	IsDeveloper   bool   `json:"is_developer"`
	DeveloperInfo struct {
		DeveloperName string `json:"developer_name" validate:"required_if=IsDeveloper true"`
		CompanyName   string `json:"company_name"`
		Website       string `json:"website"`
		Description   string `json:"description"`
	} `json:"developer_info"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID            uint      `json:"id"`
	Username      string    `json:"username"`
	Email         string    `json:"email"`
	Phone         string    `json:"phone"`
	Role          string    `json:"role"`
	Status        string    `json:"status"`
	Avatar        string    `json:"avatar"`
	CreatedAt     time.Time `json:"created_at"`
	LastLoginAt   time.Time `json:"last_login_at"`
	IsDeveloper   bool      `json:"is_developer"`
	DeveloperName string    `json:"developer_name"`
	CompanyName   string    `json:"company_name"`
	Website       string    `json:"website"`
	Description   string    `json:"description"`
	VerifyStatus  string    `json:"verify_status"`
	// 地址信息字段
	Address       string    `json:"address"`
	Province      string    `json:"province"`
	City          string    `json:"city"`
	District      string    `json:"district"`
	Street        string    `json:"street"`
}

// AdminCreateUserRequest 管理员创建用户请求
type AdminCreateUserRequest struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Email    string `json:"email" validate:"required,email"`
	Phone    string `json:"phone" validate:"omitempty,len=11"`
	Password string `json:"password" validate:"required,min=6,max=50"`
	Role     string `json:"role" validate:"required,oneof=user developer operator reviewer admin"`
}

// UpdateUserRoleRequest 更新用户角色请求
type UpdateUserRoleRequest struct {
	Role string `json:"role" validate:"required,oneof=user developer operator reviewer admin"`
}

// UpdateUserStatusRequest 更新用户状态请求
type UpdateUserStatusRequest struct {
	Status string `json:"status" validate:"required,oneof=active suspended banned"`
}

// Register 用户注册
//	@Summary		用户注册
//	@Description	创建新用户账号
//	@Tags			用户
//	@Accept			json
//	@Produce		json
//	@Param			data	body		RegisterRequest	true	"用户注册信息"
//	@Success		200		{object}	Response		"注册成功"
//	@Failure		400		{object}	Response		"参数错误/用户名或邮箱已存在"
//	@Failure		500		{object}	Response		"服务器错误"
//	@Router			/users/register [post]
func (c *UserController) Register(ctx *gin.Context) {
	// 检查数据库连接
	if c.DB == nil {
		ServerError(ctx, "服务器配置错误：数据库未连接，请联系管理员", nil)
		return
	}

	var req RegisterRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		ParamError(ctx, err.Error())
		return
	}

	// 检查用户名是否已存在
	var count int64
	c.DB.Model(&models.User{}).Where("username = ?", req.Username).Count(&count)
	if count > 0 {
		Fail(ctx, 400, "用户名已被使用")
		return
	}

	// 检查邮箱是否已存在
	c.DB.Model(&models.User{}).Where("email = ?", req.Email).Count(&count)
	if count > 0 {
		Fail(ctx, 400, "邮箱已被使用")
		return
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		logger.Error("密码加密失败", zap.Error(err))
		ServerError(ctx, "注册失败，请稍后重试", err)
		return
	}

	// 创建用户
	user := &models.User{
		Username: req.Username,
		Email:    req.Email,
		Phone:    req.Phone,
		Password: string(hashedPassword),
		Role:     string(models.UserRoleUser),
		Status:   models.UserStatusActive,
	}

	if err := c.DB.Create(&user).Error; err != nil {
		logger.Error("注册失败", zap.Error(err))
		ServerError(ctx, "注册失败，请稍后重试", err)
		return
	}

	SuccessWithMessage(ctx, "注册成功", nil)
}

// Login 用户登录
//	@Summary		用户登录
//	@Description	登录获取认证令牌
//	@Tags			用户
//	@Accept			json
//	@Produce		json
//	@Param			data	body		LoginRequest							true	"登录信息"
//	@Success		200		{object}	Response{data=map[string]interface{}}	"登录成功，返回token和用户信息"
//	@Failure		400		{object}	Response								"参数错误/用户名或密码错误"
//	@Failure		403		{object}	Response								"账号已被禁用"
//	@Failure		500		{object}	Response								"服务器错误"
//	@Router			/users/login [post]
func (c *UserController) Login(ctx *gin.Context) {
	// 检查数据库连接
	if c.DB == nil {
		ServerError(ctx, "服务器配置错误：数据库未连接，请联系管理员", nil)
		return
	}

	var req LoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		ParamError(ctx, err.Error())
		return
	}

	// 查询用户（支持用户名或邮箱登录）
	var user *models.User
	var err error

	// 首先尝试通过用户名查询
	user, err = models.GetUserByUsername(c.DB, req.UsernameOrEmail)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果用户名不存在，尝试通过邮箱查询
			user, err = models.GetUserByEmail(c.DB, req.UsernameOrEmail)
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					Fail(ctx, 400, "用户名/邮箱或密码错误")
				} else {
					logger.Error("查询用户失败", zap.Error(err))
					ServerError(ctx, "登录失败，请稍后重试", err)
				}
				return
			}
		} else {
			logger.Error("查询用户失败", zap.Error(err))
			ServerError(ctx, "登录失败，请稍后重试", err)
			return
		}
	}

	// 检查用户状态
	if user.Status != models.UserStatusActive {
		Fail(ctx, 403, "账号已被禁用，请联系管理员")
		return
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		logger.Error("密码验证失败", zap.Error(err))
		ServerError(ctx, "登录失败，请稍后重试", err)
		return
	}

	// 生成JWT令牌
	token, err := c.JWTService.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		logger.Error("生成JWT令牌失败", zap.Error(err))
		ServerError(ctx, "登录失败，请稍后重试", err)
		return
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLoginAt = &now
	user.LoginCount++
	if err := c.DB.Model(&user).Update("last_login_at", now).Error; err != nil {
		logger.Error("更新最后登录时间失败", zap.Error(err))
		ServerError(ctx, "登录失败，请稍后重试", err)
		return
	}

	Success(ctx, gin.H{
		"token": token,
		"user": UserResponse{
			ID:            user.ID,
			Username:      user.Username,
			Email:         user.Email,
			Phone:         user.Phone,
			Role:          user.Role,
			Status:        string(user.Status),
			Avatar:        user.Avatar,
			CreatedAt:     user.CreatedAt,
			LastLoginAt:   now,
			IsDeveloper:   user.IsDeveloper,
			DeveloperName: user.DeveloperName,
			CompanyName:   user.CompanyName,
			Website:       user.Website,
			Description:   user.Description,
			VerifyStatus:  string(user.VerifyStatus),
		},
	})
}

// GetProfile 获取用户资料
//	@Summary		获取用户资料
//	@Description	获取当前登录用户的详细资料
//	@Tags			用户
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Success		200	{object}	Response{data=UserResponse}	"返回用户资料"
//	@Failure		401	{object}	Response					"未授权"
//	@Failure		404	{object}	Response					"用户不存在"
//	@Failure		500	{object}	Response					"服务器错误"
//	@Router			/users/profile [get]
func (c *UserController) GetProfile(ctx *gin.Context) {
	// 从上下文中获取用户信息
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "")
		return
	}

	// 查询用户
	user, err := models.GetUserByID(c.DB, userID.(uint))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "用户不存在")
		} else {
			logger.Error("查询用户失败", zap.Error(err))
			ServerError(ctx, "获取资料失败，请稍后重试", err)
		}
		return
	}

	lastLoginAt := time.Time{}
	if user.LastLoginAt != nil {
		lastLoginAt = *user.LastLoginAt
	}

	Success(ctx, UserResponse{
		ID:            user.ID,
		Username:      user.Username,
		Email:         user.Email,
		Phone:         user.Phone,
		Role:          user.Role,
		Status:        string(user.Status),
		Avatar:        user.Avatar,
		CreatedAt:     user.CreatedAt,
		LastLoginAt:   lastLoginAt,
		IsDeveloper:   user.IsDeveloper,
		DeveloperName: user.DeveloperName,
		CompanyName:   user.CompanyName,
		Website:       user.Website,
		Description:   user.Description,
		VerifyStatus:  string(user.VerifyStatus),
	})
}

// UpdateProfile 更新用户资料
//	@Summary		更新用户资料
//	@Description	更新当前登录用户的资料信息
//	@Tags			用户
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			data	body		UpdateProfileRequest		true	"更新的用户资料"
//	@Success		200		{object}	Response{data=UserResponse}	"更新成功，返回更新后的用户资料"
//	@Failure		400		{object}	Response					"参数错误/用户名或邮箱已存在/密码错误"
//	@Failure		401		{object}	Response					"未授权"
//	@Failure		404		{object}	Response					"用户不存在"
//	@Failure		500		{object}	Response					"服务器错误"
//	@Router			/users/profile [put]
func (c *UserController) UpdateProfile(ctx *gin.Context) {
	var req UpdateProfileRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		ParamError(ctx, err.Error())
		return
	}

	// 从上下文中获取用户信息
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "")
		return
	}

	// 查询用户
	user, err := models.GetUserByID(c.DB, userID.(uint))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "用户不存在")
		} else {
			logger.Error("查询用户失败", zap.Error(err))
			ServerError(ctx, "更新资料失败，请稍后重试", err)
		}
		return
	}

	// 更新用户名
	if req.Username != "" && req.Username != user.Username {
		// 检查用户名是否已存在
		var count int64
		c.DB.Model(&models.User{}).Where("username = ? AND id != ?", req.Username, user.ID).Count(&count)
		if count > 0 {
			Fail(ctx, 400, "用户名已被使用")
			return
		}
		user.Username = req.Username
	}

	// 更新邮箱
	if req.Email != "" && req.Email != user.Email {
		// 检查邮箱是否已存在
		var count int64
		c.DB.Model(&models.User{}).Where("email = ? AND id != ?", req.Email, user.ID).Count(&count)
		if count > 0 {
			Fail(ctx, 400, "邮箱已被使用")
			return
		}
		user.Email = req.Email
	}

	// 更新手机号
	if req.Phone != "" {
		user.Phone = req.Phone
	}

	// 更新头像
	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}

	// 更新基本信息字段
	if req.Description != "" {
		user.Description = req.Description
	}
	if req.CompanyName != "" {
		user.CompanyName = req.CompanyName
	}
	if req.Address != "" {
		user.Address = req.Address
	}
	if req.Province != "" {
		user.Province = req.Province
	}
	if req.City != "" {
		user.City = req.City
	}
	if req.District != "" {
		user.District = req.District
	}
	if req.Street != "" {
		user.Street = req.Street
	}

	// 更新密码
	if req.NewPassword != "" {
		// 验证旧密码
		if req.OldPassword == "" {
			Fail(ctx, 400, "请输入旧密码")
			return
		}

		if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.OldPassword)); err != nil {
			Fail(ctx, 400, "旧密码错误")
			return
		}

		// 加密新密码
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
		if err != nil {
			logger.Error("密码加密失败", zap.Error(err))
			ServerError(ctx, "更新密码失败，请稍后重试", err)
			return
		}
		user.Password = string(hashedPassword)
	}

	// 更新开发者信息
	if req.IsDeveloper && !user.IsDeveloper {
		if req.DeveloperInfo.DeveloperName == "" {
			Fail(ctx, 400, "请填写开发者名称")
			return
		}
		user.IsDeveloper = true
		user.DeveloperName = req.DeveloperInfo.DeveloperName
		user.CompanyName = req.DeveloperInfo.CompanyName
		user.Website = req.DeveloperInfo.Website
		user.Description = req.DeveloperInfo.Description
		user.VerifyStatus = models.VerifyStatusPending
	} else if req.IsDeveloper && user.IsDeveloper {
		// 已经是开发者，更新开发者信息
		if req.DeveloperInfo.DeveloperName != "" {
			user.DeveloperName = req.DeveloperInfo.DeveloperName
		}
		if req.DeveloperInfo.CompanyName != "" {
			user.CompanyName = req.DeveloperInfo.CompanyName
		}
		if req.DeveloperInfo.Website != "" {
			user.Website = req.DeveloperInfo.Website
		}
		if req.DeveloperInfo.Description != "" {
			user.Description = req.DeveloperInfo.Description
		}
	}

	// 保存用户信息
	if err := c.DB.Save(&user).Error; err != nil {
		logger.Error("更新用户失败", zap.Error(err))
		ServerError(ctx, "更新资料失败，请稍后重试", err)
		return
	}

	var lastLoginAt time.Time
	if user.LastLoginAt != nil {
		lastLoginAt = *user.LastLoginAt
	}

	SuccessWithMessage(ctx, "更新资料成功", UserResponse{
		ID:            user.ID,
		Username:      user.Username,
		Email:         user.Email,
		Phone:         user.Phone,
		Role:          user.Role,
		Status:        string(user.Status),
		Avatar:        user.Avatar,
		CreatedAt:     user.CreatedAt,
		LastLoginAt:   lastLoginAt,
		IsDeveloper:   user.IsDeveloper,
		DeveloperName: user.DeveloperName,
		CompanyName:   user.CompanyName,
		Website:       user.Website,
		Description:   user.Description,
		VerifyStatus:  string(user.VerifyStatus),
		Address:       user.Address,
		Province:      user.Province,
		City:          user.City,
		District:      user.District,
		Street:        user.Street,
	})
}

// AdminCreateUser 管理员创建用户
//	@Summary		管理员创建用户
//	@Description	管理员创建新用户并指定角色
//	@Tags			管理员
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			data	body		AdminCreateUserRequest	true	"用户信息"
//	@Success		200		{object}	Response				"创建成功"
//	@Failure		400		{object}	Response				"参数错误"
//	@Failure		401		{object}	Response				"未授权"
//	@Failure		403		{object}	Response				"权限不足"
//	@Failure		500		{object}	Response				"服务器错误"
//	@Router			/admin/users [post]
func (c *UserController) AdminCreateUser(ctx *gin.Context) {
	var req AdminCreateUserRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		ParamError(ctx, err.Error())
		return
	}

	// 检查用户名是否已存在
	var count int64
	c.DB.Model(&models.User{}).Where("username = ?", req.Username).Count(&count)
	if count > 0 {
		Fail(ctx, 400, "用户名已被使用")
		return
	}

	// 检查邮箱是否已存在
	c.DB.Model(&models.User{}).Where("email = ?", req.Email).Count(&count)
	if count > 0 {
		Fail(ctx, 400, "邮箱已被使用")
		return
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		logger.Error("密码加密失败", zap.Error(err))
		ServerError(ctx, "创建用户失败，请稍后重试", err)
		return
	}

	// 创建用户
	user := &models.User{
		Username: req.Username,
		Email:    req.Email,
		Phone:    req.Phone,
		Password: string(hashedPassword),
		Role:     req.Role,
		Status:   models.UserStatusActive,
	}

	if err := c.DB.Create(&user).Error; err != nil {
		logger.Error("管理员创建用户失败", zap.Error(err))
		ServerError(ctx, "创建用户失败，请稍后重试", err)
		return
	}

	SuccessWithMessage(ctx, "创建用户成功", nil)
}

// UpdateUserRole 更新用户角色
//	@Summary		更新用户角色
//	@Description	管理员更新用户角色
//	@Tags			管理员
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id		path		int						true	"用户ID"
//	@Param			data	body		UpdateUserRoleRequest	true	"角色信息"
//	@Success		200		{object}	Response				"更新成功"
//	@Failure		400		{object}	Response				"参数错误"
//	@Failure		401		{object}	Response				"未授权"
//	@Failure		403		{object}	Response				"权限不足"
//	@Failure		404		{object}	Response				"用户不存在"
//	@Failure		500		{object}	Response				"服务器错误"
//	@Router			/admin/users/{id}/role [put]
func (c *UserController) UpdateUserRole(ctx *gin.Context) {
	// 获取用户ID
	userID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		ParamError(ctx, "无效的用户ID")
		return
	}

	var req UpdateUserRoleRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		ParamError(ctx, err.Error())
		return
	}

	// 查询用户
	user, err := models.GetUserByID(c.DB, uint(userID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "用户不存在")
		} else {
			logger.Error("查询用户失败", zap.Error(err))
			ServerError(ctx, "更新角色失败，请稍后重试", err)
		}
		return
	}

	// 保存原始角色，用于后续判断
	originalRole := user.Role

	// 更新角色
	user.Role = req.Role
	if err := c.DB.Model(&user).Update("role", req.Role).Error; err != nil {
		logger.Error("更新用户角色失败", zap.Error(err))
		ServerError(ctx, "更新角色失败，请稍后重试", err)
		return
	}

	// 如果是将普通用户设为开发者，检查开发者认证状态
	if req.Role == string(models.UserRoleDeveloper) && originalRole == string(models.UserRoleUser) {
		if err := c.DB.Model(&user).Updates(map[string]interface{}{
			"is_developer":  true,
			"verify_status": models.VerifyStatusApproved,
		}).Error; err != nil {
			logger.Error("更新开发者状态失败", zap.Error(err))
			ServerError(ctx, "更新角色失败，请稍后重试", err)
			return
		}
	}

	// 如果是将开发者降级为普通用户，重置开发者认证状态，使其需要重新认证
	if req.Role == string(models.UserRoleUser) && originalRole == string(models.UserRoleDeveloper) {
		if err := c.DB.Model(&user).Updates(map[string]interface{}{
			"is_developer":  false,
			"verify_status": "", // 清空认证状态，使其需要重新认证
		}).Error; err != nil {
			logger.Error("重置开发者认证状态失败", zap.Error(err))
			ServerError(ctx, "更新角色失败，请稍后重试", err)
			return
		}
	}

	SuccessWithMessage(ctx, "更新角色成功", nil)
}

// ListUsers 获取用户列表（管理员权限）
//	@Summary		获取用户列表
//	@Description	管理员获取所有用户列表
//	@Tags			管理员
//	@Accept			json
//	@Produce		json
//	@Param			page			query		int								false	"页码，默认1"
//	@Param			pageSize		query		int								false	"每页数量，默认10"
//	@Param			keyword			query		string							false	"搜索关键词（用户名/邮箱/手机）"
//	@Param			role			query		string							false	"用户角色"
//	@Param			status			query		string							false	"用户状态"
//	@Param			developerStatus	query		string							false	"开发者状态"
//	@Success		200				{object}	Response{data=[]UserResponse}	"用户列表"
//	@Failure		401				{object}	Response						"未授权"
//	@Failure		403				{object}	Response						"无权限"
//	@Failure		500				{object}	Response						"服务器错误"
//	@Router			/admin/users [get]
func (c *UserController) ListUsers(ctx *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 获取筛选参数
	keyword := ctx.Query("keyword")
	role := ctx.Query("role")
	status := ctx.Query("status")
	developerStatus := ctx.Query("developerStatus")

	// 构建查询
	query := c.DB.Model(&models.User{})

	// 添加筛选条件
	if keyword != "" {
		query = query.Where("username LIKE ? OR email LIKE ? OR phone LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}
	if role != "" {
		query = query.Where("role = ?", role)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 开发者状态筛选
	if developerStatus != "" {
		// 由于用户表中包含verify_status字段，直接使用它进行筛选
		query = query.Where("verify_status = ?", developerStatus)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Error("获取用户总数失败", zap.Error(err))
		ServerError(ctx, "获取用户列表失败", err)
		return
	}

	// 获取分页数据
	var users []models.User
	if err := query.Limit(pageSize).Offset(offset).Find(&users).Error; err != nil {
		logger.Error("获取用户列表失败", zap.Error(err))
		ServerError(ctx, "获取用户列表失败", err)
		return
	}

	// 转换为响应格式
	var userResponses []UserResponse
	for _, user := range users {
		userResponse := UserResponse{
			ID:            user.ID,
			Username:      user.Username,
			Email:         user.Email,
			Phone:         user.Phone,
			Role:          user.Role,
			Status:        string(user.Status),
			Avatar:        user.Avatar,
			CreatedAt:     user.CreatedAt,
			IsDeveloper:   user.IsDeveloper,
			DeveloperName: user.DeveloperName,
			CompanyName:   user.CompanyName,
			Website:       user.Website,
			Description:   user.Description,
			VerifyStatus:  string(user.VerifyStatus),
		}

		if user.LastLoginAt != nil {
			userResponse.LastLoginAt = *user.LastLoginAt
		}

		userResponses = append(userResponses, userResponse)
	}

	Success(ctx, gin.H{
		"data":      userResponses,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// Logout 用户退出登录
//	@Summary		用户退出登录
//	@Description	使当前用户的JWT令牌失效
//	@Tags			用户
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Success		200	{object}	Response	"登出成功"
//	@Failure		401	{object}	Response	"未授权"
//	@Router			/users/logout [post]
func (c *UserController) Logout(ctx *gin.Context) {
	// 从上下文中获取用户信息
	userID, exists := ctx.Get("user_id")
	if !exists {
		Unauthorized(ctx, "")
		return
	}

	// 记录退出登录日志
	logger.Info("用户退出登录", zap.Uint("user_id", userID.(uint)))

	// 由于JWT是无状态的，这里只需要返回成功响应
	// 客户端需要删除本地存储的token
	SuccessWithMessage(ctx, "登出成功", nil)
}

// UpdateUserStatus 更新用户状态
//	@Summary		更新用户状态
//	@Description	管理员更新用户状态（禁用/启用）
//	@Tags			管理员
//	@Accept			json
//	@Produce		json
//	@Security		Bearer
//	@Param			id		path		int						true	"用户ID"
//	@Param			data	body		UpdateUserStatusRequest	true	"状态信息"
//	@Success		200		{object}	Response				"更新成功"
//	@Failure		400		{object}	Response				"参数错误"
//	@Failure		401		{object}	Response				"未授权"
//	@Failure		403		{object}	Response				"权限不足"
//	@Failure		404		{object}	Response				"用户不存在"
//	@Failure		500		{object}	Response				"服务器错误"
//	@Router			/admin/users/{id}/status [put]
func (c *UserController) UpdateUserStatus(ctx *gin.Context) {
	// 获取用户ID
	userID, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		ParamError(ctx, "无效的用户ID")
		return
	}

	var req UpdateUserStatusRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ParamError(ctx, "无效的请求参数")
		return
	}

	// 验证请求参数
	if err := c.Validate.Struct(req); err != nil {
		ParamError(ctx, err.Error())
		return
	}

	// 查询用户
	user, err := models.GetUserByID(c.DB, uint(userID))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "用户不存在")
		} else {
			logger.Error("查询用户失败", zap.Error(err))
			ServerError(ctx, "更新状态失败，请稍后重试", err)
		}
		return
	}

	// 更新状态
	status := models.UserStatus(req.Status)
	user.Status = status
	if err := c.DB.Model(&user).Update("status", status).Error; err != nil {
		logger.Error("更新用户状态失败", zap.Error(err))
		ServerError(ctx, "更新状态失败，请稍后重试", err)
		return
	}

	SuccessWithMessage(ctx, "更新状态成功", nil)
}

// GetUserDetail 获取用户详情
func (c *UserController) GetUserDetail(ctx *gin.Context) {
	userID := ctx.Param("id")
	if userID == "" {
		BadRequest(ctx, "用户ID不能为空")
		return
	}

	var user models.User
	if err := c.DB.Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "用户不存在")
			return
		}
		logger.Error("获取用户详情失败", zap.Error(err))
		ServerError(ctx, "获取用户详情失败，请稍后重试", err)
		return
	}

	// 隐藏敏感信息
	user.Password = ""

	SuccessWithMessage(ctx, "获取用户详情成功", user)
}

// GetUserLoginRecords 获取用户登录记录
func (c *UserController) GetUserLoginRecords(ctx *gin.Context) {
	userID := ctx.Param("id")
	if userID == "" {
		BadRequest(ctx, "用户ID不能为空")
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	offset := (page - 1) * pageSize

	// 验证用户是否存在
	var user models.User
	if err := c.DB.Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "用户不存在")
			return
		}
		logger.Error("查询用户失败", zap.Error(err))
		ServerError(ctx, "查询用户失败，请稍后重试", err)
		return
	}

	// 查询登录记录总数
	var total int64
	if err := c.DB.Model(&models.LoginRecord{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		logger.Error("查询登录记录总数失败", zap.Error(err))
		ServerError(ctx, "查询登录记录失败，请稍后重试", err)
		return
	}

	// 查询登录记录
	var records []models.LoginRecord
	if err := c.DB.Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&records).Error; err != nil {
		logger.Error("查询登录记录失败", zap.Error(err))
		ServerError(ctx, "查询登录记录失败，请稍后重试", err)
		return
	}

	result := map[string]interface{}{
		"data":     records,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	}

	SuccessWithMessage(ctx, "获取登录记录成功", result)
}

// GetUserAppRecords 获取用户应用记录
func (c *UserController) GetUserAppRecords(ctx *gin.Context) {
	userID := ctx.Param("id")
	if userID == "" {
		BadRequest(ctx, "用户ID不能为空")
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("pageSize", "10"))
	offset := (page - 1) * pageSize

	// 验证用户是否存在
	var user models.User
	if err := c.DB.Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			NotFound(ctx, "用户不存在")
			return
		}
		logger.Error("查询用户失败", zap.Error(err))
		ServerError(ctx, "查询用户失败，请稍后重试", err)
		return
	}

	// 查询应用记录总数
	var total int64
	if err := c.DB.Model(&models.Application{}).Where("developer_id = ?", userID).Count(&total).Error; err != nil {
		logger.Error("查询应用记录总数失败", zap.Error(err))
		ServerError(ctx, "查询应用记录失败，请稍后重试", err)
		return
	}

	// 查询应用记录
	var apps []models.Application
	if err := c.DB.Where("developer_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&apps).Error; err != nil {
		logger.Error("查询应用记录失败", zap.Error(err))
		ServerError(ctx, "查询应用记录失败，请稍后重试", err)
		return
	}

	result := map[string]interface{}{
		"data":     apps,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	}

	SuccessWithMessage(ctx, "获取应用记录成功", result)
}
