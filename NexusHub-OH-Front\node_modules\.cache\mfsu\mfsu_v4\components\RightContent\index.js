"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { SelectLang as UmiSelectLang } from "@umijs/max";
import { history, useModel } from "@umijs/max";
import { Space } from "antd";
import { AvatarDropdown } from "./AvatarDropdown";
import NoticeIcon from "../NoticeIcon";
export const SelectLang = () => {
  return /* @__PURE__ */ jsx(UmiSelectLang, {});
};
export const Question = () => {
  return /* @__PURE__ */ jsx(
    "div",
    {
      style: {
        display: "flex",
        height: 26
      },
      onClick: () => {
        history.push("/content/help");
      },
      children: /* @__PURE__ */ jsx(QuestionCircleOutlined, {})
    }
  );
};
export { AvatarDropdown };
const RightContent = () => {
  const { initialState } = useModel("@@initialState");
  return /* @__PURE__ */ jsxs(Space, { size: 16, children: [
    /* @__PURE__ */ jsx(Question, {}),
    /* @__PURE__ */ jsx(SelectLang, {}),
    initialState?.currentUser && /* @__PURE__ */ jsx(NoticeIcon, {}),
    /* @__PURE__ */ jsx(AvatarDropdown, { children: /* @__PURE__ */ jsx("span", { children: initialState?.currentUser?.username }) })
  ] });
};
export default RightContent;
