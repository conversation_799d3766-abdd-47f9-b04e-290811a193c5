import { HttpClient } from './HttpClient';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { 
  AppModel,
  AppDetailModel,
  AppListResponse,
  AppDetailResponse, 
  AppSearchParams,
  AppReviewListResponse
} from '../models/App';
import { CategoryModel, CategoryListResponse } from '../models/Category';
import { BannerModel, BannerResponse } from '../models/Banner';
import {
  UserModel,
  LoginRequest,
  LoginResponse, 
  RegisterRequest, 
  RegisterResponse,
  UserProfileResponse,
  UpdateProfileRequest
} from '../models/User';
import {
  FeaturedCollectionModel,
  FeaturedCollectionListResponse,
  FeaturedCollectionDetailResponse,
  FeaturedCollectionAppsResponse
} from '../models/FeaturedCollection';

// 定义后端分类响应接口
interface BackendCategoryResponse {
  id: number;
  name: string;
  description: string;
  icon: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string | null;
  parent_id?: number | null;
}

// 定义通用HTTP响应接口
interface HttpResponse<T> {
  code?: number;
  message?: string;
  data?: T;
}

// 定义分类数组响应类型
type CategoryArrayResponse = BackendCategoryResponse[] | HttpResponse<BackendCategoryResponse[]>;

// 定义请求参数接口
interface SearchParams {
  limit?: number;
  page?: number;
  page_size?: number;
}

// 定义评论请求接口
interface ReviewRequest {
  rating: number;
  content: string;
}

// 定义搜索相关接口
interface SearchSuggestionParams {
  keyword: string;
}

// 定义搜索历史接口
interface SearchHistoryRequest {
  keyword: string;
}

/**
 * API服务类
 */
export class ApiService {
  private static instance: ApiService;
  private httpClient: HttpClient;

  private constructor() {
    this.httpClient = HttpClient.getInstance();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  /**
   * 设置认证令牌
   */
  setAuthToken(token: string) {
    this.httpClient.setAuthToken(token);
  }

  // ==================== 用户相关API ====================

  /**
   * 用户注册
   */
  async register(data: RegisterRequest): Promise<RegisterResponse> {
    return await this.httpClient.post<RegisterResponse>('/users/register', data);
  }

  /**
   * 用户登录
   */
  async login(data: LoginRequest): Promise<LoginResponse> {
    return await this.httpClient.post<LoginResponse>('/users/login', data);
  }

  /**
   * 获取用户资料
   */
  async getUserProfile(): Promise<UserProfileResponse> {
    return await this.httpClient.get<UserProfileResponse>('/users/profile');
  }

  /**
   * 更新用户资料
   */
  async updateUserProfile(data: UpdateProfileRequest): Promise<UserProfileResponse> {
    return await this.httpClient.put<UserProfileResponse>('/users/profile', data as Record<string, Object>);
  }

  /**
   * 用户登出
   */
  async logout(): Promise<Record<string, Object>> {
    return await this.httpClient.post('/users/logout');
  }

  // ==================== 应用相关API ====================

  /**
   * 获取应用列表（公开接口）
   */
  async getAppList(params?: AppSearchParams): Promise<AppListResponse> {
    const queryParams: Record<string, string | number | boolean> = {};
    if (params) {
      Object.entries(params).forEach((entry:[string,number|string]) => {
        const key = entry[0];
        const value = entry[1];
        if (value !== undefined && value !== null) {
          queryParams[key] = value;
        }
      });
    }
    return await this.httpClient.get<AppListResponse>('/public/apps', queryParams);
  }

  /**
   * 获取应用详情（公开接口）
   */
  async getAppDetail(id: number): Promise<AppDetailResponse> {
    return await this.httpClient.get<AppDetailResponse>(`/public/apps/${id}`);
  }

  /**
   * 获取应用详情（匿名接口，保持兼容性）
   */
  async getAppDetailAnonymous(id: number): Promise<AppDetailResponse> {
    return await this.httpClient.get<AppDetailResponse>(`/public/apps/${id}`);
  }

  /**
   * 记录应用下载（公开接口）
   */
  async recordAppDownloadAnonymous(appId: number, versionId: number): Promise<Record<string, Object>> {
    return await this.httpClient.post(`/public/apps/${appId}/versions/${versionId}/download`);
  }



  /**
   * 获取推荐应用（公开接口）
   */
  async getRecommendedApps(page?: number, pageSize?: number): Promise<AppListResponse> {
    try {
      hilog.info(0x0000, 'ApiService', '正在请求推荐应用...');
      const params: Record<string, string | number | boolean> = {};
      if (page !== undefined) {
        params.page = page;
      }
      if (pageSize !== undefined) {
        params.page_size = pageSize;
      }
      const response = await this.httpClient.get<AppListResponse>('/public/apps/recommended', params);
      hilog.info(0x0000, 'ApiService', '推荐应用请求响应: %{public}s', JSON.stringify(response));
      return response;
    } catch (error) {
       hilog.error(0x0000, 'ApiService', '获取推荐应用失败: %{public}s', JSON.stringify(error));
       return {
         code: 500,
         message: error instanceof Error ? error.message : '获取推荐应用失败',
         data: {
           list: [],
           pagination: {
             page: 1,
             page_size: 10,
             total: 0,
             total_pages: 0
           }
         }
       };
     }
  }

  /**
   * 获取热门应用（公开接口）
   */
  async getPopularApps(page?: number, pageSize?: number): Promise<AppListResponse> {
    const params: Record<string, string | number | boolean> = {};
    if (page !== undefined) {
      params.page = page;
    }
    if (pageSize !== undefined) {
      params.page_size = pageSize;
    }
    return await this.httpClient.get<AppListResponse>('/public/apps/popular', params);
  }

  /**
   * 获取最新应用（公开接口）
   */
  async getLatestApps(page?: number, pageSize?: number): Promise<AppListResponse> {
    const params: Record<string, string | number | boolean> = {};
    if (page !== undefined) {
      params.page = page;
    }
    if (pageSize !== undefined) {
      params.page_size = pageSize;
    }
    return await this.httpClient.get<AppListResponse>('/public/apps/latest', params);
  }

  /**
   * 获取编辑推荐应用
   */
  async getEditorChoiceApps(limit?: number): Promise<AppListResponse> {
    const params: Record<string, string | number | boolean> = {};
    if (limit !== undefined) {
      params.limit = limit;
    }
    return await this.httpClient.get<AppListResponse>('/apps/editor-choice', params);
  }

  /**
   * 获取轮播图列表
   */
  async getBanners(): Promise<BannerResponse> {
    try {
      hilog.info(0x0000, 'ApiService', '正在请求轮播图列表...');
      const response = await this.httpClient.get<BannerResponse>('/public/banners');
      hilog.info(0x0000, 'ApiService', '轮播图列表请求响应: %{public}s', JSON.stringify(response));
      return response;
    } catch (error) {
      hilog.error(0x0000, 'ApiService', '获取轮播图列表失败: %{public}s', JSON.stringify(error));
      // 返回一个错误响应而不是抛出异常
      return {
        code: 500,
        message: error instanceof Error ? error.message : '获取轮播图列表失败',
        data: []
      };
    }
  }

  /**
   * 获取精选应用
   */
  async getFeaturedApps(limit?: number): Promise<AppListResponse> {
    const params: Record<string, string | number | boolean> = {};
    if (limit !== undefined) {
      params.limit = limit;
    }
    try {
       hilog.info(0x0000, 'ApiService', '正在请求精选应用...');
       const response = await this.httpClient.get<AppListResponse>('/public/apps/featured', params);
       hilog.info(0x0000, 'ApiService', '精选应用请求响应: %{public}s', JSON.stringify(response));
       return response;
     } catch (error) {
       hilog.error(0x0000, 'ApiService', '获取精选应用失败: %{public}s', JSON.stringify(error));
       // 返回一个错误响应而不是抛出异常
       return {
         code: 500,
         message: error instanceof Error ? error.message : '获取精选应用失败',
         data: {
           list: [],
           pagination: {
             page: 1,
             page_size: 10,
             total: 0,
             total_pages: 0
           }
         }
       };
     }
  }

  /**
   * 获取应用评论（公开接口）
   */
  async getAppReviews(appId: number, page?: number, pageSize?: number): Promise<AppReviewListResponse> {
    const params: Record<string, string | number | boolean> = {};
    if (page !== undefined) {
      params.page = page;
    }
    if (pageSize !== undefined) {
      params.page_size = pageSize;
    }
    return await this.httpClient.get(`/apps/${appId}/reviews`, params);
  }

  /**
   * 提交应用评论
   */
  async submitAppReview(appId: number, rating: number, content: string): Promise<Record<string, Object>> {
    const reviewData: ReviewRequest = {
      rating,
      content
    };
    return await this.httpClient.post(`/apps/${appId}/reviews`, reviewData);
  }

  /**
   * 下载应用
   */
  async downloadApp(appId: number): Promise<Record<string, Object>> {
    return await this.httpClient.post(`/apps/${appId}/download`);
  }

  /**
   * 收藏应用
   */
  async favoriteApp(appId: number): Promise<Record<string, Object>> {
    return await this.httpClient.post(`/apps/${appId}/favorite`);
  }

  /**
   * 取消收藏应用
   */
  async unfavoriteApp(appId: number): Promise<Record<string, Object>> {
    return await this.httpClient.delete(`/apps/${appId}/favorite`);
  }

  /**
   * 获取用户收藏的应用
   */
  async getFavoriteApps(page?: number, pageSize?: number): Promise<AppListResponse> {
    const params: Record<string, string | number | boolean> = {};
    if (page !== undefined) {
      params.page = page;
    }
    if (pageSize !== undefined) {
      params.page_size = pageSize;
    }
    return await this.httpClient.get<AppListResponse>('/users/favorites', params);
  }

  /**
   * 获取用户下载历史
   */
  async getDownloadHistory(page?: number, pageSize?: number): Promise<AppListResponse> {
    const params: Record<string, string | number | boolean> = {};
    if (page !== undefined) {
      params.page = page;
    }
    if (pageSize !== undefined) {
      params.page_size = pageSize;
    }
    return await this.httpClient.get<AppListResponse>('/users/downloads', params);
  }

  // ==================== 分类相关API ====================

  /**
   * 转换后端分类数据为前端格式
   */
  private transformCategoryData(backendCategory: BackendCategoryResponse): CategoryModel {
    // 预定义颜色数组，用于为分类分配颜色
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
      '#82E0AA', '#F8C471'
    ];

    return {
      id: backendCategory.id || 0,
      name: backendCategory.name || '',
      name_en: this.generateEnglishName(backendCategory.name || ''), // 生成英文名
      description: backendCategory.description || '',
      icon: backendCategory.icon || this.getDefaultIcon(backendCategory.name || ''), // 获取默认图标
      color: colors[(backendCategory.id || 0) % colors.length], // 根据ID分配颜色
      sort_order: backendCategory.sort_order || 0,
      is_active: backendCategory.is_active !== undefined ? backendCategory.is_active : true,
      app_count: 0, // 默认为0，后续可通过其他API获取
      created_at: backendCategory.created_at || '',
      updated_at: backendCategory.updated_at || '',
      deleted_at: backendCategory.deleted_at || null,
      parent_id: backendCategory.parent_id || null
    };
  }

  /**
   * 生成英文名称
   */
  private generateEnglishName(chineseName: string): string {
    const nameMap: Record<string, string> = {
      '工具': 'Tools',
      '游戏': 'Games',
      '教育': 'Education',
      '生活': 'Lifestyle',
      '社交': 'Social',
      '摄影': 'Photography',
      '音乐': 'Music',
      '视频': 'Video',
      '新闻': 'News',
      '购物': 'Shopping',
      '旅行': 'Travel',
      '健康': 'Health',
      '办公': 'Office',
      '休闲': 'Casual',
      '动作': 'Action',
      '策略': 'Strategy',
      '角色扮演': 'RPG',
      'F-oh': 'F-oh'
    };
    return nameMap[chineseName] || chineseName;
  }

  /**
   * 获取默认图标
   */
  private getDefaultIcon(categoryName: string): string {
    const iconMap: Record<string, string> = {
      '工具': '🔧',
      '游戏': '🎮',
      '教育': '📚',
      '生活': '🏠',
      '社交': '💬',
      '摄影': '📷',
      '音乐': '🎵',
      '视频': '🎬',
      '新闻': '📰',
      '购物': '🛒',
      '旅行': '✈️',
      '健康': '🏥',
      '办公': '💼',
      '休闲': '🎯',
      '动作': '⚡',
      '策略': '🧠',
      '角色扮演': '🎭'
    };
    return iconMap[categoryName] || '📱';
  }

  /**
   * 获取应用分类列表（公开接口）
   */
  async getAppCategories(): Promise<CategoryListResponse> {
    try {
       hilog.info(0x0000, 'ApiService', '正在请求应用分类...');
       const response = await this.httpClient.get<CategoryArrayResponse>('/public/categories');
       hilog.info(0x0000, 'ApiService', '应用分类请求响应: %{public}s', JSON.stringify(response));

       // 检查响应格式
       if (Array.isArray(response)) {
         // 直接返回数组的情况
         const transformedData = response.map((item: BackendCategoryResponse) => this.transformCategoryData(item));
         return {
           code: 200,
           message: 'success',
           data: transformedData
         };
       } else if (response && response.data && Array.isArray(response.data)) {
         // 包装在data字段中的情况
         const transformedData = response.data.map((item: BackendCategoryResponse) => this.transformCategoryData(item));
         return {
           code: response.code || 200,
           message: response.message || 'success',
           data: transformedData
         };
       } else {
         // 其他格式，尝试直接转换
         const transformedData = response ? [this.transformCategoryData(response as BackendCategoryResponse)] : [];
         return {
           code: 200,
           message: 'success',
           data: transformedData
         };
       }
     } catch (error) {
       hilog.error(0x0000, 'ApiService', '获取应用分类失败: %{public}s', JSON.stringify(error));
       return {
         code: 500,
         message: error instanceof Error ? error.message : '获取应用分类失败',
         data: []
       };
     }
  }

  /**
   * 根据分类获取应用（公开接口）
   */
  async getAppsByCategory(categoryId: number, page?: number, pageSize?: number): Promise<AppListResponse> {
    const queryParams: Record<string, string | number | boolean> = {};
    if (page !== undefined) {
      queryParams.page = page;
    }
    if (pageSize !== undefined) {
      queryParams.page_size = pageSize;
    }
    return await this.httpClient.get<AppListResponse>(`/categories/${categoryId}/apps`, queryParams);
  }

  // ==================== 搜索相关API ====================

  /**
   * 搜索应用（公开接口）
   */
  async searchApps(keyword: string, page?: number, pageSize?: number): Promise<AppListResponse> {
    const params: Record<string, string | number | boolean> = {};
    params.keyword = keyword;
    if (page !== undefined) {
      params.page = page;
    }
    if (pageSize !== undefined) {
      params.page_size = pageSize;
    }
    return await this.httpClient.get<AppListResponse>('/search/apps', params);
  }

  /**
   * 获取搜索建议（公开接口）
   */
  async getSearchSuggestions(keyword: string): Promise<Record<string, Object>> {
    const params: SearchSuggestionParams = { keyword };
    return await this.httpClient.get('/search/suggestions', params);
  }

  /**
   * 获取热门关键词（公开接口）
   */
  async getHotKeywords(): Promise<Record<string, Object>> {
    return await this.httpClient.get('/search/hot-keywords');
  }

  /**
   * 保存搜索历史
   */
  async saveSearchHistory(keyword: string): Promise<Record<string, Object>> {
    const data: SearchHistoryRequest = { keyword };
    return await this.httpClient.post('/search/history', data);
  }

  /**
   * 获取搜索历史
   */
  async getSearchHistory(): Promise<Record<string, Object>> {
    return await this.httpClient.get('/search/history');
  }

  /**
   * 清空搜索历史
   */
  async clearSearchHistory(): Promise<Record<string, Object>> {
    return await this.httpClient.delete('/search/history');
  }

  // ==================== 地理位置相关API ====================

  /**
   * 获取国家列表
   */
  async getCountries(): Promise<Record<string, Object>> {
    return await this.httpClient.get('/geographic/country');
  }

  /**
   * 获取省份列表
   */
  async getProvinces(countryId: string): Promise<Record<string, Object>> {
    return await this.httpClient.get('/geographic/province');
  }

  /**
   * 获取城市列表
   */
  async getCities(province: string): Promise<Record<string, Object>> {
    return await this.httpClient.get(`/geographic/city/${province}`);
  }

  /**
   * 获取区县列表
   */
  async getDistricts(city: string): Promise<Record<string, Object>> {
    return await this.httpClient.get(`/geographic/district/${city}`);
  }

  /**
   * 获取街道列表
   */
  async getStreets(district: string): Promise<Record<string, Object>> {
    return await this.httpClient.get(`/geographic/street/${district}`);
  }

  // ==================== 通知相关API ====================

  /**
   * 获取通知列表
   */
  async getNotifications(page?: number, pageSize?: number): Promise<Record<string, Object>> {
    const params: Record<string, string | number | boolean> = {};
    if (page !== undefined) {
      params.page = page;
    }
    if (pageSize !== undefined) {
      params.page_size = pageSize;
    }
    return await this.httpClient.get('/notifications', params);
  }

  /**
   * 标记通知为已读
   */
  async markNotificationAsRead(notificationId: number): Promise<Record<string, Object>> {
    return await this.httpClient.put(`/notifications/${notificationId}/read`);
  }

  /**
   * 获取未读通知数量
   */
  async getUnreadNotificationCount(): Promise<Record<string, Object>> {
    return await this.httpClient.get('/notifications/unread/count');
  }

  /**
   * 获取通知设置
   */
  async getNotificationSettings(): Promise<Record<string, Object>> {
    return await this.httpClient.get('/notifications/settings');
  }

  /**
   * 更新通知设置
   */
  async updateNotificationSettings(settings: Record<string, Object>): Promise<Record<string, Object>> {
    return await this.httpClient.put('/notifications/settings', settings);
  }

  /**
   * 删除通知
   */
  async deleteNotification(notificationId: number): Promise<Record<string, Object>> {
    return await this.httpClient.delete(`/notifications/${notificationId}`);
  }

  // ==================== 精选集相关API ====================

  /**
   * 获取精选集列表
   * @param page 页码
   * @param pageSize 每页数量
   * @param status 状态过滤
   * @returns 精选集列表响应
   */
  async getFeaturedCollections(page: number = 1, pageSize: number = 20, status?: string): Promise<FeaturedCollectionListResponse> {
    const params: Record<string, string | number> = {} as Record<string, string | number>;
    params.page = page;
    params.page_size = pageSize;

    if (status) {
      params.status = status;
    }

    // 直接返回后端数据结构，无需转换
    return this.httpClient.get<FeaturedCollectionListResponse>('/public/featured-collections', params);
  }

  /**
   * 获取精选集详情
   * @param id 精选集ID
   * @returns 精选集详情响应
   */
  async getFeaturedCollectionDetail(id: number): Promise<FeaturedCollectionDetailResponse> {
    return this.httpClient.get<FeaturedCollectionDetailResponse>(`/public/featured-collections/${id}`);
  }

  /**
   * 获取精选集中的应用列表
   * @param id 精选集ID
   * @param page 页码
   * @param pageSize 每页数量
   * @returns 精选集应用列表响应
   */
  async getFeaturedCollectionApps(id: number, page: number = 1, pageSize: number = 20): Promise<FeaturedCollectionAppsResponse> {
    const params: Record<string, number> = {} as Record<string, number>;
    params.page = page;
    params.page_size = pageSize;
    
    return this.httpClient.get<FeaturedCollectionAppsResponse>(`/public/featured-collections/${id}/apps`, params);
  }

  // ==================== 系统相关API ====================

  /**
   * 健康检查（公开接口）
   */
  async healthCheck(): Promise<Record<string, Object>> {
    return await this.httpClient.get('/health');
  }

  /**
   * 获取配置信息（公开接口）
   */
  async getConfig(): Promise<Record<string, Object>> {
    return await this.httpClient.get('/config');
  }

}