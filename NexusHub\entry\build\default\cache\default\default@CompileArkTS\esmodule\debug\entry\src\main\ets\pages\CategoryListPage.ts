if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface CategoryListPage_Params {
    categories?: CategoryModel[];
    loadingState?: LoadingState;
    searchKeyword?: string;
    filteredCategories?: CategoryModel[];
    deviceUtils?;
    apiService?;
}
import type { CategoryModel, CategoryListResponse } from '../models/Category';
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { LoadingView, LoadingState } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import preferences from "@ohos:data.preferences";
import hilog from "@ohos:hilog";
export class CategoryListPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__categories = new ObservedPropertyObjectPU([], this, "categories");
        this.__loadingState = new ObservedPropertySimplePU(LoadingState.LOADING, this, "loadingState");
        this.__searchKeyword = new ObservedPropertySimplePU('', this, "searchKeyword");
        this.__filteredCategories = new ObservedPropertyObjectPU([], this, "filteredCategories");
        this.deviceUtils = DeviceUtils.getInstance();
        this.apiService = ApiService.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: CategoryListPage_Params) {
        if (params.categories !== undefined) {
            this.categories = params.categories;
        }
        if (params.loadingState !== undefined) {
            this.loadingState = params.loadingState;
        }
        if (params.searchKeyword !== undefined) {
            this.searchKeyword = params.searchKeyword;
        }
        if (params.filteredCategories !== undefined) {
            this.filteredCategories = params.filteredCategories;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
    }
    updateStateVars(params: CategoryListPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__categories.purgeDependencyOnElmtId(rmElmtId);
        this.__loadingState.purgeDependencyOnElmtId(rmElmtId);
        this.__searchKeyword.purgeDependencyOnElmtId(rmElmtId);
        this.__filteredCategories.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__categories.aboutToBeDeleted();
        this.__loadingState.aboutToBeDeleted();
        this.__searchKeyword.aboutToBeDeleted();
        this.__filteredCategories.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __categories: ObservedPropertyObjectPU<CategoryModel[]>;
    get categories() {
        return this.__categories.get();
    }
    set categories(newValue: CategoryModel[]) {
        this.__categories.set(newValue);
    }
    private __loadingState: ObservedPropertySimplePU<LoadingState>;
    get loadingState() {
        return this.__loadingState.get();
    }
    set loadingState(newValue: LoadingState) {
        this.__loadingState.set(newValue);
    }
    private __searchKeyword: ObservedPropertySimplePU<string>;
    get searchKeyword() {
        return this.__searchKeyword.get();
    }
    set searchKeyword(newValue: string) {
        this.__searchKeyword.set(newValue);
    }
    private __filteredCategories: ObservedPropertyObjectPU<CategoryModel[]>;
    get filteredCategories() {
        return this.__filteredCategories.get();
    }
    set filteredCategories(newValue: CategoryModel[]) {
        this.__filteredCategories.set(newValue);
    }
    private deviceUtils;
    private apiService;
    aboutToAppear() {
        this.loadCategories();
    }
    /**
     * 检查并设置认证token
     */
    private async checkAndSetAuthToken(): Promise<void> {
        try {
            const context = this.getUIContext().getHostContext();
            const options: preferences.Options = { name: 'user_data' };
            const dataPreferences = preferences.getPreferencesSync(context, options);
            const token = dataPreferences.getSync('token', '') as string;
            if (token) {
                this.apiService.setAuthToken(token);
            }
        }
        catch (error) {
            hilog.error(0x0000, 'CategoryListPage', '检查登录状态失败: %{public}s', JSON.stringify(error));
        }
    }
    /**
     * 加载分类列表
     */
    private async loadCategories() {
        try {
            this.loadingState = LoadingState.LOADING;
            // 检查登录状态并设置token（可选）
            await this.checkAndSetAuthToken();
            // 使用公开接口获取分类列表
            const response: CategoryListResponse = await this.apiService.getAppCategories();
            hilog.info(0x0000, 'CategoryListPage', '分类数据响应: code=%{public}d, dataLength=%{public}d', response.code, response.data?.length || 0);
            // 改进的数据验证逻辑
            if (response && response.data && Array.isArray(response.data)) {
                this.categories = response.data;
                this.filteredCategories = this.categories;
                // 根据数据长度设置状态
                if (this.categories.length > 0) {
                    this.loadingState = LoadingState.SUCCESS;
                    hilog.info(0x0000, 'CategoryListPage', '成功加载 %{public}d 个分类', this.categories.length);
                }
                else {
                    this.loadingState = LoadingState.EMPTY;
                    hilog.info(0x0000, 'CategoryListPage', '分类数据为空');
                }
            }
            else {
                // 数据格式不正确
                hilog.error(0x0000, 'CategoryListPage', '分类数据格式错误: %{public}s', JSON.stringify(response));
                this.loadingState = LoadingState.ERROR;
            }
        }
        catch (error) {
            hilog.error(0x0000, 'CategoryListPage', '加载分类列表失败: %{public}s', JSON.stringify(error));
            this.loadingState = LoadingState.ERROR;
        }
    }
    /**
     * 搜索分类
     */
    private searchCategories(keyword: string): void {
        this.searchKeyword = keyword;
        if (keyword.trim() === '') {
            this.filteredCategories = this.categories;
        }
        else {
            this.filteredCategories = this.categories.filter(category => category.name.toLowerCase().includes(keyword.toLowerCase()) ||
                (category.description && category.description.toLowerCase().includes(keyword.toLowerCase())));
        }
    }
    /**
     * 跳转到分类页面
     */
    private navigateToCategory(categoryId: string, categoryName: string) {
        this.getUIContext().getRouter().pushUrl({
            url: 'pages/CategoryPage',
            params: { categoryId, categoryName }
        });
    }
    /**
     * 搜索栏
     */
    private SearchBar(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
            Row.height(40);
            Row.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
            Row.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Row.margin({ left: 16, right: 16, top: 8, bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(20);
            Image.height(20);
            Image.fillColor(Constants.COLORS.TEXT_HINT);
            Image.margin({ left: 12, right: 8 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '搜索分类', text: this.searchKeyword });
            TextInput.layoutWeight(1);
            TextInput.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
            TextInput.fontColor(Constants.COLORS.TEXT_PRIMARY);
            TextInput.backgroundColor(Color.Transparent);
            TextInput.border({ width: 0 });
            TextInput.onChange((value: string) => {
                this.searchCategories(value);
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.searchKeyword.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Image.width(20);
                        Image.height(20);
                        Image.fillColor(Constants.COLORS.TEXT_HINT);
                        Image.margin({ right: 12 });
                        Image.onClick(() => {
                            this.searchKeyword = '';
                            this.searchCategories('');
                        });
                    }, Image);
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Row.pop();
    }
    /**
     * 分类项
     */
    private CategoryItem(category: CategoryModel, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) });
            Row.width('100%');
            Row.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Row.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Row.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Row.margin({ left: 16, right: 16, bottom: 8 });
            Row.onClick(() => this.navigateToCategory(category.id.toString(), category.name));
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 分类图标
            Image.create(category.icon || Constants.PLACEHOLDER_IMAGE);
            // 分类图标
            Image.width(this.deviceUtils.isTablet() ? 56 : 48);
            // 分类图标
            Image.height(this.deviceUtils.isTablet() ? 56 : 48);
            // 分类图标
            Image.objectFit(ImageFit.Cover);
            // 分类图标
            Image.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            // 分类图标
            Image.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 分类信息
            Column.create({ space: 4 });
            // 分类信息
            Column.alignItems(HorizontalAlign.Start);
            // 分类信息
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(category.name);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.layoutWeight(1);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${category.app_count || 0}个应用`);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (category.description) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(category.description);
                        Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                        Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Text.maxLines(2);
                        Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                        Text.lineHeight(20);
                    }, Text);
                    Text.pop();
                });
            }
            // 热门标签已移除
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 分类信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 箭头
            Image.create({ "id": 16777278, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 箭头
            Image.width(16);
            // 箭头
            Image.height(16);
            // 箭头
            Image.fillColor(Constants.COLORS.TEXT_HINT);
        }, Image);
        Row.pop();
    }
    /**
     * 分类网格（平板设备）
     */
    private CategoryGrid(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Grid.create();
            Grid.columnsTemplate('1fr 1fr 1fr 1fr');
            Grid.rowsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Grid.columnsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Grid.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Grid.scrollBar(BarState.Auto);
        }, Grid);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const category = _item;
                {
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        GridItem.create(() => { }, false);
                    };
                    const observedDeepRender = () => {
                        this.observeComponentCreation2(itemCreation2, GridItem);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Column.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) });
                            Column.width('100%');
                            Column.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
                            Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                            Column.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                            Column.justifyContent(FlexAlign.Center);
                            Column.alignItems(HorizontalAlign.Center);
                            Column.onClick(() => this.navigateToCategory(category.id.toString(), category.name));
                        }, Column);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            // 分类图标
                            Stack.create();
                            // 分类图标
                            Stack.alignContent(Alignment.TopEnd);
                        }, Stack);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Image.create(category.icon || Constants.PLACEHOLDER_IMAGE);
                            Image.width(64);
                            Image.height(64);
                            Image.objectFit(ImageFit.Cover);
                            Image.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
                            Image.backgroundColor(Constants.COLORS.BACKGROUND_LIGHT);
                        }, Image);
                        // 分类图标
                        Stack.pop();
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            // 分类名称
                            Text.create(category.name);
                            // 分类名称
                            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL));
                            // 分类名称
                            Text.fontWeight(FontWeight.Medium);
                            // 分类名称
                            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
                            // 分类名称
                            Text.maxLines(1);
                            // 分类名称
                            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                            // 分类名称
                            Text.textAlign(TextAlign.Center);
                        }, Text);
                        // 分类名称
                        Text.pop();
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            // 应用数量
                            Text.create(`${category.app_count || 0}个应用`);
                            // 应用数量
                            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
                            // 应用数量
                            Text.fontColor(Constants.COLORS.TEXT_HINT);
                            // 应用数量
                            Text.textAlign(TextAlign.Center);
                        }, Text);
                        // 应用数量
                        Text.pop();
                        Column.pop();
                        GridItem.pop();
                    };
                    observedDeepRender();
                }
            };
            this.forEachUpdateFunction(elmtId, this.filteredCategories, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        Grid.pop();
    }
    /**
     * 分类列表（手机设备）
     */
    private CategoryList(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            List.create({ space: 0 });
            List.scrollBar(BarState.Auto);
        }, List);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const category = _item;
                {
                    const itemCreation = (elmtId, isInitialRender) => {
                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                        itemCreation2(elmtId, isInitialRender);
                        if (!isInitialRender) {
                            ListItem.pop();
                        }
                        ViewStackProcessor.StopGetAccessRecording();
                    };
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        ListItem.create(deepRenderFunction, true);
                    };
                    const deepRenderFunction = (elmtId, isInitialRender) => {
                        itemCreation(elmtId, isInitialRender);
                        this.CategoryItem.bind(this)(category);
                        ListItem.pop();
                    };
                    this.observeComponentCreation2(itemCreation2, ListItem);
                    ListItem.pop();
                }
            };
            this.forEachUpdateFunction(elmtId, this.filteredCategories, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        List.pop();
    }
    /**
     * 统计信息
     */
    private StatsInfo(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) });
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceAround);
            Row.padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM));
            Row.backgroundColor(Constants.COLORS.WHITE);
            Row.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Row.margin({ left: 16, right: 16, top: 8, bottom: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 4 });
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create((this.categories?.length || 0).toString());
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Constants.COLORS.PRIMARY);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('分类总数');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 4 });
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTotalApps().toString());
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Constants.COLORS.SUCCESS);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('应用总数');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 4 });
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getHotCategories().toString());
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Constants.COLORS.ERROR);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('热门分类');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    /**
     * 获取应用总数
     */
    private getTotalApps(): number {
        return this.categories.reduce((total, category) => total + (category.app_count || 0), 0);
    }
    /**
     * 获取热门分类数量
     */
    private getHotCategories(): number {
        return 0; // 热门分类功能已移除
    }
    /**
     * 空状态视图
     */
    private EmptyView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.searchKeyword.length > 0 && (!this.filteredCategories || this.filteredCategories.length === 0)) {
                this.ifElseBranchUpdateFunction(0, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.EMPTY,
                                    message: `未找到包含"${this.searchKeyword}"的分类`
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/CategoryListPage.ets", line: 349, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.EMPTY,
                                        message: `未找到包含"${this.searchKeyword}"的分类`
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.EMPTY,
                                    message: `未找到包含"${this.searchKeyword}"的分类`
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: '16vp', right: '16vp' });
            // 顶部导航栏
            Row.justifyContent(FlexAlign.Center);
            // 顶部导航栏
            Row.alignItems(VerticalAlign.Center);
            // 顶部导航栏
            Row.backgroundColor(Constants.COLORS.WHITE);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('应用分类');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.width('100%');
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.loadingState === LoadingState.LOADING) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, { state: LoadingState.LOADING }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/CategoryListPage.ets", line: 375, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.LOADING
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.LOADING
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.ERROR) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.ERROR,
                                    onRetry: (): Promise<void> => this.loadCategories()
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/CategoryListPage.ets", line: 378, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.ERROR,
                                        onRetry: (): Promise<void> => this.loadCategories()
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.ERROR
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.loadingState === LoadingState.EMPTY) {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {
                                    state: LoadingState.EMPTY,
                                    message: '暂无分类数据'
                                }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/CategoryListPage.ets", line: 384, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        state: LoadingState.EMPTY,
                                        message: '暂无分类数据'
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    state: LoadingState.EMPTY,
                                    message: '暂无分类数据'
                                });
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(3, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                    }, Column);
                    // 搜索栏
                    this.SearchBar.bind(this)();
                    // 统计信息
                    this.StatsInfo.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 分类列表/网格
                        if (this.filteredCategories && this.filteredCategories.length > 0) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    If.create();
                                    if (this.deviceUtils.isTablet()) {
                                        this.ifElseBranchUpdateFunction(0, () => {
                                            this.CategoryGrid.bind(this)();
                                        });
                                    }
                                    else {
                                        this.ifElseBranchUpdateFunction(1, () => {
                                            this.CategoryList.bind(this)();
                                        });
                                    }
                                }, If);
                                If.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                                this.EmptyView.bind(this)();
                            });
                        }
                    }, If);
                    If.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "CategoryListPage";
    }
}
registerNamedRoute(() => new CategoryListPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/CategoryListPage", pageFullPath: "entry/src/main/ets/pages/CategoryListPage", integratedHsp: "false", moduleType: "followWithHap" });
