"use strict";
import { request } from "@umijs/max";
export async function queryProjectNotice() {
  return request("/dashboard/workbench/summary");
}
export async function queryActivities(limit) {
  return request("/dashboard/workbench/activities", {
    params: { limit: limit || 20 }
  });
}
export async function queryTasks(params) {
  return request("/dashboard/workbench/tasks", {
    params: {
      page: 1,
      page_size: 20,
      ...params
    }
  });
}
export async function createTask(params) {
  return request("/dashboard/workbench/tasks", {
    method: "POST",
    data: params
  });
}
export async function updateTask(id, params) {
  return request(`/dashboard/workbench/tasks/${id}`, {
    method: "PUT",
    data: params
  });
}
export async function deleteTask(id) {
  return request(`/dashboard/workbench/tasks/${id}`, {
    method: "DELETE"
  });
}
