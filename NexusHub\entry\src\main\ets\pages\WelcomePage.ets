import { FirstLaunchService } from '../services/FirstLaunchService';
import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 首次启动欢迎页面
 * 提供沉浸式的应用介绍体验，无底部导航栏
 */
@Entry
@Component
struct WelcomePage {
  @State currentStep: number = 0;
  @State isNavigating: boolean = false;

  private deviceUtils = DeviceUtils.getInstance();
  private firstLaunchService = FirstLaunchService.getInstance();
  private static readonly DOMAIN = 0x0000;
  private static readonly TAG = 'WelcomePage';

  // 欢迎页面内容配置
  private welcomeSteps = [
    {
      title: '欢迎使用 NexusHub',
      subtitle: '您的专属应用商店',
      description: '发现、下载和管理优质应用，享受便捷的应用体验',
      icon: $r('app.media.ic_home'),
      color: Constants.COLORS.PRIMARY
    },
    {
      title: '精选应用推荐',
      subtitle: '个性化推荐系统',
      description: '基于您的喜好和使用习惯，为您推荐最适合的应用',
      icon: $r('app.media.ic_featured'),
      color: '#FF6B35'
    },
    {
      title: '分类浏览',
      subtitle: '快速找到所需',
      description: '按照不同类别整理应用，让您快速找到需要的工具和娱乐应用',
      icon: $r('app.media.ic_category'),
      color: '#4ECDC4'
    },
    {
      title: '安全可靠',
      subtitle: '值得信赖的选择',
      description: '所有应用经过严格审核，确保安全性和质量，让您放心使用',
      icon: $r('app.media.ic_profile'),
      color: '#45B7D1'
    }
  ];

  aboutToAppear() {
    hilog.info(WelcomePage.DOMAIN, WelcomePage.TAG, '欢迎页面初始化');
  }

  /**
   * 进入主应用
   */
  private async enterMainApp(): Promise<void> {
    if (this.isNavigating) {
      return; // 防止重复点击
    }

    try {
      this.isNavigating = true;
      hilog.info(WelcomePage.DOMAIN, WelcomePage.TAG, '开始进入主应用');

      // 标记首次启动已完成
      await this.firstLaunchService.markFirstLaunchCompleted();

      // 导航到主应用界面
      await this.getUIContext().getRouter().replaceUrl({
        url: 'pages/Index'
      });

      hilog.info(WelcomePage.DOMAIN, WelcomePage.TAG, '成功进入主应用');
    } catch (error) {
      this.isNavigating = false;
      hilog.error(WelcomePage.DOMAIN, WelcomePage.TAG, 
        '进入主应用失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 跳过欢迎页
   */
  private async skipWelcome(): Promise<void> {
    await this.enterMainApp();
  }

  /**
   * 下一步
   */
  private nextStep(): void {
    if (this.currentStep < this.welcomeSteps.length - 1) {
      this.currentStep++;
    } else {
      this.enterMainApp();
    }
  }

  /**
   * 上一步
   */
  private previousStep(): void {
    if (this.currentStep > 0) {
      this.currentStep--;
    }
  }

  /**
   * 构建欢迎步骤内容
   */
  @Builder
  private buildWelcomeStep(step: any) {
    Column({ space: 32 }) {
      // 图标
      Image(step.icon)
        .width(120)
        .height(120)
        .fillColor(step.color)
        .objectFit(ImageFit.Contain)

      // 文本内容
      Column({ space: 16 }) {
        Text(step.title)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.EXTRA_LARGE))
          .fontWeight(FontWeight.Bold)
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
          .textAlign(TextAlign.Center)

        Text(step.subtitle)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontColor(step.color)
          .textAlign(TextAlign.Center)

        Text(step.description)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM))
          .fontColor($r('sys.color.ohos_id_color_text_secondary'))
          .textAlign(TextAlign.Center)
          .lineHeight(24)
          .margin({ left: 32, right: 32 })
      }
      .width('100%')
    }
    .width('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  /**
   * 构建步骤指示器
   */
  @Builder
  private buildStepIndicator() {
    Row({ space: 8 }) {
      ForEach(this.welcomeSteps, (step: any, index: number) => {
        Circle({ width: 8, height: 8 })
          .fill(index === this.currentStep ? Constants.COLORS.PRIMARY : $r('sys.color.ohos_id_color_button_normal'))
          .animation({
            duration: 300,
            curve: Curve.EaseInOut
          })
      }, (step: any, index: number) => index.toString())
    }
    .justifyContent(FlexAlign.Center)
  }

  /**
   * 构建底部按钮区域
   */
  @Builder
  private buildBottomButtons() {
    Column({ space: 16 }) {
      // 步骤指示器
      this.buildStepIndicator()

      // 按钮区域
      Row({ space: 16 }) {
        // 跳过按钮
        if (this.currentStep < this.welcomeSteps.length - 1) {
          Button('跳过')
            .type(ButtonType.Normal)
            .borderRadius(24)
            .backgroundColor($r('sys.color.ohos_id_color_button_normal'))
            .fontColor($r('sys.color.ohos_id_color_text_primary'))
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM))
            .width(100)
            .height(48)
            .onClick(() => this.skipWelcome())
        }

        Blank()

        // 主要操作按钮
        Button(this.currentStep === this.welcomeSteps.length - 1 ? '开始使用' : '下一步')
          .type(ButtonType.Normal)
          .borderRadius(24)
          .backgroundColor(Constants.COLORS.PRIMARY)
          .fontColor($r('sys.color.ohos_id_color_foreground_contrary'))
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM))
          .width(this.currentStep === this.welcomeSteps.length - 1 ? 120 : 100)
          .height(48)
          .enabled(!this.isNavigating)
          .onClick(() => this.nextStep())
      }
      .width('100%')
      .padding({ left: 32, right: 32 })
    }
    .width('100%')
  }

  build() {
    Column() {
      // 主要内容区域
      Column() {
        // 当前步骤内容
        this.buildWelcomeStep(this.welcomeSteps[this.currentStep])
      }
      .layoutWeight(1)
      .width('100%')
      .justifyContent(FlexAlign.Center)
      .gesture(
        // 添加左右滑动手势
        PanGesture({ direction: PanDirection.Horizontal, distance: 50 })
          .onActionEnd((event: GestureEvent) => {
            if (event.offsetX > 50) {
              // 向右滑动，上一步
              this.previousStep();
            } else if (event.offsetX < -50) {
              // 向左滑动，下一步
              this.nextStep();
            }
          })
      )

      // 底部按钮区域
      this.buildBottomButtons()
        .margin({ bottom: 48 })
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
    .padding({ top: 48 })
  }
}
