package api

import (
	"strconv"

	"nexushub-oh-back/internal/services"
	"nexushub-oh-back/pkg/logger"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// MessageController 消息队列控制器
type MessageController struct {
	messageService *services.MessageService
}

// NewMessageController 创建新的消息控制器
func NewMessageController(messageService *services.MessageService) *MessageController {
	return &MessageController{
		messageService: messageService,
	}
}

// SendNotificationRequest 发送通知请求
type SendNotificationRequest struct {
	UserID  uint   `json:"user_id" binding:"required"`
	Title   string `json:"title" binding:"required"`
	Content string `json:"content" binding:"required"`
	Type    string `json:"type" binding:"required,oneof=info warning error success"`
}

// SendEmailRequest 发送邮件请求
type SendEmailRequest struct {
	To      string `json:"to" binding:"required,email"`
	Subject string `json:"subject" binding:"required"`
	Body    string `json:"body" binding:"required"`
	IsHTML  bool   `json:"is_html"`
}

// UserActivityRequest 用户活动记录请求
type UserActivityRequest struct {
	UserID     uint   `json:"user_id" binding:"required"`
	Action     string `json:"action" binding:"required"`
	Resource   string `json:"resource" binding:"required"`
	ResourceID uint   `json:"resource_id"`
}

// SendNotification 发送通知消息
//	@Summary		发送通知消息
//	@Description	向指定用户发送通知消息
//	@Tags			消息队列
//	@Accept			json
//	@Produce		json
//	@Param			request	body		SendNotificationRequest	true	"通知请求"
//	@Success		200		{object}	Response
//	@Failure		400		{object}	Response
//	@Failure		500		{object}	Response
//	@Router			/api/v1/messages/notification [post]
func (mc *MessageController) SendNotification(c *gin.Context) {
	if mc.messageService == nil {
		ServerError(c, "消息服务未初始化", nil)
		return
	}

	var req SendNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	err := mc.messageService.PublishNotificationMessage(
		c.Request.Context(),
		req.UserID,
		req.Title,
		req.Content,
		req.Type,
	)

	if err != nil {
		logger.Error("发送通知消息失败", zap.Error(err))
		ServerError(c, "发送通知消息失败", err)
		return
	}

	SuccessWithMessage(c, "通知消息发送成功", nil)
}

// SendEmail 发送邮件消息
//	@Summary		发送邮件消息
//	@Description	发送邮件到消息队列
//	@Tags			消息队列
//	@Accept			json
//	@Produce		json
//	@Param			request	body		SendEmailRequest	true	"邮件请求"
//	@Success		200		{object}	Response
//	@Failure		400		{object}	Response
//	@Failure		500		{object}	Response
//	@Router			/api/v1/messages/email [post]
func (mc *MessageController) SendEmail(c *gin.Context) {
	if mc.messageService == nil {
		ServerError(c, "消息服务未初始化", nil)
		return
	}

	var req SendEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	err := mc.messageService.PublishEmailMessage(
		c.Request.Context(),
		req.To,
		req.Subject,
		req.Body,
		req.IsHTML,
	)

	if err != nil {
		logger.Error("发送邮件消息失败", zap.Error(err))
		ServerError(c, "发送邮件消息失败", err)
		return
	}

	SuccessWithMessage(c, "邮件消息发送成功", nil)
}

// RecordUserActivity 记录用户活动
//	@Summary		记录用户活动
//	@Description	记录用户活动到消息队列
//	@Tags			消息队列
//	@Accept			json
//	@Produce		json
//	@Param			request	body		UserActivityRequest	true	"用户活动请求"
//	@Success		200		{object}	Response
//	@Failure		400		{object}	Response
//	@Failure		500		{object}	Response
//	@Router			/api/v1/messages/activity [post]
func (mc *MessageController) RecordUserActivity(c *gin.Context) {
	if mc.messageService == nil {
		ServerError(c, "消息服务未初始化", nil)
		return
	}

	var req UserActivityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取客户端IP和User-Agent
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	err := mc.messageService.PublishUserActivityMessage(
		c.Request.Context(),
		req.UserID,
		req.Action,
		req.Resource,
		req.ResourceID,
		clientIP,
		userAgent,
	)

	if err != nil {
		logger.Error("记录用户活动失败", zap.Error(err))
		ServerError(c, "记录用户活动失败", err)
		return
	}

	SuccessWithMessage(c, "用户活动记录成功", nil)
}

// TriggerAppReview 触发应用审核
//	@Summary		触发应用审核
//	@Description	触发应用审核流程
//	@Tags			消息队列
//	@Accept			json
//	@Produce		json
//	@Param			app_id	path		int		true	"应用ID"
//	@Param			action	query		string	true	"操作类型"	Enums(submit,approve,reject)
//	@Param			reason	query		string	false	"原因（拒绝时必填）"
//	@Success		200		{object}	Response
//	@Failure		400		{object}	Response
//	@Failure		500		{object}	Response
//	@Router			/api/v1/messages/app-review/{app_id} [post]
func (mc *MessageController) TriggerAppReview(c *gin.Context) {
	if mc.messageService == nil {
		ServerError(c, "消息服务未初始化", nil)
		return
	}

	// 获取应用ID
	appIDStr := c.Param("app_id")
	appID, err := strconv.ParseUint(appIDStr, 10, 32)
	if err != nil {
		BadRequest(c, "无效的应用ID")
		return
	}

	// 获取操作类型
	action := c.Query("action")
	if action == "" {
		BadRequest(c, "操作类型不能为空")
		return
	}

	if action != "submit" && action != "approve" && action != "reject" {
		BadRequest(c, "无效的操作类型")
		return
	}

	// 获取原因（拒绝时必填）
	reason := c.Query("reason")
	if action == "reject" && reason == "" {
		BadRequest(c, "拒绝时必须提供原因")
		return
	}

	// 这里应该从数据库获取应用信息，为了简化直接使用参数
	appName := c.Query("app_name")
	if appName == "" {
		appName = "未知应用"
	}

	developerIDStr := c.Query("developer_id")
	developerID, _ := strconv.ParseUint(developerIDStr, 10, 32)

	err = mc.messageService.PublishAppReviewMessage(
		c.Request.Context(),
		uint(appID),
		appName,
		uint(developerID),
		action,
		reason,
	)

	if err != nil {
		logger.Error("触发应用审核失败", zap.Error(err))
		ServerError(c, "触发应用审核失败", err)
		return
	}

	SuccessWithMessage(c, "应用审核流程已触发", nil)
}

// GetQueueStatus 获取队列状态
//	@Summary		获取队列状态
//	@Description	获取消息队列状态信息
//	@Tags			消息队列
//	@Produce		json
//	@Success		200	{object}	Response
//	@Failure		500	{object}	Response
//	@Router			/api/v1/messages/status [get]
func (mc *MessageController) GetQueueStatus(c *gin.Context) {
	if mc.messageService == nil {
		ServerError(c, "消息服务未初始化", nil)
		return
	}

	// 这里可以添加获取队列状态的逻辑
	// 由于RabbitMQ管理API比较复杂，这里返回基本状态
	status := gin.H{
		"status": "connected",
		"queues": []string{
			services.QueueAppReview,
			services.QueueNotification,
			services.QueueEmailSend,
			services.QueueAppAnalytics,
			services.QueueUserActivity,
		},
		"message": "消息队列服务正常运行",
	}

	Success(c, status)
}