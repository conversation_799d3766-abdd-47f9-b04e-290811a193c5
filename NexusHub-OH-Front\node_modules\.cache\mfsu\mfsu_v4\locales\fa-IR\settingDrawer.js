"use strict";
export default {
  "app.setting.pagestyle": "\u062A\u0646\u0638\u06CC\u0645 \u0646\u0648\u0639 \u0635\u0641\u062D\u0647",
  "app.setting.pagestyle.dark": "\u0633\u0628\u06A9 \u062A\u06CC\u0631\u0647",
  "app.setting.pagestyle.light": "\u0633\u0628\u06A9 \u0633\u0628\u06A9",
  "app.setting.content-width": "\u0639\u0631\u0636 \u0645\u062D\u062A\u0648\u0627",
  "app.setting.content-width.fixed": "\u062B\u0627\u0628\u062A",
  "app.setting.content-width.fluid": "\u0634\u0646\u0627\u0648\u0631",
  "app.setting.themecolor": "\u0631\u0646\u06AF \u062A\u0645",
  "app.setting.themecolor.dust": "\u06AF\u0631\u062F \u0648 \u063A\u0628\u0627\u0631 \u0642\u0631\u0645\u0632",
  "app.setting.themecolor.volcano": "\u0622\u062A\u0634\u0641\u0634\u0627\u0646",
  "app.setting.themecolor.sunset": "\u063A\u0631\u0648\u0628 \u0646\u0627\u0631\u0646\u062C\u06CC",
  "app.setting.themecolor.cyan": "\u0641\u06CC\u0631\u0648\u0632\u0647 \u0627\u06CC",
  "app.setting.themecolor.green": "\u0633\u0628\u0632 \u0642\u0637\u0628\u06CC",
  "app.setting.themecolor.daybreak": "\u0622\u0628\u06CC \u0631\u0648\u0634\u0646(\u067E\u06CC\u0634\u0641\u0631\u0636)",
  "app.setting.themecolor.geekblue": "\u0686\u0633\u0628 \u06AF\u06CC\u06A9",
  "app.setting.themecolor.purple": "\u0628\u0646\u0641\u0634 \u0637\u0644\u0627\u06CC\u06CC",
  "app.setting.navigationmode": "\u062D\u0627\u0644\u062A \u067E\u06CC\u0645\u0627\u06CC\u0634",
  "app.setting.sidemenu": "\u0637\u0631\u062D \u0645\u0646\u0648\u06CC \u06A9\u0646\u0627\u0631\u06CC",
  "app.setting.topmenu": "\u0637\u0631\u062D \u0645\u0646\u0648\u06CC \u0628\u0627\u0644\u0627\u06CC\u06CC",
  "app.setting.fixedheader": "\u0633\u0631\u0635\u0641\u062D\u0647 \u062B\u0627\u0628\u062A",
  "app.setting.fixedsidebar": "\u0646\u0648\u0627\u0631 \u06A9\u0646\u0627\u0631\u06CC \u062B\u0627\u0628\u062A",
  "app.setting.fixedsidebar.hint": "\u06A9\u0627\u0631 \u0628\u0631 \u0631\u0648\u06CC \u0645\u0646\u0648\u06CC \u06A9\u0646\u0627\u0631\u06CC",
  "app.setting.hideheader": "\u0647\u062F\u0631 \u067E\u0646\u0647\u0627\u0646 \u0647\u0646\u06AF\u0627\u0645 \u067E\u06CC\u0645\u0627\u06CC\u0634",
  "app.setting.hideheader.hint": "\u0648\u0642\u062A\u06CC Hidden Header \u0641\u0639\u0627\u0644 \u0628\u0627\u0634\u062F \u06A9\u0627\u0631 \u0645\u06CC \u06A9\u0646\u062F",
  "app.setting.othersettings": "\u062A\u0646\u0638\u06CC\u0645\u0627\u062A \u062F\u06CC\u06AF\u0631",
  "app.setting.weakmode": "\u062D\u0627\u0644\u062A \u0636\u0639\u06CC\u0641",
  "app.setting.copy": "\u062A\u0646\u0638\u06CC\u0645\u0627\u062A \u06A9\u067E\u06CC",
  "app.setting.copyinfo": "\u0645\u0648\u0641\u0642\u06CC\u062A \u062F\u0631 \u06A9\u067E\u06CC \u06A9\u0631\u062F\u0646 \uFF0C \u0644\u0637\u0641\u0627 defaultSettings \u0631\u0627 \u062F\u0631 src / models / setting.js \u062C\u0627\u06CC\u06AF\u0632\u06CC\u0646 \u06A9\u0646\u06CC\u062F",
  "app.setting.production.hint": "\u0635\u0641\u062D\u0647 \u062A\u0646\u0638\u06CC\u0645 \u0641\u0642\u0637 \u062F\u0631 \u0645\u062D\u06CC\u0637 \u062A\u0648\u0633\u0639\u0647 \u0646\u0645\u0627\u06CC\u0634 \u062F\u0627\u062F\u0647 \u0645\u06CC \u0634\u0648\u062F \u060C \u0644\u0637\u0641\u0627\u064B \u062F\u0633\u062A\u06CC \u062A\u063A\u06CC\u06CC\u0631 \u062F\u0647\u06CC\u062F"
};
