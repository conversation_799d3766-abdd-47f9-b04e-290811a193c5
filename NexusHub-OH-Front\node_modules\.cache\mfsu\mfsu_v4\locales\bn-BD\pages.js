"use strict";
export default {
  "pages.layouts.userLayout.title": "\u09AA\u09BF\u0981\u09AA\u09A1\u09BC\u09BE \u09A1\u09BF\u099C\u09BE\u0987\u09A8 \u09B9\u099A\u09CD\u099B\u09C7 \u09B8\u09BF\u09B9\u09C1 \u099C\u09C7\u09B2\u09BE\u09B0 \u09B8\u09AC\u099A\u09C7\u09AF\u09BC\u09C7 \u09AA\u09CD\u09B0\u09AD\u09BE\u09AC\u09B6\u09BE\u09B2\u09C0 \u0993\u09AF\u09BC\u09C7\u09AC \u09A1\u09BF\u099C\u09BE\u0987\u09A8\u09C7\u09B0 \u09B8\u09CD\u09AA\u09C7\u09B8\u09BF\u09AB\u09BF\u0995\u09C7\u09B6\u09A8",
  "pages.login.accountLogin.tab": "\u0985\u09CD\u09AF\u09BE\u0995\u09BE\u0989\u09A8\u09CD\u099F\u09C7 \u09B2\u0997\u0987\u09A8",
  "pages.login.accountLogin.errorMessage": "\u09AD\u09C1\u09B2 \u09AC\u09CD\u09AF\u09AC\u09B9\u09BE\u09B0\u0995\u09BE\u09B0\u09C0\u09B0 \u09A8\u09BE\u09AE/\u09AA\u09BE\u09B8\u0993\u09AF\u09BC\u09BE\u09B0\u09CD\u09A1(admin/ant.design)",
  "pages.login.failure": "\u09B2\u0997\u0987\u09A8 \u09AC\u09CD\u09AF\u09B0\u09CD\u09A5 \u09B9\u09AF\u09BC\u09C7\u099B\u09C7\u0964 \u0986\u09AC\u09BE\u09B0 \u099A\u09C7\u09B7\u09CD\u099F\u09BE \u0995\u09B0\u09C1\u09A8!",
  "pages.login.success": "\u09B8\u09AB\u09B2 \u09B2\u0997\u0987\u09A8!",
  "pages.login.username.placeholder": "\u09AC\u09CD\u09AF\u09AC\u09B9\u09BE\u09B0\u0995\u09BE\u09B0\u09C0\u09B0 \u09A8\u09BE\u09AE: admin or user",
  "pages.login.username.required": "\u0986\u09AA\u09A8\u09BE\u09B0 \u09AC\u09CD\u09AF\u09AC\u09B9\u09BE\u09B0\u0995\u09BE\u09B0\u09C0\u09B0 \u09A8\u09BE\u09AE \u0987\u09A8\u09AA\u09C1\u099F \u0995\u09B0\u09C1\u09A8!",
  "pages.login.password.placeholder": "\u09AA\u09BE\u09B8\u0993\u09AF\u09BC\u09BE\u09B0\u09CD\u09A1: ant.design",
  "pages.login.password.required": "\u0986\u09AA\u09A8\u09BE\u09B0 \u09AA\u09BE\u09B8\u0993\u09AF\u09BC\u09BE\u09B0\u09CD\u09A1 \u0987\u09A8\u09AA\u09C1\u099F \u0995\u09B0\u09C1\u09A8!",
  "pages.login.phoneLogin.tab": "\u09AB\u09CB\u09A8 \u09B2\u0997\u0987\u09A8",
  "pages.login.phoneLogin.errorMessage": "\u09AF\u09BE\u099A\u09BE\u0987\u0995\u09B0\u09A3 \u0995\u09CB\u09A1 \u09A4\u09CD\u09B0\u09C1\u099F\u09BF",
  "pages.login.phoneNumber.placeholder": "\u09AB\u09CB\u09A8 \u09A8\u09AE\u09CD\u09AC\u09B0",
  "pages.login.phoneNumber.required": "\u0986\u09AA\u09A8\u09BE\u09B0 \u09AB\u09CB\u09A8 \u09A8\u09AE\u09CD\u09AC\u09B0 \u0987\u09A8\u09AA\u09C1\u099F \u0995\u09B0\u09C1\u09A8!",
  "pages.login.phoneNumber.invalid": "\u09AB\u09CB\u09A8 \u09A8\u09AE\u09CD\u09AC\u09B0\u099F\u09BF \u09B8\u09A0\u09BF\u0995 \u09A8\u09DF!",
  "pages.login.captcha.placeholder": "\u09AF\u09BE\u099A\u09BE\u0987\u0995\u09B0\u09A3\u09C7\u09B0 \u0995\u09CB\u09A1",
  "pages.login.captcha.required": "\u09A6\u09AF\u09BC\u09BE \u0995\u09B0\u09C7 \u09AD\u09C7\u09B0\u09BF\u09AB\u09BF\u0995\u09C7\u09B6\u09A8 \u0995\u09CB\u09A1\u099F\u09BF \u0987\u09A8\u09AA\u09C1\u099F \u0995\u09B0\u09C1\u09A8!",
  "pages.login.phoneLogin.getVerificationCode": "\u0995\u09CB\u09A1 \u09AA\u09BE\u09A8",
  "pages.getCaptchaSecondText": "\u09B8\u09C7\u0995\u09C7\u09A8\u09CD\u09A1",
  "pages.login.rememberMe": "\u0986\u09AE\u09BE\u0995\u09C7 \u09AE\u09A8\u09C7 \u09B0\u09BE\u0996\u09C1\u09A8",
  "pages.login.forgotPassword": "\u09AA\u09BE\u09B8\u0993\u09AF\u09BC\u09BE\u09B0\u09CD\u09A1 \u09AD\u09C1\u09B2\u09C7 \u0997\u09C7\u099B\u09C7\u09A8?",
  "pages.login.submit": "\u09AA\u09CD\u09B0\u09AC\u09C7\u09B6 \u0995\u09B0\u09C1\u09A8",
  "pages.login.loginWith": "\u09B2\u0997\u0987\u09A8 \u0995\u09B0\u09A4\u09C7 \u09AA\u09BE\u09B0\u09C7\u09A8:",
  "pages.login.registerAccount": "\u0985\u09CD\u09AF\u09BE\u0995\u09BE\u0989\u09A8\u09CD\u099F \u09A8\u09BF\u09AC\u09A8\u09CD\u09A7\u09A8 \u0995\u09B0\u09C1\u09A8",
  "pages.welcome.link": "\u09B8\u09CD\u09AC\u09BE\u0997\u09A4\u09AE",
  "pages.welcome.alertMessage": "\u09A6\u09CD\u09B0\u09C1\u09A4 \u098F\u09AC\u0982 \u09B6\u0995\u09CD\u09A4\u09BF\u09B6\u09BE\u09B2\u09C0 \u09AD\u09BE\u09B0\u09C0 \u09B6\u09C1\u09B2\u09CD\u0995 \u0989\u09AA\u09BE\u09A6\u09BE\u09A8 \u09AA\u09CD\u09B0\u0995\u09BE\u09B6 \u0995\u09B0\u09BE \u09B9\u09AF\u09BC\u09C7\u099B\u09C7\u0964",
  "pages.404.subTitle": "\u09A6\u09C1\u0983\u0996\u09BF\u09A4, \u0986\u09AA\u09A8\u09BF \u09AF\u09C7 \u09AA\u09C3\u09B7\u09CD\u09A0\u09BE\u099F\u09BF \u09A6\u09C7\u0996\u09A4\u09C7 \u099A\u09BE\u09A8 \u09A4\u09BE \u09AC\u09BF\u09A6\u09CD\u09AF\u09AE\u09BE\u09A8 \u09A8\u09C7\u0987\u0964",
  "pages.404.buttonText": "\u09AA\u09CD\u09B0\u09A7\u09BE\u09A8 \u09AA\u09BE\u09A4\u09BE\u09DF \u09AB\u09BF\u09B0\u09C7 \u09AF\u09BE\u09A8",
  "pages.admin.subPage.title": "\u098F\u0987 \u09AA\u09C3\u09B7\u09CD\u09A0\u09BE\u099F\u09BF \u0995\u09C7\u09AC\u09B2 \u0985\u09CD\u09AF\u09BE\u09A1\u09AE\u09BF\u09A8 \u09A6\u09CD\u09AC\u09BE\u09B0\u09BE \u09A6\u09C7\u0996\u09BE \u09AF\u09BE\u09AC\u09C7",
  "pages.admin.subPage.alertMessage": "UMI UI \u098F\u0996\u09A8 \u09AA\u09CD\u09B0\u0995\u09BE\u09B6\u09BF\u09A4 \u09B9\u09AF\u09BC\u09C7\u099B\u09C7, \u0985\u09AD\u09BF\u099C\u09CD\u099E\u09A4\u09BE \u09B6\u09C1\u09B0\u09C1 \u0995\u09B0\u09A4\u09C7 npm run ui \u09AC\u09CD\u09AF\u09AC\u09B9\u09BE\u09B0 \u0995\u09B0\u09A4\u09C7 \u09B8\u09CD\u09AC\u09BE\u0997\u09A4\u09AE\u0964",
  "pages.searchTable.createForm.newRule": "\u09A8\u09A4\u09C1\u09A8 \u09AC\u09BF\u09A7\u09BF",
  "pages.searchTable.updateForm.ruleConfig": "\u09AC\u09BF\u09A7\u09BF \u0995\u09A8\u09AB\u09BF\u0997\u09BE\u09B0\u09C7\u09B6\u09A8",
  "pages.searchTable.updateForm.basicConfig": "\u09AE\u09CC\u09B2\u09BF\u0995 \u09A4\u09A5\u09CD\u09AF",
  "pages.searchTable.updateForm.ruleName.nameLabel": "\u09AC\u09BF\u09A7\u09BF \u09A8\u09BE\u09AE",
  "pages.searchTable.updateForm.ruleName.nameRules": "\u09AC\u09BF\u09A7\u09BF\u09B0 \u09A8\u09BE\u09AE \u09B2\u09BF\u0996\u09C1\u09A8!",
  "pages.searchTable.updateForm.ruleDesc.descLabel": "\u09AC\u09BF\u09A7\u09BF\u09B0 \u09AC\u09BF\u09AC\u09B0\u09A3",
  "pages.searchTable.updateForm.ruleDesc.descPlaceholder": "\u0995\u09AE\u09AA\u0995\u09CD\u09B7\u09C7 \u09AA\u09BE\u0981\u099A\u099F\u09BF \u0985\u0995\u09CD\u09B7\u09B0 \u09B2\u09BF\u0996\u09C1\u09A8",
  "pages.searchTable.updateForm.ruleDesc.descRules": "\u0995\u09AE\u09AA\u0995\u09CD\u09B7\u09C7 \u09AA\u09BE\u0981\u099A\u099F\u09BF \u0985\u0995\u09CD\u09B7\u09B0\u09C7\u09B0 \u098F\u0995\u099F\u09BF \u09AC\u09BF\u09A7\u09BE\u09A8 \u09AC\u09BF\u09AC\u09B0\u09A3 \u09B2\u09BF\u0996\u09C1\u09A8!",
  "pages.searchTable.updateForm.ruleProps.title": "\u09AC\u09C8\u09B6\u09BF\u09B7\u09CD\u099F\u09CD\u09AF \u0995\u09A8\u09AB\u09BF\u0997\u09BE\u09B0 \u0995\u09B0\u09C1\u09A8",
  "pages.searchTable.updateForm.object": "\u09A8\u09BF\u09B0\u09C0\u0995\u09CD\u09B7\u09A3 \u0985\u09AC\u099C\u09C7\u0995\u09CD\u099F",
  "pages.searchTable.updateForm.ruleProps.templateLabel": "\u09AC\u09BF\u09A7\u09BF \u099F\u09C7\u09AE\u09CD\u09AA\u09B2\u09C7\u099F",
  "pages.searchTable.updateForm.ruleProps.typeLabel": "\u09AC\u09BF\u09A7\u09BF \u09AA\u09CD\u09B0\u0995\u09BE\u09B0",
  "pages.searchTable.updateForm.schedulingPeriod.title": "\u09B8\u09AE\u09AF\u09BC\u09B8\u09C2\u099A\u09C0 \u09A8\u09BF\u09B0\u09CD\u09A7\u09BE\u09B0\u09A3 \u0995\u09B0\u09C1\u09A8",
  "pages.searchTable.updateForm.schedulingPeriod.timeLabel": "\u09B6\u09C1\u09B0\u09C1\u09B0 \u09B8\u09AE\u09AF\u09BC",
  "pages.searchTable.updateForm.schedulingPeriod.timeRules": "\u098F\u0995\u099F\u09BF \u09B6\u09C1\u09B0\u09C1\u09B0 \u09B8\u09AE\u09AF\u09BC \u099A\u09AF\u09BC\u09A8 \u0995\u09B0\u09C1\u09A8!",
  "pages.searchTable.titleDesc": "\u09AC\u09B0\u09CD\u09A3\u09A8\u09BE",
  "pages.searchTable.ruleName": "\u09AC\u09BF\u09A7\u09BF \u09A8\u09BE\u09AE \u09AA\u09CD\u09B0\u09AF\u09BC\u09CB\u099C\u09A8",
  "pages.searchTable.titleCallNo": "\u09AA\u09B0\u09BF\u09B7\u09C7\u09AC\u09BE \u0995\u09B2 \u09B8\u0982\u0996\u09CD\u09AF\u09BE",
  "pages.searchTable.titleStatus": "\u0985\u09AC\u09B8\u09CD\u09A5\u09BE",
  "pages.searchTable.nameStatus.default": "\u09A1\u09BF\u09AB\u09B2\u09CD\u099F",
  "pages.searchTable.nameStatus.running": "\u099A\u09B2\u09AE\u09BE\u09A8",
  "pages.searchTable.nameStatus.online": "\u0985\u09A8\u09B2\u09BE\u0987\u09A8",
  "pages.searchTable.nameStatus.abnormal": "\u0985\u09B8\u09CD\u09AC\u09BE\u09AD\u09BE\u09AC\u09BF\u0995",
  "pages.searchTable.titleUpdatedAt": "\u09B8\u09B0\u09CD\u09AC\u09B6\u09C7\u09B7 \u09A8\u09BF\u09B0\u09CD\u09A7\u09BE\u09B0\u09BF\u09A4",
  "pages.searchTable.exception": "\u09AC\u09CD\u09AF\u09A4\u09BF\u0995\u09CD\u09B0\u09AE \u099C\u09A8\u09CD\u09AF \u0995\u09BE\u09B0\u09A3 \u09B2\u09BF\u0996\u09C1\u09A8!",
  "pages.searchTable.titleOption": "\u0985\u09AA\u09B6\u09A8",
  "pages.searchTable.config": "\u0995\u09A8\u09AB\u09BF\u0997\u09BE\u09B0\u09C7\u09B6\u09A8",
  "pages.searchTable.subscribeAlert": "\u09B8\u09A4\u09B0\u09CD\u0995\u09A4\u09BE \u09B8\u09BE\u09AC\u09B8\u09CD\u0995\u09CD\u09B0\u09BE\u0987\u09AC \u0995\u09B0\u09C1\u09A8",
  "pages.searchTable.title": "\u0987\u09A8\u0995\u09AF\u09BC\u09C7\u09B0\u09BF \u09AB\u09B0\u09AE",
  "pages.searchTable.new": "\u09A8\u09A4\u09C1\u09A8",
  "pages.searchTable.chosen": "\u09A8\u09BF\u09B0\u09CD\u09AC\u09BE\u099A\u09BF\u09A4",
  "pages.searchTable.item": "\u0986\u0987\u099F\u09C7\u09AE",
  "pages.searchTable.totalServiceCalls": "\u09AA\u09B0\u09BF\u09B7\u09C7\u09AC\u09BE \u0995\u09B2\u0997\u09C1\u09B2\u09BF\u09B0 \u09AE\u09CB\u099F \u09B8\u0982\u0996\u09CD\u09AF\u09BE",
  "pages.searchTable.tenThousand": "000",
  "pages.searchTable.batchDeletion": "\u098F\u0995\u09B8\u09BE\u0996\u09C7 \u09A1\u09BF\u09B2\u09BF\u099F",
  "pages.searchTable.batchApproval": "\u098F\u0995\u09B8\u09BE\u0996\u09C7 \u0985\u09A8\u09C1\u09AE\u09CB\u09A6\u09A8"
};
