"use strict";
import { request } from "@umijs/max";
export async function postReviewerAppsIdReview(params, body, options) {
  const { id: param0, ...queryParams } = params;
  return request(`/reviewer/apps/${param0}/review`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    params: { ...queryParams },
    data: body,
    ...options || {}
  });
}
export async function getReviewerAppsPending(params, options) {
  return request("/reviewer/apps/pending", {
    method: "GET",
    params: {
      ...params
    },
    ...options || {}
  });
}
