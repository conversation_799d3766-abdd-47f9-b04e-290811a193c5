import { PageContainer } from '@ant-design/pro-components';
import { Card, Form, Input, Button, Select, Upload, InputNumber, Space, message, Alert } from 'antd';
import { UploadOutlined, InboxOutlined, PlusOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import React, { useState, useEffect } from 'react';
import { uploadFile } from '@/services/upload'; // 导入新的上传服务
import { createApp, submitAppForReview } from '@/services/app'; // 导入应用创建和提交审核服务
import { getTags } from '@/services/ant-design-pro/biaoqianguanli'; // 导入标签管理服务
import { getCategories } from '@/services/ant-design-pro/fenleiguanli'; // 导入分类管理服务

const { Option } = Select;
const { TextArea } = Input;

const AppCreate: React.FC = () => {
  const [form] = Form.useForm();
  const [uploadLoading, setUploadLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [iconUrl, setIconUrl] = useState<string>(''); // 存储上传后的图标URL
  const [packageUrl, setPackageUrl] = useState<string>(''); // 存储上传后的安装包URL
  const [screenshotUrls, setScreenshotUrls] = useState<string[]>([]); // 存储上传后的截图URL列表
  const [tagsList, setTagsList] = useState<API.TagResponse[]>([]); // 存储标签列表
  const [tagsLoading, setTagsLoading] = useState<boolean>(false); // 标签加载状态
  const [categoriesList, setCategoriesList] = useState<API.CategoryResponse[]>([]); // 存储分类列表
  const [categoriesLoading, setCategoriesLoading] = useState<boolean>(false); // 分类加载状态
  
  // 获取用户信息
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;
  const isAdmin = currentUser?.role === 'admin';

  // 获取标签列表
  const fetchTags = async () => {
    try {
      setTagsLoading(true);
      const response = await getTags({ include_inactive: false });
      if (response && Array.isArray(response)) {
        setTagsList(response);
      }
    } catch (error) {
      console.error('获取标签列表失败:', error);
      message.error('获取标签列表失败');
    } finally {
      setTagsLoading(false);
    }
  };

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      setCategoriesLoading(true);
      const response = await getCategories({ include_inactive: false });
      if (response && Array.isArray(response)) {
        setCategoriesList(response);
      }
    } catch (error) {
      console.error('获取分类列表失败:', error);
      message.error('获取分类列表失败');
    } finally {
      setCategoriesLoading(false);
    }
  };

  // 组件加载时获取标签和分类列表
  useEffect(() => {
    fetchTags();
    fetchCategories();
  }, []);

  // 处理图标上传
  const handleIconChange = async (info: any) => {
    if (info.file.status === 'uploading') {
      setUploadLoading(true);
      return;
    }
    
    if (info.file.status === 'done') {
      // 如果是自定义上传，文件状态可能不准确，这里只使用预览功能
      getBase64(info.file.originFileObj, (url: string) => {
        setPreviewUrl(url);
      });
    } else if (info.file.status === 'error') {
      setUploadLoading(false);
      message.error(`${info.file.name} 上传失败`);
    }
  };

  // 将文件转换为Base64以供预览
  const getBase64 = (file: File, callback: (url: string) => void) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result as string));
    reader.readAsDataURL(file);
  };

  // 自定义上传图标方法
  const customUploadIcon = async (options: any) => {
    const { file, onSuccess, onError } = options;
    try {
      setUploadLoading(true);
      // 使用新的上传服务
      const url = await uploadFile('avatar', file);
      setIconUrl(url);
      // 设置表单字段值
      form.setFieldsValue({ icon: url });
      setUploadLoading(false);
      onSuccess(null, new Response(new Blob(), { status: 200 }));
      message.success(`${file.name} 上传成功`);
    } catch (error) {
      setUploadLoading(false);
      onError(error);
      message.error(`${file.name} 上传失败`);
    }
  };

  // 自定义上传安装包方法
  const customUploadPackage = async (options: any) => {
    const { file, onSuccess, onError } = options;
    try {
      // 使用新的上传服务
      const url = await uploadFile('package', file);
      setPackageUrl(url);
      // 设置表单字段值
      form.setFieldsValue({ appPackage: url });
      onSuccess(null, new Response(new Blob(), { status: 200 }));
      message.success(`${file.name} 上传成功`);
    } catch (error) {
      onError(error);
      message.error(`${file.name} 上传失败`);
    }
  };

  // 自定义上传截图方法
  const customUploadScreenshot = async (options: any) => {
    const { file, onSuccess, onError } = options;
    try {
      // 使用新的上传服务
      const url = await uploadFile('screenshot', file);
      // 更新截图URL列表
      const newScreenshotUrls = [...screenshotUrls, url];
      setScreenshotUrls(newScreenshotUrls);
      // 设置表单字段值
      form.setFieldsValue({ screenshots: newScreenshotUrls });
      onSuccess(null, new Response(new Blob(), { status: 200 }));
      message.success(`${file.name} 上传成功`);
    } catch (error) {
      onError(error);
      message.error(`${file.name} 上传失败`);
    }
  };

  // 保存至草稿
  const handleSaveDraft = async (values: any) => {
    // 添加图标和安装包的URL到表单数据
    const formData = {
      name: values.name,
      package: values.package,
      description: values.description,
      short_desc: values.short_desc,
      icon: iconUrl,
      category: String(values.category), // 确保category为字符串
      current_version: values.version,
      size: values.size,
      min_open_harmony_os_ver: values.min_open_harmony_os_ver || '1.0.0', // 提供默认值
      tags: Array.isArray(values.tags) ? values.tags.join(',') : values.tags, // 处理tags格式
      website_url: values.website_url,
      privacy_url: values.privacy_url,
    };
    
    console.log('保存草稿的表单数据:', formData);
    try {
      // 创建应用（后端会自动设置为草稿状态）
      await createApp(formData);
      message.success('应用已保存至草稿');
      history.push('/app/list');
    } catch (error) {
      message.error('保存草稿失败，请重试');
      console.error('保存草稿失败:', error);
    }
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    // 表单验证会自动检查必填字段，这里不需要额外验证
    
    // 添加图标和安装包的URL到表单数据
    const formData = {
      name: values.name,
      package: values.package,
      description: values.description,
      short_desc: values.short_desc,
      icon: iconUrl,
      category: String(values.category), // 确保category为字符串
      current_version: values.version,
      size: values.size,
      min_open_harmony_os_ver: values.min_open_harmony_os_ver || '1.0.0', // 提供默认值
      tags: Array.isArray(values.tags) ? values.tags.join(',') : values.tags, // 处理tags格式
      website_url: values.website_url,
      privacy_url: values.privacy_url,
    };
    
    console.log('提交的表单数据:', formData);
    try {
      // 创建应用
      const response = await createApp(formData);
      const appId = response?.data?.id || response?.id;
      
      if (isAdmin) {
        // 管理员创建的应用直接上架
        message.success('应用创建成功，已直接上架');
      } else {
        // 开发者创建的应用需要提交审核
        if (appId) {
          try {
            await submitAppForReview(appId.toString());
            message.success('应用创建成功，已提交审核，请等待管理员审核');
          } catch (submitError) {
            console.error('提交审核失败:', submitError);
            message.warning('应用创建成功，但提交审核失败，请在应用列表中手动提交审核');
          }
        } else {
          message.success('应用创建成功，请在应用列表中提交审核');
        }
      }
      
      history.push('/app/list');
    } catch (error) {
      message.error('创建失败，请重试');
      console.error('创建应用失败:', error);
    }
  };

  return (
    <PageContainer
      header={{
        title: '创建应用',
        subTitle: '发布新应用到应用商店',
      }}
    >
      {/* 角色提示信息 */}
      {isAdmin ? (
        <Alert
          message="管理员权限"
          description="您创建的应用将直接上架，无需审核流程。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <Alert
          message="开发者权限"
          description="您创建的应用需要提交给管理员审核，审核通过后才能上架。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}
      
      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            // 移除price默认值，让占位符正常显示
          }}
        >
          <Form.Item
            name="name"
            label="应用名称"
            rules={[
              { required: true, message: '请输入应用名称' },
              { min: 2, max: 100, message: '应用名称长度必须在2-100个字符之间' }
            ]}
          >
            <Input placeholder="请输入应用名称" />
          </Form.Item>

          <Form.Item
            name="package"
            label="包名"
            rules={[
              { required: true, message: '请输入包名' },
              { pattern: /^[a-z][a-z0-9_]*(\.[a-z0-9_]+)+[0-9a-z_]$/i, message: '包名格式不正确' }
            ]}
          >
            <Input placeholder="请输入包名，例如：com.example.app" />
          </Form.Item>

          <Form.Item
            name="icon"
            label="应用图标"
            rules={[{ required: true, message: '请上传应用图标' }]}
          >
            <Upload
              name="icon"
              listType="picture-card"
              showUploadList={false}
              customRequest={customUploadIcon} // 使用自定义上传方法
              onChange={handleIconChange}
              beforeUpload={(file) => {
                const isImage = file.type.startsWith('image/');
                if (!isImage) {
                  message.error('只能上传图片文件!');
                }
                const isLt2M = file.size / 1024 / 1024 < 2;
                if (!isLt2M) {
                  message.error('图片大小不能超过2MB!');
                }
                return isImage && isLt2M;
              }}
            >
              {previewUrl ? (
                <img src={previewUrl} alt="应用图标" style={{ width: '100%' }} />
              ) : (
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>上传</div>
                </div>
              )}
            </Upload>
            <div style={{ marginTop: 8 }}>建议尺寸: 512x512px, PNG格式</div>
          </Form.Item>

          <Form.Item
            name="developer"
            label="开发者"
            rules={[{ required: true, message: '请输入开发者名称' }]}
          >
            <Input placeholder="请输入开发者名称" />
          </Form.Item>

          <Form.Item
            name="category"
            label="应用分类"
            rules={[{ required: true, message: '请选择应用分类' }]}
          >
            <Select 
              placeholder="请选择应用分类"
              loading={categoriesLoading}
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {categoriesList.map((category) => (
                <Option key={category.id} value={category.name}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="tags"
            label="应用标签"
            rules={[{ required: true, message: '请选择至少一个标签' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择应用标签"
              maxTagCount={5}
              loading={tagsLoading}
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {tagsList.map((tag) => (
                <Option key={tag.id} value={tag.name}>
                  {tag.name}
                </Option>
              ))}
            </Select>
          </Form.Item>



          <Form.Item
            name="version"
            label="版本号"
            rules={[
              { required: true, message: '请输入版本号' },
              { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式应为 X.Y.Z' }
            ]}
          >
            <Input placeholder="请输入版本号，例如：1.0.0" />
          </Form.Item>

          <Form.Item
            name="size"
            label="应用大小"
            rules={[{ required: true, message: '请输入应用大小' }]}
          >
            <Input placeholder="请输入应用大小，例如：10MB" />
          </Form.Item>

          <Form.Item
            name="min_open_harmony_os_ver"
            label="最低HarmonyOS版本"
            rules={[{ required: true, message: '请输入最低HarmonyOS版本' }]}
          >
            <Select placeholder="请选择最低HarmonyOS版本">
              <Option value="2.0">HarmonyOS 2.0</Option>
              <Option value="3.0">HarmonyOS 3.0</Option>
              <Option value="4.0">HarmonyOS 4.0</Option>
              <Option value="5.0">HarmonyOS 5.0</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="price"
            label="价格"
            rules={[{ required: true, message: '请设置价格' }]}
          >
            <Select 
              placeholder="请选择价格类型"
              onChange={(value) => {
                // 当选择免费时，清空价格金额
                if (value === 'free') {
                  form.setFieldsValue({ priceAmount: undefined });
                }
              }}
            >
              <Option value="free">免费</Option>
              <Option value="paid">付费</Option>
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.price !== currentValues.price}
          >
            {({ getFieldValue }) => 
              getFieldValue('price') === 'paid' ? (
                <Form.Item
                  name="priceAmount"
                  label="价格金额"
                  rules={[{ required: true, message: '请输入价格金额' }]}
                >
                  <InputNumber 
                    min={0.01} 
                    precision={2} 
                    placeholder="请输入价格" 
                    addonBefore="¥" 
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              ) : null
            }
          </Form.Item>

          <Form.Item
            name="description"
            label="应用描述"
            rules={[{ required: true, message: '请输入应用描述' }]}
          >
            <TextArea rows={4} placeholder="请输入应用描述" />
          </Form.Item>

          <Form.Item
            name="short_desc"
            label="简短描述"
            rules={[
              { required: true, message: '请输入简短描述' },
              { max: 50, message: '简短描述不能超过50个字符' }
            ]}
          >
            <Input placeholder="请输入简短描述，最多50个字符" />
          </Form.Item>

          <Form.Item
            name="appPackage"
            label="应用安装包"
            rules={[{ required: true, message: '请上传应用安装包' }]}
          >
            <Upload.Dragger
              name="file"
              maxCount={1}
              customRequest={customUploadPackage} // 使用自定义上传方法
              accept=".hap,.hsp,.app"
              onChange={(info) => {
                if (info.file.status === 'done') {
                  message.success(`${info.file.name} 上传成功`);
                } else if (info.file.status === 'error') {
                  message.error(`${info.file.name} 上传失败`);
                }
              }}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">支持 HAP、HSP、APP 格式的应用安装包</p>
            </Upload.Dragger>
          </Form.Item>

          <Form.Item
            name="screenshots"
            label="应用截图"
            rules={[{ required: true, message: '请上传至少一张应用截图' }]}
          >
            <Upload
              name="screenshots"
              listType="picture-card"
              customRequest={customUploadScreenshot}
              accept="image/*"
              multiple
              onRemove={(file) => {
                // 处理删除截图
                const index = screenshotUrls.findIndex((url, idx) => idx === file.uid || url === file.url);
                if (index > -1) {
                  const newScreenshotUrls = screenshotUrls.filter((_, idx) => idx !== index);
                  setScreenshotUrls(newScreenshotUrls);
                  form.setFieldsValue({ screenshots: newScreenshotUrls });
                }
              }}
              onChange={(info) => {
                if (info.file.status === 'done') {
                  message.success(`${info.file.name} 上传成功`);
                } else if (info.file.status === 'error') {
                  message.error(`${info.file.name} 上传失败`);
                }
              }}
            >
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上传</div>
              </div>
            </Upload>
            <div>建议上传3-5张截图，展示应用主要功能</div>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                创建应用
              </Button>
              <Button 
                type="default" 
                onClick={() => {
                  form.validateFields().then((values) => {
                    handleSaveDraft(values);
                  }).catch((errorInfo) => {
                    console.log('表单验证失败:', errorInfo);
                    message.warning('请完善必填信息后再保存草稿');
                  });
                }}
              >
                保存至草稿
              </Button>
              <Button onClick={() => history.back()}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </PageContainer>
  );
};

export default AppCreate;