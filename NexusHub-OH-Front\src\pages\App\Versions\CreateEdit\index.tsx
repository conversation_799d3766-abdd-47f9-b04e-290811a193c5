import { PageContainer } from '@ant-design/pro-components';
import { Card, Form, Input, Button, Upload, message, Space, InputNumber, Select } from 'antd';
const { Option } = Select;
import { UploadOutlined, SaveOutlined, ArrowLeftOutlined, PictureOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import { useRequest, history, useParams, request } from '@umijs/max';
import type { UploadFile } from 'antd/es/upload/interface';
import { createAppVersion, updateAppVersion, getAppVersionDetail } from '@/services/version';
import type { AppVersion } from '@/services/version';

const { TextArea } = Input;

const CreateEditVersion: React.FC = () => {
  const params = useParams<{ id: string; versionId?: string }>();
  const appId = params.id;
  const versionId = params.versionId;
  const isEdit = !!versionId;
  
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [screenshotList, setScreenshotList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);

  // 获取版本详情（编辑模式）
  const { data: versionDetail, loading: detailLoading } = useRequest(
    () => isEdit ? getAppVersionDetail(Number(versionId)) : Promise.resolve(null),
    {
      ready: isEdit,
      onSuccess: (data) => {
        if (data) {
          form.setFieldsValue({
            versionName: data.versionName,
            versionCode: data.versionCode,
            updateDescription: data.updateDescription,
          });
          // 如果有文件信息，设置文件列表
          if (data.filePath) {
            setFileList([{
              uid: '1',
              name: data.fileName || 'app.apk',
              status: 'done',
              url: data.filePath,
            }]);
          }
          // 如果有应用截图，设置截图列表
          if (data.screenshots && data.screenshots.length > 0) {
            const screenshots = data.screenshots.map((screenshot: string, index: number) => ({
              uid: `screenshot-${index}`,
              name: `screenshot-${index + 1}.jpg`,
              status: 'done' as const,
              url: screenshot,
            }));
            setScreenshotList(screenshots);
          }
        }
      },
    }
  );

  // 获取上传token
  const getUploadToken = async (fileType: string, fileName: string) => {
    const response = await request('/upload/token', {
      method: 'GET',
      params: {
        file_type: fileType,
        file_name: fileName,
      },
    });
    
    if (!response.data || !response.data.file_url) {
      throw new Error('获取上传URL失败');
    }
    
    return response.data.file_url;
  };

  // 上传文件到对象存储
  const uploadFileToStorage = async (file: File, uploadUrl: string) => {
    if (!uploadUrl) {
      throw new Error('上传URL不能为空');
    }
    
    const response = await fetch(uploadUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type,
      },
    });
    if (!response.ok) {
      throw new Error('文件上传失败');
    }
    return uploadUrl.split('?')[0]; // 返回不带查询参数的URL
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    if (!isEdit && fileList.length === 0) {
      message.error('请上传应用文件（HAP、HSP、APP格式）');
      return;
    }

    try {
      setUploading(true);
      
      let packageUrl = '';
      let fileSize = 0;
      const screenshots: string[] = [];
      
      // 如果有新文件上传
      if (fileList.length > 0 && fileList[0].originFileObj) {
        const file = fileList[0].originFileObj;
        const uploadUrl = await getUploadToken('package', file.name);
        packageUrl = await uploadFileToStorage(file, uploadUrl);
        fileSize = file.size;
      }
      
      // 如果有应用截图上传
      for (const screenshot of screenshotList) {
        if (screenshot.originFileObj) {
          const uploadUrl = await getUploadToken('screenshot', screenshot.originFileObj.name);
          const screenshotUrl = await uploadFileToStorage(screenshot.originFileObj, uploadUrl);
          screenshots.push(screenshotUrl);
        } else if (screenshot.url) {
          // 保留已有的截图
          screenshots.push(screenshot.url);
        }
      }
      
      const requestData = {
        version_name: values.versionName,
        version_code: values.versionCode,
        change_log: values.updateDescription || '',
        min_open_harmony_os_ver: values.minHarmonyOSVer,
        ...(packageUrl && { package_url: packageUrl }),
        ...(fileSize && { size: fileSize }),
        ...(screenshots.length > 0 && { screenshots }),
      };

      if (isEdit) {
        await updateAppVersion(Number(appId), Number(versionId), requestData);
        message.success('版本更新成功');
      } else {
        await createAppVersion(Number(appId), requestData);
        message.success('版本创建成功');
      }
      
      // 返回版本列表页
      history.push(`/app/list/versions/${appId}`);
    } catch (error) {
      console.error('操作失败:', error);
      message.error(isEdit ? '版本更新失败' : '版本创建失败');
    } finally {
      setUploading(false);
    }
  };

  // 文件上传配置
  const uploadProps = {
    beforeUpload: (file: File) => {
      // 检查文件类型
      const fileName = file.name.toLowerCase();
      const allowedExtensions = ['.hap', '.hsp', '.app'];
      const isValidFile = allowedExtensions.some(ext => fileName.endsWith(ext));
      if (!isValidFile) {
        message.error('只能上传 HAP、HSP、APP 格式的文件');
        return false;
      }
      
      // 检查文件大小（限制为 100MB）
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        message.error('文件大小不能超过 100MB');
        return false;
      }
      
      return false; // 阻止自动上传
    },
    fileList,
    onChange: ({ fileList: newFileList }) => {
      setFileList(newFileList.slice(-1)); // 只保留最后一个文件
    },
    onRemove: () => {
      setFileList([]);
    },
  };

  // 应用截图上传配置
  const screenshotUploadProps = {
    beforeUpload: (file: File) => {
      // 检查文件类型
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('只能上传图片文件');
        return false;
      }
      
      // 检查文件大小（限制为 5MB）
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error('图片大小不能超过 5MB');
        return false;
      }
      
      return false; // 阻止自动上传
    },
    fileList: screenshotList,
    onChange: ({ fileList: newFileList }) => {
      setScreenshotList(newFileList.slice(-5)); // 最多保留5张截图
    },
    onRemove: (file: UploadFile) => {
      setScreenshotList(screenshotList.filter(item => item.uid !== file.uid));
    },
    multiple: true,
    listType: 'picture-card' as const,
  };

  return (
    <PageContainer>
      <Card bordered={false}>
        <div style={{ marginBottom: 16 }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => history.push(`/app/list/versions/${appId}`)}
          >
            返回版本列表
          </Button>
        </div>
        
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          loading={detailLoading}
        >
          <Form.Item
            name="versionName"
            label="版本号"
            rules={[
              { required: true, message: '请输入版本号' },
              { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式应为 x.y.z（如：1.0.0）' }
            ]}
          >
            <Input placeholder="请输入版本号，如：1.0.0" />
          </Form.Item>
          
          <Form.Item
            name="versionCode"
            label="版本代码"
            rules={[
              { required: true, message: '请输入版本代码' },
              { type: 'number', min: 1, message: '版本代码必须是大于0的整数' }
            ]}
          >
            <InputNumber 
              placeholder="请输入版本代码（整数）" 
              style={{ width: '100%' }}
              min={1}
            />
          </Form.Item>
          
          <Form.Item
            name="updateDescription"
            label="更新说明"
            rules={[{ required: true, message: '请输入更新说明' }]}
          >
            <TextArea 
              rows={6} 
              placeholder="请详细描述本次更新的内容..." 
              showCount
              maxLength={1000}
            />
          </Form.Item>
          
          <Form.Item
            name="minHarmonyOSVer"
            label="最低HarmonyOS版本"
            rules={[{ required: true, message: '请选择最低HarmonyOS版本' }]}
          >
            <Select placeholder="请选择最低HarmonyOS版本">
              <Option value="2.0">HarmonyOS 2.0</Option>
              <Option value="3.0">HarmonyOS 3.0</Option>
              <Option value="4.0">HarmonyOS 4.0</Option>
              <Option value="5.0">HarmonyOS 5.0</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            label="应用文件"
            required={!isEdit}
          >
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>
                {isEdit ? '重新上传文件（可选）' : '选择应用文件'}
              </Button>
            </Upload>
            <div style={{ marginTop: 8, color: '#666', fontSize: '12px' }}>
              支持 HAP、HSP、APP 格式，文件大小不超过 100MB
            </div>
          </Form.Item>
          
          <Form.Item
            label="应用截图"
            extra="上传应用截图，最多5张，支持 JPG、PNG 格式，单张图片不超过 5MB"
          >
            <Upload {...screenshotUploadProps}>
              {screenshotList.length >= 5 ? null : (
                <div style={{ 
                  width: 104, 
                  height: 104, 
                  border: '1px dashed #d9d9d9', 
                  borderRadius: 6,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  transition: 'border-color 0.3s'
                }}>
                  <PictureOutlined style={{ fontSize: 24, color: '#999' }} />
                  <div style={{ marginTop: 8, color: '#999', fontSize: 12 }}>上传截图</div>
                </div>
              )}
            </Upload>
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit" 
                icon={<SaveOutlined />}
                loading={uploading}
              >
                {isEdit ? '更新版本' : '创建版本'}
              </Button>
              <Button 
                onClick={() => history.push(`/app/list/versions/${appId}`)}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </PageContainer>
  );
};

export default CreateEditVersion;