"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import {
  ProForm,
  ProFormDependency,
  ProFormSelect,
  ProFormText,
  ProFormTextArea
} from "@ant-design/pro-components";
import { useRequest, request } from "@umijs/max";
import { Button, message, Upload } from "antd";
import { UploadOutlined } from "@ant-design/icons";
import { useState, useEffect } from "react";
import { uploadFile } from "@/services/upload";
import { queryCity, queryCurrent, queryProvince, queryCountry, queryDistrict, queryStreet } from "../service";
import useStyles from "./index.style";
const validatorPhone = (rule, value, callback) => {
  if (!value) {
    callback("\u8BF7\u8F93\u5165\u60A8\u7684\u8054\u7CFB\u7535\u8BDD!");
    return;
  }
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(value)) {
    callback("\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u624B\u673A\u53F7\u683C\u5F0F!");
    return;
  }
  callback();
};
const BaseView = () => {
  const { styles } = useStyles();
  const [avatarUploading, setAvatarUploading] = useState(false);
  const [currentAvatar, setCurrentAvatar] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const AvatarView = ({ avatar }) => {
    const handleAvatarUpload = async (options) => {
      const { file, onSuccess, onError } = options;
      try {
        setAvatarUploading(true);
        const isImage = file.type.startsWith("image/");
        if (!isImage) {
          message.error("\u53EA\u80FD\u4E0A\u4F20\u56FE\u7247\u6587\u4EF6!");
          onError(new Error("\u6587\u4EF6\u7C7B\u578B\u9519\u8BEF"));
          return;
        }
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isLt2M) {
          message.error("\u56FE\u7247\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC72MB!");
          onError(new Error("\u6587\u4EF6\u5927\u5C0F\u8D85\u9650"));
          return;
        }
        const fileUrl = await uploadFile("avatar", file);
        const response = await request("/users/profile", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json"
          },
          data: {
            avatar: fileUrl
          }
        });
        if (response.code === 200) {
          message.success("\u5934\u50CF\u66F4\u65B0\u6210\u529F");
          setCurrentAvatar(fileUrl);
          onSuccess(fileUrl);
          refreshUserData();
        } else {
          message.error(response.message || "\u5934\u50CF\u66F4\u65B0\u5931\u8D25");
          onError(new Error(response.message || "\u66F4\u65B0\u5931\u8D25"));
        }
      } catch (error) {
        console.error("\u5934\u50CF\u4E0A\u4F20\u5931\u8D25:", error);
        message.error("\u5934\u50CF\u4E0A\u4F20\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5");
        onError(error);
      } finally {
        setAvatarUploading(false);
      }
    };
    return /* @__PURE__ */ jsxs(Fragment, { children: [
      /* @__PURE__ */ jsx("div", { className: styles.avatar_title, children: "\u5934\u50CF" }),
      /* @__PURE__ */ jsx("div", { className: styles.avatar, children: /* @__PURE__ */ jsx("img", { src: currentAvatar || avatar, alt: "avatar" }) }),
      /* @__PURE__ */ jsx(
        Upload,
        {
          showUploadList: false,
          customRequest: handleAvatarUpload,
          accept: "image/*",
          disabled: avatarUploading,
          beforeUpload: () => false,
          children: /* @__PURE__ */ jsx("div", { className: styles.button_view, children: /* @__PURE__ */ jsxs(Button, { loading: avatarUploading, children: [
            /* @__PURE__ */ jsx(UploadOutlined, {}),
            avatarUploading ? "\u4E0A\u4F20\u4E2D..." : "\u66F4\u6362\u5934\u50CF"
          ] }) })
        }
      )
    ] });
  };
  const { data: currentUser, loading, refresh: refreshUserData } = useRequest(() => {
    return queryCurrent();
  });
  useEffect(() => {
    if (currentUser) {
      setCurrentAvatar(currentUser.avatar || "");
    }
  }, [currentUser]);
  const getAvatarURL = () => {
    if (currentUser) {
      if (currentUser.avatar) {
        return currentUser.avatar;
      }
      const url = "https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png";
      return url;
    }
    return "";
  };
  const handleFinish = async (values) => {
    if (submitting) {
      return;
    }
    try {
      setSubmitting(true);
      const updateData = {
        username: values.name,
        email: values.email,
        description: values.profile,
        company_name: values.group,
        address: values.detailAddress,
        province: values.province?.label || values.province,
        city: values.city?.label || values.city,
        district: values.district?.label || values.district,
        street: values.street?.label || values.street,
        phone: values.phone
      };
      console.log("\u{1F50D} [DEBUG] \u63D0\u4EA4\u66F4\u65B0\u6570\u636E:", updateData);
      const response = await request("/users/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json"
        },
        data: updateData
      });
      console.log("\u{1F50D} [DEBUG] \u66F4\u65B0\u54CD\u5E94:", response);
      if (response.code === 200 || response.code === 0) {
        message.success("\u66F4\u65B0\u57FA\u672C\u4FE1\u606F\u6210\u529F");
        refreshUserData();
      } else {
        message.error(response.message || "\u66F4\u65B0\u57FA\u672C\u4FE1\u606F\u5931\u8D25");
      }
    } catch (error) {
      console.error("\u66F4\u65B0\u7528\u6237\u8D44\u6599\u5931\u8D25:", error);
      message.error("\u66F4\u65B0\u57FA\u672C\u4FE1\u606F\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5");
    } finally {
      setSubmitting(false);
    }
  };
  return /* @__PURE__ */ jsx("div", { className: styles.baseView, children: loading ? null : /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx("div", { className: styles.left, children: /* @__PURE__ */ jsxs(
      ProForm,
      {
        layout: "vertical",
        onFinish: handleFinish,
        submitter: {
          searchConfig: {
            submitText: submitting ? "\u66F4\u65B0\u4E2D..." : "\u66F4\u65B0\u57FA\u672C\u4FE1\u606F"
          },
          render: (_, dom) => dom[1],
          submitButtonProps: {
            loading: submitting,
            disabled: submitting
          }
        },
        initialValues: {
          ...currentUser,
          phone: currentUser?.phone,
          country: currentUser?.country ? {
            label: currentUser.country === "China" ? "\u4E2D\u56FD" : currentUser.country,
            value: currentUser.country === "China" ? "CN" : currentUser.country
          } : void 0,
          province: currentUser?.geographic?.province ? {
            label: currentUser.geographic.province.name,
            value: currentUser.geographic.province.id
          } : void 0,
          city: currentUser?.geographic?.city ? {
            label: currentUser.geographic.city.name,
            value: currentUser.geographic.city.id
          } : void 0,
          district: currentUser?.geographic?.district ? {
            label: currentUser.geographic.district.name,
            value: currentUser.geographic.district.id
          } : void 0,
          street: currentUser?.geographic?.street ? {
            label: currentUser.geographic.street.name,
            value: currentUser.geographic.street.id
          } : void 0
        },
        hideRequiredMark: true,
        children: [
          /* @__PURE__ */ jsx(
            ProFormText,
            {
              width: "md",
              name: "email",
              label: "\u90AE\u7BB1",
              rules: [
                {
                  required: true,
                  message: "\u8BF7\u8F93\u5165\u60A8\u7684\u90AE\u7BB1!"
                }
              ]
            }
          ),
          /* @__PURE__ */ jsx(
            ProFormText,
            {
              width: "md",
              name: "name",
              label: "\u6635\u79F0",
              rules: [
                {
                  required: true,
                  message: "\u8BF7\u8F93\u5165\u60A8\u7684\u6635\u79F0!"
                }
              ]
            }
          ),
          /* @__PURE__ */ jsx(
            ProFormTextArea,
            {
              name: "profile",
              label: "\u4E2A\u4EBA\u7B80\u4ECB",
              rules: [
                {
                  required: true,
                  message: "\u8BF7\u8F93\u5165\u4E2A\u4EBA\u7B80\u4ECB!"
                }
              ],
              placeholder: "\u4E2A\u4EBA\u7B80\u4ECB"
            }
          ),
          /* @__PURE__ */ jsx(
            ProFormSelect,
            {
              width: "sm",
              name: "country",
              label: "\u56FD\u5BB6/\u5730\u533A",
              placeholder: "\u8BF7\u9009\u62E9\u56FD\u5BB6/\u5730\u533A",
              rules: [
                {
                  required: true,
                  message: "\u8BF7\u8F93\u5165\u60A8\u7684\u56FD\u5BB6\u6216\u5730\u533A!"
                }
              ],
              request: async () => {
                console.log("\u{1F50D} [DEBUG] \u5F00\u59CB\u8BF7\u6C42\u56FD\u5BB6\u6570\u636E...");
                return queryCountry().then(({ data }) => {
                  console.log("\u{1F50D} [DEBUG] \u56FD\u5BB6\u6570\u636E\u539F\u59CB\u54CD\u5E94:", data);
                  const result = data.map((item) => {
                    console.log("\u{1F50D} [DEBUG] \u5904\u7406\u56FD\u5BB6\u9879:", item);
                    return {
                      label: item.label || item.name,
                      value: item.key || item.id
                    };
                  });
                  console.log("\u{1F50D} [DEBUG] \u56FD\u5BB6\u6570\u636E\u6700\u7EC8\u7ED3\u679C:", result);
                  return result;
                }).catch((error) => {
                  console.error("\u{1F50D} [DEBUG] \u56FD\u5BB6\u6570\u636E\u8BF7\u6C42\u5931\u8D25:", error);
                  return [];
                });
              }
            }
          ),
          /* @__PURE__ */ jsxs(ProForm.Group, { title: "\u6240\u5728\u7701\u5E02", size: 8, children: [
            /* @__PURE__ */ jsx(
              ProFormSelect,
              {
                rules: [
                  {
                    required: true,
                    message: "\u8BF7\u8F93\u5165\u60A8\u7684\u6240\u5728\u7701!"
                  }
                ],
                width: "sm",
                placeholder: "\u8BF7\u9009\u62E9\u7701\u4EFD",
                fieldProps: {
                  labelInValue: true
                },
                name: "province",
                className: styles.item,
                request: async () => {
                  console.log("\u{1F50D} [DEBUG] \u5F00\u59CB\u8BF7\u6C42\u7701\u4EFD\u6570\u636E...");
                  return queryProvince().then(({ data }) => {
                    console.log("\u{1F50D} [DEBUG] \u7701\u4EFD\u6570\u636E\u539F\u59CB\u54CD\u5E94:", data);
                    const result = data.map((item) => {
                      console.log("\u{1F50D} [DEBUG] \u5904\u7406\u7701\u4EFD\u9879:", item);
                      return {
                        label: item.label || item.name,
                        value: item.key || item.id
                      };
                    });
                    console.log("\u{1F50D} [DEBUG] \u7701\u4EFD\u6570\u636E\u6700\u7EC8\u7ED3\u679C:", result);
                    return result;
                  }).catch((error) => {
                    console.error("\u{1F50D} [DEBUG] \u7701\u4EFD\u6570\u636E\u8BF7\u6C42\u5931\u8D25:", error);
                    return [];
                  });
                }
              }
            ),
            /* @__PURE__ */ jsx(ProFormDependency, { name: ["province"], children: ({ province }) => {
              return /* @__PURE__ */ jsx(
                ProFormSelect,
                {
                  params: {
                    key: province?.value
                  },
                  name: "city",
                  width: "sm",
                  placeholder: "\u8BF7\u9009\u62E9\u57CE\u5E02",
                  rules: [
                    {
                      required: true,
                      message: "\u8BF7\u8F93\u5165\u60A8\u7684\u6240\u5728\u57CE\u5E02!"
                    }
                  ],
                  disabled: !province,
                  className: styles.item,
                  fieldProps: {
                    labelInValue: true
                  },
                  request: async () => {
                    if (!province?.value) {
                      return [];
                    }
                    console.log("\u{1F50D} [DEBUG] \u5F00\u59CB\u8BF7\u6C42\u57CE\u5E02\u6570\u636E\uFF0C\u7701\u4EFDID:", province.value);
                    return queryCity(province.value || "").then(({ data }) => {
                      console.log("\u{1F50D} [DEBUG] \u57CE\u5E02\u6570\u636E\u539F\u59CB\u54CD\u5E94:", data);
                      if (!data || !Array.isArray(data)) {
                        console.log("\u{1F50D} [DEBUG] \u57CE\u5E02\u6570\u636E\u4E3A\u7A7A\u6216\u683C\u5F0F\u9519\u8BEF\uFF0C\u8FD4\u56DE\u7A7A\u6570\u7EC4");
                        return [];
                      }
                      const result = data.map((item) => {
                        console.log("\u{1F50D} [DEBUG] \u5904\u7406\u57CE\u5E02\u9879:", item);
                        return {
                          label: item.label || item.name,
                          value: item.key || item.id
                        };
                      });
                      console.log("\u{1F50D} [DEBUG] \u57CE\u5E02\u6570\u636E\u6700\u7EC8\u7ED3\u679C:", result);
                      return result;
                    }).catch((error) => {
                      console.error("\u{1F50D} [DEBUG] \u57CE\u5E02\u6570\u636E\u8BF7\u6C42\u5931\u8D25:", error);
                      return [];
                    });
                  }
                }
              );
            } })
          ] }),
          /* @__PURE__ */ jsxs(ProForm.Group, { title: "\u533A\u9547\u8857\u9053", size: 8, children: [
            /* @__PURE__ */ jsx(ProFormDependency, { name: ["city"], children: ({ city }) => {
              return /* @__PURE__ */ jsx(
                ProFormSelect,
                {
                  params: {
                    key: city?.value
                  },
                  name: "district",
                  width: "sm",
                  placeholder: "\u8BF7\u9009\u62E9\u533A/\u53BF",
                  rules: [
                    {
                      required: true,
                      message: "\u8BF7\u9009\u62E9\u60A8\u7684\u533A\u9547!"
                    }
                  ],
                  disabled: !city,
                  className: styles.item,
                  fieldProps: {
                    labelInValue: true
                  },
                  request: async () => {
                    if (!city?.value) {
                      return [];
                    }
                    console.log("\u{1F50D} [DEBUG] \u5F00\u59CB\u8BF7\u6C42\u533A\u9547\u6570\u636E\uFF0C\u57CE\u5E02ID:", city.value);
                    return queryDistrict(city.value || "").then(({ data }) => {
                      console.log("\u{1F50D} [DEBUG] \u533A\u9547\u6570\u636E\u539F\u59CB\u54CD\u5E94:", data);
                      if (!data || !Array.isArray(data)) {
                        console.log("\u{1F50D} [DEBUG] \u533A\u9547\u6570\u636E\u4E3A\u7A7A\u6216\u683C\u5F0F\u9519\u8BEF\uFF0C\u8FD4\u56DE\u7A7A\u6570\u7EC4");
                        return [];
                      }
                      const result = data.map((item) => {
                        console.log("\u{1F50D} [DEBUG] \u5904\u7406\u533A\u9547\u9879:", item);
                        return {
                          label: item.label || item.name,
                          value: item.key || item.id
                        };
                      });
                      console.log("\u{1F50D} [DEBUG] \u533A\u9547\u6570\u636E\u6700\u7EC8\u7ED3\u679C:", result);
                      return result;
                    }).catch((error) => {
                      console.error("\u{1F50D} [DEBUG] \u533A\u9547\u6570\u636E\u8BF7\u6C42\u5931\u8D25:", error);
                      return [];
                    });
                  }
                }
              );
            } }),
            /* @__PURE__ */ jsx(ProFormDependency, { name: ["district"], children: ({ district }) => {
              return /* @__PURE__ */ jsx(
                ProFormSelect,
                {
                  params: {
                    key: district?.value
                  },
                  name: "street",
                  width: "sm",
                  placeholder: "\u8BF7\u9009\u62E9\u8857\u9053",
                  rules: [
                    {
                      required: true,
                      message: "\u8BF7\u9009\u62E9\u60A8\u7684\u8857\u9053!"
                    }
                  ],
                  disabled: !district,
                  className: styles.item,
                  fieldProps: {
                    labelInValue: true
                  },
                  request: async () => {
                    if (!district?.value) {
                      return [];
                    }
                    console.log("\u{1F50D} [DEBUG] \u5F00\u59CB\u8BF7\u6C42\u8857\u9053\u6570\u636E\uFF0C\u533A\u9547ID:", district.value);
                    return queryStreet(district.value || "").then(({ data }) => {
                      console.log("\u{1F50D} [DEBUG] \u8857\u9053\u6570\u636E\u539F\u59CB\u54CD\u5E94:", data);
                      if (!data || !Array.isArray(data)) {
                        console.log("\u{1F50D} [DEBUG] \u8857\u9053\u6570\u636E\u4E3A\u7A7A\u6216\u683C\u5F0F\u9519\u8BEF\uFF0C\u8FD4\u56DE\u7A7A\u6570\u7EC4");
                        return [];
                      }
                      const result = data.map((item) => {
                        console.log("\u{1F50D} [DEBUG] \u5904\u7406\u8857\u9053\u9879:", item);
                        return {
                          label: item.label || item.name,
                          value: item.key || item.id
                        };
                      });
                      console.log("\u{1F50D} [DEBUG] \u8857\u9053\u6570\u636E\u6700\u7EC8\u7ED3\u679C:", result);
                      return result;
                    }).catch((error) => {
                      console.error("\u{1F50D} [DEBUG] \u8857\u9053\u6570\u636E\u8BF7\u6C42\u5931\u8D25:", error);
                      return [];
                    });
                  }
                }
              );
            } })
          ] }),
          /* @__PURE__ */ jsx(
            ProFormText,
            {
              width: "md",
              name: "detailAddress",
              label: "\u8BE6\u7EC6\u5730\u5740",
              rules: [
                {
                  required: true,
                  message: "\u8BF7\u8F93\u5165\u60A8\u7684\u8BE6\u7EC6\u5730\u5740!"
                }
              ],
              placeholder: "\u8BF7\u8F93\u5165\u60A8\u7684\u8BE6\u7EC6\u5730\u5740!"
            }
          ),
          /* @__PURE__ */ jsx(
            ProFormText,
            {
              width: "md",
              name: "phone",
              label: "\u8054\u7CFB\u7535\u8BDD",
              rules: [
                {
                  required: true,
                  message: "\u8BF7\u8F93\u5165\u60A8\u7684\u8054\u7CFB\u7535\u8BDD!"
                },
                {
                  validator: validatorPhone
                }
              ],
              placeholder: "\u8BF7\u8F93\u5165\u60A8\u7684\u624B\u673A\u53F7"
            }
          )
        ]
      }
    ) }),
    /* @__PURE__ */ jsx("div", { className: styles.right, children: /* @__PURE__ */ jsx(AvatarView, { avatar: getAvatarURL() }) })
  ] }) });
};
export default BaseView;
