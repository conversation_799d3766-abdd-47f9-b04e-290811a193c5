if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface FeaturedPage_Params {
    collections?: FeaturedCollectionModel[];
    isLoading?: boolean;
    isLoadingMore?: boolean;
    hasMore?: boolean;
    currentPage?: number;
    pageSize?: number;
    errorMessage?: string;
    showError?: boolean;
    apiService?: ApiService;
    deviceUtils?: DeviceUtils;
}
import type { FeaturedCollectionModel, FeaturedCollectionListResponse } from '../models/FeaturedCollection';
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import { ApiService } from "@normalized:N&&&entry/src/main/ets/services/ApiService&";
import { LoadingView, LoadMoreView } from "@normalized:N&&&entry/src/main/ets/components/LoadingView&";
import hilog from "@ohos:hilog";
export class FeaturedPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__collections = new ObservedPropertyObjectPU([], this, "collections");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__isLoadingMore = new ObservedPropertySimplePU(false, this, "isLoadingMore");
        this.__hasMore = new ObservedPropertySimplePU(true, this, "hasMore");
        this.__currentPage = new ObservedPropertySimplePU(1, this, "currentPage");
        this.__pageSize = new ObservedPropertySimplePU(20, this, "pageSize");
        this.__errorMessage = new ObservedPropertySimplePU('', this, "errorMessage");
        this.__showError = new ObservedPropertySimplePU(false, this, "showError");
        this.apiService = ApiService.getInstance();
        this.deviceUtils = DeviceUtils.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: FeaturedPage_Params) {
        if (params.collections !== undefined) {
            this.collections = params.collections;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.isLoadingMore !== undefined) {
            this.isLoadingMore = params.isLoadingMore;
        }
        if (params.hasMore !== undefined) {
            this.hasMore = params.hasMore;
        }
        if (params.currentPage !== undefined) {
            this.currentPage = params.currentPage;
        }
        if (params.pageSize !== undefined) {
            this.pageSize = params.pageSize;
        }
        if (params.errorMessage !== undefined) {
            this.errorMessage = params.errorMessage;
        }
        if (params.showError !== undefined) {
            this.showError = params.showError;
        }
        if (params.apiService !== undefined) {
            this.apiService = params.apiService;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
    }
    updateStateVars(params: FeaturedPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__collections.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoadingMore.purgeDependencyOnElmtId(rmElmtId);
        this.__hasMore.purgeDependencyOnElmtId(rmElmtId);
        this.__currentPage.purgeDependencyOnElmtId(rmElmtId);
        this.__pageSize.purgeDependencyOnElmtId(rmElmtId);
        this.__errorMessage.purgeDependencyOnElmtId(rmElmtId);
        this.__showError.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__collections.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__isLoadingMore.aboutToBeDeleted();
        this.__hasMore.aboutToBeDeleted();
        this.__currentPage.aboutToBeDeleted();
        this.__pageSize.aboutToBeDeleted();
        this.__errorMessage.aboutToBeDeleted();
        this.__showError.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __collections: ObservedPropertyObjectPU<FeaturedCollectionModel[]>;
    get collections() {
        return this.__collections.get();
    }
    set collections(newValue: FeaturedCollectionModel[]) {
        this.__collections.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __isLoadingMore: ObservedPropertySimplePU<boolean>;
    get isLoadingMore() {
        return this.__isLoadingMore.get();
    }
    set isLoadingMore(newValue: boolean) {
        this.__isLoadingMore.set(newValue);
    }
    private __hasMore: ObservedPropertySimplePU<boolean>;
    get hasMore() {
        return this.__hasMore.get();
    }
    set hasMore(newValue: boolean) {
        this.__hasMore.set(newValue);
    }
    private __currentPage: ObservedPropertySimplePU<number>;
    get currentPage() {
        return this.__currentPage.get();
    }
    set currentPage(newValue: number) {
        this.__currentPage.set(newValue);
    }
    private __pageSize: ObservedPropertySimplePU<number>;
    get pageSize() {
        return this.__pageSize.get();
    }
    set pageSize(newValue: number) {
        this.__pageSize.set(newValue);
    }
    private __errorMessage: ObservedPropertySimplePU<string>;
    get errorMessage() {
        return this.__errorMessage.get();
    }
    set errorMessage(newValue: string) {
        this.__errorMessage.set(newValue);
    }
    private __showError: ObservedPropertySimplePU<boolean>;
    get showError() {
        return this.__showError.get();
    }
    set showError(newValue: boolean) {
        this.__showError.set(newValue);
    }
    private apiService: ApiService;
    private deviceUtils: DeviceUtils;
    /**
     * 页面即将出现时的回调
     */
    aboutToAppear() {
        this.loadFeaturedCollections();
    }
    /**
     * 加载精选集列表
     */
    private async loadFeaturedCollections() {
        if (this.isLoading)
            return;
        this.isLoading = true;
        this.showError = false;
        this.currentPage = 1;
        this.hasMore = true;
        try {
            hilog.info(0x0000, 'FeaturedPage', '开始加载精选集列表...');
            const response: FeaturedCollectionListResponse = await this.apiService.getFeaturedCollections(this.currentPage, this.pageSize, 'active');
            hilog.info(0x0000, 'FeaturedPage', '精选集列表响应: code=%{public}d, dataExists=%{public}s', response?.code || -1, response?.data ? 'true' : 'false');
            // 改进的数据验证逻辑 - 使用后端返回的list字段
            if (response && response.data && Array.isArray(response.data.list)) {
                this.collections = response.data.list;
                // 设置分页信息
                if (response.data.pagination) {
                    this.hasMore = response.data.pagination.page < response.data.pagination.total_pages;
                    hilog.info(0x0000, 'FeaturedPage', '分页信息: page=%{public}d, total_pages=%{public}d, hasMore=%{public}s', response.data.pagination.page, response.data.pagination.total_pages, this.hasMore ? 'true' : 'false');
                }
                else {
                    this.hasMore = false;
                }
                // 根据数据长度设置状态
                if (this.collections.length > 0) {
                    hilog.info(0x0000, 'FeaturedPage', '成功加载 %{public}d 个精选集', this.collections.length);
                }
                else {
                    hilog.info(0x0000, 'FeaturedPage', '精选集数据为空');
                }
            }
            else {
                // 数据格式不正确或响应失败
                hilog.error(0x0000, 'FeaturedPage', '精选集数据格式错误或响应失败: code=%{public}d, message=%{public}s', response?.code || -1, response?.message || 'unknown');
                this.showError = true;
                this.errorMessage = response?.message || '数据加载失败，请重试';
            }
        }
        catch (error) {
            hilog.error(0x0000, 'FeaturedPage', '加载精选集失败: %{public}s', JSON.stringify(error));
            this.showError = true;
            this.errorMessage = '网络连接失败，请检查网络设置';
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 加载更多精选集
     */
    private async loadMoreCollections() {
        if (this.isLoadingMore || !this.hasMore)
            return;
        this.isLoadingMore = true;
        try {
            const nextPage = this.currentPage + 1;
            hilog.info(0x0000, 'FeaturedPage', '开始加载更多精选集，页码: %{public}d', nextPage);
            const response: FeaturedCollectionListResponse = await this.apiService.getFeaturedCollections(nextPage, this.pageSize, 'active');
            hilog.info(0x0000, 'FeaturedPage', '加载更多响应: code=%{public}d, dataExists=%{public}s', response?.code || -1, response?.data ? 'true' : 'false');
            if (response && response.data && Array.isArray(response.data.list)) {
                const newCollections: FeaturedCollectionModel[] = response.data.list;
                this.collections = this.collections.concat(newCollections);
                this.currentPage = nextPage;
                // 更新分页状态
                if (response.data.pagination) {
                    this.hasMore = response.data.pagination.page < response.data.pagination.total_pages;
                }
                else {
                    this.hasMore = false;
                }
                hilog.info(0x0000, 'FeaturedPage', '成功加载更多 %{public}d 个精选集，总数: %{public}d', newCollections.length, this.collections.length);
            }
            else {
                this.hasMore = false;
                hilog.error(0x0000, 'FeaturedPage', '加载更多数据格式错误: %{public}s', JSON.stringify(response));
                this.getUIContext().getPromptAction().showToast({
                    message: response?.message || '加载失败',
                    duration: 2000
                });
            }
        }
        catch (error) {
            hilog.error(0x0000, 'FeaturedPage', '加载更多精选集失败: %{public}s', JSON.stringify(error));
            this.hasMore = false;
            this.getUIContext().getPromptAction().showToast({
                message: '网络连接失败',
                duration: 2000
            });
        }
        finally {
            this.isLoadingMore = false;
        }
    }
    /**
     * 处理精选集卡片点击
     */
    private handleCollectionClick(collection: FeaturedCollectionModel) {
        this.getUIContext().getRouter().pushUrl({
            url: 'pages/FeaturedCollectionDetailPage',
            params: {
                collectionId: collection.id,
                collection: collection
            }
        }).catch((error: Error) => {
            hilog.error(0x0000, 'FeaturedPage', '导航到精选集详情页失败: %{public}s', JSON.stringify(error));
            this.getUIContext().getPromptAction().showToast({
                message: '页面跳转失败',
                duration: 2000
            });
        });
    }
    /**
     * 返回上一页
     */
    private goBack() {
        this.getUIContext().getRouter().back();
    }
    /**
     * 构建精选集卡片
     */
    private buildCollectionCard(collection: FeaturedCollectionModel, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.backgroundColor(Constants.COLORS.WHITE);
            Column.borderRadius(Constants.BORDER_RADIUS.MEDIUM);
            Column.shadow({
                radius: 4,
                color: Constants.COLORS.SHADOW,
                offsetX: 0,
                offsetY: 2
            });
            Column.onClick(() => this.handleCollectionClick(collection));
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 封面图片
            Image.create(collection.cover_image || Constants.PLACEHOLDER_IMAGE);
            // 封面图片
            Image.width('100%');
            // 封面图片
            Image.height(120);
            // 封面图片
            Image.objectFit(ImageFit.Cover);
            // 封面图片
            Image.borderRadius({ topLeft: Constants.BORDER_RADIUS.MEDIUM, topRight: Constants.BORDER_RADIUS.MEDIUM });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 内容区域
            Column.create({ space: 8 });
            // 内容区域
            Column.alignItems(HorizontalAlign.Start);
            // 内容区域
            Column.padding(12);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(collection.name);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor(Constants.COLORS.TEXT_PRIMARY);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(collection.description);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_SECONDARY);
            Text.maxLines(2);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${collection.app_count}个应用`);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(Constants.COLORS.TEXT_HINT);
        }, Text);
        Text.pop();
        Row.pop();
        // 内容区域
        Column.pop();
        Column.pop();
    }
    /**
     * 构建页面UI
     */
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Navigation.create(new NavPathStack(), { moduleName: "entry", pagePath: "entry/src/main/ets/pages/FeaturedPage", isUserCreateStack: false });
            Navigation.title('精选集');
            Navigation.titleMode(NavigationTitleMode.Mini);
            Navigation.hideBackButton(true);
            Navigation.width('100%');
            Navigation.height('100%');
        }, Navigation);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 内容区域
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.layoutWeight(1);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LoadingView(this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/FeaturedPage.ets", line: 240, col: 11 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {};
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {});
                            }
                        }, { name: "LoadingView" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.showError) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create({ "id": 16777254, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Image.width(64);
                        Image.height(64);
                        Image.fillColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Image.margin({ bottom: 16 });
                    }, Image);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.errorMessage);
                        Text.fontSize(Constants.FONT_SIZE.NORMAL);
                        Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Text.textAlign(TextAlign.Center);
                        Text.margin({ bottom: 24 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('重试');
                        Button.fontSize(Constants.FONT_SIZE.NORMAL);
                        Button.fontColor({ "id": 125829186, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Button.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
                        Button.borderRadius(8);
                        Button.padding({ left: 24, right: 24, top: 8, bottom: 8 });
                        Button.onClick(() => this.loadFeaturedCollections());
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 精选集列表
                        List.create();
                        // 精选集列表
                        List.layoutWeight(1);
                        // 精选集列表
                        List.scrollBar(BarState.Auto);
                        // 精选集列表
                        List.edgeEffect(EdgeEffect.Spring);
                        // 精选集列表
                        List.padding({ top: 16 });
                        // 精选集列表
                        List.onReachEnd(() => {
                            if (this.hasMore && !this.isLoadingMore) {
                                this.loadMoreCollections();
                            }
                        });
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const collection = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.margin({ left: 16, right: 16, bottom: 16 });
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.buildCollectionCard.bind(this)(collection);
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.collections, forEachItemGenFunction, (collection: FeaturedCollectionModel) => collection.id.toString(), true, false);
                    }, ForEach);
                    ForEach.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 加载更多组件
                        if (this.hasMore || this.isLoadingMore) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                {
                                    const itemCreation = (elmtId, isInitialRender) => {
                                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                        itemCreation2(elmtId, isInitialRender);
                                        if (!isInitialRender) {
                                            ListItem.pop();
                                        }
                                        ViewStackProcessor.StopGetAccessRecording();
                                    };
                                    const itemCreation2 = (elmtId, isInitialRender) => {
                                        ListItem.create(deepRenderFunction, true);
                                        ListItem.padding({ top: 12, bottom: 12 });
                                    };
                                    const deepRenderFunction = (elmtId, isInitialRender) => {
                                        itemCreation(elmtId, isInitialRender);
                                        {
                                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                if (isInitialRender) {
                                                    let componentCall = new LoadMoreView(this, {
                                                        isLoading: this.isLoadingMore,
                                                        hasMore: this.hasMore,
                                                        onLoadMore: () => {
                                                            if (!this.isLoadingMore && this.hasMore) {
                                                                this.loadMoreCollections();
                                                            }
                                                        }
                                                    }, undefined, elmtId, () => { }, { page: "entry/src/main/ets/pages/FeaturedPage.ets", line: 280, col: 17 });
                                                    ViewPU.create(componentCall);
                                                    let paramsLambda = () => {
                                                        return {
                                                            isLoading: this.isLoadingMore,
                                                            hasMore: this.hasMore,
                                                            onLoadMore: () => {
                                                                if (!this.isLoadingMore && this.hasMore) {
                                                                    this.loadMoreCollections();
                                                                }
                                                            }
                                                        };
                                                    };
                                                    componentCall.paramsGenerator_ = paramsLambda;
                                                }
                                                else {
                                                    this.updateStateVarsOfChildByElmtId(elmtId, {
                                                        isLoading: this.isLoadingMore,
                                                        hasMore: this.hasMore
                                                    });
                                                }
                                            }, { name: "LoadMoreView" });
                                        }
                                        ListItem.pop();
                                    };
                                    this.observeComponentCreation2(itemCreation2, ListItem);
                                    ListItem.pop();
                                }
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    // 精选集列表
                    List.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
        Navigation.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "FeaturedPage";
    }
}
registerNamedRoute(() => new FeaturedPage(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/FeaturedPage", pageFullPath: "entry/src/main/ets/pages/FeaturedPage", integratedHsp: "false", moduleType: "followWithHap" });
