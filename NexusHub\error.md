06-17 22:47:07.597   45616-45691   C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [][OnPointerEvent:216] ac: down: 612506
06-17 22:47:07.597   45616-45691   C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [P:D:612506][OnPointerEvent:541] id:612506 recv
06-17 22:47:07.597   45616-45691   C04213/com.exam...InputKeyFlow  com.example.nexushub  I     [] OnInputEvent(86): eid:142,InputId:612506,wid:295,ac:2
06-17 22:47:07.598   45616-45691   C04219/com.exa...shub/WMSDecor  com.example.nexushub  W     [] GetDecorHeight(2235): Get app window decor height failed
06-17 22:47:07.599   45616-45691   C03919/com.exa...InputTracking  com.example.nexushub  I     [(-1:100000:singleton)] pointdown windowId: 295
06-17 22:47:07.599   45616-45691   C04213/com.exam...InputKeyFlow  com.example.nexushub  I     [] ConsumePointerEventInner(872): InputId:612506,wid:295,pointId:0,srcType:2,rect:[0,0,1224,2776],notify:1
06-17 22:47:07.600   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] InputTracking id:612506, fingerId:0, type=0, inject=0, isPrivacyMode=0
06-17 22:47:07.600   45616-45616   C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, enter
06-17 22:47:07.600   45616-45616   C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, leave
06-17 22:47:07.602   45616-45616   C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, enter
06-17 22:47:07.602   45616-45616   C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, leave
06-17 22:47:07.603   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] InputTracking id:612506, touch test hitted node info: fingerId: 0{ tag: Column, depth: 12 };{ tag: Image, depth: 13 };
06-17 22:47:07.603   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] InputTracking id:612506, touch test hitted recognizer type info: recognizer type ClickRecognizer node info: { tag: Column };recognizer type LongPressRecognizer node info: { tag: Image }; { tag: Image };recognizer type PanRecognizer node info: { tag: Image };
06-17 22:47:07.603   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:612506, exclusive 0 type: 0
06-17 22:47:07.603   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:612506, parallel 0 type: 0
06-17 22:47:07.603   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:612506, sequenced 0 type: 0
06-17 22:47:07.603   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:612506, LongPress 0 down, state: 0
06-17 22:47:07.603   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:612506, LongPress 0 down, state: 0
06-17 22:47:07.603   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:612506, click 0 down, ETF: 0, CTP: 0, state: 0
06-17 22:47:07.604   45616-45616   C03919/com.exa...InputTracking  com.example.nexushub  I     [(100000:100000:scope)] Consumed new event id=612506 in ace_container, lastEventInfo: id:612504
06-17 22:47:07.619   45616-45616   C01406/com.exa...shub/OHOS::RS  com.example.nexushub  E     FlushImplicitTransaction return, [renderServiceClient_:1, transactionData empty:1]
06-17 22:47:07.654   45616-45616   C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, enter
06-17 22:47:07.654   45616-45616   C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, leave
06-17 22:47:07.697   45616-45691   C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [][OnPointerEvent:216] ac: move: 612507
06-17 22:47:07.732   45616-45616   C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [B:UK:612508][SetLastProcessedEventId:54] Last eventId:612460, current eventId:612508
06-17 22:47:07.735   45616-47086   C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [][MarkProcessed:71] Ffrt PE: 612461 612508
06-17 22:47:07.745   45616-45691   C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [][OnPointerEvent:216] ac: move, first: 612508-(2025-06-17 22:47:07.731ms), 612509, count: 2, last: ac: up: 612510
06-17 22:47:07.745   45616-45691   C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [P:U:612510][OnPointerEvent:541] id:612510 recv
06-17 22:47:07.745   45616-45691   C04213/com.exam...InputKeyFlow  com.example.nexushub  I     [] OnInputEvent(86): eid:143,InputId:612510,wid:295,ac:4
06-17 22:47:07.746   45616-45691   C04213/com.exam...InputKeyFlow  com.example.nexushub  I     [] ConsumePointerEventInner(872): InputId:612510,wid:295,pointId:0,srcType:2,rect:[0,0,1224,2776],notify:1
06-17 22:47:07.746   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] InputTracking id:612510, fingerId:0, type=1, inject=0, isPrivacyMode=0
06-17 22:47:07.746   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:612510, exclusive 0 type: 1
06-17 22:47:07.747   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:612510, parallel 0 type: 1
06-17 22:47:07.747   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:612510, sequenced 0 type: 1
06-17 22:47:07.747   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:612510, LongPress 0 up, state: 1
06-17 22:47:07.747   45616-45616   C03905/com.exa...ushub/AceDrag  com.example.nexushub  I     [(100000:100000:scope)] Trigger pan onReject
06-17 22:47:07.747   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:612510, LongPress 0 up, state: 1
06-17 22:47:07.747   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:612510, click 0 up, state: 0
06-17 22:47:07.747   45616-45616   C0391E/com.exa...ub/AceGesture  com.example.nexushub  I     [(100000:100000:scope)] Click try accept
06-17 22:47:07.747   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Click accepted, tag: Column
06-17 22:47:07.747   45616-45616   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] id: 0, log: {types: Sequenced, node: Image, prcd: Down, state: READY, prcd: Up, state: FAIL}{types: LongPress, node: Image, prcd: Down, state: DETECTING, prcd: Up, state: FAIL}{types: Pan, node: Image}{types: LongPress, node: Image, prcd: Down, state: DETECTING, prcd: Up, state: FAIL}{types: Click, node: Column, prcd: Down, state: READY, extraInfo: ETF: 0 CFP: 0, prcd: Up, state: SUCCEED, extraInfo: ETF: 0 CFP: 0}
06-17 22:47:07.748   45616-45616   C03919/com.exa...InputTracking  com.example.nexushub  I     [(100000:100000:scope)] Consumed new event id=612510 in ace_container, lastEventInfo: id:612509
06-17 22:47:07.755   45616-45616   C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] destroy TxtParagraph with placeholderCnt_ 0, textAlign_ 0, count 800
06-17 22:47:07.760   45616-45616   A00000/com.exam...FeaturedPage  com.example.nexushub  I     开始加载精选集列表...
06-17 22:47:07.760   45616-45616   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     Built URL: http://172.20.10.3:8080/api/v1/public/featured-collections
06-17 22:47:07.760   45616-45616   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     GET Request - URL: http://172.20.10.3:8080/api/v1/public/featured-collections?page=1&page_size=20&status=active
06-17 22:47:07.760   45616-45616   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     GET Request - Headers: {"Content-Type":"application/json","Accept":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoibmV4dXNodWIiLCJzdWIiOiIxIiwiZXhwIjoxNzUwMjU3OTYzLCJuYmYiOjE3NTAxNzE1NjMsImlhdCI6MTc1MDE3MTU2M30.MZf3mWQ8wdOLWtUdoqDk2WAlkFJYaAZJ1xf2EVAlpy0"}
06-17 22:47:07.762   45616-45616   C03922/com.exa...AceNavigation  com.example.nexushub  I     [(100000:100000:scope)] navigation necessary to immediately trigger a refresh
06-17 22:47:07.763   45616-45616   C03922/com.exa...AceNavigation  com.example.nexushub  I     [(100000:100000:scope)] NavBar SafeArea expand as SafeAreaExpandOpts: type:SAFE_AREA_TYPE_OTHERS_3, edges: SAFE_AREA_EDGE_ALL
06-17 22:47:07.763   45616-45616   C03922/com.exa...AceNavigation  com.example.nexushub  I     [(100000:100000:scope)] Navigation SafArea expand as SafeAreaExpandOpts: type:SAFE_AREA_TYPE_ALL, edges: SAFE_AREA_EDGE_ALL
06-17 22:47:07.763   45616-45616   C03922/com.exa...AceNavigation  com.example.nexushub  I     [(100000:100000:scope)] NavBar SafeArea expand as SafeAreaExpandOpts: type:SAFE_AREA_TYPE_SYSTEM, edges: SAFE_AREA_EDGE_ALL
06-17 22:47:07.763   45616-45616   C0390D/com.exa...ub/AceOverlay  com.example.nexushub  W     [(100000:100000:scope)] failed to get foldCreaseRegion
06-17 22:47:07.765   45616-45616   C03922/com.exa...AceNavigation  com.example.nexushub  I     [(100000:100000:scope)] sync with js stack
06-17 22:47:07.765   45616-45616   C03922/com.exa...AceNavigation  com.example.nexushub  I     [(100000:100000:scope)] last standard page index is -1
06-17 22:47:07.765   45616-45616   C03922/com.exa...AceNavigation  com.example.nexushub  I     [(100000:100000:scope)] page is not change. don't transition
06-17 22:47:07.811   45616-45702   C015B0/com.exa...shub/NETSTACK  com.example.nexushub  I     [http_exec.cpp:418] taskid=-2147483598, size:2045, dns:0.277, connect:0.000, tls:0.000, firstSend:0.355, firstRecv:48.449, total:49.137, redirect:0.000, errCode:0, RespCode:200, httpVer:2, method:GET, osErr:0
06-17 22:47:07.812   45616-45616   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     GET Response - Code: 200, Result: "{\"code\":200,\"message\":\"获取精选集列表成功\",\"data\":{\"items\":[{\"id\":2,\"title\":\"111\",\"description\":\"111\",\"icon\":\"\",\"cover_image\":\"\",\"sort_order\":0,\"is_active\":true,\"is_public\":true,\"creator_id\":1,\"creator\":{\"id\":1,\"created_at\":\"2025-06-07T04:50:58.266145+08:00\",\"updated_at\":\"2025-06-17T22:46:03.450819+08:00\",\"username\":\"admin\",\"email\":\"<EMAIL>\",\"phone\":\"***********\",\"role\":\"admin\",\"status\":\"active\",\"avatar\":\"http://**********:9000/nexushub/avatars/1/1749989867_215819vodx2xa8887xiapc.png.icon.png\",\"last_login_at\":\"2025-06-17T22:46:03.447811+08:00\",\"login_count\":0,\"address\":\"\",\"province\":\"\",\"city\":\"\",\"district\":\"\",\"street\":\"\",\"is_developer\":false,\"developer_name\":\"\",\"company_name\":\"\",\"website\":\"\",\"description\":\"\",\"contact_email\":\"\",\"contact_phone\":\"\",\"business_license\":\"\",\"identity_card\":\"\",\"developer_avatar\":\"\",\"developer_address\":\"\",\"submitted_at\":\"0001-01-01T00:00:00Z\",\"verified_at\":null,\"verify_reason\":\"\",\"verify_status\":\"pending\"},\"created_at\":\"2025-06-16 12:05:06\",\"updated_at\":\"2025-06-16 12:05:06\"},{\"id\":1,\"title\":\"钱钱钱\",\"description\":\"钱钱钱\",\"icon\":\"\",\"cover_image\":\"\",\"sort_order\":0,\"is_active\":true,\"is_public\":true,\"creator_id\":1,\"creator\":{\"id\":1,\"created_at\":\"2025-06-07T04:50:58.266145+08:00\",\"updated_at\":\"2025-06-17T22:46:03.450819+08:00\",\"username\":\"admin\",\"email\":\"<EMAIL>\",\"phone\":\"***********\",\"role\":\"admin\",\"status\":\"active\",\"avatar\":\"http://**********:9000/nexushub/avatars/1/1749989867_215819vodx2xa8887xiapc.png.icon.png\",\"last_login_at\":\"2025-06-17T22:46:03.447811+08:00\",\"login_count\":0,\"address\":\"\",\"province\":\"\",\"city\":\"\",\"district\":\"\",\"street\":\"\",\"is_developer\":false,\"developer_name\":\"\",\"company_name\":\"\",\"website\":\"\",\"description\":\"\",\"contact_email\":\"\",\"contact_phone\":\"\",\"business_license\":\"\",\"identity_card\":\"\",\"developer_avatar\":\"\",\"developer_address\":\"\",\"submitted_at\":\"0001-01-01T00:00:00Z\",\"verified_at\":null,\"verify_reason\":\"\",\"verify_status\":\"pending\"},\"created_at\":\"2025-06-16 12:04:26\",\"updated_at\":\"2025-06-16 12:04:26\"}],\"total\":2,\"page\":1,\"page_size\":20,\"total_pages\":1}}"
06-17 22:47:07.812   45616-45616   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     HTTP Response - Code: 200, URL: unknown
06-17 22:47:07.812   45616-45616   A00000/com.exam...FeaturedPage  com.example.nexushub  I     精选集列表响应: code=200, dataExists=true
06-17 22:47:07.812   45616-45616   A00000/com.exam...FeaturedPage  com.example.nexushub  E     精选集数据格式错误或响应失败: code=200, message=获取精选集列表成功
06-17 22:47:07.966   45616-47041   C01719/com.exa...nexushub/ffrt  com.example.nexushub  W     36:~WorkerThread:72 to exit, qos[0]
06-17 22:47:07.966   45616-47042   C01719/com.exa...nexushub/ffrt  com.example.nexushub  W     37:~WorkerThread:72 to exit, qos[0]
06-17 22:47:07.967   45616-47040   C01719/com.exa...nexushub/ffrt  com.example.nexushub  W     38:~WorkerThread:72 to exit, qos[0]
06-17 22:47:08.550   45616-45616   C02504/com.exa...hub/thp_extra  com.example.nexushub  I     ThpExtraRunCommand[122]ver:5.0.7ThpExtraRunCommand, cmd:THP_UpdateViewsLocation, param:thp#Location#
06-17 22:47:08.550   45616-45616   C02504/com.exa...hub/thp_extra  com.example.nexushub  I     InitTpInterfaces[33]InitTpInterfaces+
06-17 22:47:08.550   45616-45616   C02504/com.exa...hub/thp_extra  com.example.nexushub  E     InitTpInterfaces[35]inited +