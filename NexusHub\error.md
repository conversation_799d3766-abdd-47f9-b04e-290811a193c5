06-17 23:42:27.219   3941-4025     C015B0/com.exa...shub/NETSTACK  com.example.nexushub  E     [epoll_multi_driver.cpp:75] epoll wait event 0 err: 0
06-17 23:42:28.837   3941-4053     C015B0/com.exa...shub/NETSTACK  com.example.nexushub  E     [epoll_multi_driver.cpp:75] epoll wait event 0 err: 11
06-17 23:42:28.845   3941-4018     C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [][OnPointerEvent:216] ac: down: 615695
06-17 23:42:28.845   3941-4018     C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [P:D:615695][OnPointerEvent:541] id:615695 recv
06-17 23:42:28.845   3941-4018     C04213/com.exam...InputKeyFlow  com.example.nexushub  I     [] OnInputEvent(86): eid:68,InputId:615695,wid:298,ac:2
06-17 23:42:28.846   3941-4018     C04219/com.exa...shub/WMSDecor  com.example.nexushub  W     [] GetDecorHeight(2235): Get app window decor height failed
06-17 23:42:28.848   3941-4018     C03919/com.exa...InputTracking  com.example.nexushub  I     [(-1:100000:singleton)] pointdown windowId: 298
06-17 23:42:28.848   3941-4018     C04213/com.exam...InputKeyFlow  com.example.nexushub  I     [] ConsumePointerEventInner(872): InputId:615695,wid:298,pointId:0,srcType:2,rect:[0,0,1224,2776],notify:1
06-17 23:42:28.849   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] InputTracking id:615695, fingerId:0, type=0, inject=0, isPrivacyMode=0
06-17 23:42:28.849   3941-4018     C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [][OnPointerEvent:216] ac: move: 615696
06-17 23:42:28.849   3941-3941     C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, enter
06-17 23:42:28.849   3941-3941     C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, leave
06-17 23:42:28.850   3941-3941     C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, enter
06-17 23:42:28.850   3941-3941     C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, leave
06-17 23:42:28.850   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] InputTracking id:615695, touch test hitted node info: fingerId: 0{ tag: List, depth: 15 };{ tag: Column, depth: 19 };{ tag: Image, depth: 20 };
06-17 23:42:28.850   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] InputTracking id:615695, touch test hitted recognizer type info: recognizer type ClickRecognizer node info: { tag: Column };recognizer type LongPressRecognizer node info: { tag: Image }; { tag: Image };recognizer type PanRecognizer node info: { tag: Image };
06-17 23:42:28.851   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:615695, exclusive 0 type: 0
06-17 23:42:28.851   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:615695, parallel 0 type: 0
06-17 23:42:28.851   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:615695, sequenced 0 type: 0
06-17 23:42:28.851   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:615695, LongPress 0 down, state: 0
06-17 23:42:28.851   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:615695, LongPress 0 down, state: 0
06-17 23:42:28.851   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:615695, click 0 down, ETF: 0, CTP: 0, state: 0
06-17 23:42:28.851   3941-3941     C03919/com.exa...InputTracking  com.example.nexushub  I     [(100000:100000:scope)] Consumed new event id=615695 in ace_container, lastEventInfo: id:615693
06-17 23:42:28.858   3941-3941     C01406/com.exa...shub/OHOS::RS  com.example.nexushub  E     FlushImplicitTransaction return, [renderServiceClient_:1, transactionData empty:1]
06-17 23:42:28.870   3941-3941     C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [B:UK:615700][SetLastProcessedEventId:54] Last eventId:615645, current eventId:615700
06-17 23:42:28.870   3941-4702     C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [][MarkProcessed:71] Ffrt PE: 615646 615700
06-17 23:42:28.886   3941-4018     C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [P:M:615702][OnPointerEvent:228] Last eventId:615652, current eventId:615702
06-17 23:42:28.902   3941-3941     C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, enter
06-17 23:42:28.902   3941-3941     C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, leave
06-17 23:42:28.956   3941-4018     C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [][OnPointerEvent:216] ac: move, first: 615697-(2025-06-17 23:42:28.851ms), 615704, count: 8, last: ac: up: 615705
06-17 23:42:28.957   3941-4018     C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [P:U:615705][OnPointerEvent:541] id:615705 recv
06-17 23:42:28.957   3941-4018     C04213/com.exam...InputKeyFlow  com.example.nexushub  I     [] OnInputEvent(86): eid:69,InputId:615705,wid:298,ac:4
06-17 23:42:28.959   3941-4018     C04213/com.exam...InputKeyFlow  com.example.nexushub  I     [] ConsumePointerEventInner(872): InputId:615705,wid:298,pointId:0,srcType:2,rect:[0,0,1224,2776],notify:1
06-17 23:42:28.960   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] InputTracking id:615705, fingerId:0, type=1, inject=0, isPrivacyMode=0
06-17 23:42:28.960   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:615705, exclusive 0 type: 1
06-17 23:42:28.960   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:615705, parallel 0 type: 1
06-17 23:42:28.960   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:615705, sequenced 0 type: 1
06-17 23:42:28.960   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:615705, LongPress 0 up, state: 1
06-17 23:42:28.960   3941-3941     C03905/com.exa...ushub/AceDrag  com.example.nexushub  I     [(100000:100000:scope)] Trigger pan onReject
06-17 23:42:28.960   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:615705, LongPress 0 up, state: 1
06-17 23:42:28.960   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:615705, click 0 up, state: 0
06-17 23:42:28.960   3941-3941     C0391E/com.exa...ub/AceGesture  com.example.nexushub  I     [(100000:100000:scope)] Click try accept
06-17 23:42:28.960   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Click accepted, tag: Column
06-17 23:42:28.961   3941-3941     C03900/com.exa....nexushub/Ace  com.example.nexushub  W     [(100000:100000:scope)] [Engine Log] can't find this page pages/FeaturedCollectionDetailPage path
06-17 23:42:28.961   3941-3941     C03925/com.exa...hub/AceRouter  com.example.nexushub  W     [(100000:100000:scope)] empty path found in StartPush with url: pages/FeaturedCollectionDetailPage
06-17 23:42:28.962   3941-3941     A00000/com.exam...FeaturedPage  com.example.nexushub  E     导航到精选集详情页失败: {"code":"100002"}
06-17 23:42:28.962   3941-3941     C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] id: 0, log: {types: Sequenced, node: Image, prcd: Down, state: READY, prcd: Up, state: FAIL}{types: LongPress, node: Image, prcd: Down, state: DETECTING, prcd: Up, state: FAIL}{types: Pan, node: Image}{types: LongPress, node: Image, prcd: Down, state: DETECTING, prcd: Up, state: FAIL}{types: Click, node: Column, prcd: Down, state: READY, extraInfo: ETF: 0 CFP: 0, prcd: Up, state: SUCCEED, extraInfo: ETF: 0 CFP: 0}
06-17 23:42:28.962   3941-3941     C03919/com.exa...InputTracking  com.example.nexushub  I     [(100000:100000:scope)] Consumed new event id=615705 in ace_container, lastEventInfo: id:615704
06-17 23:42:29.058   3941-3986     C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     RSUIDirector::ProcessMessages messageId:59, cmdCount:2, instanceId:100000
06-17 23:42:29.059   3941-3941     C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     RSUIDirector::PostTask messageId:59, cmdCount:2, instanceId:100000
06-17 23:42:29.766   3941-3941     C02504/com.exa...hub/thp_extra  com.example.nexushub  I     ThpExtraRunCommand[122]ver:5.0.7ThpExtraRunCommand, cmd:THP_UpdateViewsLocation, param:thp#Location#
06-17 23:42:29.766   3941-3941     C02504/com.exa...hub/thp_extra  com.example.nexushub  I     InitTpInterfaces[33]InitTpInterfaces+
06-17 23:42:29.766   3941-3941     C02504/com.exa...hub/thp_extra  com.example.nexushub  E     InitTpInterfaces[35]inited +
06-17 23:42:31.179   3941-3986     C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     RSUIDirector::ProcessMessages messageId:60, cmdCount:2, instanceId:100000
06-17 23:42:31.180   3941-3941     C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     RSUIDirector::PostTask messageId:60, cmdCount:2, instanceId:100000
06-17 23:42:31.180   3941-3941     C0390D/com.exa...ub/AceOverlay  com.example.nexushub  I     [(100000:100000:scope)] toast remove from root