06-17 23:07:21.488   53825-53825   C03915/com.exa...hub/AceSwiper  com.example.nexushub  I     [(100000:100000:scope)] Swiper ShowNext, id:651
06-17 23:07:21.492   53825-53825   C03915/com.exa...hub/AceSwiper  com.example.nexushub  I     [(100000:100000:scope)] Swiper start property animation with offsetX: -1224.000000, offsetY: 0.000000
06-17 23:07:21.500   53825-53825   C03915/com.exa...hub/AceSwiper  com.example.nexushub  I     [(100000:100000:scope)] FireAnimationStartEvent, index: 2, targetIndex: 0, id:651
06-17 23:07:21.574   53825-55705   C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [][OnPointerEvent:216] ac: down: 614435
06-17 23:07:21.575   53825-55705   C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [P:D:614435][OnPointerEvent:541] id:614435 recv
06-17 23:07:21.575   53825-55705   C04213/com.exam...InputKeyFlow  com.example.nexushub  I     [] OnInputEvent(86): eid:24,InputId:614435,wid:297,ac:2
06-17 23:07:21.576   53825-55705   C04219/com.exa...shub/WMSDecor  com.example.nexushub  W     [] GetDecorHeight(2235): Get app window decor height failed
06-17 23:07:21.577   53825-55705   C03919/com.exa...InputTracking  com.example.nexushub  I     [(-1:100000:singleton)] pointdown windowId: 297
06-17 23:07:21.577   53825-55705   C04213/com.exam...InputKeyFlow  com.example.nexushub  I     [] ConsumePointerEventInner(872): InputId:614435,wid:297,pointId:0,srcType:2,rect:[0,0,1224,2776],notify:1
06-17 23:07:21.577   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] InputTracking id:614435, fingerId:0, type=0, inject=0, isPrivacyMode=0
06-17 23:07:21.578   53825-55705   C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [][OnPointerEvent:216] ac: move: 614436
06-17 23:07:21.578   53825-53825   C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, enter
06-17 23:07:21.578   53825-53825   C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, leave
06-17 23:07:21.578   53825-53825   C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, enter
06-17 23:07:21.578   53825-53825   C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, leave
06-17 23:07:21.579   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] InputTracking id:614435, touch test hitted node info: fingerId: 0{ tag: Refresh, depth: 13 };{ tag: Scroll, depth: 14 };{ tag: Image, depth: 26 };
06-17 23:07:21.579   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] InputTracking id:614435, touch test hitted recognizer type info: recognizer type ClickRecognizer node info: { tag: Scroll }; { tag: Row };recognizer type LongPressRecognizer node info: { tag: Image }; { tag: Image };recognizer type PanRecognizer node info: { tag: Image }; { tag: Scroll }; { tag: Refresh };
06-17 23:07:21.579   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614435, exclusive 0 type: 0
06-17 23:07:21.579   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614435, exclusive 0 type: 0
06-17 23:07:21.579   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614435, click 0 down, ETF: 0, CTP: 0, state: 0
06-17 23:07:21.579   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614435, exclusive 0 type: 0
06-17 23:07:21.579   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614435, parallel 0 type: 0
06-17 23:07:21.579   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614435, sequenced 0 type: 0
06-17 23:07:21.579   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614435, LongPress 0 down, state: 0
06-17 23:07:21.579   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614435, LongPress 0 down, state: 0
06-17 23:07:21.579   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614435, click 0 down, ETF: 0, CTP: 0, state: 0
06-17 23:07:21.579   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614435, pan 0 down, state: 0
06-17 23:07:21.579   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614435, pan 0 down, state: 0
06-17 23:07:21.579   53825-53825   C03919/com.exa...InputTracking  com.example.nexushub  I     [(100000:100000:scope)] Consumed new event id=614435 in ace_container, lastEventInfo: id:614402
06-17 23:07:21.630   53825-53825   C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, enter
06-17 23:07:21.630   53825-53825   C02220/com.exam...TunnelClient  com.example.nexushub  I     in Connect, leave
06-17 23:07:21.697   53825-55705   C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [][OnPointerEvent:216] ac: move, first: 614437-(2025-06-17 23:07:21.582ms), 614452, count: 16, last: ac: up: 614453
06-17 23:07:21.697   53825-55705   C02805/com.exam...InputKeyFlow  com.example.nexushub  I     [P:U:614453][OnPointerEvent:541] id:614453 recv
06-17 23:07:21.697   53825-55705   C04213/com.exam...InputKeyFlow  com.example.nexushub  I     [] OnInputEvent(86): eid:25,InputId:614453,wid:297,ac:4
06-17 23:07:21.698   53825-55705   C04213/com.exam...InputKeyFlow  com.example.nexushub  I     [] ConsumePointerEventInner(872): InputId:614453,wid:297,pointId:0,srcType:2,rect:[0,0,1224,2776],notify:1
06-17 23:07:21.698   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] InputTracking id:614453, fingerId:0, type=1, inject=0, isPrivacyMode=0
06-17 23:07:21.698   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614453, exclusive 0 type: 1
06-17 23:07:21.698   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614453, exclusive 0 type: 1
06-17 23:07:21.698   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614453, click 0 up, state: 0
06-17 23:07:21.698   53825-53825   C0391E/com.exa...ub/AceGesture  com.example.nexushub  I     [(100000:100000:scope)] Click try accept
06-17 23:07:21.698   53825-53825   C0390B/com.exa...AceScrollable  com.example.nexushub  I     [(100000:100000:scope)] Scrollable GestureJudge:0, 0
06-17 23:07:21.698   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614453, exclusive 0 type: 1
06-17 23:07:21.698   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614453, parallel 0 type: 1
06-17 23:07:21.698   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614453, sequenced 0 type: 1
06-17 23:07:21.698   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614453, LongPress 0 up, state: 1
06-17 23:07:21.698   53825-53825   C03905/com.exa...ushub/AceDrag  com.example.nexushub  I     [(100000:100000:scope)] Trigger pan onReject
06-17 23:07:21.698   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614453, LongPress 0 up, state: 1
06-17 23:07:21.698   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614453, click 0 up, state: 0
06-17 23:07:21.698   53825-53825   C0391E/com.exa...ub/AceGesture  com.example.nexushub  I     [(100000:100000:scope)] Click try accept
06-17 23:07:21.698   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Click accepted, tag: Row
06-17 23:07:21.699   53825-53825   C03925/com.exa...hub/AceRouter  com.example.nexushub  I     [(100000:100000:scope)] Page router manager is creating page[4]: url: pages/AppDetailPage path: pages/AppDetailPage.js, recoverable: yes, namedRouter: no
06-17 23:07:21.699   53825-53825   C03F00/com.exa...b/ArkCompiler  com.example.nexushub  W     [debugger] DebuggerImpl::NotifyScriptParsed: Script already been parsed: url: entry|entry|1.0.0|src/main/ets/pages/AppDetailPage.ts fileName: /data/storage/el1/bundle/entry/ets/modules.abc
06-17 23:07:21.699   53825-53825   C03900/com.exa....nexushub/Ace  com.example.nexushub  I     [(100000:100000:scope)] build ohmUrl for forward compatibility
06-17 23:07:21.699   53825-53825   C03925/com.exa...hub/AceRouter  com.example.nexushub  I     [(100000:100000:scope)] add named router record, name: , bundleName: com.example.nexushub, moduleName: entry, pagePath: pages/AppDetailPage, pageFullPath: entry/src/main/ets/pages/AppDetailPage, ohmUrl: @bundle:com.example.nexushub/entry/ets/pages/AppDetailPage
06-17 23:07:21.700   53825-53825   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     Built URL: http://***********:8080/api/v1/apps/22/reviews
06-17 23:07:21.700   53825-53825   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     GET Request - URL: http://***********:8080/api/v1/apps/22/reviews?page=1&page_size=10
06-17 23:07:21.700   53825-53825   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     GET Request - Headers: {"Content-Type":"application/json","Accept":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoibmV4dXNodWIiLCJzdWIiOiIxIiwiZXhwIjoxNzUwMjU3OTYzLCJuYmYiOjE3NTAxNzE1NjMsImlhdCI6MTc1MDE3MTU2M30.MZf3mWQ8wdOLWtUdoqDk2WAlkFJYaAZJ1xf2EVAlpy0"}
06-17 23:07:21.706   53825-53825   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     Built URL: http://***********:8080/api/v1/public/apps/22
06-17 23:07:21.706   53825-53825   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     GET Request - URL: http://***********:8080/api/v1/public/apps/22
06-17 23:07:21.706   53825-53825   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     GET Request - Headers: {"Content-Type":"application/json","Accept":"application/json","Authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiaXNzIjoibmV4dXNodWIiLCJzdWIiOiIxIiwiZXhwIjoxNzUwMjU3OTYzLCJuYmYiOjE3NTAxNzE1NjMsImlhdCI6MTc1MDE3MTU2M30.MZf3mWQ8wdOLWtUdoqDk2WAlkFJYaAZJ1xf2EVAlpy0"}
06-17 23:07:21.711   53825-53825   C0391C/com.exa...shub/AceFocus  com.example.nexushub  I     [(100000:100000:scope)] Focus view: page/2 hide
06-17 23:07:21.711   53825-53825   C0391C/com.exa...shub/AceFocus  com.example.nexushub  I     [(100000:100000:scope)] Focus view: page/2 lost focus
06-17 23:07:21.711   53825-53825   C03922/com.exa...AceNavigation  com.example.nexushub  I     [(100000:100000:scope)] can't find inner navigation
06-17 23:07:21.711   53825-53825   C0391C/com.exa...shub/AceFocus  com.example.nexushub  I     [(100000:100000:scope)] Focus view: page/874 show
06-17 23:07:21.711   53825-53825   C03922/com.exa...AceNavigation  com.example.nexushub  I     [(100000:100000:scope)] can't find inner navigation
06-17 23:07:21.711   53825-53825   C03933/com.exa...b/AceKeyboard  com.example.nexushub  I     [(100000:100000:scope)] StageManager FrameNode notNeedSoftKeyboard.
06-17 23:07:21.711   53825-53825   C03933/com.exa...b/AceKeyboard  com.example.nexushub  I     [(100000:100000:scope)] Container not ScenceBoardWindow.
06-17 23:07:21.711   53825-53825   C03933/com.exa...b/AceKeyboard  com.example.nexushub  I     [(100000:100000:scope)] Ime Not Shown, Ime Not Attached, No need to close keyboard
06-17 23:07:21.711   53825-53825   C03925/com.exa...hub/AceRouter  com.example.nexushub  I     [(100000:100000:scope)] LoadPage Success
06-17 23:07:21.711   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614453, pan 0 up, state: 6
06-17 23:07:21.711   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] Id:614453, pan 0 up, state: 6
06-17 23:07:21.711   53825-53825   C03951/com.exam...InputKeyFlow  com.example.nexushub  I     [(100000:100000:scope)] id: 0, log: {types: Click, node: Scroll, prcd: Down, state: READY, extraInfo: ETF: 0 CFP: 0, prcd: Up, state: FAIL, extraInfo: ETF: 0 CFP: 0}{types: Sequenced, node: Image, prcd: Down, state: READY, prcd: Up, state: FAIL}{types: LongPress, node: Image, prcd: Down, state: DETECTING, prcd: Up, state: FAIL}{types: Pan, node: Image}{types: LongPress, node: Image, prcd: Down, state: DETECTING, prcd: Up, state: FAIL}{types: Click, node: Row, prcd: Down, state: READY, extraInfo: ETF: 0 CFP: 0, prcd: Up, state: SUCCEED, extraInfo: ETF: 0 CFP: 0}{types: Pan, node: Scroll, prcd: Down, state: DETECTING, prcd: Up, state: FAIL, extraInfo: currentFingers: 1 fingers: 1}{types: Pan, node: Refresh, prcd: Down, state: DETECTING, prcd: Up, state: FAIL, extraInfo: currentFingers: 1 fingers: 1}
06-17 23:07:21.711   53825-53825   C03919/com.exa...InputTracking  com.example.nexushub  I     [(100000:100000:scope)] Consumed new event id=614453 in ace_container, lastEventInfo: id:614452
06-17 23:07:21.712   53825-53825   C03930/com.exam...AceAnimation  com.example.nexushub  I     [(100000:100000:scope)] start pageTransition, from node 2 to 874
06-17 23:07:21.765   53825-55711   C015B0/com.exa...shub/NETSTACK  com.example.nexushub  I     [http_exec.cpp:418] taskid=-2147483635, size:877, dns:0.135, connect:0.000, tls:0.000, firstSend:0.449, firstRecv:54.768, total:55.417, redirect:0.000, errCode:0, RespCode:200, httpVer:2, method:GET, osErr:0
06-17 23:07:21.765   53825-55711   C015B0/com.exa...shub/NETSTACK  com.example.nexushub  I     [http_exec.cpp:418] taskid=-2147483636, size:81, dns:0.098, connect:0.000, tls:0.000, firstSend:0.220, firstRecv:58.663, total:58.998, redirect:0.000, errCode:0, RespCode:200, httpVer:2, method:GET, osErr:0
06-17 23:07:21.766   53825-53825   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     GET Response - Code: 200, Result: "{\"code\":200,\"message\":\"操作成功\",\"data\":{\"app\":{\"id\":22,\"name\":\"英语学习助手\",\"package\":\"com.example.english\",\"description\":\"专业的英语学习应用，包含词汇、语法、听力练习\",\"short_desc\":\"英语学习工具\",\"icon\":\"https://example.com/english.png\",\"banner_image\":\"\",\"category\":\"教育\",\"developer_id\":1,\"developer_name\":\"\",\"company_name\":\"\",\"website\":\"\",\"current_version\":\"3.0.1\",\"package_url\":\"\",\"status\":\"approved\",\"release_date\":\"0001-01-01T00:00:00Z\",\"size\":25165824,\"download_count\":15600,\"average_rating\":4.6,\"rating_count\":800,\"min_open_harmony_os_ver\":\"\",\"tags\":\"教育,英语,学习\",\"website_url\":\"\",\"privacy_url\":\"\",\"is_verified\":false,\"is_featured\":false,\"is_editor\":true,\"is_top\":false,\"reject_reason\":\"\",\"created_at\":\"2025-06-12T16:53:08.831944+08:00\",\"updated_at\":\"2025-06-12T16:53:08.831944+08:00\"},\"screenshots\":[],\"versions\":[]}}"
06-17 23:07:21.766   53825-53825   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     HTTP Response - Code: 200, URL: unknown
06-17 23:07:21.766   53825-53825   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     GET Response - Code: 200, Result: "{\"code\":200,\"message\":\"操作成功\",\"data\":[],\"total\":0,\"page\":1,\"page_size\":10}"
06-17 23:07:21.766   53825-53825   A00000/com.exa...ub/HttpClient  com.example.nexushub  I     HTTP Response - Code: 200, URL: unknown
06-17 23:07:21.766   53825-53825   A00000/com.exa...AppDetailPage  com.example.nexushub  E     加载评论失败: {}
06-17 23:07:21.774   53825-56067   C0391F/com.exa...shub/AceImage  com.example.nexushub  W     [(100000:100000:scope)] Image source type not supported! srcType: -1, src: <private>. nodeId = 894-890
06-17 23:07:21.776   53825-56067   C0391F/com.exa...shub/AceImage  com.example.nexushub  W     [(100000:100000:scope)] Failed to create image loader, Image source type not supported. src = <private>, nodeId = 894-890.
06-17 23:07:21.778   53825-53825   C01408/com.exa...nexushub/Text  com.example.nexushub  W     generatePaintRegion: Call generatePaintRegion when paragraph is not formatted
06-17 23:07:21.779   53825-53825   C01408/com.exa...nexushub/Text  com.example.nexushub  W     generatePaintRegion: Call generatePaintRegion when paragraph is not formatted
06-17 23:07:21.779   53825-53825   C01408/com.exa...nexushub/Text  com.example.nexushub  W     generatePaintRegion: Call generatePaintRegion when paragraph is not formatted
06-17 23:07:21.779   53825-53825   C01408/com.exa...nexushub/Text  com.example.nexushub  W     generatePaintRegion: Call generatePaintRegion when paragraph is not formatted
06-17 23:07:21.779   53825-53825   C01408/com.exa...nexushub/Text  com.example.nexushub  W     generatePaintRegion: Call generatePaintRegion when paragraph is not formatted
06-17 23:07:21.781   53825-53825   C0391F/com.exa...shub/AceImage  com.example.nexushub  W     [(100000:100000:scope)] Image LoadFail, source = <private>, reason: data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMDAgNzBMMTMwIDEwMEg3MEwxMDAgNzBaIiBmaWxsPSIjQ0NDQ0NDIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNDAiIHN0cm9rZT0iI0NDQ0NDQyIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LXNpemU9IjEyIj7lm77niYc8L3RleHQ+Cjwvc3ZnPgo=Failed to create image loader, Image source type not supported
06-17 23:07:21.805   53825-53825   C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     RSUIDirector::ProcessMessages messageId:32, cmdCount:2, instanceId:100000
06-17 23:07:21.806   53825-53825   C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     RSUIDirector::PostTask messageId:32, cmdCount:2, instanceId:100000
06-17 23:07:21.846   53825-55723   C015B0/com.exa...shub/NETSTACK  com.example.nexushub  E     [epoll_multi_driver.cpp:75] epoll wait event 0 err: 0
06-17 23:07:21.905   53825-53825   C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     RSUIDirector::ProcessMessages messageId:33, cmdCount:2, instanceId:100000
06-17 23:07:21.905   53825-53825   C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     RSUIDirector::PostTask messageId:33, cmdCount:2, instanceId:100000
06-17 23:07:21.907   53825-55708   C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     RSUIDirector::ProcessMessages messageId:34, cmdCount:2, instanceId:100000
06-17 23:07:21.908   53825-53825   C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     RSUIDirector::PostTask messageId:34, cmdCount:2, instanceId:100000
06-17 23:07:21.908   53825-53825   C03915/com.exa...hub/AceSwiper  com.example.nexushub  I     [(100000:100000:scope)] Swiper finish property animation with offsetX: -1224.000000, offsetY: 0.000000 isVerifiedSuc 1
06-17 23:07:21.909   53825-53825   C03915/com.exa...hub/AceSwiper  com.example.nexushub  I     [(100000:100000:scope)] FireAnimationEndEvent index: 0, currentOffset: has_value 1, value 0.000000vp, isForce: 0, aniStartCalledCount 1, id:651
06-17 23:07:22.056   53825-55708   C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     RSUIDirector::ProcessMessages messageId:35, cmdCount:1, instanceId:100000
06-17 23:07:22.057   53825-53825   C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     RSUIDirector::PostTask messageId:35, cmdCount:1, instanceId:100000
06-17 23:07:22.110   53825-55708   C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     RSUIDirector::ProcessMessages messageId:36, cmdCount:4, instanceId:100000
06-17 23:07:22.110   53825-53825   C01406/com.exa...shub/OHOS::RS  com.example.nexushub  I     RSUIDirector::PostTask messageId:36, cmdCount:4, instanceId:100000
06-17 23:07:22.111   53825-53825   C03930/com.exam...AceAnimation  com.example.nexushub  I     [(100000:100000:scope)] pageTransition in finish, nodeId:874
06-17 23:07:22.111   53825-53825   C0391C/com.exa...shub/AceFocus  com.example.nexushub  I     [(100000:100000:scope)] Focus view: page/874 show
06-17 23:07:22.112   53825-53825   C03930/com.exam...AceAnimation  com.example.nexushub  I     [(100000:100000:scope)] pageTransition exit finish, nodeId:2
06-17 23:07:22.112   53825-53825   C0391C/com.exa...shub/AceFocus  com.example.nexushub  I     [(100000:100000:scope)] Focus view: page/2 hide
06-17 23:07:22.112   53825-53825   C0391C/com.exa...shub/AceFocus  com.example.nexushub  I     [(100000:100000:scope)] Focus view: page/2 lost focus
06-17 23:07:22.112   53825-53825   C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] visible change to 0
06-17 23:07:22.112   53825-53825   C03914/com.exam...AceTextField  com.example.nexushub  I     [(100000:100000:scope)] visible change to 0
06-17 23:07:22.114   53825-53825   C0391C/com.exa...shub/AceFocus  com.example.nexushub  I     [(100000:100000:scope)] Request focus on focus view: page/874.
06-17 23:07:22.114   53825-53825   C0391C/com.exa...shub/AceFocus  com.example.nexushub  I     [(100000:100000:scope)] Focus view has no default focus.
06-17 23:07:22.114   53825-53825   C0391C/com.exa...shub/AceFocus  com.example.nexushub  I     [(100000:100000:scope)] FocusSwitch end, Column/secure_field onBlur, Column/secure_field onFocus, start: 2, end: 1, update: 2
06-17 23:07:22.114   53825-53825   C03933/com.exa...b/AceKeyboard  com.example.nexushub  I     [(100000:100000:scope)] current focus node info : (Column/877).
06-17 23:07:22.114   53825-53825   C03933/com.exa...b/AceKeyboard  com.example.nexushub  I     [(100000:100000:scope)] FrameNode(Column/877) notNeedSoftKeyboard.
06-17 23:07:22.114   53825-53825   C03933/com.exa...b/AceKeyboard  com.example.nexushub  I     [(100000:100000:scope)] Ime Not Shown, Ime Not Attached, No need to close keyboard
06-17 23:07:22.114   53825-53825   C0391C/com.exa...shub/AceFocus  com.example.nexushub  I     [(100000:100000:scope)] Request focus on view root scope: Column/877 return: 1.
06-17 23:07:22.193   53825-56041   C01719/com.exa...nexushub/ffrt  com.example.nexushub  W     20:~WorkerThread:72 to exit, qos[0]
06-17 23:07:22.193   53825-56039   C01719/com.exa...nexushub/ffrt  com.example.nexushub  W     21:~WorkerThread:72 to exit, qos[0]
06-17 23:07:22.194   53825-56040   C01719/com.exa...nexushub/ffrt  com.example.nexushub  W     22:~WorkerThread:72 to exit, qos[0]
06-17 23:07:22.504   53825-53825   C02504/com.exa...hub/thp_extra  com.example.nexushub  I     ThpExtraRunCommand[122]ver:5.0.7ThpExtraRunCommand, cmd:THP_UpdateViewsLocation, param:thp#Location#994,1840,1184,1895#1088,184,1170,301#54,195,136,290
06-17 23:07:22.504   53825-53825   C02504/com.exa...hub/thp_extra  com.example.nexushub  I     InitTpInterfaces[33]InitTpInterfaces+
06-17 23:07:22.504   53825-53825   C02504/com.exa...hub/thp_extra  com.example.nexushub  E     InitTpInterfaces[35]inited +
06-17 23:07:24.489   53825-53825   C03915/com.exa...hub/AceSwiper  com.example.nexushub  I     [(100000:100000:scope)] Swiper ShowNext, id:651