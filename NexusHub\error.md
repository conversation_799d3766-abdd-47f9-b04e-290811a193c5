> hvigor hvigor client: The argument passed to Node.js or hvigor version have changed and a new daemon will be created to replace the existing daemon. Reason:{isHvigorVersionChanged: false, isNodeEnvChanged: true}
> hvigor hvigor client: Starting hvigor daemon.
> hvigor Hvigor Daemon started in 928 ms
> hvigor UP-TO-DATE :entry:default@PreBuild...  
> hvigor Finished :entry:default@CreateModuleInfo... after 6 ms 
> hvigor UP-TO-DATE :entry:default@GenerateMetadata...  
> hvigor Finished :entry:default@ConfigureCmake... after 1 ms 
> hvigor UP-TO-DATE :entry:default@MergeProfile...  
> hvigor UP-TO-DATE :entry:default@CreateBuildProfile...
> hvigor Finished :entry:default@PreCheckSyscap... after 1 ms
> hvigor UP-TO-DATE :entry:default@GeneratePkgContextInfo...  
> hvigor Finished :entry:default@ProcessIntegratedHsp... after 3 ms 
> hvigor Finished :entry:default@BuildNativeWithCmake... after 1 ms 
> hvigor UP-TO-DATE :entry:default@MakePackInfo...
> hvigor UP-TO-DATE :entry:default@SyscapTransform...  
> hvigor UP-TO-DATE :entry:default@ProcessProfile...  
> hvigor UP-TO-DATE :entry:default@ProcessRouterMap...  
> hvigor Finished :entry:default@ProcessStartupConfig... after 9 ms 
> hvigor Finished :entry:default@BuildNativeWithNinja... after 3 ms 
> hvigor UP-TO-DATE :entry:default@ProcessResource...  
> hvigor UP-TO-DATE :entry:default@GenerateLoaderJson...  
> hvigor UP-TO-DATE :entry:default@ProcessLibs...  
> hvigor Finished :entry:default@CompileResource... after 287 ms 
> hvigor UP-TO-DATE :entry:default@DoNativeStrip...  
> hvigor Finished :entry:default@BuildJS... after 10 ms 
> hvigor UP-TO-DATE :entry:default@CacheNativeLibs...  
> hvigor ERROR: Failed :entry:default@CompileArkTS... 
> hvigor ERROR: ArkTS Compiler Error
1 WARN: ArkTS:WARN: For details about ArkTS syntax errors, see FAQs
1 ERROR: 10605099 ArkTS Compiler Error
Error Message: It is possible to spread only arrays or classes derived from arrays into the rest parameter or array literals (arkts-no-spread) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppDetailPage.ets:93:11


2 ERROR: 10505001 ArkTS Compiler Error
Error Message: Property 'average_rating' does not exist on type 'AppDetailModel'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppDetailPage.ets:234:53


3 ERROR: 10505001 ArkTS Compiler Error
Error Message: Property 'average_rating' does not exist on type 'AppDetailModel'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppDetailPage.ets:240:50


4 ERROR: 10505001 ArkTS Compiler Error
Error Message: Property 'current_version' does not exist on type 'AppDetailModel'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppDetailPage.ets:338:30


5 ERROR: 10505001 ArkTS Compiler Error
Error Message: Property 'category' does not exist on type 'AppDetailModel'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppDetailPage.ets:349:30


COMPILE RESULT:FAIL {ERROR:6 WARN:1}
> hvigor ERROR: BUILD FAILED in 18 s 803 ms