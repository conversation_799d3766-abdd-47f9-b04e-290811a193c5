"use strict";
import dayjs from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs";
import antdPlugin from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/antd-dayjs-webpack-plugin@1.0.6_dayjs@1.11.13/node_modules/antd-dayjs-webpack-plugin/src/antd-plugin.js";
import isSameOrBefore from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isSameOrBefore";
import isSameOrAfter from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isSameOrAfter";
import advancedFormat from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/advancedFormat";
import customParseFormat from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/customParseFormat";
import weekday from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekday";
import weekYear from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekYear";
import weekOfYear from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekOfYear";
import isMoment from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isMoment";
import localeData from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/localeData";
import localizedFormat from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/localizedFormat";
import duration from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/duration";
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(advancedFormat);
dayjs.extend(customParseFormat);
dayjs.extend(weekday);
dayjs.extend(weekYear);
dayjs.extend(weekOfYear);
dayjs.extend(isMoment);
dayjs.extend(localeData);
dayjs.extend(localizedFormat);
dayjs.extend(duration);
dayjs.extend(antdPlugin);
