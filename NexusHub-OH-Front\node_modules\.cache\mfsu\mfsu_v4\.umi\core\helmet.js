"use strict";
import React from "react";
import { HelmetProvider } from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/@umijs+renderer-react@4.4.1_5d37cdc93ae4c557f74cbc834d273583/node_modules/@umijs/renderer-react";
import { context } from "./helmetContext";
export const innerProvider = (container) => {
  return React.createElement(HelmetProvider, { context }, container);
};
