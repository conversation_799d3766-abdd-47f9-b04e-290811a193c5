/**
 * 精选集详情页面
 * 展示精选集中的应用列表
 */
import {
  FeaturedCollectionModel,
  FeaturedCollectionDetailResponse,
  FeaturedCollectionAppsResponse,
  FeaturedCollectionAppModel
} from '../models/FeaturedCollection';
import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { AppCard } from '../components/AppCard';
import { LoadingView, LoadMoreView } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { promptAction } from '@kit.ArkUI';
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 精选集详情页面组件
 */
@Entry
@Component
struct FeaturedCollectionDetailPage {
  @State collection: FeaturedCollectionModel | null = null;
  @State apps: FeaturedCollectionAppModel[] = [];
  @State isLoading: boolean = false;
  @State isLoadingMore: boolean = false;
  @State hasMore: boolean = true;
  @State currentPage: number = 1;
  @State pageSize: number = 20;
  @State errorMessage: string = '';
  @State showError: boolean = false;
  @State collectionId: number = 0;
  
  private apiService: ApiService = ApiService.getInstance();
  private deviceUtils: DeviceUtils = DeviceUtils.getInstance();
  
  /**
   * 页面即将出现时的回调
   */
  aboutToAppear() {
    // 获取路由参数
    const params = this.getUIContext().getRouter().getParams();
    if (params) {
      this.collectionId = (params as Record<string, Object>).collectionId as number;
      this.collection = (params as Record<string, Object>).collection as FeaturedCollectionModel;
    }
    
    if (this.collectionId > 0) {
      this.loadCollectionDetail();
      this.loadCollectionApps();
    }
  }
  
  /**
   * 加载精选集详情
   */
  private async loadCollectionDetail() {
    try {
      const response: FeaturedCollectionDetailResponse = await this.apiService.getFeaturedCollectionDetail(this.collectionId);
      
      if (response && response.code === 200 && response.data) {
        this.collection = response.data;
      }
    } catch (error) {
      hilog.error(0x0000, 'FeaturedCollectionDetailPage', '加载精选集详情失败: %{public}s', JSON.stringify(error));
    }
  }
  
  /**
   * 加载精选集应用列表
   */
  private async loadCollectionApps() {
    if (this.isLoading) return;
    
    this.isLoading = true;
    this.showError = false;
    this.currentPage = 1;
    this.hasMore = true;
    
    try {
      const response: FeaturedCollectionAppsResponse = await this.apiService.getFeaturedCollectionApps(
        this.collectionId,
        this.currentPage,
        this.pageSize
      );
      
      if (response && response.code === 200 && response.data) {
        this.apps = response.data.list || [];
        this.hasMore = response.data.pagination ?
          response.data.pagination.page < response.data.pagination.total_pages : false;
      } else {
        this.showError = true;
        this.errorMessage = response?.message || '加载失败';
      }
    } catch (error) {
      hilog.error(0x0000, 'FeaturedCollectionDetailPage', '加载精选集应用失败: %{public}s', JSON.stringify(error));
      this.showError = true;
      this.errorMessage = '网络连接失败，请检查网络设置';
    } finally {
      this.isLoading = false;
    }
  }
  
  /**
   * 加载更多应用
   */
  private async loadMoreApps() {
    if (this.isLoadingMore || !this.hasMore) return;
    
    this.isLoadingMore = true;
    
    try {
      const nextPage = this.currentPage + 1;
      const response: FeaturedCollectionAppsResponse = await this.apiService.getFeaturedCollectionApps(
        this.collectionId,
        nextPage,
        this.pageSize
      );
      
      if (response && response.code === 200 && response.data) {
        const newApps: FeaturedCollectionAppModel[] = response.data.list || [];
        this.apps = this.apps.concat(newApps);
        this.currentPage = nextPage;
        this.hasMore = response.data.pagination ?
          response.data.pagination.page < response.data.pagination.total_pages : false;
      } else {
        this.hasMore = false;
        this.getUIContext().getPromptAction().showToast({
          message: '加载失败',
          duration: 2000
        });
      }
    } catch (error) {
      hilog.error(0x0000, 'FeaturedCollectionDetailPage', '加载更多应用失败: %{public}s', JSON.stringify(error));
      this.hasMore = false;
      this.getUIContext().getPromptAction().showToast({
        message: '网络连接失败',
        duration: 2000
      });
    } finally {
      this.isLoadingMore = false;
    }
  }
  
  /**
   * 处理应用卡片点击
   */
  private handleAppClick(app: FeaturedCollectionAppModel) {
    this.getUIContext().getRouter().pushUrl({
      url: 'pages/AppDetailPage',
      params: {
        appId: app.id,
        app: app
      }
    }).catch((error: Error) => {
      hilog.error(0x0000, 'FeaturedCollectionDetailPage', '导航到应用详情页失败: %{public}s', JSON.stringify(error));
      this.getUIContext().getPromptAction().showToast({
        message: '页面跳转失败',
        duration: 2000
      });
    });
  }
  
  /**
   * 返回上一页
   */
  private goBack() {
    this.getUIContext().getRouter().back();
  }
  
  /**
   * 构建精选集头部信息
   */
  @Builder
  private buildCollectionHeader() {
    if (this.collection) {
      Column({ space: 16 }) {
        // 封面图片
        Image(this.collection.cover_image || Constants.PLACEHOLDER_IMAGE)
          .width('100%')
          .height(200)
          .objectFit(ImageFit.Cover)
          .borderRadius(Constants.BORDER_RADIUS.MEDIUM)

        // 标题和描述
        Column({ space: 8 }) {
          Text(this.collection.name)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.XLARGE))
            .fontWeight(FontWeight.Bold)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .maxLines(2)
            .textOverflow({ overflow: TextOverflow.Ellipsis })

          Text(this.collection.description)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM))
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
            .maxLines(3)
            .textOverflow({ overflow: TextOverflow.Ellipsis })

          Text(`共${this.collection.app_count}个应用`)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_TERTIARY)
        }
        .alignItems(HorizontalAlign.Start)
        .width('100%')
      }
      .padding(16)
      .backgroundColor(Constants.COLORS.WHITE)
      .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
      .margin({ left: 16, right: 16, bottom: 8 })
    }
  }
  
  /**
   * 构建应用卡片（适配精选集应用数据结构）
   */
  @Builder
  buildAppCard(app: FeaturedCollectionAppModel) {
    Row() {
      // 应用图标
      Image(app.icon || $r('app.media.app_icon'))
        .width(56)
        .height(56)
        .borderRadius(12)
        .objectFit(ImageFit.Cover)
      
      // 应用信息
      Column() {
        // 应用名称
        Text(app.name)
          .fontSize(Constants.FONT_SIZE.MEDIUM)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .fontWeight(FontWeight.Medium)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          .width('100%')
          .textAlign(TextAlign.Start)
        
        // 应用描述
        Text(app.short_description || app.description)
          .fontSize(Constants.FONT_SIZE.SMALL)
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
          .maxLines(2)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          .width('100%')
          .textAlign(TextAlign.Start)
          .margin({ top: 4 })
        
        // 评分和下载量
        Row() {
          // 评分
          Row() {
            Image($r('app.media.ic_star'))
              .width(12)
              .height(12)
              .fillColor(Constants.COLORS.RATING)
            
            Text((app.rating || 0).toFixed(1))
              .fontSize(Constants.FONT_SIZE.SMALL)
              .fontColor(Constants.COLORS.TEXT_SECONDARY)
              .margin({ left: 4 })
          }
          
          // 下载量
          Text(`${this.formatDownloadCount(app.download_count)}下载`)
            .fontSize(Constants.FONT_SIZE.SMALL)
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
            .margin({ left: 16 })
        }
        .width('100%')
        .margin({ top: 8 })
      }
      .layoutWeight(1)
      .margin({ left: 12 })
      .alignItems(HorizontalAlign.Start)
      
      // 下载按钮
      Button('下载')
        .fontSize(Constants.FONT_SIZE.SMALL)
        .fontColor(Constants.COLORS.WHITE)
        .backgroundColor(Constants.COLORS.PRIMARY)
        .borderRadius(16)
        .padding({ left: 16, right: 16, top: 6, bottom: 6 })
        .margin({ left: 12 })
    }
    .width('100%')
    .padding(16)
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(12)
    .shadow({
      radius: 4,
      color: '#0D000000',
      offsetX: 0,
      offsetY: 1
    })
    .onClick(() => this.handleAppClick(app))
  }
  
  /**
   * 格式化下载数量
   */
  private formatDownloadCount(count: number): string {
    if (count >= 10000) {
      return `${Math.floor(count / 10000)}万`;
    } else if (count >= 1000) {
      return `${Math.floor(count / 1000)}千`;
    } else {
      return count.toString();
    }
  }
  
  /**
   * 构建页面UI
   */
  build() {
    Stack() {
      Column() {
        // 顶部标题栏
        Row() {
          Button() {
            Image($r('app.media.ic_back'))
              .width(24)
              .height(24)
              .fillColor(Constants.COLORS.TEXT_PRIMARY)
          }
          .width(40)
          .height(40)
          .backgroundColor(Color.Transparent)
          .onClick(() => this.goBack())
          
          Text(this.collection?.name || '精选集详情')
            .fontSize(Constants.FONT_SIZE.LARGE)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Medium)
            .layoutWeight(1)
            .textAlign(TextAlign.Center)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
          
          // 占位，保持标题居中
          Row().width(40).height(40)
        }
        .width('100%')
        .height(56)
        .padding({ left: 16, right: 16 })
        .backgroundColor(Constants.COLORS.WHITE)
        
        // 内容区域
        if (this.isLoading) {
          LoadingView()
            .layoutWeight(1)
        } else if (this.showError) {
          Column() {
            Image($r('app.media.ic_error'))
              .width(64)
              .height(64)
              .fillColor(Constants.COLORS.TEXT_SECONDARY)
              .margin({ bottom: 16 })
            
            Text(this.errorMessage)
              .fontSize(Constants.FONT_SIZE.NORMAL)
              .fontColor(Constants.COLORS.TEXT_SECONDARY)
              .textAlign(TextAlign.Center)
              .margin({ bottom: 24 })
            
            Button('重试')
              .fontSize(Constants.FONT_SIZE.NORMAL)
              .fontColor(Constants.COLORS.PRIMARY)
              .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
              .borderRadius(8)
              .padding({ left: 24, right: 24, top: 8, bottom: 8 })
              .onClick(() => this.loadCollectionApps())
          }
          .layoutWeight(1)
          .justifyContent(FlexAlign.Center)
          .alignItems(HorizontalAlign.Center)
        } else {
          // 应用列表
          List() {
            // 精选集头部信息
            ListItem() {
              this.buildCollectionHeader()
            }
            
            // 应用列表
            ForEach(this.apps, (app: FeaturedCollectionAppModel, index: number) => {
              ListItem() {
                this.buildAppCard(app)
                  .margin({ left: 16, right: 16, bottom: 12 })
              }
            }, (app: FeaturedCollectionAppModel) => app.id.toString())
            
            // 加载更多组件
            if (this.hasMore || this.isLoadingMore) {
              ListItem() {
                LoadMoreView({
                  isLoading: this.isLoadingMore,
                  hasMore: this.hasMore,
                  onLoadMore: () => {
                    if (!this.isLoadingMore && this.hasMore) {
                      this.loadMoreApps();
                    }
                  }
                })
              }
              .padding({ top: 12, bottom: 12 })
            }
          }
          .layoutWeight(1)
          .scrollBar(BarState.Auto)
          .edgeEffect(EdgeEffect.Spring)
          .onReachEnd(() => {
            if (this.hasMore && !this.isLoadingMore) {
              this.loadMoreApps();
            }
          })
        }
      }
      .width('100%')
      .height('100%')
      .backgroundColor(Constants.COLORS.BACKGROUND)
    }
    .width('100%')
    .height('100%')
  }
}