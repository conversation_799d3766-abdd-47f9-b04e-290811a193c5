"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { PageContainer } from "@ant-design/pro-components";
import { Card, Table, Button, Space, Tag, message, Modal, Form, Input, Select } from "antd";
import { CheckOutlined, CloseOutlined, EyeOutlined, ReloadOutlined } from "@ant-design/icons";
import { useState } from "react";
import { useRequest } from "@umijs/max";
import { getPendingVersions, reviewAppVersion } from "@/services/version";
const { TextArea } = Input;
const { Option } = Select;
const VersionReview = () => {
  const [searchParams, setSearchParams] = useState({});
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [currentVersion, setCurrentVersion] = useState(null);
  const [form] = Form.useForm();
  const { data, loading, run } = useRequest(
    () => getPendingVersions({
      page: searchParams.page || 1,
      pageSize: searchParams.pageSize || 20
    }),
    {
      refreshDeps: [searchParams]
    }
  );
  const handleReview = async (version, action) => {
    if (action === "reject") {
      setCurrentVersion(version);
      setReviewModalVisible(true);
      form.setFieldsValue({ action: "reject" });
    } else {
      try {
        const reviewData = {
          status: "approved",
          reason: "\u7248\u672C\u5BA1\u6838\u901A\u8FC7"
        };
        await reviewAppVersion(version.id, reviewData);
        message.success("\u7248\u672C\u5BA1\u6838\u901A\u8FC7");
        run();
      } catch (error) {
        console.error("\u5BA1\u6838\u5931\u8D25:", error);
        message.error("\u5BA1\u6838\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
      }
    }
  };
  const handleReviewSubmit = async (values) => {
    if (!currentVersion) return;
    try {
      const reviewData = {
        status: values.action === "approve" ? "approved" : "rejected",
        reason: values.comment || (values.action === "approve" ? "\u7248\u672C\u5BA1\u6838\u901A\u8FC7" : "\u7248\u672C\u5BA1\u6838\u62D2\u7EDD")
      };
      await reviewAppVersion(currentVersion.id, reviewData);
      message.success(values.action === "approve" ? "\u7248\u672C\u5BA1\u6838\u901A\u8FC7" : "\u7248\u672C\u5DF2\u62D2\u7EDD");
      setReviewModalVisible(false);
      setCurrentVersion(null);
      form.resetFields();
      run();
    } catch (error) {
      console.error("\u5BA1\u6838\u5931\u8D25:", error);
      message.error("\u5BA1\u6838\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
    }
  };
  const handleViewDetail = (version) => {
    Modal.info({
      title: "\u7248\u672C\u8BE6\u60C5",
      width: 600,
      content: /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsx("strong", { children: "\u5E94\u7528\u540D\u79F0:" }),
          " ",
          version.appName
        ] }),
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsx("strong", { children: "\u7248\u672C\u53F7:" }),
          " ",
          version.versionName
        ] }),
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsx("strong", { children: "\u7248\u672C\u4EE3\u7801:" }),
          " ",
          version.versionCode
        ] }),
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsx("strong", { children: "\u5F00\u53D1\u8005:" }),
          " ",
          version.developerName
        ] }),
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsx("strong", { children: "\u6587\u4EF6\u5927\u5C0F:" }),
          " ",
          version.fileSize ? `${(version.fileSize / 1024 / 1024).toFixed(2)} MB` : "\u672A\u77E5"
        ] }),
        /* @__PURE__ */ jsx("p", { children: /* @__PURE__ */ jsx("strong", { children: "\u66F4\u65B0\u8BF4\u660E:" }) }),
        /* @__PURE__ */ jsx("div", { style: { background: "#f5f5f5", padding: "8px", borderRadius: "4px", maxHeight: "200px", overflow: "auto" }, children: version.updateDescription || "\u65E0\u66F4\u65B0\u8BF4\u660E" }),
        /* @__PURE__ */ jsxs("p", { children: [
          /* @__PURE__ */ jsx("strong", { children: "\u63D0\u4EA4\u65F6\u95F4:" }),
          " ",
          version.createdAt || version.created_at ? new Date(version.createdAt || version.created_at).toLocaleString() : "-"
        ] })
      ] })
    });
  };
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80
    },
    {
      title: "\u5E94\u7528\u540D\u79F0",
      dataIndex: "appName",
      key: "appName",
      width: 150,
      ellipsis: true
    },
    {
      title: "\u7248\u672C\u53F7",
      dataIndex: "versionName",
      key: "versionName",
      width: 100
    },
    {
      title: "\u7248\u672C\u4EE3\u7801",
      dataIndex: "versionCode",
      key: "versionCode",
      width: 100
    },
    {
      title: "\u5F00\u53D1\u8005",
      dataIndex: "developerName",
      key: "developerName",
      width: 120,
      ellipsis: true
    },
    {
      title: "\u6587\u4EF6\u5927\u5C0F",
      dataIndex: "fileSize",
      key: "fileSize",
      width: 100,
      render: (size) => size ? `${(size / 1024 / 1024).toFixed(2)} MB` : "\u672A\u77E5"
    },
    {
      title: "\u63D0\u4EA4\u65F6\u95F4",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      render: (time, record) => {
        const timeValue = time || record.created_at;
        return timeValue ? new Date(timeValue).toLocaleString() : "-";
      },
      sorter: true
    },
    {
      title: "\u72B6\u6001",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status) => {
        let color = "orange";
        let text = "\u5F85\u5BA1\u6838";
        if (status === "approved") {
          color = "green";
          text = "\u5DF2\u901A\u8FC7";
        } else if (status === "rejected") {
          color = "red";
          text = "\u5DF2\u62D2\u7EDD";
        } else if (status === "published") {
          color = "blue";
          text = "\u5DF2\u53D1\u5E03";
        }
        return /* @__PURE__ */ jsx(Tag, { color, children: text });
      }
    },
    {
      title: "\u64CD\u4F5C",
      key: "action",
      width: 200,
      render: (_, record) => /* @__PURE__ */ jsxs(Space, { size: "small", children: [
        /* @__PURE__ */ jsx(
          Button,
          {
            type: "link",
            icon: /* @__PURE__ */ jsx(EyeOutlined, {}),
            size: "small",
            onClick: () => handleViewDetail(record),
            children: "\u67E5\u770B"
          }
        ),
        record.status === "pending" && /* @__PURE__ */ jsxs(Fragment, { children: [
          /* @__PURE__ */ jsx(
            Button,
            {
              type: "primary",
              icon: /* @__PURE__ */ jsx(CheckOutlined, {}),
              size: "small",
              onClick: () => handleReview(record, "approve"),
              children: "\u901A\u8FC7"
            }
          ),
          /* @__PURE__ */ jsx(
            Button,
            {
              danger: true,
              icon: /* @__PURE__ */ jsx(CloseOutlined, {}),
              size: "small",
              onClick: () => handleReview(record, "reject"),
              children: "\u62D2\u7EDD"
            }
          )
        ] })
      ] })
    }
  ];
  return /* @__PURE__ */ jsxs(PageContainer, { children: [
    /* @__PURE__ */ jsxs(Card, { bordered: false, children: [
      /* @__PURE__ */ jsxs("div", { style: { marginBottom: 16, display: "flex", justifyContent: "space-between", alignItems: "center" }, children: [
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("span", { children: "\u7248\u672C\u5BA1\u6838\u7BA1\u7406" }),
          /* @__PURE__ */ jsxs(Tag, { color: "orange", style: { marginLeft: 8 }, children: [
            "\u5F85\u5BA1\u6838\u7248\u672C: ",
            data?.total || 0
          ] })
        ] }),
        /* @__PURE__ */ jsx(
          Button,
          {
            icon: /* @__PURE__ */ jsx(ReloadOutlined, {}),
            onClick: () => run(),
            children: "\u5237\u65B0"
          }
        )
      ] }),
      /* @__PURE__ */ jsx(
        Table,
        {
          columns,
          dataSource: data?.data,
          rowKey: "id",
          loading,
          scroll: { x: 1200 },
          pagination: {
            current: data?.page || 1,
            pageSize: data?.pageSize || 20,
            total: data?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `\u7B2C ${range[0]}-${range[1]} \u6761/\u5171 ${total} \u6761`,
            onChange: (page, pageSize) => {
              setSearchParams({
                ...searchParams,
                page,
                pageSize
              });
            }
          }
        }
      )
    ] }),
    /* @__PURE__ */ jsx(
      Modal,
      {
        title: "\u7248\u672C\u5BA1\u6838",
        open: reviewModalVisible,
        onCancel: () => {
          setReviewModalVisible(false);
          setCurrentVersion(null);
          form.resetFields();
        },
        footer: null,
        children: /* @__PURE__ */ jsxs(
          Form,
          {
            form,
            layout: "vertical",
            onFinish: handleReviewSubmit,
            children: [
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "action",
                  label: "\u5BA1\u6838\u64CD\u4F5C",
                  rules: [{ required: true, message: "\u8BF7\u9009\u62E9\u5BA1\u6838\u64CD\u4F5C" }],
                  children: /* @__PURE__ */ jsxs(Select, { placeholder: "\u8BF7\u9009\u62E9\u5BA1\u6838\u64CD\u4F5C", children: [
                    /* @__PURE__ */ jsx(Option, { value: "approve", children: "\u901A\u8FC7" }),
                    /* @__PURE__ */ jsx(Option, { value: "reject", children: "\u62D2\u7EDD" })
                  ] })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "comment",
                  label: "\u5BA1\u6838\u610F\u89C1",
                  rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u5BA1\u6838\u610F\u89C1" }],
                  children: /* @__PURE__ */ jsx(
                    TextArea,
                    {
                      rows: 4,
                      placeholder: "\u8BF7\u8F93\u5165\u5BA1\u6838\u610F\u89C1..."
                    }
                  )
                }
              ),
              /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsxs(Space, { children: [
                /* @__PURE__ */ jsx(Button, { type: "primary", htmlType: "submit", children: "\u63D0\u4EA4\u5BA1\u6838" }),
                /* @__PURE__ */ jsx(Button, { onClick: () => {
                  setReviewModalVisible(false);
                  setCurrentVersion(null);
                  form.resetFields();
                }, children: "\u53D6\u6D88" })
              ] }) })
            ]
          }
        )
      }
    )
  ] });
};
export default VersionReview;
