{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "9b03da75-11df-444b-8d14-1637cfe683a8", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148234541800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0496a3d-2bd0-4476-8a08-d327f67ab038", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148234704300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b373e95-9d8d-4c64-ac64-82678731c93c", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148269174000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb71d84f-4d8d-4350-a4a8-bca5b8caada0", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148269493500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d88a63bb-eae4-414f-aa29-b816a822da6c", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148271102100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e442647b-68e1-4e12-a2d7-710274a13a2e", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156148271410200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf9190c6-239c-4f66-8a9a-e8d36f6313b8", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158161289900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "423c0628-23c1-4376-b6e4-2045fc3662b6", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158172005000, "endTime": 156158438205500}, "additional": {"children": ["c16055a9-3c72-4e69-b85d-b6bbc9335f4e", "9faac573-712d-4c19-91dd-54fdf497748f", "a32f18c8-2547-4008-8978-44a41464e600", "2436e5a3-e549-488e-ab25-694f9bdb4b5e", "13b1e465-1d92-4d55-bf16-72b40e68c00b", "2ce09500-5eb4-432c-a5e0-c3955b62abb0", "f25cccff-cc92-44b4-859c-96976a255961"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "e82c58e7-9546-43fa-b8f2-15b0a99306db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c16055a9-3c72-4e69-b85d-b6bbc9335f4e", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158172006700, "endTime": 156158189214200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "423c0628-23c1-4376-b6e4-2045fc3662b6", "logId": "d2d4dd00-9ace-4006-811a-59ace61fb95c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9faac573-712d-4c19-91dd-54fdf497748f", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158189230900, "endTime": 156158436178900}, "additional": {"children": ["83fd8c82-d958-4114-bbe5-464d93a6180f", "ea2bfbdd-483b-43d6-beac-2221e177e509", "8c6cf3fd-2900-4528-b63a-1d6b41e191b8", "a9f6d3f2-18f8-4133-81b5-52fb2f898801", "e85ae5a9-f8bc-44df-8058-48c9257a153e", "92b2f989-b509-461e-8e65-3eb8d9a64297", "6428d88b-2783-44ed-b0c8-1bdc9cc053b2", "42943464-c1c5-4674-9da2-1d5f10271b0a", "a97fd5fe-f02a-4ecf-90f0-3036c493ec1e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "423c0628-23c1-4376-b6e4-2045fc3662b6", "logId": "05377c25-9a21-4bfd-9675-4644cb2c060e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a32f18c8-2547-4008-8978-44a41464e600", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158436214700, "endTime": 156158438191700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "423c0628-23c1-4376-b6e4-2045fc3662b6", "logId": "ce7f1c40-ca43-464b-a552-ebfeee1f3701"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2436e5a3-e549-488e-ab25-694f9bdb4b5e", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158438197400, "endTime": 156158438199200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "423c0628-23c1-4376-b6e4-2045fc3662b6", "logId": "571bf5e0-a7fd-4c38-b865-5b1ad9b29a72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13b1e465-1d92-4d55-bf16-72b40e68c00b", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158177188400, "endTime": 156158177224400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "423c0628-23c1-4376-b6e4-2045fc3662b6", "logId": "0a40d865-48d1-4641-ab9c-5ccbb5ee32cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a40d865-48d1-4641-ab9c-5ccbb5ee32cd", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158177188400, "endTime": 156158177224400}, "additional": {"logType": "info", "children": [], "durationId": "13b1e465-1d92-4d55-bf16-72b40e68c00b", "parent": "e82c58e7-9546-43fa-b8f2-15b0a99306db"}}, {"head": {"id": "2ce09500-5eb4-432c-a5e0-c3955b62abb0", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158184246700, "endTime": 156158184269800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "423c0628-23c1-4376-b6e4-2045fc3662b6", "logId": "6b611140-7f8d-4b15-9288-28e152c9e380"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b611140-7f8d-4b15-9288-28e152c9e380", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158184246700, "endTime": 156158184269800}, "additional": {"logType": "info", "children": [], "durationId": "2ce09500-5eb4-432c-a5e0-c3955b62abb0", "parent": "e82c58e7-9546-43fa-b8f2-15b0a99306db"}}, {"head": {"id": "b9969370-dafe-49d5-8dd1-3d0fc4e58765", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158184335100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fec13bd-0375-472b-9774-96842d814b86", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158189052500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2d4dd00-9ace-4006-811a-59ace61fb95c", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158172006700, "endTime": 156158189214200}, "additional": {"logType": "info", "children": [], "durationId": "c16055a9-3c72-4e69-b85d-b6bbc9335f4e", "parent": "e82c58e7-9546-43fa-b8f2-15b0a99306db"}}, {"head": {"id": "83fd8c82-d958-4114-bbe5-464d93a6180f", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158197718000, "endTime": 156158197729700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9faac573-712d-4c19-91dd-54fdf497748f", "logId": "22596bed-96b1-4464-9fb4-7d9382c8e3e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea2bfbdd-483b-43d6-beac-2221e177e509", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158197754800, "endTime": 156158204230800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9faac573-712d-4c19-91dd-54fdf497748f", "logId": "7f310de0-83b6-4cc7-8380-25ef9a8f1515"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c6cf3fd-2900-4528-b63a-1d6b41e191b8", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158204245800, "endTime": 156158309054800}, "additional": {"children": ["527c6eaa-79cc-40f4-bd78-a38993df271e", "79315575-1238-4e23-9ce2-ecde350d496d", "52a32934-3721-46bd-b2ad-434650794812"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9faac573-712d-4c19-91dd-54fdf497748f", "logId": "8b95b467-552c-40fb-a582-1ff4ab150a1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9f6d3f2-18f8-4133-81b5-52fb2f898801", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158309064100, "endTime": 156158334600300}, "additional": {"children": ["6c380958-5a69-4f84-9ef1-cd99a314a839"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9faac573-712d-4c19-91dd-54fdf497748f", "logId": "ce944620-6e08-4529-8827-9130818d931e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e85ae5a9-f8bc-44df-8058-48c9257a153e", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158334622400, "endTime": 156158391640400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9faac573-712d-4c19-91dd-54fdf497748f", "logId": "2602082b-78b4-4400-a31c-a6e832683eca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92b2f989-b509-461e-8e65-3eb8d9a64297", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158393169600, "endTime": 156158412626400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9faac573-712d-4c19-91dd-54fdf497748f", "logId": "9bc17f04-afeb-4588-8b61-0b4165bc64a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6428d88b-2783-44ed-b0c8-1bdc9cc053b2", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158412705300, "endTime": 156158435963800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9faac573-712d-4c19-91dd-54fdf497748f", "logId": "72caba8e-ef69-4250-a83b-709db49eb677"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42943464-c1c5-4674-9da2-1d5f10271b0a", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158435986700, "endTime": 156158436163400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9faac573-712d-4c19-91dd-54fdf497748f", "logId": "068e2698-8578-4f3a-9c54-cc62d0403236"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22596bed-96b1-4464-9fb4-7d9382c8e3e7", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158197718000, "endTime": 156158197729700}, "additional": {"logType": "info", "children": [], "durationId": "83fd8c82-d958-4114-bbe5-464d93a6180f", "parent": "05377c25-9a21-4bfd-9675-4644cb2c060e"}}, {"head": {"id": "7f310de0-83b6-4cc7-8380-25ef9a8f1515", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158197754800, "endTime": 156158204230800}, "additional": {"logType": "info", "children": [], "durationId": "ea2bfbdd-483b-43d6-beac-2221e177e509", "parent": "05377c25-9a21-4bfd-9675-4644cb2c060e"}}, {"head": {"id": "527c6eaa-79cc-40f4-bd78-a38993df271e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158205263000, "endTime": 156158205285400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8c6cf3fd-2900-4528-b63a-1d6b41e191b8", "logId": "7f1072f2-477c-4d05-8e29-dcea7f810507"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f1072f2-477c-4d05-8e29-dcea7f810507", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158205263000, "endTime": 156158205285400}, "additional": {"logType": "info", "children": [], "durationId": "527c6eaa-79cc-40f4-bd78-a38993df271e", "parent": "8b95b467-552c-40fb-a582-1ff4ab150a1c"}}, {"head": {"id": "79315575-1238-4e23-9ce2-ecde350d496d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158208604900, "endTime": 156158308411400}, "additional": {"children": ["f9efa91e-6f4e-4c98-a7d5-65a37a00a282", "058ba56d-8ecc-483e-9c2b-c261a0980733"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8c6cf3fd-2900-4528-b63a-1d6b41e191b8", "logId": "82468961-a160-4bc3-b97a-072a66f80c05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9efa91e-6f4e-4c98-a7d5-65a37a00a282", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158208606400, "endTime": 156158214153300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "79315575-1238-4e23-9ce2-ecde350d496d", "logId": "38d47351-27f8-41af-8344-741decf65641"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "058ba56d-8ecc-483e-9c2b-c261a0980733", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158214178300, "endTime": 156158308400600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "79315575-1238-4e23-9ce2-ecde350d496d", "logId": "f1e2fe5b-151d-4604-8c13-31a2b635c164"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7e736c9-f957-4a56-889e-8df11725dc13", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158208610300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "497a5eb3-4512-4555-9d13-61220b1b801c", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158213933800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38d47351-27f8-41af-8344-741decf65641", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158208606400, "endTime": 156158214153300}, "additional": {"logType": "info", "children": [], "durationId": "f9efa91e-6f4e-4c98-a7d5-65a37a00a282", "parent": "82468961-a160-4bc3-b97a-072a66f80c05"}}, {"head": {"id": "000d80d0-6d25-4996-af1c-66be1ca4574b", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158214191100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef812f8a-5324-4e44-9b64-330fba613c2b", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158227850600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "558acf93-dc19-452c-aaec-e2e8eb0dfacf", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158228017800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7482d988-65ed-48eb-95e6-3026269d1496", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158228189600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb641d1c-b70b-4ca1-8acf-baf2cd1631eb", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158228345000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "976f0840-09e3-4d61-8f83-7535455967cf", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158231259500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b79f5009-630b-4e4b-83e1-f1443d07a2e8", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158248920900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38f8c9c9-832e-4579-9c44-79cfc0c5df44", "name": "Sdk init in 32 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158270228500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f8e6a0c-f937-452e-a243-51d746e9dcc8", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158270549400}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 19, "second": 48}, "markType": "other"}}, {"head": {"id": "4384b0e3-2eb5-4565-b035-a8b160accfe6", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158270567900}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 19, "second": 48}, "markType": "other"}}, {"head": {"id": "615a9d5c-697c-4df2-bbda-db2fe0c9666b", "name": "Project task initialization takes 35 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158307668200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f2f4884-db15-48ac-8a97-937df52e5f81", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158307825700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00dfb2da-04fc-4574-9f77-0d4ce16afe5f", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158308184500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8addf926-cc0e-46d5-bc37-7095c29a1936", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158308319700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1e2fe5b-151d-4604-8c13-31a2b635c164", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158214178300, "endTime": 156158308400600}, "additional": {"logType": "info", "children": [], "durationId": "058ba56d-8ecc-483e-9c2b-c261a0980733", "parent": "82468961-a160-4bc3-b97a-072a66f80c05"}}, {"head": {"id": "82468961-a160-4bc3-b97a-072a66f80c05", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158208604900, "endTime": 156158308411400}, "additional": {"logType": "info", "children": ["38d47351-27f8-41af-8344-741decf65641", "f1e2fe5b-151d-4604-8c13-31a2b635c164"], "durationId": "79315575-1238-4e23-9ce2-ecde350d496d", "parent": "8b95b467-552c-40fb-a582-1ff4ab150a1c"}}, {"head": {"id": "52a32934-3721-46bd-b2ad-434650794812", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158309032100, "endTime": 156158309046100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8c6cf3fd-2900-4528-b63a-1d6b41e191b8", "logId": "238d88cb-f814-4cbc-a850-62d12c21a8f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "238d88cb-f814-4cbc-a850-62d12c21a8f5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158309032100, "endTime": 156158309046100}, "additional": {"logType": "info", "children": [], "durationId": "52a32934-3721-46bd-b2ad-434650794812", "parent": "8b95b467-552c-40fb-a582-1ff4ab150a1c"}}, {"head": {"id": "8b95b467-552c-40fb-a582-1ff4ab150a1c", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158204245800, "endTime": 156158309054800}, "additional": {"logType": "info", "children": ["7f1072f2-477c-4d05-8e29-dcea7f810507", "82468961-a160-4bc3-b97a-072a66f80c05", "238d88cb-f814-4cbc-a850-62d12c21a8f5"], "durationId": "8c6cf3fd-2900-4528-b63a-1d6b41e191b8", "parent": "05377c25-9a21-4bfd-9675-4644cb2c060e"}}, {"head": {"id": "6c380958-5a69-4f84-9ef1-cd99a314a839", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158309564500, "endTime": 156158334587100}, "additional": {"children": ["e489cd10-e540-4863-b7bb-934a0c15d21d", "92310bba-7bfc-4472-bd29-822fd337cb0f", "80e6fa8d-944f-4c02-9284-465ac275357b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a9f6d3f2-18f8-4133-81b5-52fb2f898801", "logId": "e1086a0e-a9fc-43fc-8a20-41549c79b355"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e489cd10-e540-4863-b7bb-934a0c15d21d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158312371300, "endTime": 156158312387600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c380958-5a69-4f84-9ef1-cd99a314a839", "logId": "be055b7f-34a6-4a7e-b345-0cb7351fac11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be055b7f-34a6-4a7e-b345-0cb7351fac11", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158312371300, "endTime": 156158312387600}, "additional": {"logType": "info", "children": [], "durationId": "e489cd10-e540-4863-b7bb-934a0c15d21d", "parent": "e1086a0e-a9fc-43fc-8a20-41549c79b355"}}, {"head": {"id": "92310bba-7bfc-4472-bd29-822fd337cb0f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158314148000, "endTime": 156158332616500}, "additional": {"children": ["046edd36-5bf7-4055-8325-9e55907ed0d0", "5653ba72-702f-45b8-9918-5dc5912a7e07"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c380958-5a69-4f84-9ef1-cd99a314a839", "logId": "058dd0b7-3073-4809-8d7e-49e0c8718b9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "046edd36-5bf7-4055-8325-9e55907ed0d0", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158314148900, "endTime": 156158316531600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "92310bba-7bfc-4472-bd29-822fd337cb0f", "logId": "9e07db64-add3-44cf-bd09-c84bab044aa9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5653ba72-702f-45b8-9918-5dc5912a7e07", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158316542700, "endTime": 156158332602800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "92310bba-7bfc-4472-bd29-822fd337cb0f", "logId": "cd4a9718-6d66-4d98-9004-2664e59093ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2463eeed-4b0a-4b95-b125-98d8fb1a67db", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158314150900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ab1f08e-8fb6-4d4c-9463-a2d39aa5eed1", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158316433800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e07db64-add3-44cf-bd09-c84bab044aa9", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158314148900, "endTime": 156158316531600}, "additional": {"logType": "info", "children": [], "durationId": "046edd36-5bf7-4055-8325-9e55907ed0d0", "parent": "058dd0b7-3073-4809-8d7e-49e0c8718b9a"}}, {"head": {"id": "4dfbffac-4e4e-474f-80e6-7be0540b45e1", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158316548300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15c91756-0e9e-422c-b113-44269b4a8197", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158324960200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d2f7d46-f9d5-4751-9c6e-e67ac1fa2d17", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158325103900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70aa4830-99fd-483c-af66-c2e8b9c309dd", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158325337300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8739d7d-4659-4c4e-b004-875ddec6ae05", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158325512500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "700abf39-a135-424b-ab73-8a8f7a7551b3", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158325587900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09bea1c3-0bca-407c-9d09-76cd97b452af", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158325655000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8921d604-0003-4d13-8bac-8646f975303a", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158325734300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24e88e70-fe99-4231-a829-ecd40a2df866", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158325813100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71e4fc75-e335-43bd-bdd3-0843286e0194", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158326145600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24b80d81-7578-4a75-8db9-ec058131dc54", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158326315800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47996288-0c3a-4ae1-af37-6187dc59a71c", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158326405900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "159a2ec6-3aca-4b09-907b-27ab1b634df5", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158326472100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3613c63f-b95b-4782-890d-d60168c82c1f", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158326547800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffdb2faf-72bb-45da-a070-aaaed25f375e", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158326617900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1bd8ab9-5a87-4394-bcf0-31ef5b1f72bd", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158326771300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a65109d9-3f52-48e2-be90-4087f47039b5", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158326977600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ab851eb-f0c8-4451-8502-fa75edeca89d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158327047500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10743024-8fc7-4bbe-97ef-1a92c7f0e5e7", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158327107700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d2acecc-a01f-441c-b1fe-4ab7569df0c7", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158327176000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "313100d3-9bf9-468d-98f5-44e6da874a5b", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158332212300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e1aeb43-ed70-4553-aea3-b44d101f33bf", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158332385400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd02778f-1572-4fed-ba4d-ebbe9fc2a4b0", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158332458900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6970e6a8-d22f-4189-96e2-2464aa88641c", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158332541500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd4a9718-6d66-4d98-9004-2664e59093ac", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158316542700, "endTime": 156158332602800}, "additional": {"logType": "info", "children": [], "durationId": "5653ba72-702f-45b8-9918-5dc5912a7e07", "parent": "058dd0b7-3073-4809-8d7e-49e0c8718b9a"}}, {"head": {"id": "058dd0b7-3073-4809-8d7e-49e0c8718b9a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158314148000, "endTime": 156158332616500}, "additional": {"logType": "info", "children": ["9e07db64-add3-44cf-bd09-c84bab044aa9", "cd4a9718-6d66-4d98-9004-2664e59093ac"], "durationId": "92310bba-7bfc-4472-bd29-822fd337cb0f", "parent": "e1086a0e-a9fc-43fc-8a20-41549c79b355"}}, {"head": {"id": "80e6fa8d-944f-4c02-9284-465ac275357b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158334558800, "endTime": 156158334574400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c380958-5a69-4f84-9ef1-cd99a314a839", "logId": "ec01bbe9-4f7d-4d3a-9ea5-5c45da3f5373"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec01bbe9-4f7d-4d3a-9ea5-5c45da3f5373", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158334558800, "endTime": 156158334574400}, "additional": {"logType": "info", "children": [], "durationId": "80e6fa8d-944f-4c02-9284-465ac275357b", "parent": "e1086a0e-a9fc-43fc-8a20-41549c79b355"}}, {"head": {"id": "e1086a0e-a9fc-43fc-8a20-41549c79b355", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158309564500, "endTime": 156158334587100}, "additional": {"logType": "info", "children": ["be055b7f-34a6-4a7e-b345-0cb7351fac11", "058dd0b7-3073-4809-8d7e-49e0c8718b9a", "ec01bbe9-4f7d-4d3a-9ea5-5c45da3f5373"], "durationId": "6c380958-5a69-4f84-9ef1-cd99a314a839", "parent": "ce944620-6e08-4529-8827-9130818d931e"}}, {"head": {"id": "ce944620-6e08-4529-8827-9130818d931e", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158309064100, "endTime": 156158334600300}, "additional": {"logType": "info", "children": ["e1086a0e-a9fc-43fc-8a20-41549c79b355"], "durationId": "a9f6d3f2-18f8-4133-81b5-52fb2f898801", "parent": "05377c25-9a21-4bfd-9675-4644cb2c060e"}}, {"head": {"id": "195c8f4e-7f71-4ea4-83bf-800e60f8122d", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158353070200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fbaf96f-356a-4b81-9dd5-73ac5ae77fad", "name": "hvigorfile, resolve hvigorfile dependencies in 57 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158391450100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2602082b-78b4-4400-a31c-a6e832683eca", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158334622400, "endTime": 156158391640400}, "additional": {"logType": "info", "children": [], "durationId": "e85ae5a9-f8bc-44df-8058-48c9257a153e", "parent": "05377c25-9a21-4bfd-9675-4644cb2c060e"}}, {"head": {"id": "a97fd5fe-f02a-4ecf-90f0-3036c493ec1e", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158392899400, "endTime": 156158393157800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9faac573-712d-4c19-91dd-54fdf497748f", "logId": "aaf9358b-de94-47bf-995e-aa9f7cf9093d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f844a77a-2645-4818-9e0c-1148865e6c2e", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158392935500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaf9358b-de94-47bf-995e-aa9f7cf9093d", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158392899400, "endTime": 156158393157800}, "additional": {"logType": "info", "children": [], "durationId": "a97fd5fe-f02a-4ecf-90f0-3036c493ec1e", "parent": "05377c25-9a21-4bfd-9675-4644cb2c060e"}}, {"head": {"id": "2a28570d-bee9-475a-9f61-d924cf443094", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158394609700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8827febe-25f6-45d7-88ff-4f649aeb151b", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158411367800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bc17f04-afeb-4588-8b61-0b4165bc64a8", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158393169600, "endTime": 156158412626400}, "additional": {"logType": "info", "children": [], "durationId": "92b2f989-b509-461e-8e65-3eb8d9a64297", "parent": "05377c25-9a21-4bfd-9675-4644cb2c060e"}}, {"head": {"id": "3d5fdc73-79d9-4d8d-bd3e-0fcba122d94c", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158412732800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52e281b9-a605-4918-a0c9-85f9131c8bdd", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158426623400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5faa48fd-7cee-4a58-bf0e-5b13c377c224", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158426790500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0da14e6f-b54d-41d6-87c1-6b134bc11dc5", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158427028200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e60333bc-562e-4556-9362-cc0aa776e7a6", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158430991100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2594b7bc-b83e-4c2d-b834-165aaf6b7965", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158431098600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72caba8e-ef69-4250-a83b-709db49eb677", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158412705300, "endTime": 156158435963800}, "additional": {"logType": "info", "children": [], "durationId": "6428d88b-2783-44ed-b0c8-1bdc9cc053b2", "parent": "05377c25-9a21-4bfd-9675-4644cb2c060e"}}, {"head": {"id": "0d285fe1-1904-4193-bd2d-6916138ba7e2", "name": "Configuration phase cost:239 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158436016800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "068e2698-8578-4f3a-9c54-cc62d0403236", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158435986700, "endTime": 156158436163400}, "additional": {"logType": "info", "children": [], "durationId": "42943464-c1c5-4674-9da2-1d5f10271b0a", "parent": "05377c25-9a21-4bfd-9675-4644cb2c060e"}}, {"head": {"id": "05377c25-9a21-4bfd-9675-4644cb2c060e", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158189230900, "endTime": 156158436178900}, "additional": {"logType": "info", "children": ["22596bed-96b1-4464-9fb4-7d9382c8e3e7", "7f310de0-83b6-4cc7-8380-25ef9a8f1515", "8b95b467-552c-40fb-a582-1ff4ab150a1c", "ce944620-6e08-4529-8827-9130818d931e", "2602082b-78b4-4400-a31c-a6e832683eca", "9bc17f04-afeb-4588-8b61-0b4165bc64a8", "72caba8e-ef69-4250-a83b-709db49eb677", "068e2698-8578-4f3a-9c54-cc62d0403236", "aaf9358b-de94-47bf-995e-aa9f7cf9093d"], "durationId": "9faac573-712d-4c19-91dd-54fdf497748f", "parent": "e82c58e7-9546-43fa-b8f2-15b0a99306db"}}, {"head": {"id": "f25cccff-cc92-44b4-859c-96976a255961", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158438162400, "endTime": 156158438181300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "423c0628-23c1-4376-b6e4-2045fc3662b6", "logId": "9e1ff312-f70d-4bcb-bb45-018f6b6f11ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e1ff312-f70d-4bcb-bb45-018f6b6f11ed", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158438162400, "endTime": 156158438181300}, "additional": {"logType": "info", "children": [], "durationId": "f25cccff-cc92-44b4-859c-96976a255961", "parent": "e82c58e7-9546-43fa-b8f2-15b0a99306db"}}, {"head": {"id": "ce7f1c40-ca43-464b-a552-ebfeee1f3701", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158436214700, "endTime": 156158438191700}, "additional": {"logType": "info", "children": [], "durationId": "a32f18c8-2547-4008-8978-44a41464e600", "parent": "e82c58e7-9546-43fa-b8f2-15b0a99306db"}}, {"head": {"id": "571bf5e0-a7fd-4c38-b865-5b1ad9b29a72", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158438197400, "endTime": 156158438199200}, "additional": {"logType": "info", "children": [], "durationId": "2436e5a3-e549-488e-ab25-694f9bdb4b5e", "parent": "e82c58e7-9546-43fa-b8f2-15b0a99306db"}}, {"head": {"id": "e82c58e7-9546-43fa-b8f2-15b0a99306db", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158172005000, "endTime": 156158438205500}, "additional": {"logType": "info", "children": ["d2d4dd00-9ace-4006-811a-59ace61fb95c", "05377c25-9a21-4bfd-9675-4644cb2c060e", "ce7f1c40-ca43-464b-a552-ebfeee1f3701", "571bf5e0-a7fd-4c38-b865-5b1ad9b29a72", "0a40d865-48d1-4641-ab9c-5ccbb5ee32cd", "6b611140-7f8d-4b15-9288-28e152c9e380", "9e1ff312-f70d-4bcb-bb45-018f6b6f11ed"], "durationId": "423c0628-23c1-4376-b6e4-2045fc3662b6"}}, {"head": {"id": "a4fc4b51-0636-45f8-ab3c-eb457bc8c82d", "name": "Configuration task cost before running: 272 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158438388600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23e45dca-8ca6-4c8a-a2d7-6328615866a5", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158452358100, "endTime": 156158473018400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "4603589a-6301-41ff-8d2a-388a04f59c8e", "logId": "209493cd-4153-48fa-ac1a-0870cb6ae4c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4603589a-6301-41ff-8d2a-388a04f59c8e", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158440603400}, "additional": {"logType": "detail", "children": [], "durationId": "23e45dca-8ca6-4c8a-a2d7-6328615866a5"}}, {"head": {"id": "7e0cf753-0b98-4bb7-8c0e-ff5dbceeba6c", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158441883100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60295bc3-72a6-4a35-8f8e-9430fb7c9fdf", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158442035700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dca45853-63d5-40b8-b56d-2e98b2b45e48", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158442915600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c97f275f-3309-43b0-a2e3-3502c745f874", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158444049900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20718f3e-784c-40c2-84e6-e41fe5170265", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158445874900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d6d1aab-387a-44e0-b2db-f8050377fd6d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158446164300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89ee6969-2800-41ae-8b95-e4ddb1d0f749", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158452384700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec35cf45-0304-4aaa-b96b-ab98f006b9b8", "name": "Incremental task entry:default@PreBuild pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158472669100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c896acf-b66a-46ab-bb24-bebb526c62fd", "name": "entry : default@PreBuild cost memory 0.3195648193359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158472851400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "209493cd-4153-48fa-ac1a-0870cb6ae4c0", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158452358100, "endTime": 156158473018400}, "additional": {"logType": "info", "children": [], "durationId": "23e45dca-8ca6-4c8a-a2d7-6328615866a5"}}, {"head": {"id": "5c6adfea-975c-4c76-b9aa-2311c9e8a0ae", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158484781700, "endTime": 156158487536000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d8535230-a971-4bfc-9c41-6bd0419ab1bc", "logId": "9cae74d5-ef3f-4bc2-9b27-3ad9f97372b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8535230-a971-4bfc-9c41-6bd0419ab1bc", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158482198900}, "additional": {"logType": "detail", "children": [], "durationId": "5c6adfea-975c-4c76-b9aa-2311c9e8a0ae"}}, {"head": {"id": "7c2577d9-e87a-4fee-b717-f36d61b8481a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158483778900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3bf8755-d940-4376-9e0f-7cc69bbcd3cf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158483930700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28f6bcc3-e5ca-49f1-b5b1-77b20688ce69", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158484793600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f5f6eb4-c999-4b00-8248-f3e3c2974088", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158485809400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17880f31-8dcc-494f-abbf-1909fc29c4a4", "name": "entry : default@CreateModuleInfo cost memory 0.06000518798828125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158487220300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b24ddd06-afc9-4971-84c1-15dc79e4a76b", "name": "runTaskFromQueue task cost before running: 321 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158487420100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cae74d5-ef3f-4bc2-9b27-3ad9f97372b8", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158484781700, "endTime": 156158487536000, "totalTime": 2602900}, "additional": {"logType": "info", "children": [], "durationId": "5c6adfea-975c-4c76-b9aa-2311c9e8a0ae"}}, {"head": {"id": "c973b0fe-5961-4c04-8ced-a67d7b159e4d", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158508288400, "endTime": 156158513067900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ed174d9f-ddf8-4404-9261-a064f238997d", "logId": "976e5d3a-7df9-4f94-ab07-170f89b9cab3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed174d9f-ddf8-4404-9261-a064f238997d", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158495426900}, "additional": {"logType": "detail", "children": [], "durationId": "c973b0fe-5961-4c04-8ced-a67d7b159e4d"}}, {"head": {"id": "4475910b-6793-49c3-9082-7cbbfb3f763e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158498206300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b83bef63-d80e-4786-8d8c-cd0d6b4bc23d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158498407100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb7d3e77-e109-4c6c-8dec-bba7d721c472", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158508311200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccf4d151-62dc-4fe0-83eb-5cf92b88f4b8", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158510407600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7a26b14-9334-4010-9551-a0e573bf5db0", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158512720600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9debbedb-eda4-4b14-8293-46fa8e7e8377", "name": "entry : default@GenerateMetadata cost memory 0.1014404296875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158512931200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "976e5d3a-7df9-4f94-ab07-170f89b9cab3", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158508288400, "endTime": 156158513067900}, "additional": {"logType": "info", "children": [], "durationId": "c973b0fe-5961-4c04-8ced-a67d7b159e4d"}}, {"head": {"id": "54960b23-589e-4706-9955-41196137d073", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158518806400, "endTime": 156158519456900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e6180599-6cb4-4de3-af9e-197164380e04", "logId": "038d73b7-e5a3-45e2-92d0-af0682a554c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6180599-6cb4-4de3-af9e-197164380e04", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158516046600}, "additional": {"logType": "detail", "children": [], "durationId": "54960b23-589e-4706-9955-41196137d073"}}, {"head": {"id": "8c046734-cc30-4f68-98a9-2f7f14bdd428", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158518381400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d392747-b5fc-4334-affa-5453ff2b92e0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158518569300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9943c87-277c-44f8-9285-03069626365f", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158518819400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39af6256-7a95-4523-8f9d-4e767e3737a4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158518997600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2318e10c-9628-4812-82a7-719830a58d8f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158519098500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f94726de-b8ca-4f53-a683-12a72b6160a6", "name": "entry : default@ConfigureCmake cost memory 0.037139892578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158519221200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12afe98e-5ce5-45c9-a361-3e44ae0e8fc6", "name": "runTaskFromQueue task cost before running: 353 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158519359400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "038d73b7-e5a3-45e2-92d0-af0682a554c1", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158518806400, "endTime": 156158519456900, "totalTime": 526100}, "additional": {"logType": "info", "children": [], "durationId": "54960b23-589e-4706-9955-41196137d073"}}, {"head": {"id": "e13508ad-3042-41aa-b560-bfcff0909775", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158526447000, "endTime": 156158531325900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "516cc86f-e07e-4c7f-8e7e-d3bb59dfb11f", "logId": "744bb17d-9c1b-428b-b06e-5e62349d1c85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "516cc86f-e07e-4c7f-8e7e-d3bb59dfb11f", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158522584200}, "additional": {"logType": "detail", "children": [], "durationId": "e13508ad-3042-41aa-b560-bfcff0909775"}}, {"head": {"id": "3b966c67-f774-4771-8fcc-c1d370244586", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158524844800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04d0501a-f224-410c-84a6-b2c3559e4d82", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158525028700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dff278c4-bea2-4c22-9f59-b457257462f1", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158526464100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ccb677e-d32f-48ab-a059-f2a512008ab4", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158530947800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73948d7b-2a99-4d2a-91bc-04f178fc2ea3", "name": "entry : default@MergeProfile cost memory 0.11740875244140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158531177200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "744bb17d-9c1b-428b-b06e-5e62349d1c85", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158526447000, "endTime": 156158531325900}, "additional": {"logType": "info", "children": [], "durationId": "e13508ad-3042-41aa-b560-bfcff0909775"}}, {"head": {"id": "31c008b3-0527-4835-96dd-4b22855388e8", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158538141200, "endTime": 156158544324200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6d031e4a-69d7-4f4b-a746-2769997843dd", "logId": "58edd357-a34f-40e4-8df0-30ae9db3a529"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d031e4a-69d7-4f4b-a746-2769997843dd", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158534446800}, "additional": {"logType": "detail", "children": [], "durationId": "31c008b3-0527-4835-96dd-4b22855388e8"}}, {"head": {"id": "0c488504-ead1-44cc-be10-0fbd49055b98", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158536540400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "678f085a-ab9f-4584-ab37-afba6ca5ef1e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158536707600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79496606-e113-441a-8404-adc169a693ef", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158538154800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "898d596c-7977-4015-9e76-d339f8da51ce", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158540290300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "870d78cf-7837-4563-a8ce-ab9a470c3d00", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158543965400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f9e3c8e-1eb5-43aa-85ce-589a332696ad", "name": "entry : default@CreateBuildProfile cost memory 0.1072845458984375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158544203700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58edd357-a34f-40e4-8df0-30ae9db3a529", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158538141200, "endTime": 156158544324200}, "additional": {"logType": "info", "children": [], "durationId": "31c008b3-0527-4835-96dd-4b22855388e8"}}, {"head": {"id": "599dd1bb-acbe-41b5-8d97-a2d3173c4a63", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158550117000, "endTime": 156158550853800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d22187ec-4d11-4cdd-8ed1-8bc5bcc4b17e", "logId": "04773dea-cf00-4bc1-910b-ee91d35d99dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d22187ec-4d11-4cdd-8ed1-8bc5bcc4b17e", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158547194000}, "additional": {"logType": "detail", "children": [], "durationId": "599dd1bb-acbe-41b5-8d97-a2d3173c4a63"}}, {"head": {"id": "983d427e-aae0-4e05-903a-ada53d3d744c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158548675200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63c60f0a-5b06-42a7-a888-904aa224d3e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158548845300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9bc470e-eb71-4d40-b272-5e6517e88502", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158550128100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e24c0ab-df20-4e32-b61d-23f9ac729d19", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158550314100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72c20d3c-0ed7-4c83-8bfd-255e0832c4db", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158550389200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5df70c5c-ffdb-4345-9ec8-08c9bf2ad3b7", "name": "entry : default@PreCheckSyscap cost memory 0.040740966796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158550657800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32153ace-52d5-4779-955a-4a1b486c4fd3", "name": "runTaskFromQueue task cost before running: 384 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158550781800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04773dea-cf00-4bc1-910b-ee91d35d99dd", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158550117000, "endTime": 156158550853800, "totalTime": 641000}, "additional": {"logType": "info", "children": [], "durationId": "599dd1bb-acbe-41b5-8d97-a2d3173c4a63"}}, {"head": {"id": "a11a2d8d-823d-4eac-a747-31be78524d2a", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158557094800, "endTime": 156158565404600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "80a43057-1bbe-4ecf-9b49-74329f0be368", "logId": "2c8a5ee0-bcbb-4bcd-8a59-2fec49b38e58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80a43057-1bbe-4ecf-9b49-74329f0be368", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158553402300}, "additional": {"logType": "detail", "children": [], "durationId": "a11a2d8d-823d-4eac-a747-31be78524d2a"}}, {"head": {"id": "5c0facfe-7256-4b7f-9441-098d08655b3b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158555042200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ac03e9f-9480-4524-b3c7-f0615f9eea1f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158555200600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f88db2d2-3647-478c-931a-bf9d31001024", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158557105500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbe2061c-c192-4dc6-b78a-be32e28b7c1d", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158563727000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29563e0c-c03a-4acb-9e74-c26d96e43e56", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158565131600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf6b2dd7-2085-4094-93d1-8df557c455af", "name": "entry : default@GeneratePkgContextInfo cost memory 0.25412750244140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158565293700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c8a5ee0-bcbb-4bcd-8a59-2fec49b38e58", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158557094800, "endTime": 156158565404600}, "additional": {"logType": "info", "children": [], "durationId": "a11a2d8d-823d-4eac-a747-31be78524d2a"}}, {"head": {"id": "7ffca3d0-b752-48f3-a492-a63d22ef7747", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158581083200, "endTime": 156158584871100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "7474579b-7a8a-4a2e-9791-25f2ea578b24", "logId": "479decf0-f2fe-44e0-8f7f-6001d6eb0b8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7474579b-7a8a-4a2e-9791-25f2ea578b24", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158568807300}, "additional": {"logType": "detail", "children": [], "durationId": "7ffca3d0-b752-48f3-a492-a63d22ef7747"}}, {"head": {"id": "2ad6d6be-ad85-42e8-9362-5d6c648b41ab", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158570734200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d00303d3-1a25-49b4-a715-c6abbafec479", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158570873100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f205145e-ba5a-4fa0-80bc-b314d52f2958", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158581115900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d191362b-25e0-4191-82fa-20093d489e52", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158584177400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad9864fb-f673-4437-b1bc-0e06867cceac", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158584355700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b6d7e6e-5f21-4f0d-9d4d-afe572c94dc1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158584485100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0b270ca-d61f-48cd-adc4-ec1ffafa0b88", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158584569000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b10d430-1147-4fa4-b45c-0646f845a3bc", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11993408203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158584678200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc03fb4a-229c-49d7-93ff-4cd596883659", "name": "runTaskFromQueue task cost before running: 418 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158584791800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "479decf0-f2fe-44e0-8f7f-6001d6eb0b8d", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158581083200, "endTime": 156158584871100, "totalTime": 3692600}, "additional": {"logType": "info", "children": [], "durationId": "7ffca3d0-b752-48f3-a492-a63d22ef7747"}}, {"head": {"id": "44cc469a-6736-444c-bb3b-575863b5d412", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158591666700, "endTime": 156158592346200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "bb928689-e4cd-4b25-953b-bf94383c5700", "logId": "d32e66d7-d6a3-4808-8d02-2a1ccf6a5d36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb928689-e4cd-4b25-953b-bf94383c5700", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158588136500}, "additional": {"logType": "detail", "children": [], "durationId": "44cc469a-6736-444c-bb3b-575863b5d412"}}, {"head": {"id": "042b508b-5a2c-4021-bb43-d3186e3e45cb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158590192800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f1ece77-2304-40c9-b85c-719129c02cb8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158590381900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6af74783-9914-4227-88de-17eb14a15cc2", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158591680100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73eda2ed-dafa-4166-8343-356c49386919", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158591867800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad79ffc8-dcb0-48a8-ab15-c7a16eb36cf9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158591958800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "172a0060-c399-4648-bee3-0b968d445dc1", "name": "entry : default@BuildNativeWithCmake cost memory 0.03815460205078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158592074800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "226e480b-ffa3-4ae0-8125-8443a79609e5", "name": "runTaskFromQueue task cost before running: 426 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158592234200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d32e66d7-d6a3-4808-8d02-2a1ccf6a5d36", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158591666700, "endTime": 156158592346200, "totalTime": 512100}, "additional": {"logType": "info", "children": [], "durationId": "44cc469a-6736-444c-bb3b-575863b5d412"}}, {"head": {"id": "2a4b9133-0191-4766-aa39-3adcf65bd7d1", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158599302700, "endTime": 156158606667400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c8902b67-f650-4754-8c0a-eedb69c72d9d", "logId": "9c31ba6e-b46c-4f31-9af3-9c54cd4dfd3d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8902b67-f650-4754-8c0a-eedb69c72d9d", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158594998500}, "additional": {"logType": "detail", "children": [], "durationId": "2a4b9133-0191-4766-aa39-3adcf65bd7d1"}}, {"head": {"id": "18b74607-dff9-48f4-8b80-22085c7ae7d3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158597732500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "473d8661-eb88-4002-9ea9-9a24185e4c98", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158597911900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55d61e95-833c-445a-8e46-ba963b6810a9", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158599315400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07cef2bd-3640-4844-b6f4-fe5ead6e9a36", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158606379600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b67958ac-e660-45a2-8966-aec39a97204a", "name": "entry : default@MakePackInfo cost memory 0.3169097900390625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158606559800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c31ba6e-b46c-4f31-9af3-9c54cd4dfd3d", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158599302700, "endTime": 156158606667400}, "additional": {"logType": "info", "children": [], "durationId": "2a4b9133-0191-4766-aa39-3adcf65bd7d1"}}, {"head": {"id": "d2123059-9fd4-4ac4-90ad-c495068dace4", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158614049400, "endTime": 156158623019300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f4971da4-bece-4fa8-8783-3f62472d3226", "logId": "7687c34e-39d9-49fc-a41f-7183e1e54285"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4971da4-bece-4fa8-8783-3f62472d3226", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158610143300}, "additional": {"logType": "detail", "children": [], "durationId": "d2123059-9fd4-4ac4-90ad-c495068dace4"}}, {"head": {"id": "81a93ea7-4b6a-4a11-899c-404c61b64d7d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158612005400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5c92762-9b9d-43f1-b802-3ed5e9ab8530", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158612167200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25d99ac4-4fdd-4006-b18d-ba0a5ec71ae9", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158614066200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8999c39-e332-4fba-97b7-973be8fbf4ee", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158614421400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e217709a-8802-40f5-915d-b8816fec18c2", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158615989400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2d8d32a-e15a-40c5-9adb-6fdc4cfc8151", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158622708000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a871c46-7e72-406b-b1e3-f46a1e2577bc", "name": "entry : default@SyscapTransform cost memory 0.14995574951171875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158622902500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7687c34e-39d9-49fc-a41f-7183e1e54285", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158614049400, "endTime": 156158623019300}, "additional": {"logType": "info", "children": [], "durationId": "d2123059-9fd4-4ac4-90ad-c495068dace4"}}, {"head": {"id": "32374c72-be92-4bdd-8ce1-e148a3d509d6", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158630369500, "endTime": 156158632767600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "f3067bf6-4104-4cdf-8123-f7ff910478b5", "logId": "9f6d13ab-684f-454b-86b6-471125bd06fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3067bf6-4104-4cdf-8123-f7ff910478b5", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158625766000}, "additional": {"logType": "detail", "children": [], "durationId": "32374c72-be92-4bdd-8ce1-e148a3d509d6"}}, {"head": {"id": "f28ef3f4-caae-44f4-b8e6-07032e17dd68", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158627823500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fb77d9f-264f-419a-b852-0223ac34728a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158627998800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cef16213-e7a3-48e6-b1b2-41de6f68922a", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158630385200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaec74a2-157c-47f3-8b54-44bc3796f9a6", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158632522800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cf5a483-699b-45b4-afb9-4f13dd31e163", "name": "entry : default@ProcessProfile cost memory 0.123291015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158632671600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f6d13ab-684f-454b-86b6-471125bd06fa", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158630369500, "endTime": 156158632767600}, "additional": {"logType": "info", "children": [], "durationId": "32374c72-be92-4bdd-8ce1-e148a3d509d6"}}, {"head": {"id": "1acaccc0-8696-4212-8463-db9f6bd98519", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158641313900, "endTime": 156158654248800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0665ff73-bea3-4d1c-90d1-044a5fe97665", "logId": "c2be27a9-1e76-4490-a161-a059b2198d6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0665ff73-bea3-4d1c-90d1-044a5fe97665", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158635013200}, "additional": {"logType": "detail", "children": [], "durationId": "1acaccc0-8696-4212-8463-db9f6bd98519"}}, {"head": {"id": "38cc6900-7ea4-45d4-9604-29549c0d315f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158637633300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33b5b591-0ca6-4145-9334-dcf4cd910e93", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158637841200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c6c75ba-553c-4c0c-85d2-9642d8557f75", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158641329600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a69b2354-c726-4d06-95e3-efd2f7c9230e", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158653905600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e251307b-16e6-401d-ab0c-fb606ce5cfa8", "name": "entry : default@ProcessRouterMap cost memory 0.23191070556640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158654108700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2be27a9-1e76-4490-a161-a059b2198d6a", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158641313900, "endTime": 156158654248800}, "additional": {"logType": "info", "children": [], "durationId": "1acaccc0-8696-4212-8463-db9f6bd98519"}}, {"head": {"id": "c4a42e80-8cc0-405c-b4cb-27ea0350fbdc", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158662651700, "endTime": 156158673681200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "f2a2cfb9-131f-4dbb-ba7f-2ac81ee6e932", "logId": "e9231a97-9fab-4cd9-90ac-b7450223718e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2a2cfb9-131f-4dbb-ba7f-2ac81ee6e932", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158659875600}, "additional": {"logType": "detail", "children": [], "durationId": "c4a42e80-8cc0-405c-b4cb-27ea0350fbdc"}}, {"head": {"id": "f5b0835b-fc73-40cc-a251-55129dc216d9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158662274300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b3c890-a211-4dc5-90dd-f4d87f10fa70", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158662476200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ab59ba6-5327-4559-8b3d-91973fe5f05d", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158662666700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f31e93d0-0a8c-4378-a136-7809f82a534e", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158662873400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01ebde95-063d-4c45-965d-03d7b8061c0e", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158670920000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cee58de-432d-4f20-b4a8-9f5a191fc12b", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158671132100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8f79926-d02d-488d-89cf-3e636464e212", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158671297300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18001a25-e198-4f51-ad6f-ac09af03a8dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158671394700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f9d91a1-10dd-4d3b-9dbb-ac4b7f5f8da4", "name": "entry : default@ProcessStartupConfig cost memory 0.25695037841796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158673379100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93f34723-5fe2-4739-8e32-f3e723ba5b5e", "name": "runTaskFromQueue task cost before running: 507 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158673567600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9231a97-9fab-4cd9-90ac-b7450223718e", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158662651700, "endTime": 156158673681200, "totalTime": 10882100}, "additional": {"logType": "info", "children": [], "durationId": "c4a42e80-8cc0-405c-b4cb-27ea0350fbdc"}}, {"head": {"id": "5b4bd5db-5e45-46ef-946b-9c1cae73be99", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158682303900, "endTime": 156158684435100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "60414e0e-fbe8-4f2b-8859-588a38607dce", "logId": "7857b669-2bed-4044-89c0-2c60d4b3bc76"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60414e0e-fbe8-4f2b-8859-588a38607dce", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158678741800}, "additional": {"logType": "detail", "children": [], "durationId": "5b4bd5db-5e45-46ef-946b-9c1cae73be99"}}, {"head": {"id": "b4398b17-06b1-4811-b5c2-ba6e77fe7769", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158680762600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf3c9948-9d72-461f-b9b4-65ff111648a0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158680984800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afbdb1cd-237e-46e6-a968-10267ef3cb83", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158682317000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e0d50e2-2729-49bb-b8c3-eab11b4be577", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158682511700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "241d2aeb-8786-47bf-8348-9a3abbf037af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158682602100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89542e74-7b61-41c5-9726-fb4d06846664", "name": "entry : default@BuildNativeWithNinja cost memory 0.057769775390625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158684143300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "001218bc-c3ee-4b7c-ab55-0594485228b8", "name": "runTaskFromQueue task cost before running: 518 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158684332600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7857b669-2bed-4044-89c0-2c60d4b3bc76", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158682303900, "endTime": 156158684435100, "totalTime": 1996100}, "additional": {"logType": "info", "children": [], "durationId": "5b4bd5db-5e45-46ef-946b-9c1cae73be99"}}, {"head": {"id": "0022a55a-def4-483f-87d9-4698454cca12", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158695628100, "endTime": 156158706855800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "f34460e2-f2a5-4fe4-b487-43c4c11a284e", "logId": "1cace465-3d1e-4bd2-bdc2-ee9ec4b838a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f34460e2-f2a5-4fe4-b487-43c4c11a284e", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158688917800}, "additional": {"logType": "detail", "children": [], "durationId": "0022a55a-def4-483f-87d9-4698454cca12"}}, {"head": {"id": "05666a4e-be2c-4b3e-98da-48b1654c2594", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158690997600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6dfebeb-fb64-4704-a6ac-06dd46095ac2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158691164400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f80fe987-af58-4802-b7e7-f25b1e345af8", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158693270100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2fd1f98-7071-4106-bca5-43a7abc1eb5a", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158699042200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96c44531-9752-466d-885d-41109901f3a3", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158703191900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47d5465e-a486-4ea5-8202-015c1d1a69e0", "name": "entry : default@ProcessResource cost memory 0.16089630126953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158703400200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cace465-3d1e-4bd2-bdc2-ee9ec4b838a5", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158695628100, "endTime": 156158706855800}, "additional": {"logType": "info", "children": [], "durationId": "0022a55a-def4-483f-87d9-4698454cca12"}}, {"head": {"id": "37a85ed0-690a-4697-b51c-7b8cf6f13f75", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158725119100, "endTime": 156158768421000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e5d916a4-d4a9-447a-b60e-9bef8f8736fe", "logId": "58a239b2-b2a3-49cc-b2c6-2a435e235223"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5d916a4-d4a9-447a-b60e-9bef8f8736fe", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158714833900}, "additional": {"logType": "detail", "children": [], "durationId": "37a85ed0-690a-4697-b51c-7b8cf6f13f75"}}, {"head": {"id": "3d1d2df9-0ee8-43fc-b6e2-cc5fdd5d83e2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158717156700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6a71b3a-eae1-4e4b-971c-df81781bedb5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158717348000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e372677c-2eb1-403a-945c-8bdf6e30e76d", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158725136600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97b5fc5f-ed45-40a9-92a5-ba28d4c98521", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 26 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158768199400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4df82c89-eeee-44e8-b604-d77060001f03", "name": "entry : default@GenerateLoaderJson cost memory 0.8683090209960938", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158768358500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58a239b2-b2a3-49cc-b2c6-2a435e235223", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158725119100, "endTime": 156158768421000}, "additional": {"logType": "info", "children": [], "durationId": "37a85ed0-690a-4697-b51c-7b8cf6f13f75"}}, {"head": {"id": "b67ef48b-68eb-4380-a769-53ade9beac91", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158789425400, "endTime": 156158797798400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "7f8525f0-d8d5-4263-be39-4bb4b4aee20f", "logId": "70c2739a-3dea-4029-8753-7e59c6f250bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f8525f0-d8d5-4263-be39-4bb4b4aee20f", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158785566300}, "additional": {"logType": "detail", "children": [], "durationId": "b67ef48b-68eb-4380-a769-53ade9beac91"}}, {"head": {"id": "c245273a-3bb2-4c51-af08-7b4ac77bf6c2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158787560200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "720eb32a-35f3-416e-ab9b-3211fc11ae29", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158787760300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b488be7-4fcc-4250-9a10-ced3778628e8", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158789443500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83d65839-a8f6-4dd9-b7d8-3890e62dd371", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158797481600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48ddaddb-96ff-485e-bfc0-f140bf1bf8e3", "name": "entry : default@ProcessLibs cost memory 0.14110565185546875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158797676400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70c2739a-3dea-4029-8753-7e59c6f250bd", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158789425400, "endTime": 156158797798400}, "additional": {"logType": "info", "children": [], "durationId": "b67ef48b-68eb-4380-a769-53ade9beac91"}}, {"head": {"id": "e98dca86-2cde-47e2-9838-5366b009449b", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158814788700, "endTime": 156158876695700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "f00a79cb-bcec-4f8d-be5c-c66fce0f18b3", "logId": "b5af8da4-03cc-4bdd-a16f-9851d31199e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f00a79cb-bcec-4f8d-be5c-c66fce0f18b3", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158801865100}, "additional": {"logType": "detail", "children": [], "durationId": "e98dca86-2cde-47e2-9838-5366b009449b"}}, {"head": {"id": "1173b9f4-7f03-4db9-85b3-c61868e9f421", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158804359900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4dc9e7a-839f-498a-b029-2111ff18943c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158804564200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bf0bca0-eff8-4372-b6f9-6c3416f9ce5d", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158807076600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d082e86-ee4d-4081-b9b6-261c752ecfe7", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158814859400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09e9a9fb-9583-4ed8-8a86-bff46e35fdaa", "name": "Incremental task entry:default@CompileResource pre-execution cost: 59 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158876276400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e7b5cac-905b-471d-ab2d-793ff0935f54", "name": "entry : default@CompileResource cost memory -8.042839050292969", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158876528000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5af8da4-03cc-4bdd-a16f-9851d31199e1", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158814788700, "endTime": 156158876695700}, "additional": {"logType": "info", "children": [], "durationId": "e98dca86-2cde-47e2-9838-5366b009449b"}}, {"head": {"id": "774df087-36a0-4952-b279-0b0418835507", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158889578300, "endTime": 156158894192200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6bfbdcf9-c671-4af3-8b55-9947af588a47", "logId": "792df9d4-2be3-406f-93b1-3f4b87b200c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bfbdcf9-c671-4af3-8b55-9947af588a47", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158882931800}, "additional": {"logType": "detail", "children": [], "durationId": "774df087-36a0-4952-b279-0b0418835507"}}, {"head": {"id": "2593d08d-0757-4235-8555-dbc65d49e953", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158885107700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40b8fa92-57d9-4829-ab66-280d6850a58a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158885358200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45c7fcbf-5161-4480-a7d5-3cc5043e9699", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158889595900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8cdcecc-a395-4019-8ac4-673a4260e318", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158890438500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "681a5f24-05d9-4f01-9b9e-00c4c157601d", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158893877100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c78d7cda-35d8-4359-804d-0d7ff6b20956", "name": "entry : default@DoNativeStrip cost memory 0.079071044921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158894074700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "792df9d4-2be3-406f-93b1-3f4b87b200c4", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158889578300, "endTime": 156158894192200}, "additional": {"logType": "info", "children": [], "durationId": "774df087-36a0-4952-b279-0b0418835507"}}, {"head": {"id": "547dc8b9-f9d4-4966-b8af-d5632bdc0a66", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158907045800, "endTime": 156158967769200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "5173aec4-c3cf-4944-98fa-d034748b1d04", "logId": "352ad471-5a20-4933-9b5f-4f149935eb32"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5173aec4-c3cf-4944-98fa-d034748b1d04", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158896899800}, "additional": {"logType": "detail", "children": [], "durationId": "547dc8b9-f9d4-4966-b8af-d5632bdc0a66"}}, {"head": {"id": "304011a9-d421-4c5a-b08c-81ffdfbe9096", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158898544900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44b86cc2-2296-4785-a2e0-ffdced42617d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158898732500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cbb0db9-66d2-4cdf-849d-6df948d970c3", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158907062700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1435df1b-bbc9-4aea-b924-9c13e87f25ed", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158907312200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f09d89d0-c5a5-4111-8b19-f5a1b6e8f908", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 48 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158967438600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec2fd785-08ba-44b1-92f4-69cf17d8112a", "name": "entry : default@CompileArkTS cost memory 1.1780166625976562", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158967647600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "352ad471-5a20-4933-9b5f-4f149935eb32", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158907045800, "endTime": 156158967769200}, "additional": {"logType": "info", "children": [], "durationId": "547dc8b9-f9d4-4966-b8af-d5632bdc0a66"}}, {"head": {"id": "0bf15974-34e6-4052-b3c8-9d42c8c0f29c", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158987532000, "endTime": 156159000012800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "11f30b24-681e-4d5f-b121-a894719c4ff2", "logId": "c88c2f21-7565-401b-920c-3873b92b1d2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11f30b24-681e-4d5f-b121-a894719c4ff2", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158977852800}, "additional": {"logType": "detail", "children": [], "durationId": "0bf15974-34e6-4052-b3c8-9d42c8c0f29c"}}, {"head": {"id": "33f3ff02-fa3c-414a-8bcb-3f0ad21d651e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158980043600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7ded518-bc80-4101-90d1-f92b3c023e1f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158980233200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2bf6548-bfac-4c01-92fa-742f865b7a87", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158987546700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3cfa83b-8b3c-4168-a581-3e731cce09ea", "name": "entry : default@BuildJS cost memory 0.33740234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158999681100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff9eecd4-7766-4ffe-a6ae-ac462fee22e5", "name": "runTaskFromQueue task cost before running: 833 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158999909600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c88c2f21-7565-401b-920c-3873b92b1d2b", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158987532000, "endTime": 156159000012800, "totalTime": 12337200}, "additional": {"logType": "info", "children": [], "durationId": "0bf15974-34e6-4052-b3c8-9d42c8c0f29c"}}, {"head": {"id": "c853182f-fc61-4a24-8898-f9a40e5cd88d", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159008368700, "endTime": 156159011914000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c7939084-0a04-4c9a-af4c-8ec45cc6348a", "logId": "f5353b48-f2c0-455d-89ca-3245012da5be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7939084-0a04-4c9a-af4c-8ec45cc6348a", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159002358200}, "additional": {"logType": "detail", "children": [], "durationId": "c853182f-fc61-4a24-8898-f9a40e5cd88d"}}, {"head": {"id": "118a2c68-60da-4f15-adb5-05a69df97406", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159003994400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4c6f0e0-b87c-4e8b-baab-1fd8ac0cc619", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159004128800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "292f3280-a625-428d-ab47-de7cf7542342", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159008378500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0a6ee5f-0ef7-4845-918e-96b711a4efd3", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159009092600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8976127a-acf4-45e0-9556-d59f6b82d7a0", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159011679300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df9ce9ae-95f1-4cff-ad2b-b38af8b7beb1", "name": "entry : default@CacheNativeLibs cost memory 0.0941925048828125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159011810800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5353b48-f2c0-455d-89ca-3245012da5be", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159008368700, "endTime": 156159011914000}, "additional": {"logType": "info", "children": [], "durationId": "c853182f-fc61-4a24-8898-f9a40e5cd88d"}}, {"head": {"id": "56eb1612-c633-483e-86b5-44370c4b073b", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159019530800, "endTime": 156159021929100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "62c062ef-7cbc-4f8d-9cc7-6ceee111349e", "logId": "1c9c208b-ff1e-4780-b919-1e68f77f9897"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62c062ef-7cbc-4f8d-9cc7-6ceee111349e", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159015329300}, "additional": {"logType": "detail", "children": [], "durationId": "56eb1612-c633-483e-86b5-44370c4b073b"}}, {"head": {"id": "c8e4aca8-ca25-4b03-9bf7-6d5cf1b83994", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159017394400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4660f0a-710c-4dca-b626-a1e6a8a2754a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159017603900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77f4fa71-365b-4b61-93b2-cb7023f5d26f", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159019546700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2368594b-bc77-445d-92b3-e5e9630d3267", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159020099000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "460df4ca-2a31-4c6e-b381-f87af3cd1bc6", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159021695200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17ec9fb3-bff5-4083-af21-ede1a21f86c6", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07526397705078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159021834200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c9c208b-ff1e-4780-b919-1e68f77f9897", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159019530800, "endTime": 156159021929100}, "additional": {"logType": "info", "children": [], "durationId": "56eb1612-c633-483e-86b5-44370c4b073b"}}, {"head": {"id": "e9202794-e89c-4bcd-be6d-78ae4e695da0", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159034990000, "endTime": 156159067832500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "c1c586f5-0fa7-4700-9e65-df32ff94128b", "logId": "45929d90-a42f-4582-92e2-3889333f1c19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1c586f5-0fa7-4700-9e65-df32ff94128b", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159025580100}, "additional": {"logType": "detail", "children": [], "durationId": "e9202794-e89c-4bcd-be6d-78ae4e695da0"}}, {"head": {"id": "7a6557a9-418c-43ad-bba6-db263b17f265", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159027344000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e322d16-c2f4-4fee-a145-338e338f4e55", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159027495700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77cab152-5215-494a-a508-cfca2e5d0ed4", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159035001700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30b91746-4c6b-49ce-99f0-732aab9c661a", "name": "Incremental task entry:default@PackageHap pre-execution cost: 31 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159067505500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba2a48c3-b2de-425b-a311-908594b249c3", "name": "entry : default@PackageHap cost memory 0.99169921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159067719300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45929d90-a42f-4582-92e2-3889333f1c19", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159034990000, "endTime": 156159067832500}, "additional": {"logType": "info", "children": [], "durationId": "e9202794-e89c-4bcd-be6d-78ae4e695da0"}}, {"head": {"id": "72eebb96-630b-42c5-82eb-be42ab5ba50c", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159080413500, "endTime": 156159086217200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": [], "detailId": "4ff9b2b6-1130-41f6-8595-c1d9c8a8bf61", "logId": "9ff8d74f-6c83-44ad-99b4-b67b0af33913"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ff9b2b6-1130-41f6-8595-c1d9c8a8bf61", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159074267900}, "additional": {"logType": "detail", "children": [], "durationId": "72eebb96-630b-42c5-82eb-be42ab5ba50c"}}, {"head": {"id": "23050709-7250-4ba3-a3a3-c8ab93efb721", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159075938500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7b9c89e-1857-4260-a8ef-57cb5249e415", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159076068000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab269b49-74c2-4377-823a-fb043f091625", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159080435400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "734b9edf-430e-4092-9798-2c1a749a1ff5", "name": "Incremental task entry:default@SignHap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159085877800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eee1e7dd-761f-4895-b593-3538d1630979", "name": "entry : default@SignHap cost memory 0.10390472412109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159086098600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ff8d74f-6c83-44ad-99b4-b67b0af33913", "name": "UP-TO-DATE :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159080413500, "endTime": 156159086217200}, "additional": {"logType": "info", "children": [], "durationId": "72eebb96-630b-42c5-82eb-be42ab5ba50c"}}, {"head": {"id": "b42001c1-dfec-4438-8e67-ce8285e4710f", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159092882800, "endTime": 156159109286800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "89754981-b9ee-4546-8f03-8d8541db9ea8", "logId": "a9c3d275-f7c8-425b-9397-89cf4ec2e6ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89754981-b9ee-4546-8f03-8d8541db9ea8", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159089458700}, "additional": {"logType": "detail", "children": [], "durationId": "b42001c1-dfec-4438-8e67-ce8285e4710f"}}, {"head": {"id": "e5a5faad-8599-4411-919c-53af01fef33a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159091387400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bde73b6-bc62-445b-ba5f-0af7d3e59d0f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159091535200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f64a8a7b-1310-4393-aa55-5f67a9c8c86a", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159092893400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48bf93c1-6cbf-49e4-a5f4-9803ac98bd3b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159108936500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b815515-0762-46ff-b885-f2cad4d5d8fe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159109092000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90a898f8-c1f0-4415-b426-0a0d1a0c15c5", "name": "entry : default@CollectDebugSymbol cost memory 0.24039459228515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159109166200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c545435c-a3d2-48da-ac9f-98807ad58ea1", "name": "runTaskFromQueue task cost before running: 943 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159109242600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9c3d275-f7c8-425b-9397-89cf4ec2e6ba", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159092882800, "endTime": 156159109286800, "totalTime": 16342000}, "additional": {"logType": "info", "children": [], "durationId": "b42001c1-dfec-4438-8e67-ce8285e4710f"}}, {"head": {"id": "63258294-0e3f-4298-9331-d7327e6b7519", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159113676300, "endTime": 156159114009800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "db81e167-9d48-4ee2-8610-2c695e92789a", "logId": "69443a9d-824d-475d-a601-3113e614601b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db81e167-9d48-4ee2-8610-2c695e92789a", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159113604200}, "additional": {"logType": "detail", "children": [], "durationId": "63258294-0e3f-4298-9331-d7327e6b7519"}}, {"head": {"id": "b184d0ce-6031-4cae-8f9a-5d74bf6813f3", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159113685100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b717f39-e14b-4ce5-8da4-2b66de9ffc67", "name": "entry : assembleHap cost memory 0.011627197265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159113869100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd79261a-5ec8-4521-ac1a-d53d227f3eeb", "name": "runTaskFromQueue task cost before running: 948 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159113965000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69443a9d-824d-475d-a601-3113e614601b", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159113676300, "endTime": 156159114009800, "totalTime": 265100}, "additional": {"logType": "info", "children": [], "durationId": "63258294-0e3f-4298-9331-d7327e6b7519"}}, {"head": {"id": "ed594943-5b8a-4a84-973d-88a3f8f6540a", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159135894400, "endTime": 156159135930100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "10a660bb-98df-46f7-a757-bba166b99920", "logId": "4b1e8a94-53d3-48a5-b04d-8eca560c4ad2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b1e8a94-53d3-48a5-b04d-8eca560c4ad2", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159135894400, "endTime": 156159135930100}, "additional": {"logType": "info", "children": [], "durationId": "ed594943-5b8a-4a84-973d-88a3f8f6540a"}}, {"head": {"id": "63269cf8-7274-4d04-b739-812462e3111d", "name": "BUILD SUCCESSFUL in 970 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159135995500}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "29ac4754-3f90-4c85-b525-e9ff4825c7f8", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156158166880600, "endTime": 156159136452400}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 19, "second": 48}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "cd37b573-bbb1-411c-ba64-0b9ddbe48517", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159136490800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2948273b-92fb-42c0-a915-b336072e6dc4", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159136643100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44ed876d-bf47-4f9b-a149-6bc1b42d6945", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159137458400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11910125-dbed-4bc7-b9cc-636072bfadec", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159137582700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f87ff65d-7ffb-4e4a-a02b-d0669d3fa92f", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159137674900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35a021c7-f879-4c01-b3d2-5e787bd39667", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159137747300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bb36c86-4a42-4909-be4a-b37e047f626e", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159137817400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a97a4577-fa1d-4ea2-9de4-8c1ed6eb0a17", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159138984800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaec7628-39eb-4537-9851-53d15faebc9e", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159139398700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19a1e879-8ded-46b1-b9ab-5f86cc4e2508", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159139504900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4db0810b-b984-49e3-aa23-31dd07d652fb", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159139578000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd19366d-6c5a-4416-ac9e-cc3ebeb5421f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159139643500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7919f305-48ea-4bab-8c2c-ed6bdee341d3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159139711900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87cb5a44-07af-4676-bab3-0a37a6d3002a", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159142132700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96a2d308-acad-4ae5-aaef-580b17cd0f83", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159142698800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "788a65ab-bd2e-43c0-8bcc-e894fb17393a", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159143106200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0175e07-1d82-419e-a732-6198b8b59aba", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159143220200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ac83a65-b312-49e1-af3e-64ed59615e14", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159143295000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fe839b3-ad3d-4d2c-bb92-856abb447c84", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159143361400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1470b89-cdaa-4086-9f23-c7cb1ee64fa7", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159143421900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2503f64f-9848-4213-9c79-e95efbda9a39", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159143484700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b305c153-8dd8-4751-9c04-69b527f9d4ab", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159143554900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efa4806e-a43c-4356-87ae-ed70811316a6", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159148115600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5357b919-84ba-466e-8328-edc97efd47f3", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159150859700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "800bd677-3f55-443d-a6f2-91bae4255f4d", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159151852800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0340a7c-12b2-44f5-8bee-872e09f69432", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159152407000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "940cbc36-501a-491e-b6b3-df7c581b7149", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159152968900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57e2e6c7-22e6-43ca-b7d6-cb6f93c0fbb6", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159154296400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63577cfa-3b16-4412-9908-54c898eae399", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159155242900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35a8483b-619c-4c1b-a383-63e704d347df", "name": "Incremental task entry:default@BuildJS post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159155539900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e03a3975-c385-4bc3-9a73-fe8f7f72f876", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159155600800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9902d168-3311-4d18-8edf-d60706452f08", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159155635600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93cdd589-5738-4af7-8b07-8ffd65b171f6", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159155665400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaa29464-5873-48eb-82ea-2def1cc49244", "name": "There is no need to refresh cache, since the incremental task entry:default@SignHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159155695900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "968fb9b9-a32a-449e-a055-d21b5fb296df", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159161789600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f108ea7-163d-419e-81f9-90bc7f371e43", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159162625300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b69b16a4-76bd-4ce8-aa05-965693e76993", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159163981600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1836306-b959-4281-81f0-c2720e1818e8", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 156159164708300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}