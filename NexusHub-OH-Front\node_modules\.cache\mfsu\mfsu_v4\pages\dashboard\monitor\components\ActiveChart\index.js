"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { Area } from "@ant-design/plots";
import { Statistic } from "antd";
import { useEffect, useRef, useState } from "react";
import useStyles from "./index.style";
function fixedZero(val) {
  return val * 1 < 10 ? `0${val}` : val;
}
function getActiveData() {
  const activeData = [];
  for (let i = 0; i < 24; i += 1) {
    activeData.push({
      x: `${fixedZero(i)}:00`,
      y: Math.floor(Math.random() * 200) + i * 50
    });
  }
  return activeData;
}
const ActiveChart = () => {
  const timerRef = useRef(null);
  const requestRef = useRef(null);
  const { styles } = useStyles();
  const [activeData, setActiveData] = useState([]);
  const loopData = () => {
    requestRef.current = requestAnimationFrame(() => {
      timerRef.current = window.setTimeout(() => {
        setActiveData(getActiveData());
        loopData();
      }, 2e3);
    });
  };
  useEffect(() => {
    loopData();
    return () => {
      clearTimeout(timerRef.current);
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, []);
  return /* @__PURE__ */ jsxs("div", { className: styles.activeChart, children: [
    /* @__PURE__ */ jsx(Statistic, { title: "\u76EE\u6807\u8BC4\u4F30", value: "\u6709\u671B\u8FBE\u5230\u9884\u671F" }),
    /* @__PURE__ */ jsx(
      "div",
      {
        style: {
          marginTop: 32
        },
        children: /* @__PURE__ */ jsx(
          Area,
          {
            padding: [0, 0, 0, 0],
            xField: "x",
            axis: false,
            yField: "y",
            height: 84,
            style: { fill: "linear-gradient(-90deg, white 0%, #6294FA 100%)", fillOpacity: 0.6 },
            data: activeData
          }
        )
      }
    ),
    activeData && /* @__PURE__ */ jsxs("div", { children: [
      /* @__PURE__ */ jsxs("div", { className: styles.activeChartGrid, children: [
        /* @__PURE__ */ jsxs("p", { children: [
          [...activeData].sort()[activeData.length - 1]?.y + 200,
          " \u4EBF\u5143"
        ] }),
        /* @__PURE__ */ jsxs("p", { children: [
          [...activeData].sort()[Math.floor(activeData.length / 2)]?.y,
          " \u4EBF\u5143"
        ] })
      ] }),
      /* @__PURE__ */ jsx("div", { className: styles.dashedLine, children: /* @__PURE__ */ jsx("div", { className: styles.line }) }),
      /* @__PURE__ */ jsx("div", { className: styles.dashedLine, children: /* @__PURE__ */ jsx("div", { className: styles.line }) })
    ] }),
    activeData && /* @__PURE__ */ jsxs("div", { className: styles.activeChartLegend, children: [
      /* @__PURE__ */ jsx("span", { children: "00:00" }),
      /* @__PURE__ */ jsx("span", { children: activeData[Math.floor(activeData.length / 2)]?.x }),
      /* @__PURE__ */ jsx("span", { children: activeData[activeData.length - 1]?.x })
    ] })
  ] });
};
export default ActiveChart;
