// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/**
 * 用户注册
 * @param params - 注册参数
 */
export async function register(params: {
  username: string;
  password: string;
  nickname: string;
  email: string;
  phone?: string;
}) {
  return request('/users/register', {
    method: 'POST',
    data: params,
  });
}

/**
 * 用户登录
 * @param params - 登录参数
 */
export async function login(params: {
  username_or_email: string;
  password: string;
}) {
  return request('/users/login', {
    method: 'POST',
    data: params,
  });
}

/**
 * 邮箱登录
 * @param params - 邮箱登录参数
 */
export async function emailLogin(params: {
  email: string;
  password: string;
}) {
  return request('/users/email-login', {
    method: 'POST',
    data: params,
  });
}

/**
 * 发送邮箱验证码
 * @param email - 邮箱地址
 */
export async function sendEmailVerifyCode(email: string) {
  return request('/users/email/verify/send', {
    method: 'POST',
    data: { email },
  });
}

/**
 * 验证邮箱
 * @param params - 验证参数
 */
export async function verifyEmail(params: {
  email: string;
  code: string;
}) {
  return request('/users/email/verify', {
    method: 'POST',
    data: params,
  });
}

/**
 * 发送重置密码验证码
 * @param email - 邮箱地址
 */
export async function sendResetPasswordCode(email: string) {
  return request('/users/password/reset/send', {
    method: 'POST',
    data: { email },
  });
}

/**
 * 重置密码
 * @param params - 重置密码参数
 */
export async function resetPassword(params: {
  email: string;
  code: string;
  new_password: string;
}) {
  return request('/users/password/reset', {
    method: 'POST',
    data: params,
  });
}

/**
 * 发送登录验证码
 * @param email - 邮箱地址
 */
export async function sendLoginCaptcha(email: string) {
  return request('/users/login/captcha/send', {
    method: 'POST',
    data: { email },
  });
}

/**
 * 使用验证码登录
 * @param params - 验证码登录参数
 */
export async function loginWithCaptcha(params: {
  email: string;
  code: string;
}) {
  return request('/users/login/captcha', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取用户资料
 */
export async function getUserProfile() {
  return request('/users/profile', {
    method: 'GET',
  });
}

/**
 * 更新用户资料
 * @param params - 更新参数
 */
export async function updateUserProfile(params: {
  username?: string;
  email?: string;
  phone?: string;
  avatar?: string;
  old_password?: string;
  new_password?: string;
  is_developer?: boolean;
  developer_info?: {
    developer_name?: string;
    company_name?: string;
    website?: string;
    description?: string;
  };
}) {
  return request('/users/profile', {
    method: 'PUT',
    data: params,
  });
}

/**
 * 修改密码
 * @param params - 修改密码参数
 */
export async function changePassword(params: {
  old_password: string;
  new_password: string;
}) {
  return request('/users/profile', {
    method: 'PUT',
    data: params,
  });
}

/**
 * 获取上传凭证
 * @param params - 上传参数
 */
export async function getUploadToken(params: {
  file_type: string;
  file_name: string;
}) {
  return request('/upload/token', {
    method: 'GET',
    params,
  });
}

/**
 * 申请成为开发者（已废弃，请使用开发者认证接口）
 * @param params - 申请参数
 */
export async function applyDeveloper(params: {
  companyName: string;
  website?: string;
  description: string;
  contactEmail: string;
  contactPhone?: string;
}) {
  return request('/developers/verify', {
    method: 'POST',
    data: {
      developer_name: params.companyName,
      company_name: params.companyName,
      website: params.website,
      description: params.description,
      contact_email: params.contactEmail,
      contact_phone: params.contactPhone,
      developer_address: '',
      identity_card: '',
    },
  });
}

/**
 * 获取用户详情
 * @param id - 用户ID
 */
export async function getUserDetail(id: string) {
  return request(`/admin/users/${id}`, {
    method: 'GET',
  });
}

/**
 * 获取用户登录记录
 * @param userId - 用户ID
 * @param params - 查询参数
 */
export async function getUserLoginRecords(userId: string, params?: { page?: number; pageSize?: number }) {
  return request(`/admin/users/${userId}/login-records`, {
    method: 'GET',
    params,
  });
}

/**
 * 获取用户应用记录
 * @param userId - 用户ID
 * @param params - 查询参数
 */
export async function getUserAppRecords(userId: string, params?: { page?: number; pageSize?: number }) {
  return request(`/admin/users/${userId}/app-records`, {
    method: 'GET',
    params,
  });
}

/**
 * 更新用户角色
 * @param userId - 用户ID
 * @param role - 新角色
 */
export async function updateUserRole(userId: string, role: string) {
  return request(`/admin/users/${userId}/role`, {
    method: 'PUT',
    data: { role },
  });
}

/**
 * 管理员创建用户
 * @param params - 用户信息
 */
export async function createUser(params: {
  username: string;
  email: string;
  phone?: string;
  password: string;
  role: string;
}) {
  return request('/admin/users', {
    method: 'POST',
    data: params,
  });
}

/**
 * 切换用户锁定状态
 * @param userId - 用户ID
 * @param locked - 是否锁定
 */
export async function toggleUserLockStatus(userId: string, locked: boolean) {
  return request(`/admin/users/${userId}/lock`, {
    method: 'PUT',
    data: { locked },
  });
}