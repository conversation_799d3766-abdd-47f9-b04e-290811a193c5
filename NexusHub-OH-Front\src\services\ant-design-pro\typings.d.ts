declare namespace API {
  type AdminCreateUserRequest = {
    email: string;
    password: string;
    phone?: string;
    role: 'user' | 'developer' | 'operator' | 'reviewer' | 'admin';
    username: string;
  };

  type AdminVerifyRequest = {
    verify_reason?: string;
    verify_status: 'pending' | 'approved' | 'rejected';
  };

  type AlertEvent = {
    /** 创建时间 */
    created_at?: string;
    /** 告警描述 */
    description?: string;
    id?: number;
    /** 解决时间 */
    resolved_at?: string;
    /** 严重程度：low, medium, high, critical */
    severity?: string;
    /** 状态：active, resolved */
    status?: string;
    /** 告警类型 */
    type?: string;
  };

  type AnalyticsSummary = {
    /** 今日新增应用 */
    new_apps_today?: number;
    /** 今日新增下载 */
    new_downloads_today?: number;
    /** 今日新增用户 */
    new_users_today?: number;
    /** 待审核应用数 */
    pending_apps_count?: number;
    /** 待审核评论数 */
    pending_reviews_count?: number;
    /** 总应用数 */
    total_apps?: number;
    /** 总开发者数 */
    total_developers?: number;
    /** 总下载量 */
    total_downloads?: number;
    /** 总评论数 */
    total_reviews?: number;
    /** 总用户数 */
    total_users?: number;
  };

  type AnalyticsTrend = {
    /** 应用增长趋势 */
    app_trend?: DateValue[];
    /** 开发者增长趋势 */
    developer_trend?: DateValue[];
    /** 下载增长趋势 */
    download_trend?: DateValue[];
    /** 用户增长趋势 */
    user_trend?: DateValue[];
  };

  type AppDetailsResponse = {
    average_rating?: number;
    banner_image?: string;
    category?: string;
    company_name?: string;
    created_at?: string;
    current_version?: string;
    description?: string;
    developer_id?: number;
    developer_name?: string;
    download_count?: number;
    icon?: string;
    id?: number;
    is_editor?: boolean;
    is_featured?: boolean;
    is_top?: boolean;
    is_verified?: boolean;
    min_open_harmony_os_ver?: string;
    name?: string;
    package?: string;
    privacy_url?: string;
    rating_count?: number;
    release_date?: string;
    short_desc?: string;
    size?: number;
    status?: string;
    tags?: string;
    updated_at?: string;
    website?: string;
    website_url?: string;
  };

  type AppDocument = {
    average_rating?: number;
    category?: string;
    current_version?: string;
    description?: string;
    developer_id?: number;
    developer_name?: string;
    download_count?: number;
    id?: number;
    is_editor?: boolean;
    is_featured?: boolean;
    is_top?: boolean;
    is_verified?: boolean;
    /** 用于搜索的关键词 */
    keywords?: string[];
    min_open_harmony_os_ver?: string;
    name?: string;
    package?: string;
    rating_count?: number;
    short_desc?: string;
    status?: string;
    tags?: string;
  };

  type AppDownloadStats = {
    date?: string;
    downloads?: number;
  };

  type AppDownloadStatsResponse = {
    daily_stats?: AppDownloadStats[];
    device_stats?: DeviceDownloadStats[];
    total_downloads?: number;
  };

  type AppResponse = {
    average_rating?: number;
    category_id?: number;
    created_at?: string;
    current_versions?: string;
    deleted_at?: string;
    description?: string;
    developer_id?: number;
    download_count?: number;
    icon?: string;
    id?: number;
    min_android_sdk?: number;
    name?: string;
    package_name?: string;
    review_count?: number;
    status?: string;
    updated_at?: string;
  };

  type AppReviewRequest = {
    reason?: string;
    status: 'approved' | 'rejected';
  };

  type AppTagRequest = {
    tag_ids: number[];
  };

  type CategoryDetailResponse = {
    /** 分类信息 */
    category?: CategoryResponse;
    /** 子分类列表 */
    subcategories?: CategoryResponse[];
  };

  type CategoryResponse = {
    /** 创建时间 */
    created_at?: string;
    /** 删除时间 */
    deleted_at?: string;
    /** 分类描述 */
    description?: string;
    /** 分类图标URL */
    icon?: string;
    /** 分类ID */
    id?: number;
    /** 是否启用 */
    is_active?: boolean;
    /** 分类名称 */
    name?: string;
    /** 父分类ID */
    parent_id?: number;
    /** 排序权重 */
    sort_order?: number;
    /** 更新时间 */
    updated_at?: string;
  };

  type CategoryStats = {
    /** 应用数量 */
    app_count?: number;
    /** 分类ID */
    category_id?: number;
    /** 分类名称 */
    category_name?: string;
    /** 下载数量 */
    download_count?: number;
  };

  type CreateAppRequest = {
    banner_image?: string;
    category: string;
    description?: string;
    icon: string;
    min_open_harmony_os_ver: string;
    name: string;
    package: string;
    privacy_url?: string;
    short_desc?: string;
    tags?: string;
    website_url?: string;
  };

  type CreateCategoryRequest = {
    description?: string;
    icon?: string;
    name: string;
    parent_id?: number;
    sort_order?: number;
  };

  type CreateReviewRequest = {
    app_version: string;
    content: string;
    rating: number;
    title: string;
  };

  type CreateTagRequest = {
    color?: string;
    description?: string;
    name: string;
  };

  type DateValue = {
    /** 日期，格式 YYYY-MM-DD */
    date?: string;
    /** 值 */
    value?: number;
  };

  type deleteAppsIdTagsTagIdParams = {
    /** 应用ID */
    id: number;
    /** 标签ID */
    tag_id: number;
  };

  type deleteCategoriesIdParams = {
    /** 分类ID */
    id: number;
  };

  type deleteDashboardWorkbenchTasksIdParams = {
    /** 任务ID */
    id: number;
  };

  type deleteTagsIdParams = {
    /** 标签ID */
    id: number;
  };

  type DeveloperVerifyRequest = {
    business_license?: string;
    company_name?: string;
    contact_email: string;
    contact_phone: string;
    description: string;
    developer_address: string;
    developer_avatar?: string;
    developer_name: string;
    identity_card: string;
    website?: string;
  };

  type DeveloperVerifyResponse = {
    business_license?: string;
    company_name?: string;
    contact_email?: string;
    contact_phone?: string;
    description?: string;
    developer_address?: string;
    developer_avatar?: string;
    developer_name?: string;
    id?: number;
    identity_card?: string;
    submitted_at?: string;
    username?: string;
    verified_at?: string;
    verify_reason?: string;
    verify_status?: string;
    website?: string;
  };

  type DeveloperStatsResponse = {
    /** 总申请数 */
    total_applications?: number;
    /** 待审核数量 */
    pending_count?: number;
    /** 已通过数量 */
    approved_count?: number;
    /** 已拒绝数量 */
    rejected_count?: number;
    /** 今日新申请数 */
    today_applications?: number;
    /** 通过率 */
    approval_rate?: string;
  };

  type RecentApplicationResponse = {
    /** 申请ID */
    id?: number;
    /** 用户名 */
    username?: string;
    /** 开发者名称 */
    developer_name?: string;
    /** 公司名称 */
    company_name?: string;
    /** 认证状态 */
    verify_status?: 'pending' | 'approved' | 'rejected';
    /** 提交时间 */
    submitted_at?: string;
    /** 开发者头像 */
    developer_avatar?: string;
  };

  type DeviceDownloadStats = {
    device_type?: string;
    downloads?: number;
  };

  type DevResponseRequest = {
    content: string;
  };

  type DownloadRecordResponse = {
    app_icon?: string;
    app_name?: string;
    application_id?: number;
    created_at?: string;
    device_model?: string;
    device_os?: string;
    device_type?: string;
    id?: number;
    status?: string;
    user_id?: number;
    version_name?: string;
  };

  type ErrorResponse = {
    code?: number;
    message?: string;
  };

  type getAdminDevelopersVerifyParams = {
    /** 页码，默认1 */
    page?: number;
    /** 每页数量，默认20 */
    page_size?: number;
    /** 认证状态(pending/approved/rejected) */
    status?: string;
  };

  type getAdminUsersParams = {
    /** 页码，默认1 */
    page?: number;
    /** 每页数量，默认10 */
    pageSize?: number;
    /** 搜索关键词（用户名/邮箱/手机） */
    keyword?: string;
    /** 用户角色 */
    role?: string;
    /** 用户状态 */
    status?: string;
    /** 开发者状态 */
    developerStatus?: string;
  };

  type getApiV1UploadTokenParams = {
    /** 文件类型(avatar/license/identity/screenshot/package) */
    file_type: string;
    /** 文件名称 */
    file_name: string;
  };

  type getAppsIdParams = {
    /** 应用ID */
    id: number;
  };

  type getAppsIdReviewsParams = {
    /** 应用ID */
    id: number;
    /** 页码，默认1 */
    page?: number;
    /** 每页数量，默认20 */
    page_size?: number;
  };

  type getAppsIdTagsParams = {
    /** 应用ID */
    id: number;
  };

  type getAppsParams = {
    /** 页码，默认1 */
    page?: number;
    /** 每页数量，默认20 */
    page_size?: number;
    /** 应用类别 */
    category?: string;
    /** 搜索关键字 */
    keyword?: string;
  };

  type getCategoriesIdParams = {
    /** 分类ID */
    id: number;
  };

  type getCategoriesParams = {
    /** 是否包含未激活的分类 */
    include_inactive?: boolean;
  };

  type getCategoriesRootParams = {
    /** 是否包含未激活的分类 */
    include_inactive?: boolean;
  };

  type getDashboardAnalyticsPopularAppsParams = {
    /** 返回数量，默认10个 */
    limit?: number;
  };

  type getDashboardAnalyticsTrendParams = {
    /** 统计天数，默认30天 */
    days?: number;
  };

  type getDashboardMonitoringAlertsParams = {
    /** 页码，默认1 */
    page?: number;
    /** 每页数量，默认20 */
    page_size?: number;
    /** 状态过滤(active/resolved) */
    status?: string;
  };

  type getDashboardMonitoringLogsParams = {
    /** 页码，默认1 */
    page?: number;
    /** 每页数量，默认20 */
    page_size?: number;
    /** 日志级别过滤(info/warning/error/critical) */
    level?: string;
  };

  type getDashboardWorkbenchActivitiesParams = {
    /** 返回数量，默认10条 */
    limit?: number;
  };

  type getDashboardWorkbenchTasksParams = {
    /** 页码，默认1 */
    page?: number;
    /** 每页数量，默认20 */
    page_size?: number;
    /** 任务状态过滤(pending/in_progress/completed) */
    status?: string;
  };

  type getReviewerAppsPendingParams = {
    /** 页码，默认1 */
    page?: number;
    /** 每页数量，默认20 */
    limit?: number;
  };

  type getSearchAppsParams = {
    /** 搜索关键词 */
    keyword?: string;
    /** 应用分类 */
    category?: string;
    /** 标签列表 */
    tags?: string[];
    /** 最低评分 */
    min_rating?: number;
    /** 是否认证 */
    is_verified?: boolean;
    /** 是否推荐 */
    is_featured?: boolean;
    /** 排序字段(name/download_count/rating) */
    sort_by?: 'name' | 'download_count' | 'rating';
    /** 排序方向(asc/desc) */
    sort_order?: 'asc' | 'desc';
    /** 页码，默认1 */
    page?: number;
    /** 每页数量，默认20 */
    page_size?: number;
  };

  type getSearchReviewsParams = {
    /** 搜索关键词 */
    keyword?: string;
    /** 用户ID */
    user_id?: number;
    /** 应用ID */
    application_id?: number;
    /** 最低评分 */
    min_rating?: number;
    /** 最高评分 */
    max_rating?: number;
    /** 评论状态 */
    status?: string;
    /** 是否有开发者回复 */
    has_dev_reply?: boolean;
    /** 排序字段 */
    sort_by?: 'created_at' | 'rating' | 'like_count';
    /** 排序方向 */
    sort_order?: 'asc' | 'desc';
    /** 页码，默认1 */
    page?: number;
    /** 每页数量，默认20 */
    page_size?: number;
  };

  type getSearchSuggestionsParams = {
    /** 搜索关键词 */
    q: string;
    /** 建议数量限制，默认10 */
    limit?: number;
  };

  type getSearchTagsParams = {
    /** 搜索关键词 */
    keyword?: string;
    /** 是否活跃 */
    is_active?: boolean;
    /** 排序字段 */
    sort_by?: 'name' | 'app_count' | 'created_at';
    /** 排序方向 */
    sort_order?: 'asc' | 'desc';
    /** 页码，默认1 */
    page?: number;
    /** 每页数量，默认20 */
    page_size?: number;
  };

  type getSearchTagsSuggestParams = {
    /** 搜索前缀 */
    prefix: string;
    /** 建议数量限制，默认10 */
    limit?: number;
  };

  type getSearchUsersParams = {
    /** 搜索关键词 */
    keyword?: string;
    /** 用户角色 */
    role?: string;
    /** 用户状态 */
    status?: string;
    /** 是否为开发者 */
    is_developer?: boolean;
    /** 认证状态 */
    verify_status?: string;
    /** 排序字段 */
    sort_by?: 'username' | 'created_at' | 'last_login_at' | 'login_count';
    /** 排序方向 */
    sort_order?: 'asc' | 'desc';
    /** 页码，默认1 */
    page?: number;
    /** 每页数量，默认20 */
    page_size?: number;
  };

  type getStatsAppsIdDownloadsParams = {
    /** 应用ID */
    id: number;
    /** 统计天数，默认30天 */
    days?: number;
  };

  type getStatsDownloadsParams = {
    /** 页码，默认1 */
    page?: number;
    /** 每页数量，默认20 */
    page_size?: number;
  };

  type getTagsIdAppsParams = {
    /** 标签ID */
    id: number;
  };

  type getTagsIdParams = {
    /** 标签ID */
    id: number;
  };

  type getTagsParams = {
    /** 是否包含未激活的标签 */
    include_inactive?: boolean;
  };

  type LoginRequest = {
    password: string;
    username_or_email: string;
  };

  type MonitoringData = {
    summary?: MonitoringSummary;
    system_status?: Record;
  };

  type MonitoringSummary = {
    /** 平均响应时间(ms) */
    average_response_time?: number;
    /** CPU使用率 */
    cpu_usage?: number;
    /** 数据库连接数 */
    database_connections?: number;
    /** 磁盘使用率 */
    disk_usage?: number;
    /** 错误率 */
    error_rate?: number;
    /** 内存使用率 */
    memory_usage?: number;
    /** 每分钟请求数 */
    requests_per_minute?: number;
    /** 服务器状态 */
    server_status?: string;
    /** 运行时间(小时) */
    uptime_hours?: number;
  };

  type PagedResponse = {
    /** 数据 */
    data?: any;
    /** 当前页码 */
    page?: number;
    /** 每页数量 */
    page_size?: number;
    /** 总记录数 */
    total?: number;
  };

  type PageResponse = {
    code?: number;
    data?: any;
    message?: string;
    page?: number;
    page_size?: number;
    total?: number;
  };

  type PopularApp = {
    app_icon?: string;
    app_id?: number;
    app_name?: string;
    category_name?: string;
    developer_name?: string;
    download_count?: number;
    rating?: number;
  };

  type postAdminDevelopersIdVerifyParams = {
    /** 用户ID */
    id: number;
  };

  type postApiV1MessagesAppReviewAppIdParams = {
    /** 应用ID */
    app_id: number;
    /** 操作类型 */
    action: 'submit' | 'approve' | 'reject';
    /** 原因（拒绝时必填） */
    reason?: string;
  };

  type postAppsIdReviewsParams = {
    /** 应用ID */
    id: number;
  };

  type postAppsIdReviewsReviewIdLikeParams = {
    /** 应用ID */
    id: number;
    /** 评论ID */
    review_id: number;
  };

  type postAppsIdReviewsReviewIdRespondParams = {
    /** 应用ID */
    id: number;
    /** 评论ID */
    review_id: number;
  };

  type postAppsIdReviewsReviewIdUnlikeParams = {
    /** 应用ID */
    id: number;
    /** 评论ID */
    review_id: number;
  };

  type postAppsIdTagsParams = {
    /** 应用ID */
    id: number;
  };

  type postAppsIdVersionsVersionIdDownloadParams = {
    /** 应用ID */
    id: number;
    /** 版本ID */
    version_id: number;
  };

  type postReviewerAppsIdReviewParams = {
    /** 应用ID */
    id: number;
  };

  type putAdminUsersIdRoleParams = {
    /** 用户ID */
    id: number;
  };

  type putAdminUsersIdStatusParams = {
    /** 用户ID */
    id: number;
  };

  type putAppsIdParams = {
    /** 应用ID */
    id: number;
  };

  type putCategoriesIdParams = {
    /** 分类ID */
    id: number;
  };

  type putDashboardWorkbenchTasksIdParams = {
    /** 任务ID */
    id: number;
  };

  type putTagsIdParams = {
    /** 标签ID */
    id: number;
  };

  type RecentActivity = {
    /** 活动内容 */
    content?: string;
    /** 创建时间 */
    created_at?: string;
    id?: number;
    /** 活动类型：app_create, app_update, review, download, etc. */
    type?: string;
    /** 活动所属用户ID */
    user_id?: number;
  };

  type RegisterRequest = {
    email: string;
    password: string;
    phone?: string;
    username: string;
  };

  type Response = {
    code?: number;
    data?: any;
    message?: string;
  };

  type ReviewDocument = {
    app_name?: string;
    app_package?: string;
    app_version?: string;
    application_id?: number;
    content?: string;
    created_at?: string;
    dev_response?: string;
    id?: number;
    /** 用于搜索的关键词 */
    keywords?: string[];
    like_count?: number;
    rating?: number;
    status?: string;
    title?: string;
    updated_at?: string;
    user_id?: number;
    username?: string;
  };

  type ReviewResponse = {
    app_name?: string;
    app_version?: string;
    application_id?: number;
    avatar?: string;
    content?: string;
    created_at?: string;
    dev_response?: string;
    id?: number;
    like_count?: number;
    rating?: number;
    status?: string;
    title?: string;
    updated_at?: string;
    user_id?: number;
    username?: string;
  };

  type ReviewSearchResponse = {
    page?: number;
    page_size?: number;
    reviews?: ReviewDocument[];
    total?: number;
    total_pages?: number;
  };

  type SearchResponse = {
    apps?: AppDocument[];
    page?: number;
    page_size?: number;
    total?: number;
    total_pages?: number;
  };

  type SendEmailRequest = {
    body: string;
    is_html?: boolean;
    subject: string;
    to: string;
  };

  type SendNotificationRequest = {
    content: string;
    title: string;
    type: 'info' | 'warning' | 'error' | 'success';
    user_id: number;
  };

  type SuccessResponse = {
    code?: number;
    data?: any;
    message?: string;
  };

  type SystemLog = {
    /** 创建时间 */
    created_at?: string;
    id?: number;
    /** 日志级别：info, warning, error, critical */
    level?: string;
    /** 日志消息 */
    message?: string;
    /** 日志来源 */
    source?: string;
  };

  type TagAppCountResponse = {
    app_count?: number;
    tag?: TagResponse;
  };

  type TagDocument = {
    /** 使用该标签的应用数量 */
    app_count?: number;
    color?: string;
    created_at?: string;
    description?: string;
    id?: number;
    is_active?: boolean;
    /** 用于搜索的关键词 */
    keywords?: string[];
    name?: string;
    updated_at?: string;
  };

  type TagResponse = {
    color?: string;
    created_at?: string;
    deleted_at?: string;
    description?: string;
    id?: number;
    is_active?: boolean;
    name?: string;
    updated_at?: string;
  };

  type TagSearchResponse = {
    page?: number;
    page_size?: number;
    tags?: TagDocument[];
    total?: number;
    total_pages?: number;
  };

  type TagStatsDocument = {
    app_count?: number;
    tag_id?: number;
    tag_name?: string;
    total_apps?: number;
    /** 使用率 */
    usage_rate?: number;
  };

  type TaskItem = {
    /** 完成时间 */
    completed_at?: string;
    /** 创建时间 */
    created_at?: string;
    /** 任务描述 */
    description?: string;
    /** 截止日期 */
    due_date?: string;
    id?: number;
    /** 优先级：low, medium, high */
    priority?: string;
    /** 任务状态：pending, in_progress, completed */
    status?: string;
    /** 任务标题 */
    title?: string;
    /** 更新时间 */
    updated_at?: string;
    /** 任务所属用户ID */
    user_id?: number;
  };

  type UpdateAppRequest = {
    banner_image?: string;
    category?: string;
    description?: string;
    icon?: string;
    min_open_harmony_os_ver?: string;
    name?: string;
    privacy_url?: string;
    short_desc?: string;
    tags?: string;
    website_url?: string;
  };

  type UpdateCategoryRequest = {
    description?: string;
    icon?: string;
    is_active?: boolean;
    name?: string;
    parent_id?: number;
    sort_order?: number;
  };

  type UpdateProfileRequest = {
    avatar?: string;
    developer_info?: {
      company_name?: string;
      description?: string;
      developer_name?: string;
      website?: string;
    };
    email?: string;
    is_developer?: boolean;
    new_password?: string;
    old_password?: string;
    phone?: string;
    username?: string;
    address?: string;
    province?: string;
    city?: string;
    district?: string;
    street?: string;
    company_name?: string;
    description?: string;
  };

  type UpdateTagRequest = {
    color?: string;
    description?: string;
    is_active?: boolean;
    name?: string;
  };

  type UpdateUserRoleRequest = {
    role: 'user' | 'developer' | 'operator' | 'reviewer' | 'admin';
  };

  type UpdateUserStatusRequest = {
    status: 'active' | 'suspended' | 'banned';
  };

  type UploadResult = {
    file_url?: string;
  };

  type UserActivityRequest = {
    action: string;
    resource: string;
    resource_id?: number;
    user_id: number;
  };

  type UserDocument = {
    company_name?: string;
    contact_email?: string;
    contact_phone?: string;
    created_at?: string;
    description?: string;
    developer_address?: string;
    developer_name?: string;
    email?: string;
    id?: number;
    is_developer?: boolean;
    /** 用于搜索的关键词 */
    keywords?: string[];
    last_login_at?: string;
    login_count?: number;
    phone?: string;
    role?: string;
    status?: string;
    updated_at?: string;
    username?: string;
    verify_status?: string;
    website?: string;
  };

  type UserResponse = {
    avatar?: string;
    company_name?: string;
    created_at?: string;
    description?: string;
    developer_name?: string;
    email?: string;
    id?: number;
    is_developer?: boolean;
    last_login_at?: string;
    phone?: string;
    role?: string;
    status?: string;
    username?: string;
    verify_status?: string;
    website?: string;
  };

  type UserSearchResponse = {
    page?: number;
    page_size?: number;
    total?: number;
    total_pages?: number;
    users?: UserDocument[];
  };

  type WorkbenchSummary = {
    /** 我的应用平均评分 */
    average_rating?: number;
    /** 已完成任务数量 */
    completed_task_count?: number;
    /** 我的应用数量 */
    my_app_count?: number;
    /** 我的应用新评论数量 */
    new_reviews_count?: number;
    /** 我的待审核应用数量 */
    pending_apps_count?: number;
    /** 我的应用总下载量 */
    total_downloads?: number;
    /** 总任务数量 */
    total_task_count?: number;
  };
}
