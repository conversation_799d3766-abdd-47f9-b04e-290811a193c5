import { UserModel } from '../models/User';
import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { LoadingView, LoadingState } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { preferences } from '@kit.ArkData';
// getContext is deprecated, use this.getUIContext().getHostContext() instead
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 个人中心页面
 */
@Entry
@Component
struct ProfilePage {
  @State userInfo: UserModel | null = null;
  @State loadingState: LoadingState = LoadingState.LOADING;
  @State isLoggedIn: boolean = false;
  @State downloadedAppsCount: number = 0;
  @State favoriteAppsCount: number = 0;
  @State reviewsCount: number = 0;

  private deviceUtils = DeviceUtils.getInstance();
  private apiService = ApiService.getInstance();

  aboutToAppear() {
    this.checkLoginStatus();
  }

  /**
   * 页面显示时的回调
   */
  onPageShow() {
    this.checkLoginStatus();
  }

  /**
   * 检查登录状态
   */
  private async checkLoginStatus(): Promise<void> {
    try {
      const context = this.getUIContext().getHostContext();
      const options: preferences.Options = { name: 'user_data' };
      const dataPreferences = preferences.getPreferencesSync(context, options);
      const token = dataPreferences.getSync('token', '') as string;
      
      if (token) {
        this.isLoggedIn = true;
        await this.loadUserProfile();
      } else {
        this.isLoggedIn = false;
        this.loadingState = LoadingState.SUCCESS;
      }
    } catch (error) {
      hilog.error(0x0000, 'ProfilePage', '检查登录状态失败: %{public}s', JSON.stringify(error));
      this.isLoggedIn = false;
      this.loadingState = LoadingState.SUCCESS;
    }
  }

  /**
   * 加载用户资料
   */
  private async loadUserProfile() {
    try {
      this.loadingState = LoadingState.LOADING;
      const response = await this.apiService.getUserProfile();
      
      if (response.code === 200 && response.data) {
        this.userInfo = response.data;
        await this.loadUserStats();
        this.loadingState = LoadingState.SUCCESS;
      } else {
        this.loadingState = LoadingState.ERROR;
      }
    } catch (error) {
      hilog.error(0x0000, 'ProfilePage', '加载用户资料失败: %{public}s', JSON.stringify(error));
      this.loadingState = LoadingState.ERROR;
    }
  }

  /**
   * 加载用户统计数据
   */
  private async loadUserStats() {
    try {
      // 这里可以调用相应的API获取用户统计数据
      // 暂时使用模拟数据
      this.downloadedAppsCount = 25;
      this.favoriteAppsCount = 12;
      this.reviewsCount = 8;
    } catch (error) {
      hilog.error(0x0000, 'ProfilePage', '加载用户统计数据失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 登出
   */
  private logout(): void {
    try {
      const context = this.getUIContext().getHostContext();
      const options: preferences.Options = { name: 'user_data' };
      const dataPreferences = preferences.getPreferencesSync(context, options);
      dataPreferences.clearSync();
      dataPreferences.flush();
      
      this.isLoggedIn = false;
      this.userInfo = null;
    } catch (error) {
      hilog.error(0x0000, 'ProfilePage', '登出失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 跳转到登录页面
   */
  private navigateToLogin() {
    this.getUIContext().getRouter().pushUrl({ url: 'pages/LoginPage' });
  }

  /**
   * 跳转到设置页面
   */
  private navigateToSettings() {
    this.getUIContext().getRouter().pushUrl({ url: 'pages/SettingsPage' });
  }

  /**
   * 跳转到我的应用页面
   */
  private navigateToMyApps() {
    this.getUIContext().getRouter().pushUrl({ url: 'pages/MyAppsPage' });
  }

  /**
   * 跳转到我的收藏页面
   */
  private navigateToFavorites() {
    this.getUIContext().getRouter().pushUrl({ url: 'pages/FavoritesPage' });
  }

  /**
   * 跳转到我的评论页面
   */
  private navigateToMyReviews() {
    this.getUIContext().getRouter().pushUrl({ url: 'pages/MyReviewsPage' });
  }

  /**
   * 用户头像和信息
   */
  @Builder
  private UserHeader() {
    if (this.isLoggedIn && this.userInfo) {
      Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
        // 用户头像
        Image(this.userInfo.avatar || Constants.PLACEHOLDER_IMAGE)
          .width(this.deviceUtils.isTablet() ? 80 : 64)
          .height(this.deviceUtils.isTablet() ? 80 : 64)
          .borderRadius((this.deviceUtils.isTablet() ? 80 : 64) / 2)
          .objectFit(ImageFit.Cover)
          .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)

        // 用户信息
        Column({ space: 4 }) {
          Text(this.userInfo.username || this.userInfo.email)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontWeight(FontWeight.Bold)
            .fontColor(Constants.COLORS.WHITE)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })

          if (this.userInfo.username) {
          Text(this.userInfo.username)
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
              .fontColor(Constants.COLORS.WHITE)
              .opacity(0.9)
              .maxLines(1)
              .textOverflow({ overflow: TextOverflow.Ellipsis })
          }

          // 用户等级或状态
          Row({ space: 8 }) {
            Text('普通用户')
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(Constants.COLORS.WHITE)
              .backgroundColor($r('app.color.overlay_medium'))
              .padding({ left: '8vp', right: '8vp', top: '2vp', bottom: '2vp' })
              .borderRadius(Constants.BORDER_RADIUS.SMALL)

            if (this.userInfo.verify_status === 'verified') {
              Row({ space: 4 }) {
                Text('✅')
                  .width(12)
                  .height(12)
                  .fontColor(Constants.COLORS.SUCCESS)
                Text('已认证')
                  .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
                  .fontColor(Constants.COLORS.SUCCESS)
              }
              .backgroundColor($r('sys.color.ohos_id_color_background'))
              .padding({ left: '6vp', right: '6vp', top: '2vp', bottom: '2vp' })
              .borderRadius(Constants.BORDER_RADIUS.SMALL)
            }
          }
        }
        .alignItems(HorizontalAlign.Start)
        .layoutWeight(1)

        // 编辑按钮
        Text('✏️')
          .width(20)
          .height(20)
          .fontColor(Constants.COLORS.WHITE)
          .onClick(() => {
            this.getUIContext().getRouter().pushUrl({ url: 'pages/EditProfilePage' });
          })
      }
      .width('100%')
      .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
      .alignItems(VerticalAlign.Center)
    } else {
      // 未登录状态
      Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
        Text('👤')
          .width(this.deviceUtils.isTablet() ? 80 : 64)
          .height(this.deviceUtils.isTablet() ? 80 : 64)
          .fontColor(Constants.COLORS.WHITE)
          .opacity(0.8)

        Text('登录后享受更多功能')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(Constants.COLORS.WHITE)
          .opacity(0.9)

        Button('立即登录')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor(Constants.COLORS.PRIMARY)
          .backgroundColor(Constants.COLORS.WHITE)
          .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
          .padding({ left: '24vp', right: '24vp', top: '8vp', bottom: '8vp' })
          .onClick(() => this.navigateToLogin())
      }
      .width('100%')
      .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
    }
  }

  /**
   * 用户统计数据
   */
  @Builder
  private UserStats() {
    if (this.isLoggedIn) {
      Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) }) {
        Column({ space: 4 }) {
          Text(this.downloadedAppsCount.toString())
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontWeight(FontWeight.Bold)
            .fontColor(Constants.COLORS.PRIMARY)
          Text('已下载')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
        }
        .alignItems(HorizontalAlign.Center)
        .onClick(() => this.navigateToMyApps())

        Column({ space: 4 }) {
          Text(this.favoriteAppsCount.toString())
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontWeight(FontWeight.Bold)
            .fontColor(Constants.COLORS.ERROR)
          Text('收藏')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
        }
        .alignItems(HorizontalAlign.Center)
        .onClick(() => this.navigateToFavorites())

        Column({ space: 4 }) {
          Text(this.reviewsCount.toString())
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontWeight(FontWeight.Bold)
            .fontColor(Constants.COLORS.SUCCESS)
          Text('评论')
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_SECONDARY)
        }
        .alignItems(HorizontalAlign.Center)
        .onClick(() => this.navigateToMyReviews())
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceAround)
      .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
      .backgroundColor(Constants.COLORS.WHITE)
      .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
      .margin({ left: 16, right: 16, top: -20 })
    }
  }

  /**
   * 菜单项
   */
  @Builder
  private MenuItem(icon: Resource | string, title: string, subtitle?: string, onClick?: () => void) {
    Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
      if (typeof icon === 'string') {
         Text(icon)
           .fontSize(20)
       } else {
         Image(icon)
           .width(24)
           .height(24)
           .fillColor($r('sys.color.ohos_id_color_text_secondary'))
       }

      Column({ space: 2 }) {
        Text(title)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
          .fontWeight(FontWeight.Medium)

        if (subtitle) {
          Text(subtitle)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor($r('sys.color.ohos_id_color_text_hint'))
        }
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)

      Text('▶️')
        .width(16)
        .height(16)
        .fontColor($r('sys.color.ohos_id_color_text_hint'))
    }
    .width('100%')
    .height(this.deviceUtils.isTablet() ? 64 : 56)
    .padding({ left: '16vp', right: '16vp' })
    .onClick(() => onClick?.())
  }

  /**
   * 功能菜单
   */
  @Builder
  private FunctionMenu() {
    Column() {
      if (this.isLoggedIn) {
        this.MenuItem('📥', '我的下载', '管理已下载的应用', (): void => this.navigateToMyApps())
        Divider().color($r('sys.color.ohos_id_color_list_separator')).margin({ left: 56 })
        
        this.MenuItem('❤️', '我的收藏', '查看收藏的应用', (): void => this.navigateToFavorites())
        Divider().color($r('sys.color.ohos_id_color_list_separator')).margin({ left: 56 })
        
        this.MenuItem('💬', '我的评论', '查看发表的评论', (): void => this.navigateToMyReviews())
        Divider().color(Constants.COLORS.BORDER).margin({ left: 56 })
      }

      this.MenuItem($r('app.media.ic_history'), '浏览历史', '查看最近浏览的应用', (): void => {
        this.getUIContext().getRouter().pushUrl({ url: 'pages/HistoryPage' });
      })
      Divider().color(Constants.COLORS.BORDER).margin({ left: 56 })

      if (this.isLoggedIn) {
        this.MenuItem('🔔', '通知中心', '查看系统通知和消息', (): void => {
          this.getUIContext().getRouter().pushUrl({ url: 'pages/NotificationPage' });
        })
        Divider().color(Constants.COLORS.BORDER).margin({ left: 56 })
      }

      this.MenuItem('⚙️', '设置', '应用设置和偏好', (): void => this.navigateToSettings())
      Divider().color(Constants.COLORS.BORDER).margin({ left: 56 })

      this.MenuItem('📊', '系统状态', '查看系统健康状态', (): void => {
        this.getUIContext().getRouter().pushUrl({ url: 'pages/SystemStatusPage' });
      })
      Divider().color(Constants.COLORS.BORDER).margin({ left: 56 })

      this.MenuItem('❓', '帮助与反馈', '获取帮助或提供反馈', (): void => {
        this.getUIContext().getRouter().pushUrl({ url: 'pages/HelpPage' });
      })
      Divider().color(Constants.COLORS.BORDER).margin({ left: 56 })

      this.MenuItem('ℹ️', '关于我们', '了解应用信息', (): void => {
        this.getUIContext().getRouter().pushUrl({ url: 'pages/AboutPage' });
      })

      if (this.isLoggedIn) {
        Divider().color(Constants.COLORS.BORDER).margin({ left: 56 })
        this.MenuItem('🚪', '退出登录', '', (): void => this.logout())
      }
    }
    .width('100%')
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .margin({ left: 16, right: 16, top: 16 })
  }

  build() {
    Column() {
      if (this.loadingState === LoadingState.LOADING) {
        LoadingView({ state: LoadingState.LOADING })
          .layoutWeight(1)
      } else if (this.loadingState === LoadingState.ERROR) {
        LoadingView({ 
          state: LoadingState.ERROR,
          onRetry: (): Promise<void> => this.loadUserProfile()
        })
          .layoutWeight(1)
      } else {
        Scroll() {
          Column() {
            // 顶部背景和用户信息
            Column() {
              // 状态栏占位
              Column()
                .height(44)

              // 用户头像和信息
              this.UserHeader()
            }
            .width('100%')
            .linearGradient({
              direction: GradientDirection.Bottom,
              colors: [[$r('sys.color.ohos_id_color_primary'), 0], [$r('sys.color.ohos_id_color_primary'), 1]]
            })

            // 用户统计数据
            this.UserStats()

            // 功能菜单
            this.FunctionMenu()

            // 底部间距
            Column()
              .height(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE))
          }
        }
        .scrollable(ScrollDirection.Vertical)
        .scrollBar(BarState.Auto)
        .layoutWeight(1)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
  }
}

export { ProfilePage };