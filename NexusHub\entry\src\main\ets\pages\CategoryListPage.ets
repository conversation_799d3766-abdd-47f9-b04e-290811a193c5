import { CategoryModel, CategoryListResponse } from '../models/Category';
import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { LoadingView, LoadingState } from '../components/LoadingView';
import { SearchBar } from '../components/SearchBar';
import { router } from '@kit.ArkUI';
import { preferences } from '@kit.ArkData';
import { hilog } from '@kit.PerformanceAnalysisKit';
// getContext is deprecated, use this.getUIContext().getHostContext() instead

/**
 * 分类列表页面
 */
@Entry
@Component
export struct CategoryListPage {
  @State categories: CategoryModel[] = [];
  @State loadingState: LoadingState = LoadingState.LOADING;
  @State searchKeyword: string = '';
  @State filteredCategories: CategoryModel[] = [];

  private deviceUtils = DeviceUtils.getInstance();
  private apiService = ApiService.getInstance();

  aboutToAppear() {
    this.loadCategories();
  }

  /**
   * 检查并设置认证token
   */
  private async checkAndSetAuthToken(): Promise<void> {
    try {
      const context = this.getUIContext().getHostContext();
      const options: preferences.Options = { name: 'user_data' };
      const dataPreferences = preferences.getPreferencesSync(context, options);
      const token = dataPreferences.getSync('token', '') as string;
      
      if (token) {
        this.apiService.setAuthToken(token);
      }
    } catch (error) {
      hilog.error(0x0000, 'CategoryListPage', '检查登录状态失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 加载分类列表
   */
  private async loadCategories() {
    try {
      this.loadingState = LoadingState.LOADING;

      // 检查登录状态并设置token（可选）
      await this.checkAndSetAuthToken();

      // 使用公开接口获取分类列表
      const response: CategoryListResponse = await this.apiService.getAppCategories();

      hilog.info(0x0000, 'CategoryListPage', '分类数据响应: code=%{public}d, dataLength=%{public}d',
        response.code, response.data?.length || 0);

      // 改进的数据验证逻辑
      if (response && response.data && Array.isArray(response.data)) {
        this.categories = response.data;
        this.filteredCategories = this.categories;

        // 根据数据长度设置状态
        if (this.categories.length > 0) {
          this.loadingState = LoadingState.SUCCESS;
          hilog.info(0x0000, 'CategoryListPage', '成功加载 %{public}d 个分类', this.categories.length);
        } else {
          this.loadingState = LoadingState.EMPTY;
          hilog.info(0x0000, 'CategoryListPage', '分类数据为空');
        }
      } else {
        // 数据格式不正确
        hilog.error(0x0000, 'CategoryListPage', '分类数据格式错误: %{public}s', JSON.stringify(response));
        this.loadingState = LoadingState.ERROR;
      }
    } catch (error) {
      hilog.error(0x0000, 'CategoryListPage', '加载分类列表失败: %{public}s', JSON.stringify(error));
      this.loadingState = LoadingState.ERROR;
    }
  }

  /**
   * 搜索分类
   */
  private searchCategories(keyword: string): void {
    this.searchKeyword = keyword;
    if (keyword.trim() === '') {
      this.filteredCategories = this.categories;
    } else {
      this.filteredCategories = this.categories.filter(category => 
        category.name.toLowerCase().includes(keyword.toLowerCase()) ||
        (category.description && category.description.toLowerCase().includes(keyword.toLowerCase()))
      );
    }
  }

  /**
   * 跳转到分类页面
   */
  private navigateToCategory(categoryId: string, categoryName: string) {
    this.getUIContext().getRouter().pushUrl({
      url: 'pages/CategoryPage',
      params: { categoryId, categoryName }
    });
  }

  /**
   * 搜索栏
   */
  @Builder
  private SearchBar() {
    Row() {
      Image($r('app.media.icons'))
        .width(20)
        .height(20)
        .fillColor(Constants.COLORS.TEXT_HINT)
        .margin({ left: 12, right: 8 })

      TextInput({ placeholder: '搜索分类', text: this.searchKeyword })
        .layoutWeight(1)
        .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
        .fontColor(Constants.COLORS.TEXT_PRIMARY)
        .backgroundColor(Color.Transparent)
        .border({ width: 0 })
        .onChange((value: string) => {
          this.searchCategories(value);
        })

      if (this.searchKeyword.length > 0) {
        Image($r('app.media.icons'))
          .width(20)
          .height(20)
          .fillColor(Constants.COLORS.TEXT_HINT)
          .margin({ right: 12 })
          .onClick(() => {
            this.searchKeyword = '';
            this.searchCategories('');
          })
      }
    }
    .width('100%')
    .height(40)
    .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .margin({ left: 16, right: 16, top: 8, bottom: 8 })
  }

  /**
   * 分类项
   */
  @Builder
  private CategoryItem(category: CategoryModel) {
    Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM) }) {
      // 分类图标
      Image(category.icon || Constants.PLACEHOLDER_IMAGE)
        .width(this.deviceUtils.isTablet() ? 56 : 48)
        .height(this.deviceUtils.isTablet() ? 56 : 48)
        .objectFit(ImageFit.Cover)
        .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
        .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)

      // 分类信息
      Column({ space: 4 }) {
        Row() {
          Text(category.name)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
            .fontWeight(FontWeight.Medium)
            .fontColor($r('sys.color.ohos_id_color_text_primary'))
            .layoutWeight(1)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })

          Text(`${category.app_count || 0}个应用`)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_HINT)
        }
        .width('100%')

        if (category.description) {
          Text(category.description)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
            .fontColor($r('sys.color.ohos_id_color_text_secondary'))
            .maxLines(2)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .lineHeight(20)
        }

        // 热门标签已移除
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)

      // 箭头
      Image($r('app.media.icons'))
        .width(16)
        .height(16)
        .fillColor(Constants.COLORS.TEXT_HINT)
    }
    .width('100%')
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .backgroundColor($r('sys.color.ohos_id_color_background'))
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .margin({ left: 16, right: 16, bottom: 8 })
    .onClick(() => this.navigateToCategory(category.id.toString(), category.name))
  }

  /**
   * 分类网格（平板设备）
   */
  @Builder
  private CategoryGrid() {
    Grid() {
      ForEach(this.filteredCategories, (category: CategoryModel) => {
        GridItem() {
          Column({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.SMALL) }) {
            // 分类图标
            Stack() {
              Image(category.icon || Constants.PLACEHOLDER_IMAGE)
                .width(64)
                .height(64)
                .objectFit(ImageFit.Cover)
                .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
                .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)

              // 热门标签已移除
            }
            .alignContent(Alignment.TopEnd)

            // 分类名称
            Text(category.name)
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.NORMAL))
              .fontWeight(FontWeight.Medium)
              .fontColor(Constants.COLORS.TEXT_PRIMARY)
              .maxLines(1)
              .textOverflow({ overflow: TextOverflow.Ellipsis })
              .textAlign(TextAlign.Center)

            // 应用数量
            Text(`${category.app_count || 0}个应用`)
              .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
              .fontColor(Constants.COLORS.TEXT_HINT)
              .textAlign(TextAlign.Center)
          }
          .width('100%')
          .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
          .backgroundColor($r('sys.color.ohos_id_color_background'))
          .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
          .justifyContent(FlexAlign.Center)
          .alignItems(HorizontalAlign.Center)
          .onClick(() => this.navigateToCategory(category.id.toString(), category.name))
        }
      })
    }
    .columnsTemplate('1fr 1fr 1fr 1fr')
    .rowsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .columnsGap(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .scrollBar(BarState.Auto)
  }

  /**
   * 分类列表（手机设备）
   */
  @Builder
  private CategoryList() {
    List({ space: 0 }) {
      ForEach(this.filteredCategories, (category: CategoryModel) => {
        ListItem() {
          this.CategoryItem(category)
        }
      })
    }
    .scrollBar(BarState.Auto)
  }

  /**
   * 统计信息
   */
  @Builder
  private StatsInfo() {
    Row({ space: this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.LARGE) }) {
      Column({ space: 4 }) {
        Text((this.categories?.length || 0).toString())
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.PRIMARY)
        Text('分类总数')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
      }
      .alignItems(HorizontalAlign.Center)

      Column({ space: 4 }) {
        Text(this.getTotalApps().toString())
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.SUCCESS)
        Text('应用总数')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
      }
      .alignItems(HorizontalAlign.Center)

      Column({ space: 4 }) {
        Text(this.getHotCategories().toString())
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.ERROR)
        Text('热门分类')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
      }
      .alignItems(HorizontalAlign.Center)
    }
    .width('100%')
    .justifyContent(FlexAlign.SpaceAround)
    .padding(this.deviceUtils.getAdaptiveSpacing(Constants.SPACING.MEDIUM))
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .margin({ left: 16, right: 16, top: 8, bottom: 8 })
  }

  /**
   * 获取应用总数
   */
  private getTotalApps(): number {
    return this.categories.reduce((total, category) => total + (category.app_count || 0), 0);
  }

  /**
   * 获取热门分类数量
   */
  private getHotCategories(): number {
    return 0; // 热门分类功能已移除
  }

  /**
   * 空状态视图
   */
  @Builder
  private EmptyView() {
    if (this.searchKeyword.length > 0 && (!this.filteredCategories || this.filteredCategories.length === 0)) {
      LoadingView({ 
        state: LoadingState.EMPTY,
        message: `未找到包含"${this.searchKeyword}"的分类`
      })
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Text('应用分类')
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE))
          .fontWeight(FontWeight.Medium)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .width('100%')
          .textAlign(TextAlign.Center)
      }
      .width('100%')
      .height(56)
      .padding({ left: '16vp', right: '16vp' })
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .backgroundColor(Constants.COLORS.WHITE)

      if (this.loadingState === LoadingState.LOADING) {
        LoadingView({ state: LoadingState.LOADING })
          .layoutWeight(1)
      } else if (this.loadingState === LoadingState.ERROR) {
        LoadingView({ 
          state: LoadingState.ERROR,
          onRetry: (): Promise<void> => this.loadCategories()
        })
          .layoutWeight(1)
      } else if (this.loadingState === LoadingState.EMPTY) {
        LoadingView({ 
          state: LoadingState.EMPTY,
          message: '暂无分类数据'
        })
          .layoutWeight(1)
      } else {
        Column() {
          // 搜索栏
          this.SearchBar()

          // 统计信息
          this.StatsInfo()

          // 分类列表/网格
          if (this.filteredCategories && this.filteredCategories.length > 0) {
            if (this.deviceUtils.isTablet()) {
              this.CategoryGrid()
            } else {
              this.CategoryList()
            }
          } else {
            this.EmptyView()
          }
        }
        .layoutWeight(1)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
  }
}

