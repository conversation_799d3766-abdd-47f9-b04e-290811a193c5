"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { useState } from "react";
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  message,
  Avatar,
  Tag,
  Badge,
  Dropdown,
  Grid,
  DatePicker
} from "antd";
import {
  SearchOutlined,
  UserOutlined,
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
  MoreOutlined
} from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-components";
import { useRequest } from "ahooks";
import { history, useAccess } from "@umijs/max";
import { getUserList } from "@/services/admin";
import { getSearchUsers } from "@/services/ant-design-pro/sousuo";
import {
  putAdminUsersIdRole,
  putAdminUsersIdStatus
} from "@/services/ant-design-pro/guanliyuan";
import PermissionWrapper, { PermissionButton } from "@/components/PermissionWrapper";
const { useBreakpoint } = Grid;
const { RangePicker } = DatePicker;
const UserList = () => {
  const screens = useBreakpoint();
  const access = useAccess();
  const [searchParams, setSearchParams] = useState({
    page: 1,
    pageSize: 10,
    keyword: "",
    role: "",
    status: ""
  });
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [useSearchAPI, setUseSearchAPI] = useState(false);
  const isMobile = !screens.md;
  const isTablet = screens.md && !screens.lg;
  const shouldUseSearchAPI = !!(searchParams.keyword?.trim() || searchParams.role || searchParams.status);
  const { data, loading, run } = useRequest(
    async () => {
      if (shouldUseSearchAPI) {
        const response = await getSearchUsers({
          keyword: searchParams.keyword,
          role: searchParams.role,
          status: searchParams.status,
          page: searchParams.page.toString(),
          page_size: searchParams.pageSize.toString()
        });
        if (response.code === 200 && response.data) {
          const convertedUsers = response.data.users?.map((user) => {
            return {
              id: user.id?.toString() || "",
              username: user.username || "",
              nickname: user.developer_name || user.username || "",
              avatar: "",
              // UserDocument中没有avatar字段
              email: user.email || "",
              phone: user.contact_phone || user.phone || "",
              role: user.role || "",
              status: user.status || "",
              registerTime: user.created_at || "",
              lastLoginTime: user.last_login_at || user.created_at || ""
            };
          }) || [];
          const convertedData = {
            data: convertedUsers,
            total: response.data.total || 0,
            code: response.code,
            message: response.message
          };
          return convertedData;
        }
        return {
          data: [],
          total: 0,
          code: response.code || 500,
          message: response.message || "\u641C\u7D22\u5931\u8D25"
        };
      } else {
        return getUserList({
          page: searchParams.page,
          pageSize: searchParams.pageSize
        });
      }
    },
    {
      refreshDeps: [searchParams, shouldUseSearchAPI]
    }
  );
  const handleSearch = (values) => {
    setSearchParams({
      ...searchParams,
      page: 1,
      // 重置到第一页
      keyword: values.keyword || "",
      role: values.role || "",
      status: values.status || ""
    });
    setTimeout(() => {
      run();
    }, 100);
  };
  const handleReset = () => {
    searchForm.resetFields();
    setSearchParams({
      page: 1,
      pageSize: 10,
      keyword: "",
      role: "",
      status: ""
    });
    setTimeout(() => {
      run();
    }, 100);
  };
  const handleViewDetail = (id) => {
    history.push(`/user-management/detail/${id}`);
  };
  const handleEdit = (id) => {
    const user = data?.data?.find((item) => item.id === id);
    if (user) {
      setCurrentUser(user);
      form.setFieldsValue({
        username: user.username,
        nickname: user.nickname,
        email: user.email,
        phone: user.phone,
        role: user.role,
        status: user.status
      });
      setEditModalVisible(true);
    }
  };
  const handleEditSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (!currentUser) return;
      if (values.role !== currentUser.role) {
        await putAdminUsersIdRole(
          { id: currentUser.id },
          { role: values.role }
        );
      }
      if (values.status !== currentUser.status) {
        await putAdminUsersIdStatus(
          { id: currentUser.id },
          { status: values.status }
        );
      }
      message.success(`\u7528\u6237 ${currentUser?.nickname || currentUser?.username} \u4FE1\u606F\u66F4\u65B0\u6210\u529F`);
      setEditModalVisible(false);
      run();
    } catch (error) {
      console.error("\u66F4\u65B0\u7528\u6237\u4FE1\u606F\u5931\u8D25:", error);
      message.error("\u66F4\u65B0\u7528\u6237\u4FE1\u606F\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5");
    }
  };
  const handleLock = (id, isLocked) => {
    message.success(`${isLocked ? "\u89E3\u9501" : "\u9501\u5B9A"}\u7528\u6237 ${id} \u6210\u529F`);
  };
  const getRoleTag = (role) => {
    switch (role) {
      case "admin":
        return /* @__PURE__ */ jsx(Tag, { color: "red", children: "\u7BA1\u7406\u5458" });
      case "developer":
        return /* @__PURE__ */ jsx(Tag, { color: "blue", children: "\u5F00\u53D1\u8005" });
      case "user":
        return /* @__PURE__ */ jsx(Tag, { color: "green", children: "\u666E\u901A\u7528\u6237" });
      default:
        return /* @__PURE__ */ jsx(Tag, { children: role });
    }
  };
  const getStatusBadge = (status) => {
    switch (status) {
      case "active":
        return /* @__PURE__ */ jsx(Badge, { status: "success", text: "\u6D3B\u8DC3" });
      case "inactive":
        return /* @__PURE__ */ jsx(Badge, { status: "default", text: "\u975E\u6D3B\u8DC3" });
      case "banned":
        return /* @__PURE__ */ jsx(Badge, { status: "error", text: "\u5DF2\u7981\u7528" });
      default:
        return /* @__PURE__ */ jsx(Badge, { status: "processing", text: status });
    }
  };
  const columns = [
    {
      title: "\u7528\u6237ID",
      dataIndex: "id",
      key: "id",
      width: 80,
      hidden: isMobile
      // 移动端隐藏ID列
    },
    {
      title: "\u7528\u6237\u4FE1\u606F",
      key: "userInfo",
      render: (_, record) => /* @__PURE__ */ jsxs("div", { style: { display: "flex", alignItems: "center" }, children: [
        /* @__PURE__ */ jsx(
          Avatar,
          {
            src: record.avatar,
            size: isMobile ? "default" : "large",
            style: { marginRight: 8, flexShrink: 0 }
          }
        ),
        /* @__PURE__ */ jsxs("div", { style: { minWidth: 0, flex: 1 }, children: [
          /* @__PURE__ */ jsx("div", { style: { fontWeight: "bold", marginBottom: 2 }, children: record.nickname }),
          /* @__PURE__ */ jsx("div", { style: { fontSize: 12, color: "#999", marginBottom: 2 }, children: record.username }),
          isMobile && /* @__PURE__ */ jsxs("div", { style: { fontSize: 11, color: "#666" }, children: [
            /* @__PURE__ */ jsx("div", { children: record.email }),
            record.phone && /* @__PURE__ */ jsx("div", { children: record.phone })
          ] }),
          isMobile && /* @__PURE__ */ jsxs(Space, { size: "small", style: { marginTop: 4 }, children: [
            getRoleTag(record.role),
            getStatusBadge(record.status)
          ] })
        ] })
      ] })
    },
    // 桌面端显示的详细列
    ...!isMobile ? [
      {
        title: "\u8054\u7CFB\u65B9\u5F0F",
        key: "contact",
        render: (_, record) => /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("div", { children: record.email }),
          /* @__PURE__ */ jsx("div", { children: record.phone })
        ] })
      },
      {
        title: "\u89D2\u8272",
        dataIndex: "role",
        key: "role",
        render: (role) => getRoleTag(role)
      },
      {
        title: "\u72B6\u6001",
        dataIndex: "status",
        key: "status",
        render: (status) => getStatusBadge(status)
      },
      {
        title: "\u6CE8\u518C\u65F6\u95F4",
        dataIndex: "registerTime",
        key: "registerTime",
        sorter: (a, b) => a.registerTime.localeCompare(b.registerTime)
      },
      {
        title: "\u6700\u540E\u767B\u5F55",
        dataIndex: "lastLoginTime",
        key: "lastLoginTime",
        sorter: (a, b) => a.lastLoginTime.localeCompare(b.lastLoginTime)
      }
    ] : [],
    {
      title: "\u64CD\u4F5C",
      key: "action",
      width: isMobile ? 80 : 200,
      fixed: isMobile ? "right" : void 0,
      render: (_, record) => /* @__PURE__ */ jsxs(Space, { size: "small", direction: isMobile ? "vertical" : "horizontal", children: [
        /* @__PURE__ */ jsx(
          Button,
          {
            type: "link",
            size: "small",
            icon: /* @__PURE__ */ jsx(UserOutlined, {}),
            onClick: () => handleViewDetail(record.id),
            children: isMobile ? "" : "\u8BE6\u60C5"
          }
        ),
        /* @__PURE__ */ jsx(PermissionButton, { permission: "canEditUser", children: /* @__PURE__ */ jsx(
          Button,
          {
            type: "link",
            size: "small",
            icon: /* @__PURE__ */ jsx(EditOutlined, {}),
            onClick: () => handleEdit(record.id),
            children: isMobile ? "" : "\u7F16\u8F91"
          }
        ) }),
        /* @__PURE__ */ jsx(PermissionButton, { permission: "canEditUser", children: /* @__PURE__ */ jsx(
          Dropdown,
          {
            menu: {
              items: [
                {
                  key: "lock",
                  label: record.status === "banned" ? "\u89E3\u9501\u7528\u6237" : "\u9501\u5B9A\u7528\u6237",
                  icon: record.status === "banned" ? /* @__PURE__ */ jsx(UnlockOutlined, {}) : /* @__PURE__ */ jsx(LockOutlined, {}),
                  onClick: () => handleLock(record.id, record.status === "banned")
                },
                {
                  key: "delete",
                  label: "\u5220\u9664\u7528\u6237",
                  danger: true,
                  disabled: !access.canDeleteUser
                }
              ]
            },
            children: /* @__PURE__ */ jsx(Button, { type: "link", size: "small", icon: /* @__PURE__ */ jsx(MoreOutlined, {}) })
          }
        ) })
      ] })
    }
  ];
  return /* @__PURE__ */ jsx(PermissionWrapper, { permission: "canViewUserList", children: /* @__PURE__ */ jsxs(PageContainer, { children: [
    /* @__PURE__ */ jsxs(Card, { children: [
      /* @__PURE__ */ jsxs(
        Form,
        {
          form: searchForm,
          onFinish: handleSearch,
          layout: isMobile ? "vertical" : "inline",
          style: { marginBottom: 16 },
          children: [
            /* @__PURE__ */ jsx(Form.Item, { name: "keyword", style: { marginBottom: isMobile ? 12 : 16 }, children: /* @__PURE__ */ jsx(
              Input,
              {
                placeholder: "\u641C\u7D22\u7528\u6237\u540D\u3001\u90AE\u7BB1\u6216\u5F00\u53D1\u8005\u4FE1\u606F",
                prefix: /* @__PURE__ */ jsx(SearchOutlined, {}),
                style: { width: isMobile ? "100%" : 250 },
                allowClear: true
              }
            ) }),
            /* @__PURE__ */ jsx(Form.Item, { name: "role", style: { marginBottom: isMobile ? 12 : 16 }, children: /* @__PURE__ */ jsxs(
              Select,
              {
                placeholder: "\u9009\u62E9\u89D2\u8272",
                style: { width: isMobile ? "100%" : 120 },
                allowClear: true,
                children: [
                  /* @__PURE__ */ jsx(Select.Option, { value: "admin", children: "\u7BA1\u7406\u5458" }),
                  /* @__PURE__ */ jsx(Select.Option, { value: "developer", children: "\u5F00\u53D1\u8005" }),
                  /* @__PURE__ */ jsx(Select.Option, { value: "user", children: "\u666E\u901A\u7528\u6237" })
                ]
              }
            ) }),
            /* @__PURE__ */ jsx(Form.Item, { name: "status", style: { marginBottom: isMobile ? 12 : 16 }, children: /* @__PURE__ */ jsxs(
              Select,
              {
                placeholder: "\u9009\u62E9\u72B6\u6001",
                style: { width: isMobile ? "100%" : 120 },
                allowClear: true,
                children: [
                  /* @__PURE__ */ jsx(Select.Option, { value: "active", children: "\u6B63\u5E38" }),
                  /* @__PURE__ */ jsx(Select.Option, { value: "inactive", children: "\u672A\u6FC0\u6D3B" }),
                  /* @__PURE__ */ jsx(Select.Option, { value: "banned", children: "\u5DF2\u5C01\u7981" })
                ]
              }
            ) }),
            /* @__PURE__ */ jsx(Form.Item, { style: { marginBottom: isMobile ? 12 : 16 }, children: /* @__PURE__ */ jsxs(Space, { direction: isMobile ? "horizontal" : "horizontal", style: { width: isMobile ? "100%" : "auto" }, children: [
              /* @__PURE__ */ jsx(
                Button,
                {
                  type: "primary",
                  htmlType: "submit",
                  icon: /* @__PURE__ */ jsx(SearchOutlined, {}),
                  style: { flex: isMobile ? 1 : "none" },
                  children: "\u641C\u7D22"
                }
              ),
              /* @__PURE__ */ jsx(
                Button,
                {
                  onClick: handleReset,
                  style: { flex: isMobile ? 1 : "none" },
                  children: "\u91CD\u7F6E"
                }
              )
            ] }) })
          ]
        }
      ),
      /* @__PURE__ */ jsx("div", { style: { marginBottom: 16 }, children: /* @__PURE__ */ jsxs(
        Space,
        {
          direction: isMobile ? "vertical" : "horizontal",
          style: { width: isMobile ? "100%" : "auto" },
          size: isMobile ? 8 : "small",
          children: [
            /* @__PURE__ */ jsx(PermissionButton, { permission: "canEditUser", children: /* @__PURE__ */ jsx(
              Button,
              {
                type: "primary",
                style: { width: isMobile ? "100%" : "auto" },
                children: "\u65B0\u589E\u7528\u6237"
              }
            ) }),
            /* @__PURE__ */ jsx(PermissionButton, { permission: "canEditUser", children: /* @__PURE__ */ jsx(Button, { style: { width: isMobile ? "100%" : "auto" }, children: "\u6279\u91CF\u64CD\u4F5C" }) }),
            /* @__PURE__ */ jsx(Button, { style: { width: isMobile ? "100%" : "auto" }, children: "\u5BFC\u51FA\u6570\u636E" })
          ]
        }
      ) }),
      /* @__PURE__ */ jsx(
        Table,
        {
          columns,
          dataSource: shouldUseSearchAPI ? data?.data || [] : data?.data?.data || [],
          loading,
          rowKey: "id",
          scroll: {
            x: isMobile ? 800 : void 0,
            y: isMobile ? 400 : void 0
          },
          size: isMobile ? "small" : "middle",
          pagination: {
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: shouldUseSearchAPI ? data?.total || 0 : data?.data?.total || 0,
            showSizeChanger: !isMobile,
            showQuickJumper: !isMobile,
            simple: isMobile,
            showTotal: !isMobile ? (total, range) => `\u7B2C ${range[0]}-${range[1]} \u6761/\u603B\u5171 ${total} \u6761` : void 0,
            onChange: (page, pageSize) => {
              setSearchParams({
                ...searchParams,
                page,
                pageSize: pageSize || 10
              });
            },
            position: [isMobile ? "bottomCenter" : "bottomRight"]
          }
        }
      )
    ] }),
    /* @__PURE__ */ jsx(
      Modal,
      {
        title: "\u7F16\u8F91\u7528\u6237\u4FE1\u606F",
        open: editModalVisible,
        onOk: handleEditSubmit,
        onCancel: () => setEditModalVisible(false),
        width: isMobile ? "95%" : 600,
        centered: isMobile,
        children: /* @__PURE__ */ jsxs(
          Form,
          {
            form,
            layout: "vertical",
            initialValues: {
              status: "active",
              role: "user"
            },
            children: [
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  label: "\u7528\u6237\u540D",
                  name: "username",
                  rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u7528\u6237\u540D" }],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u7528\u6237\u540D" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  label: "\u6635\u79F0",
                  name: "nickname",
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u6635\u79F0" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  label: "\u90AE\u7BB1",
                  name: "email",
                  rules: [
                    { required: true, message: "\u8BF7\u8F93\u5165\u90AE\u7BB1" },
                    { type: "email", message: "\u8BF7\u8F93\u5165\u6709\u6548\u7684\u90AE\u7BB1\u5730\u5740" }
                  ],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u90AE\u7BB1" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  label: "\u624B\u673A\u53F7",
                  name: "phone",
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u624B\u673A\u53F7" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  label: "\u89D2\u8272",
                  name: "role",
                  rules: [{ required: true, message: "\u8BF7\u9009\u62E9\u89D2\u8272" }],
                  children: /* @__PURE__ */ jsxs(Select, { placeholder: "\u8BF7\u9009\u62E9\u89D2\u8272", children: [
                    /* @__PURE__ */ jsx(Select.Option, { value: "user", children: "\u666E\u901A\u7528\u6237" }),
                    /* @__PURE__ */ jsx(Select.Option, { value: "developer", children: "\u5F00\u53D1\u8005" }),
                    access.canManageRole && /* @__PURE__ */ jsx(Select.Option, { value: "admin", children: "\u7BA1\u7406\u5458" })
                  ] })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  label: "\u72B6\u6001",
                  name: "status",
                  rules: [{ required: true, message: "\u8BF7\u9009\u62E9\u72B6\u6001" }],
                  children: /* @__PURE__ */ jsxs(Select, { placeholder: "\u8BF7\u9009\u62E9\u72B6\u6001", children: [
                    /* @__PURE__ */ jsx(Select.Option, { value: "active", children: "\u6B63\u5E38" }),
                    /* @__PURE__ */ jsx(Select.Option, { value: "inactive", children: "\u672A\u6FC0\u6D3B" }),
                    /* @__PURE__ */ jsx(Select.Option, { value: "banned", children: "\u5DF2\u5C01\u7981" })
                  ] })
                }
              )
            ]
          }
        )
      }
    )
  ] }) });
};
export default UserList;
