"use strict";
import { jsx } from "react/jsx-runtime";
import React from "react";
import { Provider } from "./";
import { models as rawModels } from "./model";
function ProviderWrapper(props) {
  const models = React.useMemo(() => {
    return Object.keys(rawModels).reduce((memo, key) => {
      memo[rawModels[key].namespace] = rawModels[key].model;
      return memo;
    }, {});
  }, []);
  return /* @__PURE__ */ jsx(Provider, { models, ...props, children: props.children });
}
export function dataflowProvider(container, opts) {
  return /* @__PURE__ */ jsx(ProviderWrapper, { ...opts, children: container });
}
