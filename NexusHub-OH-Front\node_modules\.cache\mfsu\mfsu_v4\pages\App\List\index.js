"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { PageContainer } from "@ant-design/pro-components";
import { Card, Table, Button, Tag, Space, Input, Select, DatePicker, Badge, Avatar, Tooltip, message, Modal, Dropdown, Form } from "antd";
import { SearchOutlined, ReloadOutlined, EyeOutlined, EditOutlined, DeleteOutlined, DownloadOutlined, PlusOutlined, AppstoreOutlined, MoreOutlined, ExportOutlined, FilterOutlined } from "@ant-design/icons";
import { useState } from "react";
import { useRequest, history, useModel } from "@umijs/max";
import { getAppList, submitAppForReview } from "@/services/app";
import { getDeveloperApps } from "@/services/developer";
import PermissionWrapper, { PermissionButton } from "@/components/PermissionWrapper";
import { canEditApp, canDeleteApp } from "@/utils/permission";
const { RangePicker } = DatePicker;
const { Option } = Select;
const fetchAppList = async (params, userRole) => {
  console.log("Fetching app list with params:", params, "userRole:", userRole);
  try {
    let response;
    const apiParams = {
      page: params.page || 1,
      page_size: params.pageSize || 10
    };
    if (params.keyword) apiParams.keyword = params.keyword;
    if (params.category) apiParams.category = params.category;
    if (userRole === "developer") {
      if (params.status) apiParams.status = params.status;
      response = await getDeveloperApps(apiParams);
    } else if (userRole === "admin") {
      if (params.status && params.status !== void 0 && params.status !== "") {
        apiParams.status = params.status;
      }
      response = await getAppList(apiParams);
    } else {
      apiParams.status = "approved";
      response = await getAppList(apiParams);
    }
    console.log("API\u54CD\u5E94\u6570\u636E:", response);
    if (response && response.code === 200) {
      return {
        data: response.data || [],
        total: response.total || 0,
        page: response.page || 1,
        pageSize: response.page_size || 10
      };
    }
    return { data: [], total: 0 };
  } catch (error) {
    console.error("\u83B7\u53D6\u5E94\u7528\u5217\u8868\u5931\u8D25:", error);
    message.error("\u83B7\u53D6\u5E94\u7528\u5217\u8868\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
    return { data: [], total: 0 };
  }
};
const AppList = () => {
  const { initialState } = useModel("@@initialState");
  const currentUser = initialState?.currentUser;
  const [searchParams, setSearchParams] = useState({
    page: 1,
    pageSize: 10,
    keyword: "",
    category: "",
    status: void 0,
    // 默认不筛选状态，显示全部应用
    developer: "",
    dateRange: null,
    minDownloads: "",
    minRating: ""
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [rejectReasonModalVisible, setRejectReasonModalVisible] = useState(false);
  const [currentRejectReason, setCurrentRejectReason] = useState("");
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [advancedSearchVisible, setAdvancedSearchVisible] = useState(false);
  const [batchOperationVisible, setBatchOperationVisible] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [form] = Form.useForm();
  const { data, loading, refresh } = useRequest(() => fetchAppList({
    ...searchParams,
    page: current,
    pageSize
  }, currentUser?.role), {
    refreshDeps: [searchParams, current, pageSize, currentUser?.role],
    formatResult: (res) => {
      console.log("\u683C\u5F0F\u5316API\u54CD\u5E94:", res);
      return res;
    }
  });
  const appListData = data?.data || [];
  const totalItems = data?.total || 0;
  const handleSearch = (values) => {
    setSearchParams(values);
    setCurrent(1);
  };
  const handleViewDetail = (id) => {
    history.push(`/app/detail/${id}`);
  };
  const handleEdit = (id) => {
    history.push(`/app/edit/${id}`);
  };
  const handleDelete = (id) => {
    message.success(`\u5220\u9664\u5E94\u7528 ID: ${id}`);
    refresh();
  };
  const handleSubmitReview = async (id) => {
    try {
      await submitAppForReview(id.toString());
      message.success("\u5E94\u7528\u5DF2\u63D0\u4EA4\u5BA1\u6838");
      refresh();
    } catch (error) {
      message.error("\u63D0\u4EA4\u5BA1\u6838\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5");
      console.error("\u63D0\u4EA4\u5BA1\u6838\u5931\u8D25:", error);
    }
  };
  const handleResubmitReview = async (id) => {
    try {
      await submitAppForReview(id.toString());
      message.success("\u5E94\u7528\u5DF2\u91CD\u65B0\u63D0\u4EA4\u5BA1\u6838");
      refresh();
    } catch (error) {
      message.error("\u91CD\u65B0\u63D0\u4EA4\u5BA1\u6838\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5");
      console.error("\u91CD\u65B0\u63D0\u4EA4\u5BA1\u6838\u5931\u8D25:", error);
    }
  };
  const handleShowRejectReason = (reason) => {
    setCurrentRejectReason(reason || "\u65E0\u5177\u4F53\u539F\u56E0");
    setRejectReasonModalVisible(true);
  };
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("\u8BF7\u9009\u62E9\u8981\u5220\u9664\u7684\u5E94\u7528");
      return;
    }
    Modal.confirm({
      title: "\u786E\u8BA4\u5220\u9664",
      content: `\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684 ${selectedRowKeys.length} \u4E2A\u5E94\u7528\u5417\uFF1F\u6B64\u64CD\u4F5C\u4E0D\u53EF\u6062\u590D\u3002`,
      okText: "\u786E\u8BA4\u5220\u9664",
      okType: "danger",
      cancelText: "\u53D6\u6D88",
      onOk: async () => {
        try {
          message.success(`\u5DF2\u5220\u9664${selectedRowKeys.length}\u4E2A\u5E94\u7528`);
          setSelectedRowKeys([]);
          setSelectedRows([]);
          refresh();
        } catch (error) {
          message.error("\u6279\u91CF\u5220\u9664\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5");
        }
      }
    });
  };
  const handleBatchApprove = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("\u8BF7\u9009\u62E9\u8981\u5BA1\u6838\u901A\u8FC7\u7684\u5E94\u7528");
      return;
    }
    Modal.confirm({
      title: "\u6279\u91CF\u5BA1\u6838\u901A\u8FC7",
      content: `\u786E\u5B9A\u8981\u5BA1\u6838\u901A\u8FC7\u9009\u4E2D\u7684 ${selectedRowKeys.length} \u4E2A\u5E94\u7528\u5417\uFF1F`,
      okText: "\u786E\u8BA4\u901A\u8FC7",
      cancelText: "\u53D6\u6D88",
      onOk: async () => {
        try {
          message.success(`\u5DF2\u5BA1\u6838\u901A\u8FC7${selectedRowKeys.length}\u4E2A\u5E94\u7528`);
          setSelectedRowKeys([]);
          setSelectedRows([]);
          refresh();
        } catch (error) {
          message.error("\u6279\u91CF\u5BA1\u6838\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5");
        }
      }
    });
  };
  const handleBatchReject = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("\u8BF7\u9009\u62E9\u8981\u62D2\u7EDD\u7684\u5E94\u7528");
      return;
    }
    Modal.confirm({
      title: "\u6279\u91CF\u62D2\u7EDD",
      content: `\u786E\u5B9A\u8981\u62D2\u7EDD\u9009\u4E2D\u7684 ${selectedRowKeys.length} \u4E2A\u5E94\u7528\u5417\uFF1F`,
      okText: "\u786E\u8BA4\u62D2\u7EDD",
      okType: "danger",
      cancelText: "\u53D6\u6D88",
      onOk: async () => {
        try {
          message.success(`\u5DF2\u62D2\u7EDD${selectedRowKeys.length}\u4E2A\u5E94\u7528`);
          setSelectedRowKeys([]);
          setSelectedRows([]);
          refresh();
        } catch (error) {
          message.error("\u6279\u91CF\u62D2\u7EDD\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5");
        }
      }
    });
  };
  const handleCreate = () => {
    history.push("/app/create");
  };
  const handleExport = async (format = "excel") => {
    setExportLoading(true);
    try {
      const exportParams = {
        ...searchParams,
        format,
        selectedIds: selectedRowKeys.length > 0 ? selectedRowKeys : void 0
      };
      const exportData = selectedRowKeys.length > 0 ? selectedRows : appListData;
      const csvContent = generateCSV(exportData);
      downloadFile(csvContent, `\u5E94\u7528\u5217\u8868_${(/* @__PURE__ */ new Date()).toISOString().split("T")[0]}.csv`, "text/csv");
      message.success(`\u5BFC\u51FA${format.toUpperCase()}\u6587\u4EF6\u6210\u529F`);
    } catch (error) {
      message.error("\u5BFC\u51FA\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5");
      console.error("\u5BFC\u51FA\u5931\u8D25:", error);
    } finally {
      setExportLoading(false);
    }
  };
  const generateCSV = (data2) => {
    const headers = ["\u5E94\u7528\u540D\u79F0", "\u5F00\u53D1\u8005", "\u7C7B\u522B", "\u72B6\u6001", "\u7248\u672C", "\u4E0B\u8F7D\u91CF", "\u8BC4\u5206", "\u66F4\u65B0\u65F6\u95F4"];
    const rows = data2.map((item) => [
      item.name,
      item.developer_name || item.developerName || item.developer || "",
      item.category,
      item.status,
      item.current_version || item.currentVersion || item.version || "",
      item.download_count || item.downloadCount || item.downloads || 0,
      item.average_rating || item.averageRating || item.rating || 0,
      item.updated_at ? new Date(item.updated_at).toLocaleString() : ""
    ]);
    return [headers, ...rows].map((row) => row.join(",")).join("\n");
  };
  const downloadFile = (content, filename, contentType) => {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };
  const handleAdvancedSearch = (values) => {
    const newSearchParams = {
      ...searchParams,
      ...values,
      dateRange: values.dateRange
    };
    setSearchParams(newSearchParams);
    setCurrent(1);
    setAdvancedSearchVisible(false);
    refresh();
  };
  const handleResetSearch = () => {
    const resetParams = {
      page: 1,
      pageSize: 10,
      keyword: "",
      category: "",
      status: void 0,
      developer: "",
      dateRange: null,
      minDownloads: "",
      minRating: ""
    };
    setSearchParams(resetParams);
    form.resetFields();
    setCurrent(1);
    refresh();
  };
  const columns = [
    {
      title: "\u5E94\u7528\u4FE1\u606F",
      key: "appInfo",
      width: 250,
      render: (_, record) => /* @__PURE__ */ jsxs("div", { style: { display: "flex", alignItems: "center" }, children: [
        /* @__PURE__ */ jsx(
          Avatar,
          {
            src: record.icon,
            size: 40,
            style: { marginRight: 12 },
            icon: /* @__PURE__ */ jsx(AppstoreOutlined, {}),
            onError: () => {
              console.warn("\u5E94\u7528\u56FE\u6807\u52A0\u8F7D\u5931\u8D25:", record.icon);
              return false;
            }
          }
        ),
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("div", { style: { fontWeight: "bold" }, children: record.name }),
          /* @__PURE__ */ jsx("div", { style: { fontSize: 12, color: "#888" }, children: record.developer_name || record.developerName || record.developer })
        ] })
      ] })
    },
    {
      title: "\u7C7B\u522B",
      dataIndex: "category",
      key: "category",
      width: 100
    },
    {
      title: "\u72B6\u6001",
      dataIndex: "status",
      key: "status",
      width: 120,
      render: (status) => {
        let color = "blue";
        let text = "\u5F85\u5BA1\u6838";
        if (status === "published" || status === "approved") {
          color = "green";
          text = "\u5DF2\u53D1\u5E03";
        } else if (status === "draft") {
          color = "gray";
          text = "\u8349\u7A3F";
        } else if (status === "rejected") {
          color = "red";
          text = "\u5DF2\u62D2\u7EDD";
        }
        return /* @__PURE__ */ jsx(Badge, { status: color, text });
      }
    },
    {
      title: "\u7248\u672C",
      dataIndex: "current_version",
      key: "version",
      width: 100,
      render: (version, record) => record.current_version || record.currentVersion || record.version
    },
    {
      title: "\u5927\u5C0F",
      dataIndex: "size",
      key: "size",
      width: 100
    },
    {
      title: "\u4E0B\u8F7D\u91CF",
      dataIndex: "download_count",
      key: "downloads",
      width: 120,
      render: (downloads, record) => {
        const count = record.download_count || record.downloadCount || record.downloads || 0;
        if (count >= 1e9) {
          return `${(count / 1e9).toFixed(1)}B`;
        } else if (count >= 1e6) {
          return `${(count / 1e6).toFixed(1)}M`;
        } else if (count >= 1e3) {
          return `${(count / 1e3).toFixed(1)}K`;
        }
        return count;
      },
      sorter: (a, b) => {
        const aCount = a.download_count || a.downloadCount || a.downloads || 0;
        const bCount = b.download_count || b.downloadCount || b.downloads || 0;
        return aCount - bCount;
      }
    },
    {
      title: "\u8BC4\u5206",
      dataIndex: "average_rating",
      key: "rating",
      width: 100,
      render: (rating, record) => {
        const score = record.average_rating || record.averageRating || record.rating || 0;
        if (score === 0) return "\u6682\u65E0\u8BC4\u5206";
        return score.toFixed(1);
      },
      sorter: (a, b) => {
        const aRating = a.average_rating || a.averageRating || a.rating || 0;
        const bRating = b.average_rating || b.averageRating || b.rating || 0;
        return aRating - bRating;
      }
    },
    {
      title: "\u4EF7\u683C",
      dataIndex: "price",
      key: "price",
      width: 100,
      render: (price) => {
        return "\u514D\u8D39";
      }
    },
    {
      title: "\u66F4\u65B0\u65F6\u95F4",
      dataIndex: "updated_at",
      key: "updateDate",
      width: 120,
      render: (date, record) => {
        const updateDate = record.updated_at || record.updatedAt;
        if (!updateDate) return "-";
        return new Date(updateDate).toLocaleString();
      },
      sorter: (a, b) => {
        const aDate = new Date(a.updated_at || a.updatedAt || 0).getTime();
        const bDate = new Date(b.updated_at || b.updatedAt || 0).getTime();
        return aDate - bDate;
      }
    },
    {
      title: "\u64CD\u4F5C",
      key: "action",
      width: 200,
      fixed: "right",
      render: (_, record) => {
        const canEdit = canEditApp(
          currentUser?.role,
          record.developer_id?.toString() || "",
          currentUser?.id?.toString() || ""
        );
        const canDelete = canDeleteApp(
          currentUser?.role,
          record.developer_id?.toString() || "",
          currentUser?.id?.toString() || ""
        );
        return /* @__PURE__ */ jsxs(Space, { size: "small", children: [
          /* @__PURE__ */ jsx(Tooltip, { title: "\u67E5\u770B\u8BE6\u60C5", children: /* @__PURE__ */ jsx(
            Button,
            {
              type: "link",
              size: "small",
              icon: /* @__PURE__ */ jsx(EyeOutlined, {}),
              onClick: () => handleViewDetail(record.id),
              children: "\u8BE6\u60C5"
            }
          ) }),
          /* @__PURE__ */ jsx(Tooltip, { title: "\u7248\u672C\u7BA1\u7406", children: /* @__PURE__ */ jsx(
            Button,
            {
              type: "link",
              size: "small",
              onClick: () => history.push(`/app/list/versions/${record.id}`),
              children: "\u7248\u672C\u7BA1\u7406"
            }
          ) }),
          record.status === "draft" && canEdit && currentUser?.role === "developer" && /* @__PURE__ */ jsx(Tooltip, { title: "\u63D0\u4EA4\u5BA1\u6838", children: /* @__PURE__ */ jsx(
            Button,
            {
              type: "link",
              size: "small",
              onClick: () => handleSubmitReview(record.id),
              children: "\u63D0\u4EA4\u5BA1\u6838"
            }
          ) }),
          record.status === "rejected" && canEdit && currentUser?.role === "developer" && /* @__PURE__ */ jsxs(Fragment, { children: [
            /* @__PURE__ */ jsx(Tooltip, { title: `\u62D2\u7EDD\u539F\u56E0\uFF1A${record.reject_reason || "\u65E0\u5177\u4F53\u539F\u56E0"}`, children: /* @__PURE__ */ jsx(
              Button,
              {
                type: "link",
                size: "small",
                danger: true,
                onClick: () => handleShowRejectReason(record.reject_reason),
                children: "\u67E5\u770B\u62D2\u7EDD\u539F\u56E0"
              }
            ) }),
            /* @__PURE__ */ jsx(Tooltip, { title: "\u91CD\u65B0\u63D0\u4EA4\u5BA1\u6838", children: /* @__PURE__ */ jsx(
              Button,
              {
                type: "link",
                size: "small",
                onClick: () => handleResubmitReview(record.id),
                children: "\u91CD\u65B0\u63D0\u4EA4"
              }
            ) })
          ] }),
          /* @__PURE__ */ jsx(PermissionButton, { permission: canEdit ? "canViewApp" : "canEditApp", children: /* @__PURE__ */ jsx(Tooltip, { title: "\u7F16\u8F91\u5E94\u7528", children: /* @__PURE__ */ jsx(
            Button,
            {
              type: "link",
              size: "small",
              icon: /* @__PURE__ */ jsx(EditOutlined, {}),
              onClick: () => handleEdit(record.id),
              disabled: !canEdit,
              children: "\u7F16\u8F91"
            }
          ) }) }),
          /* @__PURE__ */ jsx(PermissionButton, { permission: "canDeleteApp", children: /* @__PURE__ */ jsx(Tooltip, { title: "\u5220\u9664\u5E94\u7528", children: /* @__PURE__ */ jsx(
            Button,
            {
              type: "link",
              size: "small",
              danger: true,
              icon: /* @__PURE__ */ jsx(DeleteOutlined, {}),
              onClick: () => handleDelete(record.id),
              disabled: !canDelete,
              children: "\u5220\u9664"
            }
          ) }) })
        ] });
      }
    }
  ];
  return /* @__PURE__ */ jsx(PermissionWrapper, { permission: "canViewApp", children: /* @__PURE__ */ jsxs(PageContainer, { children: [
    /* @__PURE__ */ jsxs(Card, { children: [
      /* @__PURE__ */ jsx("div", { style: { marginBottom: 16 }, children: /* @__PURE__ */ jsxs(Space, { wrap: true, children: [
        /* @__PURE__ */ jsx(
          Input,
          {
            placeholder: "\u641C\u7D22\u5E94\u7528\u540D\u79F0\u6216\u5305\u540D",
            prefix: /* @__PURE__ */ jsx(SearchOutlined, {}),
            style: { width: 250 },
            value: searchParams.keyword,
            onChange: (e) => setSearchParams({ ...searchParams, keyword: e.target.value }),
            onPressEnter: () => refresh()
          }
        ),
        /* @__PURE__ */ jsxs(
          Select,
          {
            placeholder: "\u9009\u62E9\u5206\u7C7B",
            style: { width: 150 },
            allowClear: true,
            value: searchParams.category || void 0,
            onChange: (value) => setSearchParams({ ...searchParams, category: value || "" }),
            children: [
              /* @__PURE__ */ jsx(Option, { value: "productivity", children: "\u6548\u7387\u5DE5\u5177" }),
              /* @__PURE__ */ jsx(Option, { value: "entertainment", children: "\u5A31\u4E50" }),
              /* @__PURE__ */ jsx(Option, { value: "education", children: "\u6559\u80B2" }),
              /* @__PURE__ */ jsx(Option, { value: "business", children: "\u5546\u52A1" }),
              /* @__PURE__ */ jsx(Option, { value: "lifestyle", children: "\u751F\u6D3B" }),
              /* @__PURE__ */ jsx(Option, { value: "games", children: "\u6E38\u620F" })
            ]
          }
        ),
        /* @__PURE__ */ jsxs(
          Select,
          {
            placeholder: "\u9009\u62E9\u72B6\u6001",
            style: { width: 120 },
            allowClear: true,
            value: searchParams.status || void 0,
            onChange: (value) => setSearchParams({ ...searchParams, status: value || void 0 }),
            children: [
              /* @__PURE__ */ jsx(Option, { value: "pending", children: "\u5F85\u5BA1\u6838" }),
              /* @__PURE__ */ jsx(Option, { value: "approved", children: "\u5DF2\u4E0A\u67B6" }),
              /* @__PURE__ */ jsx(Option, { value: "rejected", children: "\u5DF2\u62D2\u7EDD" }),
              /* @__PURE__ */ jsx(Option, { value: "offline", children: "\u5DF2\u4E0B\u67B6" })
            ]
          }
        ),
        /* @__PURE__ */ jsx(Button, { type: "primary", icon: /* @__PURE__ */ jsx(SearchOutlined, {}), onClick: () => refresh(), children: "\u641C\u7D22" }),
        /* @__PURE__ */ jsx(
          Button,
          {
            icon: /* @__PURE__ */ jsx(FilterOutlined, {}),
            onClick: () => setAdvancedSearchVisible(true),
            children: "\u9AD8\u7EA7\u641C\u7D22"
          }
        ),
        /* @__PURE__ */ jsx(Button, { icon: /* @__PURE__ */ jsx(ReloadOutlined, {}), onClick: handleResetSearch, children: "\u91CD\u7F6E" })
      ] }) }),
      /* @__PURE__ */ jsx(
        Modal,
        {
          title: "\u9AD8\u7EA7\u641C\u7D22",
          open: advancedSearchVisible,
          onCancel: () => setAdvancedSearchVisible(false),
          footer: null,
          width: 600,
          children: /* @__PURE__ */ jsxs(
            Form,
            {
              form,
              layout: "vertical",
              onFinish: handleAdvancedSearch,
              initialValues: searchParams,
              children: [
                /* @__PURE__ */ jsx(Form.Item, { label: "\u5F00\u53D1\u8005", name: "developer", children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u5F00\u53D1\u8005\u540D\u79F0" }) }),
                /* @__PURE__ */ jsx(Form.Item, { label: "\u66F4\u65B0\u65F6\u95F4\u8303\u56F4", name: "dateRange", children: /* @__PURE__ */ jsx(RangePicker, { style: { width: "100%" } }) }),
                /* @__PURE__ */ jsx(Form.Item, { label: "\u6700\u5C0F\u4E0B\u8F7D\u91CF", name: "minDownloads", children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u6700\u5C0F\u4E0B\u8F7D\u91CF", type: "number" }) }),
                /* @__PURE__ */ jsx(Form.Item, { label: "\u6700\u5C0F\u8BC4\u5206", name: "minRating", children: /* @__PURE__ */ jsxs(Select, { placeholder: "\u8BF7\u9009\u62E9\u6700\u5C0F\u8BC4\u5206", allowClear: true, children: [
                  /* @__PURE__ */ jsx(Option, { value: "1", children: "1\u661F\u53CA\u4EE5\u4E0A" }),
                  /* @__PURE__ */ jsx(Option, { value: "2", children: "2\u661F\u53CA\u4EE5\u4E0A" }),
                  /* @__PURE__ */ jsx(Option, { value: "3", children: "3\u661F\u53CA\u4EE5\u4E0A" }),
                  /* @__PURE__ */ jsx(Option, { value: "4", children: "4\u661F\u53CA\u4EE5\u4E0A" }),
                  /* @__PURE__ */ jsx(Option, { value: "5", children: "5\u661F" })
                ] }) }),
                /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsxs(Space, { children: [
                  /* @__PURE__ */ jsx(Button, { type: "primary", htmlType: "submit", children: "\u641C\u7D22" }),
                  /* @__PURE__ */ jsx(Button, { onClick: () => {
                    form.resetFields();
                    setAdvancedSearchVisible(false);
                  }, children: "\u53D6\u6D88" })
                ] }) })
              ]
            }
          )
        }
      ),
      /* @__PURE__ */ jsx("div", { style: { marginBottom: 16 }, children: /* @__PURE__ */ jsxs(Space, { wrap: true, children: [
        /* @__PURE__ */ jsx(PermissionButton, { permission: "canCreateApp", children: /* @__PURE__ */ jsx(
          Button,
          {
            type: "primary",
            icon: /* @__PURE__ */ jsx(PlusOutlined, {}),
            onClick: () => history.push("/app/create"),
            children: "\u521B\u5EFA\u5E94\u7528"
          }
        ) }),
        /* @__PURE__ */ jsx(PermissionButton, { permission: ["canEditApp", "canDeleteApp"], children: /* @__PURE__ */ jsx(
          Dropdown,
          {
            menu: {
              items: [
                {
                  key: "approve",
                  label: "\u6279\u91CF\u5BA1\u6838\u901A\u8FC7",
                  icon: /* @__PURE__ */ jsx(EyeOutlined, {}),
                  onClick: handleBatchApprove,
                  disabled: selectedRowKeys.length === 0
                },
                {
                  key: "reject",
                  label: "\u6279\u91CF\u62D2\u7EDD",
                  icon: /* @__PURE__ */ jsx(DeleteOutlined, {}),
                  onClick: handleBatchReject,
                  disabled: selectedRowKeys.length === 0
                },
                {
                  type: "divider"
                },
                {
                  key: "delete",
                  label: "\u6279\u91CF\u5220\u9664",
                  icon: /* @__PURE__ */ jsx(DeleteOutlined, {}),
                  onClick: handleBatchDelete,
                  disabled: selectedRowKeys.length === 0,
                  danger: true
                }
              ]
            },
            trigger: ["click"],
            children: /* @__PURE__ */ jsxs(Button, { children: [
              "\u6279\u91CF\u64CD\u4F5C ",
              selectedRowKeys.length > 0 && `(${selectedRowKeys.length})`,
              /* @__PURE__ */ jsx(MoreOutlined, {})
            ] })
          }
        ) }),
        /* @__PURE__ */ jsx(
          Dropdown,
          {
            menu: {
              items: [
                {
                  key: "csv",
                  label: "\u5BFC\u51FA\u4E3ACSV",
                  icon: /* @__PURE__ */ jsx(ExportOutlined, {}),
                  onClick: () => handleExport("csv")
                },
                {
                  key: "excel",
                  label: "\u5BFC\u51FA\u4E3AExcel",
                  icon: /* @__PURE__ */ jsx(ExportOutlined, {}),
                  onClick: () => handleExport("excel")
                },
                {
                  type: "divider"
                },
                {
                  key: "selected",
                  label: `\u5BFC\u51FA\u9009\u4E2D\u9879 (${selectedRowKeys.length})`,
                  icon: /* @__PURE__ */ jsx(ExportOutlined, {}),
                  onClick: () => handleExport("csv"),
                  disabled: selectedRowKeys.length === 0
                }
              ]
            },
            trigger: ["click"],
            children: /* @__PURE__ */ jsx(Button, { icon: /* @__PURE__ */ jsx(DownloadOutlined, {}), loading: exportLoading, children: "\u5BFC\u51FA\u6570\u636E" })
          }
        ),
        selectedRowKeys.length > 0 && /* @__PURE__ */ jsxs(Tag, { color: "blue", children: [
          "\u5DF2\u9009\u62E9 ",
          selectedRowKeys.length,
          " \u9879"
        ] }),
        currentUser?.role === "developer" && /* @__PURE__ */ jsx(Tag, { color: "green", children: "\u663E\u793A\u6211\u7684\u5E94\u7528" })
      ] }) }),
      /* @__PURE__ */ jsx(
        Table,
        {
          columns,
          dataSource: appListData,
          loading,
          rowKey: "id",
          scroll: { x: 1200 },
          rowSelection: {
            selectedRowKeys,
            onChange: (selectedRowKeys2, selectedRows2) => {
              setSelectedRowKeys(selectedRowKeys2);
              setSelectedRows(selectedRows2);
            },
            getCheckboxProps: (record) => ({
              disabled: false,
              // 可以根据权限或状态禁用某些行的选择
              name: record.name
            })
          },
          pagination: {
            current,
            pageSize,
            total: totalItems,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `\u7B2C ${range[0]}-${range[1]} \u6761/\u603B\u5171 ${total} \u6761`,
            onChange: (page, size) => {
              setCurrent(page);
              setPageSize(size || 10);
            }
          }
        }
      )
    ] }),
    /* @__PURE__ */ jsx(
      Modal,
      {
        title: "\u62D2\u7EDD\u539F\u56E0",
        open: rejectReasonModalVisible,
        onCancel: () => setRejectReasonModalVisible(false),
        footer: [
          /* @__PURE__ */ jsx(Button, { onClick: () => setRejectReasonModalVisible(false), children: "\u5173\u95ED" }, "close")
        ],
        width: 500,
        children: /* @__PURE__ */ jsx("div", { style: { padding: "16px 0" }, children: /* @__PURE__ */ jsx("p", { style: { fontSize: "14px", lineHeight: "1.6", margin: 0 }, children: currentRejectReason }) })
      }
    )
  ] }) });
};
export default AppList;
