"use strict";
import { jsx } from "react/jsx-runtime";
import { useState, useEffect } from "react";
import { List, Switch, message, Spin, Card } from "antd";
import {
  getNotificationSettings,
  updateNotificationSettings
} from "@/services/ant-design-pro/notifications";
const NotificationView = () => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(null);
  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await getNotificationSettings();
      if (response.success && response.data) {
        setSettings(response.data);
      }
    } catch (error) {
      message.error("\u83B7\u53D6\u901A\u77E5\u8BBE\u7F6E\u5931\u8D25");
    } finally {
      setLoading(false);
    }
  };
  const handleSettingChange = async (key, value) => {
    if (!settings) return;
    setUpdating(key);
    try {
      const response = await updateNotificationSettings({ [key]: value });
      if (response.success) {
        setSettings((prev) => prev ? { ...prev, [key]: value } : null);
        message.success("\u8BBE\u7F6E\u66F4\u65B0\u6210\u529F");
      }
    } catch (error) {
      message.error("\u8BBE\u7F6E\u66F4\u65B0\u5931\u8D25");
    } finally {
      setUpdating(null);
    }
  };
  useEffect(() => {
    fetchSettings();
  }, []);
  const getData = () => {
    if (!settings) return [];
    return [
      {
        key: "account_security",
        title: "\u8D26\u6237\u5B89\u5168",
        description: "\u8D26\u6237\u5BC6\u7801\u53D8\u66F4\u3001\u767B\u5F55\u5F02\u5E38\u7B49\u5B89\u5168\u76F8\u5173\u901A\u77E5",
        checked: settings.account_security
      },
      {
        key: "system_messages",
        title: "\u7CFB\u7EDF\u6D88\u606F",
        description: "\u7CFB\u7EDF\u516C\u544A\u3001\u7EF4\u62A4\u901A\u77E5\u7B49\u91CD\u8981\u6D88\u606F",
        checked: settings.system_messages
      },
      {
        key: "task_reminders",
        title: "\u4EFB\u52A1\u63D0\u9192",
        description: "\u5E94\u7528\u5BA1\u6838\u3001\u5F00\u53D1\u8005\u8BA4\u8BC1\u7B49\u4EFB\u52A1\u72B6\u6001\u53D8\u66F4\u63D0\u9192",
        checked: settings.task_reminders
      },
      {
        key: "email_notifications",
        title: "\u90AE\u4EF6\u901A\u77E5",
        description: "\u91CD\u8981\u901A\u77E5\u540C\u65F6\u53D1\u9001\u5230\u60A8\u7684\u90AE\u7BB1",
        checked: settings.email_notifications
      }
    ];
  };
  const data = getData();
  if (loading) {
    return /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx("div", { style: { textAlign: "center", padding: "40px 0" }, children: /* @__PURE__ */ jsx(Spin, { size: "large" }) }) });
  }
  return /* @__PURE__ */ jsx(Card, { title: "\u901A\u77E5\u8BBE\u7F6E", extra: /* @__PURE__ */ jsx("span", { style: { fontSize: "14px", color: "#666" }, children: "\u7BA1\u7406\u60A8\u63A5\u6536\u901A\u77E5\u7684\u65B9\u5F0F" }), children: /* @__PURE__ */ jsx(
    List,
    {
      itemLayout: "horizontal",
      dataSource: data,
      renderItem: (item) => {
        const isUpdating = updating === item.key;
        return /* @__PURE__ */ jsx(
          List.Item,
          {
            actions: [
              /* @__PURE__ */ jsx(
                Switch,
                {
                  checkedChildren: "\u5F00",
                  unCheckedChildren: "\u5173",
                  checked: item.checked,
                  loading: isUpdating,
                  onChange: (checked) => handleSettingChange(item.key, checked)
                },
                "switch"
              )
            ],
            children: /* @__PURE__ */ jsx(
              List.Item.Meta,
              {
                title: item.title,
                description: item.description
              }
            )
          }
        );
      }
    }
  ) });
};
export default NotificationView;
