{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "d92788dd-c0e4-4376-8a58-754fa9877c96", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550793589700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3ba7604-6b9e-45bd-ab53-3ad65ef971a7", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550793938600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcc9a58f-8730-4517-bf9b-591b4d8bd68a", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550797531200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7e6a6ba-60e4-497a-b572-47682ed1d624", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550798131700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c20dcfc-bfbd-4b0f-bb40-d444d4ac4d50", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550799332100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b915c09-8773-40ef-8437-9a280f1c88e7", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550799903600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d6a5e12-e271-48c7-b5df-148407171875", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550803366600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48ce0e94-34e7-4035-bb9b-19915a0abf31", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154550862422000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf35736e-9151-4122-a0eb-ef5614a15521", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918290590300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc4be02e-b06d-4baa-98ee-677b3042f958", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918299152900, "endTime": 154918511663000}, "additional": {"children": ["808e6272-a76f-4ee6-801d-980ac6fcbe68", "c0fe74d3-4c27-4623-80fa-ecf587276ca3", "54a5da13-5f3e-4c0a-bceb-0f8444991f36", "442cc61a-9a4f-4e2b-a82d-1ec4245b128f", "626ff417-388f-482b-bb63-d94adbf27d04", "435b0f38-c999-4d32-8162-2ea7903d74c8", "937dc17d-31e7-4ba0-95f5-1ceae94396d4"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "d9b4d673-0cc8-4599-8607-b27dbfd2231c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "808e6272-a76f-4ee6-801d-980ac6fcbe68", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918299154500, "endTime": 154918313334400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bc4be02e-b06d-4baa-98ee-677b3042f958", "logId": "a0218063-f448-4a3d-bdf0-420cc3c7b892"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0fe74d3-4c27-4623-80fa-ecf587276ca3", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918313351000, "endTime": 154918510002200}, "additional": {"children": ["950f1024-6d42-4c5c-a49f-6dcdccb18806", "daf4dd3e-0000-4fe5-b5d3-654e500851c3", "66bb8499-9f46-4dc9-94d9-17e93df1091d", "aa62a1cc-f73e-425b-bd57-83728cb6805e", "88919e9a-5f8d-4254-814f-cf3e05601832", "a18a765c-96ff-4162-90c1-4dfe28a2b977", "60e74758-ea65-444b-ba9c-705eb94d5692", "342ec31c-2eb9-448a-b711-48157ae8edfb", "4be2f275-ecf0-4712-93d4-632faf6edc87"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bc4be02e-b06d-4baa-98ee-677b3042f958", "logId": "61286f60-7081-42bd-81c5-f3e74c0f0d2c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54a5da13-5f3e-4c0a-bceb-0f8444991f36", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918510026300, "endTime": 154918511651400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bc4be02e-b06d-4baa-98ee-677b3042f958", "logId": "1e992c3a-83ec-4cd2-a807-d1fe957ebf7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "442cc61a-9a4f-4e2b-a82d-1ec4245b128f", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918511656300, "endTime": 154918511658500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bc4be02e-b06d-4baa-98ee-677b3042f958", "logId": "c5dce8b2-06d3-4bb1-9cba-c68406696403"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "626ff417-388f-482b-bb63-d94adbf27d04", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918303496400, "endTime": 154918303547400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bc4be02e-b06d-4baa-98ee-677b3042f958", "logId": "069021a4-0604-4724-98d2-075251006106"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "069021a4-0604-4724-98d2-075251006106", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918303496400, "endTime": 154918303547400}, "additional": {"logType": "info", "children": [], "durationId": "626ff417-388f-482b-bb63-d94adbf27d04", "parent": "d9b4d673-0cc8-4599-8607-b27dbfd2231c"}}, {"head": {"id": "435b0f38-c999-4d32-8162-2ea7903d74c8", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918307811400, "endTime": 154918307830700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bc4be02e-b06d-4baa-98ee-677b3042f958", "logId": "cc2cefce-e981-4f04-9fe1-8f16a1721543"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc2cefce-e981-4f04-9fe1-8f16a1721543", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918307811400, "endTime": 154918307830700}, "additional": {"logType": "info", "children": [], "durationId": "435b0f38-c999-4d32-8162-2ea7903d74c8", "parent": "d9b4d673-0cc8-4599-8607-b27dbfd2231c"}}, {"head": {"id": "5d6cff73-acbb-478e-b93d-5eb9216b34e7", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918308013100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d00f210-c25e-490a-a5d5-d9e2f938f4ca", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918313156900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0218063-f448-4a3d-bdf0-420cc3c7b892", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918299154500, "endTime": 154918313334400}, "additional": {"logType": "info", "children": [], "durationId": "808e6272-a76f-4ee6-801d-980ac6fcbe68", "parent": "d9b4d673-0cc8-4599-8607-b27dbfd2231c"}}, {"head": {"id": "950f1024-6d42-4c5c-a49f-6dcdccb18806", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918317384800, "endTime": 154918317391200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0fe74d3-4c27-4623-80fa-ecf587276ca3", "logId": "9a41ae89-aceb-4fef-bf55-4ae8c5729d25"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "daf4dd3e-0000-4fe5-b5d3-654e500851c3", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918317466300, "endTime": 154918321502200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0fe74d3-4c27-4623-80fa-ecf587276ca3", "logId": "87b4c058-e60b-4199-b7dc-ba482b0ec985"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66bb8499-9f46-4dc9-94d9-17e93df1091d", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918321512000, "endTime": 154918414734500}, "additional": {"children": ["7ec3c04d-d4db-44ea-9108-0210e3447c8a", "0a2d6944-6018-448d-a189-a2fa3b8e45fc", "694c595f-940f-49b5-a24d-8a69a98d8009"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0fe74d3-4c27-4623-80fa-ecf587276ca3", "logId": "fd9f5c7f-4e96-42a8-bd07-4578816b7d9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa62a1cc-f73e-425b-bd57-83728cb6805e", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918414745700, "endTime": 154918439012600}, "additional": {"children": ["784559e2-3aa6-46d8-8800-e4f788abfe17"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0fe74d3-4c27-4623-80fa-ecf587276ca3", "logId": "81aee7a2-f95a-4baf-9ebc-42b998f9d573"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88919e9a-5f8d-4254-814f-cf3e05601832", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918439062600, "endTime": 154918482233300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0fe74d3-4c27-4623-80fa-ecf587276ca3", "logId": "79b0a06d-ce0d-4bf2-bee6-2a4202a4e422"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a18a765c-96ff-4162-90c1-4dfe28a2b977", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918483570600, "endTime": 154918493507900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0fe74d3-4c27-4623-80fa-ecf587276ca3", "logId": "39fbd8ce-18dd-4d79-b14a-8eb17839b39d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60e74758-ea65-444b-ba9c-705eb94d5692", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918493525400, "endTime": 154918509875400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0fe74d3-4c27-4623-80fa-ecf587276ca3", "logId": "8662f5b9-9744-42a6-9d81-ad7d8e049a86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "342ec31c-2eb9-448a-b711-48157ae8edfb", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918509890700, "endTime": 154918509992200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0fe74d3-4c27-4623-80fa-ecf587276ca3", "logId": "59695003-b7e3-43aa-a8be-f5722e528cf6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a41ae89-aceb-4fef-bf55-4ae8c5729d25", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918317384800, "endTime": 154918317391200}, "additional": {"logType": "info", "children": [], "durationId": "950f1024-6d42-4c5c-a49f-6dcdccb18806", "parent": "61286f60-7081-42bd-81c5-f3e74c0f0d2c"}}, {"head": {"id": "87b4c058-e60b-4199-b7dc-ba482b0ec985", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918317466300, "endTime": 154918321502200}, "additional": {"logType": "info", "children": [], "durationId": "daf4dd3e-0000-4fe5-b5d3-654e500851c3", "parent": "61286f60-7081-42bd-81c5-f3e74c0f0d2c"}}, {"head": {"id": "7ec3c04d-d4db-44ea-9108-0210e3447c8a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918321999600, "endTime": 154918322016700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "66bb8499-9f46-4dc9-94d9-17e93df1091d", "logId": "5ec46caa-cf26-4024-aec3-8ecd535b8d9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ec46caa-cf26-4024-aec3-8ecd535b8d9c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918321999600, "endTime": 154918322016700}, "additional": {"logType": "info", "children": [], "durationId": "7ec3c04d-d4db-44ea-9108-0210e3447c8a", "parent": "fd9f5c7f-4e96-42a8-bd07-4578816b7d9b"}}, {"head": {"id": "0a2d6944-6018-448d-a189-a2fa3b8e45fc", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918323488800, "endTime": 154918414218000}, "additional": {"children": ["145445b2-1dd4-4f0e-a317-85d697aba6c8", "bb14bd96-11fd-404f-a2be-dcd0d0e6ff99"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "66bb8499-9f46-4dc9-94d9-17e93df1091d", "logId": "53cd5d53-5c06-43ed-9f3e-782fddfcf0f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "145445b2-1dd4-4f0e-a317-85d697aba6c8", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918323489700, "endTime": 154918341444000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0a2d6944-6018-448d-a189-a2fa3b8e45fc", "logId": "2c1a0ee4-6cdd-42ad-856e-59492e2e2630"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb14bd96-11fd-404f-a2be-dcd0d0e6ff99", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918341460100, "endTime": 154918414207600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0a2d6944-6018-448d-a189-a2fa3b8e45fc", "logId": "9ecdf1cc-7b32-462a-a472-eb645b7f7296"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c13a576a-e4be-410e-aea0-753e8f0ebc5b", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918323492400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ab33889-8960-4e20-974d-10209073a629", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918341287400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c1a0ee4-6cdd-42ad-856e-59492e2e2630", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918323489700, "endTime": 154918341444000}, "additional": {"logType": "info", "children": [], "durationId": "145445b2-1dd4-4f0e-a317-85d697aba6c8", "parent": "53cd5d53-5c06-43ed-9f3e-782fddfcf0f6"}}, {"head": {"id": "3b19aeee-8b0b-4d71-b7a2-13b592ecf7e6", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918341495300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26559b6a-637a-49ae-9613-aae1f7574151", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918360805700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d80fb5ce-2da8-4b4d-87f8-f80f8ad58bd0", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918361021000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4532ee1-2303-4738-afc5-4a4c5b7b24c1", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918361144800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e61c32f-d8ab-46cd-8d76-c2f116cb9e02", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918361944800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd1c7efa-80e4-4951-acdd-0c796aa983cb", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918364088000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "487d0022-4e91-4d8e-9625-e4be7da4728d", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918377817600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d92eb7c-5199-45b1-b784-1eedd19844dc", "name": "Sdk init in 27 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918396563000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73d62e56-a63f-4153-a78c-388e47822ce3", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918396761900}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 59, "second": 8}, "markType": "other"}}, {"head": {"id": "9c9e762e-8a7e-47fd-98dc-bdfdf0db7ba6", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918396781300}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 59, "second": 8}, "markType": "other"}}, {"head": {"id": "eb9290ad-2040-4775-b0d6-7a0913b45e13", "name": "Project task initialization takes 16 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918413999900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9954b4fd-7266-4a68-970a-534bb379affb", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918414108200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09bd72a0-2593-486e-b8a2-e1eb347e23af", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918414151200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12c3ed7f-32f1-4e62-ad00-127e6592142f", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918414179200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ecdf1cc-7b32-462a-a472-eb645b7f7296", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918341460100, "endTime": 154918414207600}, "additional": {"logType": "info", "children": [], "durationId": "bb14bd96-11fd-404f-a2be-dcd0d0e6ff99", "parent": "53cd5d53-5c06-43ed-9f3e-782fddfcf0f6"}}, {"head": {"id": "53cd5d53-5c06-43ed-9f3e-782fddfcf0f6", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918323488800, "endTime": 154918414218000}, "additional": {"logType": "info", "children": ["2c1a0ee4-6cdd-42ad-856e-59492e2e2630", "9ecdf1cc-7b32-462a-a472-eb645b7f7296"], "durationId": "0a2d6944-6018-448d-a189-a2fa3b8e45fc", "parent": "fd9f5c7f-4e96-42a8-bd07-4578816b7d9b"}}, {"head": {"id": "694c595f-940f-49b5-a24d-8a69a98d8009", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918414710400, "endTime": 154918414723500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "66bb8499-9f46-4dc9-94d9-17e93df1091d", "logId": "cc7b7a0b-e6c2-4e05-83f7-f34d2ca67c7d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc7b7a0b-e6c2-4e05-83f7-f34d2ca67c7d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918414710400, "endTime": 154918414723500}, "additional": {"logType": "info", "children": [], "durationId": "694c595f-940f-49b5-a24d-8a69a98d8009", "parent": "fd9f5c7f-4e96-42a8-bd07-4578816b7d9b"}}, {"head": {"id": "fd9f5c7f-4e96-42a8-bd07-4578816b7d9b", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918321512000, "endTime": 154918414734500}, "additional": {"logType": "info", "children": ["5ec46caa-cf26-4024-aec3-8ecd535b8d9c", "53cd5d53-5c06-43ed-9f3e-782fddfcf0f6", "cc7b7a0b-e6c2-4e05-83f7-f34d2ca67c7d"], "durationId": "66bb8499-9f46-4dc9-94d9-17e93df1091d", "parent": "61286f60-7081-42bd-81c5-f3e74c0f0d2c"}}, {"head": {"id": "784559e2-3aa6-46d8-8800-e4f788abfe17", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918415246300, "endTime": 154918439002700}, "additional": {"children": ["28385bbd-af26-4d8c-b7fc-ec504fc9075b", "ec0f8e27-2c7b-4098-b205-511f9c5a0a1f", "3fd448ea-1740-4c6a-b1fe-7c0651f9f243"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa62a1cc-f73e-425b-bd57-83728cb6805e", "logId": "cf0eb05f-5191-4cde-9ff0-e27d869fefc3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28385bbd-af26-4d8c-b7fc-ec504fc9075b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918417297400, "endTime": 154918417451300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "784559e2-3aa6-46d8-8800-e4f788abfe17", "logId": "27dcaf09-a15b-4988-9e3b-6bd9560cb040"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27dcaf09-a15b-4988-9e3b-6bd9560cb040", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918417297400, "endTime": 154918417451300}, "additional": {"logType": "info", "children": [], "durationId": "28385bbd-af26-4d8c-b7fc-ec504fc9075b", "parent": "cf0eb05f-5191-4cde-9ff0-e27d869fefc3"}}, {"head": {"id": "ec0f8e27-2c7b-4098-b205-511f9c5a0a1f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918418941300, "endTime": 154918437855900}, "additional": {"children": ["bb9ae208-568a-40ed-9905-0348f8914a8d", "a111d6b6-1230-4217-a796-194fc59b3492"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "784559e2-3aa6-46d8-8800-e4f788abfe17", "logId": "f1229a5c-daa5-4ec0-a340-27c3e9578e3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb9ae208-568a-40ed-9905-0348f8914a8d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918418942300, "endTime": 154918421023900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec0f8e27-2c7b-4098-b205-511f9c5a0a1f", "logId": "427059d9-59ce-4018-b7c3-a1e45ba6a03b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a111d6b6-1230-4217-a796-194fc59b3492", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918421032100, "endTime": 154918437845000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec0f8e27-2c7b-4098-b205-511f9c5a0a1f", "logId": "363854fd-35b8-4f75-92d3-9a7daf34dcf1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69462e30-0e3c-4990-8bbc-3f4ca7d3e526", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918418944700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff2603a8-2e19-486c-b985-7cde87198885", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918420938500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "427059d9-59ce-4018-b7c3-a1e45ba6a03b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918418942300, "endTime": 154918421023900}, "additional": {"logType": "info", "children": [], "durationId": "bb9ae208-568a-40ed-9905-0348f8914a8d", "parent": "f1229a5c-daa5-4ec0-a340-27c3e9578e3a"}}, {"head": {"id": "4f22ea3a-02dc-4af7-b9fd-d3918ed2f17f", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918421040300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19fa99f1-2807-4136-ab73-799f9cbfe777", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918431505400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e72c26b-2c3c-4adf-8fc0-4f4066d095f2", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918431630500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c9b6097-8569-4f2d-9976-13059e169284", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918431782900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d7a585c-2638-487c-9f0b-73a2929f906d", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918431857600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66080c18-a25e-4035-bdcd-270b9e898b5b", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918431904500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "131c6e80-d985-45c7-8193-5f6ed25c51d8", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918431929600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "123972f4-017e-4d7b-a231-07beface6926", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918431965600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f1dcb8a-1cf7-4a41-a458-7ab7d57cfe60", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918431994300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da81b0c4-7354-4d1a-9b6d-caeabfdab069", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918432124900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96a8f9f2-b8a3-4a1f-bb98-854fd0d9fca5", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918432190700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd6539c3-d694-4f33-9f04-f8ecd4b3896e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918432222200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71c8d4d9-87a2-41bc-8080-cb473949b809", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918432245500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e3852ef-556f-4207-bdb8-aaa2c4c8e470", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918432282900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e675e55-26c0-4cf9-82a4-6a8b22506f9c", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918432320900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d32cb5f-852d-4937-8f22-8c55c20d8043", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918432393400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a69aa39-b632-456d-a0cd-d2f4a8670200", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918432448000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8cb0bb7-c879-48f2-b33b-1408ebdac7e4", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918432474200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ffb79ce-6fa7-4a37-b0f6-3c4ede180e14", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918432495800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31f1fa1d-f291-4311-acd4-b6dbb8d23b94", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918432719400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe6d0df8-d4b5-4f3f-a578-de7254278749", "name": "Module entry task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918437584500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0384337-1f0d-4849-8179-d01a598fa4a8", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918437743100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e2c036a-c894-4212-b560-aab64da72431", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918437788800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2463083f-d025-4d8f-9095-4417b6660fbb", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918437817100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "363854fd-35b8-4f75-92d3-9a7daf34dcf1", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918421032100, "endTime": 154918437845000}, "additional": {"logType": "info", "children": [], "durationId": "a111d6b6-1230-4217-a796-194fc59b3492", "parent": "f1229a5c-daa5-4ec0-a340-27c3e9578e3a"}}, {"head": {"id": "f1229a5c-daa5-4ec0-a340-27c3e9578e3a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918418941300, "endTime": 154918437855900}, "additional": {"logType": "info", "children": ["427059d9-59ce-4018-b7c3-a1e45ba6a03b", "363854fd-35b8-4f75-92d3-9a7daf34dcf1"], "durationId": "ec0f8e27-2c7b-4098-b205-511f9c5a0a1f", "parent": "cf0eb05f-5191-4cde-9ff0-e27d869fefc3"}}, {"head": {"id": "3fd448ea-1740-4c6a-b1fe-7c0651f9f243", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918438971500, "endTime": 154918438982600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "784559e2-3aa6-46d8-8800-e4f788abfe17", "logId": "bc958d62-528c-48de-a27a-7868eb874ccb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc958d62-528c-48de-a27a-7868eb874ccb", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918438971500, "endTime": 154918438982600}, "additional": {"logType": "info", "children": [], "durationId": "3fd448ea-1740-4c6a-b1fe-7c0651f9f243", "parent": "cf0eb05f-5191-4cde-9ff0-e27d869fefc3"}}, {"head": {"id": "cf0eb05f-5191-4cde-9ff0-e27d869fefc3", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918415246300, "endTime": 154918439002700}, "additional": {"logType": "info", "children": ["27dcaf09-a15b-4988-9e3b-6bd9560cb040", "f1229a5c-daa5-4ec0-a340-27c3e9578e3a", "bc958d62-528c-48de-a27a-7868eb874ccb"], "durationId": "784559e2-3aa6-46d8-8800-e4f788abfe17", "parent": "81aee7a2-f95a-4baf-9ebc-42b998f9d573"}}, {"head": {"id": "81aee7a2-f95a-4baf-9ebc-42b998f9d573", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918414745700, "endTime": 154918439012600}, "additional": {"logType": "info", "children": ["cf0eb05f-5191-4cde-9ff0-e27d869fefc3"], "durationId": "aa62a1cc-f73e-425b-bd57-83728cb6805e", "parent": "61286f60-7081-42bd-81c5-f3e74c0f0d2c"}}, {"head": {"id": "cf728bb5-6395-4393-8f2c-f3f1a889e38f", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918451396100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "247dff2b-51c8-4693-be18-bba2d3cb9b00", "name": "hvigorfile, resolve hvigorfile dependencies in 43 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918482084600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79b0a06d-ce0d-4bf2-bee6-2a4202a4e422", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918439062600, "endTime": 154918482233300}, "additional": {"logType": "info", "children": [], "durationId": "88919e9a-5f8d-4254-814f-cf3e05601832", "parent": "61286f60-7081-42bd-81c5-f3e74c0f0d2c"}}, {"head": {"id": "4be2f275-ecf0-4712-93d4-632faf6edc87", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918483040100, "endTime": 154918483551400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0fe74d3-4c27-4623-80fa-ecf587276ca3", "logId": "9acf5d97-0394-4230-8d69-77ec92213139"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eaf910b4-dc77-4f0f-9c01-83c47db4923d", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918483211500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9acf5d97-0394-4230-8d69-77ec92213139", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918483040100, "endTime": 154918483551400}, "additional": {"logType": "info", "children": [], "durationId": "4be2f275-ecf0-4712-93d4-632faf6edc87", "parent": "61286f60-7081-42bd-81c5-f3e74c0f0d2c"}}, {"head": {"id": "db5e18f4-a3d0-4fbe-be44-524df5da2380", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918485378600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9adc993c-2e83-4394-a600-3f27b30a33d3", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918492444600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39fbd8ce-18dd-4d79-b14a-8eb17839b39d", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918483570600, "endTime": 154918493507900}, "additional": {"logType": "info", "children": [], "durationId": "a18a765c-96ff-4162-90c1-4dfe28a2b977", "parent": "61286f60-7081-42bd-81c5-f3e74c0f0d2c"}}, {"head": {"id": "c413ef0a-5a08-4ad3-a14a-0e57a6a33da2", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918493618900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ee9703e-3eb9-45cb-8f99-a03a0561b2ff", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918501171300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f02238e0-67a3-4697-abed-eb0182ab9df1", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918501295100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e61e219d-d048-4da3-b7a5-95dba5f47530", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918501903500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43f17ede-4c77-428d-99de-861da9966000", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918504986800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c662656b-3a6b-4aac-b59f-b941f765f8ca", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918505080800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8662f5b9-9744-42a6-9d81-ad7d8e049a86", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918493525400, "endTime": 154918509875400}, "additional": {"logType": "info", "children": [], "durationId": "60e74758-ea65-444b-ba9c-705eb94d5692", "parent": "61286f60-7081-42bd-81c5-f3e74c0f0d2c"}}, {"head": {"id": "ad59eda3-e435-4237-926c-63635999fff5", "name": "Configuration phase cost:193 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918509909600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59695003-b7e3-43aa-a8be-f5722e528cf6", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918509890700, "endTime": 154918509992200}, "additional": {"logType": "info", "children": [], "durationId": "342ec31c-2eb9-448a-b711-48157ae8edfb", "parent": "61286f60-7081-42bd-81c5-f3e74c0f0d2c"}}, {"head": {"id": "61286f60-7081-42bd-81c5-f3e74c0f0d2c", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918313351000, "endTime": 154918510002200}, "additional": {"logType": "info", "children": ["9a41ae89-aceb-4fef-bf55-4ae8c5729d25", "87b4c058-e60b-4199-b7dc-ba482b0ec985", "fd9f5c7f-4e96-42a8-bd07-4578816b7d9b", "81aee7a2-f95a-4baf-9ebc-42b998f9d573", "79b0a06d-ce0d-4bf2-bee6-2a4202a4e422", "39fbd8ce-18dd-4d79-b14a-8eb17839b39d", "8662f5b9-9744-42a6-9d81-ad7d8e049a86", "59695003-b7e3-43aa-a8be-f5722e528cf6", "9acf5d97-0394-4230-8d69-77ec92213139"], "durationId": "c0fe74d3-4c27-4623-80fa-ecf587276ca3", "parent": "d9b4d673-0cc8-4599-8607-b27dbfd2231c"}}, {"head": {"id": "937dc17d-31e7-4ba0-95f5-1ceae94396d4", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918511609200, "endTime": 154918511638500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bc4be02e-b06d-4baa-98ee-677b3042f958", "logId": "c0046fc5-a584-4e0a-940e-e11dda99c468"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0046fc5-a584-4e0a-940e-e11dda99c468", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918511609200, "endTime": 154918511638500}, "additional": {"logType": "info", "children": [], "durationId": "937dc17d-31e7-4ba0-95f5-1ceae94396d4", "parent": "d9b4d673-0cc8-4599-8607-b27dbfd2231c"}}, {"head": {"id": "1e992c3a-83ec-4cd2-a807-d1fe957ebf7b", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918510026300, "endTime": 154918511651400}, "additional": {"logType": "info", "children": [], "durationId": "54a5da13-5f3e-4c0a-bceb-0f8444991f36", "parent": "d9b4d673-0cc8-4599-8607-b27dbfd2231c"}}, {"head": {"id": "c5dce8b2-06d3-4bb1-9cba-c68406696403", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918511656300, "endTime": 154918511658500}, "additional": {"logType": "info", "children": [], "durationId": "442cc61a-9a4f-4e2b-a82d-1ec4245b128f", "parent": "d9b4d673-0cc8-4599-8607-b27dbfd2231c"}}, {"head": {"id": "d9b4d673-0cc8-4599-8607-b27dbfd2231c", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918299152900, "endTime": 154918511663000}, "additional": {"logType": "info", "children": ["a0218063-f448-4a3d-bdf0-420cc3c7b892", "61286f60-7081-42bd-81c5-f3e74c0f0d2c", "1e992c3a-83ec-4cd2-a807-d1fe957ebf7b", "c5dce8b2-06d3-4bb1-9cba-c68406696403", "069021a4-0604-4724-98d2-075251006106", "cc2cefce-e981-4f04-9fe1-8f16a1721543", "c0046fc5-a584-4e0a-940e-e11dda99c468"], "durationId": "bc4be02e-b06d-4baa-98ee-677b3042f958"}}, {"head": {"id": "ef891359-cb84-4be6-8a3d-1ffbda5db5cc", "name": "Configuration task cost before running: 217 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918512173700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12f823c5-b6de-421d-a6cd-59f1cdf5e9b4", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918526348300, "endTime": 154918539645600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "b687dee8-fde3-4ba5-abcf-2f7f810ca533", "logId": "cbd140a7-0559-45bc-b54d-37a3b8013a0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b687dee8-fde3-4ba5-abcf-2f7f810ca533", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918514253100}, "additional": {"logType": "detail", "children": [], "durationId": "12f823c5-b6de-421d-a6cd-59f1cdf5e9b4"}}, {"head": {"id": "1d61bbd9-6b98-4142-a783-7a5fd30ef47e", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918515111000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea801853-b111-4fbb-a3bc-4e512f2a8289", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918515355000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91835b37-543c-4117-9f22-d3dd5fc84261", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918516412300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db7ef2b7-f776-4f11-8e67-cd0287cdfe9e", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918517717500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2e4f8c5-9e1e-40ff-8730-48c0261361f2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918519083300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27f4483c-3729-42c7-888e-2897c6f0c961", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918519200100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ef54d42-890b-4665-b8b5-4ebc95a966de", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918526372800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9955752d-fe5b-4fb0-8c4b-e359454bf45e", "name": "Incremental task entry:default@PreBuild pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918539408100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b97bcadf-1390-4372-aa0f-aabb24905c94", "name": "entry : default@PreBuild cost memory -3.5787277221679688", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918539562400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbd140a7-0559-45bc-b54d-37a3b8013a0c", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918526348300, "endTime": 154918539645600}, "additional": {"logType": "info", "children": [], "durationId": "12f823c5-b6de-421d-a6cd-59f1cdf5e9b4"}}, {"head": {"id": "de8d3217-7fc1-4718-8260-ed8206121e2b", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918548715500, "endTime": 154918550530800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0e68840c-0441-482e-97d9-48ebee375eef", "logId": "12d77def-60e1-42a9-a56f-31cc2e5cd724"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e68840c-0441-482e-97d9-48ebee375eef", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918545769200}, "additional": {"logType": "detail", "children": [], "durationId": "de8d3217-7fc1-4718-8260-ed8206121e2b"}}, {"head": {"id": "e8d53e77-20ac-4017-97b6-2295a6ea5137", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918547893800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f66c0c36-0ba7-419c-acf1-9af52b724a0c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918548022400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d92f56d9-09fb-4227-abf5-69219f12c7df", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918548726200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6393b92-2314-4aad-b33f-53738d286d44", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918549444200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fc76243-fb24-4918-92d4-7b5bc3514a44", "name": "entry : default@CreateModuleInfo cost memory 0.05968475341796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918550365700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4047c464-83c3-4f80-9d3f-f76a7fc9736c", "name": "runTaskFromQueue task cost before running: 255 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918550478800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12d77def-60e1-42a9-a56f-31cc2e5cd724", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918548715500, "endTime": 154918550530800, "totalTime": 1746100}, "additional": {"logType": "info", "children": [], "durationId": "de8d3217-7fc1-4718-8260-ed8206121e2b"}}, {"head": {"id": "f059898f-75a6-4d3c-97cd-b0e69cf55eae", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918558762700, "endTime": 154918561911000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9e78621c-7fdc-443d-85bd-c56b8a994556", "logId": "0a674b01-eaf4-4d56-a728-27ccef7449f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e78621c-7fdc-443d-85bd-c56b8a994556", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918552856300}, "additional": {"logType": "detail", "children": [], "durationId": "f059898f-75a6-4d3c-97cd-b0e69cf55eae"}}, {"head": {"id": "9bdccd5b-afc3-4ce2-85c4-7289f9b8e232", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918553927400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b563388-05c7-4000-b683-c8c783f3ef1f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918554030900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41430a38-4eb7-4a4a-ac3d-f4109141c6c7", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918558777600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f343689-dda3-4d1d-94e7-c03853afe152", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918560047900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d55cf91-8748-466a-acdf-0888fda7cd35", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918561732400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b08b1bb-75b2-4e61-8ae0-5e11ed770762", "name": "entry : default@GenerateMetadata cost memory 0.101806640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918561851100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a674b01-eaf4-4d56-a728-27ccef7449f1", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918558762700, "endTime": 154918561911000}, "additional": {"logType": "info", "children": [], "durationId": "f059898f-75a6-4d3c-97cd-b0e69cf55eae"}}, {"head": {"id": "6a687c64-9015-41fb-9f8b-c6879f17698e", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918565657900, "endTime": 154918565980300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8f9f284b-c45e-4d3a-8c0a-5302b8ca9a14", "logId": "3df6c8de-4dab-4061-b645-d35dde075466"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f9f284b-c45e-4d3a-8c0a-5302b8ca9a14", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918564136600}, "additional": {"logType": "detail", "children": [], "durationId": "6a687c64-9015-41fb-9f8b-c6879f17698e"}}, {"head": {"id": "78433f97-ae65-402c-ba3a-d5a78dddea95", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918565263500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f70f49b2-a0e9-484b-849c-b54bf6445713", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918565392100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6fdade6-44c4-4406-975e-d439c0a25565", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918565666800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e20ab712-821c-4a6b-bb2e-396d93e23ba8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918565769200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35b1585e-e2d1-42a9-8567-38590deff6d7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918565809400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bae9143-d4b0-4e75-a86a-ba10344ab88b", "name": "entry : default@ConfigureCmake cost memory 0.0373382568359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918565877800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4951a799-ab6d-4725-969e-a58ac3fabb76", "name": "runTaskFromQueue task cost before running: 270 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918565937300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3df6c8de-4dab-4061-b645-d35dde075466", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918565657900, "endTime": 154918565980300, "totalTime": 264500}, "additional": {"logType": "info", "children": [], "durationId": "6a687c64-9015-41fb-9f8b-c6879f17698e"}}, {"head": {"id": "a2b7af82-5fb4-4b61-b23a-6476228605e9", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918569698800, "endTime": 154918572019300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "5f223ca6-8ed7-424d-bd78-6b0b07a347be", "logId": "fabf334d-f699-4ab3-879f-5e92813e1fb1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f223ca6-8ed7-424d-bd78-6b0b07a347be", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918567781000}, "additional": {"logType": "detail", "children": [], "durationId": "a2b7af82-5fb4-4b61-b23a-6476228605e9"}}, {"head": {"id": "43f435c6-eb66-4550-a962-6e320ea5bcc1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918568855700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f3d4ce2-34cb-4a30-b942-58d932b44d18", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918568961400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b243803-f2d0-4e57-8ea4-3c5853dd3f5b", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918569708300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bccf07c-2381-42eb-8665-cfaa64a84887", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918571845900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "344c7a21-eb30-4dc2-a1ab-fad87c6eeacc", "name": "entry : default@MergeProfile cost memory 0.1181182861328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918571955600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fabf334d-f699-4ab3-879f-5e92813e1fb1", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918569698800, "endTime": 154918572019300}, "additional": {"logType": "info", "children": [], "durationId": "a2b7af82-5fb4-4b61-b23a-6476228605e9"}}, {"head": {"id": "d5ec1eab-9751-44e8-a921-317f864c358a", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918576519900, "endTime": 154918579105300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "596d3e09-63cf-4655-8895-80ef7e7124bb", "logId": "4f89f72a-9863-47d4-a6bf-1aa74a321837"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "596d3e09-63cf-4655-8895-80ef7e7124bb", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918574537700}, "additional": {"logType": "detail", "children": [], "durationId": "d5ec1eab-9751-44e8-a921-317f864c358a"}}, {"head": {"id": "a0c751b2-41c9-4981-b498-0f232f8c0eba", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918575586300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dce1f57e-5940-49d4-8c0b-fa5e8daef6f8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918575684700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec5f6ec5-bfa7-45d3-ba9b-993a85f80670", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918576528100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcdce1b3-cabd-47a4-a935-f1a9b89075d7", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918577497000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79e358e4-aaa1-4e48-be2c-02a8cb68754d", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918578945900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f78362f9-8ac6-4c61-9b33-964f97d6e931", "name": "entry : default@CreateBuildProfile cost memory 0.10666656494140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918579049400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f89f72a-9863-47d4-a6bf-1aa74a321837", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918576519900, "endTime": 154918579105300}, "additional": {"logType": "info", "children": [], "durationId": "d5ec1eab-9751-44e8-a921-317f864c358a"}}, {"head": {"id": "f659ef62-7d3f-455d-bc94-57644475215b", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918582502300, "endTime": 154918583113900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "8d471088-682a-49d5-a516-2f6e6df48696", "logId": "98114eee-426c-4f0f-8013-28f453072bd3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d471088-682a-49d5-a516-2f6e6df48696", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918580726000}, "additional": {"logType": "detail", "children": [], "durationId": "f659ef62-7d3f-455d-bc94-57644475215b"}}, {"head": {"id": "56d2e4c2-2a87-4261-976e-1f867fa23fb9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918581686900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80668a8d-a958-4cc3-9784-350da2659898", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918581799700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1ebdd1b-970c-4838-b0ae-fa79013137d9", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918582510000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b19310f9-aa18-4048-86ba-cd3db83e7379", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918582624300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25039aa9-a817-4e83-b163-736c16de4b61", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918582666000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "130c2461-6160-4169-937c-6edd707570d5", "name": "entry : default@PreCheckSyscap cost memory 0.0409698486328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918582973100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5087669e-b9f5-4007-9924-33537bf97bd2", "name": "runTaskFromQueue task cost before running: 288 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918583066200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98114eee-426c-4f0f-8013-28f453072bd3", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918582502300, "endTime": 154918583113900, "totalTime": 545800}, "additional": {"logType": "info", "children": [], "durationId": "f659ef62-7d3f-455d-bc94-57644475215b"}}, {"head": {"id": "b3210844-1741-4a8d-ae0e-438562feaa1a", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918587161900, "endTime": 154918592706400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "194cb365-7087-4516-b047-6704ea20513e", "logId": "b6332bb0-b333-4140-a567-208c5c5d148d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "194cb365-7087-4516-b047-6704ea20513e", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918584748600}, "additional": {"logType": "detail", "children": [], "durationId": "b3210844-1741-4a8d-ae0e-438562feaa1a"}}, {"head": {"id": "d0ecc6f2-0999-41b9-93d5-5e26158fc153", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918585704000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee53dce3-04a7-4213-b0f9-eb6ab03a3105", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918585811100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cac8101-57ce-4341-bffa-e2d862855eac", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918587170900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "837162e1-391a-4634-a274-cdbd8c99c832", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918591906000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dcb04d1-c7a0-4c96-ba0f-5f6d1a1c5faa", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918592556200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b7f3694-f572-40df-b1a2-3c5d92b16a1f", "name": "entry : default@GeneratePkgContextInfo cost memory 0.251434326171875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918592652400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6332bb0-b333-4140-a567-208c5c5d148d", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918587161900, "endTime": 154918592706400}, "additional": {"logType": "info", "children": [], "durationId": "b3210844-1741-4a8d-ae0e-438562feaa1a"}}, {"head": {"id": "4266d668-c25d-46d3-99d1-2bfe7696e5a9", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918599301300, "endTime": 154918601188000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "b68b370a-4613-4f66-8187-854257b3c98a", "logId": "8bfeab49-77c1-4502-b864-d3f68f725652"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b68b370a-4613-4f66-8187-854257b3c98a", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918594284000}, "additional": {"logType": "detail", "children": [], "durationId": "4266d668-c25d-46d3-99d1-2bfe7696e5a9"}}, {"head": {"id": "f836dbe7-960f-4b26-bbf9-a38097e5309f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918595141000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aba5cb51-17e7-43d3-873f-4b5b751a6565", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918595224200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "645c0f12-6c87-435e-b40e-ead547857fb8", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918599315000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74f8f2dc-e07a-4088-ac81-3072bad7085b", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918600835500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f693d87d-4651-46df-8ece-09f182a0f406", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918600934000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "243c890e-d6ef-47a4-a7a6-548f1aeda857", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918601000900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a97d4e3b-b0f1-4306-a1b3-7d6372447d25", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918601035000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fddaf677-e258-48d7-a270-24a37045da67", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11899566650390625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918601092100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3761651-83da-486c-84fa-a04d64e6da47", "name": "runTaskFromQueue task cost before running: 306 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918601151400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bfeab49-77c1-4502-b864-d3f68f725652", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918599301300, "endTime": 154918601188000, "totalTime": 1840800}, "additional": {"logType": "info", "children": [], "durationId": "4266d668-c25d-46d3-99d1-2bfe7696e5a9"}}, {"head": {"id": "84ea3a0c-d0ce-4a83-a12c-c9bbdb401c8d", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918604998300, "endTime": 154918605304500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "885ee4b1-07e3-4da1-a6c4-a207517085b9", "logId": "56eae374-10b3-4a30-bc14-a1405ca72a2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "885ee4b1-07e3-4da1-a6c4-a207517085b9", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918603117400}, "additional": {"logType": "detail", "children": [], "durationId": "84ea3a0c-d0ce-4a83-a12c-c9bbdb401c8d"}}, {"head": {"id": "fcb25096-7278-4657-a3b1-c9c8cbc0ac29", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918604192100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18898e98-7717-44d7-bf65-2990c9ee8df8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918604282100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9222d58-7514-47bf-891a-1d65ecc20a9d", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918605004700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82fb24e2-da1b-4a15-8f93-2a6f48019b54", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918605110400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e168b04f-b33f-4760-aeb9-a5632890a878", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918605151600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cebe1e10-411b-451d-a5d2-d3992b22b8d0", "name": "entry : default@BuildNativeWithCmake cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918605202800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98d7f797-a99a-49e4-9b01-fa9a4577a6e6", "name": "runTaskFromQueue task cost before running: 310 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918605271200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56eae374-10b3-4a30-bc14-a1405ca72a2b", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918604998300, "endTime": 154918605304500, "totalTime": 259700}, "additional": {"logType": "info", "children": [], "durationId": "84ea3a0c-d0ce-4a83-a12c-c9bbdb401c8d"}}, {"head": {"id": "0a7d6d28-3a8e-400a-8f61-345d72fde61d", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918608368300, "endTime": 154918611714800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bd783127-65e2-4302-ad77-fef4cb1927f1", "logId": "08aa3d2a-747a-4c33-acee-1f2caa014d36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd783127-65e2-4302-ad77-fef4cb1927f1", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918606819000}, "additional": {"logType": "detail", "children": [], "durationId": "0a7d6d28-3a8e-400a-8f61-345d72fde61d"}}, {"head": {"id": "75b322c7-3f1c-41bf-b26e-2a3f4e06969b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918607688600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0d02446-3c6f-4764-bb9e-5e6138694a1b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918607785600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0efe194e-3b8d-4774-a0fd-54f5e04468ea", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918608374800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "550f8e14-c7df-48b8-9eb2-52ad64044d74", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918611569000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "834800c3-4896-4178-a1ab-098a122ddb30", "name": "entry : default@MakePackInfo cost memory 0.16280364990234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918611663000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08aa3d2a-747a-4c33-acee-1f2caa014d36", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918608368300, "endTime": 154918611714800}, "additional": {"logType": "info", "children": [], "durationId": "0a7d6d28-3a8e-400a-8f61-345d72fde61d"}}, {"head": {"id": "1987cf8e-681c-4edc-86f7-dfc7cd0e073a", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918615994700, "endTime": 154918619377800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4f0a2a79-e011-4b21-b570-0f2e8a97c1d7", "logId": "155f80ad-42d7-447f-a5bd-8c6c7d650a79"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f0a2a79-e011-4b21-b570-0f2e8a97c1d7", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918613867900}, "additional": {"logType": "detail", "children": [], "durationId": "1987cf8e-681c-4edc-86f7-dfc7cd0e073a"}}, {"head": {"id": "30e5c1f1-2874-4be6-b649-42b94d51b645", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918614863300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e720075-2c79-483f-9a74-977911bea502", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918614970100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57b345b5-81a3-46d4-9dfa-54ac9e3fc44e", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918616002800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dc8d707-d7d3-4679-b64d-4c7b809dff9a", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918616191900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0e579e9-fecd-4a54-9bf3-6744cdb4f951", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918617056000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fec73095-386a-497f-93e4-1a5a56402f51", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918619132300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ede0f20-abe7-4f5b-a439-2b9273088de6", "name": "entry : default@SyscapTransform cost memory 0.1496734619140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918619272000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "155f80ad-42d7-447f-a5bd-8c6c7d650a79", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918615994700, "endTime": 154918619377800}, "additional": {"logType": "info", "children": [], "durationId": "1987cf8e-681c-4edc-86f7-dfc7cd0e073a"}}, {"head": {"id": "17193995-bba2-431c-a5aa-10100516d0b1", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918623532400, "endTime": 154918625536100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "e9cd1210-bcdc-4196-8bea-b7bc486cf309", "logId": "89f7d2a7-07d8-4640-93e2-0360718744e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9cd1210-bcdc-4196-8bea-b7bc486cf309", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918621281100}, "additional": {"logType": "detail", "children": [], "durationId": "17193995-bba2-431c-a5aa-10100516d0b1"}}, {"head": {"id": "3ea359c3-73d0-49e3-9e63-29e57ccc6cdc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918622151500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f51dc19-8ad7-4f28-8491-add93ffbd842", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918622234500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d018a45-b37a-41e2-ba9f-8057f11e2a16", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918623539800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ae5c0a0-8fbf-4d9c-b172-b482b573f8cf", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918625382600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "385dec80-5dc9-4e29-ba86-8a8748242c69", "name": "entry : default@ProcessProfile cost memory 0.1265869140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918625483500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89f7d2a7-07d8-4640-93e2-0360718744e7", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918623532400, "endTime": 154918625536100}, "additional": {"logType": "info", "children": [], "durationId": "17193995-bba2-431c-a5aa-10100516d0b1"}}, {"head": {"id": "50e5e695-6d5c-4b17-a0d9-63fe9db26f36", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918631627100, "endTime": 154918643517400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "13915b52-feaf-4980-9d4e-d2de396be15b", "logId": "5bb38e67-2880-402c-b8ff-73fbd9f20d3d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13915b52-feaf-4980-9d4e-d2de396be15b", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918628742200}, "additional": {"logType": "detail", "children": [], "durationId": "50e5e695-6d5c-4b17-a0d9-63fe9db26f36"}}, {"head": {"id": "e7056e05-c18e-4286-b7af-bb38cf46e426", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918629762600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38d59fe4-0363-4143-9c33-f1cdace106b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918629981800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb85eee4-d01b-418c-a45f-9d74b1f5f41f", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918631635600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4211dec2-1701-4fcc-9e40-ba66e5a5102d", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918643199200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ef7be70-77b5-4656-abfc-ce7124e93256", "name": "entry : default@ProcessRouterMap cost memory 0.23166656494140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918643401900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bb38e67-2880-402c-b8ff-73fbd9f20d3d", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918631627100, "endTime": 154918643517400}, "additional": {"logType": "info", "children": [], "durationId": "50e5e695-6d5c-4b17-a0d9-63fe9db26f36"}}, {"head": {"id": "940e19f7-a789-472e-807f-f1d51499d7a7", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918655318600, "endTime": 154918661028700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "31f6df4e-ea52-4cc1-b07e-7da7ee75b01f", "logId": "9b167306-8cf1-4596-a45e-4a3f3afafbc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31f6df4e-ea52-4cc1-b07e-7da7ee75b01f", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918647838000}, "additional": {"logType": "detail", "children": [], "durationId": "940e19f7-a789-472e-807f-f1d51499d7a7"}}, {"head": {"id": "de556571-2c82-4775-9e88-adfcd8b09b58", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918655110800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1123c167-0c9b-4dbd-92d2-4449c0c808c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918655231700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a15b5a69-5cc6-46a7-b8d2-999c65c00550", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918655325100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fd727ad-59a9-4f7f-8030-f081cd4dc258", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918655468600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe0b3844-baa5-4b4a-8b73-472c84323c0f", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918659288100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6283d4f9-644c-46d2-81b4-840427f3d7e7", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918659420900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d15277a1-5bf5-4723-9820-4dcfd875b108", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918659498300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27aea2ff-75a8-4877-9adb-84ce98a08916", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918659537100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf9c6da7-37ca-406f-8969-b668d55c88a5", "name": "entry : default@ProcessStartupConfig cost memory 0.280029296875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918660846000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6038779-c3b8-48e5-b2a1-765a5995cd06", "name": "runTaskFromQueue task cost before running: 365 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918660975500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b167306-8cf1-4596-a45e-4a3f3afafbc9", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918655318600, "endTime": 154918661028700, "totalTime": 5635800}, "additional": {"logType": "info", "children": [], "durationId": "940e19f7-a789-472e-807f-f1d51499d7a7"}}, {"head": {"id": "3e9148b9-f135-4f1c-b6e4-7d4383950643", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918665813600, "endTime": 154918667161900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8117634a-3a6d-46d8-ba89-46c28f6cffa4", "logId": "33d75d60-5fa3-4d60-9914-57b22e47ca53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8117634a-3a6d-46d8-ba89-46c28f6cffa4", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918663928300}, "additional": {"logType": "detail", "children": [], "durationId": "3e9148b9-f135-4f1c-b6e4-7d4383950643"}}, {"head": {"id": "88a4f31d-0b99-418f-bb76-343e37c44c96", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918664939500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4bcc3a5-821a-4144-8126-133cbd0b1c4e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918665048900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43c67d29-3821-495b-89cd-3e0e83bd0279", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918665826200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05af16d6-9b99-4c5b-8a5c-a51d064b1ea8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918665946300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b98acc7-764a-42de-ac51-7ab7846e6474", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918666066500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e6d365f-52e1-4edb-8375-fb1686ebefa5", "name": "entry : default@BuildNativeWithNinja cost memory 0.06702423095703125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918666994100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee13b91b-f68c-4dfa-a5cb-c32449213a9b", "name": "runTaskFromQueue task cost before running: 372 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918667110300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33d75d60-5fa3-4d60-9914-57b22e47ca53", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918665813600, "endTime": 154918667161900, "totalTime": 1277800}, "additional": {"logType": "info", "children": [], "durationId": "3e9148b9-f135-4f1c-b6e4-7d4383950643"}}, {"head": {"id": "2774daac-0a95-42c1-ad29-e4a9404c8426", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918673489500, "endTime": 154918678812100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "9e7d855d-5358-4553-8b10-d5a710a8f299", "logId": "f0ebc46e-29bd-47dc-a0d3-d904a4ca358b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e7d855d-5358-4553-8b10-d5a710a8f299", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918669710500}, "additional": {"logType": "detail", "children": [], "durationId": "2774daac-0a95-42c1-ad29-e4a9404c8426"}}, {"head": {"id": "9a1de0aa-38af-441a-9235-1116209ec511", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918670817900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0d9e1d1-0661-4245-81e5-35bf84b4af63", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918670906600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae1a2282-5a69-4598-ae90-b92ea8b24f4e", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918672066700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06bcbda7-9ec5-4acf-afe2-9b771946f4a9", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918675066800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f97f0895-9577-4299-89e3-3d3eb6c73076", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918677210100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "076729ac-5d60-448e-9f27-13d66b56c7e7", "name": "entry : default@ProcessResource cost memory 0.161102294921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918677315100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0ebc46e-29bd-47dc-a0d3-d904a4ca358b", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918673489500, "endTime": 154918678812100}, "additional": {"logType": "info", "children": [], "durationId": "2774daac-0a95-42c1-ad29-e4a9404c8426"}}, {"head": {"id": "eb914e15-32fb-4457-a5a9-4c3669a3ef80", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918687579200, "endTime": 154918707371400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b722946d-1e75-481c-9906-6e4b4f88c366", "logId": "3e0719dd-3dac-4cdd-adb4-0d49a98c4f15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b722946d-1e75-481c-9906-6e4b4f88c366", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918682649300}, "additional": {"logType": "detail", "children": [], "durationId": "eb914e15-32fb-4457-a5a9-4c3669a3ef80"}}, {"head": {"id": "a7c534da-496c-4aad-92af-faa3590a1e8e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918683625500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "add2f8e0-a8af-48fa-b558-142c9f64daf5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918683739500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ada3074c-4c69-4a12-85c0-e1d761b1028d", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918687590400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea3aa5f1-36cd-4634-96fe-d2a763ccbd48", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918707183200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c00b23f-7865-4220-9dc0-302e2ce7473a", "name": "entry : default@GenerateLoaderJson cost memory 0.875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918707315400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e0719dd-3dac-4cdd-adb4-0d49a98c4f15", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918687579200, "endTime": 154918707371400}, "additional": {"logType": "info", "children": [], "durationId": "eb914e15-32fb-4457-a5a9-4c3669a3ef80"}}, {"head": {"id": "e2c90a63-195f-4263-8be3-e9c38b6723d3", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918717549000, "endTime": 154918722069800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "116645c1-414a-4a10-acf2-ed5ab7e793d8", "logId": "4fbee4ba-c677-4ce0-964b-e447e1a74a88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "116645c1-414a-4a10-acf2-ed5ab7e793d8", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918715300300}, "additional": {"logType": "detail", "children": [], "durationId": "e2c90a63-195f-4263-8be3-e9c38b6723d3"}}, {"head": {"id": "17d690f8-d209-476c-b8e4-05bb869ba72e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918716286600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "204bb16c-c95b-4236-9128-d6901d142e2e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918716478600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d324c4f6-51e9-46cc-b5e8-8b0b5b2a05f7", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918717558400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "167fde9e-2ce3-4ab3-aa7a-fed011b869e5", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918721907100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbcc7d41-79df-4131-9554-114e2b20b33c", "name": "entry : default@ProcessLibs cost memory 0.142852783203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918722013400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fbee4ba-c677-4ce0-964b-e447e1a74a88", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918717549000, "endTime": 154918722069800}, "additional": {"logType": "info", "children": [], "durationId": "e2c90a63-195f-4263-8be3-e9c38b6723d3"}}, {"head": {"id": "1a02a01c-1138-452f-8d70-9721557faff0", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918728376500, "endTime": 154918753000100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "34c2c00c-62ab-4dbc-a351-45271feb3dd5", "logId": "57b4feb8-9fd3-4abc-ad9f-8cddf56d7824"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34c2c00c-62ab-4dbc-a351-45271feb3dd5", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918723994500}, "additional": {"logType": "detail", "children": [], "durationId": "1a02a01c-1138-452f-8d70-9721557faff0"}}, {"head": {"id": "dcf11fbb-4b99-4525-b4e0-113a73eca7d5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918724899800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53fe8f36-aadd-42c8-b6d9-1c5efd112530", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918724991000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ad438da-4403-4d4c-a962-15af7a6d2d01", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918726025900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc2a4921-0d7b-4f8d-a2bb-80eaf60959ea", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918728405300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4c37cc4-dabd-4061-aafa-2669bd99be2e", "name": "Incremental task entry:default@CompileResource pre-execution cost: 24 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918752706800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "181cb9df-55f8-46d6-a281-267b71ace954", "name": "entry : default@CompileResource cost memory 1.3152694702148438", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918752891800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57b4feb8-9fd3-4abc-ad9f-8cddf56d7824", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918728376500, "endTime": 154918753000100}, "additional": {"logType": "info", "children": [], "durationId": "1a02a01c-1138-452f-8d70-9721557faff0"}}, {"head": {"id": "77dcc4cc-49c9-43c6-92f2-71cc2f306071", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918759740600, "endTime": 154918761880300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7717998e-87e6-42ff-a4d1-d8d962b62f8c", "logId": "078c980b-43c3-4f97-b222-b16097b26098"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7717998e-87e6-42ff-a4d1-d8d962b62f8c", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918756255100}, "additional": {"logType": "detail", "children": [], "durationId": "77dcc4cc-49c9-43c6-92f2-71cc2f306071"}}, {"head": {"id": "7230c1b1-da0e-4cde-aef6-085ff7becb09", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918757407800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "600c45ab-2e13-407d-9c70-9fc7329072ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918757510100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a45c4578-d5c1-401c-a646-266df4fb906f", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918759749000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5015a42-ce90-4fc5-ae38-fffb806c7fb2", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918760252200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26ec16e3-95b0-4383-ab9d-ee3669641c28", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918761701200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94a7d5ae-6daf-487a-a626-14affe07d834", "name": "entry : default@DoNativeStrip cost memory 0.0807952880859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918761813400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "078c980b-43c3-4f97-b222-b16097b26098", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918759740600, "endTime": 154918761880300}, "additional": {"logType": "info", "children": [], "durationId": "77dcc4cc-49c9-43c6-92f2-71cc2f306071"}}, {"head": {"id": "d3867aba-884f-4eac-9393-5fabf3679740", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918768681500, "endTime": 154930102234600}, "additional": {"children": ["ce76fbbe-b9c6-42d0-9e73-d3fc843761ba", "986a0ce8-beda-4358-911e-59c7b53de129"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "87b1314d-bb53-4349-adfd-617edb2964f4", "logId": "29452e61-49dd-4f8f-a262-4d32fe6f1466"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87b1314d-bb53-4349-adfd-617edb2964f4", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918763660100}, "additional": {"logType": "detail", "children": [], "durationId": "d3867aba-884f-4eac-9393-5fabf3679740"}}, {"head": {"id": "0fb8ed03-862c-4d94-bae1-83860a1cc58d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918764819700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0205190-58aa-40c5-83ec-3d96c5951b1a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918764924500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "204793a8-3336-4fd4-a648-df92a3c7fd45", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918768690700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e458c913-2c3b-48ab-9c93-9f976af3d4e5", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918768835100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45d6b26e-512b-40be-89b8-fee871853bbf", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918800885900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ebf2eb8-76f1-4baa-8022-860cabc7ad04", "name": "default@CompileArkTS work[16] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918803054700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce76fbbe-b9c6-42d0-9e73-d3fc843761ba", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154921862673500, "endTime": 154930094999600}, "additional": {"children": ["4186c5a2-7ff0-4c24-9696-5c3ef2528592", "6454afc1-1f85-4830-a2bf-dc2d5782aa3b", "64aae306-5741-4ed6-b93f-8df5174c48a3", "62f2db1c-4714-4568-9b88-e78a8ce8e865", "8789b005-4480-4d04-85b5-75b7c49f63ab", "4340e468-526f-4bd4-b4d1-6d722d955633", "2cbcc80d-c4f6-4dac-9e71-0f701d609e62"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "d3867aba-884f-4eac-9393-5fabf3679740", "logId": "09c5f69c-e70c-4cfb-a05a-cc14bdbe8c67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "455c9b8f-c07c-4ede-aa97-652c90adbf40", "name": "default@CompileArkTS work[16] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804045000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f225f138-b809-443b-aa4b-b6740598125a", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804126400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e838d99-c475-4337-a61b-bcf3af99e59d", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804165100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29d64034-595b-4143-a575-54e57804e982", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804190300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88d2a143-d584-40d1-8d27-62bc001de177", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804213600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f73e9f1-2001-4ed4-bd1a-23a3fe161fed", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804236600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e81d5ea8-ed37-4297-9841-19cd3f5689e3", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804260400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a71f6d3f-54c1-4829-844f-0ca7920a52c2", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804283200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa08be38-3228-427e-a296-b489a336fc08", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804304700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48998400-5f27-460f-99c0-cbe3f8347fd7", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804325800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99d08aa6-ff01-4f9e-a92d-5f3bb38bbd20", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804346800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fec0388a-343f-48b8-8b7d-7110cba94072", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804368200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51de66fb-51cb-4f19-8421-46835e60d461", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804390200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5129a9b3-9271-4c5a-b692-aa021426a6ad", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804412800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31e679a5-32e5-4db5-af58-cc6750387d41", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804445000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c702cc08-5ac4-4d18-acf5-dd9a29370024", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804468100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffec484c-ff14-43f0-8f67-03ece8c6bbcd", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918804597600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71db8746-1f76-4bcd-86b0-7ff75d072c75", "name": "default@CompileArkTS work[16] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918805560400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae7d3981-f6f5-43bd-bf3e-8b6569a07738", "name": "default@CompileArkTS work[16] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918805682000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93199e55-9164-409f-bb55-76a92927e057", "name": "CopyResources startTime: 154918805729900", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918805731500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0739d3b2-40ca-4c64-9617-f4465155a3bb", "name": "default@CompileArkTS work[17] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918805785500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "986a0ce8-beda-4358-911e-59c7b53de129", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 154920034452800, "endTime": 154920049119000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "d3867aba-884f-4eac-9393-5fabf3679740", "logId": "84f33b50-eb3f-4f2c-a347-59d782184d75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6dd32ebe-44bd-4b61-a976-cbb9a44186b0", "name": "default@CompileArkTS work[17] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918806630600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f645f6d7-d11c-495f-a4ef-47207455a2ee", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918806732800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27dedbb1-b733-48a8-ba37-c14f21cfe496", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918806809200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf6b3031-ae94-48f1-b224-346dfeb1359b", "name": "default@CompileArkTS work[17] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918807644800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f519ee33-2e15-4661-9289-009a56f526a5", "name": "default@CompileArkTS work[17] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918807726500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc595cff-ca46-4292-b1df-216064bceede", "name": "entry : default@CompileArkTS cost memory 1.8124618530273438", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918807855600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9409379c-1e56-4480-bdbb-48b821ef774d", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918814831500, "endTime": 154918822766900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "1301ebf0-0447-41b3-a418-b6072af00252", "logId": "a29985d1-9c07-4944-975f-d028824ea96a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1301ebf0-0447-41b3-a418-b6072af00252", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918809443000}, "additional": {"logType": "detail", "children": [], "durationId": "9409379c-1e56-4480-bdbb-48b821ef774d"}}, {"head": {"id": "a1459bd1-7bb3-4d3d-936b-a0a439ae87d4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918810407700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ab88f61-90b3-4399-9fbd-a9985939df3c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918810492900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd1daa1d-549b-436c-a9bd-6e86c8f2907e", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918814855300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fac8631a-c629-47e2-9bae-0bcbdb18f108", "name": "entry : default@BuildJS cost memory 0.3401031494140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918822554000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8ab7b5e-e8fa-4cfb-973e-697269ae3e90", "name": "runTaskFromQueue task cost before running: 527 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918822705100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a29985d1-9c07-4944-975f-d028824ea96a", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918814831500, "endTime": 154918822766900, "totalTime": 7853200}, "additional": {"logType": "info", "children": [], "durationId": "9409379c-1e56-4480-bdbb-48b821ef774d"}}, {"head": {"id": "dd008c1a-420e-4293-8830-f48b6e9cb485", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918827908700, "endTime": 154918830931200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4672beac-6d80-4ad6-8b78-d1cbfdda4098", "logId": "d6908215-7e1d-4578-aff9-b98c55af475e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4672beac-6d80-4ad6-8b78-d1cbfdda4098", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918824374000}, "additional": {"logType": "detail", "children": [], "durationId": "dd008c1a-420e-4293-8830-f48b6e9cb485"}}, {"head": {"id": "c14147b1-c0d4-4889-af1f-4cd7c2d6b0ab", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918825418800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b3e0bcf-63d4-4775-9e9b-ce0d4ca7b8d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918825513000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b54ea8f2-69d6-4532-80e8-49ecd9f618c3", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918827917800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec91c89f-9070-4558-a9be-b872a693e7bb", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918828708400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63957257-1125-4528-a212-b79b2c8dfb1d", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918830720000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8a7d06a-e3f7-40bc-a64c-d1010620a8d2", "name": "entry : default@CacheNativeLibs cost memory 0.09955596923828125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918830865800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6908215-7e1d-4578-aff9-b98c55af475e", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918827908700, "endTime": 154918830931200}, "additional": {"logType": "info", "children": [], "durationId": "dd008c1a-420e-4293-8830-f48b6e9cb485"}}, {"head": {"id": "0871bb5f-559c-4ad8-aadd-4d9224a2b0e4", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154920049358700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3082e82-0349-4ac5-bd0e-cc755eef1cfc", "name": "CopyResources is end, endTime: 154920049575400", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154920049580400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32c3d58c-a42e-424f-91eb-52b5c9225f5e", "name": "default@CompileArkTS work[17] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154920049764200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84f33b50-eb3f-4f2c-a347-59d782184d75", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 154920034452800, "endTime": 154920049119000}, "additional": {"logType": "info", "children": [], "durationId": "986a0ce8-beda-4358-911e-59c7b53de129", "parent": "29452e61-49dd-4f8f-a262-4d32fe6f1466"}}, {"head": {"id": "788240d0-055e-434b-ad71-e2a8124c5839", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154920049855600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd52f015-8eba-4d63-a1a0-d5341c5f887a", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930095393300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4186c5a2-7ff0-4c24-9696-5c3ef2528592", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154921863820600, "endTime": 154922969866400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ce76fbbe-b9c6-42d0-9e73-d3fc843761ba", "logId": "a4d3900d-3c74-4e3b-932f-4e21456fac3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4d3900d-3c74-4e3b-932f-4e21456fac3e", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154921863820600, "endTime": 154922969866400}, "additional": {"logType": "info", "children": [], "durationId": "4186c5a2-7ff0-4c24-9696-5c3ef2528592", "parent": "09c5f69c-e70c-4cfb-a05a-cc14bdbe8c67"}}, {"head": {"id": "6454afc1-1f85-4830-a2bf-dc2d5782aa3b", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154922971343000, "endTime": 154923013867800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ce76fbbe-b9c6-42d0-9e73-d3fc843761ba", "logId": "15438432-ee04-4e96-8ce3-0fa44800da5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15438432-ee04-4e96-8ce3-0fa44800da5a", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154922971343000, "endTime": 154923013867800}, "additional": {"logType": "info", "children": [], "durationId": "6454afc1-1f85-4830-a2bf-dc2d5782aa3b", "parent": "09c5f69c-e70c-4cfb-a05a-cc14bdbe8c67"}}, {"head": {"id": "64aae306-5741-4ed6-b93f-8df5174c48a3", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154923013974600, "endTime": 154923014084200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ce76fbbe-b9c6-42d0-9e73-d3fc843761ba", "logId": "5121e81c-32ad-4c04-bdc4-6f203c272218"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5121e81c-32ad-4c04-bdc4-6f203c272218", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154923013974600, "endTime": 154923014084200}, "additional": {"logType": "info", "children": [], "durationId": "64aae306-5741-4ed6-b93f-8df5174c48a3", "parent": "09c5f69c-e70c-4cfb-a05a-cc14bdbe8c67"}}, {"head": {"id": "62f2db1c-4714-4568-9b88-e78a8ce8e865", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154923014134800, "endTime": 154929854638200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ce76fbbe-b9c6-42d0-9e73-d3fc843761ba", "logId": "61b02c91-70ef-4df8-acf9-65473d0e13a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61b02c91-70ef-4df8-acf9-65473d0e13a1", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154923014134800, "endTime": 154929854638200}, "additional": {"logType": "info", "children": [], "durationId": "62f2db1c-4714-4568-9b88-e78a8ce8e865", "parent": "09c5f69c-e70c-4cfb-a05a-cc14bdbe8c67"}}, {"head": {"id": "8789b005-4480-4d04-85b5-75b7c49f63ab", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154929854939900, "endTime": 154929881538100}, "additional": {"children": ["e016782b-64af-4bc8-a3c4-a24b013550f3", "e62f4c30-ce0c-434f-8d79-3ad9c8955c5e", "5e132c19-310a-4fc8-a59a-c9330d00db01"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ce76fbbe-b9c6-42d0-9e73-d3fc843761ba", "logId": "46350987-1e6f-498d-a35f-a264a568c0bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46350987-1e6f-498d-a35f-a264a568c0bb", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154929854939900, "endTime": 154929881538100}, "additional": {"logType": "info", "children": ["7d31f581-ac1c-428e-aa4d-09c59d34f6d0", "0f3a5f56-3b8e-469e-969c-9f4341d3d022", "876d0aa0-225e-499d-9589-01325195eef3"], "durationId": "8789b005-4480-4d04-85b5-75b7c49f63ab", "parent": "09c5f69c-e70c-4cfb-a05a-cc14bdbe8c67"}}, {"head": {"id": "e016782b-64af-4bc8-a3c4-a24b013550f3", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154929855116700, "endTime": 154929855147600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "8789b005-4480-4d04-85b5-75b7c49f63ab", "logId": "7d31f581-ac1c-428e-aa4d-09c59d34f6d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d31f581-ac1c-428e-aa4d-09c59d34f6d0", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154929855116700, "endTime": 154929855147600}, "additional": {"logType": "info", "children": [], "durationId": "e016782b-64af-4bc8-a3c4-a24b013550f3", "parent": "46350987-1e6f-498d-a35f-a264a568c0bb"}}, {"head": {"id": "e62f4c30-ce0c-434f-8d79-3ad9c8955c5e", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154929855160400, "endTime": 154929873404200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "8789b005-4480-4d04-85b5-75b7c49f63ab", "logId": "0f3a5f56-3b8e-469e-969c-9f4341d3d022"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f3a5f56-3b8e-469e-969c-9f4341d3d022", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154929855160400, "endTime": 154929873404200}, "additional": {"logType": "info", "children": [], "durationId": "e62f4c30-ce0c-434f-8d79-3ad9c8955c5e", "parent": "46350987-1e6f-498d-a35f-a264a568c0bb"}}, {"head": {"id": "5e132c19-310a-4fc8-a59a-c9330d00db01", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154929873414800, "endTime": 154929881460400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "8789b005-4480-4d04-85b5-75b7c49f63ab", "logId": "876d0aa0-225e-499d-9589-01325195eef3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "876d0aa0-225e-499d-9589-01325195eef3", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154929873414800, "endTime": 154929881460400}, "additional": {"logType": "info", "children": [], "durationId": "5e132c19-310a-4fc8-a59a-c9330d00db01", "parent": "46350987-1e6f-498d-a35f-a264a568c0bb"}}, {"head": {"id": "4340e468-526f-4bd4-b4d1-6d722d955633", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154929881558500, "endTime": 154930090072000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ce76fbbe-b9c6-42d0-9e73-d3fc843761ba", "logId": "39f356a1-6eec-4c76-9eab-eb5950c89156"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39f356a1-6eec-4c76-9eab-eb5950c89156", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154929881558500, "endTime": 154930090072000}, "additional": {"logType": "info", "children": [], "durationId": "4340e468-526f-4bd4-b4d1-6d722d955633", "parent": "09c5f69c-e70c-4cfb-a05a-cc14bdbe8c67"}}, {"head": {"id": "2cbcc80d-c4f6-4dac-9e71-0f701d609e62", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154919957640200, "endTime": 154921859873000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ce76fbbe-b9c6-42d0-9e73-d3fc843761ba", "logId": "8c3075a3-61fb-4c72-9a0c-035478b23e9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c3075a3-61fb-4c72-9a0c-035478b23e9e", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154919957640200, "endTime": 154921859873000}, "additional": {"logType": "info", "children": [], "durationId": "2cbcc80d-c4f6-4dac-9e71-0f701d609e62", "parent": "09c5f69c-e70c-4cfb-a05a-cc14bdbe8c67"}}, {"head": {"id": "7048d1d0-c092-45a5-ae27-fe5589f7d102", "name": "default@CompileArkTS work[16] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930101744900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09c5f69c-e70c-4cfb-a05a-cc14bdbe8c67", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154921862673500, "endTime": 154930094999600}, "additional": {"logType": "info", "children": ["a4d3900d-3c74-4e3b-932f-4e21456fac3e", "15438432-ee04-4e96-8ce3-0fa44800da5a", "5121e81c-32ad-4c04-bdc4-6f203c272218", "61b02c91-70ef-4df8-acf9-65473d0e13a1", "46350987-1e6f-498d-a35f-a264a568c0bb", "39f356a1-6eec-4c76-9eab-eb5950c89156", "8c3075a3-61fb-4c72-9a0c-035478b23e9e"], "durationId": "ce76fbbe-b9c6-42d0-9e73-d3fc843761ba", "parent": "29452e61-49dd-4f8f-a262-4d32fe6f1466"}}, {"head": {"id": "fe4f8b33-3568-416a-9db9-247e7bfba0e2", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930102028500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29452e61-49dd-4f8f-a262-4d32fe6f1466", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918768681500, "endTime": 154930102234600, "totalTime": 8286222800}, "additional": {"logType": "info", "children": ["09c5f69c-e70c-4cfb-a05a-cc14bdbe8c67", "84f33b50-eb3f-4f2c-a347-59d782184d75"], "durationId": "d3867aba-884f-4eac-9393-5fabf3679740"}}, {"head": {"id": "3659c2f2-5a03-4d4b-82db-56d7876adba1", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930108836300, "endTime": 154930109926200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "e17d6323-6024-4698-8c2f-28cf57a2295a", "logId": "37e8e096-f035-4dd2-b2e9-b5b4ec6ed2b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e17d6323-6024-4698-8c2f-28cf57a2295a", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930107101300}, "additional": {"logType": "detail", "children": [], "durationId": "3659c2f2-5a03-4d4b-82db-56d7876adba1"}}, {"head": {"id": "bb6d1c73-ba09-4997-a2ff-1886606ffe93", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930107858700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28ef26fc-bcfd-473a-8341-1aa6d4436678", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930107942300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da108ba9-a3d4-4b82-b1d7-7bea48c71a7f", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930108843000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "841a23fa-fdcc-485e-af12-b9f7c9cd7b4e", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930109091600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63e86fd4-33dd-47b8-8500-08995591bd90", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930109784100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d430a5bd-c4eb-4bac-9623-b63040755986", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07686614990234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930109867800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37e8e096-f035-4dd2-b2e9-b5b4ec6ed2b7", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930108836300, "endTime": 154930109926200}, "additional": {"logType": "info", "children": [], "durationId": "3659c2f2-5a03-4d4b-82db-56d7876adba1"}}, {"head": {"id": "b162d6bf-e6b0-4b4b-ba34-03917f678dbd", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930122596700, "endTime": 154930288194500}, "additional": {"children": ["6cbb1269-4e13-4d43-9d29-d5a7eeacd927", "df2be5dd-06d8-4646-bf3c-bc72e6c55834"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "3a267703-d7a1-4789-8993-f10cedabd096", "logId": "6e140b65-94fc-4fc3-a4e4-4c6124461ea5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a267703-d7a1-4789-8993-f10cedabd096", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930113641100}, "additional": {"logType": "detail", "children": [], "durationId": "b162d6bf-e6b0-4b4b-ba34-03917f678dbd"}}, {"head": {"id": "16c7a7fc-ef77-417a-be98-5eddb62131c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930115473200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1ede313-ca69-429a-b2ff-440a21bcc52f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930115589200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7c413b5-d511-4ab3-b6ad-1775b6d1a6ff", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930122634400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20941273-a859-49d8-a83e-6b899e58758a", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930142934700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8bac70c-910e-498e-b7fc-81391f2ab479", "name": "Incremental task entry:default@PackageHap pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930143120700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bad7c332-864d-443e-8e94-2055c6178013", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930143247400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dace5621-7b03-4492-97f7-1186a42302a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930143352500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cbb1269-4e13-4d43-9d29-d5a7eeacd927", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930145178100, "endTime": 154930148607100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b162d6bf-e6b0-4b4b-ba34-03917f678dbd", "logId": "22578f60-ca07-4b17-9dcc-a1617296489f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d55ee3f1-cfa0-40dd-8a36-5cc312ca6681", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930148415100}, "additional": {"logType": "debug", "children": [], "durationId": "b162d6bf-e6b0-4b4b-ba34-03917f678dbd"}}, {"head": {"id": "22578f60-ca07-4b17-9dcc-a1617296489f", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930145178100, "endTime": 154930148607100}, "additional": {"logType": "info", "children": [], "durationId": "6cbb1269-4e13-4d43-9d29-d5a7eeacd927", "parent": "6e140b65-94fc-4fc3-a4e4-4c6124461ea5"}}, {"head": {"id": "df2be5dd-06d8-4646-bf3c-bc72e6c55834", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930149283200, "endTime": 154930281854500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b162d6bf-e6b0-4b4b-ba34-03917f678dbd", "logId": "f22a6627-03cb-4b1c-8f86-118a56f94795"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34b4f5c9-43ca-41a9-aa15-8ec02fe405a1", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930280715400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f22a6627-03cb-4b1c-8f86-118a56f94795", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930149283200, "endTime": 154930281844700}, "additional": {"logType": "info", "children": [], "durationId": "df2be5dd-06d8-4646-bf3c-bc72e6c55834", "parent": "6e140b65-94fc-4fc3-a4e4-4c6124461ea5"}}, {"head": {"id": "f3161750-5337-4d58-8fe0-72163b58ccb0", "name": "entry : default@PackageHap cost memory 1.5594253540039062", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930287974400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2858b8b1-0af3-4b9b-8423-c7467cb1262e", "name": "runTaskFromQueue task cost before running: 11 s 993 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930288140200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e140b65-94fc-4fc3-a4e4-4c6124461ea5", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930122596700, "endTime": 154930288194500, "totalTime": 165518100}, "additional": {"logType": "info", "children": ["22578f60-ca07-4b17-9dcc-a1617296489f", "f22a6627-03cb-4b1c-8f86-118a56f94795"], "durationId": "b162d6bf-e6b0-4b4b-ba34-03917f678dbd"}}, {"head": {"id": "87f11017-259c-4b4b-98ec-6c7d70cf9634", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930295577400, "endTime": 154930602861600}, "additional": {"children": ["6276a9a5-c8be-42bb-8c8f-d02c407701b1", "95539cc7-4a7a-41a5-82d7-0f4b0eb59d17"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "4265444e-ebcb-4533-8fde-aab638d13fa4", "logId": "47e8eff0-6b0e-40dc-838c-9a1d4a5a89ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4265444e-ebcb-4533-8fde-aab638d13fa4", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930292109300}, "additional": {"logType": "detail", "children": [], "durationId": "87f11017-259c-4b4b-98ec-6c7d70cf9634"}}, {"head": {"id": "c071d698-ec9e-4e62-9d4f-887056bf1bfb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930293015500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c11b9a4-01c7-4689-9bed-a3a87f2a6e34", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930293113700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "005c9457-3be6-4f03-9b4b-6da6b4aba419", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930295587200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deb40bc9-72eb-4e4b-bd89-e9fd0608c340", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930297499200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4ebd464-dfd8-45be-8cc7-be2653ec7490", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930297618900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c305dcf1-267c-4e3f-a25b-9385414306b2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930297689300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bea25f3-eaef-4994-a38d-f7e52e07564b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930297722300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6276a9a5-c8be-42bb-8c8f-d02c407701b1", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930299533600, "endTime": 154930387769500}, "additional": {"children": ["9916141a-bbc9-45de-b63b-9016e5660064"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87f11017-259c-4b4b-98ec-6c7d70cf9634", "logId": "1595c4bf-19c0-4c9f-80e2-174c091f0d63"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9916141a-bbc9-45de-b63b-9016e5660064", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930316186300, "endTime": 154930386363700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6276a9a5-c8be-42bb-8c8f-d02c407701b1", "logId": "8e669c4d-ea2e-4574-a39d-ed79a438d7a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f0ab78b-a61a-4da5-b644-bbe479fc5dde", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930318962700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47a4ae06-3fd4-4829-ae9b-a3200e027495", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930385913400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e669c4d-ea2e-4574-a39d-ed79a438d7a0", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930316186300, "endTime": 154930386363700}, "additional": {"logType": "info", "children": [], "durationId": "9916141a-bbc9-45de-b63b-9016e5660064", "parent": "1595c4bf-19c0-4c9f-80e2-174c091f0d63"}}, {"head": {"id": "1595c4bf-19c0-4c9f-80e2-174c091f0d63", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930299533600, "endTime": 154930387769500}, "additional": {"logType": "info", "children": ["8e669c4d-ea2e-4574-a39d-ed79a438d7a0"], "durationId": "6276a9a5-c8be-42bb-8c8f-d02c407701b1", "parent": "47e8eff0-6b0e-40dc-838c-9a1d4a5a89ec"}}, {"head": {"id": "95539cc7-4a7a-41a5-82d7-0f4b0eb59d17", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930388534800, "endTime": 154930602273200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87f11017-259c-4b4b-98ec-6c7d70cf9634", "logId": "da181309-05cd-4870-b292-c79e88c10bab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d201beb7-6752-4a55-9193-78f874896700", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930390710100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f550fa8-dd00-463d-8413-c3ccd706359f", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930601740200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da181309-05cd-4870-b292-c79e88c10bab", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930388534800, "endTime": 154930602273200}, "additional": {"logType": "info", "children": [], "durationId": "95539cc7-4a7a-41a5-82d7-0f4b0eb59d17", "parent": "47e8eff0-6b0e-40dc-838c-9a1d4a5a89ec"}}, {"head": {"id": "fc918d77-77dc-4789-a6d5-db1935bc76a7", "name": "entry : default@SignHap cost memory 1.0087890625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930602653600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d16201e5-ddef-47af-ad28-cc576006c163", "name": "runTaskFromQueue task cost before running: 12 s 307 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930602805500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47e8eff0-6b0e-40dc-838c-9a1d4a5a89ec", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930295577400, "endTime": 154930602861600, "totalTime": 307199100}, "additional": {"logType": "info", "children": ["1595c4bf-19c0-4c9f-80e2-174c091f0d63", "da181309-05cd-4870-b292-c79e88c10bab"], "durationId": "87f11017-259c-4b4b-98ec-6c7d70cf9634"}}, {"head": {"id": "746668ba-bc32-4fe0-b272-980568b26fe2", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930606811300, "endTime": 154930612855000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b881073b-f556-4956-aef5-6ed91c7e84bc", "logId": "9a0ceee3-3b8b-46b7-8a72-a5e8bb87f6a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b881073b-f556-4956-aef5-6ed91c7e84bc", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930604877000}, "additional": {"logType": "detail", "children": [], "durationId": "746668ba-bc32-4fe0-b272-980568b26fe2"}}, {"head": {"id": "8f89922a-d7e3-4343-b301-02c39b545baf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930605853600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d21d350-a9fb-4ecf-8d0e-13b8b1ead61b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930605966600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "913da543-d489-440b-930d-ad73a27d4f42", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930606821300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb967a7e-e631-4c75-ab88-602c68313e92", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930612441500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34a27205-8a55-46c5-b1d1-6eee12841214", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930612588400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e776e3b1-e730-4040-9b23-dc03a027a739", "name": "entry : default@CollectDebugSymbol cost memory 0.24167633056640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930612728800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3c92c0a-3bfd-4dfa-a6da-5af39ec7d741", "name": "runTaskFromQueue task cost before running: 12 s 317 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930612810800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a0ceee3-3b8b-46b7-8a72-a5e8bb87f6a7", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930606811300, "endTime": 154930612855000, "totalTime": 5977800}, "additional": {"logType": "info", "children": [], "durationId": "746668ba-bc32-4fe0-b272-980568b26fe2"}}, {"head": {"id": "d32671df-3969-4d8f-beb0-37a764850b62", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930614723400, "endTime": 154930615071200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "df0430b4-09ca-48ca-b9fa-abb9fb153a12", "logId": "8f4cc251-c321-4c79-b0f8-811d0bddb572"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df0430b4-09ca-48ca-b9fa-abb9fb153a12", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930614667300}, "additional": {"logType": "detail", "children": [], "durationId": "d32671df-3969-4d8f-beb0-37a764850b62"}}, {"head": {"id": "2a005bb0-f783-4f75-a2fd-adbd99077b6b", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930614731300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d2ba2fa-12b1-4289-9d91-5b3ba66e4bfb", "name": "entry : assembleHap cost memory 0.0117034912109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930614932400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a55ffdb4-c995-44c1-bf0f-85f62d3313f6", "name": "runTaskFromQueue task cost before running: 12 s 320 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930615027200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f4cc251-c321-4c79-b0f8-811d0bddb572", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930614723400, "endTime": 154930615071200, "totalTime": 282700}, "additional": {"logType": "info", "children": [], "durationId": "d32671df-3969-4d8f-beb0-37a764850b62"}}, {"head": {"id": "ac2f8c13-e1c3-41f2-8c6c-7dea2423cbca", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930629553600, "endTime": 154930629591300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "281a8d2d-0ee8-4cf6-8e2e-e65677cc1c33", "logId": "97c5448d-42b2-450e-b16a-578fe541f2e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97c5448d-42b2-450e-b16a-578fe541f2e9", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930629553600, "endTime": 154930629591300}, "additional": {"logType": "info", "children": [], "durationId": "ac2f8c13-e1c3-41f2-8c6c-7dea2423cbca"}}, {"head": {"id": "16caaf03-5299-4e5e-bfe2-36dd9350de71", "name": "BUILD SUCCESSFUL in 12 s 334 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930629715800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "fdabdd53-1892-4490-8eed-f49a2bcf8977", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154918295977400, "endTime": 154930630062700}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 59, "second": 20}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "bff9691e-db43-4f29-b323-a1b3421a7e7a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930630096400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eafe5089-4f03-4ad3-b2ab-9752b64934a2", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930630162200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09a70be0-22b2-4ad8-a8ec-e97705a38ed6", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930630602200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebfd5dc3-3614-4aee-8356-4283cf6f0e48", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930630692500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7de923be-8297-43aa-ba1a-486e29ca0cc6", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930630730700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eca235a9-35f1-48e7-8ba7-f8031cb3150b", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930630797100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f354bbac-4b60-4916-a410-4393816bd5a3", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930630827400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c106e560-88ec-4ed7-a85a-c6eec093b631", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930631478400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9bbe37c-dba0-47f7-bc01-2e00bcfd0d6e", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930631777800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49ce69ed-961c-44a7-b34c-1f04b7992aa2", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930631842100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "813c2d3c-c88b-4ee3-96eb-f337429bd099", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930631884800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e443186c-dc47-468a-9f17-405c94466254", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930631915000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d7207e5-5262-4546-be36-a1177a0943d9", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930631950300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d84209f-f632-4bba-a83c-f357ff41544e", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930633202300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6783cc4-7795-4b58-8213-8a91270b5639", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930633651700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd252d9b-37d9-4a54-ba23-e5e04ee0cf57", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930633976600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f15d623-5d1b-48fb-9ae5-b1c3466844df", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930634053700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebd8d1ca-43ab-4dcc-bc44-bf4b9a42cc0f", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930634120100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6269e7a0-db11-4970-9310-9c4048365ed1", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930634165200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c0b764a-6fad-411b-ac9e-a273530340bd", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930634206400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15877cba-ff88-4cf5-9900-cfe1f67f1bd3", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930634243700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7455a0f-50f9-43d5-982a-f29245c921dd", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930637209900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88948d62-064a-4c9b-950d-083c76dbf050", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930637852100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6f7c563-bb05-4054-9a4a-36c47af30c6e", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930638221600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23434f21-cf1d-4c92-8da6-fe0164927c52", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930638488000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19780dba-072e-411d-b804-25d2a5664b93", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930638705200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "701df33a-8a42-4cd0-8298-ae2ab8da9401", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930639401500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "280e4103-0db6-43d0-859a-bbde9a5eda3e", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930645948600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db73ab3b-259e-4ff6-b09e-ba795ca33bf2", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930646259400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d018cfca-8a3c-4537-99d4-69460025e76c", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930646724900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74245b84-ddf4-4bd1-865a-509aadbc2c06", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930647672900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e866166a-6259-4bbb-8796-33a65cf430b4", "name": "Incremental task entry:default@CompileArkTS post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930648345400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e155c8ca-e2eb-4dfe-b426-c7b17ce218f1", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930650457400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66ca8125-7ef4-4edc-8193-1cd2f662a759", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930651147900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7bac714-3c55-44c8-b3da-3ec276da00f4", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930651532100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1708725a-68d3-4333-87de-09dc95841982", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930651806400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c24de9ce-d0e1-431d-9c68-c8bdda045db7", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930652015200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb0a2190-a751-44d7-acb0-19f1dae4352c", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930652914100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ca29d26-c786-4bfb-aac8-d87ab94d3587", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930653766800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c575c9b2-acfb-4955-95da-25095fde8c94", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930654013800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfca0b3a-b41f-4dc7-877c-c68866df03d6", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930654097800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38468676-c24e-4f81-b902-32fde0f9ba13", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930654132600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5c052bf-beff-43c1-86b8-4ff7588401fd", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930655383100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a763fa26-e8b8-482f-9a9f-cdffb0920777", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930655825500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c8a9a98-b103-4b14-9d10-8b62fbac53da", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930656062000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7588e58a-2031-48ed-82d4-25353914a960", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930662690500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5d26802-ee1b-4595-b2e6-abd548efec62", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930663050700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb80775e-3ed2-44f3-9b16-3b3422ac457b", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930663410300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d616eb3-4c0c-4eab-ac91-986bc8ac66f8", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930663744500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68bdbaf9-4635-46b0-901f-e27629641f80", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930663879100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "382dc1b7-2743-4671-820c-7740226d5b4b", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930664431200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b826450-e2f8-4fd6-957d-cb84ea964e76", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930664761700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "272f4fa3-1e04-461d-ac21-6c51b99f5fb1", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930665785600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "918acafa-2536-490c-ba0e-f46d0f608551", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930666121000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa3dc67c-7831-468c-af80-0d86172b3404", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930666337400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "214a50d4-c9a4-4278-9276-84bb33e9af3e", "name": "Incremental task entry:default@PackageHap post-execution cost:13 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930666571700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51197060-afd4-429c-868f-042a2e1127dd", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930666824300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e767782-3a41-4e67-b420-3bd383f528c8", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930667025100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea49c21e-8578-4d15-ae2f-fee49d15cf4a", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930667200000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f586c36-974a-4865-a69d-d9564e35d496", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930667388300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e6e9028-2a2f-4208-a90e-ad5764e8680e", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930667442600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b762fb5-b85c-4b8a-be69-b61d78d99a3c", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930667631100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdd542e8-8a7e-44c8-98e7-6aec6c7dcc09", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930670066500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5f54da7-22d5-4ae9-aad5-15643a4bdf8a", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930670316900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eee20970-a1bb-4408-b4de-0df315eee9ff", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930670756300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fe777a7-104e-4dde-adab-9fc39c44fe65", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154930670957500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}