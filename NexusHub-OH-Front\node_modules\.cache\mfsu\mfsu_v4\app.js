"use strict";
import { jsx } from "react/jsx-runtime";
import { history } from "@umijs/max";
import { message, ConfigProvider } from "antd";
import defaultSettings from "../config/defaultSettings";
import requestConfig from "./requestConfig";
import React from "react";
import { getUserProfile } from "@/services/user";
import RightContent from "./components/RightContent";
import { AppWrapper } from "./components/AppWrapper";
export async function getInitialState() {
  const fetchUserInfo = async () => {
    try {
      const useLogto2 = process.env.REACT_APP_USE_LOGTO === "true";
      if (useLogto2) {
        return void 0;
      }
      const token = localStorage.getItem("token");
      console.log("\u83B7\u53D6token:", token);
      if (!token) return void 0;
      const result = await getUserProfile();
      console.log("\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u7ED3\u679C:", result);
      return result.data;
    } catch (error) {
      console.error("\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u5931\u8D25:", error);
      localStorage.removeItem("token");
      return void 0;
    }
  };
  const useLogto = process.env.REACT_APP_USE_LOGTO === "true";
  const { pathname } = location;
  if (pathname !== "/user/login" && pathname !== "/user/register" && pathname !== "/callback") {
    const currentUser = await fetchUserInfo();
    return {
      fetchUserInfo,
      currentUser,
      settings: defaultSettings,
      useLogto
    };
  }
  return {
    fetchUserInfo,
    settings: defaultSettings,
    useLogto
  };
}
export const layout = ({ initialState }) => {
  return {
    logo: "/logo.svg",
    menu: {
      locale: true
    },
    // 水印设置
    waterMarkProps: {
      content: initialState?.currentUser?.username
    },
    // 页脚设置
    footerRender: () => /* @__PURE__ */ jsx("div", { style: { textAlign: "center", padding: "16px" }, children: "NexusHub \u5E94\u7528\u5546\u5E97\u7BA1\u7406\u7CFB\u7EDF \xA92025 Created by NexusHub Team" }),
    // 添加右侧内容渲染
    rightContentRender: () => /* @__PURE__ */ jsx(RightContent, {}),
    // 登录状态校验
    onPageChange: () => {
      const { location: location2 } = history;
      if (location2.pathname === "/callback") {
        return;
      }
      if (initialState?.useLogto) {
        return;
      }
      if (!initialState?.currentUser && location2.pathname !== "/user/login" && location2.pathname !== "/user/register") {
        message.warning("\u8BF7\u5148\u767B\u5F55");
        history.push("/user/login");
      }
    },
    // 继承默认设置
    ...initialState?.settings
  };
};
export const request = requestConfig;
export const rootContainer = (container) => {
  const useLogto = process.env.REACT_APP_USE_LOGTO === "true";
  return React.createElement(
    ConfigProvider,
    null,
    React.createElement(AppWrapper, { useLogto }, container)
  );
};
