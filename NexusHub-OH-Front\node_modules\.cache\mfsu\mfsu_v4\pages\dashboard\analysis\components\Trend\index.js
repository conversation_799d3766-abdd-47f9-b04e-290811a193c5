"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { CaretDownOutlined, CaretUpOutlined } from "@ant-design/icons";
import classNames from "classnames";
import useStyles from "./index.style";
const Trend = ({
  colorful = true,
  reverseColor = false,
  flag,
  children,
  className,
  ...rest
}) => {
  const { styles } = useStyles();
  const classString = classNames(
    styles.trendItem,
    {
      [styles.trendItemGrey]: !colorful,
      [styles.reverseColor]: reverseColor && colorful
    },
    className
  );
  return /* @__PURE__ */ jsxs("div", { ...rest, className: classString, title: typeof children === "string" ? children : "", children: [
    /* @__PURE__ */ jsx("span", { children }),
    flag && /* @__PURE__ */ jsx("span", { className: styles[flag], children: flag === "up" ? /* @__PURE__ */ jsx(CaretUpOutlined, {}) : /* @__PURE__ */ jsx(CaretDownOutlined, {}) })
  ] });
};
export default Trend;
