"use strict";
import { useState, useEffect, useCallback } from "react";
import { getInitialState } from "@/app";
const initState = {
  initialState: void 0,
  loading: true,
  error: void 0
};
export default () => {
  const [state, setState] = useState(initState);
  const refresh = useCallback(async () => {
    setState((s) => ({ ...s, loading: true, error: void 0 }));
    try {
      const ret = await getInitialState();
      setState((s) => ({ ...s, initialState: ret, loading: false }));
    } catch (e) {
      setState((s) => ({ ...s, error: e, loading: false }));
    }
  }, []);
  const setInitialState = useCallback(
    async (initialState) => {
      setState((s) => {
        if (typeof initialState === "function") {
          return { ...s, initialState: initialState(s.initialState), loading: false };
        }
        return { ...s, initialState, loading: false };
      });
    },
    []
  );
  useEffect(() => {
    refresh();
  }, []);
  return {
    ...state,
    refresh,
    setInitialState
  };
};
