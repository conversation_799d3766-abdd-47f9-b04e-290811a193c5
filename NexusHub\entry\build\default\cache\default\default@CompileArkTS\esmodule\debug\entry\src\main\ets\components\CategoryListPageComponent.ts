if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface CategoryListPageComponent_Params {
    isLoading?: boolean;
    searchKeyword?: string;
    deviceUtils?;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import hilog from "@ohos:hilog";
export class CategoryListPageComponent extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__searchKeyword = new ObservedPropertySimplePU('', this, "searchKeyword");
        this.deviceUtils = DeviceUtils.getInstance();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: CategoryListPageComponent_Params) {
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.searchKeyword !== undefined) {
            this.searchKeyword = params.searchKeyword;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
    }
    updateStateVars(params: CategoryListPageComponent_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__searchKeyword.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__isLoading.aboutToBeDeleted();
        this.__searchKeyword.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __searchKeyword: ObservedPropertySimplePU<string>;
    get searchKeyword() {
        return this.__searchKeyword.get();
    }
    set searchKeyword(newValue: string) {
        this.__searchKeyword.set(newValue);
    }
    private deviceUtils;
    aboutToAppear() {
        hilog.info(0x0000, 'CategoryListPageComponent', '分类页面组件初始化');
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 24 });
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 标题区域
            Text.create('应用分类');
            // 标题区域
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.EXTRA_LARGE));
            // 标题区域
            Text.fontWeight(FontWeight.Bold);
            // 标题区域
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 标题区域
            Text.alignSelf(ItemAlign.Start);
            // 标题区域
            Text.margin({ left: 16, right: 16, top: 16 });
        }, Text);
        // 标题区域
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 搜索栏
            Row.create({ space: 12 });
            // 搜索栏
            Row.width('100%');
            // 搜索栏
            Row.padding({ left: 16, right: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '搜索分类...', text: this.searchKeyword });
            TextInput.layoutWeight(1);
            TextInput.height(40);
            TextInput.borderRadius(20);
            TextInput.backgroundColor({ "id": 125829510, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            TextInput.padding({ left: 16, right: 16 });
            TextInput.onChange((value: string) => {
                this.searchKeyword = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild({ type: ButtonType.Circle });
            Button.width(40);
            Button.height(40);
            Button.backgroundColor(Constants.COLORS.PRIMARY);
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777270, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(20);
            Image.height(20);
            Image.fillColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        Button.pop();
        // 搜索栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 内容区域
            Column.create({ space: 16 });
            // 内容区域
            Column.width('100%');
            // 内容区域
            Column.layoutWeight(1);
            // 内容区域
            Column.justifyContent(FlexAlign.Center);
            // 内容区域
            Column.padding(24);
            // 内容区域
            Column.backgroundColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 内容区域
            Column.borderRadius(16);
            // 内容区域
            Column.margin({ left: 16, right: 16, bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('浏览应用分类');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM));
            Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('按照不同类别快速找到您需要的应用，包括游戏、工具、社交、教育等各种分类。');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.textAlign(TextAlign.Center);
            Text.margin({ left: 32, right: 32 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('即将推出');
            Button.type(ButtonType.Normal);
            Button.borderRadius(8);
            Button.backgroundColor({ "id": 125829510, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Button.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Button.enabled(false);
            Button.margin({ top: 16 });
        }, Button);
        Button.pop();
        // 内容区域
        Column.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
