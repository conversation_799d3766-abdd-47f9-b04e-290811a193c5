"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { Pie } from "@ant-design/plots";
import { Card, Radio, Typography } from "antd";
import numeral from "numeral";
import useStyles from "../style.style";
const { Text } = Typography;
const ProportionSales = ({
  dropdownGroup,
  salesType,
  loading,
  salesPieData,
  handleChangeSalesType
}) => {
  const { styles } = useStyles();
  return /* @__PURE__ */ jsx(
    Card,
    {
      loading,
      className: styles.salesCard,
      bordered: false,
      title: "\u5206\u7C7B\u5360\u6BD4",
      style: {
        height: "100%"
      },
      extra: /* @__PURE__ */ jsxs("div", { className: styles.salesCardExtra, children: [
        dropdownGroup,
        /* @__PURE__ */ jsx("div", { className: styles.salesTypeRadio, children: /* @__PURE__ */ jsxs(Radio.Group, { value: salesType, onChange: handleChangeSalesType, children: [
          /* @__PURE__ */ jsx(Radio.Button, { value: "all", children: "\u5E94\u7528\u6570\u91CF" }),
          /* @__PURE__ */ jsx(Radio.Button, { value: "online", children: "\u4E0B\u8F7D\u91CF" })
        ] }) })
      ] }),
      children: /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx(Text, { children: salesType === "all" ? "\u5E94\u7528\u6570\u91CF\u5360\u6BD4" : "\u4E0B\u8F7D\u91CF\u5360\u6BD4" }),
        /* @__PURE__ */ jsx(
          Pie,
          {
            height: 340,
            radius: 0.8,
            innerRadius: 0.5,
            angleField: "y",
            colorField: "x",
            data: salesPieData,
            legend: false,
            label: {
              position: "spider",
              text: (item) => {
                return `${item.x}: ${numeral(item.y).format("0,0")}`;
              }
            }
          }
        )
      ] })
    }
  );
};
export default ProportionSales;
