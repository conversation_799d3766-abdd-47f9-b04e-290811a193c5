"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { <PERSON> } from "@ant-design/plots";
import { Card, Col, Row, Tabs } from "antd";
import useStyles from "../style.style";
import NumberInfo from "./NumberInfo";
import numeral from "numeral";
const CustomTab = ({
  data,
  currentTabKey: currentKey
}) => {
  const total = data.app_count + data.download_count > 0 ? data.app_count / (data.app_count + data.download_count) : 0;
  return /* @__PURE__ */ jsxs(
    Row,
    {
      gutter: 8,
      style: {
        width: 138,
        margin: "8px 0"
      },
      children: [
        /* @__PURE__ */ jsx(Col, { span: 12, children: /* @__PURE__ */ jsx(
          NumberInfo,
          {
            title: data.category_name,
            subTitle: "\u5E94\u7528\u6570\u91CF",
            gap: 2,
            total: numeral(data.app_count).format("0,0"),
            theme: currentKey !== data.category_name ? "light" : void 0
          }
        ) }),
        /* @__PURE__ */ jsx(
          Col,
          {
            span: 12,
            style: {
              paddingTop: 36
            },
            children: /* @__PURE__ */ jsx(Tiny.Ring, { height: 60, width: 60, percent: total, color: ["#E8EEF4", "#5FABF4"] })
          }
        )
      ]
    }
  );
};
const OfflineData = ({
  activeKey,
  loading,
  offlineData,
  offlineChartData,
  handleTabChange
}) => {
  const { styles } = useStyles();
  return /* @__PURE__ */ jsx(
    Card,
    {
      loading,
      className: styles.offlineCard,
      bordered: false,
      style: {
        marginTop: 32
      },
      title: "\u5206\u7C7B\u7EDF\u8BA1",
      children: /* @__PURE__ */ jsx(
        Tabs,
        {
          activeKey,
          onChange: handleTabChange,
          items: (offlineData || []).map((category) => ({
            key: category.category_name,
            label: /* @__PURE__ */ jsx(CustomTab, { data: category, currentTabKey: activeKey }),
            children: /* @__PURE__ */ jsx(
              "div",
              {
                style: {
                  padding: "0 24px"
                },
                children: /* @__PURE__ */ jsxs(Row, { gutter: [16, 16], children: [
                  /* @__PURE__ */ jsx(Col, { span: 12, children: /* @__PURE__ */ jsx(Card, { bordered: false, title: "\u5E94\u7528\u6570\u91CF", children: /* @__PURE__ */ jsx("div", { style: { fontSize: "24px", fontWeight: "bold", textAlign: "center" }, children: numeral(category.app_count).format("0,0") }) }) }),
                  /* @__PURE__ */ jsx(Col, { span: 12, children: /* @__PURE__ */ jsx(Card, { bordered: false, title: "\u4E0B\u8F7D\u603B\u91CF", children: /* @__PURE__ */ jsx("div", { style: { fontSize: "24px", fontWeight: "bold", textAlign: "center" }, children: numeral(category.download_count).format("0,0") }) }) })
                ] })
              }
            )
          }))
        }
      )
    }
  );
};
export default OfflineData;
