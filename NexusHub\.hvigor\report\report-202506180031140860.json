{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "330082da-30d8-44df-8216-0e869a66cf31", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940426514800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f26127c-7b5c-4810-9755-e4c42108fdf7", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940426646100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eadd1fd-f491-420f-af90-907e53bfac53", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940430411500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e20e446-7d7f-4621-8ed5-ad35d88eede7", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940430935300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56340a1e-f109-45d5-851a-6ae70497a8b4", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940431832100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a818542-aba0-48cc-94c6-75a8a09858b4", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940432322100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7385098-5124-4ffd-b3d1-8486808a1746", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940434523300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baaeb3b0-bbb8-421a-b246-6930a16bf510", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 152940483837900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9857e48-1eed-4fb9-bb6f-0797e8a64250", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244302087700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03140710-ce07-427b-8dd3-106a6d40416b", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244308019100, "endTime": 153244485316500}, "additional": {"children": ["513215d2-ff7b-44ac-ba5b-84f2632ac746", "bd82a437-c7af-4c66-bfe5-f10cdc24a6f4", "647c783b-2207-47ee-8f89-fa28f49f297f", "d99835ce-b1b6-4f40-8add-98c863b3c587", "389880b4-53fe-4598-a8a6-a4385a861a5d", "ac3bb2d0-1f10-4417-bec8-744994ab9a8d", "68fd3c81-4632-42f5-b1a0-d2d343c06f06"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "4d1b3d0a-c030-4ada-a39b-3a7904e96a11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "513215d2-ff7b-44ac-ba5b-84f2632ac746", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244308020300, "endTime": 153244320687200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03140710-ce07-427b-8dd3-106a6d40416b", "logId": "9d01cdf1-7fb6-4375-96af-5ec4bdefc130"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd82a437-c7af-4c66-bfe5-f10cdc24a6f4", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244320705300, "endTime": 153244483689300}, "additional": {"children": ["27182f88-d899-456d-b5a1-acc653a636b8", "5f7d0c05-b0c1-45bd-9065-2abf1faf60ae", "07af325a-b722-4c47-9888-2d73ce6571d5", "19b3b15f-e82e-44ad-9fe7-65c8e6796524", "7f40ec41-d41b-4e1c-b4fc-b64ddc07f824", "ba23b04c-a36d-4a8e-b2cf-d645f219bf0a", "8c1b437f-2c15-4001-8be6-7faf0e17446b", "62e6aef7-a454-43e7-aa0b-6dde4629ee57", "d08e21fa-a270-426f-ae1a-f506b6853fa0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03140710-ce07-427b-8dd3-106a6d40416b", "logId": "a910613f-302d-451d-b27d-ea8d2d10d908"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "647c783b-2207-47ee-8f89-fa28f49f297f", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244483717900, "endTime": 153244485301300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03140710-ce07-427b-8dd3-106a6d40416b", "logId": "c6b4203a-b473-44ab-b7df-e890179e8049"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d99835ce-b1b6-4f40-8add-98c863b3c587", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244485306400, "endTime": 153244485313500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03140710-ce07-427b-8dd3-106a6d40416b", "logId": "72a5265d-e79b-4ac6-bca5-abaaa7bd30fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "389880b4-53fe-4598-a8a6-a4385a861a5d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244311740300, "endTime": 153244311782600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03140710-ce07-427b-8dd3-106a6d40416b", "logId": "a6d6701b-1bba-4460-8b6f-605f00f8ddd2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6d6701b-1bba-4460-8b6f-605f00f8ddd2", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244311740300, "endTime": 153244311782600}, "additional": {"logType": "info", "children": [], "durationId": "389880b4-53fe-4598-a8a6-a4385a861a5d", "parent": "4d1b3d0a-c030-4ada-a39b-3a7904e96a11"}}, {"head": {"id": "ac3bb2d0-1f10-4417-bec8-744994ab9a8d", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244315739200, "endTime": 153244315757700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03140710-ce07-427b-8dd3-106a6d40416b", "logId": "c1cac26d-4748-4c1c-a1e1-a613296fe970"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1cac26d-4748-4c1c-a1e1-a613296fe970", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244315739200, "endTime": 153244315757700}, "additional": {"logType": "info", "children": [], "durationId": "ac3bb2d0-1f10-4417-bec8-744994ab9a8d", "parent": "4d1b3d0a-c030-4ada-a39b-3a7904e96a11"}}, {"head": {"id": "5e9e42b6-3588-4c66-a276-63b5277bd7e6", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244315812000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45a7b05b-cac4-472f-80cb-5b677ea0908c", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244320539200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d01cdf1-7fb6-4375-96af-5ec4bdefc130", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244308020300, "endTime": 153244320687200}, "additional": {"logType": "info", "children": [], "durationId": "513215d2-ff7b-44ac-ba5b-84f2632ac746", "parent": "4d1b3d0a-c030-4ada-a39b-3a7904e96a11"}}, {"head": {"id": "27182f88-d899-456d-b5a1-acc653a636b8", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244326069700, "endTime": 153244326085500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd82a437-c7af-4c66-bfe5-f10cdc24a6f4", "logId": "ef698ecb-02b9-4a24-acbe-37b2f3689fdd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f7d0c05-b0c1-45bd-9065-2abf1faf60ae", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244326109600, "endTime": 153244329467200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd82a437-c7af-4c66-bfe5-f10cdc24a6f4", "logId": "fbab39ce-70b2-48cf-92bf-66455e65b535"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07af325a-b722-4c47-9888-2d73ce6571d5", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244329479500, "endTime": 153244397204300}, "additional": {"children": ["10dc7d9f-b9dd-478e-a12a-5f0cc69710fa", "d19c975f-b958-421c-82b9-449684324d3a", "e89653d6-0c39-4146-9ad3-e4e2d8d43996"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd82a437-c7af-4c66-bfe5-f10cdc24a6f4", "logId": "2a08cd50-9dd0-4fb3-84e9-2718de0c92b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19b3b15f-e82e-44ad-9fe7-65c8e6796524", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244397218000, "endTime": 153244417230100}, "additional": {"children": ["215cbc70-cc1a-40e0-bd63-e90083a1718c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd82a437-c7af-4c66-bfe5-f10cdc24a6f4", "logId": "654cfc40-2144-4d43-ae49-930b2f939b91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f40ec41-d41b-4e1c-b4fc-b64ddc07f824", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244417237500, "endTime": 153244459448700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd82a437-c7af-4c66-bfe5-f10cdc24a6f4", "logId": "4933a090-6259-4033-9ff0-d678bab90a55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba23b04c-a36d-4a8e-b2cf-d645f219bf0a", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244460444700, "endTime": 153244470338700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd82a437-c7af-4c66-bfe5-f10cdc24a6f4", "logId": "ba54702f-f26e-40b2-b6b9-b5195f564793"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c1b437f-2c15-4001-8be6-7faf0e17446b", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244470365600, "endTime": 153244483534200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd82a437-c7af-4c66-bfe5-f10cdc24a6f4", "logId": "01502f17-84eb-49e3-ac33-7b889af3cc87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62e6aef7-a454-43e7-aa0b-6dde4629ee57", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244483551600, "endTime": 153244483678200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd82a437-c7af-4c66-bfe5-f10cdc24a6f4", "logId": "35972c55-23f5-489f-9e08-42098ab4db59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef698ecb-02b9-4a24-acbe-37b2f3689fdd", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244326069700, "endTime": 153244326085500}, "additional": {"logType": "info", "children": [], "durationId": "27182f88-d899-456d-b5a1-acc653a636b8", "parent": "a910613f-302d-451d-b27d-ea8d2d10d908"}}, {"head": {"id": "fbab39ce-70b2-48cf-92bf-66455e65b535", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244326109600, "endTime": 153244329467200}, "additional": {"logType": "info", "children": [], "durationId": "5f7d0c05-b0c1-45bd-9065-2abf1faf60ae", "parent": "a910613f-302d-451d-b27d-ea8d2d10d908"}}, {"head": {"id": "10dc7d9f-b9dd-478e-a12a-5f0cc69710fa", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244329979700, "endTime": 153244330001100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "07af325a-b722-4c47-9888-2d73ce6571d5", "logId": "3fc85272-1d5a-41f6-be83-f7c258364661"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fc85272-1d5a-41f6-be83-f7c258364661", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244329979700, "endTime": 153244330001100}, "additional": {"logType": "info", "children": [], "durationId": "10dc7d9f-b9dd-478e-a12a-5f0cc69710fa", "parent": "2a08cd50-9dd0-4fb3-84e9-2718de0c92b2"}}, {"head": {"id": "d19c975f-b958-421c-82b9-449684324d3a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244331669000, "endTime": 153244396610300}, "additional": {"children": ["f97a1bb2-125f-4532-92b2-7d36a6ee6b6d", "7b822add-5078-479a-b4ea-f5a6c7925267"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "07af325a-b722-4c47-9888-2d73ce6571d5", "logId": "f397d680-d140-475e-87d4-7e37b27cb28c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f97a1bb2-125f-4532-92b2-7d36a6ee6b6d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244331670000, "endTime": 153244337318700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d19c975f-b958-421c-82b9-449684324d3a", "logId": "13ef75f7-7ac1-4042-9248-c0415aa6e8fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b822add-5078-479a-b4ea-f5a6c7925267", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244337336100, "endTime": 153244396599200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d19c975f-b958-421c-82b9-449684324d3a", "logId": "9ab553dc-2675-49d4-a37f-ab67f6b64321"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c512a167-e38c-4bf1-921e-6cac66f57983", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244331675100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29fa1461-2ab1-43b9-9117-55e97561e833", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244337161700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13ef75f7-7ac1-4042-9248-c0415aa6e8fc", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244331670000, "endTime": 153244337318700}, "additional": {"logType": "info", "children": [], "durationId": "f97a1bb2-125f-4532-92b2-7d36a6ee6b6d", "parent": "f397d680-d140-475e-87d4-7e37b27cb28c"}}, {"head": {"id": "dba01bac-5fcf-446b-bf0c-b207686bdc3c", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244337355000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "008f2e78-12ec-481c-95fb-e82744e389d0", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244344563100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cef0a876-2b67-48aa-9872-2e25d79093ea", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244344701900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6c130e7-3f5a-4c21-add3-a1bff11b5847", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244344817500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "824064a3-5185-4070-b82e-eea8efe1eaae", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244344902800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "442d3fff-17e7-4c25-8ad4-18817819f0a4", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244346246300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1cf46b4-ff3d-4405-8d04-f8de3359fafa", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244356220900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dc1e022-e63e-4248-b735-7499596c85c1", "name": "Sdk init in 28 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244377134300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6be27f3-98a4-46cf-8b86-5dfbbefda6e6", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244377313600}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 31, "second": 14}, "markType": "other"}}, {"head": {"id": "a75944bb-9d02-44a2-a108-8995f7f91494", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244377332000}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 31, "second": 14}, "markType": "other"}}, {"head": {"id": "9e38e512-c702-40a9-86b6-be321ec5f4c6", "name": "Project task initialization takes 17 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244396365300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26e784c9-bce6-477f-b13a-a98ac7722a4a", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244396488300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0bb8486-4eda-4587-bf36-e7bf7485ecfd", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244396536100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96a7b7e5-7fcc-486e-8c3b-94ca5d7be7a7", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244396568700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ab553dc-2675-49d4-a37f-ab67f6b64321", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244337336100, "endTime": 153244396599200}, "additional": {"logType": "info", "children": [], "durationId": "7b822add-5078-479a-b4ea-f5a6c7925267", "parent": "f397d680-d140-475e-87d4-7e37b27cb28c"}}, {"head": {"id": "f397d680-d140-475e-87d4-7e37b27cb28c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244331669000, "endTime": 153244396610300}, "additional": {"logType": "info", "children": ["13ef75f7-7ac1-4042-9248-c0415aa6e8fc", "9ab553dc-2675-49d4-a37f-ab67f6b64321"], "durationId": "d19c975f-b958-421c-82b9-449684324d3a", "parent": "2a08cd50-9dd0-4fb3-84e9-2718de0c92b2"}}, {"head": {"id": "e89653d6-0c39-4146-9ad3-e4e2d8d43996", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244397177400, "endTime": 153244397193200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "07af325a-b722-4c47-9888-2d73ce6571d5", "logId": "43952731-2f4b-45a0-910d-ceaafed81835"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43952731-2f4b-45a0-910d-ceaafed81835", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244397177400, "endTime": 153244397193200}, "additional": {"logType": "info", "children": [], "durationId": "e89653d6-0c39-4146-9ad3-e4e2d8d43996", "parent": "2a08cd50-9dd0-4fb3-84e9-2718de0c92b2"}}, {"head": {"id": "2a08cd50-9dd0-4fb3-84e9-2718de0c92b2", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244329479500, "endTime": 153244397204300}, "additional": {"logType": "info", "children": ["3fc85272-1d5a-41f6-be83-f7c258364661", "f397d680-d140-475e-87d4-7e37b27cb28c", "43952731-2f4b-45a0-910d-ceaafed81835"], "durationId": "07af325a-b722-4c47-9888-2d73ce6571d5", "parent": "a910613f-302d-451d-b27d-ea8d2d10d908"}}, {"head": {"id": "215cbc70-cc1a-40e0-bd63-e90083a1718c", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244397768400, "endTime": 153244417218500}, "additional": {"children": ["51a8a1a9-2a7c-4726-8f6c-e41dbe320528", "57aed5ec-9963-4f7a-8b9c-e1d410b3a563", "e19b6f24-cc97-4ac5-b333-b73f374576bc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "19b3b15f-e82e-44ad-9fe7-65c8e6796524", "logId": "e0caba9f-522c-4842-8de4-90ff97768303"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51a8a1a9-2a7c-4726-8f6c-e41dbe320528", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244400975800, "endTime": 153244400992800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "215cbc70-cc1a-40e0-bd63-e90083a1718c", "logId": "26342fc5-b52c-413e-bde8-fc0cd02e95c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26342fc5-b52c-413e-bde8-fc0cd02e95c1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244400975800, "endTime": 153244400992800}, "additional": {"logType": "info", "children": [], "durationId": "51a8a1a9-2a7c-4726-8f6c-e41dbe320528", "parent": "e0caba9f-522c-4842-8de4-90ff97768303"}}, {"head": {"id": "57aed5ec-9963-4f7a-8b9c-e1d410b3a563", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244402591400, "endTime": 153244416030900}, "additional": {"children": ["6fd93742-c081-4d60-a153-e1d6fdd03cc0", "17b28502-31ee-4ee6-a677-b9e49929308e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "215cbc70-cc1a-40e0-bd63-e90083a1718c", "logId": "4345ac2e-72d4-445a-a86c-4e42aaa5e3ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6fd93742-c081-4d60-a153-e1d6fdd03cc0", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244402592400, "endTime": 153244405849200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57aed5ec-9963-4f7a-8b9c-e1d410b3a563", "logId": "6d25aef8-d95a-45cd-a7bb-43690c22738a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17b28502-31ee-4ee6-a677-b9e49929308e", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244405863700, "endTime": 153244416021600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57aed5ec-9963-4f7a-8b9c-e1d410b3a563", "logId": "73ac9154-7890-4c00-a906-e277d77990e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "967c8850-745c-4d14-b5c4-aeeca7a21479", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244402596800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "927b2a92-5eca-44af-9bdd-cf2c82959d5e", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244405728900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d25aef8-d95a-45cd-a7bb-43690c22738a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244402592400, "endTime": 153244405849200}, "additional": {"logType": "info", "children": [], "durationId": "6fd93742-c081-4d60-a153-e1d6fdd03cc0", "parent": "4345ac2e-72d4-445a-a86c-4e42aaa5e3ec"}}, {"head": {"id": "1c067a96-3bcc-4255-9403-61e5d339d517", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244405875100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a32b5d18-f64f-4b74-ad58-8664337235be", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244411540900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e63918e4-f53b-4e0e-86de-892e669e381a", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244411666100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e33b6b4-9450-4d78-9c7b-0599a69d11f1", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244411817200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b74b101-07c2-495b-b224-8048a53fd3d2", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244411902900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5945c71d-f898-479e-8540-ae0c9002cf53", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244411938500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bdbca0a-a45f-4dba-9a3b-ab9c82667daa", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244411975500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f09c7d17-1975-4c51-883c-f2b449387f41", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244412022600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5db1ea8-2a29-465b-af2d-3e9e080ed528", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244412056700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6a6cb15-b09f-44f0-aa9e-46eef8baa3e4", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244412204300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c4e7e9d-96dd-4803-a465-8873461f1a1a", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244412273400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35c1c484-2a3c-4dc4-90af-643ad54493cb", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244412309200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70fff2fd-e2c6-42e2-96ef-cc9b7b6e30c5", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244412339000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36e04ae1-851c-49e1-8f6a-652d4d4550f6", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244412377300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c20e3a37-5454-440b-9113-dacb1e7f0649", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244412407300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2d6efc2-855e-4783-ac63-5a35351eaf76", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244412482800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "698475d5-4ad5-4f81-80ab-d7fd4a7d42cb", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244412549400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84a28a45-dfc7-4c2d-90a3-ab9cd08135ff", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244412592500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4a80e86-27de-4cd2-8352-08d778ca94be", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244412620100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6bbbaf4-14fb-4cbe-8744-21f85894b885", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244413312100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b6b31b-8ad0-44ae-8e4d-a173210c4362", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244415794700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8474cf44-79fa-4ef0-8f20-c8724b911f20", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244415920000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1d52fd4-88cd-4a8f-b378-8b9bc48d754c", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244415964000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e1d8935-19d0-4c53-acf7-43d867090e47", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244415994000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73ac9154-7890-4c00-a906-e277d77990e5", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244405863700, "endTime": 153244416021600}, "additional": {"logType": "info", "children": [], "durationId": "17b28502-31ee-4ee6-a677-b9e49929308e", "parent": "4345ac2e-72d4-445a-a86c-4e42aaa5e3ec"}}, {"head": {"id": "4345ac2e-72d4-445a-a86c-4e42aaa5e3ec", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244402591400, "endTime": 153244416030900}, "additional": {"logType": "info", "children": ["6d25aef8-d95a-45cd-a7bb-43690c22738a", "73ac9154-7890-4c00-a906-e277d77990e5"], "durationId": "57aed5ec-9963-4f7a-8b9c-e1d410b3a563", "parent": "e0caba9f-522c-4842-8de4-90ff97768303"}}, {"head": {"id": "e19b6f24-cc97-4ac5-b333-b73f374576bc", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244417181600, "endTime": 153244417197100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "215cbc70-cc1a-40e0-bd63-e90083a1718c", "logId": "2344e740-e244-4c09-812b-1df057c2c0c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2344e740-e244-4c09-812b-1df057c2c0c6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244417181600, "endTime": 153244417197100}, "additional": {"logType": "info", "children": [], "durationId": "e19b6f24-cc97-4ac5-b333-b73f374576bc", "parent": "e0caba9f-522c-4842-8de4-90ff97768303"}}, {"head": {"id": "e0caba9f-522c-4842-8de4-90ff97768303", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244397768400, "endTime": 153244417218500}, "additional": {"logType": "info", "children": ["26342fc5-b52c-413e-bde8-fc0cd02e95c1", "4345ac2e-72d4-445a-a86c-4e42aaa5e3ec", "2344e740-e244-4c09-812b-1df057c2c0c6"], "durationId": "215cbc70-cc1a-40e0-bd63-e90083a1718c", "parent": "654cfc40-2144-4d43-ae49-930b2f939b91"}}, {"head": {"id": "654cfc40-2144-4d43-ae49-930b2f939b91", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244397218000, "endTime": 153244417230100}, "additional": {"logType": "info", "children": ["e0caba9f-522c-4842-8de4-90ff97768303"], "durationId": "19b3b15f-e82e-44ad-9fe7-65c8e6796524", "parent": "a910613f-302d-451d-b27d-ea8d2d10d908"}}, {"head": {"id": "5c984e1c-54a5-4475-8fec-4451a73f42b1", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244429845900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b8abf7a-25d2-4bce-9b05-9a3749ee231e", "name": "hvigorfile, resolve hvigorfile dependencies in 43 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244459297600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4933a090-6259-4033-9ff0-d678bab90a55", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244417237500, "endTime": 153244459448700}, "additional": {"logType": "info", "children": [], "durationId": "7f40ec41-d41b-4e1c-b4fc-b64ddc07f824", "parent": "a910613f-302d-451d-b27d-ea8d2d10d908"}}, {"head": {"id": "d08e21fa-a270-426f-ae1a-f506b6853fa0", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244460233400, "endTime": 153244460432100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd82a437-c7af-4c66-bfe5-f10cdc24a6f4", "logId": "5382930b-df22-4fca-aa2e-30615924a427"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8aa30b3c-48d6-4f45-9384-242283b61476", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244460267900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5382930b-df22-4fca-aa2e-30615924a427", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244460233400, "endTime": 153244460432100}, "additional": {"logType": "info", "children": [], "durationId": "d08e21fa-a270-426f-ae1a-f506b6853fa0", "parent": "a910613f-302d-451d-b27d-ea8d2d10d908"}}, {"head": {"id": "5a94999c-f6c7-446d-b126-450d30fa62f4", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244461800300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07b577e0-5983-4976-b1b7-3b92872d3960", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244469267600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba54702f-f26e-40b2-b6b9-b5195f564793", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244460444700, "endTime": 153244470338700}, "additional": {"logType": "info", "children": [], "durationId": "ba23b04c-a36d-4a8e-b2cf-d645f219bf0a", "parent": "a910613f-302d-451d-b27d-ea8d2d10d908"}}, {"head": {"id": "83026ee9-13ed-45d1-b621-449690426748", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244470412200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9d34c1e-2129-4165-8aa1-641ffef6d5b0", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244477159800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64b58bd0-2597-4ba7-a3c6-c65894d74ace", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244477273300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b812c1a-8924-4789-9333-a6b8bcbe7d2b", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244477428300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8646c57-d6bc-45d5-8c88-fea2fd52fc52", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244480268700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b92acd1-74c6-489f-83c9-68cc4124791c", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244480365200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01502f17-84eb-49e3-ac33-7b889af3cc87", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244470365600, "endTime": 153244483534200}, "additional": {"logType": "info", "children": [], "durationId": "8c1b437f-2c15-4001-8be6-7faf0e17446b", "parent": "a910613f-302d-451d-b27d-ea8d2d10d908"}}, {"head": {"id": "480c765d-84ad-4251-a975-d9c9155666a7", "name": "Configuration phase cost:158 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244483598700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35972c55-23f5-489f-9e08-42098ab4db59", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244483551600, "endTime": 153244483678200}, "additional": {"logType": "info", "children": [], "durationId": "62e6aef7-a454-43e7-aa0b-6dde4629ee57", "parent": "a910613f-302d-451d-b27d-ea8d2d10d908"}}, {"head": {"id": "a910613f-302d-451d-b27d-ea8d2d10d908", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244320705300, "endTime": 153244483689300}, "additional": {"logType": "info", "children": ["ef698ecb-02b9-4a24-acbe-37b2f3689fdd", "fbab39ce-70b2-48cf-92bf-66455e65b535", "2a08cd50-9dd0-4fb3-84e9-2718de0c92b2", "654cfc40-2144-4d43-ae49-930b2f939b91", "4933a090-6259-4033-9ff0-d678bab90a55", "ba54702f-f26e-40b2-b6b9-b5195f564793", "01502f17-84eb-49e3-ac33-7b889af3cc87", "35972c55-23f5-489f-9e08-42098ab4db59", "5382930b-df22-4fca-aa2e-30615924a427"], "durationId": "bd82a437-c7af-4c66-bfe5-f10cdc24a6f4", "parent": "4d1b3d0a-c030-4ada-a39b-3a7904e96a11"}}, {"head": {"id": "68fd3c81-4632-42f5-b1a0-d2d343c06f06", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244485264900, "endTime": 153244485286500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03140710-ce07-427b-8dd3-106a6d40416b", "logId": "6dcd1294-7627-48bb-a806-6143d798b67b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6dcd1294-7627-48bb-a806-6143d798b67b", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244485264900, "endTime": 153244485286500}, "additional": {"logType": "info", "children": [], "durationId": "68fd3c81-4632-42f5-b1a0-d2d343c06f06", "parent": "4d1b3d0a-c030-4ada-a39b-3a7904e96a11"}}, {"head": {"id": "c6b4203a-b473-44ab-b7df-e890179e8049", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244483717900, "endTime": 153244485301300}, "additional": {"logType": "info", "children": [], "durationId": "647c783b-2207-47ee-8f89-fa28f49f297f", "parent": "4d1b3d0a-c030-4ada-a39b-3a7904e96a11"}}, {"head": {"id": "72a5265d-e79b-4ac6-bca5-abaaa7bd30fd", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244485306400, "endTime": 153244485313500}, "additional": {"logType": "info", "children": [], "durationId": "d99835ce-b1b6-4f40-8add-98c863b3c587", "parent": "4d1b3d0a-c030-4ada-a39b-3a7904e96a11"}}, {"head": {"id": "4d1b3d0a-c030-4ada-a39b-3a7904e96a11", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244308019100, "endTime": 153244485316500}, "additional": {"logType": "info", "children": ["9d01cdf1-7fb6-4375-96af-5ec4bdefc130", "a910613f-302d-451d-b27d-ea8d2d10d908", "c6b4203a-b473-44ab-b7df-e890179e8049", "72a5265d-e79b-4ac6-bca5-abaaa7bd30fd", "a6d6701b-1bba-4460-8b6f-605f00f8ddd2", "c1cac26d-4748-4c1c-a1e1-a613296fe970", "6dcd1294-7627-48bb-a806-6143d798b67b"], "durationId": "03140710-ce07-427b-8dd3-106a6d40416b"}}, {"head": {"id": "6857d464-6f05-46f5-845a-981f240d31f9", "name": "Configuration task cost before running: 181 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244485480400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63b7e7f9-99c8-4879-a561-e6a1f7f6f053", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244495564300, "endTime": 153244508567900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "aaa1abd2-bd5f-48b9-a0cd-42e3ca0deff9", "logId": "1b1d973c-c5b2-44bb-8167-028fd78ff9ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aaa1abd2-bd5f-48b9-a0cd-42e3ca0deff9", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244488089100}, "additional": {"logType": "detail", "children": [], "durationId": "63b7e7f9-99c8-4879-a561-e6a1f7f6f053"}}, {"head": {"id": "1ae4283e-a721-408a-a16b-c5a57627aa84", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244488962400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c468087-f000-46e4-9a49-068e67764027", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244489115800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e444742-810d-4efd-a464-759103c8148d", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244489856400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a1092e9-1582-47b5-b840-c069f0f3520e", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244490649900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3bfb23c-bc0f-4e62-8a8a-43767eea9ade", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244491651500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de9b7874-390f-4f5a-a2df-6aaddd2d21df", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244491730400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bacebab4-aebc-4033-81a4-3d106378abeb", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244495582500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d30c4dc4-7fc6-4ae4-86e9-67b902543614", "name": "Incremental task entry:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244508336800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b5f4bdb-65e1-495d-a817-685ea82d9da4", "name": "entry : default@PreBuild cost memory 0.32309722900390625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244508492900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b1d973c-c5b2-44bb-8167-028fd78ff9ab", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244495564300, "endTime": 153244508567900}, "additional": {"logType": "info", "children": [], "durationId": "63b7e7f9-99c8-4879-a561-e6a1f7f6f053"}}, {"head": {"id": "a5fdb589-e0d9-486f-a0d2-ac1f93bc90b2", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244515377000, "endTime": 153244517143200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "db1eebd6-0676-435f-a82a-b8d93a523554", "logId": "06112f77-1a3d-4685-b531-ebaa477c6b7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db1eebd6-0676-435f-a82a-b8d93a523554", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244513323000}, "additional": {"logType": "detail", "children": [], "durationId": "a5fdb589-e0d9-486f-a0d2-ac1f93bc90b2"}}, {"head": {"id": "1401bca1-1008-4079-8cbe-f636c72cc05f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244514429000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fad5c40-c7e0-4bfb-9da7-1576dfc05919", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244514536800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bf413ff-0251-423e-8dee-4b28646c5c7a", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244515389600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce0d1c96-5a33-4957-9aef-510ded95f466", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244516060400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "899996ee-cb02-4609-9368-a9e0cc0fd99e", "name": "entry : default@CreateModuleInfo cost memory 0.06037139892578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244516975900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f03f52bc-b9b1-4c2b-a732-3056c1e27124", "name": "runTaskFromQueue task cost before running: 212 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244517090800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06112f77-1a3d-4685-b531-ebaa477c6b7f", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244515377000, "endTime": 153244517143200, "totalTime": 1692800}, "additional": {"logType": "info", "children": [], "durationId": "a5fdb589-e0d9-486f-a0d2-ac1f93bc90b2"}}, {"head": {"id": "f609e9f5-9422-4b87-91b4-b4f6c2ae4e72", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244527093800, "endTime": 153244529380200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1a00f174-b3c4-4d6a-bb08-0eb2b6d003f8", "logId": "89444c81-59ff-4366-ada8-a6cbcc7b4530"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a00f174-b3c4-4d6a-bb08-0eb2b6d003f8", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244519185700}, "additional": {"logType": "detail", "children": [], "durationId": "f609e9f5-9422-4b87-91b4-b4f6c2ae4e72"}}, {"head": {"id": "3a56e183-521d-476a-ad0e-49eb5151a43f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244520360000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "448efd6f-d7d7-4b91-a5df-89f1d9d578cf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244520577700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4541833-189e-46d3-922d-221f4cb1432b", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244527110900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db56edfa-5ef9-422f-a23a-6fd19c94f03b", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244528236700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a34e727-18b4-4cd4-9d80-76802e31b1ac", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244529218500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b90fabfe-243b-40cb-91a4-942ddd489ed9", "name": "entry : default@GenerateMetadata cost memory 0.1014556884765625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244529321100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89444c81-59ff-4366-ada8-a6cbcc7b4530", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244527093800, "endTime": 153244529380200}, "additional": {"logType": "info", "children": [], "durationId": "f609e9f5-9422-4b87-91b4-b4f6c2ae4e72"}}, {"head": {"id": "a6ece22c-f789-48e3-9d62-032670dd5b92", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244534416900, "endTime": 153244534685700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a7d86705-e680-41a9-be04-ecae944d9dd1", "logId": "d7d47615-0af4-44ec-95df-3977a3f7e256"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7d86705-e680-41a9-be04-ecae944d9dd1", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244532999000}, "additional": {"logType": "detail", "children": [], "durationId": "a6ece22c-f789-48e3-9d62-032670dd5b92"}}, {"head": {"id": "254eb09b-7ad7-418d-89a0-8219ee5959c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244534173700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2d8586a-b95a-48b2-8efb-a8e415f6ad10", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244534287800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b0a2e18-216b-4ea9-aad2-35c95f82efef", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244534424400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ea04082-e0ab-4980-aa82-1936347139f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244534502100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e93e58d9-86dd-4f57-bcea-35913ab9e52c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244534536500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d422f82a-5140-4289-8f26-71de952ef3fd", "name": "entry : default@ConfigureCmake cost memory 0.0374755859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244534590100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b49cd666-95ff-4c7d-b97b-fb7545c64e3a", "name": "runTaskFromQueue task cost before running: 230 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244534652600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7d47615-0af4-44ec-95df-3977a3f7e256", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244534416900, "endTime": 153244534685700, "totalTime": 214800}, "additional": {"logType": "info", "children": [], "durationId": "a6ece22c-f789-48e3-9d62-032670dd5b92"}}, {"head": {"id": "ad85694c-ade7-4400-8d46-898927ace67b", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244538575700, "endTime": 153244541807200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1ffa822a-5a4a-48a9-a0ba-851379b5fe1a", "logId": "e4e7d3ff-d697-47f8-a79a-ee915ee74647"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ffa822a-5a4a-48a9-a0ba-851379b5fe1a", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244536212600}, "additional": {"logType": "detail", "children": [], "durationId": "ad85694c-ade7-4400-8d46-898927ace67b"}}, {"head": {"id": "6132406c-76be-44b4-ae6d-f47f01e37478", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244537609500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f79767e-0a72-454e-97be-166caee74937", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244537736100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8194f1b-1be8-4b66-bb73-e06931b9454f", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244538592600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6c7dbe1-2722-455f-9bd6-f8b28d61ed6e", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244541570400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06f7c4e7-9ed1-443f-aa7b-53eeaf42ebed", "name": "entry : default@MergeProfile cost memory 0.1186981201171875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244541735700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4e7d3ff-d697-47f8-a79a-ee915ee74647", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244538575700, "endTime": 153244541807200}, "additional": {"logType": "info", "children": [], "durationId": "ad85694c-ade7-4400-8d46-898927ace67b"}}, {"head": {"id": "4253f827-2862-4fed-abba-550db6313970", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244545938700, "endTime": 153244549000800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e8e8dbb8-8348-4195-b389-e342dc01cd97", "logId": "24decd13-9a89-4f84-be1d-29076f98f375"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8e8dbb8-8348-4195-b389-e342dc01cd97", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244543721100}, "additional": {"logType": "detail", "children": [], "durationId": "4253f827-2862-4fed-abba-550db6313970"}}, {"head": {"id": "b1b2d34b-7cb8-49d0-9fb6-888398ab99c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244544853900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c46779d-096c-4988-9b41-6805060613cc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244544979400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b41ace79-8974-443e-b888-ad0949a3cda6", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244545950200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "781e6cc9-9de2-423e-9fe8-ddb985c4c3a9", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244547194300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd4e55b-c1b0-4829-8dbe-525f7c4617f2", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244548787600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "960a6aad-7e04-489e-9ad8-e294f22d8130", "name": "entry : default@CreateBuildProfile cost memory 0.10823822021484375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244548932300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24decd13-9a89-4f84-be1d-29076f98f375", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244545938700, "endTime": 153244549000800}, "additional": {"logType": "info", "children": [], "durationId": "4253f827-2862-4fed-abba-550db6313970"}}, {"head": {"id": "3d713431-f7c6-4cc7-a8a8-981dd317cfe4", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244552526200, "endTime": 153244553078000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "b6f29166-4b25-4cf1-83cc-587523c9aad2", "logId": "2655c012-50d1-4943-a425-57c5ec8123b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6f29166-4b25-4cf1-83cc-587523c9aad2", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244550640500}, "additional": {"logType": "detail", "children": [], "durationId": "3d713431-f7c6-4cc7-a8a8-981dd317cfe4"}}, {"head": {"id": "0a60d8d5-5cc7-4253-8876-3cc277cf4d30", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244551732600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24762455-9cef-43a8-8783-59104f3eac5d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244551838500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3d5a871-0427-4a6a-86ee-2bb4c4f8e57c", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244552535200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a91fbf2-d252-4987-9791-04fedd7e56f6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244552662100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7494441-60ad-4a37-bed4-246f4e3324ac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244552708000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60d2d462-3bfb-4d6d-bb50-fc5fecb031e0", "name": "entry : default@PreCheckSyscap cost memory 0.041107177734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244552922300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42a8e755-a9e3-4df3-9432-689913a750e0", "name": "runTaskFromQueue task cost before running: 248 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244553012800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2655c012-50d1-4943-a425-57c5ec8123b5", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244552526200, "endTime": 153244553078000, "totalTime": 463700}, "additional": {"logType": "info", "children": [], "durationId": "3d713431-f7c6-4cc7-a8a8-981dd317cfe4"}}, {"head": {"id": "84a0c92d-46a8-4ca6-8d6f-a606e2f79872", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244559035800, "endTime": 153244564759000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b96417f2-29d6-468b-9c20-eb1d0e76cfc8", "logId": "2bd4b663-716b-40cd-b663-f437b04443d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b96417f2-29d6-468b-9c20-eb1d0e76cfc8", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244555905100}, "additional": {"logType": "detail", "children": [], "durationId": "84a0c92d-46a8-4ca6-8d6f-a606e2f79872"}}, {"head": {"id": "a942faea-4f26-4376-ae9f-2451a57564c9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244557336900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6617811f-9a49-4f83-8e22-5e483c0d7b42", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244557465600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdee6ae1-b2a4-4c91-b17c-a98d5ba69c68", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244559048100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69f32e95-8b6d-483e-a714-564aaa84992c", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244563734000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bedf9f1-8204-4bfb-a7c0-c849e9a6af92", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244564559500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7f3dc7f-fc51-421e-88a0-e13112ad2dfc", "name": "entry : default@GeneratePkgContextInfo cost memory 0.2374114990234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244564687400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bd4b663-716b-40cd-b663-f437b04443d0", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244559035800, "endTime": 153244564759000}, "additional": {"logType": "info", "children": [], "durationId": "84a0c92d-46a8-4ca6-8d6f-a606e2f79872"}}, {"head": {"id": "144653d9-f81d-4277-8454-a636687e4d61", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244576198200, "endTime": 153244578393800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "b53d2436-7fec-4493-a88f-a958dadae9f6", "logId": "6e8d15bf-022a-4951-b010-e72c58469dc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b53d2436-7fec-4493-a88f-a958dadae9f6", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244566425300}, "additional": {"logType": "detail", "children": [], "durationId": "144653d9-f81d-4277-8454-a636687e4d61"}}, {"head": {"id": "682226be-34c1-4047-9180-e96e9873cd5c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244567428800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd93366e-d022-47a9-82d5-ea034da585a2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244567536300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f85c5493-b3a7-4df0-8d5c-7fe73d6452a1", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244576217300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17f70250-a6bb-4f1d-ae34-0921ac61e75e", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244577925500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4923d48a-80c5-495c-a3e7-b8001362de25", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244578060000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e394cabb-2a91-4599-87b5-afedbe0b1019", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244578147300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0c48f81-f253-44a2-a136-e677ee98eb43", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244578216200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "530c03d4-b212-4a3f-a05b-4d5a3d6b65b6", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12067413330078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244578288400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89cf7207-2ae2-451f-8e6f-ed323415ed9f", "name": "runTaskFromQueue task cost before running: 273 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244578353800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e8d15bf-022a-4951-b010-e72c58469dc2", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244576198200, "endTime": 153244578393800, "totalTime": 2142500}, "additional": {"logType": "info", "children": [], "durationId": "144653d9-f81d-4277-8454-a636687e4d61"}}, {"head": {"id": "74f2d0ae-ad45-4a64-a06c-0a4f5fb5aa4d", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244582569900, "endTime": 153244582919500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "55f34269-4069-44ad-b06a-34c9357e7a4d", "logId": "3248f899-6573-466f-a9ce-3788118e69c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55f34269-4069-44ad-b06a-34c9357e7a4d", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244580550000}, "additional": {"logType": "detail", "children": [], "durationId": "74f2d0ae-ad45-4a64-a06c-0a4f5fb5aa4d"}}, {"head": {"id": "88aaa1a2-6985-4a4d-bc63-c26b914deca9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244581689800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fac9ef1-df45-4be3-a11b-bc760a6b638f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244581786300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78a054c7-c0c7-4527-9aa2-ad7a0a651f42", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244582578600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3da80f80-feda-4685-9e2f-d39595c57148", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244582695900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e697849-41a0-4076-8e52-a9d7425cc3da", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244582743500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "248a858e-5c44-4eaf-85fc-2b6f3f9f1bfc", "name": "entry : default@BuildNativeWithCmake cost memory 0.03852081298828125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244582808500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d34d946-c2f3-4cf6-b130-91d0e4d588ad", "name": "runTaskFromQueue task cost before running: 278 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244582873100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3248f899-6573-466f-a9ce-3788118e69c7", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244582569900, "endTime": 153244582919500, "totalTime": 283800}, "additional": {"logType": "info", "children": [], "durationId": "74f2d0ae-ad45-4a64-a06c-0a4f5fb5aa4d"}}, {"head": {"id": "cb30306b-5a18-4886-9ac8-e1d53e3c9ed0", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244586178400, "endTime": 153244590781900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e516f612-a01a-4034-bb95-24f63f8d6962", "logId": "5c38601e-cfde-4922-9d04-908e29b95872"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e516f612-a01a-4034-bb95-24f63f8d6962", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244584362500}, "additional": {"logType": "detail", "children": [], "durationId": "cb30306b-5a18-4886-9ac8-e1d53e3c9ed0"}}, {"head": {"id": "86ee24b8-ff55-4543-afe7-c6a70d854857", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244585338200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec0f59e8-057a-4600-8b2f-ab80e18dc0af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244585473300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6f11a8b-cc5f-4ab0-92cb-e5ab207e444c", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244586187100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b4d6a47-fe93-423e-a4ec-109a80224eec", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244590544700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "750a5235-2e83-4db1-89b9-f7f728241ed7", "name": "entry : default@MakePackInfo cost memory 0.16362762451171875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244590705500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c38601e-cfde-4922-9d04-908e29b95872", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244586178400, "endTime": 153244590781900}, "additional": {"logType": "info", "children": [], "durationId": "cb30306b-5a18-4886-9ac8-e1d53e3c9ed0"}}, {"head": {"id": "e88f06a6-e2cf-4307-975f-cc9aacf6c787", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244595255300, "endTime": 153244598625300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e24a6ecd-7d0c-40b7-b8ff-c5c3b208fc91", "logId": "5fbacf79-3e13-4f91-a5c6-74a3ec96e99f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e24a6ecd-7d0c-40b7-b8ff-c5c3b208fc91", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244592896600}, "additional": {"logType": "detail", "children": [], "durationId": "e88f06a6-e2cf-4307-975f-cc9aacf6c787"}}, {"head": {"id": "2864badb-a678-4575-8b94-dca19f96cb0b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244593925500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b8003ef-f966-443f-a841-5251a463642e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244594037000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5e5b419-b43b-46dd-8306-41bf81243203", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244595264900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc45bc46-a694-4d70-9f7e-ea486962c4e3", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244595456600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "886ae099-631d-4783-807a-020b57a2d976", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244596193700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da592824-f4ad-4ec3-ac18-aa05ecdacc51", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244598454300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bb20058-e57e-47ea-8b59-6b50ae091b50", "name": "entry : default@SyscapTransform cost memory 0.1501007080078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244598562800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fbacf79-3e13-4f91-a5c6-74a3ec96e99f", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244595255300, "endTime": 153244598625300}, "additional": {"logType": "info", "children": [], "durationId": "e88f06a6-e2cf-4307-975f-cc9aacf6c787"}}, {"head": {"id": "c787d4ec-5fd6-4533-b448-c3fd3a745255", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244602305900, "endTime": 153244604396200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "7fde3653-670b-4247-8e89-499d00a3806e", "logId": "70d59d60-c230-4030-af7a-b9e8b5c13c9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7fde3653-670b-4247-8e89-499d00a3806e", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244600005600}, "additional": {"logType": "detail", "children": [], "durationId": "c787d4ec-5fd6-4533-b448-c3fd3a745255"}}, {"head": {"id": "90b75136-1648-4afa-9eee-2c963da41ea2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244601007300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "881df647-7d3c-42ea-a3d2-8e2f336914bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244601128500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbe84071-00b6-4bf5-9a0e-f3b51a3c5206", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244602317400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "370d645f-94d1-42ef-9d10-be328ed168eb", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244604146100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f03bf07d-65ce-4814-aaed-ef4ec5bc6c51", "name": "entry : default@ProcessProfile cost memory 0.12245941162109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244604313200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70d59d60-c230-4030-af7a-b9e8b5c13c9f", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244602305900, "endTime": 153244604396200}, "additional": {"logType": "info", "children": [], "durationId": "c787d4ec-5fd6-4533-b448-c3fd3a745255"}}, {"head": {"id": "2b931653-9257-4321-874c-93085b812d07", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244609284600, "endTime": 153244615521800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c4f5b619-4459-41cb-a5c2-8ec088ad27cf", "logId": "0be0c862-c212-468c-94ee-022dd5009194"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4f5b619-4459-41cb-a5c2-8ec088ad27cf", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244606509800}, "additional": {"logType": "detail", "children": [], "durationId": "2b931653-9257-4321-874c-93085b812d07"}}, {"head": {"id": "d33e7438-8544-44d3-8a5c-667db74e7d1c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244607512800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89adc713-8486-4c5a-92eb-ad5e3382d3e4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244607629300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81a8b716-12ae-47f8-8435-14ae26771a97", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244609300500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b6a5ebd-7cc0-46f3-b9c4-54f33951b687", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244615303800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5689af1a-8153-4b8a-9c84-f2223b657fb2", "name": "entry : default@ProcessRouterMap cost memory 0.23291015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244615443500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0be0c862-c212-468c-94ee-022dd5009194", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244609284600, "endTime": 153244615521800}, "additional": {"logType": "info", "children": [], "durationId": "2b931653-9257-4321-874c-93085b812d07"}}, {"head": {"id": "fd021f82-a6b5-478b-b128-b23d2b93b668", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244619388000, "endTime": 153244625252200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "a5229755-3c74-4aa7-910e-e76a2c2b74ca", "logId": "1df2d1d2-b92c-4819-ad54-e11586c69414"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5229755-3c74-4aa7-910e-e76a2c2b74ca", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244618290200}, "additional": {"logType": "detail", "children": [], "durationId": "fd021f82-a6b5-478b-b128-b23d2b93b668"}}, {"head": {"id": "a0b07471-110f-4320-9514-d63596432c80", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244619210600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78c586c2-88b5-4115-9581-0a7829c342da", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244619303600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66e627e8-71d1-4ad8-a86b-822e00303186", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244619393800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b78fe43-5417-473d-907e-b91d3054ecdc", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244619477500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ca6c11b-236f-4a4f-8306-07a13c52d865", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244623636800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf67ec13-d925-4ba4-91a6-3ad1222f4f42", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244623806400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cb126df-19a4-4e81-88bf-b008495f31ce", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244623899500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bb9771e-4f1f-410d-8f44-c2afabac3c0e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244623950700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "501c985a-653c-4aa2-a064-46f1b38cdef8", "name": "entry : default@ProcessStartupConfig cost memory 0.2587738037109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244625059200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1215ee15-906f-4aee-a913-025f8251a2fe", "name": "runTaskFromQueue task cost before running: 320 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244625198500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1df2d1d2-b92c-4819-ad54-e11586c69414", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244619388000, "endTime": 153244625252200, "totalTime": 5779900}, "additional": {"logType": "info", "children": [], "durationId": "fd021f82-a6b5-478b-b128-b23d2b93b668"}}, {"head": {"id": "aa65f672-8a79-43a1-a47a-d97095b6c3ef", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244629679300, "endTime": 153244630848200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "970da863-ff20-46d0-b336-b223aaf20ac3", "logId": "200d9783-b678-4e32-b3a9-186bb5408542"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "970da863-ff20-46d0-b336-b223aaf20ac3", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244627882400}, "additional": {"logType": "detail", "children": [], "durationId": "aa65f672-8a79-43a1-a47a-d97095b6c3ef"}}, {"head": {"id": "27cb5d40-7765-49cc-b7fe-207c69c75e19", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244628894100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13791c33-d232-4868-9ebc-63db8ac5d77e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244628986300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f913edd-dbf9-460f-ac7d-a1561532127d", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244629688100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e70d522-0984-4b8a-a262-93c12ed8630b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244629801900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea8565ea-47f9-45b6-966a-d5f7b7583694", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244629846400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "483fffab-97f5-42b5-b0c3-80dc4252d43a", "name": "entry : default@BuildNativeWithNinja cost memory 0.0581512451171875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244630679500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b1ac1f8-07e8-4fdc-b8c0-3d0093c8932e", "name": "runTaskFromQueue task cost before running: 326 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244630798000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "200d9783-b678-4e32-b3a9-186bb5408542", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244629679300, "endTime": 153244630848200, "totalTime": 1096800}, "additional": {"logType": "info", "children": [], "durationId": "aa65f672-8a79-43a1-a47a-d97095b6c3ef"}}, {"head": {"id": "4931a029-f8f4-4f1c-bf95-f5d0858f4f9a", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244637914100, "endTime": 153244643405900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "98a2abcf-b818-493b-a491-099319f98964", "logId": "61aa0e94-61d1-45e7-872e-62574bf2c0fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98a2abcf-b818-493b-a491-099319f98964", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244634429800}, "additional": {"logType": "detail", "children": [], "durationId": "4931a029-f8f4-4f1c-bf95-f5d0858f4f9a"}}, {"head": {"id": "280dd52c-25b0-4da5-8a21-ed95af35be32", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244635485500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64c542a8-a7a6-4108-b9d6-c37b97f2e140", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244635587200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d40f9dc4-26a3-4787-887e-6bd949df0271", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244636600700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d9e9c81-0c00-4191-945f-6d02a5c76c68", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244639532700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e322858e-abf4-43c1-9423-a0902d9c7c09", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244641660800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a89d718f-9afc-48b7-8198-2ed39e8dec84", "name": "entry : default@ProcessResource cost memory 0.16170501708984375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244641807400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61aa0e94-61d1-45e7-872e-62574bf2c0fe", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244637914100, "endTime": 153244643405900}, "additional": {"logType": "info", "children": [], "durationId": "4931a029-f8f4-4f1c-bf95-f5d0858f4f9a"}}, {"head": {"id": "7f9fb5be-44cd-4a78-99fd-fb2ddbb42c3e", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244650911600, "endTime": 153244670199900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9e9d7f06-2e0d-4adf-9879-b160a9c928c2", "logId": "537cddff-a9ce-42cf-8531-316bc9bb6fc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e9d7f06-2e0d-4adf-9879-b160a9c928c2", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244646638300}, "additional": {"logType": "detail", "children": [], "durationId": "7f9fb5be-44cd-4a78-99fd-fb2ddbb42c3e"}}, {"head": {"id": "4e36704f-e3f3-452f-85b5-228324e81573", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244647553800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c305b17-60d0-4a08-8287-c456e020124b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244647653800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0092ad6d-887c-442c-9b42-30163ca70943", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244650923100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7305564-9be4-4211-9e2c-a41035a01e87", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244669872800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d736421-330c-4166-b496-2888e548b925", "name": "entry : default@GenerateLoaderJson cost memory 0.8725814819335938", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244670116600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "537cddff-a9ce-42cf-8531-316bc9bb6fc8", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244650911600, "endTime": 153244670199900}, "additional": {"logType": "info", "children": [], "durationId": "7f9fb5be-44cd-4a78-99fd-fb2ddbb42c3e"}}, {"head": {"id": "70a8a63f-80ba-42cd-89e5-42ab<PERSON><PERSON><PERSON>b", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244681631400, "endTime": 153244685804600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "590fcb7c-c2b6-43eb-a650-a40d2cf93442", "logId": "4ee50cb3-735d-4d18-8fe7-95aa47ac2894"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "590fcb7c-c2b6-43eb-a650-a40d2cf93442", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244679528100}, "additional": {"logType": "detail", "children": [], "durationId": "70a8a63f-80ba-42cd-89e5-42ab<PERSON><PERSON><PERSON>b"}}, {"head": {"id": "affb7938-3705-4ada-8649-7e929b895d74", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244680617600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "692992f0-56f9-476f-b799-9ceaa91ee3d9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244680725200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64544f2e-d1b1-4a77-aaec-930af68f1df3", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244681641800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6982583c-947f-492c-bb70-fd716536e4b0", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244685613200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26b79e07-8c9c-4766-af79-452d341361a4", "name": "entry : default@ProcessLibs cost memory 0.14196014404296875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244685741300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ee50cb3-735d-4d18-8fe7-95aa47ac2894", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244681631400, "endTime": 153244685804600}, "additional": {"logType": "info", "children": [], "durationId": "70a8a63f-80ba-42cd-89e5-42ab<PERSON><PERSON><PERSON>b"}}, {"head": {"id": "c022cfd3-5a06-461c-aea0-c5cb1a7e4843", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244693732000, "endTime": 153244719187700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "64d0d937-18a5-4fe6-8283-284e3552d583", "logId": "e5f76431-b20d-4b6e-9ad5-9cd03930e138"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64d0d937-18a5-4fe6-8283-284e3552d583", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244688087900}, "additional": {"logType": "detail", "children": [], "durationId": "c022cfd3-5a06-461c-aea0-c5cb1a7e4843"}}, {"head": {"id": "79ffabbb-ed62-4513-b4d7-bac144e12b7c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244689422400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b3d6882-96c5-4125-9216-f45fb47fa490", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244689557100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a726412-9cf6-40f1-9138-471d0f004985", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244690561900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98ab977f-7d93-48b3-9ff3-01def43cd3be", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244693759400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9d4c63e-4b23-4aeb-8d47-ba119dc65a3d", "name": "Incremental task entry:default@CompileResource pre-execution cost: 24 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244718887300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "839debed-8545-4159-9cf8-0649be1fea47", "name": "entry : default@CompileResource cost memory 1.3149948120117188", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244719087900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5f76431-b20d-4b6e-9ad5-9cd03930e138", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244693732000, "endTime": 153244719187700}, "additional": {"logType": "info", "children": [], "durationId": "c022cfd3-5a06-461c-aea0-c5cb1a7e4843"}}, {"head": {"id": "ede49451-9526-447d-8dec-4eb5df7a7025", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244725723000, "endTime": 153244727865900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "bd1a7e65-1a0e-4ef2-8430-40cd0cdebf6e", "logId": "e72403be-6737-4dc5-9a67-270859379f9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd1a7e65-1a0e-4ef2-8430-40cd0cdebf6e", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244722314500}, "additional": {"logType": "detail", "children": [], "durationId": "ede49451-9526-447d-8dec-4eb5df7a7025"}}, {"head": {"id": "76c90cec-6f82-4222-a827-e011a22a821d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244723374500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2d0c1f5-4875-4753-8722-08fa6ce1f881", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244723504400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea4ebe42-bb6c-4386-b524-28f9896cd7d5", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244725735600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc085965-3e2a-43f1-b3ab-503ee085b3a2", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244726282200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9e80b64-77e1-4668-a8e6-2146cc41a625", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244727694600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f27fb1e-54f2-40a9-9fa8-4d19112fe496", "name": "entry : default@DoNativeStrip cost memory 0.08011627197265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244727806800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e72403be-6737-4dc5-9a67-270859379f9a", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244725723000, "endTime": 153244727865900}, "additional": {"logType": "info", "children": [], "durationId": "ede49451-9526-447d-8dec-4eb5df7a7025"}}, {"head": {"id": "8a373016-51e6-4ce5-938c-074786c5d301", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244735104500, "endTime": 153255652089500}, "additional": {"children": ["413e82b4-6d8f-4f0d-af41-49dd536125fc", "b41e231d-65b6-4470-92b3-735901565f8f"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "92a2c7fd-1ab7-498c-8f31-61eb6df0166b", "logId": "c3b9da66-725e-49b1-80c1-51632c35c072"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92a2c7fd-1ab7-498c-8f31-61eb6df0166b", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244729281900}, "additional": {"logType": "detail", "children": [], "durationId": "8a373016-51e6-4ce5-938c-074786c5d301"}}, {"head": {"id": "813558c8-a846-4fae-a388-d352f82a2fd7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244730505000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "082d5d41-9ccd-47af-9e86-92473582513d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244730620600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0d1696a-22eb-4e11-9780-07542d4d6a13", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244735116900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2ee8579-1857-47ff-a454-7f9f7e21781d", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244735295700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd254f05-5c44-438b-95c0-14c73dc83648", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244765651500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1894300b-6586-4147-b354-d429ea018c69", "name": "default@CompileArkTS work[8] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244767572100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "413e82b4-6d8f-4f0d-af41-49dd536125fc", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 153244769634400, "endTime": 153255651936400}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "8a373016-51e6-4ce5-938c-074786c5d301", "logId": "8fd4e51d-86f3-4db1-9a75-b8844cef7ef9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22ad6d8b-46b8-4b04-afa8-1767b9d37ac3", "name": "default@CompileArkTS work[8] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768478100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cbde14a-72e0-4642-8b28-ecd48bc696c2", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768564900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3358dc1-5c50-4f96-8cb3-533637c66963", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768606100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3955a04f-1ce0-432a-b418-3db0f53695ce", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768631600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d09631fc-d8fd-4ea4-b52b-8feaee6cc523", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768655300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5601aa0e-7b6b-4ab4-a569-3e4c6803fe43", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768677800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8029cf58-201d-4304-920d-b414d6f12d9c", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768700300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79899482-c022-45c2-84b6-75cd8a41cdc3", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768723300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf505d1e-398b-4831-aacb-36c211034fa1", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768746800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d158282-2739-445f-828c-2ccedfdaa901", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768769300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d452f439-2b1a-474c-8a27-cd2028283db6", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768791500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2603cb5-5971-4801-b1e9-1e46289818f0", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768815300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "857308e8-7164-44fa-8586-7401299b6dbd", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768839600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cfedf51-6a29-44b9-b5b1-00ce50a93421", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768862500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ae38c06-7750-4eeb-abf5-f71fd7c2ac04", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768887500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bd76ae3-d1d9-41bd-97b0-c5e9abe3ccb8", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768909200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b44c7eb-7bb2-4cc9-abbc-071f868ab18a", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244768944200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb53b632-fa1b-4712-bef5-3139127820f7", "name": "default@CompileArkTS work[8] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244769643300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e45093af-8fe6-465b-9d1b-c1fb9a86b7a1", "name": "default@CompileArkTS work[8] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244769721800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdb1c58f-852e-4925-b001-758d720712a0", "name": "CopyResources startTime: 153244769758600", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244769760600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0a0c37d-24c5-4ed6-b935-c49dc5a8db28", "name": "default@CompileArkTS work[9] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244769805300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b41e231d-65b6-4470-92b3-735901565f8f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 153245823942900, "endTime": 153245838767400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "8a373016-51e6-4ce5-938c-074786c5d301", "logId": "92c2cfdf-699c-446b-af80-b018ed76ecaa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c679e9e3-9444-4538-893d-e878c04f8df6", "name": "default@CompileArkTS work[9] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244770770400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b1b018b-0a67-4bff-b24d-40e0135b5949", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244770935100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a15383d-4863-49a9-9ec4-7d8ba2d78b1f", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244771057300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abf1b519-5c94-4fed-b5e2-df60539f0069", "name": "default@CompileArkTS work[9] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244772405200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "702e3936-4e81-4861-8024-7c3694fafc3a", "name": "default@CompileArkTS work[9] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244772573500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d033fd23-7461-45cc-9b04-c20644fdb792", "name": "entry : default@CompileArkTS cost memory 1.7402572631835938", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244772723600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "202fb538-2165-4679-beb1-f4030af53e1a", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244780014900, "endTime": 153244788324200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "0b717e84-6023-4580-a233-cbf2226922c8", "logId": "4b395d11-64c3-4ebb-aea6-2bb316dd3679"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b717e84-6023-4580-a233-cbf2226922c8", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244774277600}, "additional": {"logType": "detail", "children": [], "durationId": "202fb538-2165-4679-beb1-f4030af53e1a"}}, {"head": {"id": "a6ccef56-28f3-4710-80ec-d452ae749a0d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244775551600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c8e825d-a123-4bb9-bbac-e7b5f5cc3395", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244775709400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30cc66e8-66b5-4868-8d88-678caf7c670c", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244780026100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dd46c61-2865-417f-b336-7685c2a5ab8c", "name": "entry : default@BuildJS cost memory -6.7428741455078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244788022000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27be7151-4aa7-45b9-9d55-31c06cf46565", "name": "runTaskFromQueue task cost before running: 483 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244788261800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b395d11-64c3-4ebb-aea6-2bb316dd3679", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244780014900, "endTime": 153244788324200, "totalTime": 8207500}, "additional": {"logType": "info", "children": [], "durationId": "202fb538-2165-4679-beb1-f4030af53e1a"}}, {"head": {"id": "787682c1-162f-4217-8c84-a4177bd0146b", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244793972500, "endTime": 153244797198900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "cd24dcdf-b725-4d23-8931-4dbb59392507", "logId": "d75b42a1-eebd-49b4-aba5-84a677ae83d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd24dcdf-b725-4d23-8931-4dbb59392507", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244790601000}, "additional": {"logType": "detail", "children": [], "durationId": "787682c1-162f-4217-8c84-a4177bd0146b"}}, {"head": {"id": "4313d4a2-e8df-46fc-a622-44a8d64d49b9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244791544100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "364afb37-6081-4e1a-bc76-0cac04055156", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244791665600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "200b2bcd-b2b7-4b46-84c9-3cc1c2ddc3fd", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244794006900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9c21555-ed09-4f75-beff-08e255df7af0", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244794695800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d80a8e9-d089-459a-8f41-35f9df62c3f8", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244797025200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddb19ab3-48bc-4e23-9080-b0e38243e50f", "name": "entry : default@CacheNativeLibs cost memory 0.09539031982421875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244797138900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d75b42a1-eebd-49b4-aba5-84a677ae83d2", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244793972500, "endTime": 153244797198900}, "additional": {"logType": "info", "children": [], "durationId": "787682c1-162f-4217-8c84-a4177bd0146b"}}, {"head": {"id": "91221c32-ffef-4c8a-9ffe-64899f36e4f7", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153245839140700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c998fd8-36bd-481a-9b42-e6e24598405e", "name": "CopyResources is end, endTime: 153245839350700", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153245839356000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d53714cc-3074-4d99-8b88-e1661a898786", "name": "default@CompileArkTS work[9] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153245839452900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92c2cfdf-699c-446b-af80-b018ed76ecaa", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 153245823942900, "endTime": 153245838767400}, "additional": {"logType": "info", "children": [], "durationId": "b41e231d-65b6-4470-92b3-735901565f8f", "parent": "c3b9da66-725e-49b1-80c1-51632c35c072"}}, {"head": {"id": "8084e3e5-4270-4bbd-83bb-b2c9a2ec2491", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153245839643800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "434cdc95-236c-4816-953d-cf22e09cf9f6", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255651756900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e02eae6e-5d88-44c6-8c66-64470c9f737b", "name": "default@CompileArkTS work[8] failed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255651996000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fd4e51d-86f3-4db1-9a75-b8844cef7ef9", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 153244769634400, "endTime": 153255651936400}, "additional": {"logType": "error", "children": [], "durationId": "413e82b4-6d8f-4f0d-af41-49dd536125fc", "parent": "c3b9da66-725e-49b1-80c1-51632c35c072"}}, {"head": {"id": "c3b9da66-725e-49b1-80c1-51632c35c072", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244735104500, "endTime": 153255652089500}, "additional": {"logType": "error", "children": ["8fd4e51d-86f3-4db1-9a75-b8844cef7ef9", "92c2cfdf-699c-446b-af80-b018ed76ecaa"], "durationId": "8a373016-51e6-4ce5-938c-074786c5d301"}}, {"head": {"id": "76863a44-173d-42ed-8792-092dd1025e0c", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255652184300}, "additional": {"logType": "debug", "children": [], "durationId": "8a373016-51e6-4ce5-938c-074786c5d301"}}, {"head": {"id": "c4c73faa-8549-47ad-b3b7-546ae3bc8d01", "name": "ERROR: stacktrace = Error: ArkTS Compiler Error\r\n\u001b[31m1 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Module '\"./HomePage\"' declares 'HomePage' locally, but it is not exported. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/Index.ets:1:10\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m2 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Module '\"./SearchPage\"' declares 'SearchPage' locally, but it is not exported. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/Index.ets:2:10\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m3 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Module '\"./FeaturedPage\"' declares 'FeaturedPage' locally, but it is not exported. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/Index.ets:3:10\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m4 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Module '\"./CategoryListPage\"' declares 'CategoryListPage' locally, but it is not exported. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/Index.ets:4:10\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m5 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Module '\"./ProfilePage\"' declares 'ProfilePage' locally, but it is not exported. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/Index.ets:5:10\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m6 ERROR: \u001b[31m10905204 ArkTS Compiler Error\r\nError Message: 'HomePage()' does not meet UI component syntax. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/Index.ets:74:7\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m7 ERROR: \u001b[31m10905204 ArkTS Compiler Error\r\nError Message: 'FeaturedPage()' does not meet UI component syntax. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/Index.ets:76:7\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m8 ERROR: \u001b[31m10905204 ArkTS Compiler Error\r\nError Message: 'CategoryListPage()' does not meet UI component syntax. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/Index.ets:78:7\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m9 ERROR: \u001b[31m10905204 ArkTS Compiler Error\r\nError Message: 'ProfilePage()' does not meet UI component syntax. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/Index.ets:80:7\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:10}\u001b[39m\n    at runArkPack (C:\\command-line-tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5479)", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255652886900}, "additional": {"logType": "debug", "children": [], "durationId": "8a373016-51e6-4ce5-938c-074786c5d301"}}, {"head": {"id": "380c39ea-931d-440a-938f-03a096d66fd4", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255665467800, "endTime": 153255665536800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7dfefda0-6518-45cb-bb4e-2e574e5c4742", "logId": "aa34e545-1590-44f2-8113-fc41fc80894b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa34e545-1590-44f2-8113-fc41fc80894b", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255665467800, "endTime": 153255665536800}, "additional": {"logType": "info", "children": [], "durationId": "380c39ea-931d-440a-938f-03a096d66fd4"}}, {"head": {"id": "073cd163-ddfb-424c-91e5-bc274cb26952", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153244305351100, "endTime": 153255665739500}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 31, "second": 25}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "7c062a33-a078-4184-bf37-f508d8ee1dea", "name": "BUILD FAILED in 11 s 361 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255665766700}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "3675362e-e81f-4381-abc8-58cb2d73c8e9", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255665928700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6cd6643-f7b9-4d16-bf9b-6b997dd3f478", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255665987400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53207aff-0710-4467-8cec-f5eb5181cd11", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255666524500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c48814f9-67ab-4a06-b9b2-a5a59816dd56", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255666620900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3de122aa-de1b-4367-bb96-82b518370688", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255666665200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a53a1f37-b63b-4cfd-ae2a-963cd4073efe", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255666697100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52377178-c75b-46e5-bf4b-ab4481bc9305", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255666736000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a9dfd21-0913-43ae-9a24-d62072722747", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255667281200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da49f976-4257-4dad-9d83-c174327e3f55", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255667486400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f88a4754-0752-42b2-9fd4-7369e4ccd1ab", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255667555800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76ba5271-5761-4267-a178-c8e2f5dbb1e0", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255667589500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25d7a132-2013-43ae-b9a7-b04ec86706ed", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255667619300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05243242-fba7-4b73-b9f4-c7f2ad4f36a7", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255667650900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "138ac5ec-1aac-4634-8b7a-29b072f5aac9", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255668661800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fad889a4-440d-4b12-b108-1e4b4dcb9686", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255668986400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f68d88a6-213c-4edd-92ea-fabeb03ddb90", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255669204000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "756c5c61-0d4a-477e-9c19-80304742e6e4", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255669269100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "560d5d7d-8ff5-42cf-8756-4121890e0b31", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255669309800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e935ac2-e8a6-4244-a6e7-40349d3b469b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255669341600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3837461f-7124-44e4-94e1-a5e52a435479", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255669377200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edfdea5a-b150-407e-b948-2fc604be3518", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255669405900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbc511e2-7c79-4c5e-ab81-9426e325d1ff", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255672563700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab05583e-fa13-4c38-8963-251f987013b1", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255673744400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a6e1e47-bb41-4f87-8d5b-b744dafbf9ea", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255674208400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "365036bd-c433-4731-b2ef-6e53c0661e76", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255674471300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "641d88c6-8b53-4c9a-88cb-5734c37f8b70", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255674678800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d5a94ae-32ca-44b5-a79b-68e48ea28da1", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255675404500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1817c7f8-9f43-468d-8e81-3f34469bbe2a", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255681545300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee349ede-ae5c-486a-8255-12843e0b9a0d", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255681994600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc354df4-293e-45d4-b0df-cf753d643055", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255682413000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9440e3d-b95f-497b-8aa2-bd43447a873a", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255683499000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a27f66db-6e4f-4e3b-9dc6-9a27f9bd77c7", "name": "Incremental task entry:default@CompileArkTS post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255684120700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b251248e-e61e-41e7-8b7e-5914e0c4b111", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255686058300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e86bf214-f947-494c-af15-7e4a528f847c", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255686918200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a87428fc-6715-4bc5-b1d3-19d4cb494984", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255687463600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "726d4501-9dbe-4411-810e-43243ebb6305", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255688277400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d63ccfe3-78c2-40bf-9fb3-f8bb4a5f73ce", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255688823000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f5b21a7-894b-4cf9-a83a-505f28dde5e6", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255690257500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b80040c-601b-4523-b521-b29e60ccf239", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255691209600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01175145-1e54-4558-a166-97c3ad37dba6", "name": "Incremental task entry:default@BuildJS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255691593500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4a7a825-5fa9-4367-93a7-42042eccc175", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255691675300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}