"use strict";
export default {
  "component.globalHeader.search": "\u691C\u7D22",
  "component.globalHeader.search.example1": "\u691C\u7D22\u4F8B1",
  "component.globalHeader.search.example2": "\u691C\u7D22\u4F8B2",
  "component.globalHeader.search.example3": "\u691C\u7D22\u4F8B3",
  "component.globalHeader.help": "\u30D8\u30EB\u30D7",
  "component.globalHeader.notification": "\u901A\u77E5",
  "component.globalHeader.notification.empty": "\u3059\u3079\u3066\u306E\u901A\u77E5\u3092\u8868\u793A\u3057\u307E\u3057\u305F\u3002",
  "component.globalHeader.message": "\u30E1\u30C3\u30BB\u30FC\u30B8",
  "component.globalHeader.message.empty": "\u3059\u3079\u3066\u306E\u30E1\u30C3\u30BB\u30FC\u30B8\u3092\u8868\u793A\u3057\u307E\u3057\u305F\u3002",
  "component.globalHeader.event": "\u30A4\u30D9\u30F3\u30C8",
  "component.globalHeader.event.empty": "\u3059\u3079\u3066\u306E\u30A4\u30D9\u30F3\u30C8\u3092\u8868\u793A\u3057\u307E\u3057\u305F\u3002",
  "component.noticeIcon.clear": "\u30AF\u30EA\u30A2",
  "component.noticeIcon.cleared": "\u30AF\u30EA\u30A2\u6E08\u307F",
  "component.noticeIcon.empty": "\u901A\u77E5\u306A\u3057",
  "component.noticeIcon.view-more": "\u3082\u3063\u3068\u898B\u308B"
};
