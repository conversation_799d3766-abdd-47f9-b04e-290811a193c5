{"version": "2.0", "ppid": 32260, "events": [{"head": {"id": "3f5616d3-95f8-44c8-aff4-f0f55765c7df", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423770479900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bfc1948-a2c5-4d00-9cdb-93b5d4b5c5bf", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423781075400, "endTime": 149427127226500}, "additional": {"children": ["db4cb2a4-3283-485f-8c7a-47d1392f4c94", "ca0d51d7-bfe0-40fc-9343-ddbce4525b8f", "c45b18d8-7446-431b-8230-cd5e32832fb6", "1c8809a9-8c60-420c-8b60-71877ec4c7de", "ff099b86-f434-4f6c-9aac-1488482be359", "ef3c7ca3-6f6a-4816-bdf2-92da8f51f367", "3a94107c-2c65-428b-a918-ba9a41ea863e"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "43d6c6af-dbb6-4e05-b211-f9db091aa829"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db4cb2a4-3283-485f-8c7a-47d1392f4c94", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423781081200, "endTime": 149423801113200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8bfc1948-a2c5-4d00-9cdb-93b5d4b5c5bf", "logId": "3719745f-df7c-40df-a747-1a594f57abf1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca0d51d7-bfe0-40fc-9343-ddbce4525b8f", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423801138600, "endTime": 149427124844300}, "additional": {"children": ["bf5cb310-cd06-4998-93b0-8986646941e7", "29258400-1795-4af2-a3ce-5ee51f0340fb", "f35e3c9f-13e3-46b4-b696-0464352fb6ee", "9a5ecd30-2d95-44d4-8261-93b30f8b900d", "545ca0d2-20b1-4070-985a-f9369b832df3", "d8f5f4ca-5819-4e19-98d4-b2aae45b0af4", "de167951-d7ee-4d4c-95bd-3fc5de485b3e", "69c29994-e188-4186-b820-926c36de9d83", "6dceed6c-a060-4082-852e-c005b09ea1ef"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8bfc1948-a2c5-4d00-9cdb-93b5d4b5c5bf", "logId": "3f93a65c-5728-44f6-af45-ba7eda043059"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c45b18d8-7446-431b-8230-cd5e32832fb6", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427124871800, "endTime": 149427127197900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8bfc1948-a2c5-4d00-9cdb-93b5d4b5c5bf", "logId": "a6b19881-8566-4b70-b4cc-f61a259eb40a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c8809a9-8c60-420c-8b60-71877ec4c7de", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427127203300, "endTime": 149427127218500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8bfc1948-a2c5-4d00-9cdb-93b5d4b5c5bf", "logId": "eeaa82e9-72d1-40e7-ad62-5465608b1622"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff099b86-f434-4f6c-9aac-1488482be359", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423786862100, "endTime": 149423787102300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8bfc1948-a2c5-4d00-9cdb-93b5d4b5c5bf", "logId": "fb8cd1c6-825e-4247-852e-3a6eb26ffbcd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb8cd1c6-825e-4247-852e-3a6eb26ffbcd", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423786862100, "endTime": 149423787102300}, "additional": {"logType": "info", "children": [], "durationId": "ff099b86-f434-4f6c-9aac-1488482be359", "parent": "43d6c6af-dbb6-4e05-b211-f9db091aa829"}}, {"head": {"id": "ef3c7ca3-6f6a-4816-bdf2-92da8f51f367", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423792737100, "endTime": 149423792789000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8bfc1948-a2c5-4d00-9cdb-93b5d4b5c5bf", "logId": "6a5c0818-3f56-4258-b5cc-da9df96839c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a5c0818-3f56-4258-b5cc-da9df96839c6", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423792737100, "endTime": 149423792789000}, "additional": {"logType": "info", "children": [], "durationId": "ef3c7ca3-6f6a-4816-bdf2-92da8f51f367", "parent": "43d6c6af-dbb6-4e05-b211-f9db091aa829"}}, {"head": {"id": "91b03620-76a1-4a50-8323-d895c1fae751", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423793308400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "057a0be7-4795-45c8-8fdf-aae101ce62eb", "name": "Cache service initialization finished in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423800931500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3719745f-df7c-40df-a747-1a594f57abf1", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423781081200, "endTime": 149423801113200}, "additional": {"logType": "info", "children": [], "durationId": "db4cb2a4-3283-485f-8c7a-47d1392f4c94", "parent": "43d6c6af-dbb6-4e05-b211-f9db091aa829"}}, {"head": {"id": "bf5cb310-cd06-4998-93b0-8986646941e7", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423806917100, "endTime": 149423806974400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca0d51d7-bfe0-40fc-9343-ddbce4525b8f", "logId": "6f80ab3e-5e75-4b4c-9eb0-ddf8c05ace3c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29258400-1795-4af2-a3ce-5ee51f0340fb", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423807022500, "endTime": 149423811643100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca0d51d7-bfe0-40fc-9343-ddbce4525b8f", "logId": "8e5a2a68-64fd-4103-b1df-49cb018218a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f35e3c9f-13e3-46b4-b696-0464352fb6ee", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423811770600, "endTime": 149426967986900}, "additional": {"children": ["9554720c-6235-4593-b0bf-22a7d1b10359", "b0d0107c-513d-4659-9676-0fd5b793beaf", "193975a9-6c8c-4dd0-b2ca-2b41a712c36b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca0d51d7-bfe0-40fc-9343-ddbce4525b8f", "logId": "242be8e4-f0a6-4571-a14b-419e26005d9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a5ecd30-2d95-44d4-8261-93b30f8b900d", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426968120900, "endTime": 149427004934700}, "additional": {"children": ["b8921bdc-75b2-41f1-8b2d-93cefceb4b38"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca0d51d7-bfe0-40fc-9343-ddbce4525b8f", "logId": "972d4ba5-842a-4411-b383-2c5c56678f74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "545ca0d2-20b1-4070-985a-f9369b832df3", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427004994900, "endTime": 149427075208200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca0d51d7-bfe0-40fc-9343-ddbce4525b8f", "logId": "97bc4139-e098-4a57-88b5-baee9cfad938"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8f5f4ca-5819-4e19-98d4-b2aae45b0af4", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427076826400, "endTime": 149427093636100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca0d51d7-bfe0-40fc-9343-ddbce4525b8f", "logId": "90596ad6-5f33-4e45-8130-4ccba87c7f64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de167951-d7ee-4d4c-95bd-3fc5de485b3e", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427093661200, "endTime": 149427124514200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca0d51d7-bfe0-40fc-9343-ddbce4525b8f", "logId": "b7a711ca-90d5-424b-ab5a-e9adfc4ff354"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69c29994-e188-4186-b820-926c36de9d83", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427124537800, "endTime": 149427124828600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca0d51d7-bfe0-40fc-9343-ddbce4525b8f", "logId": "8b63d2fe-828b-48e8-b25d-4520c2d1cc21"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f80ab3e-5e75-4b4c-9eb0-ddf8c05ace3c", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423806917100, "endTime": 149423806974400}, "additional": {"logType": "info", "children": [], "durationId": "bf5cb310-cd06-4998-93b0-8986646941e7", "parent": "3f93a65c-5728-44f6-af45-ba7eda043059"}}, {"head": {"id": "8e5a2a68-64fd-4103-b1df-49cb018218a8", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423807022500, "endTime": 149423811643100}, "additional": {"logType": "info", "children": [], "durationId": "29258400-1795-4af2-a3ce-5ee51f0340fb", "parent": "3f93a65c-5728-44f6-af45-ba7eda043059"}}, {"head": {"id": "9554720c-6235-4593-b0bf-22a7d1b10359", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423812587100, "endTime": 149423812651700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f35e3c9f-13e3-46b4-b696-0464352fb6ee", "logId": "d52c9ee9-6b3f-49e7-b83f-ddce99ea1cbb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d52c9ee9-6b3f-49e7-b83f-ddce99ea1cbb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423812587100, "endTime": 149423812651700}, "additional": {"logType": "info", "children": [], "durationId": "9554720c-6235-4593-b0bf-22a7d1b10359", "parent": "242be8e4-f0a6-4571-a14b-419e26005d9b"}}, {"head": {"id": "b0d0107c-513d-4659-9676-0fd5b793beaf", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423815361600, "endTime": 149426967087100}, "additional": {"children": ["94549314-d473-425b-9f9e-62922899f84f", "22c843d8-1ab9-445a-a550-89272bb4fc8a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f35e3c9f-13e3-46b4-b696-0464352fb6ee", "logId": "0a006834-d6c9-49e7-8804-8d38a39c8062"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94549314-d473-425b-9f9e-62922899f84f", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423815363700, "endTime": 149426682334900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b0d0107c-513d-4659-9676-0fd5b793beaf", "logId": "92a9c74d-25cf-480e-a76f-ae70907522ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22c843d8-1ab9-445a-a550-89272bb4fc8a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426682363800, "endTime": 149426967070900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b0d0107c-513d-4659-9676-0fd5b793beaf", "logId": "cfe2d74f-54c8-4fb7-9b83-93d278471ba9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f3b1858-c8de-4210-9e6f-113b6f8769d2", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423815372200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "104cbfb0-2c13-437f-8a91-edf3af4e840b", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426682150300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92a9c74d-25cf-480e-a76f-ae70907522ac", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423815363700, "endTime": 149426682334900}, "additional": {"logType": "info", "children": [], "durationId": "94549314-d473-425b-9f9e-62922899f84f", "parent": "0a006834-d6c9-49e7-8804-8d38a39c8062"}}, {"head": {"id": "7caaa4d5-4136-4a26-a81a-746e67d57b6f", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426682490700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c2a5255-38dd-437d-ab90-9116e4358ef0", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426881188200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b26147-e041-4957-a6e5-cb256b6bd643", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426881438900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ed8b602-1ec1-49e3-ba21-03d53d569ea6", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426881946800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7ec3e3c-01d5-4e42-9f89-ca04c9ccc496", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426882174600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a116fce-5da1-42f8-8920-d9dfadd4fe8d", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426886398300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32def199-441a-4cf4-a77c-3208f1bebd1f", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426909526100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e930682-5f1f-4d08-a397-4450eb5e3ad2", "name": "Sdk init in 39 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426935369900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "632ae29f-5e6c-46ad-be88-717538e72d38", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426935804800}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 23, "minute": 27, "second": 36}, "markType": "other"}}, {"head": {"id": "b26988db-58a7-4d30-a4e1-0d639079f925", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426935977500}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 23, "minute": 27, "second": 36}, "markType": "other"}}, {"head": {"id": "522fa8e7-9dad-461c-b704-99c661ac4192", "name": "Project task initialization takes 24 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426966446800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46c872bb-105e-4ab1-9943-26d49e805d63", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426966838300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d04fe90-93cf-49e2-9e84-4f26624039ff", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426966952600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca7e3558-604a-47d8-a18f-361e9f281afb", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426967002900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfe2d74f-54c8-4fb7-9b83-93d278471ba9", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426682363800, "endTime": 149426967070900}, "additional": {"logType": "info", "children": [], "durationId": "22c843d8-1ab9-445a-a550-89272bb4fc8a", "parent": "0a006834-d6c9-49e7-8804-8d38a39c8062"}}, {"head": {"id": "0a006834-d6c9-49e7-8804-8d38a39c8062", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423815361600, "endTime": 149426967087100}, "additional": {"logType": "info", "children": ["92a9c74d-25cf-480e-a76f-ae70907522ac", "cfe2d74f-54c8-4fb7-9b83-93d278471ba9"], "durationId": "b0d0107c-513d-4659-9676-0fd5b793beaf", "parent": "242be8e4-f0a6-4571-a14b-419e26005d9b"}}, {"head": {"id": "193975a9-6c8c-4dd0-b2ca-2b41a712c36b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426967912500, "endTime": 149426967962200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f35e3c9f-13e3-46b4-b696-0464352fb6ee", "logId": "710285cc-466a-4780-9828-dfdd0ba7b21a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "710285cc-466a-4780-9828-dfdd0ba7b21a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426967912500, "endTime": 149426967962200}, "additional": {"logType": "info", "children": [], "durationId": "193975a9-6c8c-4dd0-b2ca-2b41a712c36b", "parent": "242be8e4-f0a6-4571-a14b-419e26005d9b"}}, {"head": {"id": "242be8e4-f0a6-4571-a14b-419e26005d9b", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423811770600, "endTime": 149426967986900}, "additional": {"logType": "info", "children": ["d52c9ee9-6b3f-49e7-b83f-ddce99ea1cbb", "0a006834-d6c9-49e7-8804-8d38a39c8062", "710285cc-466a-4780-9828-dfdd0ba7b21a"], "durationId": "f35e3c9f-13e3-46b4-b696-0464352fb6ee", "parent": "3f93a65c-5728-44f6-af45-ba7eda043059"}}, {"head": {"id": "b8921bdc-75b2-41f1-8b2d-93cefceb4b38", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426968733900, "endTime": 149427004907600}, "additional": {"children": ["f09945c8-3ae9-4219-a01b-e4af35ea57f3", "0b10fd3c-cfbb-4955-a0b7-fd3f70e96ef7", "ff308882-36cd-489e-afca-c51c6d0f82af"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9a5ecd30-2d95-44d4-8261-93b30f8b900d", "logId": "3bc2faca-8af5-45ef-b9c4-36a1ea980a19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f09945c8-3ae9-4219-a01b-e4af35ea57f3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426971662900, "endTime": 149426971678100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b8921bdc-75b2-41f1-8b2d-93cefceb4b38", "logId": "adb3b0a5-c82b-4634-84c9-9ff9baf153db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "adb3b0a5-c82b-4634-84c9-9ff9baf153db", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426971662900, "endTime": 149426971678100}, "additional": {"logType": "info", "children": [], "durationId": "f09945c8-3ae9-4219-a01b-e4af35ea57f3", "parent": "3bc2faca-8af5-45ef-b9c4-36a1ea980a19"}}, {"head": {"id": "0b10fd3c-cfbb-4955-a0b7-fd3f70e96ef7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426973520600, "endTime": 149427002979500}, "additional": {"children": ["9e619f59-96e0-425f-8583-f6d7df504031", "19b43945-2db6-47db-890e-a6c618e02453"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b8921bdc-75b2-41f1-8b2d-93cefceb4b38", "logId": "290d078d-f7d5-40a4-8755-566af65d8753"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e619f59-96e0-425f-8583-f6d7df504031", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426973521600, "endTime": 149426979331200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b10fd3c-cfbb-4955-a0b7-fd3f70e96ef7", "logId": "45d8c318-1773-4cf2-b8d9-377f7bf5a646"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19b43945-2db6-47db-890e-a6c618e02453", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426979352700, "endTime": 149427002970900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b10fd3c-cfbb-4955-a0b7-fd3f70e96ef7", "logId": "07cc510e-0c3e-42e4-8aa1-4fdb2ae1780a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fffe7b33-8377-47dc-9ac9-a6799d170546", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426973526400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0deec273-2560-4de7-8af8-3efd456f7203", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426979199100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45d8c318-1773-4cf2-b8d9-377f7bf5a646", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426973521600, "endTime": 149426979331200}, "additional": {"logType": "info", "children": [], "durationId": "9e619f59-96e0-425f-8583-f6d7df504031", "parent": "290d078d-f7d5-40a4-8755-566af65d8753"}}, {"head": {"id": "69e2a6db-a838-4163-954c-307bf6a1451e", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426979465300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "086967ed-9672-4453-a226-bc394bbe3e0c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426992638800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9dd9b47-0539-4cbc-bd0c-9589de29ebdc", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426992937100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31da51fd-3243-4826-b801-262e98a57a16", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426993389600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "719b3d50-e781-471a-a5f2-b6843a00cfa1", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426993646400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53850c90-1a38-42ab-aac5-470d926efd40", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426993714400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb441b7e-f100-4cca-8be4-51943ae22f86", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426993755300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5630b905-9fe7-4142-b995-dc11bc9e6156", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426993820000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12e3bd54-1969-452a-bec9-1898a1c60a80", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426993874200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6390de26-c8ac-4811-a51d-52581f0cfcac", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426994154700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "766556e7-212d-4860-bea5-c9fbff86b5ab", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426994297200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb6a3893-f85c-494b-8358-e48d4dc90d9e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426994352300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34bc2f49-8590-45c0-92bc-3c520b16fe67", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426994385900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af4bfcd4-5ece-4ae9-bfbf-272d3cc7cb53", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426994584900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d527e5b-5657-4142-bd7a-370522f5e36e", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426994676700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06339641-dfa9-46d1-92ca-79198ab42163", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426994937900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dc69c43-0bfc-4bf2-b5c0-4d9225c1b4c1", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426995074600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba8abf67-2574-42ff-9612-a7505d1ad56c", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426995115300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1679bdce-439c-41f6-9d59-22db3080e943", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426995253700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e488bd24-8c0b-465f-ab0e-043e9403e96b", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426995672400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9df936a5-b210-48f4-b63e-2534aef8f63b", "name": "Module entry task initialization takes 4 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427002673000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "090e4274-043a-4301-aff1-f8792f72de6b", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427002847000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13665189-b954-4d01-95c4-1248853fcc84", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427002906400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dd5541b-f924-4036-9535-5b3788a58400", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427002939700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07cc510e-0c3e-42e4-8aa1-4fdb2ae1780a", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426979352700, "endTime": 149427002970900}, "additional": {"logType": "info", "children": [], "durationId": "19b43945-2db6-47db-890e-a6c618e02453", "parent": "290d078d-f7d5-40a4-8755-566af65d8753"}}, {"head": {"id": "290d078d-f7d5-40a4-8755-566af65d8753", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426973520600, "endTime": 149427002979500}, "additional": {"logType": "info", "children": ["45d8c318-1773-4cf2-b8d9-377f7bf5a646", "07cc510e-0c3e-42e4-8aa1-4fdb2ae1780a"], "durationId": "0b10fd3c-cfbb-4955-a0b7-fd3f70e96ef7", "parent": "3bc2faca-8af5-45ef-b9c4-36a1ea980a19"}}, {"head": {"id": "ff308882-36cd-489e-afca-c51c6d0f82af", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427004875200, "endTime": 149427004891000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b8921bdc-75b2-41f1-8b2d-93cefceb4b38", "logId": "1ff0dc45-4854-4419-9c30-1fbfd7c93bef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ff0dc45-4854-4419-9c30-1fbfd7c93bef", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427004875200, "endTime": 149427004891000}, "additional": {"logType": "info", "children": [], "durationId": "ff308882-36cd-489e-afca-c51c6d0f82af", "parent": "3bc2faca-8af5-45ef-b9c4-36a1ea980a19"}}, {"head": {"id": "3bc2faca-8af5-45ef-b9c4-36a1ea980a19", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426968733900, "endTime": 149427004907600}, "additional": {"logType": "info", "children": ["adb3b0a5-c82b-4634-84c9-9ff9baf153db", "290d078d-f7d5-40a4-8755-566af65d8753", "1ff0dc45-4854-4419-9c30-1fbfd7c93bef"], "durationId": "b8921bdc-75b2-41f1-8b2d-93cefceb4b38", "parent": "972d4ba5-842a-4411-b383-2c5c56678f74"}}, {"head": {"id": "972d4ba5-842a-4411-b383-2c5c56678f74", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149426968120900, "endTime": 149427004934700}, "additional": {"logType": "info", "children": ["3bc2faca-8af5-45ef-b9c4-36a1ea980a19"], "durationId": "9a5ecd30-2d95-44d4-8261-93b30f8b900d", "parent": "3f93a65c-5728-44f6-af45-ba7eda043059"}}, {"head": {"id": "902dd655-8bbb-4fca-8693-d71bfca42527", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427024770500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fe6c611-4c02-4e9f-95b1-c671d6cd6565", "name": "hvigorfile, resolve hvigorfile dependencies in 70 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427075031400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97bc4139-e098-4a57-88b5-baee9cfad938", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427004994900, "endTime": 149427075208200}, "additional": {"logType": "info", "children": [], "durationId": "545ca0d2-20b1-4070-985a-f9369b832df3", "parent": "3f93a65c-5728-44f6-af45-ba7eda043059"}}, {"head": {"id": "6dceed6c-a060-4082-852e-c005b09ea1ef", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427076186900, "endTime": 149427076772800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca0d51d7-bfe0-40fc-9343-ddbce4525b8f", "logId": "ce8dd9dc-af88-43a2-bc9a-a2828cca9d4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf478e1a-8649-4f39-a1cc-9d037905b2e0", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427076334700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce8dd9dc-af88-43a2-bc9a-a2828cca9d4a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427076186900, "endTime": 149427076772800}, "additional": {"logType": "info", "children": [], "durationId": "6dceed6c-a060-4082-852e-c005b09ea1ef", "parent": "3f93a65c-5728-44f6-af45-ba7eda043059"}}, {"head": {"id": "2deec0db-ff09-4cdc-9169-cb0356692362", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427081548000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c1ae915-9372-4e08-8faa-f891e3c170b0", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427092188100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90596ad6-5f33-4e45-8130-4ccba87c7f64", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427076826400, "endTime": 149427093636100}, "additional": {"logType": "info", "children": [], "durationId": "d8f5f4ca-5819-4e19-98d4-b2aae45b0af4", "parent": "3f93a65c-5728-44f6-af45-ba7eda043059"}}, {"head": {"id": "ba81356c-e1c8-47a0-a05c-6fca86156728", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427093776400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69f72756-2f08-4354-93e1-64d357544e4d", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427110268700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "636c1654-c198-40bd-b7cd-6074ca250843", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427110405900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dde1989-64eb-4cdd-b5da-711a91ded593", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427111167800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06c50c85-0028-4907-9b05-57b087a8e247", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427117433900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "201eb55f-5433-4728-a3c7-85672a9cfb4a", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427117569400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7a711ca-90d5-424b-ab5a-e9adfc4ff354", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427093661200, "endTime": 149427124514200}, "additional": {"logType": "info", "children": [], "durationId": "de167951-d7ee-4d4c-95bd-3fc5de485b3e", "parent": "3f93a65c-5728-44f6-af45-ba7eda043059"}}, {"head": {"id": "012764c2-b7d7-49e2-b366-51453cce4b9f", "name": "Configuration phase cost:3 s 318 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427124711000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b63d2fe-828b-48e8-b25d-4520c2d1cc21", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427124537800, "endTime": 149427124828600}, "additional": {"logType": "info", "children": [], "durationId": "69c29994-e188-4186-b820-926c36de9d83", "parent": "3f93a65c-5728-44f6-af45-ba7eda043059"}}, {"head": {"id": "3f93a65c-5728-44f6-af45-ba7eda043059", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423801138600, "endTime": 149427124844300}, "additional": {"logType": "info", "children": ["6f80ab3e-5e75-4b4c-9eb0-ddf8c05ace3c", "8e5a2a68-64fd-4103-b1df-49cb018218a8", "242be8e4-f0a6-4571-a14b-419e26005d9b", "972d4ba5-842a-4411-b383-2c5c56678f74", "97bc4139-e098-4a57-88b5-baee9cfad938", "90596ad6-5f33-4e45-8130-4ccba87c7f64", "b7a711ca-90d5-424b-ab5a-e9adfc4ff354", "8b63d2fe-828b-48e8-b25d-4520c2d1cc21", "ce8dd9dc-af88-43a2-bc9a-a2828cca9d4a"], "durationId": "ca0d51d7-bfe0-40fc-9343-ddbce4525b8f", "parent": "43d6c6af-dbb6-4e05-b211-f9db091aa829"}}, {"head": {"id": "3a94107c-2c65-428b-a918-ba9a41ea863e", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427127126100, "endTime": 149427127181700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8bfc1948-a2c5-4d00-9cdb-93b5d4b5c5bf", "logId": "16502a36-0475-4f9c-b92b-8214ce6df888"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16502a36-0475-4f9c-b92b-8214ce6df888", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427127126100, "endTime": 149427127181700}, "additional": {"logType": "info", "children": [], "durationId": "3a94107c-2c65-428b-a918-ba9a41ea863e", "parent": "43d6c6af-dbb6-4e05-b211-f9db091aa829"}}, {"head": {"id": "a6b19881-8566-4b70-b4cc-f61a259eb40a", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427124871800, "endTime": 149427127197900}, "additional": {"logType": "info", "children": [], "durationId": "c45b18d8-7446-431b-8230-cd5e32832fb6", "parent": "43d6c6af-dbb6-4e05-b211-f9db091aa829"}}, {"head": {"id": "eeaa82e9-72d1-40e7-ad62-5465608b1622", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427127203300, "endTime": 149427127218500}, "additional": {"logType": "info", "children": [], "durationId": "1c8809a9-8c60-420c-8b60-71877ec4c7de", "parent": "43d6c6af-dbb6-4e05-b211-f9db091aa829"}}, {"head": {"id": "43d6c6af-dbb6-4e05-b211-f9db091aa829", "name": "init", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423781075400, "endTime": 149427127226500}, "additional": {"logType": "info", "children": ["3719745f-df7c-40df-a747-1a594f57abf1", "3f93a65c-5728-44f6-af45-ba7eda043059", "a6b19881-8566-4b70-b4cc-f61a259eb40a", "eeaa82e9-72d1-40e7-ad62-5465608b1622", "fb8cd1c6-825e-4247-852e-3a6eb26ffbcd", "6a5c0818-3f56-4258-b5cc-da9df96839c6", "16502a36-0475-4f9c-b92b-8214ce6df888"], "durationId": "8bfc1948-a2c5-4d00-9cdb-93b5d4b5c5bf"}}, {"head": {"id": "318b45ad-90d2-4075-b9af-b98c322fc5f4", "name": "Configuration task cost before running: 3 s 352 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427127837800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14111ce9-e4e0-47c1-9a2d-894b97954ace", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427146876400, "endTime": 149427163286500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "9e569941-5540-4dbd-89cd-f00ff2060a86", "logId": "e1df66d7-7b7e-4903-8ca1-8b3984f7853e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e569941-5540-4dbd-89cd-f00ff2060a86", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427130173100}, "additional": {"logType": "detail", "children": [], "durationId": "14111ce9-e4e0-47c1-9a2d-894b97954ace"}}, {"head": {"id": "9dc53d86-b85a-410e-a4b7-89a50680226b", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427131894500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b278bde-ff09-4977-9f3b-ec9693848d15", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427132115900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a2859bf-2c21-4a1c-bed9-e844b800c818", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427133692500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cbfef05-8802-438b-a9e5-6c47c7375ef2", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427135993800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54726e5c-cf73-4d06-a475-8dd11368dfe6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427138734300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9800203e-a0bc-4adf-9f8a-79105ba8658b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427138844500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a07a638-f630-49dd-ae3b-1ea0aaaf7da0", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427146920600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea31e5e3-bbeb-4cea-a226-63c9e8836d07", "name": "Incremental task entry:default@PreBuild pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427162862500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55280e34-3973-450d-aa33-e24afeba491f", "name": "entry : default@PreBuild cost memory 0.49948883056640625", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427163096300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1df66d7-7b7e-4903-8ca1-8b3984f7853e", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427146876400, "endTime": 149427163286500}, "additional": {"logType": "info", "children": [], "durationId": "14111ce9-e4e0-47c1-9a2d-894b97954ace"}}, {"head": {"id": "39108da7-0f37-45ab-9f06-2887aa687335", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427174733300, "endTime": 149427180608500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "55948de9-571b-4e96-8c85-42953d833f65", "logId": "d20cf34a-adaf-4402-8001-5700cf6300cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55948de9-571b-4e96-8c85-42953d833f65", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427170351400}, "additional": {"logType": "detail", "children": [], "durationId": "39108da7-0f37-45ab-9f06-2887aa687335"}}, {"head": {"id": "e7d3e813-b91b-4cba-bb3a-10c5d4781202", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427172880200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1812ec71-b694-4ea5-9fbc-9bf775d629b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427173087300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6ba81d3-5c0b-4fb0-9d07-683bef49c768", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427174759300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eac2d49-235a-4f53-9324-1b697d9150d9", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427177118200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a521685-1ffe-4ea2-b438-8ab6475f2314", "name": "entry : default@CreateModuleInfo cost memory 0.0645294189453125", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427179795200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "611731f1-e436-4337-9b33-59142553f16b", "name": "runTaskFromQueue task cost before running: 3 s 404 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427180298900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d20cf34a-adaf-4402-8001-5700cf6300cf", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427174733300, "endTime": 149427180608500, "totalTime": 5509600}, "additional": {"logType": "info", "children": [], "durationId": "39108da7-0f37-45ab-9f06-2887aa687335"}}, {"head": {"id": "6a459a47-109c-4c5d-9de2-eb0814df1330", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427197155500, "endTime": 149427202357000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "400a99d4-8ee9-4624-bfda-b85b2278bb93", "logId": "2d29ab7f-6e06-4058-992e-8026a4f731d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "400a99d4-8ee9-4624-bfda-b85b2278bb93", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427186270000}, "additional": {"logType": "detail", "children": [], "durationId": "6a459a47-109c-4c5d-9de2-eb0814df1330"}}, {"head": {"id": "a7337d51-833c-413b-ac9c-77bc942b5e09", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427189099000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "507917f4-bc55-4cf9-8f87-ae41e306b1cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427189302000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1d2a0f9-6cc6-4586-a291-acd32d2d72e1", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427197177000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "072e1542-1ff0-4628-8545-1e31b74b5f08", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427199039300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa57ccf3-acca-4860-98a7-18694c21f503", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427202094500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84ad8796-2ff6-48fa-a912-68ecde5fa4c4", "name": "entry : default@GenerateMetadata cost memory 0.10811614990234375", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427202275900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d29ab7f-6e06-4058-992e-8026a4f731d7", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427197155500, "endTime": 149427202357000}, "additional": {"logType": "info", "children": [], "durationId": "6a459a47-109c-4c5d-9de2-eb0814df1330"}}, {"head": {"id": "8d733f9f-30d6-4f58-9841-a7042fc17681", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427206635100, "endTime": 149427207275900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "686c39b1-c595-43e0-a90f-ce6c34885df9", "logId": "b07df37b-3815-4d00-b4b9-9c030444e221"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "686c39b1-c595-43e0-a90f-ce6c34885df9", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427204874900}, "additional": {"logType": "detail", "children": [], "durationId": "8d733f9f-30d6-4f58-9841-a7042fc17681"}}, {"head": {"id": "df74104c-8f4c-485c-a01b-f1ee89526926", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427206136000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d66e86b-419e-4321-8a84-561ddd23a5bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427206269300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04309bff-7b9b-40ef-ae23-83fbde4fb4f6", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427206650000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71eab4ea-0891-4f38-b645-f6365d88e862", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427206949800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9944f68e-1816-4d57-9ef7-eb5a0e375432", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427207023200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b24d599-bcef-4fe6-973f-5047e4979b1c", "name": "entry : default@ConfigureCmake cost memory 0.037750244140625", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427207118100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edc1f992-75c4-456e-882c-480fa404ad0e", "name": "runTaskFromQueue task cost before running: 3 s 431 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427207230900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b07df37b-3815-4d00-b4b9-9c030444e221", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427206635100, "endTime": 149427207275900, "totalTime": 564300}, "additional": {"logType": "info", "children": [], "durationId": "8d733f9f-30d6-4f58-9841-a7042fc17681"}}, {"head": {"id": "de7d766c-5efc-4cd6-a940-e58c6a75ebe6", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427211960800, "endTime": 149427217769800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "c6e64947-4ef6-410a-93e1-39057c486d4c", "logId": "5fd5771a-0e46-4fff-beaa-5968dc0b7e55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6e64947-4ef6-410a-93e1-39057c486d4c", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427209605900}, "additional": {"logType": "detail", "children": [], "durationId": "de7d766c-5efc-4cd6-a940-e58c6a75ebe6"}}, {"head": {"id": "52fbf0c3-a1a2-4e78-8743-69660ad4b3a7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427210864200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9fa8f28-0ae2-4069-a9df-238613a052ac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427210984000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7fde21b-d6a4-4d02-a9a8-711c4a666671", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427211978500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "689abb50-9c14-4562-bfed-126aff845d83", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427217487900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91a790a9-8caf-4066-b1e5-4f68d3216b11", "name": "entry : default@MergeProfile cost memory 0.1244049072265625", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427217676500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fd5771a-0e46-4fff-beaa-5968dc0b7e55", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427211960800, "endTime": 149427217769800}, "additional": {"logType": "info", "children": [], "durationId": "de7d766c-5efc-4cd6-a940-e58c6a75ebe6"}}, {"head": {"id": "23d69305-7a22-4e0c-a443-860ec1307f6e", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427222396700, "endTime": 149427225851300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4ae3bdd9-4a66-4ee2-b8eb-c5bc47f5e980", "logId": "7349aa82-ae1f-4035-9297-9b17e721f37f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ae3bdd9-4a66-4ee2-b8eb-c5bc47f5e980", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427219912600}, "additional": {"logType": "detail", "children": [], "durationId": "23d69305-7a22-4e0c-a443-860ec1307f6e"}}, {"head": {"id": "7aa3310e-4dd2-4197-b418-6613e4eb5dd3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427221176900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ea3179d-ea50-4680-8240-2b4805252479", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427221304600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "912efdbb-caff-420e-902d-bb9739aad59e", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427222414000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17a5f44d-0530-4466-b897-c1f5df4c84af", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427223832000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ac0a851-7640-4858-853f-c31c74da3c55", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427225650100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e06ad77-3308-4c4c-b178-4c1c03f6e8c5", "name": "entry : default@CreateBuildProfile cost memory 0.1143951416015625", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427225778200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7349aa82-ae1f-4035-9297-9b17e721f37f", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427222396700, "endTime": 149427225851300}, "additional": {"logType": "info", "children": [], "durationId": "23d69305-7a22-4e0c-a443-860ec1307f6e"}}, {"head": {"id": "f93b8d7a-e02f-49f2-a5da-13c8be7b54d4", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427230362800, "endTime": 149427231127300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "ee75e1a3-d536-48fc-925e-2fc17a13a42f", "logId": "f8661809-b0a8-47bc-950d-b726658f8433"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee75e1a3-d536-48fc-925e-2fc17a13a42f", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427227895400}, "additional": {"logType": "detail", "children": [], "durationId": "f93b8d7a-e02f-49f2-a5da-13c8be7b54d4"}}, {"head": {"id": "dae90cee-9350-45a3-8a57-b7f85d8f7aed", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427229260400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bad63fe7-7a4d-4cbc-90df-27171b7359d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427229398100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9913b1a-f36a-4e0e-a286-3b035396c154", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427230381100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "108113e8-19e5-453a-8519-155556d3caef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427230532100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99950d33-99be-4e8d-92b4-16195e470161", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427230583200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "526ca681-f04f-41aa-b0e4-1d31860f0081", "name": "entry : default@PreCheckSyscap cost memory 0.04160308837890625", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427230950900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccc5c180-554d-4c18-aa16-59989a3932f1", "name": "runTaskFromQueue task cost before running: 3 s 455 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427231075400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8661809-b0a8-47bc-950d-b726658f8433", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427230362800, "endTime": 149427231127300, "totalTime": 685100}, "additional": {"logType": "info", "children": [], "durationId": "f93b8d7a-e02f-49f2-a5da-13c8be7b54d4"}}, {"head": {"id": "0332590d-be86-4deb-ab49-8eef380a53c5", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427239137500, "endTime": 149427249449900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "12b98440-ec3e-4d98-b99d-11e86a41ab49", "logId": "0443a098-bed7-428f-8522-3aeaacb9eb93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12b98440-ec3e-4d98-b99d-11e86a41ab49", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427233897400}, "additional": {"logType": "detail", "children": [], "durationId": "0332590d-be86-4deb-ab49-8eef380a53c5"}}, {"head": {"id": "5c22ff1d-8859-42f8-9be9-c1c6c43811d4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427236386900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56031af0-181c-4509-89f8-07b168bc5a63", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427236521300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a16b383c-365a-4b31-8b31-98c0d9be4172", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427239164900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8eae9520-1cb0-4b7a-a9a1-83de334dc320", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427247822100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d53d338-e3af-47a0-b636-8bcf6de23f18", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427249183900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df5366b4-9bb5-4972-81d2-a0b10e4eeaca", "name": "entry : default@GeneratePkgContextInfo cost memory 0.4923858642578125", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427249368400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0443a098-bed7-428f-8522-3aeaacb9eb93", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427239137500, "endTime": 149427249449900}, "additional": {"logType": "info", "children": [], "durationId": "0332590d-be86-4deb-ab49-8eef380a53c5"}}, {"head": {"id": "fe7b3217-d68d-4ada-9302-a0318e4f3c2b", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427258975800, "endTime": 149427261785200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "7e6ef3bd-3e11-412c-8577-4f1ad3d008a2", "logId": "dfa858ae-e326-4fc6-8603-1fc16576f03e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e6ef3bd-3e11-412c-8577-4f1ad3d008a2", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427251665100}, "additional": {"logType": "detail", "children": [], "durationId": "fe7b3217-d68d-4ada-9302-a0318e4f3c2b"}}, {"head": {"id": "f4e79953-2b87-4eb8-a60b-59b61bb321f5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427252869500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6f23428-abbc-4180-8987-5c102301d1e6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427252977700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cf29dca-b234-42b1-877a-2bd993cbfd5d", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427258995100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4390f37-9ceb-46d6-9897-d6f8ea76b97f", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427261180000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82d6a936-db23-4c97-876b-d2a8ab871d90", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427261362700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c78a1a-fda2-4309-87c2-33083ec8e4e1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427261458000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09478ba1-92b3-4c09-9ff0-205aa1e2e6e5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427261500900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e956425b-5a5a-4d85-8095-8f16fcda6c7f", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12285614013671875", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427261653300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18625be0-e0d2-4753-9253-3869f0786551", "name": "runTaskFromQueue task cost before running: 3 s 486 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427261743800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfa858ae-e326-4fc6-8603-1fc16576f03e", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427258975800, "endTime": 149427261785200, "totalTime": 2741200}, "additional": {"logType": "info", "children": [], "durationId": "fe7b3217-d68d-4ada-9302-a0318e4f3c2b"}}, {"head": {"id": "8588883b-4df5-40f3-ac97-f311d33b9a52", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427266944700, "endTime": 149427267439100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "9c9e3b5f-8ef2-437c-93db-d55087d416a4", "logId": "e5c6d1d1-6e6e-470c-b6ed-b4b88553cf35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c9e3b5f-8ef2-437c-93db-d55087d416a4", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427264509700}, "additional": {"logType": "detail", "children": [], "durationId": "8588883b-4df5-40f3-ac97-f311d33b9a52"}}, {"head": {"id": "43263ac5-1d80-494c-b581-3da0c5026011", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427265924900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bee537d-b32f-4287-ae17-5f5e7c8ceecc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427266041500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b225cbf-78bb-4673-8096-db61a46c31c1", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427266959800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6e186eb-8adb-45eb-9f19-9430e7de81f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427267112500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dda3dc5-3d39-4d23-9946-6abfa1ae3677", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427267165100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4598bcbf-9bf0-4d43-819e-b62dc136187d", "name": "entry : default@BuildNativeWithCmake cost memory 0.03899383544921875", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427267283800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "372b0a22-fcc3-4f1a-a4cc-e0139b8b755b", "name": "runTaskFromQueue task cost before running: 3 s 491 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427267379100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5c6d1d1-6e6e-470c-b6ed-b4b88553cf35", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427266944700, "endTime": 149427267439100, "totalTime": 406300}, "additional": {"logType": "info", "children": [], "durationId": "8588883b-4df5-40f3-ac97-f311d33b9a52"}}, {"head": {"id": "bd05ecdf-1504-41eb-a34e-90a8bfcebacf", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427271654700, "endTime": 149427277088100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "05a7c7cc-b2aa-4977-8845-c3101f222112", "logId": "f0a03353-bded-4fae-9efa-85cb3140c530"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05a7c7cc-b2aa-4977-8845-c3101f222112", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427269326100}, "additional": {"logType": "detail", "children": [], "durationId": "bd05ecdf-1504-41eb-a34e-90a8bfcebacf"}}, {"head": {"id": "e694a135-0a31-44aa-ad19-6a01a623f6e0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427270597100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb9e66e5-df74-4e7b-b3d9-3f82caab1e51", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427270773300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "007ee6bd-43d7-4a0c-8084-3a952339b6e3", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427271674800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621ac9fc-34e6-4dce-afd5-24a62e08914e", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427276697000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f6a1417-b328-4b48-9402-1ae930472043", "name": "entry : default@MakePackInfo cost memory 0.170928955078125", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427276841600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0a03353-bded-4fae-9efa-85cb3140c530", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427271654700, "endTime": 149427277088100}, "additional": {"logType": "info", "children": [], "durationId": "bd05ecdf-1504-41eb-a34e-90a8bfcebacf"}}, {"head": {"id": "742d5ea8-c27a-43e3-a26b-f0c692db9991", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427282991100, "endTime": 149427305185800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9ff212a5-fe65-41fe-b9fd-7026ee27479e", "logId": "c0303ee0-10b0-41ca-a5ca-a36b35ba69ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ff212a5-fe65-41fe-b9fd-7026ee27479e", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427279960400}, "additional": {"logType": "detail", "children": [], "durationId": "742d5ea8-c27a-43e3-a26b-f0c692db9991"}}, {"head": {"id": "938737e8-df65-4504-af2c-d3b8c1f9faee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427281310700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2695916e-e675-4092-9957-3a8c0e66e41f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427281461800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f47ee8b-4c5f-42d9-8bce-9e386b16eba6", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427283008800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6da26179-dbba-4fb2-9bef-d43c10957050", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427283250800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a5e4c1c-e0c4-4590-9158-5d48c7496b18", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427284416200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09069082-6c15-42ad-af24-fd45c40a6a34", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 21 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427304905800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15a43ab3-a3d8-44a2-aa00-f35027d886e9", "name": "entry : default@SyscapTransform cost memory 0.1602783203125", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427305088600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0303ee0-10b0-41ca-a5ca-a36b35ba69ea", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427282991100, "endTime": 149427305185800}, "additional": {"logType": "info", "children": [], "durationId": "742d5ea8-c27a-43e3-a26b-f0c692db9991"}}, {"head": {"id": "d895c675-8d43-4f95-9fdd-f869ebe5d14c", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427310498800, "endTime": 149427315325900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "ac285eda-da0d-42ba-84b2-88803587aad5", "logId": "d23ae02e-19b8-4691-af40-3a9bf4937f44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac285eda-da0d-42ba-84b2-88803587aad5", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427307352000}, "additional": {"logType": "detail", "children": [], "durationId": "d895c675-8d43-4f95-9fdd-f869ebe5d14c"}}, {"head": {"id": "f733b76f-e1ab-4e6d-b743-4c8dd767e7fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427308558000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e6c69c7-6868-42c7-93ea-25e435ccdf54", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427308667000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f004320c-19dc-4ba9-bd21-598514216c17", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427310515100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c86761c-2fae-4de6-8402-aa1f21917106", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427315093000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f64e639-f3f7-4437-abca-cadd8fc49951", "name": "entry : default@ProcessProfile cost memory 0.12865447998046875", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427315251700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d23ae02e-19b8-4691-af40-3a9bf4937f44", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427310498800, "endTime": 149427315325900}, "additional": {"logType": "info", "children": [], "durationId": "d895c675-8d43-4f95-9fdd-f869ebe5d14c"}}, {"head": {"id": "2d1277b6-a7d7-4aac-a6e9-f4ec14fa833f", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427320849300, "endTime": 149427332038100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f45f1b9e-7feb-4797-8e98-3741b01f59d3", "logId": "8ec4f7c6-c2fa-4f43-83b2-70f64f7323e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f45f1b9e-7feb-4797-8e98-3741b01f59d3", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427317377400}, "additional": {"logType": "detail", "children": [], "durationId": "2d1277b6-a7d7-4aac-a6e9-f4ec14fa833f"}}, {"head": {"id": "4e8a2e32-7e43-4039-8f50-aee94856c571", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427318648300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "651f207b-f1ef-428b-a176-c8261f3aa2b3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427318763800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10a43aa8-77bd-4cc8-a808-3ce02856d337", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427320866100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02d5e082-291a-42c2-9817-025dd7ce17a2", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427331547300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d82bfca-68d9-4e90-8b68-fbbd62f2b538", "name": "entry : default@ProcessRouterMap cost memory 0.2444610595703125", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427331860200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ec4f7c6-c2fa-4f43-83b2-70f64f7323e1", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427320849300, "endTime": 149427332038100}, "additional": {"logType": "info", "children": [], "durationId": "2d1277b6-a7d7-4aac-a6e9-f4ec14fa833f"}}, {"head": {"id": "d7a4bd22-9c6d-472d-a0c1-80d6b8391d93", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427340786300, "endTime": 149427349184800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "4666aceb-77ff-41c4-834f-3722f9033286", "logId": "20542cb5-41bb-4b3d-8076-0effa2bc1f4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4666aceb-77ff-41c4-834f-3722f9033286", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427339439600}, "additional": {"logType": "detail", "children": [], "durationId": "d7a4bd22-9c6d-472d-a0c1-80d6b8391d93"}}, {"head": {"id": "0258e804-3423-4740-8bbb-37a2839dd525", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427340547200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "831a8238-d3f8-469c-9098-22ecb0ad7e43", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427340670400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6463190f-7a5b-43ad-b9f4-8aea55f8cb0f", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427340796600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fec334d-eda9-4427-9f09-b4191a8c8ccf", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427340950300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e08c49c-10d0-4c20-acec-2cf1edc7d8aa", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427346910800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13d1a834-688a-4dfa-be22-ad38970c9cf4", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427347144800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc1b0861-7558-4e07-a766-c53700af9a21", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427347290500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88fe663b-4973-4255-bb08-810854d3655b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427347379900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79635ebd-9e9a-4c7a-9c0f-9cc2392bdc05", "name": "entry : default@ProcessStartupConfig cost memory 0.2662200927734375", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427348986000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65d41b24-b667-4ee2-a266-170cd97da474", "name": "runTaskFromQueue task cost before running: 3 s 573 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427349113400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20542cb5-41bb-4b3d-8076-0effa2bc1f4e", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427340786300, "endTime": 149427349184800, "totalTime": 8300100}, "additional": {"logType": "info", "children": [], "durationId": "d7a4bd22-9c6d-472d-a0c1-80d6b8391d93"}}, {"head": {"id": "6ca100bd-01e9-4091-ad70-dec500c98662", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427354441700, "endTime": 149427357396300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "dba07321-9076-4c78-8f26-bd6af86a5890", "logId": "d219c223-9044-4409-8ca4-ac400ec1393f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dba07321-9076-4c78-8f26-bd6af86a5890", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427352387600}, "additional": {"logType": "detail", "children": [], "durationId": "6ca100bd-01e9-4091-ad70-dec500c98662"}}, {"head": {"id": "31d65e9d-f3f0-4a19-a93a-f10e5c23a686", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427353526300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a0f0ceb-50a9-4245-8c41-cfed7e3595b6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427353626600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef727819-8e8a-4cf1-9f0b-4b6c230b5537", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427354455500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f8fffaf-277a-496e-8e3a-09e68e294ff0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427354585200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb251052-93ab-4c4f-82b8-ecc2c1b64a8a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427354635400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9163ab40-d125-4dfe-86e5-8db726b57a15", "name": "entry : default@BuildNativeWithNinja cost memory 0.05879974365234375", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427357186500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0d0331e-e0e4-4f55-bf1d-1f7c16ef9529", "name": "runTaskFromQueue task cost before running: 3 s 581 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427357340200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d219c223-9044-4409-8ca4-ac400ec1393f", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427354441700, "endTime": 149427357396300, "totalTime": 2868900}, "additional": {"logType": "info", "children": [], "durationId": "6ca100bd-01e9-4091-ad70-dec500c98662"}}, {"head": {"id": "7ab8a2b8-7f60-4bc5-962c-d5c63a4ed61c", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427365587900, "endTime": 149427373822800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d9575065-1ed6-4513-85ac-0feb36abb952", "logId": "3a5a1af3-6655-4a47-b931-b6d2dfd5aa60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9575065-1ed6-4513-85ac-0feb36abb952", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427360367400}, "additional": {"logType": "detail", "children": [], "durationId": "7ab8a2b8-7f60-4bc5-962c-d5c63a4ed61c"}}, {"head": {"id": "4bb20dea-3021-43d5-8138-3c1ad187b200", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427361601900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b44cc926-1e54-46d0-a031-c08b86ac26de", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427361711600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b85eba58-7adc-4256-a260-597913c2a8c8", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427363461300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34c97abc-5d5e-4cc4-83dc-f619b6d7fe27", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427368483700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "142f296b-d0dd-40ba-a665-e7332cb3f434", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427371812500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f654a46-2796-432d-aba2-9cc7b1559de0", "name": "entry : default@ProcessResource cost memory 0.168731689453125", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427371961400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a5a1af3-6655-4a47-b931-b6d2dfd5aa60", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427365587900, "endTime": 149427373822800}, "additional": {"logType": "info", "children": [], "durationId": "7ab8a2b8-7f60-4bc5-962c-d5c63a4ed61c"}}, {"head": {"id": "5784282b-c9ea-4264-884f-aa245d3b17bb", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427383425900, "endTime": 149427410015600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6b01e42a-5483-45fa-b05f-ea1cf3f86bcc", "logId": "c055b632-01c2-487f-9f15-30651e91f3f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b01e42a-5483-45fa-b05f-ea1cf3f86bcc", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427377910300}, "additional": {"logType": "detail", "children": [], "durationId": "5784282b-c9ea-4264-884f-aa245d3b17bb"}}, {"head": {"id": "4f661373-517f-4174-82c1-58e6c8d705c3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427379089400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f18bf1b-ca8b-4174-bb0c-0b4789744905", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427379200500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b54a75f-3b91-41be-951f-ec8add9a6ace", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427383441800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a5575c2-a83a-4df8-8256-b725ea9ed4ce", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427409688200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f55eef2-204e-4fa5-b895-9df6b7d453e2", "name": "entry : default@GenerateLoaderJson cost memory 0.9505767822265625", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427409914700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c055b632-01c2-487f-9f15-30651e91f3f2", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427383425900, "endTime": 149427410015600}, "additional": {"logType": "info", "children": [], "durationId": "5784282b-c9ea-4264-884f-aa245d3b17bb"}}, {"head": {"id": "14fbedfc-ca5a-474f-8e2e-2300c4bcda37", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427421650900, "endTime": 149427427835100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "324d05d8-0891-4ae6-af11-1570375c2a44", "logId": "b024a1b9-e16f-40c5-9804-44d52c7f4ec5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "324d05d8-0891-4ae6-af11-1570375c2a44", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427419503800}, "additional": {"logType": "detail", "children": [], "durationId": "14fbedfc-ca5a-474f-8e2e-2300c4bcda37"}}, {"head": {"id": "a10cde51-d689-4483-9f56-da9c29bfaca8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427420744000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de7d0b2c-fc87-497e-8161-2d4d669a81e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427420858200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "604d1d4d-0808-4c71-9c96-a3311dec7d36", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427421662700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f81d1ce3-7b65-40ac-a6e2-02f53b120115", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427427528900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f4f32f4-1a4a-4d3e-9df3-cfa2555ecd1d", "name": "entry : default@ProcessLibs cost memory 0.179473876953125", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427427730500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b024a1b9-e16f-40c5-9804-44d52c7f4ec5", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427421650900, "endTime": 149427427835100}, "additional": {"logType": "info", "children": [], "durationId": "14fbedfc-ca5a-474f-8e2e-2300c4bcda37"}}, {"head": {"id": "63d97313-52d6-4556-aa7a-88b90dcd2856", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427436463900, "endTime": 149427723561200}, "additional": {"children": ["7657d98a-22bf-4164-8c96-27d3493bb9d0", "d744c3d4-2f13-44b4-a13f-ff2cf52bad4e"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources' has been changed."], "detailId": "4aed909b-294a-4bd0-85bd-a5c51eb57c31", "logId": "6593d66c-4b1f-4272-a340-ff8681de1969"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4aed909b-294a-4bd0-85bd-a5c51eb57c31", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427430485700}, "additional": {"logType": "detail", "children": [], "durationId": "63d97313-52d6-4556-aa7a-88b90dcd2856"}}, {"head": {"id": "eedbc3a2-914a-48e9-ac6b-5229ceaa3d2f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427431674500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b43e935f-32c8-4b09-860d-36a90ed29f8b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427431795900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf6bd70d-c62a-4a10-949c-c720f82f41bf", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427433142600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acef0325-0f03-4483-9c27-d8e203352cc2", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427436601400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03ba74bc-c79f-4fd9-bb3d-693bfc70da28", "name": "entry:default@CompileResource is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427451677000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8ba9cb9-e64f-462d-a108-eb788476505d", "name": "Incremental task entry:default@CompileResource pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427451838900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7657d98a-22bf-4164-8c96-27d3493bb9d0", "name": "create intermediate resource category", "description": "", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427454030800, "endTime": 149427564595500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63d97313-52d6-4556-aa7a-88b90dcd2856", "logId": "0e0d6480-4436-46ea-8c10-ece7f84c463c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e0d6480-4436-46ea-8c10-ece7f84c463c", "name": "create intermediate resource category", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427454030800, "endTime": 149427564595500}, "additional": {"logType": "info", "children": [], "durationId": "7657d98a-22bf-4164-8c96-27d3493bb9d0", "parent": "6593d66c-4b1f-4272-a340-ff8681de1969"}}, {"head": {"id": "eb0fbd10-0b23-4f12-889c-6e9ecb8a33f6", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\restool.exe]\n [\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\restool.exe',\n  '-l',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resConfig.json'\n]", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427565313400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d744c3d4-2f13-44b4-a13f-ff2cf52bad4e", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427566464700, "endTime": 149427720987900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "63d97313-52d6-4556-aa7a-88b90dcd2856", "logId": "8315510b-4782-43ca-9846-8bc9e3686106"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee75537d-c46c-4f07-ae59-0cdb0c9c8d9c", "name": "current process  memoryUsage: {\n  rss: 259485696,\n  heapTotal: 167219200,\n  heapUsed: 142836264,\n  external: 3103219,\n  arrayBuffers: 90398\n} os memoryUsage :13.196495056152344", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427568579600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7acbe7c1-0933-4e6a-9360-24f8565ad766", "name": "Info: Pack: normal pack mode\r\n", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427626627900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56595588-864a-4655-90b7-85e4e2f2f7e8", "name": "Warning: 'extensionPath' value cannot be empty.\r\nat C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\opt-compression.json\r\n", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427627368100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "988cc708-b63a-409c-a887-df13818f8fb7", "name": "Info: hardware concurrency count is : 20\r\nInfo: thread count is : 20\r\n", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427679326600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e0660cf-3131-49a1-978a-f43450109d2e", "name": "Info: thread pool is started\r\n", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427679601000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b0b20d8-e9d7-4875-a8af-92421dab20b9", "name": "Info: GenericCompiler::CompileFiles\r\n", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427681876900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8a49179-f9f5-426e-8909-cc61d675ab17", "name": "Info: GenericCompiler::CompileFiles\r\n", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427686213000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e35ab1f8-491a-4700-80f9-6da860e2db16", "name": "Info: GenericCompiler::CompileFiles\r\n", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427692732400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86eed498-e274-4f5a-8b4f-2e3620b00a78", "name": "Warning: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources\\base\\media\\background.png' is defined repeatedly.\r\nWarning: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources\\base\\media\\foreground.png' is defined repeatedly.\r\n", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427692986400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "100224aa-56ac-4338-9f28-283840d6fea0", "name": "Warning: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources\\base\\media\\layered_image.json' is defined repeatedly.\r\n", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427700967400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ecb75ad-9634-45b4-8256-053d898f9cef", "name": "Info: GenericCompiler::CompileFiles\r\n", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427710428800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18c8781f-ee0d-41d9-9135-0d97d2d8655b", "name": "Info: scale icon is not enable.\r\n", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427715296500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9eb2ff8b-b842-406a-8921-9b56021aeada", "name": "Warning: The width or height of the png file referenced by the icon exceeds the limit (41 pixels)\r\nat C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\resources\\base\\media\\background.png\r\nWarning: The width or height of the png file referenced by the icon exceeds the limit (41 pixels)\r\nat C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\resources\\base\\media\\foreground.png\r\nWarning: C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\resources\\base\\media\\layered_image.json is not png format\r\n", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427715645200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a6a3920-dc33-4520-a146-3047d9725b31", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427716683100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ae26c3a-ce6f-4cde-a794-68c71bec4b87", "name": "Info: thread pool is stopped\r\n", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427717322700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8315510b-4782-43ca-9846-8bc9e3686106", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427566464700, "endTime": 149427720987900}, "additional": {"logType": "info", "children": [], "durationId": "d744c3d4-2f13-44b4-a13f-ff2cf52bad4e", "parent": "6593d66c-4b1f-4272-a340-ff8681de1969"}}, {"head": {"id": "fea02c74-ad81-478b-b34f-754e69ebd9bb", "name": "entry : default@CompileResource cost memory 11.229545593261719", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427723294600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "990dd5a1-496a-48e8-98d3-37e9f4210211", "name": "runTaskFromQueue task cost before running: 3 s 947 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427723493200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6593d66c-4b1f-4272-a340-ff8681de1969", "name": "Finished :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427436463900, "endTime": 149427723561200, "totalTime": 286968800}, "additional": {"logType": "info", "children": ["0e0d6480-4436-46ea-8c10-ece7f84c463c", "8315510b-4782-43ca-9846-8bc9e3686106"], "durationId": "63d97313-52d6-4556-aa7a-88b90dcd2856"}}, {"head": {"id": "1a10acc9-8fdf-48be-bcb3-63dece84b6bb", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427730567000, "endTime": 149427734063500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "13b02fa0-e9c1-48a3-bb43-324944b7c342", "logId": "26d88225-373e-4309-b3dd-5f12be781e92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13b02fa0-e9c1-48a3-bb43-324944b7c342", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427726458900}, "additional": {"logType": "detail", "children": [], "durationId": "1a10acc9-8fdf-48be-bcb3-63dece84b6bb"}}, {"head": {"id": "dfd00519-3459-4e67-99c4-6893633c6b50", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427727679000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10eac77d-154e-4a78-a84b-2630c9befc13", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427727787600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc172de9-0a56-4849-b1cc-b50492023ade", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427730582700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d993c991-07a5-4b95-b115-545bdff3f502", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427731264100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "602318ba-b424-4a9e-9564-4e9a8ea610ef", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427733816200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "899da04c-ae6e-4227-882f-3d44ac940845", "name": "entry : default@DoNativeStrip cost memory 0.0822296142578125", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427733981300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26d88225-373e-4309-b3dd-5f12be781e92", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427730567000, "endTime": 149427734063500}, "additional": {"logType": "info", "children": [], "durationId": "1a10acc9-8fdf-48be-bcb3-63dece84b6bb"}}, {"head": {"id": "c1fb33a9-16a1-4678-8cb8-eeafa356236f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427741373800, "endTime": 149442566366200}, "additional": {"children": ["c850748e-b323-4459-9e1c-f59e7839adc0", "44f528e3-dbfe-455f-98cf-15caf7780324"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "2e919017-c1ad-46c3-bb4a-6e23148573cc", "logId": "a19804bb-641d-4ce4-a4c1-bde9919c9caa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e919017-c1ad-46c3-bb4a-6e23148573cc", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427735980300}, "additional": {"logType": "detail", "children": [], "durationId": "c1fb33a9-16a1-4678-8cb8-eeafa356236f"}}, {"head": {"id": "0e5ae15b-5936-45b5-849c-6d9c0541619f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427736990500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d055fe-6d87-4a51-934f-94d1f977dc2a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427737088800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd88dacc-7914-4d68-a354-4db96049ef24", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427741386900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a70500e0-a1ed-43b0-b9f3-db485d281368", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427741811400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "732cda6f-e91d-40ee-af3b-128e19271b5c", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427756613800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7634e06f-0711-4bc4-a700-ef347afc967a", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427756759500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f658199c-9686-425d-b107-32bab7b575e7", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427775955700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6aa8d821-0fad-41ec-85fd-66f26cec27c6", "name": "default@CompileArkTS work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427778195600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c850748e-b323-4459-9e1c-f59e7839adc0", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Worker4", "startTime": 149427781395700, "endTime": 149442566100300}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "c1fb33a9-16a1-4678-8cb8-eeafa356236f", "logId": "adb61485-73c3-45c3-8e96-d86bb19caf0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bb618c9-6ea5-4871-bc95-bb908227d1ca", "name": "default@CompileArkTS work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427779271800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f60162b8-8019-4355-a24e-d48cb111615a", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427779498900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ba82a04-3a64-4b8c-82d8-6cb359f659d5", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427779603200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c156132-9f8d-407b-8a38-c3fa40be798c", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427779803500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "550ec0e7-5c13-4564-a3ec-0c45c798bfde", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427779869500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afd61301-b097-43fb-9d97-ca14dd3063f7", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427779921700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5be2d112-76dc-45c0-83a8-1dec9878bddf", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427779951000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "360ab7d6-619b-4b4e-860d-2be15c0a56a5", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427779977800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d474393b-e0e7-47ae-b515-d591bdfce8e5", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427780004900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b214b3e-4082-468f-85e8-5b4d1e279cd7", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427780030200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90008125-0a42-45d5-a896-8ddf1fae1fe6", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427780055800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6733305-2aa0-4cbb-a251-38a33d9c6acf", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427780078600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aad8f37-5058-4d19-bd12-f0c3d9d15a24", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427780103500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec113d58-ba57-4ba7-8d1d-b6e755e8592d", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427780127700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "331869b6-1485-40ab-9911-78b3d06c343b", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427780150600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd8a7f9a-72a6-4596-a8a3-6313ac29efc1", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427780172900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7e15a57-22ab-46ff-8762-af5f92f4cf47", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427780301500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78f55014-36eb-48b4-a241-0f1726216ff3", "name": "default@CompileArkTS work[0] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427781424500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99e51e53-143b-4e35-8d3a-a07439bf9b4a", "name": "default@CompileArkTS work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427781551900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b54987d-07c9-4609-89f0-3f39cfb7d083", "name": "CopyResources startTime: 149427781658600", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427781661900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b19b67b-81bc-41e4-b8c7-0400ecd3c758", "name": "default@CompileArkTS work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427781723500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44f528e3-dbfe-455f-98cf-15caf7780324", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Worker18", "startTime": 149428884531100, "endTime": 149428900915000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "c1fb33a9-16a1-4678-8cb8-eeafa356236f", "logId": "ace6bd5d-4c99-40fc-b6fd-b618a8db9e8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03464e3f-c171-4550-8074-2dd0c0fb53fc", "name": "default@CompileArkTS work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427782783600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4385c0a8-9982-4434-9b71-d4ff3a9f106f", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427782870600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "584445b9-9a76-4291-b33e-36ac8bea73da", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427782922100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "067280f0-2c1a-477b-b402-4c4eb2935f01", "name": "default@CompileArkTS work[1] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427783605500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0845024d-5262-4af7-8de4-42d6e9cc4067", "name": "default@CompileArkTS work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427783690700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2687b46-cac0-4b6c-af50-164cb0c8508f", "name": "entry : default@CompileArkTS cost memory 2.2417984008789062", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427783803400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1123b4a-de84-459d-92dc-f939859f8087", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427789774900, "endTime": 149427799362800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "534ea664-82d4-4a47-b43b-702644737f02", "logId": "940c236d-3ba7-49e7-bc8c-57c719be1e81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "534ea664-82d4-4a47-b43b-702644737f02", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427785222800}, "additional": {"logType": "detail", "children": [], "durationId": "a1123b4a-de84-459d-92dc-f939859f8087"}}, {"head": {"id": "8621c419-71b0-4388-8e5c-352240bfc722", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427786152400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea9707e4-ccd9-4af1-af1b-14400b440a38", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427786249100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32d3d29b-4e1a-4f2b-a9ba-ef2ab50ee4a9", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427789785500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e2050df-b6a5-4351-a311-89311dd6288f", "name": "entry : default@BuildJS cost memory -10.913421630859375", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427799110600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc78561c-cf7b-4cbc-aae0-ff723913f6fe", "name": "runTaskFromQueue task cost before running: 4 s 23 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427799286200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "940c236d-3ba7-49e7-bc8c-57c719be1e81", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427789774900, "endTime": 149427799362800, "totalTime": 9473400}, "additional": {"logType": "info", "children": [], "durationId": "a1123b4a-de84-459d-92dc-f939859f8087"}}, {"head": {"id": "37e0e859-de35-474b-ad08-ac75f81e52bd", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427804398400, "endTime": 149427807796400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4fab1928-7482-4c0c-bcc3-21d284ba235c", "logId": "af89c0a7-3e14-41a2-bac4-43cbd8eee38f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4fab1928-7482-4c0c-bcc3-21d284ba235c", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427801054900}, "additional": {"logType": "detail", "children": [], "durationId": "37e0e859-de35-474b-ad08-ac75f81e52bd"}}, {"head": {"id": "71b5de5d-9c2d-4e99-bd8d-048f438e5ac1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427802055600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d9fcd0f-aacd-4f2c-8d7c-1bfe79c8a977", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427802149100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b21fa09-b0ae-492b-ae22-c331e5e13275", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427804409500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72d0faa2-4472-4f00-8f61-af8f018d783b", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427805150800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83d0c2db-5ded-47da-b8e2-6451aebc2ba0", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427807612400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c04796b-cf92-4baf-803c-7656da2554a2", "name": "entry : default@CacheNativeLibs cost memory 0.0972137451171875", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427807728500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af89c0a7-3e14-41a2-bac4-43cbd8eee38f", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427804398400, "endTime": 149427807796400}, "additional": {"logType": "info", "children": [], "durationId": "37e0e859-de35-474b-ad08-ac75f81e52bd"}}, {"head": {"id": "8fb5cd34-c8e0-4ac7-a6df-96d05b102a07", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149428901366700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46d27c10-2edd-4a09-8fe9-f4493b7ff7a8", "name": "CopyResources is end, endTime: 149428901563700", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149428901572400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d82b6163-acd5-410b-a7dc-174ddd789ff9", "name": "default@CompileArkTS work[1] done.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149428901775200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ace6bd5d-4c99-40fc-b6fd-b618a8db9e8c", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Worker18", "startTime": 149428884531100, "endTime": 149428900915000}, "additional": {"logType": "info", "children": [], "durationId": "44f528e3-dbfe-455f-98cf-15caf7780324", "parent": "a19804bb-641d-4ce4-a4c1-bde9919c9caa"}}, {"head": {"id": "edb7fa37-e384-4656-af71-873991483687", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149428901988100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62124edb-05f0-4d50-b20e-3ba96e691a53", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442565854300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31f7b9fd-e48a-477a-bf16-0aa04046945a", "name": "default@CompileArkTS work[0] failed.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442566209500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adb61485-73c3-45c3-8e96-d86bb19caf0f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Worker4", "startTime": 149427781395700, "endTime": 149442566100300}, "additional": {"logType": "error", "children": [], "durationId": "c850748e-b323-4459-9e1c-f59e7839adc0", "parent": "a19804bb-641d-4ce4-a4c1-bde9919c9caa"}}, {"head": {"id": "a19804bb-641d-4ce4-a4c1-bde9919c9caa", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149427741373800, "endTime": 149442566366200}, "additional": {"logType": "error", "children": ["adb61485-73c3-45c3-8e96-d86bb19caf0f", "ace6bd5d-4c99-40fc-b6fd-b618a8db9e8c"], "durationId": "c1fb33a9-16a1-4678-8cb8-eeafa356236f"}}, {"head": {"id": "cb5e278c-622c-4a87-9306-6fdea3a51b52", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442566547200}, "additional": {"logType": "debug", "children": [], "durationId": "c1fb33a9-16a1-4678-8cb8-eeafa356236f"}}, {"head": {"id": "ee89df04-5936-4a43-bb7f-1987b8056301", "name": "ERROR: stacktrace = Error: ArkTS Compiler Error\r\n\u001b[33m1 WARN: \u001b[33m\u001b[33mArkTS:WARN: For details about ArkTS syntax errors, see FAQs\u001b[39m\u001b[39m\r\n\u001b[31m1 ERROR: \u001b[31m10605099 ArkTS Compiler Error\r\nError Message: It is possible to spread only arrays or classes derived from arrays into the rest parameter or array literals (arkts-no-spread) At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppDetailPage.ets:93:11\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m2 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'average_rating' does not exist on type 'AppDetailModel'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppDetailPage.ets:234:53\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m3 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'average_rating' does not exist on type 'AppDetailModel'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppDetailPage.ets:240:50\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m4 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'current_version' does not exist on type 'AppDetailModel'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppDetailPage.ets:338:30\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31m5 ERROR: \u001b[31m10505001 ArkTS Compiler Error\r\nError Message: Property 'category' does not exist on type 'AppDetailModel'. At File: C:/Users/<USER>/Documents/NexusHub-OH/NexusHub/entry/src/main/ets/pages/AppDetailPage.ets:349:30\r\n\u001b[39m\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:6 WARN:1}\u001b[39m\n    at runArkPack (C:\\Program Files\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5479)", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442567167200}, "additional": {"logType": "debug", "children": [], "durationId": "c1fb33a9-16a1-4678-8cb8-eeafa356236f"}}, {"head": {"id": "09ad0bd0-1b47-4615-b5bc-591adcf9daae", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442578952400, "endTime": 149442579211900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6b9b68fd-e051-40a9-9c63-268536a56f5a", "logId": "760b1407-8d61-4261-aa8b-32b7a28e09dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "760b1407-8d61-4261-aa8b-32b7a28e09dd", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442578952400, "endTime": 149442579211900}, "additional": {"logType": "info", "children": [], "durationId": "09ad0bd0-1b47-4615-b5bc-591adcf9daae"}}, {"head": {"id": "acd63592-67b3-481b-adf4-4dbf4f405bc3", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149423776663900, "endTime": 149442579532000}, "additional": {"time": {"year": 2025, "month": 6, "day": 17, "hour": 23, "minute": 27, "second": 52}, "completeCommand": "{\"prop\":[\"product=default\"],\"mode\":\"module\",\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.8", "category": "build", "state": "failed"}}, {"head": {"id": "0cfd3c85-9cb0-47dc-9c70-8b5ba864aab9", "name": "BUILD FAILED in 18 s 803 ms ", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442579577000}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "b3fc64d1-d7f1-4322-85ab-09a831629d21", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442579834200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6e04a84-98a2-4d46-9277-000ff549fae7", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442579938100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "101c7a78-b7c5-4ddd-80dc-033ceb971e4a", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442580377700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c82f6827-3364-429a-a7e7-b4758f908519", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442580453500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f295a9c6-6ac6-4010-8b23-f11bc0714aa4", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442580499700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f314de0-1fe6-4793-83e8-82214abb50ba", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442580531000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be240835-a1ba-4cee-81b2-97f87a66e285", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442580563400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b99447ee-4f50-4f32-90b8-db85f13689fd", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442581087200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c107ee8-5ab5-4ee3-9258-f50e570462da", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442581273700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "213acd1c-5aa9-4624-8dbb-8966f18b9155", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442581324200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dc3e02f-e6d7-4baa-aa42-3dc10001c259", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442581355000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b96977ea-e6be-4baf-8bf6-cc802ff024b3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442581399300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5391032-d38c-49ca-a330-9c47f209f130", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442581429300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "842cbb70-77d2-41bf-a023-05897d80737a", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442582383300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f380e73b-7dd0-4ec1-b498-7e4df7863636", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442582698500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a0ff645-9f32-4fed-b0bb-30f3191a53b7", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442582936100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "403fdd68-e532-4cf2-be61-b98563811e7f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442583002500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6e8aeca-98f1-4e64-bda9-8a566b0086d4", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442583090300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ecf4e42-e1e7-4a6b-805c-7328b2777334", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442583125600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dabc8143-f09d-4799-98e2-faec77dd0927", "name": "Update task entry:default@CompileResource input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442583925500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0622b1c5-23b2-41de-9767-c8249f4f7bc7", "name": "Update task entry:default@CompileResource input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\AppScope\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442583995100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46c8f05f-8414-4280-9158-34fecca9389f", "name": "Update task entry:default@CompileResource input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442586546700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd007253-9593-487f-b20e-7b6e90861acf", "name": "Update task entry:default@CompileResource input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442586841900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a561472-7612-4222-8cbf-634011e04ca4", "name": "Update task entry:default@CompileResource output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442587100200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "999e3c32-f2d5-4d3e-a813-46c264e8cd33", "name": "Update task entry:default@CompileResource output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h cache.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442596162300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8c868e4-5765-40af-ae63-45927bb9e1ed", "name": "Update task entry:default@CompileResource output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442596583300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dcc14c9-de64-494e-bf18-9b9d3e5a0956", "name": "Incremental task entry:default@CompileResource post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442597132600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "152195a0-d698-4424-99b4-16174a0d9ab6", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442597298200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "098ac273-a968-491f-bd47-ff713cddb2a1", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442600846400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b65bb4f-b0dc-4ab6-84d0-f199fa6c647c", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442601571700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b71479d-d1bf-4b07-9df3-9dc1c53f97d7", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442602000500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f35b9a59-7101-4e1e-8de3-8db52d9086a7", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442602081100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbc68f11-22c8-4e61-a615-ee0facc07f28", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442602263000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0764cc95-295c-4c51-9801-aa4cc17b0636", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442602880800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2343002-1f4d-45a7-a778-d6b3edbf2b77", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442608796900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f82541fe-60a9-441a-9dfa-c865530c0b86", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442609031500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a7e9dbd-f7ee-4e30-ac8d-5a0a7606eec8", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442609357500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3cc7c3f-e572-4646-9bcb-ad92a90fde3c", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442610226500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66339481-4e5c-4129-897c-9317c8ead17b", "name": "Incremental task entry:default@CompileArkTS post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442610947700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd927f45-8c4c-4553-9c7a-2df2fafe6314", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442613150500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e15c81a2-aa59-47ab-b82b-fe449ffd0a6d", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442613974800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a107144-82c3-4c74-b092-bad0f80b992b", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442614432100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc8097cd-93c1-4f5a-a73e-65dee1355304", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442614660500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72a899f8-5c76-41ba-aa24-5c5ac6a99cec", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442614873600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "427a9275-7f98-483a-bf9a-f70517d72d49", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442615824000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63154eef-4e80-466a-8b53-a61f0b2633d5", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442616724300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27da4a5d-f1b3-4acf-a440-7ffa0036b230", "name": "Incremental task entry:default@BuildJS post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442617082300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bae6c45-ac84-449b-8ec0-0cd829dfe39f", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 14416, "tid": "Main Thread", "startTime": 149442617155000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}