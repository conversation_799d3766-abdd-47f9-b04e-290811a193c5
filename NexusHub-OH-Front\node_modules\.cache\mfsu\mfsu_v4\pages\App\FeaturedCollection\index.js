"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { useState, useRef } from "react";
import {
  Card,
  Button,
  Space,
  Input,
  Modal,
  Form,
  message,
  Popconfirm,
  Tag,
  Select,
  Switch,
  Tooltip,
  Avatar,
  Badge
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  StarOutlined,
  AppstoreOutlined,
  EyeOutlined
} from "@ant-design/icons";
import { ProTable } from "@ant-design/pro-components";
import moment from "moment";
import {
  getFeaturedCollectionList,
  createFeaturedCollection,
  updateFeaturedCollection,
  deleteFeaturedCollection
} from "@/services/featuredCollection";
import { getAppList } from "@/services/app";
const { Option } = Select;
const { TextArea } = Input;
const FeaturedCollectionManagement = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [appSelectModalVisible, setAppSelectModalVisible] = useState(false);
  const [selectedApps, setSelectedApps] = useState([]);
  const [form] = Form.useForm();
  const actionRef = useRef();
  const fetchFeaturedCollections = async (params) => {
    try {
      const response = await getFeaturedCollectionList({
        page: params.current || 1,
        page_size: params.pageSize || 10,
        ...params.keyword && { keyword: params.keyword },
        ...params.is_active !== void 0 && { is_active: params.is_active }
      });
      if (response && response.code === 200) {
        const responseData = response.data || {};
        return {
          data: responseData.list || [],
          total: responseData.total || 0,
          success: true
        };
      }
      return {
        data: [],
        total: 0,
        success: false
      };
    } catch (error) {
      message.error("\u83B7\u53D6\u7CBE\u9009\u96C6\u5217\u8868\u5931\u8D25");
      return {
        data: [],
        total: 0,
        success: false
      };
    }
  };
  const fetchApps = async (params) => {
    try {
      const response = await getAppList({
        page: params.current || 1,
        pageSize: params.pageSize || 10,
        status: "approved",
        ...params.keyword && { keyword: params.keyword }
      });
      if (response && response.code === 200) {
        return {
          data: response.data || [],
          total: response.total || 0,
          success: true
        };
      }
      return {
        data: [],
        total: 0,
        success: false
      };
    } catch (error) {
      message.error("\u83B7\u53D6\u5E94\u7528\u5217\u8868\u5931\u8D25");
      return {
        data: [],
        total: 0,
        success: false
      };
    }
  };
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      search: false
    },
    {
      title: "\u7CBE\u9009\u96C6\u540D\u79F0",
      dataIndex: "name",
      ellipsis: true,
      render: (text, record) => /* @__PURE__ */ jsxs(Space, { children: [
        /* @__PURE__ */ jsx(StarOutlined, { style: { color: "#faad14" } }),
        /* @__PURE__ */ jsx("span", { children: text })
      ] })
    },
    {
      title: "\u63CF\u8FF0",
      dataIndex: "description",
      ellipsis: true,
      search: false,
      width: 200
    },
    {
      title: "\u5E94\u7528\u6570\u91CF",
      dataIndex: "app_count",
      width: 100,
      search: false,
      render: (count) => /* @__PURE__ */ jsx(Badge, { count, style: { backgroundColor: "#52c41a" } })
    },
    {
      title: "\u72B6\u6001",
      dataIndex: "is_active",
      width: 100,
      valueType: "select",
      valueEnum: {
        true: { text: "\u542F\u7528", status: "Success" },
        false: { text: "\u7981\u7528", status: "Default" }
      },
      render: (_, record) => /* @__PURE__ */ jsx(Tag, { color: record.is_active ? "green" : "default", children: record.is_active ? "\u542F\u7528" : "\u7981\u7528" })
    },
    {
      title: "\u663E\u793A\u987A\u5E8F",
      dataIndex: "display_order",
      width: 100,
      search: false
    },
    {
      title: "\u521B\u5EFA\u65F6\u95F4",
      dataIndex: "created_at",
      width: 180,
      search: false,
      render: (text) => moment(text).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      title: "\u64CD\u4F5C",
      valueType: "option",
      width: 200,
      render: (_, record) => [
        /* @__PURE__ */ jsx(Tooltip, { title: "\u67E5\u770B\u5E94\u7528", children: /* @__PURE__ */ jsx(
          Button,
          {
            type: "link",
            size: "small",
            icon: /* @__PURE__ */ jsx(EyeOutlined, {}),
            onClick: () => handleViewApps(record)
          }
        ) }, "view"),
        /* @__PURE__ */ jsx(Tooltip, { title: "\u7F16\u8F91", children: /* @__PURE__ */ jsx(
          Button,
          {
            type: "link",
            size: "small",
            icon: /* @__PURE__ */ jsx(EditOutlined, {}),
            onClick: () => handleEdit(record)
          }
        ) }, "edit"),
        /* @__PURE__ */ jsx(
          Popconfirm,
          {
            title: "\u786E\u5B9A\u8981\u5220\u9664\u8FD9\u4E2A\u7CBE\u9009\u96C6\u5417\uFF1F",
            onConfirm: () => handleDelete(record.id),
            okText: "\u786E\u5B9A",
            cancelText: "\u53D6\u6D88",
            children: /* @__PURE__ */ jsx(Tooltip, { title: "\u5220\u9664", children: /* @__PURE__ */ jsx(
              Button,
              {
                type: "link",
                size: "small",
                danger: true,
                icon: /* @__PURE__ */ jsx(DeleteOutlined, {})
              }
            ) })
          },
          "delete"
        )
      ]
    }
  ];
  const appColumns = [
    {
      title: "\u5E94\u7528\u56FE\u6807",
      dataIndex: "icon",
      width: 80,
      render: (icon, record) => /* @__PURE__ */ jsx(
        Avatar,
        {
          src: icon,
          icon: /* @__PURE__ */ jsx(AppstoreOutlined, {}),
          alt: record.name
        }
      )
    },
    {
      title: "\u5E94\u7528\u540D\u79F0",
      dataIndex: "name",
      ellipsis: true
    },
    {
      title: "\u5305\u540D",
      dataIndex: "package",
      ellipsis: true
    },
    {
      title: "\u5206\u7C7B",
      dataIndex: "category",
      width: 120
    }
  ];
  const handleCreate = () => {
    setEditingRecord(null);
    setSelectedApps([]);
    form.resetFields();
    setModalVisible(true);
  };
  const handleEdit = (record) => {
    setEditingRecord(record);
    setSelectedApps(record.apps || []);
    form.setFieldsValue({
      ...record,
      app_ids: record.apps?.map((app) => app.id) || []
    });
    setModalVisible(true);
  };
  const handleDelete = async (id) => {
    try {
      const result = await deleteFeaturedCollection(id);
      if (result.code === 200) {
        message.success("\u5220\u9664\u6210\u529F");
        actionRef.current?.reload();
      } else {
        message.error(result.message || "\u5220\u9664\u5931\u8D25");
      }
    } catch (error) {
      message.error("\u5220\u9664\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
    }
  };
  const handleViewApps = (record) => {
    Modal.info({
      title: `${record.name} - \u5305\u542B\u7684\u5E94\u7528`,
      width: 800,
      content: /* @__PURE__ */ jsx("div", { style: { marginTop: 16 }, children: record.apps && record.apps.length > 0 ? /* @__PURE__ */ jsx("div", { style: { display: "grid", gridTemplateColumns: "repeat(auto-fill, minmax(200px, 1fr))", gap: 16 }, children: record.apps.map((app) => /* @__PURE__ */ jsx(Card, { size: "small", children: /* @__PURE__ */ jsx(
        Card.Meta,
        {
          avatar: /* @__PURE__ */ jsx(Avatar, { src: app.icon, icon: /* @__PURE__ */ jsx(AppstoreOutlined, {}) }),
          title: app.name,
          description: app.package
        }
      ) }, app.id)) }) : /* @__PURE__ */ jsx("div", { style: { textAlign: "center", color: "#999" }, children: "\u6682\u65E0\u5E94\u7528" }) })
    });
  };
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const submitData = {
        title: values.title,
        description: values.description,
        icon: values.icon || "",
        cover_image: values.cover_image || "",
        sort_order: parseInt(values.sort_order) || 0,
        is_active: values.is_active,
        is_public: values.is_public
      };
      let result;
      if (editingRecord) {
        result = await updateFeaturedCollection(editingRecord.id, submitData);
      } else {
        result = await createFeaturedCollection(submitData);
      }
      if (result.code === 200) {
        message.success(editingRecord ? "\u66F4\u65B0\u6210\u529F" : "\u521B\u5EFA\u6210\u529F");
        setModalVisible(false);
        actionRef.current?.reload();
      } else {
        message.error(result.message || "\u64CD\u4F5C\u5931\u8D25");
      }
    } catch (error) {
      message.error("\u64CD\u4F5C\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
    }
  };
  const handleAppSelect = () => {
    setAppSelectModalVisible(true);
  };
  const handleAppSelectConfirm = (apps) => {
    setSelectedApps(apps);
    form.setFieldValue("app_ids", apps.map((app) => app.id));
    setAppSelectModalVisible(false);
  };
  return /* @__PURE__ */ jsxs("div", { children: [
    /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx(
      ProTable,
      {
        headerTitle: "\u7CBE\u9009\u96C6\u7BA1\u7406",
        actionRef,
        rowKey: "id",
        search: {
          labelWidth: "auto"
        },
        toolBarRender: () => [
          /* @__PURE__ */ jsx(
            Button,
            {
              type: "primary",
              icon: /* @__PURE__ */ jsx(PlusOutlined, {}),
              onClick: handleCreate,
              children: "\u65B0\u5EFA\u7CBE\u9009\u96C6"
            },
            "create"
          )
        ],
        request: fetchFeaturedCollections,
        columns,
        pagination: {
          defaultPageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true
        }
      }
    ) }),
    /* @__PURE__ */ jsx(
      Modal,
      {
        title: editingRecord ? "\u7F16\u8F91\u7CBE\u9009\u96C6" : "\u65B0\u5EFA\u7CBE\u9009\u96C6",
        open: modalVisible,
        onOk: handleSubmit,
        onCancel: () => setModalVisible(false),
        width: 600,
        destroyOnHidden: true,
        children: /* @__PURE__ */ jsxs(
          Form,
          {
            form,
            layout: "vertical",
            initialValues: {
              is_active: true,
              is_public: true,
              sort_order: 0
            },
            children: [
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "title",
                  label: "\u7CBE\u9009\u96C6\u540D\u79F0",
                  rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u7CBE\u9009\u96C6\u540D\u79F0" }],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u7CBE\u9009\u96C6\u540D\u79F0" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "description",
                  label: "\u63CF\u8FF0",
                  rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u63CF\u8FF0" }],
                  children: /* @__PURE__ */ jsx(TextArea, { rows: 3, placeholder: "\u8BF7\u8F93\u5165\u7CBE\u9009\u96C6\u63CF\u8FF0" })
                }
              ),
              /* @__PURE__ */ jsx(Form.Item, { label: "\u9009\u62E9\u5E94\u7528", children: /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs(
                  Button,
                  {
                    type: "dashed",
                    icon: /* @__PURE__ */ jsx(PlusOutlined, {}),
                    onClick: handleAppSelect,
                    style: { marginBottom: 8 },
                    children: [
                      "\u9009\u62E9\u5E94\u7528 (",
                      selectedApps.length,
                      ")"
                    ]
                  }
                ),
                selectedApps.length > 0 && /* @__PURE__ */ jsx("div", { style: { display: "flex", flexWrap: "wrap", gap: 8 }, children: selectedApps.map((app) => /* @__PURE__ */ jsxs(
                  Tag,
                  {
                    closable: true,
                    onClose: () => {
                      const newApps = selectedApps.filter((a) => a.id !== app.id);
                      setSelectedApps(newApps);
                      form.setFieldValue("app_ids", newApps.map((a) => a.id));
                    },
                    children: [
                      /* @__PURE__ */ jsx(Avatar, { size: "small", src: app.icon, style: { marginRight: 4 } }),
                      app.name
                    ]
                  },
                  app.id
                )) })
              ] }) }),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "sort_order",
                  label: "\u663E\u793A\u987A\u5E8F",
                  rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u663E\u793A\u987A\u5E8F" }],
                  children: /* @__PURE__ */ jsx(Input, { type: "number", placeholder: "\u6570\u5B57\u8D8A\u5C0F\u8D8A\u9760\u524D" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "is_active",
                  label: "\u72B6\u6001",
                  valuePropName: "checked",
                  children: /* @__PURE__ */ jsx(Switch, { checkedChildren: "\u542F\u7528", unCheckedChildren: "\u7981\u7528" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "is_public",
                  label: "\u516C\u5F00\u72B6\u6001",
                  valuePropName: "checked",
                  children: /* @__PURE__ */ jsx(Switch, { checkedChildren: "\u516C\u5F00", unCheckedChildren: "\u79C1\u6709" })
                }
              )
            ]
          }
        )
      }
    ),
    /* @__PURE__ */ jsx(
      Modal,
      {
        title: "\u9009\u62E9\u5E94\u7528",
        open: appSelectModalVisible,
        onCancel: () => setAppSelectModalVisible(false),
        width: 800,
        footer: [
          /* @__PURE__ */ jsx(Button, { onClick: () => setAppSelectModalVisible(false), children: "\u53D6\u6D88" }, "cancel"),
          /* @__PURE__ */ jsx(
            Button,
            {
              type: "primary",
              onClick: () => {
                setAppSelectModalVisible(false);
              },
              children: "\u786E\u5B9A"
            },
            "confirm"
          )
        ],
        children: /* @__PURE__ */ jsx(
          ProTable,
          {
            rowKey: "id",
            search: {
              labelWidth: "auto"
            },
            request: fetchApps,
            columns: [
              {
                title: "\u5E94\u7528\u56FE\u6807",
                dataIndex: "icon",
                width: 80,
                search: false,
                render: (icon, record) => /* @__PURE__ */ jsx(
                  Avatar,
                  {
                    src: icon,
                    icon: /* @__PURE__ */ jsx(AppstoreOutlined, {}),
                    alt: record.name
                  }
                )
              },
              {
                title: "\u5E94\u7528\u540D\u79F0",
                dataIndex: "name",
                ellipsis: true
              },
              {
                title: "\u5305\u540D",
                dataIndex: "package",
                ellipsis: true,
                search: false
              },
              {
                title: "\u5206\u7C7B",
                dataIndex: "category",
                width: 120,
                search: false
              }
            ],
            rowSelection: {
              selectedRowKeys: selectedApps.map((app) => app.id),
              onChange: (selectedRowKeys, selectedRows) => {
                setSelectedApps(selectedRows);
              }
            },
            pagination: {
              defaultPageSize: 5,
              showSizeChanger: false
            }
          }
        )
      }
    )
  ] });
};
export default FeaturedCollectionManagement;
