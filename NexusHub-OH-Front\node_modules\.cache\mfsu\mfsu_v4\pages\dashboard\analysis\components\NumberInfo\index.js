"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { CaretDownOutlined, CaretUpOutlined } from "@ant-design/icons";
import classNames from "classnames";
import useStyles from "./index.style";
const NumberInfo = ({
  theme,
  title,
  subTitle,
  total,
  subTotal,
  status,
  suffix,
  gap,
  ...rest
}) => {
  const { styles } = useStyles();
  return /* @__PURE__ */ jsxs(
    "div",
    {
      className: classNames(styles.numberInfo, {
        [styles[`numberInfo${theme}`]]: theme
      }),
      ...rest,
      children: [
        title && /* @__PURE__ */ jsx("div", { className: styles.numberInfoTitle, title: typeof title === "string" ? title : "", children: title }),
        subTitle && /* @__PURE__ */ jsx(
          "div",
          {
            className: styles.numberInfoSubTitle,
            title: typeof subTitle === "string" ? subTitle : "",
            children: subTitle
          }
        ),
        /* @__PURE__ */ jsxs(
          "div",
          {
            className: styles.numberInfoValue,
            style: gap ? {
              marginTop: gap
            } : {},
            children: [
              /* @__PURE__ */ jsxs("span", { children: [
                total,
                suffix && /* @__PURE__ */ jsx("em", { className: styles.suffix, children: suffix })
              ] }),
              (status || subTotal) && /* @__PURE__ */ jsxs("span", { className: styles.subTotal, children: [
                subTotal,
                status && status === "up" ? /* @__PURE__ */ jsx(CaretUpOutlined, {}) : /* @__PURE__ */ jsx(CaretDownOutlined, {})
              ] })
            ]
          }
        )
      ]
    }
  );
};
export default NumberInfo;
