package models

import (
	"gorm.io/gorm"
)

// AutoMigrate 自动迁移数据库
func AutoMigrate(db *gorm.DB) error {
	// 迁移基础表
	if err := db.AutoMigrate(
		&User{},
		&Application{},
		&AppVersion{},
		&AppScreenshot{}, // 添加应用截图表
		&Review{},
		&ReviewLike{},
		&DownloadRecord{},
		&Category{},
		&Tag{},
		&AppTag{},
		&FeaturedCollection{},    // 添加精选集表
		&FeaturedCollectionApp{}, // 添加精选集应用关联表
		&OpenHarmonyVersion{},    // 添加OpenHarmonyOS版本表
		&LoginRecord{},           // 添加登录记录表
		&GeographicData{},        // 添加地理位置数据表
	); err != nil {
		return err
	}

	// 迁移仪表盘相关表
	if err := db.AutoMigrate(
		&TaskItem{},
		&RecentActivity{},
		&SystemLog{},
		&AlertEvent{},
	); err != nil {
		return err
	}

	// 迁移通知相关表
	if err := db.AutoMigrate(
		&Notification{},
		&NotificationSettings{},
	); err != nil {
		return err
	}

	return nil
}

// InitCategories 初始化分类数据
func InitCategories(db *gorm.DB) error {
	// 检查是否已有分类
	var count int64
	if err := db.Model(&Category{}).Count(&count).Error; err != nil {
		return err
	}

	// 如果没有分类数据，则添加默认分类
	if count == 0 {
		// 辅助函数，用于创建uint指针
		uintPtr := func(i uint) *uint {
			return &i
		}

		categories := []Category{
			{Name: "应用", Icon: "app-icon.png", Description: "各类实用应用", ParentID: nil},
			{Name: "游戏", Icon: "game-icon.png", Description: "各类游戏", ParentID: nil},
			{Name: "工具", Icon: "tool-icon.png", Description: "实用工具类应用", ParentID: uintPtr(1)},
			{Name: "社交", Icon: "social-icon.png", Description: "社交类应用", ParentID: uintPtr(1)},
			{Name: "办公", Icon: "office-icon.png", Description: "办公类应用", ParentID: uintPtr(1)},
			{Name: "教育", Icon: "edu-icon.png", Description: "教育类应用", ParentID: uintPtr(1)},
			{Name: "休闲", Icon: "casual-icon.png", Description: "休闲游戏", ParentID: uintPtr(2)},
			{Name: "动作", Icon: "action-icon.png", Description: "动作游戏", ParentID: uintPtr(2)},
			{Name: "策略", Icon: "strategy-icon.png", Description: "策略游戏", ParentID: uintPtr(2)},
			{Name: "角色扮演", Icon: "rpg-icon.png", Description: "角色扮演游戏", ParentID: uintPtr(2)},
		}

		return db.CreateInBatches(categories, len(categories)).Error
	}

	return nil
}

// InitTags 初始化标签数据
func InitTags(db *gorm.DB) error {
	// 检查是否已有标签
	var count int64
	if err := db.Model(&Tag{}).Count(&count).Error; err != nil {
		return err
	}

	// 如果没有标签数据，则添加默认标签
	if count == 0 {
		tags := []Tag{
			{Name: "免费", Color: "#4CAF50", Description: "免费应用"},
			{Name: "热门", Color: "#F44336", Description: "热门应用"},
			{Name: "新上架", Color: "#2196F3", Description: "新上架应用"},
			{Name: "推荐", Color: "#FF9800", Description: "编辑推荐"},
			{Name: "限时免费", Color: "#9C27B0", Description: "限时免费应用"},
			{Name: "内购", Color: "#607D8B", Description: "包含内购的应用"},
			{Name: "无广告", Color: "#00BCD4", Description: "无广告应用"},
			{Name: "多人游戏", Color: "#E91E63", Description: "支持多人游戏"},
			{Name: "单机游戏", Color: "#795548", Description: "单机游戏"},
			{Name: "高清画质", Color: "#3F51B5", Description: "高清画质游戏"},
		}

		return db.CreateInBatches(tags, len(tags)).Error
	}

	return nil
}
