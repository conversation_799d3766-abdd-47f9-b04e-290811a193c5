"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { <PERSON><PERSON><PERSON>, Li<PERSON>, WordCloud } from "@ant-design/plots";
import { GridContent } from "@ant-design/pro-components";
import { Card, Col, Row, Statistic, Table } from "antd";
import { useEffect, useState } from "react";
import ActiveChart from "./components/ActiveChart";
import Map from "./components/Map";
import { getMonitoringData, getMonitoringLogs, getMonitoringAlerts } from "./service";
import useStyles from "./style.style";
const { Countdown } = Statistic;
const deadline = Date.now() + 1e3 * 60 * 60 * 24 * 2 + 1e3 * 30;
const Monitor = () => {
  const { styles } = useStyles();
  const [monitoringData, setMonitoringData] = useState({});
  const [logs, setLogs] = useState([]);
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [dataResult, logsResult, alertsResult] = await Promise.all([
          getMonitoringData(),
          getMonitoringLogs(),
          getMonitoringAlerts()
        ]);
        setMonitoringData(dataResult.data || {});
        setLogs(logsResult.data || []);
        setAlerts(alertsResult.data || []);
      } catch (error) {
        console.error("\u83B7\u53D6\u76D1\u63A7\u6570\u636E\u5931\u8D25:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);
  const wordCloudData = Object.entries(monitoringData.tags || {}).map(([key, value]) => ({
    id: key,
    word: key,
    weight: value
  }));
  const logColumns = [
    {
      title: "\u65F6\u95F4",
      dataIndex: "timestamp",
      key: "timestamp"
    },
    {
      title: "\u7EA7\u522B",
      dataIndex: "level",
      key: "level"
    },
    {
      title: "\u6D88\u606F",
      dataIndex: "message",
      key: "message"
    }
  ];
  return /* @__PURE__ */ jsx(GridContent, { children: /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsxs(Row, { gutter: 24, children: [
      /* @__PURE__ */ jsx(
        Col,
        {
          xl: 18,
          lg: 24,
          md: 24,
          sm: 24,
          xs: 24,
          style: {
            marginBottom: 24
          },
          children: /* @__PURE__ */ jsxs(Card, { title: "\u7CFB\u7EDF\u8D44\u6E90\u76D1\u63A7", bordered: false, loading, children: [
            /* @__PURE__ */ jsxs(Row, { children: [
              /* @__PURE__ */ jsx(Col, { md: 6, sm: 12, xs: 24, children: /* @__PURE__ */ jsx(
                Statistic,
                {
                  title: "CPU \u4F7F\u7528\u7387",
                  suffix: "%",
                  value: monitoringData.cpu_usage || 0,
                  precision: 2
                }
              ) }),
              /* @__PURE__ */ jsx(Col, { md: 6, sm: 12, xs: 24, children: /* @__PURE__ */ jsx(
                Statistic,
                {
                  title: "\u5185\u5B58\u4F7F\u7528\u7387",
                  suffix: "%",
                  value: monitoringData.memory_usage || 0,
                  precision: 2
                }
              ) }),
              /* @__PURE__ */ jsx(Col, { md: 6, sm: 12, xs: 24, children: /* @__PURE__ */ jsx(
                Statistic,
                {
                  title: "\u78C1\u76D8\u4F7F\u7528\u7387",
                  suffix: "%",
                  value: monitoringData.disk_usage || 0,
                  precision: 2
                }
              ) }),
              /* @__PURE__ */ jsx(Col, { md: 6, sm: 12, xs: 24, children: /* @__PURE__ */ jsx(
                Statistic,
                {
                  title: "\u7F51\u7EDC\u5E26\u5BBD",
                  suffix: "Mbps",
                  value: monitoringData.network_bandwidth || 0,
                  precision: 2
                }
              ) })
            ] }),
            /* @__PURE__ */ jsx("div", { className: styles.mapChart, children: /* @__PURE__ */ jsx(Map, {}) })
          ] })
        }
      ),
      /* @__PURE__ */ jsxs(Col, { xl: 6, lg: 24, md: 24, sm: 24, xs: 24, children: [
        /* @__PURE__ */ jsx(
          Card,
          {
            title: "\u7CFB\u7EDF\u5065\u5EB7\u72B6\u6001",
            style: {
              marginBottom: 24
            },
            bordered: false,
            loading,
            children: /* @__PURE__ */ jsx(ActiveChart, {})
          }
        ),
        /* @__PURE__ */ jsx(
          Card,
          {
            title: "\u7CFB\u7EDF\u54CD\u5E94\u65F6\u95F4",
            style: {
              marginBottom: 24
            },
            bodyStyle: {
              textAlign: "center"
            },
            bordered: false,
            loading,
            children: /* @__PURE__ */ jsx(
              Gauge,
              {
                height: 180,
                data: {
                  target: 100,
                  total: 200,
                  name: "\u54CD\u5E94\u65F6\u95F4",
                  value: monitoringData.response_time || 0,
                  thresholds: [20, 50, 100, 150, 200]
                },
                padding: -16,
                meta: {
                  color: {
                    range: ["#30BF78", "#FAAD14", "#F4664A", "#FF0000"]
                  }
                }
              }
            )
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ jsxs(Row, { gutter: 24, children: [
      /* @__PURE__ */ jsx(
        Col,
        {
          xl: 12,
          lg: 24,
          sm: 24,
          xs: 24,
          style: {
            marginBottom: 24
          },
          children: /* @__PURE__ */ jsx(Card, { title: "\u7CFB\u7EDF\u65E5\u5FD7", bordered: false, loading, children: /* @__PURE__ */ jsx(
            Table,
            {
              dataSource: logs,
              columns: logColumns,
              pagination: { pageSize: 5 },
              size: "small"
            }
          ) })
        }
      ),
      /* @__PURE__ */ jsx(
        Col,
        {
          xl: 6,
          lg: 12,
          sm: 24,
          xs: 24,
          style: {
            marginBottom: 24
          },
          children: /* @__PURE__ */ jsx(
            Card,
            {
              title: "\u544A\u8B66\u4E8B\u4EF6",
              loading,
              bordered: false,
              bodyStyle: {
                overflow: "hidden"
              },
              children: /* @__PURE__ */ jsx(
                WordCloud,
                {
                  data: wordCloudData,
                  height: 162,
                  textField: "word",
                  weightField: "weight",
                  colorField: "word",
                  layout: { spiral: "rectangular", fontSize: [10, 20] }
                }
              )
            }
          )
        }
      ),
      /* @__PURE__ */ jsx(
        Col,
        {
          xl: 6,
          lg: 12,
          sm: 24,
          xs: 24,
          style: {
            marginBottom: 24
          },
          children: /* @__PURE__ */ jsx(
            Card,
            {
              title: "\u8D44\u6E90\u5269\u4F59",
              bodyStyle: {
                textAlign: "center",
                fontSize: 0
              },
              bordered: false,
              loading,
              children: /* @__PURE__ */ jsx(
                Liquid,
                {
                  height: 160,
                  percent: (100 - (monitoringData.disk_usage || 0)) / 100
                }
              )
            }
          )
        }
      )
    ] })
  ] }) });
};
export default Monitor;
