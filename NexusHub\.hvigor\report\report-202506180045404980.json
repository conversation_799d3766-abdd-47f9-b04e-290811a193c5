{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "70099798-796e-43a3-bfcb-36452d1873b8", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433814514000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ac04e66-ffed-4708-ab80-984f8547d949", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433814750000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "152cbc70-5194-4237-b5ef-d2c6f6a5d425", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433871119200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f68799d-4bff-4989-9455-ca5c6f8f9238", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433871426300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eac74096-bd43-4a2c-bbb0-9d5fd2b2dccf", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433873338700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84a83644-56ce-4fa5-9d45-0b8dd65c6a05", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153433873702500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59c054ed-1c6e-4d10-86cc-06b6d4945a7e", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110715012900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03caba92-186b-4b34-83e0-209aa5041357", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110721276500, "endTime": 154110907631000}, "additional": {"children": ["bf417ab1-f8c0-4621-b15b-fb1c87c290dc", "c33df5e2-9594-41bd-b174-43538eb9382d", "b4747b4a-a673-442a-9891-81c9a6642e53", "aa626fcb-812c-42e8-89d7-e67a83abbcfd", "49367773-7dec-4d8f-8d66-2cd374833c45", "30b855fa-de0c-4b4e-a334-beaeaee3b011", "2c778109-cfc2-409b-9ebe-0ea911faea27"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "b5804a12-bc03-4a9a-86fb-f555a5c4b787"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf417ab1-f8c0-4621-b15b-fb1c87c290dc", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110721281800, "endTime": 154110734149600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03caba92-186b-4b34-83e0-209aa5041357", "logId": "3a091684-17ef-4a36-bfd9-d9c72aa6a693"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c33df5e2-9594-41bd-b174-43538eb9382d", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110734167500, "endTime": 154110906291200}, "additional": {"children": ["859e92a1-aff2-4ece-819f-79a9eab5d6b8", "039e7719-a541-4c7d-b56e-f1eee256b750", "d4207fe3-4647-444e-b90a-06ef2033a034", "9c58407a-a456-4557-88a3-f0030f51de84", "c7a50cab-a8be-4e14-9c01-ec692dba01a3", "cb42b138-745d-42de-8ce6-280f183c9a07", "f9ec63df-e558-4c8f-bd22-af2354e4d7a7", "32fad83f-e2fe-49ab-a42b-c51b5dbcd664", "843a2f7d-f7e0-4cb8-9c7a-3002b12b2fc3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03caba92-186b-4b34-83e0-209aa5041357", "logId": "e488a992-27f4-4e29-98d1-5d0ea1ae46e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4747b4a-a673-442a-9891-81c9a6642e53", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110906320200, "endTime": 154110907591100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03caba92-186b-4b34-83e0-209aa5041357", "logId": "c335776b-0288-4099-94e3-3a3341268d2a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa626fcb-812c-42e8-89d7-e67a83abbcfd", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110907596400, "endTime": 154110907627300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03caba92-186b-4b34-83e0-209aa5041357", "logId": "803ee726-fd55-4722-9a7a-a3a964fd8250"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49367773-7dec-4d8f-8d66-2cd374833c45", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110724388000, "endTime": 154110724445100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03caba92-186b-4b34-83e0-209aa5041357", "logId": "88326587-b03e-493f-86a1-d6f9176244fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88326587-b03e-493f-86a1-d6f9176244fa", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110724388000, "endTime": 154110724445100}, "additional": {"logType": "info", "children": [], "durationId": "49367773-7dec-4d8f-8d66-2cd374833c45", "parent": "b5804a12-bc03-4a9a-86fb-f555a5c4b787"}}, {"head": {"id": "30b855fa-de0c-4b4e-a334-beaeaee3b011", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110728962500, "endTime": 154110728981000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03caba92-186b-4b34-83e0-209aa5041357", "logId": "9f1a1fb8-ec89-476b-aa31-01feb1902003"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f1a1fb8-ec89-476b-aa31-01feb1902003", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110728962500, "endTime": 154110728981000}, "additional": {"logType": "info", "children": [], "durationId": "30b855fa-de0c-4b4e-a334-beaeaee3b011", "parent": "b5804a12-bc03-4a9a-86fb-f555a5c4b787"}}, {"head": {"id": "4d731ab8-e218-4e26-ba64-4ab3b84acc92", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110729066200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9eb94cd1-9f4a-44bb-942b-6b203de41a21", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110734027000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a091684-17ef-4a36-bfd9-d9c72aa6a693", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110721281800, "endTime": 154110734149600}, "additional": {"logType": "info", "children": [], "durationId": "bf417ab1-f8c0-4621-b15b-fb1c87c290dc", "parent": "b5804a12-bc03-4a9a-86fb-f555a5c4b787"}}, {"head": {"id": "859e92a1-aff2-4ece-819f-79a9eab5d6b8", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110738556600, "endTime": 154110738571800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33df5e2-9594-41bd-b174-43538eb9382d", "logId": "d728da39-f2b8-421c-afe1-67b27228be72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "039e7719-a541-4c7d-b56e-f1eee256b750", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110738586300, "endTime": 154110742981200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33df5e2-9594-41bd-b174-43538eb9382d", "logId": "58cebfbd-6b6b-4e4c-a50f-56d90f6a6f5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4207fe3-4647-444e-b90a-06ef2033a034", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110742996900, "endTime": 154110811708400}, "additional": {"children": ["2b94c001-7982-4e47-a7fe-b4ba26095eaf", "e0e63eb5-efc4-4e19-b097-652739d36b6b", "36f0b891-f0c0-4b01-b07b-220d16802703"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33df5e2-9594-41bd-b174-43538eb9382d", "logId": "fa96e4c2-a25f-4fe8-a134-640b2df9a51d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c58407a-a456-4557-88a3-f0030f51de84", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110811720700, "endTime": 154110831053500}, "additional": {"children": ["b7dbf1b7-2144-4d3f-9658-1ded31b6d528"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33df5e2-9594-41bd-b174-43538eb9382d", "logId": "b7f43dcf-230e-47b0-877f-4c63ce21cb7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7a50cab-a8be-4e14-9c01-ec692dba01a3", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110831076400, "endTime": 154110874286600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33df5e2-9594-41bd-b174-43538eb9382d", "logId": "9c4a245d-2d5d-4563-b8b7-72fe3859c2de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb42b138-745d-42de-8ce6-280f183c9a07", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110875428000, "endTime": 154110891314300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33df5e2-9594-41bd-b174-43538eb9382d", "logId": "8c55f539-ebf3-4299-b217-a7555be4f7d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9ec63df-e558-4c8f-bd22-af2354e4d7a7", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110891336500, "endTime": 154110906152000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33df5e2-9594-41bd-b174-43538eb9382d", "logId": "f4dab887-9f3c-4c3e-86ae-705a3d436193"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32fad83f-e2fe-49ab-a42b-c51b5dbcd664", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110906169900, "endTime": 154110906281400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33df5e2-9594-41bd-b174-43538eb9382d", "logId": "26d8f376-d6db-474b-93c9-912a4a09cb4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d728da39-f2b8-421c-afe1-67b27228be72", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110738556600, "endTime": 154110738571800}, "additional": {"logType": "info", "children": [], "durationId": "859e92a1-aff2-4ece-819f-79a9eab5d6b8", "parent": "e488a992-27f4-4e29-98d1-5d0ea1ae46e3"}}, {"head": {"id": "58cebfbd-6b6b-4e4c-a50f-56d90f6a6f5e", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110738586300, "endTime": 154110742981200}, "additional": {"logType": "info", "children": [], "durationId": "039e7719-a541-4c7d-b56e-f1eee256b750", "parent": "e488a992-27f4-4e29-98d1-5d0ea1ae46e3"}}, {"head": {"id": "2b94c001-7982-4e47-a7fe-b4ba26095eaf", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110743501600, "endTime": 154110743520500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d4207fe3-4647-444e-b90a-06ef2033a034", "logId": "ed82e597-1bb3-407e-8eb8-2a8dcfbc21a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed82e597-1bb3-407e-8eb8-2a8dcfbc21a7", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110743501600, "endTime": 154110743520500}, "additional": {"logType": "info", "children": [], "durationId": "2b94c001-7982-4e47-a7fe-b4ba26095eaf", "parent": "fa96e4c2-a25f-4fe8-a134-640b2df9a51d"}}, {"head": {"id": "e0e63eb5-efc4-4e19-b097-652739d36b6b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110745125600, "endTime": 154110811091200}, "additional": {"children": ["b66150dc-f655-4b99-b606-b4564dc182e7", "5f9425e9-1bc2-4e30-9593-223503ff7f73"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d4207fe3-4647-444e-b90a-06ef2033a034", "logId": "ef4b8c47-6f13-4081-8b86-792fc8b820bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b66150dc-f655-4b99-b606-b4564dc182e7", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110745126400, "endTime": 154110750620000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0e63eb5-efc4-4e19-b097-652739d36b6b", "logId": "8e079ead-f2d7-4ea0-8e9d-8b7139b55708"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f9425e9-1bc2-4e30-9593-223503ff7f73", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110750632000, "endTime": 154110811079800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0e63eb5-efc4-4e19-b097-652739d36b6b", "logId": "03ea3557-e44a-4ff1-8b25-249d55eee3af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e3e77db-9474-411e-97c2-f51128c44587", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110745129400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2b7ab9b-57c7-4bf1-be97-555d01304012", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110750506600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e079ead-f2d7-4ea0-8e9d-8b7139b55708", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110745126400, "endTime": 154110750620000}, "additional": {"logType": "info", "children": [], "durationId": "b66150dc-f655-4b99-b606-b4564dc182e7", "parent": "ef4b8c47-6f13-4081-8b86-792fc8b820bd"}}, {"head": {"id": "8fc5afdb-33a1-4dbd-b723-b1e90d8c38b4", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110750645900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21c798f9-f7db-463b-84dd-393b46ac7695", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110756731100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7579553-1717-4ed6-ad09-25698b7b99cd", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110756888900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7edee78b-5b0c-4f8b-8fa8-99ce00916ffd", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110757060500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14b333fb-d052-4220-91f9-fc67f6056e1c", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110757915400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa3a3f3c-61c3-4a93-9731-1be2e89e074b", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110759567000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79878f8c-2b08-42bf-aaf1-698e57c357fe", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110770735000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82de0045-a507-4164-a4df-28cb4514b970", "name": "Sdk init in 27 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110790474000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "889f88c2-1cbf-4619-bf7e-b7f5cd25b52c", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110790653100}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 45, "second": 40}, "markType": "other"}}, {"head": {"id": "a1d7edfe-c178-4e84-b195-70d74741ebbc", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110790705900}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 45, "second": 40}, "markType": "other"}}, {"head": {"id": "463045e3-790d-4117-8071-45b932299d8d", "name": "Project task initialization takes 19 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110810839000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dab63ca9-82d9-401c-9641-6c2385e0f142", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110810957500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d896a468-9e57-49ba-a3fb-0e0300e936da", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110811011900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65c02b25-89b1-4b17-9c7a-724e21c9ab86", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110811045300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03ea3557-e44a-4ff1-8b25-249d55eee3af", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110750632000, "endTime": 154110811079800}, "additional": {"logType": "info", "children": [], "durationId": "5f9425e9-1bc2-4e30-9593-223503ff7f73", "parent": "ef4b8c47-6f13-4081-8b86-792fc8b820bd"}}, {"head": {"id": "ef4b8c47-6f13-4081-8b86-792fc8b820bd", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110745125600, "endTime": 154110811091200}, "additional": {"logType": "info", "children": ["8e079ead-f2d7-4ea0-8e9d-8b7139b55708", "03ea3557-e44a-4ff1-8b25-249d55eee3af"], "durationId": "e0e63eb5-efc4-4e19-b097-652739d36b6b", "parent": "fa96e4c2-a25f-4fe8-a134-640b2df9a51d"}}, {"head": {"id": "36f0b891-f0c0-4b01-b07b-220d16802703", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110811681500, "endTime": 154110811696100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d4207fe3-4647-444e-b90a-06ef2033a034", "logId": "1d95831e-5aab-4786-bfcc-aec4813253bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d95831e-5aab-4786-bfcc-aec4813253bf", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110811681500, "endTime": 154110811696100}, "additional": {"logType": "info", "children": [], "durationId": "36f0b891-f0c0-4b01-b07b-220d16802703", "parent": "fa96e4c2-a25f-4fe8-a134-640b2df9a51d"}}, {"head": {"id": "fa96e4c2-a25f-4fe8-a134-640b2df9a51d", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110742996900, "endTime": 154110811708400}, "additional": {"logType": "info", "children": ["ed82e597-1bb3-407e-8eb8-2a8dcfbc21a7", "ef4b8c47-6f13-4081-8b86-792fc8b820bd", "1d95831e-5aab-4786-bfcc-aec4813253bf"], "durationId": "d4207fe3-4647-444e-b90a-06ef2033a034", "parent": "e488a992-27f4-4e29-98d1-5d0ea1ae46e3"}}, {"head": {"id": "b7dbf1b7-2144-4d3f-9658-1ded31b6d528", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110812359900, "endTime": 154110831041100}, "additional": {"children": ["59c8434b-ec94-4f45-999b-76d2a3b878b3", "540023fb-73b5-4a34-9694-a75bfdcda936", "3a73bc65-13d0-46cc-96be-edfca667a954"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c58407a-a456-4557-88a3-f0030f51de84", "logId": "b9e76ebe-f021-4001-a9ef-5ab77624fb99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59c8434b-ec94-4f45-999b-76d2a3b878b3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110815252200, "endTime": 154110815268100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b7dbf1b7-2144-4d3f-9658-1ded31b6d528", "logId": "9550f7f7-9f0a-4f0a-a6a6-3759c4d50a49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9550f7f7-9f0a-4f0a-a6a6-3759c4d50a49", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110815252200, "endTime": 154110815268100}, "additional": {"logType": "info", "children": [], "durationId": "59c8434b-ec94-4f45-999b-76d2a3b878b3", "parent": "b9e76ebe-f021-4001-a9ef-5ab77624fb99"}}, {"head": {"id": "540023fb-73b5-4a34-9694-a75bfdcda936", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110816682400, "endTime": 154110829840700}, "additional": {"children": ["a0ceddb6-8f71-4c1f-8a88-c5710305b60c", "dbb4f20e-d32e-41a3-8889-bfcce8669eab"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b7dbf1b7-2144-4d3f-9658-1ded31b6d528", "logId": "0ded0646-a363-4344-88af-8453ba92a6f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0ceddb6-8f71-4c1f-8a88-c5710305b60c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110816683100, "endTime": 154110819605200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "540023fb-73b5-4a34-9694-a75bfdcda936", "logId": "d9d062c5-d60a-49d6-9884-4d5f534af1ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbb4f20e-d32e-41a3-8889-bfcce8669eab", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110819618700, "endTime": 154110829831500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "540023fb-73b5-4a34-9694-a75bfdcda936", "logId": "16d8837c-163c-4d04-b1d0-9249d593d02e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6847821b-976c-45ef-9d60-fdd71b10ee2e", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110816685600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e75865fc-d948-4d2a-8df0-811af861d707", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110819455300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9d062c5-d60a-49d6-9884-4d5f534af1ed", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110816683100, "endTime": 154110819605200}, "additional": {"logType": "info", "children": [], "durationId": "a0ceddb6-8f71-4c1f-8a88-c5710305b60c", "parent": "0ded0646-a363-4344-88af-8453ba92a6f0"}}, {"head": {"id": "6e943f48-b451-452d-8445-2f8f66900fc6", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110819629200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2aec04b-2cd1-47c8-8eb1-76abf1b07c19", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110825305300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbe0b161-da3b-4962-9632-3d2e7dd56db5", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110825424100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae69ae5e-6fd5-48f4-b09a-29cb74bdf6e5", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110825628700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59b1f9cd-ab17-4f2a-a155-661f77a62f6c", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110825718400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "271a6141-4abf-4030-87b0-ba39c68663f2", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110825753800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0865e6b4-349f-412a-a14d-9c446a967de6", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110825786800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a332e4f-4f51-4eaa-afca-54fbf9ede17c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110825837000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afa54697-c556-4da6-b2c6-29fd0d6fbe60", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110825877200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67de40db-284c-476f-a403-f0f25c3e5f6d", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110826032400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4311bc7-95da-49d5-aa4c-e1218c85214c", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110826109100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfc6a341-40d5-418a-8eb1-9cceabf29261", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110826147800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de056b9a-07b5-4608-b399-50725afafcab", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110826177700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0f741fb-4f46-4880-b2a7-84acda4ea283", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110826221200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad210f78-3d79-4ccd-9181-7968b58fc066", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110826250700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2c213c8-ffae-425a-b0ca-029082df1dee", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110826322200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3be4cf9d-baed-4431-9a07-6645fd5d5e72", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110826422400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf2387c7-3147-499f-82be-3818ffc0ffe4", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110826473000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a0b35d3-b56e-4d7a-af10-5cc59f2a21b4", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110826500800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41af5955-a30e-4fc8-9c02-7c2bd9557b2c", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110826542200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5d75759-acda-4a46-a8f2-946388c06214", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110829604300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcdd3653-8a49-4398-b972-7ce843e1dd7b", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110829729000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3969316b-4795-4a8c-b3a3-1e75a1dac3e2", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110829771900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f4ad513-e4be-46e9-9165-c1240cee500d", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110829801700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16d8837c-163c-4d04-b1d0-9249d593d02e", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110819618700, "endTime": 154110829831500}, "additional": {"logType": "info", "children": [], "durationId": "dbb4f20e-d32e-41a3-8889-bfcce8669eab", "parent": "0ded0646-a363-4344-88af-8453ba92a6f0"}}, {"head": {"id": "0ded0646-a363-4344-88af-8453ba92a6f0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110816682400, "endTime": 154110829840700}, "additional": {"logType": "info", "children": ["d9d062c5-d60a-49d6-9884-4d5f534af1ed", "16d8837c-163c-4d04-b1d0-9249d593d02e"], "durationId": "540023fb-73b5-4a34-9694-a75bfdcda936", "parent": "b9e76ebe-f021-4001-a9ef-5ab77624fb99"}}, {"head": {"id": "3a73bc65-13d0-46cc-96be-edfca667a954", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110830912100, "endTime": 154110830923800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b7dbf1b7-2144-4d3f-9658-1ded31b6d528", "logId": "fdc897c3-b751-43db-a3e3-159850ff2106"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fdc897c3-b751-43db-a3e3-159850ff2106", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110830912100, "endTime": 154110830923800}, "additional": {"logType": "info", "children": [], "durationId": "3a73bc65-13d0-46cc-96be-edfca667a954", "parent": "b9e76ebe-f021-4001-a9ef-5ab77624fb99"}}, {"head": {"id": "b9e76ebe-f021-4001-a9ef-5ab77624fb99", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110812359900, "endTime": 154110831041100}, "additional": {"logType": "info", "children": ["9550f7f7-9f0a-4f0a-a6a6-3759c4d50a49", "0ded0646-a363-4344-88af-8453ba92a6f0", "fdc897c3-b751-43db-a3e3-159850ff2106"], "durationId": "b7dbf1b7-2144-4d3f-9658-1ded31b6d528", "parent": "b7f43dcf-230e-47b0-877f-4c63ce21cb7b"}}, {"head": {"id": "b7f43dcf-230e-47b0-877f-4c63ce21cb7b", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110811720700, "endTime": 154110831053500}, "additional": {"logType": "info", "children": ["b9e76ebe-f021-4001-a9ef-5ab77624fb99"], "durationId": "9c58407a-a456-4557-88a3-f0030f51de84", "parent": "e488a992-27f4-4e29-98d1-5d0ea1ae46e3"}}, {"head": {"id": "6e9c1572-89ed-4034-b45e-17e96574d2d4", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110843366200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a6fa344-1c6e-41a3-b951-e9848d87b9a8", "name": "hvigorfile, resolve hvigorfile dependencies in 44 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110874141100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c4a245d-2d5d-4563-b8b7-72fe3859c2de", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110831076400, "endTime": 154110874286600}, "additional": {"logType": "info", "children": [], "durationId": "c7a50cab-a8be-4e14-9c01-ec692dba01a3", "parent": "e488a992-27f4-4e29-98d1-5d0ea1ae46e3"}}, {"head": {"id": "843a2f7d-f7e0-4cb8-9c7a-3002b12b2fc3", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110875154500, "endTime": 154110875416100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c33df5e2-9594-41bd-b174-43538eb9382d", "logId": "a94ccbad-d3ed-4564-bc20-504136d701f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0dd3991-9fd7-49f7-aa07-a125756232a3", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110875253700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a94ccbad-d3ed-4564-bc20-504136d701f2", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110875154500, "endTime": 154110875416100}, "additional": {"logType": "info", "children": [], "durationId": "843a2f7d-f7e0-4cb8-9c7a-3002b12b2fc3", "parent": "e488a992-27f4-4e29-98d1-5d0ea1ae46e3"}}, {"head": {"id": "a7dde342-d50c-48f1-8eb4-9d50145ad526", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110876957000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6c90ff3-77b4-41f9-81cf-0b1fba0109a0", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110890324300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c55f539-ebf3-4299-b217-a7555be4f7d7", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110875428000, "endTime": 154110891314300}, "additional": {"logType": "info", "children": [], "durationId": "cb42b138-745d-42de-8ce6-280f183c9a07", "parent": "e488a992-27f4-4e29-98d1-5d0ea1ae46e3"}}, {"head": {"id": "46196459-a914-48c2-b785-8115f033c1af", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110891356200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e076d56c-77b6-47fd-8400-825a07510ddb", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110899146100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9c9b684-98da-417e-bccf-6178e2b0af70", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110899293200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cd2d529-425f-4c1b-a5f9-2aa391172b89", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110899466900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d70ac1d-36b1-4e29-9236-423872f96e15", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110902525600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef44ef1b-52f7-4905-b95b-e855c6c1bc4b", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110902635800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4dab887-9f3c-4c3e-86ae-705a3d436193", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110891336500, "endTime": 154110906152000}, "additional": {"logType": "info", "children": [], "durationId": "f9ec63df-e558-4c8f-bd22-af2354e4d7a7", "parent": "e488a992-27f4-4e29-98d1-5d0ea1ae46e3"}}, {"head": {"id": "b6149c38-7fa6-41b1-86da-ed1fea5a55c4", "name": "Configuration phase cost:168 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110906189800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26d8f376-d6db-474b-93c9-912a4a09cb4b", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110906169900, "endTime": 154110906281400}, "additional": {"logType": "info", "children": [], "durationId": "32fad83f-e2fe-49ab-a42b-c51b5dbcd664", "parent": "e488a992-27f4-4e29-98d1-5d0ea1ae46e3"}}, {"head": {"id": "e488a992-27f4-4e29-98d1-5d0ea1ae46e3", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110734167500, "endTime": 154110906291200}, "additional": {"logType": "info", "children": ["d728da39-f2b8-421c-afe1-67b27228be72", "58cebfbd-6b6b-4e4c-a50f-56d90f6a6f5e", "fa96e4c2-a25f-4fe8-a134-640b2df9a51d", "b7f43dcf-230e-47b0-877f-4c63ce21cb7b", "9c4a245d-2d5d-4563-b8b7-72fe3859c2de", "8c55f539-ebf3-4299-b217-a7555be4f7d7", "f4dab887-9f3c-4c3e-86ae-705a3d436193", "26d8f376-d6db-474b-93c9-912a4a09cb4b", "a94ccbad-d3ed-4564-bc20-504136d701f2"], "durationId": "c33df5e2-9594-41bd-b174-43538eb9382d", "parent": "b5804a12-bc03-4a9a-86fb-f555a5c4b787"}}, {"head": {"id": "2c778109-cfc2-409b-9ebe-0ea911faea27", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110907563000, "endTime": 154110907581300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03caba92-186b-4b34-83e0-209aa5041357", "logId": "d87345ac-534d-4485-9996-c67fc04715b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d87345ac-534d-4485-9996-c67fc04715b3", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110907563000, "endTime": 154110907581300}, "additional": {"logType": "info", "children": [], "durationId": "2c778109-cfc2-409b-9ebe-0ea911faea27", "parent": "b5804a12-bc03-4a9a-86fb-f555a5c4b787"}}, {"head": {"id": "c335776b-0288-4099-94e3-3a3341268d2a", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110906320200, "endTime": 154110907591100}, "additional": {"logType": "info", "children": [], "durationId": "b4747b4a-a673-442a-9891-81c9a6642e53", "parent": "b5804a12-bc03-4a9a-86fb-f555a5c4b787"}}, {"head": {"id": "803ee726-fd55-4722-9a7a-a3a964fd8250", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110907596400, "endTime": 154110907627300}, "additional": {"logType": "info", "children": [], "durationId": "aa626fcb-812c-42e8-89d7-e67a83abbcfd", "parent": "b5804a12-bc03-4a9a-86fb-f555a5c4b787"}}, {"head": {"id": "b5804a12-bc03-4a9a-86fb-f555a5c4b787", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110721276500, "endTime": 154110907631000}, "additional": {"logType": "info", "children": ["3a091684-17ef-4a36-bfd9-d9c72aa6a693", "e488a992-27f4-4e29-98d1-5d0ea1ae46e3", "c335776b-0288-4099-94e3-3a3341268d2a", "803ee726-fd55-4722-9a7a-a3a964fd8250", "88326587-b03e-493f-86a1-d6f9176244fa", "9f1a1fb8-ec89-476b-aa31-01feb1902003", "d87345ac-534d-4485-9996-c67fc04715b3"], "durationId": "03caba92-186b-4b34-83e0-209aa5041357"}}, {"head": {"id": "ac561b2e-4680-4c8f-8f69-8986a75f2997", "name": "Configuration task cost before running: 190 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110907970700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "170d464f-bf7d-4a72-ba3b-713db6fdc2bb", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110917423200, "endTime": 154110930210400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "7db201e9-8e73-4eec-acea-687c60067e06", "logId": "27b30c76-08bb-4dc1-b5a3-5ade608e01d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7db201e9-8e73-4eec-acea-687c60067e06", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110909709500}, "additional": {"logType": "detail", "children": [], "durationId": "170d464f-bf7d-4a72-ba3b-713db6fdc2bb"}}, {"head": {"id": "6fed4ec1-0631-41dc-a42e-58b7ef569b76", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110910459700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23ac95fb-23a7-4fb1-a681-4b995c58e314", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110910570300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6008c7a8-2ce3-4b09-94d4-6f326ef699e5", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110911348000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c7b13e5-9b04-49ca-b818-9d3b89c8e6ec", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110912203000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b96290cf-3d2c-43f2-b014-43d3fa48fe6e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110913234800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "024191a3-e9fc-422e-a091-a3b2091ed1ad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110913316500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b52d893-e17c-47b1-9292-a156e5b19ba5", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110917437200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03c001c2-9b50-4e2e-809a-ed4c24132828", "name": "Incremental task entry:default@PreBuild pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110929982400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f22af6b-3e6d-4da6-af4e-b2e3c32ca61a", "name": "entry : default@PreBuild cost memory 0.32396697998046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110930131400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27b30c76-08bb-4dc1-b5a3-5ade608e01d5", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110917423200, "endTime": 154110930210400}, "additional": {"logType": "info", "children": [], "durationId": "170d464f-bf7d-4a72-ba3b-713db6fdc2bb"}}, {"head": {"id": "8e476257-c128-4908-87af-e749e20c64f2", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110936774700, "endTime": 154110938977900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "593c5d55-41f8-4013-8023-ce53f1555ebb", "logId": "9d2e0237-e711-4c2f-a196-ccb311f44ae3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "593c5d55-41f8-4013-8023-ce53f1555ebb", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110934731000}, "additional": {"logType": "detail", "children": [], "durationId": "8e476257-c128-4908-87af-e749e20c64f2"}}, {"head": {"id": "74255cda-b4cb-4550-9792-7b82fa030ce0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110935976800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "173d310a-d655-4f5e-ae61-0dc2a6922e9b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110936089800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "808170f4-b05f-47ac-b465-9e357ab9f8f9", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110936784900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4991ba0-83e1-4e35-848e-4c3eba505485", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110937761600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c26c0b3-b2d1-415b-95b2-12cc744109be", "name": "entry : default@CreateModuleInfo cost memory 0.0600738525390625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110938774500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b812a955-82c6-4be7-b43d-9072559df52e", "name": "runTaskFromQueue task cost before running: 221 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110938911300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d2e0237-e711-4c2f-a196-ccb311f44ae3", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110936774700, "endTime": 154110938977900, "totalTime": 2113400}, "additional": {"logType": "info", "children": [], "durationId": "8e476257-c128-4908-87af-e749e20c64f2"}}, {"head": {"id": "8668fe33-fac2-4d59-b168-cbb472676672", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110948618800, "endTime": 154110951329300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3ca24f41-7a71-4917-b5a9-24df9da4dc3c", "logId": "f5fe3b9b-fac9-4625-9af7-c0871b92af49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ca24f41-7a71-4917-b5a9-24df9da4dc3c", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110942729500}, "additional": {"logType": "detail", "children": [], "durationId": "8668fe33-fac2-4d59-b168-cbb472676672"}}, {"head": {"id": "65d2d005-505d-4f39-874b-31423c5fee85", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110943867600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cf33304-f488-4002-b6ac-8cc5eecddbd1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110943984800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78864ed9-3d20-49a7-9ba7-3a3d631d31a7", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110948631700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e80be5b5-b23d-433b-ae0a-fb6414473e6e", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110949933200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c2e7179-c6ec-47d4-99c0-340f78d9ca54", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110951141400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f36a5acf-0930-465d-acbe-b8ad1a204c7c", "name": "entry : default@GenerateMetadata cost memory 0.10268402099609375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110951254700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5fe3b9b-fac9-4625-9af7-c0871b92af49", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110948618800, "endTime": 154110951329300}, "additional": {"logType": "info", "children": [], "durationId": "8668fe33-fac2-4d59-b168-cbb472676672"}}, {"head": {"id": "b2e10525-fc73-43e0-9882-65a7b4326748", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110954345400, "endTime": 154110954622500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6922cb4b-9810-454d-83a0-c815e60c627e", "logId": "69dea67c-ea56-433c-b676-fd7c6667292e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6922cb4b-9810-454d-83a0-c815e60c627e", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110952937500}, "additional": {"logType": "detail", "children": [], "durationId": "b2e10525-fc73-43e0-9882-65a7b4326748"}}, {"head": {"id": "2fa27dae-7c5b-4530-a4f5-0250591d3445", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110954106300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7d679a7-2caf-4685-bce6-1464e1e0bbc3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110954212800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8338f971-0a50-4f45-bcf8-844122e8a128", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110954351500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3088b202-a7a0-46f1-b199-f1d06ec995ba", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110954427500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7648b42-d702-4893-842f-b621b617e929", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110954465200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d4488d5-23a3-4172-b250-f4d6993119b2", "name": "entry : default@ConfigureCmake cost memory 0.0373382568359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110954520000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8444388-5d0f-4b65-96ee-70e7d3a0b1bd", "name": "runTaskFromQueue task cost before running: 237 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110954580600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69dea67c-ea56-433c-b676-fd7c6667292e", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110954345400, "endTime": 154110954622500, "totalTime": 219400}, "additional": {"logType": "info", "children": [], "durationId": "b2e10525-fc73-43e0-9882-65a7b4326748"}}, {"head": {"id": "610c681b-c75f-40e8-8c69-587cc5580348", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110958842000, "endTime": 154110960921600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "e1b11971-964e-4822-a663-0c99ed7bc313", "logId": "1e6b7578-1c2b-4fcf-9051-bb321cc89959"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1b11971-964e-4822-a663-0c99ed7bc313", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110956380900}, "additional": {"logType": "detail", "children": [], "durationId": "610c681b-c75f-40e8-8c69-587cc5580348"}}, {"head": {"id": "65a68941-9d55-4f0e-b82e-d38730573f82", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110957606800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4b18ac9-e361-49c8-ac35-5cf4a7cd45c4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110957721900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e90dd3b-755a-4264-b07b-c0648c7d2de3", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110958853700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6764acc-9bbe-42aa-a68e-d1c6b5459f1c", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110960745100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0709273e-93b2-4397-a7e7-67107f56fdc4", "name": "entry : default@MergeProfile cost memory 0.1181182861328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110960860500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e6b7578-1c2b-4fcf-9051-bb321cc89959", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110958842000, "endTime": 154110960921600}, "additional": {"logType": "info", "children": [], "durationId": "610c681b-c75f-40e8-8c69-587cc5580348"}}, {"head": {"id": "e1c8b74a-28cc-4609-bc1d-6b1a40915362", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110964680500, "endTime": 154110969173000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "66ac4da6-7ad7-4781-9b37-5820902843b9", "logId": "4736988b-c3ac-450a-bdbf-c8df4e899ab5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66ac4da6-7ad7-4781-9b37-5820902843b9", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110962653800}, "additional": {"logType": "detail", "children": [], "durationId": "e1c8b74a-28cc-4609-bc1d-6b1a40915362"}}, {"head": {"id": "49110b98-dc4c-44a0-8f2b-5afbb7c1c71d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110963645400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6c455a4-cf88-474e-9be1-c69325a3e41e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110963841900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c57fcd4e-9a3b-440d-a4b8-e25823d6f5f3", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110964687600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d73b0eb9-9823-403c-9c84-0433d4c82f92", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110967444800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6537cf60-bd13-4e51-b9a2-ae3edb0c14ba", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110968996200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "996fb0b5-9ce3-4b32-ab78-74726f95c622", "name": "entry : default@CreateBuildProfile cost memory -4.47601318359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110969106300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4736988b-c3ac-450a-bdbf-c8df4e899ab5", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110964680500, "endTime": 154110969173000}, "additional": {"logType": "info", "children": [], "durationId": "e1c8b74a-28cc-4609-bc1d-6b1a40915362"}}, {"head": {"id": "ec3e1a0a-21f2-451e-8837-e01858f500b2", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110972767200, "endTime": 154110973636400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "926381a8-53bd-4673-a5ab-1eea35b31007", "logId": "d42a8b80-ba2d-4b17-998b-60062ff59e55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "926381a8-53bd-4673-a5ab-1eea35b31007", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110970737400}, "additional": {"logType": "detail", "children": [], "durationId": "ec3e1a0a-21f2-451e-8837-e01858f500b2"}}, {"head": {"id": "c538a479-cc56-4616-abaf-d7acdce237db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110971906100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "886ac5c4-48c6-479b-8363-a1f65a706a80", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110972012900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a64eceec-6317-47b1-86f3-fde9cd46ef88", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110972775600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dff5e01-a8c2-4f15-baa9-275105420e81", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110972897400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c962157c-0f27-4219-ba21-bf101eb0db2a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110972940900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c1494aa-ad25-4686-b450-b2a38a985c38", "name": "entry : default@PreCheckSyscap cost memory 0.0409698486328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110973129800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cdcb793-77e0-455d-a3ff-b82ad8bb262f", "name": "runTaskFromQueue task cost before running: 255 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110973470400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d42a8b80-ba2d-4b17-998b-60062ff59e55", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110972767200, "endTime": 154110973636400, "totalTime": 672600}, "additional": {"logType": "info", "children": [], "durationId": "ec3e1a0a-21f2-451e-8837-e01858f500b2"}}, {"head": {"id": "5fc2a2f8-a57e-42b7-945f-20e734f0bd59", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110978126300, "endTime": 154110984503700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9cfc1e7f-a5cb-49f8-8f32-5acbf21607f5", "logId": "92579e2f-b77b-4971-ac9f-105289279fc1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9cfc1e7f-a5cb-49f8-8f32-5acbf21607f5", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110975519800}, "additional": {"logType": "detail", "children": [], "durationId": "5fc2a2f8-a57e-42b7-945f-20e734f0bd59"}}, {"head": {"id": "d0fcaa6c-a1a9-4988-aa5d-d073e858606c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110976507100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaf296bf-d6f4-47ec-be64-a79e4bd268a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110976622800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4752e9b3-0b25-4413-8403-5e48149cf5b0", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110978139200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e794599a-3eda-4b53-960e-00ac5224f4a5", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110983582900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbfa4620-ba0b-43c5-91bb-b60733eb4aac", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110984294300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e71e525-d5f2-4ede-93a6-60cceb884bc3", "name": "entry : default@GeneratePkgContextInfo cost memory 0.279571533203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110984434700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92579e2f-b77b-4971-ac9f-105289279fc1", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110978126300, "endTime": 154110984503700}, "additional": {"logType": "info", "children": [], "durationId": "5fc2a2f8-a57e-42b7-945f-20e734f0bd59"}}, {"head": {"id": "6715f51b-b5f8-4a77-b3c9-05821f10aeeb", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110992591200, "endTime": 154110995035800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "ff6291b2-7cd0-4098-a3fb-3d5c2b8c1530", "logId": "2f402a5d-8ee4-4f13-80d1-978b523bd700"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff6291b2-7cd0-4098-a3fb-3d5c2b8c1530", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110986068500}, "additional": {"logType": "detail", "children": [], "durationId": "6715f51b-b5f8-4a77-b3c9-05821f10aeeb"}}, {"head": {"id": "43977984-e12d-4905-b727-d63aa7f7dd61", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110987704700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee472a28-0bec-4c63-8e26-45dca4453202", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110987826600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c84b9144-6063-4ef6-9adc-241d2c81aa60", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110992606600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6c05011-6de3-4d0e-b4c3-fd8dc605cc19", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110994599300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63af9423-5771-4c68-986c-21e01cee74db", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110994738400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6b0eb4a-08e4-4ebb-9783-23f0964b247a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110994815300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29dd89b3-6b52-4a9f-96bc-a22b21e3072a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110994855600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e8e3351-6a91-46ec-8265-60468afab10d", "name": "entry : default@ProcessIntegratedHsp cost memory 0.120208740234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110994935700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f2fb637-cd62-4cd1-a5e3-9aaf744d9b67", "name": "runTaskFromQueue task cost before running: 277 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110994998200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f402a5d-8ee4-4f13-80d1-978b523bd700", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110992591200, "endTime": 154110995035800, "totalTime": 2397000}, "additional": {"logType": "info", "children": [], "durationId": "6715f51b-b5f8-4a77-b3c9-05821f10aeeb"}}, {"head": {"id": "41310bcd-509a-40ea-a48e-5bd2859be89b", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110999216700, "endTime": 154110999538500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ebb7708e-33b5-40cc-999a-bb9a53935b80", "logId": "bef85c08-9d56-4d55-8824-f0506785f2db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ebb7708e-33b5-40cc-999a-bb9a53935b80", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110997351700}, "additional": {"logType": "detail", "children": [], "durationId": "41310bcd-509a-40ea-a48e-5bd2859be89b"}}, {"head": {"id": "ad74a094-46ec-455e-81f5-2da1eb712c36", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110998368700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f9748e7-90e4-45bb-b14e-67feab7bf40e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110998470100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08bf5a0d-7dd9-4e5f-9adb-d0e170d2c841", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110999224400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "064bf16d-2abb-433e-81c2-74b8dda2be33", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110999333000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbee55c1-8530-4cf6-bbf4-edaa707d6331", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110999377000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9bef8ef-18b3-42a2-96c3-46384e7c4a5c", "name": "entry : default@BuildNativeWithCmake cost memory 0.03838348388671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110999440600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5bd78e3-b673-43e3-8395-882476db09e1", "name": "runTaskFromQueue task cost before running: 281 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110999499200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bef85c08-9d56-4d55-8824-f0506785f2db", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110999216700, "endTime": 154110999538500, "totalTime": 268600}, "additional": {"logType": "info", "children": [], "durationId": "41310bcd-509a-40ea-a48e-5bd2859be89b"}}, {"head": {"id": "432d7b83-80c0-4331-90ff-8c547903bbeb", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111002776600, "endTime": 154111006903400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2d42233b-3b43-46c7-a26f-1ee0641a2493", "logId": "a8faa83c-b930-4614-8f61-0c7750a816dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d42233b-3b43-46c7-a26f-1ee0641a2493", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111000983100}, "additional": {"logType": "detail", "children": [], "durationId": "432d7b83-80c0-4331-90ff-8c547903bbeb"}}, {"head": {"id": "dbf44e65-db7c-477a-ab15-49218314b814", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111001953900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ee81462-2f04-4109-b266-d6147e565dc7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111002050700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "824b1d55-4e2c-40c3-a586-17e136c138ed", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111002784500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f44db7e3-305e-40b6-b340-ee2407bc9f8e", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111006712900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edc84209-7592-4c2f-b9b1-d2d0911f0f6c", "name": "entry : default@MakePackInfo cost memory 0.1633758544921875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111006836600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8faa83c-b930-4614-8f61-0c7750a816dc", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111002776600, "endTime": 154111006903400}, "additional": {"logType": "info", "children": [], "durationId": "432d7b83-80c0-4331-90ff-8c547903bbeb"}}, {"head": {"id": "754e04e6-2e87-460d-b4ca-958d0ad1c1fb", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111011894500, "endTime": 154111015426800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9dcca1e7-df1f-46d4-a389-3eeefd49df07", "logId": "a84d21de-88a2-4c76-9726-e4a60838b60e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9dcca1e7-df1f-46d4-a389-3eeefd49df07", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111009429600}, "additional": {"logType": "detail", "children": [], "durationId": "754e04e6-2e87-460d-b4ca-958d0ad1c1fb"}}, {"head": {"id": "234b9c36-dbf8-4f5c-ba73-b9b8f16fb968", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111010518300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efcff7ac-5aad-42bb-83bf-9e5bf4f9422a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111010632000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cca572c3-aed4-476b-973b-8c5b1909f0eb", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111011906500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31ef6dd6-bb0d-43b5-bd84-5746f9a5a9f5", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111012105800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3b6e7b6-9361-4c29-811e-102c9f9b936a", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111012951000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0b87c8c-823a-4034-9af6-958969f36e2b", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111015249400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a51fee59-4ffc-4d05-baa9-1b6073b6ea56", "name": "entry : default@SyscapTransform cost memory 0.1498870849609375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111015364200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a84d21de-88a2-4c76-9726-e4a60838b60e", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111011894500, "endTime": 154111015426800}, "additional": {"logType": "info", "children": [], "durationId": "754e04e6-2e87-460d-b4ca-958d0ad1c1fb"}}, {"head": {"id": "b7b1a0f2-7e3c-45b5-9b17-a5ed4dd71cd1", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111019359900, "endTime": 154111021421900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "18e98dd9-c043-487a-9a13-356e3bed94d7", "logId": "c2d306b8-2d69-4869-a02e-86cc3ddd06fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18e98dd9-c043-487a-9a13-356e3bed94d7", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111016949200}, "additional": {"logType": "detail", "children": [], "durationId": "b7b1a0f2-7e3c-45b5-9b17-a5ed4dd71cd1"}}, {"head": {"id": "eafbc6e9-04a9-4b8e-ba39-1b02316e6dd1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111017966600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da28141b-5bab-425f-a38d-8c82773633b3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111018075900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "994557c5-c7b5-4cb7-845a-388e7ebc0a0a", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111019369300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0d82b9e-b192-4b9f-bddf-eeba851a3665", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111021239700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1318479b-c864-4583-9f3d-65853ad5cc50", "name": "entry : default@ProcessProfile cost memory 0.12369537353515625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111021357400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2d306b8-2d69-4869-a02e-86cc3ddd06fa", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111019359900, "endTime": 154111021421900}, "additional": {"logType": "info", "children": [], "durationId": "b7b1a0f2-7e3c-45b5-9b17-a5ed4dd71cd1"}}, {"head": {"id": "b86e902d-2ae8-42c1-9863-b79eb56c6b01", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111026334600, "endTime": 154111032475900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f8a36d63-b74d-430c-9213-bbc2eb1487e2", "logId": "6e7a3029-d6ba-4e8c-b8ef-52cbaa9c43af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8a36d63-b74d-430c-9213-bbc2eb1487e2", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111022980600}, "additional": {"logType": "detail", "children": [], "durationId": "b86e902d-2ae8-42c1-9863-b79eb56c6b01"}}, {"head": {"id": "cf22021c-4960-4c0b-9500-a612dfa543e2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111023993300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3841065e-126b-4c2a-8156-d3aaaa932ca4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111024121100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21739be8-f3d7-4146-beba-5e42c29481db", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111026349100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75169dd8-525a-49c8-baf5-32e8a16d7c9a", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111032257700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c28112cc-e8bb-436c-a750-6ec480a426b2", "name": "entry : default@ProcessRouterMap cost memory 0.23218536376953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111032410400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e7a3029-d6ba-4e8c-b8ef-52cbaa9c43af", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111026334600, "endTime": 154111032475900}, "additional": {"logType": "info", "children": [], "durationId": "b86e902d-2ae8-42c1-9863-b79eb56c6b01"}}, {"head": {"id": "5cf3a09d-6539-4f0e-81cd-7376f7c2fb30", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111036731600, "endTime": 154111043523600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "598ac0bd-827c-4eaa-98de-d448dc94d794", "logId": "d40d1621-bc7b-416d-a54d-765968b3b62f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "598ac0bd-827c-4eaa-98de-d448dc94d794", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111035470400}, "additional": {"logType": "detail", "children": [], "durationId": "5cf3a09d-6539-4f0e-81cd-7376f7c2fb30"}}, {"head": {"id": "0d3f24dc-7e1f-4088-92b4-74ac40d8b575", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111036533200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "910426e1-5431-4c7c-ac29-3f631afa221c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111036640000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a26f905a-288b-421a-a8d5-3e062965c542", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111036738100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4435a362-cfd7-461b-ba15-5b7aa016d9a6", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111036823300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f92409c-4b27-4231-8505-b8fe9ecec179", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111041366400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf4408a0-a478-4b80-a355-eee1db125db2", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111041627700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f546731-7a87-4f19-a10f-e276affe96dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111041753000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5435d608-6008-47ff-8d91-ec8819af7775", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111041795500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e6328ec-f494-4cfc-bad2-1701d9ed745d", "name": "entry : default@ProcessStartupConfig cost memory 0.257293701171875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111043280100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed103583-a97d-4514-b9e6-d77a6d56ed3c", "name": "runTaskFromQueue task cost before running: 325 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111043449600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d40d1621-bc7b-416d-a54d-765968b3b62f", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111036731600, "endTime": 154111043523600, "totalTime": 6684500}, "additional": {"logType": "info", "children": [], "durationId": "5cf3a09d-6539-4f0e-81cd-7376f7c2fb30"}}, {"head": {"id": "01c04804-f298-410e-9cab-b093c806eced", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111048690300, "endTime": 154111050107200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "b7282599-f679-43f1-a7be-603816c34eef", "logId": "8d054800-cf55-4a64-99b0-91bfb2c56b5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7282599-f679-43f1-a7be-603816c34eef", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111046809100}, "additional": {"logType": "detail", "children": [], "durationId": "01c04804-f298-410e-9cab-b093c806eced"}}, {"head": {"id": "bfae53bf-14f9-42d2-b242-6b0d1cb15681", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111047813600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2df4d2b-2130-4c2e-8c70-e31cb3af4523", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111047935200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df50f6b8-3feb-4c61-8d6d-921beb6168d7", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111048698100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6a68044-1253-47e3-8ebf-38d8e52f2463", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111048821700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2f4f042-2214-4441-8fed-4df3c5dd2288", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111048863300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "445e408b-9e41-419e-91e1-dc6bcf65ad96", "name": "entry : default@BuildNativeWithNinja cost memory 0.05799102783203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111049914000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a37ae268-b60c-46c8-9cb6-91dbbf32de30", "name": "runTaskFromQueue task cost before running: 332 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111050041300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d054800-cf55-4a64-99b0-91bfb2c56b5d", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111048690300, "endTime": 154111050107200, "totalTime": 1330300}, "additional": {"logType": "info", "children": [], "durationId": "01c04804-f298-410e-9cab-b093c806eced"}}, {"head": {"id": "0d08bb4e-d8d9-4817-8168-4a2a1562b1b1", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111056343100, "endTime": 154111061967500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d4be0856-e145-4e05-bf7f-d232effd6612", "logId": "8115a9da-16cf-4fcc-8847-c08aa1389b4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4be0856-e145-4e05-bf7f-d232effd6612", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111052568100}, "additional": {"logType": "detail", "children": [], "durationId": "0d08bb4e-d8d9-4817-8168-4a2a1562b1b1"}}, {"head": {"id": "8e8388a8-d22f-4534-80c8-f77237eca0d3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111053587800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90075b37-b94e-4de2-bfcd-daa238256cf7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111053739400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a01e24bf-1450-4554-bcb0-34334bf89e3e", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111054954000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73892e54-de33-400e-8da3-7004e6334a30", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111057836500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b99e74f0-cb75-45cc-83fa-4c7d43b83597", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111060041300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eee7415-a17a-4a6f-8a87-bbb82e0c8b2a", "name": "entry : default@ProcessResource cost memory 0.16130828857421875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111060174800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8115a9da-16cf-4fcc-8847-c08aa1389b4e", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111056343100, "endTime": 154111061967500}, "additional": {"logType": "info", "children": [], "durationId": "0d08bb4e-d8d9-4817-8168-4a2a1562b1b1"}}, {"head": {"id": "a17e7e15-d4f3-4241-8201-89876e671bb8", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111069771100, "endTime": 154111090514500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4c018157-9000-4831-b4b1-84957ae8db6d", "logId": "edfdfdf0-809b-4c36-9312-56adec0bd644"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c018157-9000-4831-b4b1-84957ae8db6d", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111065402100}, "additional": {"logType": "detail", "children": [], "durationId": "a17e7e15-d4f3-4241-8201-89876e671bb8"}}, {"head": {"id": "a864f150-631e-4fd5-b33c-ff72b88f77e0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111066592000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97092442-7d08-4040-bcfb-86a0bd7955bd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111066707700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5e0c311-0790-4397-8f13-a50a6e7aa4aa", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111069783200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc98963e-50e9-4c89-9e08-a21ae8a57765", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111090294400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0285016-f47c-47d0-bf2a-610fa47c72f2", "name": "entry : default@GenerateLoaderJson cost memory -4.7823028564453125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111090448400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edfdfdf0-809b-4c36-9312-56adec0bd644", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111069771100, "endTime": 154111090514500}, "additional": {"logType": "info", "children": [], "durationId": "a17e7e15-d4f3-4241-8201-89876e671bb8"}}, {"head": {"id": "e04b84b7-d730-4c35-874a-e7d51d79b7a4", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111100265800, "endTime": 154111105561800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "598f40bf-59f6-47d0-93dd-9306c46826b6", "logId": "19af7a75-2cfb-4b41-b849-b5c1623bc27f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "598f40bf-59f6-47d0-93dd-9306c46826b6", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111098624300}, "additional": {"logType": "detail", "children": [], "durationId": "e04b84b7-d730-4c35-874a-e7d51d79b7a4"}}, {"head": {"id": "109db208-e96f-4ccf-8c36-65724ab2f08f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111099506600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fb1b7eb-1db3-4806-815f-c98ff3950048", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111099606400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4913a1f3-ac0d-4bee-942f-c44f90dc9447", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111100274600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc31d53c-0a8c-4a0c-a5c0-4aecbcfd18be", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111105362200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e08d9940-929d-49d3-9860-46e1f0936856", "name": "entry : default@ProcessLibs cost memory 0.14154052734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111105497600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19af7a75-2cfb-4b41-b849-b5c1623bc27f", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111100265800, "endTime": 154111105561800}, "additional": {"logType": "info", "children": [], "durationId": "e04b84b7-d730-4c35-874a-e7d51d79b7a4"}}, {"head": {"id": "3800122a-65c4-4988-82ee-45aa52448c11", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111112998600, "endTime": 154111138131700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "85b20516-8733-4c90-9082-25da3340a497", "logId": "acf3368e-0a84-40f5-b7f0-2878c47f1b46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85b20516-8733-4c90-9082-25da3340a497", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111108468000}, "additional": {"logType": "detail", "children": [], "durationId": "3800122a-65c4-4988-82ee-45aa52448c11"}}, {"head": {"id": "86c04356-3116-44dc-85a4-8577927b4e36", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111109551300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29fd70b3-fae8-4f8e-aa04-d6ab7a737a9f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111109680600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf3041fd-5703-4b39-97c6-8ca13b26c690", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111110578200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07a44805-27ad-4376-93ea-227716da11c0", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111113020600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7fbd22f-e506-413c-aaf8-0f14f1d7d476", "name": "Incremental task entry:default@CompileResource pre-execution cost: 24 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111137900800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c84e79d-3fee-4cb7-9446-274afed247de", "name": "entry : default@CompileResource cost memory 1.31475830078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111138055000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acf3368e-0a84-40f5-b7f0-2878c47f1b46", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111112998600, "endTime": 154111138131700}, "additional": {"logType": "info", "children": [], "durationId": "3800122a-65c4-4988-82ee-45aa52448c11"}}, {"head": {"id": "d2f2c28d-f907-4137-857b-25bc02f8bde7", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111146068600, "endTime": 154111148250100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "b4c90fdf-bbb8-4943-9ada-cbcf9ace31e5", "logId": "9db413cc-35ed-4638-9edb-ab97fb684971"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4c90fdf-bbb8-4943-9ada-cbcf9ace31e5", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111142061700}, "additional": {"logType": "detail", "children": [], "durationId": "d2f2c28d-f907-4137-857b-25bc02f8bde7"}}, {"head": {"id": "14f3d755-d02b-4243-a5c2-806b5898dafb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111143306500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b284b5c-29ea-4aa1-9af2-d3564ca25d74", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111143454600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "958812eb-6e8d-407e-a4bb-e48206fcb79a", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111146083100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d1855d6-5404-4718-8679-09cb1a9144d0", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111146667900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b02ec21-23d1-4fc1-8ba9-e57ab99bb79a", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111148076800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f0114e2-0844-46fa-a7af-550ff5d1efff", "name": "entry : default@DoNativeStrip cost memory 0.07907867431640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111148190200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9db413cc-35ed-4638-9edb-ab97fb684971", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111146068600, "endTime": 154111148250100}, "additional": {"logType": "info", "children": [], "durationId": "d2f2c28d-f907-4137-857b-25bc02f8bde7"}}, {"head": {"id": "8551d2a4-98da-49e4-9290-80a7cd2dc632", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111154315600, "endTime": 154122239633400}, "additional": {"children": ["a5cfdd57-4056-412a-a540-40994c48cd7d", "6ef6c2d3-09e8-421d-9966-f12e709ac482"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed."], "detailId": "cecd3b8d-6a82-431b-9d87-b2898ebd5877", "logId": "bc32d2fa-da91-4b38-8902-45127bdda78a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cecd3b8d-6a82-431b-9d87-b2898ebd5877", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111149791400}, "additional": {"logType": "detail", "children": [], "durationId": "8551d2a4-98da-49e4-9290-80a7cd2dc632"}}, {"head": {"id": "be327a26-ffc8-4d71-bfdb-2d71d4fcf7e8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111150774900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "776c22c0-7b35-4ebe-91cc-44a137b5814f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111150892800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcc7103a-3d3a-4a85-b49f-92b9b43450a2", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111154352200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c915c00f-b2f5-4112-a8d0-42c06245754a", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111154511900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1078a327-2904-458a-8301-4c0543178b03", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111181703400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "590ff2b8-8107-4ce2-ac97-1c815984aaa2", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 21 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111181879100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8db58ecc-7aac-4460-a699-84846f013dfd", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111204877000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "609264a4-49b2-40f1-84b9-c9ba8b01c6a9", "name": "default@CompileArkTS work[12] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111207108500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5cfdd57-4056-412a-a540-40994c48cd7d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154113951798400, "endTime": 154122228621900}, "additional": {"children": ["1fb9bfa8-3ad2-4fe1-a190-31598d513c07", "70303545-3b95-41c9-8cf1-2a26c1a223c1", "0345abae-645c-4cdd-8f29-7e626d53ff70", "828a489d-755b-4f8d-be16-328e13ba64e6", "781a5749-e63f-4153-8625-259c20c5252f", "fbfa9b65-e7e3-40e0-baa5-0be34ea777ae", "c59a84e4-f8e8-4ae5-92e2-2bea59fd8567"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "8551d2a4-98da-49e4-9290-80a7cd2dc632", "logId": "db40973c-37dc-4893-9708-118d2b00b5a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6a8afc4-5f56-4f51-bca6-610d06941bae", "name": "default@CompileArkTS work[12] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208545400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e2ba710-d470-452c-b012-f4d6651b5a9c", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208670700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33f523fb-67fe-432f-91de-5c811a721a69", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208714400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a89a3f39-70fd-426d-a16c-56631248a061", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208739600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86f82281-6277-4da2-a6bd-d67945c8c59e", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208763900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f065b251-b4fd-4002-8e40-ed0cd9f17c3f", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208792700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18ed33f7-acfa-45ce-b0e3-a5d8f41d6639", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208815500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7821ece6-326b-40ec-804c-7202488fc5d7", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208838800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ac5530a-cfe1-46b0-9397-3d54c716d7ce", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208860400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3c9aa27-8dee-4c0f-b5f2-5662cd933a12", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208884600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1126bd1-414c-4194-892e-f873d51ed1cf", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208907600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95858926-b4d4-4fd4-ad9d-012475bde05d", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208928000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25c103f8-63ed-4fee-aa4a-e33c9bcea496", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208949600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64b9626e-fb63-4b91-8a5e-6f789bcbc0ba", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208970700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dd5e2aa-f6bd-4a99-a1d0-d084d340cb95", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111208992300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "639a4eab-c77e-4a1a-a818-2673fd8dd7bf", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111209015000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60a5374b-0646-469f-92c5-04df14d646fc", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111209172100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75848fb4-9e2b-421d-b581-141e116cf3cf", "name": "default@CompileArkTS work[12] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111210226900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b249c28-bda7-47b3-9601-f9adf2eb4024", "name": "default@CompileArkTS work[12] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111210380500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efa531e0-d9b5-4609-afab-cfef7bb04a8a", "name": "CopyResources startTime: 154111210437700", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111210441300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efc7ed4f-5eae-4352-8cca-42a21ce7b5b2", "name": "default@CompileArkTS work[13] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111210506000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ef6c2d3-09e8-421d-9966-f12e709ac482", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 154112315261400, "endTime": 154112330851900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "8551d2a4-98da-49e4-9290-80a7cd2dc632", "logId": "0f0114b0-9adb-4fb2-ad9a-e9635c2581b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff5d521c-a387-4b34-9718-3ed7ba16d17a", "name": "default@CompileArkTS work[13] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111211353400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b42168c2-7205-47d5-84cd-a322e4c9535a", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111211421400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c97c125f-d15b-4f01-91d9-763a3b0dffb6", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111211474800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a995a443-42a4-4436-943b-18ec3f0d34a9", "name": "default@CompileArkTS work[13] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111212174600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98f808d9-0e9c-4713-804d-b60cc8f4175a", "name": "default@CompileArkTS work[13] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111212252600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9176963-0408-4611-a565-67bbdeb839ea", "name": "entry : default@CompileArkTS cost memory 2.5683441162109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111212376400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59ec3074-1134-47fb-87b5-ee8cb1b7f3cc", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111219361100, "endTime": 154111227440400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "a30db9b6-d0da-46bd-9ff1-c8b970997b7c", "logId": "872d6ccf-971c-41b6-819d-e6b3d4c1b4d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a30db9b6-d0da-46bd-9ff1-c8b970997b7c", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111214272800}, "additional": {"logType": "detail", "children": [], "durationId": "59ec3074-1134-47fb-87b5-ee8cb1b7f3cc"}}, {"head": {"id": "fe0a2f3c-3fa8-4a27-8c96-6f7c24031e80", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111215289100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "849ca889-03cd-4cea-afbe-e59f2e52903b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111215400000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5a57221-6476-4ee4-8a62-c9ec83967ac5", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111219371900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf882479-72ab-4c68-96a1-eebd95e68d0d", "name": "entry : default@BuildJS cost memory 0.34496307373046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111227150300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca02bb5c-733a-4cb5-97cb-9c976321b023", "name": "runTaskFromQueue task cost before running: 509 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111227349500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "872d6ccf-971c-41b6-819d-e6b3d4c1b4d5", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111219361100, "endTime": 154111227440400, "totalTime": 7951000}, "additional": {"logType": "info", "children": [], "durationId": "59ec3074-1134-47fb-87b5-ee8cb1b7f3cc"}}, {"head": {"id": "c4555842-7f76-4d29-8b27-09b5544a39de", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111232846900, "endTime": 154111236300500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "56ac6c4f-fee4-40a1-a96c-6654e3a18375", "logId": "2db90caa-f5ce-4f3b-8ce7-9b0f1bcde0bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56ac6c4f-fee4-40a1-a96c-6654e3a18375", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111229142300}, "additional": {"logType": "detail", "children": [], "durationId": "c4555842-7f76-4d29-8b27-09b5544a39de"}}, {"head": {"id": "4a4672ac-55f0-4a00-8682-eb43704b5945", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111230231400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7def23e0-59a5-4c81-b43d-efcb70469927", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111230344100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28966230-a620-4a94-90a1-9b56df63b7c4", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111232859600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df47f12f-1f3e-495d-ba24-6e1870dbb9c8", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111233690600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b9b079c-b9b2-4033-bf2e-8c419fcd11a7", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111236071400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58cd2e60-e3eb-4119-ac63-c996ef9a3569", "name": "entry : default@CacheNativeLibs cost memory 0.0998077392578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111236226300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2db90caa-f5ce-4f3b-8ce7-9b0f1bcde0bb", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111232846900, "endTime": 154111236300500}, "additional": {"logType": "info", "children": [], "durationId": "c4555842-7f76-4d29-8b27-09b5544a39de"}}, {"head": {"id": "b054900e-2db8-4791-bb98-64da978e7f7b", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154112331276100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8180013-cb7b-473f-97ad-74eb56a5c97a", "name": "CopyResources is end, endTime: 154112331505600", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154112331511000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46376def-b139-407d-acbf-59efb2f41dad", "name": "default@CompileArkTS work[13] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154112331694700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f0114b0-9adb-4fb2-ad9a-e9635c2581b0", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 154112315261400, "endTime": 154112330851900}, "additional": {"logType": "info", "children": [], "durationId": "6ef6c2d3-09e8-421d-9966-f12e709ac482", "parent": "bc32d2fa-da91-4b38-8902-45127bdda78a"}}, {"head": {"id": "9f9ba805-59f2-4b18-a240-d53f61b9d0e1", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154112331802500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39e6978e-1712-425d-ac14-42e8d6098e02", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122230132400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fb9bfa8-3ad2-4fe1-a190-31598d513c07", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154113952736100, "endTime": 154114908916200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a5cfdd57-4056-412a-a540-40994c48cd7d", "logId": "483985e5-ffbe-4ae9-9f8c-998146182d6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "483985e5-ffbe-4ae9-9f8c-998146182d6b", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154113952736100, "endTime": 154114908916200}, "additional": {"logType": "info", "children": [], "durationId": "1fb9bfa8-3ad2-4fe1-a190-31598d513c07", "parent": "db40973c-37dc-4893-9708-118d2b00b5a6"}}, {"head": {"id": "70303545-3b95-41c9-8cf1-2a26c1a223c1", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154114910381100, "endTime": 154114981579400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a5cfdd57-4056-412a-a540-40994c48cd7d", "logId": "bd9e5977-a577-4b6b-b4b9-c42bd49ab19e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd9e5977-a577-4b6b-b4b9-c42bd49ab19e", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154114910381100, "endTime": 154114981579400}, "additional": {"logType": "info", "children": [], "durationId": "70303545-3b95-41c9-8cf1-2a26c1a223c1", "parent": "db40973c-37dc-4893-9708-118d2b00b5a6"}}, {"head": {"id": "0345abae-645c-4cdd-8f29-7e626d53ff70", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154114981683500, "endTime": 154114981916600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a5cfdd57-4056-412a-a540-40994c48cd7d", "logId": "d555f519-6e60-4453-aa34-dfa558aceb77"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d555f519-6e60-4453-aa34-dfa558aceb77", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154114981683500, "endTime": 154114981916600}, "additional": {"logType": "info", "children": [], "durationId": "0345abae-645c-4cdd-8f29-7e626d53ff70", "parent": "db40973c-37dc-4893-9708-118d2b00b5a6"}}, {"head": {"id": "828a489d-755b-4f8d-be16-328e13ba64e6", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154114981976200, "endTime": 154121972563400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a5cfdd57-4056-412a-a540-40994c48cd7d", "logId": "e7019e43-6746-4db4-b4e9-fe88db84b5aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7019e43-6746-4db4-b4e9-fe88db84b5aa", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154114981976200, "endTime": 154121972563400}, "additional": {"logType": "info", "children": [], "durationId": "828a489d-755b-4f8d-be16-328e13ba64e6", "parent": "db40973c-37dc-4893-9708-118d2b00b5a6"}}, {"head": {"id": "781a5749-e63f-4153-8625-259c20c5252f", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154121972917100, "endTime": 154122008875000}, "additional": {"children": ["8c6f559b-51b6-4014-be6d-4a2fd88ffa68", "1ead8b1e-79e6-4ba9-9438-307a692b77a8", "f327a45b-5b6e-48a4-bb6a-02a0a748332e"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a5cfdd57-4056-412a-a540-40994c48cd7d", "logId": "8fa956ee-ef8a-466f-bb3f-78843a1de864"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fa956ee-ef8a-466f-bb3f-78843a1de864", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154121972917100, "endTime": 154122008875000}, "additional": {"logType": "info", "children": ["09c3ece5-a242-49c0-aec8-adb286e93e66", "2ed57443-247d-4320-b132-a9851f7561d8", "04810692-3d75-4d0f-aac1-fdd4761da095"], "durationId": "781a5749-e63f-4153-8625-259c20c5252f", "parent": "db40973c-37dc-4893-9708-118d2b00b5a6"}}, {"head": {"id": "8c6f559b-51b6-4014-be6d-4a2fd88ffa68", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154121973103500, "endTime": 154121973123200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "781a5749-e63f-4153-8625-259c20c5252f", "logId": "09c3ece5-a242-49c0-aec8-adb286e93e66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09c3ece5-a242-49c0-aec8-adb286e93e66", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154121973103500, "endTime": 154121973123200}, "additional": {"logType": "info", "children": [], "durationId": "8c6f559b-51b6-4014-be6d-4a2fd88ffa68", "parent": "8fa956ee-ef8a-466f-bb3f-78843a1de864"}}, {"head": {"id": "1ead8b1e-79e6-4ba9-9438-307a692b77a8", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154121973133500, "endTime": 154122002783700}, "additional": {"children": ["36b0098c-46eb-46cd-954d-f40c45f49f54"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "781a5749-e63f-4153-8625-259c20c5252f", "logId": "2ed57443-247d-4320-b132-a9851f7561d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ed57443-247d-4320-b132-a9851f7561d8", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154121973133500, "endTime": 154122002783700}, "additional": {"logType": "info", "children": ["a4424cd4-fd2e-40f7-ad1d-a64f4334fa1f"], "durationId": "1ead8b1e-79e6-4ba9-9438-307a692b77a8", "parent": "8fa956ee-ef8a-466f-bb3f-78843a1de864"}}, {"head": {"id": "36b0098c-46eb-46cd-954d-f40c45f49f54", "name": "module 'HomePage.ets' pack", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154121975319900, "endTime": 154121990879700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1ead8b1e-79e6-4ba9-9438-307a692b77a8", "logId": "a4424cd4-fd2e-40f7-ad1d-a64f4334fa1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4424cd4-fd2e-40f7-ad1d-a64f4334fa1f", "name": "module 'HomePage.ets' pack", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154121975319900, "endTime": 154121990879700}, "additional": {"logType": "info", "children": [], "durationId": "36b0098c-46eb-46cd-954d-f40c45f49f54", "parent": "2ed57443-247d-4320-b132-a9851f7561d8"}}, {"head": {"id": "f327a45b-5b6e-48a4-bb6a-02a0a748332e", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154122002803200, "endTime": 154122008855500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "781a5749-e63f-4153-8625-259c20c5252f", "logId": "04810692-3d75-4d0f-aac1-fdd4761da095"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04810692-3d75-4d0f-aac1-fdd4761da095", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122002803200, "endTime": 154122008855500}, "additional": {"logType": "info", "children": [], "durationId": "f327a45b-5b6e-48a4-bb6a-02a0a748332e", "parent": "8fa956ee-ef8a-466f-bb3f-78843a1de864"}}, {"head": {"id": "fbfa9b65-e7e3-40e0-baa5-0be34ea777ae", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154122008895200, "endTime": 154122220244100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a5cfdd57-4056-412a-a540-40994c48cd7d", "logId": "fe9b0d26-641c-4b2e-9548-7b9bd6c72104"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe9b0d26-641c-4b2e-9548-7b9bd6c72104", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122008895200, "endTime": 154122220244100}, "additional": {"logType": "info", "children": [], "durationId": "fbfa9b65-e7e3-40e0-baa5-0be34ea777ae", "parent": "db40973c-37dc-4893-9708-118d2b00b5a6"}}, {"head": {"id": "c59a84e4-f8e8-4ae5-92e2-2bea59fd8567", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154112212911600, "endTime": 154113950198900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a5cfdd57-4056-412a-a540-40994c48cd7d", "logId": "ac17b3b7-bb35-47f3-b69c-edb1083ef885"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac17b3b7-bb35-47f3-b69c-edb1083ef885", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154112212911600, "endTime": 154113950198900}, "additional": {"logType": "info", "children": [], "durationId": "c59a84e4-f8e8-4ae5-92e2-2bea59fd8567", "parent": "db40973c-37dc-4893-9708-118d2b00b5a6"}}, {"head": {"id": "a3799d13-7d5a-4503-93d9-735b1f023d3c", "name": "default@CompileArkTS work[12] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122239121000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db40973c-37dc-4893-9708-118d2b00b5a6", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 154113951798400, "endTime": 154122228621900}, "additional": {"logType": "info", "children": ["483985e5-ffbe-4ae9-9f8c-998146182d6b", "bd9e5977-a577-4b6b-b4b9-c42bd49ab19e", "d555f519-6e60-4453-aa34-dfa558aceb77", "e7019e43-6746-4db4-b4e9-fe88db84b5aa", "8fa956ee-ef8a-466f-bb3f-78843a1de864", "fe9b0d26-641c-4b2e-9548-7b9bd6c72104", "ac17b3b7-bb35-47f3-b69c-edb1083ef885"], "durationId": "a5cfdd57-4056-412a-a540-40994c48cd7d", "parent": "bc32d2fa-da91-4b38-8902-45127bdda78a"}}, {"head": {"id": "61a4b4cf-7135-4ee0-b193-f1e26e66fca7", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122239412000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc32d2fa-da91-4b38-8902-45127bdda78a", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154111154315600, "endTime": 154122239633400, "totalTime": 8350686900}, "additional": {"logType": "info", "children": ["db40973c-37dc-4893-9708-118d2b00b5a6", "0f0114b0-9adb-4fb2-ad9a-e9635c2581b0"], "durationId": "8551d2a4-98da-49e4-9290-80a7cd2dc632"}}, {"head": {"id": "8de64235-96e6-4195-8ffb-91982b5932ff", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122248854100, "endTime": 154122250273300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "6d117cd5-0f1e-47c7-8247-e8c7ad8404e7", "logId": "79bb2327-e3ca-40a5-b52a-da897739e2b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d117cd5-0f1e-47c7-8247-e8c7ad8404e7", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122246576600}, "additional": {"logType": "detail", "children": [], "durationId": "8de64235-96e6-4195-8ffb-91982b5932ff"}}, {"head": {"id": "3d0bd134-7f61-47ab-84ea-3204b6b9b178", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122247701300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f14eaae-56e6-4a83-be90-9f18f16be078", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122247817800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cfb4264-0cd7-47c0-a810-cdebdfdb2532", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122248863700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5557ca39-e311-4357-af41-b9e856be3ef6", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122249170300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f3d4777-aa8c-427c-810a-3ca3bb8a6210", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122250119800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbb3bfc0-050e-452b-97ad-a54f88f5aa42", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07845306396484375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122250217400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79bb2327-e3ca-40a5-b52a-da897739e2b0", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122248854100, "endTime": 154122250273300}, "additional": {"logType": "info", "children": [], "durationId": "8de64235-96e6-4195-8ffb-91982b5932ff"}}, {"head": {"id": "7723a978-4a4d-4b24-b49f-45cc2e929a4a", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122260382000, "endTime": 154122422536900}, "additional": {"children": ["d579bc1d-6f47-4ae5-9661-b65a58b27714", "a1d5e892-d932-4240-9efe-a32b3f4b0a4f"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "6ded2f16-c8b0-4274-bec5-09d9677b3eca", "logId": "731c7bdf-3b76-4b9c-b619-8bc9de4801b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ded2f16-c8b0-4274-bec5-09d9677b3eca", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122252444800}, "additional": {"logType": "detail", "children": [], "durationId": "7723a978-4a4d-4b24-b49f-45cc2e929a4a"}}, {"head": {"id": "f2c0026d-f1b0-4c5a-8c14-9e1ef80c6637", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122253327100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "519e2fa0-aa18-4ce1-8c58-be3cbcd781cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122253421500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6135b1fb-54ad-461f-8fdd-93dd3c5c5bb3", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122260394000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f01278d-37b7-4a5b-b718-67ff97887a5d", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122276311700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cfbb3dc-fd70-459c-9c0e-3d81ac260630", "name": "Incremental task entry:default@PackageHap pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122276471700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40433a26-9711-4001-9802-ee33762c0581", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122276545900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2f59243-0e82-44c6-9558-09a3de9e38a8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122276583500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d579bc1d-6f47-4ae5-9661-b65a58b27714", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122278247100, "endTime": 154122280706100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7723a978-4a4d-4b24-b49f-45cc2e929a4a", "logId": "28d0cf5e-57bd-43be-83e6-2705f08278ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44429069-4259-4794-9a40-8bf433c75a2c", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122280545000}, "additional": {"logType": "debug", "children": [], "durationId": "7723a978-4a4d-4b24-b49f-45cc2e929a4a"}}, {"head": {"id": "28d0cf5e-57bd-43be-83e6-2705f08278ae", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122278247100, "endTime": 154122280706100}, "additional": {"logType": "info", "children": [], "durationId": "d579bc1d-6f47-4ae5-9661-b65a58b27714", "parent": "731c7bdf-3b76-4b9c-b619-8bc9de4801b9"}}, {"head": {"id": "a1d5e892-d932-4240-9efe-a32b3f4b0a4f", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122281260300, "endTime": 154122417521900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7723a978-4a4d-4b24-b49f-45cc2e929a4a", "logId": "5e64515b-4f4a-40f4-91bc-f51d69101e9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba5945bf-4d63-4c8d-9ac2-23f1b9a0073f", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122416739300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e64515b-4f4a-40f4-91bc-f51d69101e9a", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122281260300, "endTime": 154122417512300}, "additional": {"logType": "info", "children": [], "durationId": "a1d5e892-d932-4240-9efe-a32b3f4b0a4f", "parent": "731c7bdf-3b76-4b9c-b619-8bc9de4801b9"}}, {"head": {"id": "a3e643b7-eb53-48b0-b9ae-b74015be8393", "name": "entry : default@PackageHap cost memory 1.64398193359375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122422338900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83d656d4-3ed0-4561-8d01-7f895d05ad7c", "name": "runTaskFromQueue task cost before running: 11 s 704 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122422481600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "731c7bdf-3b76-4b9c-b619-8bc9de4801b9", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122260382000, "endTime": 154122422536900, "totalTime": 162078100}, "additional": {"logType": "info", "children": ["28d0cf5e-57bd-43be-83e6-2705f08278ae", "5e64515b-4f4a-40f4-91bc-f51d69101e9a"], "durationId": "7723a978-4a4d-4b24-b49f-45cc2e929a4a"}}, {"head": {"id": "5f1106da-3f66-4759-9174-91c56694b729", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122429325700, "endTime": 154122707494300}, "additional": {"children": ["acc37395-8385-4bc5-abde-391a70d4e2a4", "aa2ff0cd-4f86-4e94-a974-9a94bdb76ef2"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "6a6310b6-9faf-4b2c-a939-bac9ed3fde1f", "logId": "401e0ef0-eeb8-47d8-9493-a0de1a4a327a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a6310b6-9faf-4b2c-a939-bac9ed3fde1f", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122426313500}, "additional": {"logType": "detail", "children": [], "durationId": "5f1106da-3f66-4759-9174-91c56694b729"}}, {"head": {"id": "0affa8b5-5346-4305-b85f-10ecde640053", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122427133900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1439a978-ddf6-446d-b315-cdbda4a5f10f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122427224200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3ed842c-41b9-405e-89f7-4e424945662a", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122429334300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45eae85f-3b81-4330-a61c-23194ca6c2b6", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122430914800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "881ff49e-8eda-462d-a6e8-3ceac4e9d14f", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122431008900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c56885f-7c40-44f6-b1b7-1b52785d943f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122431065700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c16c9a89-ed03-4163-9e11-ffb1a7362ca9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122431096800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acc37395-8385-4bc5-abde-391a70d4e2a4", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122432526800, "endTime": 154122525184900}, "additional": {"children": ["59eeecc4-021b-460a-a35e-548bea3e0ae8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f1106da-3f66-4759-9174-91c56694b729", "logId": "feba2a57-9f8d-4ea2-ab2a-05bddf6c6afc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59eeecc4-021b-460a-a35e-548bea3e0ae8", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122453574900, "endTime": 154122523985300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "acc37395-8385-4bc5-abde-391a70d4e2a4", "logId": "e4e5b86c-d603-48cb-92fe-0541b1381b48"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "acae6f55-08cd-468d-a4e5-a1744ada03fd", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122457192900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95d63003-a89b-49d7-9c8f-b7372abd99ff", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122523528700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4e5b86c-d603-48cb-92fe-0541b1381b48", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122453574900, "endTime": 154122523985300}, "additional": {"logType": "info", "children": [], "durationId": "59eeecc4-021b-460a-a35e-548bea3e0ae8", "parent": "feba2a57-9f8d-4ea2-ab2a-05bddf6c6afc"}}, {"head": {"id": "feba2a57-9f8d-4ea2-ab2a-05bddf6c6afc", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122432526800, "endTime": 154122525184900}, "additional": {"logType": "info", "children": ["e4e5b86c-d603-48cb-92fe-0541b1381b48"], "durationId": "acc37395-8385-4bc5-abde-391a70d4e2a4", "parent": "401e0ef0-eeb8-47d8-9493-a0de1a4a327a"}}, {"head": {"id": "aa2ff0cd-4f86-4e94-a974-9a94bdb76ef2", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122525820500, "endTime": 154122707032700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5f1106da-3f66-4759-9174-91c56694b729", "logId": "b36f6dcb-2b0c-4a61-a95b-09ea6f0d3bc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb6b2517-0ce3-4da6-beb5-3233512428b9", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122527802200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "507ef4c4-85aa-46cf-94ed-fd2420ab9f8d", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122706537800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b36f6dcb-2b0c-4a61-a95b-09ea6f0d3bc2", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122525820500, "endTime": 154122707032700}, "additional": {"logType": "info", "children": [], "durationId": "aa2ff0cd-4f86-4e94-a974-9a94bdb76ef2", "parent": "401e0ef0-eeb8-47d8-9493-a0de1a4a327a"}}, {"head": {"id": "b1e89be0-63a4-4bc4-a5b4-cf61007a8d55", "name": "entry : default@SignHap cost memory 0.9309616088867188", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122707306100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b5c6a2a-0908-493e-938a-63c08d351a48", "name": "runTaskFromQueue task cost before running: 11 s 989 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122707437800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "401e0ef0-eeb8-47d8-9493-a0de1a4a327a", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122429325700, "endTime": 154122707494300, "totalTime": 278088600}, "additional": {"logType": "info", "children": ["feba2a57-9f8d-4ea2-ab2a-05bddf6c6afc", "b36f6dcb-2b0c-4a61-a95b-09ea6f0d3bc2"], "durationId": "5f1106da-3f66-4759-9174-91c56694b729"}}, {"head": {"id": "81b9bba9-d913-47c0-8839-f8787787b8b0", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122711219600, "endTime": 154122716696900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1d40fa2e-3243-4376-b3c2-ee08ce9c8b69", "logId": "5b814ce9-c0d4-4b04-b05a-7c0569fd38d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d40fa2e-3243-4376-b3c2-ee08ce9c8b69", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122709495100}, "additional": {"logType": "detail", "children": [], "durationId": "81b9bba9-d913-47c0-8839-f8787787b8b0"}}, {"head": {"id": "74db0259-d9ba-4a94-94f6-6720262dc6c8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122710427500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13e98c48-8b6a-49ee-a5fd-debbfb9ae87c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122710512800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffb35e12-ea87-4724-84ba-47232537cf7c", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122711227500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d351a68f-68e3-43e7-9b19-e51a613b894c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122716363200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f35bf473-fe58-4be1-9d29-f8f804ffa6a3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122716485200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a30e72b9-0574-4c73-ab88-bc8ede572723", "name": "entry : default@CollectDebugSymbol cost memory 0.24091339111328125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122716586600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87b1d83e-da51-4a60-9a07-ff46e17a0108", "name": "runTaskFromQueue task cost before running: 11 s 999 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122716660000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b814ce9-c0d4-4b04-b05a-7c0569fd38d5", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122711219600, "endTime": 154122716696900, "totalTime": 5424200}, "additional": {"logType": "info", "children": [], "durationId": "81b9bba9-d913-47c0-8839-f8787787b8b0"}}, {"head": {"id": "2341753d-1cfe-4154-aec4-5e7f4df2cfa0", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122718415700, "endTime": 154122718723400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "4722ea32-0846-4679-a4cc-01e6a83968af", "logId": "d13f9734-aece-4667-8c06-21cf10eb9ba8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4722ea32-0846-4679-a4cc-01e6a83968af", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122718370900}, "additional": {"logType": "detail", "children": [], "durationId": "2341753d-1cfe-4154-aec4-5e7f4df2cfa0"}}, {"head": {"id": "7a3c18bd-94d6-4123-874d-342d350f1ed0", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122718422700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3fae5e6-c329-4e05-a906-bad8759e5311", "name": "entry : assembleHap cost memory 0.01377105712890625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122718595500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b9f57f2-0d69-421f-a660-dec49eae3c10", "name": "runTaskFromQueue task cost before running: 12 s ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122718681800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d13f9734-aece-4667-8c06-21cf10eb9ba8", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122718415700, "endTime": 154122718723400, "totalTime": 252600}, "additional": {"logType": "info", "children": [], "durationId": "2341753d-1cfe-4154-aec4-5e7f4df2cfa0"}}, {"head": {"id": "c7aa6d7a-ab52-4903-9090-5584691420a1", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122729823400, "endTime": 154122729852600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e6568f9b-c60d-4386-9ab4-a6e6e0e070fb", "logId": "7c16d9c6-0f1b-4062-92f8-e3244a57e349"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c16d9c6-0f1b-4062-92f8-e3244a57e349", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122729823400, "endTime": 154122729852600}, "additional": {"logType": "info", "children": [], "durationId": "c7aa6d7a-ab52-4903-9090-5584691420a1"}}, {"head": {"id": "f21d6f8d-6d00-42f0-b08e-372336cd7196", "name": "BUILD SUCCESSFUL in 12 s 12 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122729940700}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "5699a8ca-bacb-4c80-82c2-1ee04e3bd11d", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154110718512900, "endTime": 154122730413700}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 45, "second": 52}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "5b5e67c6-84da-4bbb-81b7-5ed04b90fff5", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122730510700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b976874-2d9a-4dd1-88c7-d86882541037", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122730635000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e44f1d4b-906c-4054-8863-534246ef548c", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122731260400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ac5cb28-0e70-43e5-8901-9f4e39f12016", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122731399200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d82e45dd-684c-4018-9c6c-64951f417ef4", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122731440800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dc667e9-fc3a-4789-9a96-756044654262", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122731476600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9f8ce56-d23a-402a-81ce-b0c649126ee1", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122731507500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3428eb7-5df5-4596-af29-1dae539c3a5d", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122732109200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94b9d470-bbc4-4f48-ab91-c6f2cbeb0b65", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122732328300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf9e8603-55e4-4aa6-aef1-2157774697c6", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122732398700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d44ff87-0bfc-49ef-ba39-89a2be812190", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122732435600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7b3cf11-69e7-4428-bf81-7f527ec10e93", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122732465000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64e7dace-faa5-43eb-b887-7e42f7f6a3c1", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122732492700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "325f1264-2cfc-4aa3-a2ef-026b1b9a4079", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122733615700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2186475b-421f-4d8b-be1d-cb153f0f1d42", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122733901600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8c62dc6-0a8a-441d-a235-dc48a6fd4673", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122734118100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "636a2fac-b523-4fdb-9bfa-a0ca49eb5044", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122734187400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58755293-fc23-47b0-a90e-e5938768baeb", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122734225700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e44392c0-19ab-44d2-af86-d5b5f2012f39", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122734253700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6b6b36a-c4c3-40c8-9135-dab9244365f5", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122734287500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a473b682-6125-435e-926a-df4ecd0e09b5", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122734314900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5346edd0-b309-4ff5-92eb-16a6a3aa6bd5", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122737235300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6d1b4d9-f0d2-4fa3-9427-5bba2ee03186", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122737849200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "669d44f0-494b-4a5a-81f7-1ee88666d784", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122738212300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faa6771c-92d1-45e1-bce3-c9386b9a032b", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122738452000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b352b4cf-a275-40c8-bc2c-17382a050588", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122738639500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8336065c-ff7f-46f1-8214-9a566773e627", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122739320300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d310096d-0735-406f-8b50-39157dfe4468", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122739389800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da20737d-3d1c-415c-87c2-2232809f1402", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122739629600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09a0b01f-f6cb-4358-abe7-bdf56c912a40", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122739963100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96c53a83-d996-46d8-9532-4d67fc1e387c", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122740938900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aaab8a9-f52c-4f29-86c1-99e233de1e9f", "name": "Incremental task entry:default@CompileArkTS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122741837100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2072c1b0-2089-4c1d-b2f5-ba1f01cf229d", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122743717700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "543cbc01-ad4a-4122-89e6-f580450af599", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122744328000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a921db7-8f30-4213-b1f6-195720805622", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122744686900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71aa877f-1cc8-4f83-b55f-2dbe4ecd6d17", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122744893300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dca0f57-927c-4bba-8737-8a0dd3ed42b7", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122745107400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87592548-c610-4c48-aa58-fb33d95c7827", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122745882900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdbe55ff-90bb-413c-8f56-c417267447d1", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122746764600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d146d7ab-ee7c-4f56-90d9-6ee9c64dbe45", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122747065700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c88fcec-0dcc-444e-a794-060e7c2b6191", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122747140200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "768dbe61-691d-4aa6-80bb-a95e5bd3cd15", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122747181200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e6b4aa9-3d83-4fc7-bae8-4e2d5d713610", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122748950300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c980fed-f68e-4e97-b6ff-0d1d1e6a84d6", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122749565100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50410551-abe3-462e-922b-663af9394516", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122750050200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6bcdff1-546e-4884-acea-e7f877fa6218", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122758160800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b23675b-d494-4d39-9f1a-55a3790253bb", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122758506000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9db4eea8-a2be-433f-b028-36915ec2dd09", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122758776300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d227c88d-b844-40c6-91f7-af3b2b854c24", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122758999400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c137c323-85c2-485d-9bc7-c2c0c8a0bd81", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122759062000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8acca0ea-d8b7-4491-a46f-f8fc7704ea98", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122759257800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8df30c4f-a2f8-4857-8b00-06776c5a70bb", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122759464700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67d2a725-b748-4005-b1be-301df4cd3bff", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122760365600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e17386a2-9705-458b-a737-2e60491becbe", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122760625100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d6fe67d-da91-42bb-adb9-7e71f856744f", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122760884400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7875108a-9b3e-4af8-8ebb-a546f9f0d608", "name": "Incremental task entry:default@PackageHap post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122761183700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cccb06f1-052b-48dc-8f6c-9e2cbfa3be63", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122761499800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28ebd04f-174f-49fc-8984-239d37416208", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122761859500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cac5bf0-80cb-49ce-ac9a-8d240ae21ee4", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122762178500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8242873-7621-4e00-8fe7-0b42537c5b90", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122762474700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09e6aab3-473c-49c7-b883-df065943b600", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122762548900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b92aa5a-28f6-447c-86a4-577f605b7e98", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122762957800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c298d4dd-6eb5-48a3-adc0-d432bc702427", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122765688700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf7b6d41-0a16-485e-89df-0720f4e5add8", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122766077000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f502ec35-d087-4726-b3d5-e6a9cb906852", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122766637000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65c81ef6-bf1f-46e0-aa18-176a9bfdb360", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 154122766965600}, "additional": {"logType": "debug", "children": []}}], "workLog": []}