"use strict";
import { jsx } from "react/jsx-runtime";
import { history, useIntl } from "@umijs/max";
import { Button, Result } from "antd";
const NoFoundPage = () => /* @__PURE__ */ jsx(
  Result,
  {
    status: "404",
    title: "404",
    subTitle: useIntl().formatMessage({ id: "pages.404.subTitle" }),
    extra: /* @__PURE__ */ jsx(But<PERSON>, { type: "primary", onClick: () => history.push("/"), children: useIntl().formatMessage({ id: "pages.404.buttonText" }) })
  }
);
export default NoFoundPage;
