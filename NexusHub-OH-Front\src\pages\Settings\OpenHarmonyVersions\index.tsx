import React, { useState } from 'react';
import { Card, Table, Button, Space, Input, Modal, Form, message, Popconfirm, Tag, Switch } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';
import {
  getOpenHarmonyVersions,
  createOpenHarmonyVersion,
  updateOpenHarmonyVersion,
  deleteOpenHarmonyVersion,
  type OpenHarmonyVersion,
} from '@/services/openHarmonyVersion';



const OpenHarmonyVersions: React.FC = () => {
  const [searchText, setSearchText] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingVersion, setEditingVersion] = useState<OpenHarmonyVersion | null>(null);
  const [form] = Form.useForm();

  // 获取版本列表
  const { data: versionsData, loading, refresh } = useRequest(
    () => getOpenHarmonyVersions({ keyword: searchText }),
    {
      refreshDeps: [searchText],
    }
  );

  const versions = versionsData?.data || [];

  // 表格列定义
  const columns: ColumnsType<OpenHarmonyVersion> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '版本名称',
      dataIndex: 'version_name',
      key: 'version_name',
      render: (text: string, record: OpenHarmonyVersion) => (
        <Space>
          <span style={{ fontWeight: 'bold' }}>{text}</span>
          {record.is_active && <Tag color="green">活跃</Tag>}
        </Space>
      ),
    },
    {
      title: '版本代码',
      dataIndex: 'version_code',
      key: 'version_code',
      width: 120,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '使用次数',
      dataIndex: 'usage_count',
      key: 'usage_count',
      width: 100,
      render: (count: number) => (
        <Tag color={count > 100 ? 'red' : count > 50 ? 'orange' : 'blue'}>
          {count}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record: OpenHarmonyVersion) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个版本吗？"
            description="删除后无法恢复，请谨慎操作。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  // 处理新增
  const handleAdd = () => {
    setEditingVersion(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 处理编辑
  const handleEdit = (version: OpenHarmonyVersion) => {
    setEditingVersion(version);
    form.setFieldsValue({
      version_name: version.version_name,
      version_code: version.version_code,
      description: version.description,
      is_active: version.is_active,
    });
    setIsModalVisible(true);
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    try {
      await deleteOpenHarmonyVersion(id);
      message.success('删除成功');
      refresh();
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingVersion) {
        await updateOpenHarmonyVersion(editingVersion.id, values);
        message.success('更新成功');
      } else {
        await createOpenHarmonyVersion(values);
        message.success('创建成功');
      }
      
      setIsModalVisible(false);
      refresh();
    } catch (error) {
      message.error('操作失败');
    }
  };

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Input.Search
              placeholder="搜索版本名称"
              allowClear
              style={{ width: 300 }}
              onSearch={handleSearch}
              prefix={<SearchOutlined />}
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增版本
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={versions}
          rowKey="id"
          loading={loading}
          pagination={{
            total: versionsData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingVersion ? '编辑版本' : '新增版本'}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => setIsModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            is_active: true,
          }}
        >
          <Form.Item
            name="version_name"
            label="版本名称"
            rules={[
              { required: true, message: '请输入版本名称' },
              { pattern: /^\d+\.\d+\.\d+$/, message: '版本名称格式应为 x.y.z' },
            ]}
          >
            <Input placeholder="例如：4.0.0" />
          </Form.Item>

          <Form.Item
            name="version_code"
            label="版本代码"
            rules={[
              { required: true, message: '请输入版本代码' },
              { pattern: /^[1-9]\d*$/, message: '版本代码必须是正整数' },
            ]}
          >
            <Input placeholder="例如：40000" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入版本描述"
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="激活" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default OpenHarmonyVersions;