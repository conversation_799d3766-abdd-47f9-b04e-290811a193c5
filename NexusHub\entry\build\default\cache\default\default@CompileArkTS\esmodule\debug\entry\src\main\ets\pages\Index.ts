if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface Index_Params {
    currentTabIndex?: number;
    deviceUtils?;
    tabsController?: TabsController;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { DeviceUtils } from "@normalized:N&&&entry/src/main/ets/utils/DeviceUtils&";
import hilog from "@ohos:hilog";
class Index extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__currentTabIndex = new ObservedPropertySimplePU(0, this, "currentTabIndex");
        this.deviceUtils = DeviceUtils.getInstance();
        this.tabsController = new TabsController();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: Index_Params) {
        if (params.currentTabIndex !== undefined) {
            this.currentTabIndex = params.currentTabIndex;
        }
        if (params.deviceUtils !== undefined) {
            this.deviceUtils = params.deviceUtils;
        }
        if (params.tabsController !== undefined) {
            this.tabsController = params.tabsController;
        }
    }
    updateStateVars(params: Index_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__currentTabIndex.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__currentTabIndex.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __currentTabIndex: ObservedPropertySimplePU<number>;
    get currentTabIndex() {
        return this.__currentTabIndex.get();
    }
    set currentTabIndex(newValue: number) {
        this.__currentTabIndex.set(newValue);
    }
    private deviceUtils;
    private tabsController: TabsController;
    private static readonly DOMAIN = 0x0000;
    private static readonly TAG = 'Index';
    aboutToAppear() {
        hilog.info(Index.DOMAIN, Index.TAG, '主应用界面初始化');
    }
    /**
     * 构建自定义TabBar
     */
    private TabBarBuilder(title: string, icon: Resource, activeIcon: Resource, index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 4 });
            Column.width('100%');
            Column.height('100%');
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(this.currentTabIndex === index ? activeIcon : icon);
            Image.width(24);
            Image.height(24);
            Image.fillColor(this.currentTabIndex === index ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_HINT);
            Image.objectFit(ImageFit.Contain);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor(this.currentTabIndex === index ? Constants.COLORS.PRIMARY : Constants.COLORS.TEXT_HINT);
            Text.fontWeight(this.currentTabIndex === index ? FontWeight.Medium : FontWeight.Normal);
        }, Text);
        Text.pop();
        Column.pop();
    }
    /**
     * 首页内容组件
     */
    private HomePageContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.width('100%');
            Scroll.height('100%');
            Scroll.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Scroll.scrollBar(BarState.Off);
            Scroll.edgeEffect(EdgeEffect.Spring);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 欢迎区域
            Column.create({ space: 16 });
            // 欢迎区域
            Column.width('100%');
            // 欢迎区域
            Column.padding(24);
            // 欢迎区域
            Column.backgroundColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 欢迎区域
            Column.borderRadius(16);
            // 欢迎区域
            Column.margin({ left: 16, right: 16, top: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('欢迎使用 NexusHub');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.EXTRA_LARGE));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('发现和下载优质应用');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM));
            Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Text);
        Text.pop();
        // 欢迎区域
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 快捷操作
            Column.create({ space: 16 });
            // 快捷操作
            Column.width('100%');
            // 快捷操作
            Column.padding(16);
            // 快捷操作
            Column.backgroundColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 快捷操作
            Column.borderRadius(16);
            // 快捷操作
            Column.margin({ left: 16, right: 16, bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('快捷操作');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 16 });
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('搜索应用');
            Button.type(ButtonType.Normal);
            Button.borderRadius(8);
            Button.backgroundColor(Constants.COLORS.PRIMARY);
            Button.fontColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Button.layoutWeight(1);
            Button.onClick(() => {
                this.getUIContext().getRouter().pushUrl({ url: 'pages/SearchPage' });
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('浏览分类');
            Button.type(ButtonType.Normal);
            Button.borderRadius(8);
            Button.backgroundColor({ "id": 125829510, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Button.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Button.layoutWeight(1);
            Button.onClick(() => {
                this.currentTabIndex = 2; // 切换到分类页面
            });
        }, Button);
        Button.pop();
        Row.pop();
        // 快捷操作
        Column.pop();
        Column.pop();
        Scroll.pop();
    }
    /**
     * 精选页面内容组件
     */
    private FeaturedPageContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 24 });
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('精选集合');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.EXTRA_LARGE));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ left: 16, right: 16, top: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 16 });
            Column.width('100%');
            Column.layoutWeight(1);
            Column.justifyContent(FlexAlign.Center);
            Column.padding(24);
            Column.backgroundColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Column.borderRadius(16);
            Column.margin({ left: 16, right: 16, bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('发现精彩应用集合');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM));
            Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('这里将展示各种主题的应用集合，帮助您快速找到感兴趣的应用。');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.textAlign(TextAlign.Center);
            Text.margin({ left: 32, right: 32 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('即将推出');
            Button.type(ButtonType.Normal);
            Button.borderRadius(8);
            Button.backgroundColor({ "id": 125829510, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Button.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Button.enabled(false);
            Button.margin({ top: 16 });
        }, Button);
        Button.pop();
        Column.pop();
        Column.pop();
    }
    /**
     * 分类页面内容组件
     */
    private CategoryPageContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 24 });
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('应用分类');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.EXTRA_LARGE));
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ left: 16, right: 16, top: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 搜索栏
            Row.create({ space: 12 });
            // 搜索栏
            Row.width('100%');
            // 搜索栏
            Row.padding({ left: 16, right: 16 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '搜索分类...' });
            TextInput.layoutWeight(1);
            TextInput.height(40);
            TextInput.borderRadius(20);
            TextInput.backgroundColor({ "id": 125829510, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            TextInput.padding({ left: 16, right: 16 });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild({ type: ButtonType.Circle });
            Button.width(40);
            Button.height(40);
            Button.backgroundColor(Constants.COLORS.PRIMARY);
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777270, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(20);
            Image.height(20);
            Image.fillColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        Button.pop();
        // 搜索栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 内容区域
            Column.create({ space: 16 });
            // 内容区域
            Column.width('100%');
            // 内容区域
            Column.layoutWeight(1);
            // 内容区域
            Column.justifyContent(FlexAlign.Center);
            // 内容区域
            Column.padding(24);
            // 内容区域
            Column.backgroundColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 内容区域
            Column.borderRadius(16);
            // 内容区域
            Column.margin({ left: 16, right: 16, bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('浏览应用分类');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM));
            Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('按照不同类别快速找到您需要的应用，包括游戏、工具、社交、教育等各种分类。');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.textAlign(TextAlign.Center);
            Text.margin({ left: 32, right: 32 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('即将推出');
            Button.type(ButtonType.Normal);
            Button.borderRadius(8);
            Button.backgroundColor({ "id": 125829510, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Button.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Button.enabled(false);
            Button.margin({ top: 16 });
        }, Button);
        Button.pop();
        // 内容区域
        Column.pop();
        Column.pop();
    }
    /**
     * 个人中心页面内容组件
     */
    private ProfilePageContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.width('100%');
            Scroll.height('100%');
            Scroll.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Scroll.scrollBar(BarState.Off);
            Scroll.edgeEffect(EdgeEffect.Spring);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 用户信息区域
            Row.create({ space: 16 });
            // 用户信息区域
            Row.width('100%');
            // 用户信息区域
            Row.padding(20);
            // 用户信息区域
            Row.backgroundColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            // 用户信息区域
            Row.borderRadius(12);
            // 用户信息区域
            Row.margin({ left: 16, right: 16, top: 16 });
            // 用户信息区域
            Row.onClick(() => {
                this.getUIContext().getRouter().pushUrl({ url: 'pages/LoginPage' });
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777268, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(64);
            Image.height(64);
            Image.borderRadius(32);
            Image.backgroundColor({ "id": 125829510, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.fillColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 4 });
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('未登录');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.LARGE));
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('点击登录账号');
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL));
            Text.fontColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777245, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(20);
            Image.height(20);
            Image.fillColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        // 用户信息区域
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 功能菜单
            Column.create({ space: 0 });
            // 功能菜单
            Column.borderRadius(12);
            // 功能菜单
            Column.clip(true);
            // 功能菜单
            Column.margin({ left: 16, right: 16 });
        }, Column);
        this.buildMenuItem.bind(this)({ "id": 16777252, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, '我的应用');
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.color({ "id": 125829159, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Divider.margin({ left: 60 });
        }, Divider);
        this.buildMenuItem.bind(this)({ "id": 16777274, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, '我的收藏');
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Divider.create();
            Divider.color({ "id": 125829159, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Divider.margin({ left: 60 });
        }, Divider);
        this.buildMenuItem.bind(this)({ "id": 16777259, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, '浏览历史');
        // 功能菜单
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 设置菜单
            Column.create();
            // 设置菜单
            Column.borderRadius(12);
            // 设置菜单
            Column.clip(true);
            // 设置菜单
            Column.margin({ left: 16, right: 16, bottom: 16 });
        }, Column);
        this.buildMenuItem.bind(this)({ "id": 16777271, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, '设置');
        // 设置菜单
        Column.pop();
        Column.pop();
        Scroll.pop();
    }
    /**
     * 构建菜单项
     */
    private buildMenuItem(icon: Resource, title: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 16 });
            Row.width('100%');
            Row.height(56);
            Row.padding({ left: 20, right: 20 });
            Row.backgroundColor({ "id": 125829123, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(icon);
            Image.width(24);
            Image.height(24);
            Image.fillColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM));
            Text.fontColor({ "id": 125829210, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777245, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
            Image.width(20);
            Image.height(20);
            Image.fillColor({ "id": 125829216, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Image);
        Row.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Tabs.create({
                barPosition: BarPosition.End,
                controller: this.tabsController,
                index: this.currentTabIndex
            });
            Tabs.width('100%');
            Tabs.height('100%');
            Tabs.barMode(BarMode.Fixed);
            Tabs.barHeight(this.deviceUtils.isTablet() ? 80 : 60);
            Tabs.animationDuration(300);
            Tabs.onChange((index: number) => {
                this.currentTabIndex = index;
            });
            Tabs.backgroundColor({ "id": 125829129, "type": 10001, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" });
        }, Tabs);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.HomePageContent.bind(this)();
            });
            TabContent.tabBar({ builder: () => {
                    this.TabBarBuilder.call(this, '首页', { "id": 16777260, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, { "id": 16777260, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, 0);
                } });
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.FeaturedPageContent.bind(this)();
            });
            TabContent.tabBar({ builder: () => {
                    this.TabBarBuilder.call(this, '精选', { "id": 16777255, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, { "id": 16777255, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, 1);
                } });
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.CategoryPageContent.bind(this)();
            });
            TabContent.tabBar({ builder: () => {
                    this.TabBarBuilder.call(this, '分类', { "id": 16777247, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, { "id": 16777247, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, 2);
                } });
        }, TabContent);
        TabContent.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TabContent.create(() => {
                this.ProfilePageContent.bind(this)();
            });
            TabContent.tabBar({ builder: () => {
                    this.TabBarBuilder.call(this, '我的', { "id": 16777268, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, { "id": 16777268, "type": 20000, params: [], "bundleName": "com.example.nexushub", "moduleName": "entry" }, 3);
                } });
        }, TabContent);
        TabContent.pop();
        Tabs.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "Index";
    }
}
registerNamedRoute(() => new Index(undefined, {}), "", { bundleName: "com.example.nexushub", moduleName: "entry", pagePath: "pages/Index", pageFullPath: "entry/src/main/ets/pages/Index", integratedHsp: "false", moduleType: "followWithHap" });
