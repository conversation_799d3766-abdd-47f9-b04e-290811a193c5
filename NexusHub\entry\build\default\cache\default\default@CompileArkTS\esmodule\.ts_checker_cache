{"runtimeOS": "OpenHarmony", "sdkInfo": "true:18:5.1.0.107:Release", "fileList": {"C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.ability.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.errorCode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.featureAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.particleAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.abilityAccessCtrl.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Ability.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityLifecycleCallback.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.abilityManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityStage.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ActionExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ApplicationStateChangeCallback.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.appManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.appRecovery.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.autoFillManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ChildProcess.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.childProcessManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ChildProcessArgs.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ChildProcessOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Configuration.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ConfigurationConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.contextConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.dataUriUtils.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.dialogRequest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.EnvironmentCallback.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.errorManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.insightIntent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.InsightIntentContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.InsightIntentExecutor.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.OpenLinkOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ShareExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.StartOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionContentSession.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.wantConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.wantAgent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.application.uriPermissionManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.defaultAppManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.launcherBundleManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.overlay.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.continuation.continuationManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.continueManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.package.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.privacyManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.EmbeddedUIExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupConfig.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupConfigEntry.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupListener.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupTask.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.startupManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.sendableContextManager.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.screenLockFileManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AtomicServiceOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.EmbeddableUIAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.PhotoEditorExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.application.d.ts"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\entryability\\EntryAbility.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SettingsPage.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.faultLogger.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hiviewdfx.hiAppEvent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hichecker.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hidebug.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hilog.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hiTraceChain.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hiTraceMeter.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hiviewdfx.jsLeakWatcher.d.ts"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\entryability\\EntryAbility.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\entrybackupability\\EntryBackupAbility.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\HttpClient.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\SearchBar.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SearchPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FeaturedPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\ProfilePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppDetailPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\LoginPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationSettingsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SettingsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyAppsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FavoritesPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyReviewsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HistoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SystemStatusPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HelpPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AboutPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\LocationPickerPage.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.animator.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.AtomicServiceNavigation.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.AtomicServiceSearch.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ArcButton.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.Chip.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.NavPushPathHelper.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ChipGroup.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ComposeListItem.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ComposeTitleBar.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.Counter.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.Dialog.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.DialogV2.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.EditableTitleBar.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ExceptionPrompt.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.Filter.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.FormMenu.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.GridObjectSortComponent.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.Popup.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ProgressButton.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ProgressButtonV2.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SegmentButton.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SelectionMenu.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SelectTitleBar.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SplitLayout.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SubHeader.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SwipeRefresher.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.TabTitleBar.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ToolBar.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ToolBarV2.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.TreeView.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.InterstitialDialogAction.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.componentSnapshot.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.componentUtils.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.dragController.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.drawableDescriptor.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.inspector.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.observer.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.StateManagement.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.shape.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.curves.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.AtomicServiceWeb.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.display.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.font.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.matrix4.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.measure.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.mediaquery.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.PiPWindow.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.pluginComponent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.prompt.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.promptAction.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.router.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.screenshot.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.app.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.configuration.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.mediaquery.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.prompt.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.router.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.ArcList.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.ArcAlphabetIndexer.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.ArcScrollBar.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.theme.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.FoldSplitContainer.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.uiExtension.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.FullScreenLaunchComponent.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.AtomicServiceTabs.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.Prefetcher.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.DownloadFileButton.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.MultiNavigation.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ArcSlider.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.ArcSwiper.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SubHeaderV2.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SegmentButtonV2.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.HalfScreenLaunchComponent.d.ets"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\entryability\\EntryAbility.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\SearchBar.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SearchPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FeaturedPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\ProfilePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppDetailPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\LoginPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationSettingsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SettingsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyAppsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FavoritesPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyReviewsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HistoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SystemStatusPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HelpPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AboutPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\LocationPickerPage.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.cloudData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.cloudExtension.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.commonType.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.dataAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.dataSharePredicates.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.distributedDataObject.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.distributedKVStore.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.preferences.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.relationalStore.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.unifiedDataChannel.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.uniformTypeDescriptor.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.uniformDataStruct.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.ValuesBucket.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.sendablePreferences.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.sendableRelationalStore.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.intelligence.d.ts"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\entryability\\EntryAbility.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SearchPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\ProfilePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppDetailPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\LoginPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationSettingsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SettingsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HistoryPage.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets": {"mtimeMs": 1750167601217.56, "children": [], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\entryability\\EntryAbility.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\Index.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\HttpClient.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\AppCard.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\SearchBar.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\NavigationBar.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SearchPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FeaturedPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\ProfilePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppDetailPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\LoginPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationSettingsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SettingsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyAppsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FavoritesPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyReviewsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HistoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SystemStatusPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HelpPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AboutPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\LocationPickerPage.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\entryability\\EntryAbility.ets": {"mtimeMs": 1750139884088.1106, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.ability.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityHelper.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityHelper.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityOperation.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityResult.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\abilityResult.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\connectOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\startAbilityParameter.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.errorCode.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.featureAbility.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\startAbilityParameter.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\abilityResult.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\appVersionInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityHelper.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\connectOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\processInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityOperation.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.particleAbility.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\startAbilityParameter.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityHelper.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationRequest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\connectOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.abilityAccessCtrl.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\permissions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\security\\PermissionRequestResult.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Ability.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Configuration.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ExtensionAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityConstant.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Ability.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.EnvironmentCallback.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityStage.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityLifecycleCallback.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationContext.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.abilityManager.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityStage.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AbilityStageContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Configuration.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ActionExtensionAbility.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionAbility.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ApplicationStateChangeCallback.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationContext.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.appManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationStateObserver.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ProcessInformation.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ProcessInformation.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.appRecovery.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.autoFillManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ChildProcess.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ChildProcessArgs.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.childProcessManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ChildProcessArgs.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ChildProcessOptions.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ChildProcessArgs.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ChildProcess.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.childProcessManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ChildProcessOptions.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.childProcessManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AbilityStageContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\BaseContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\FormExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\EventHub.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityHelper.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\abilityResult.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AbilityStartCallback.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\connectOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\VpnExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\EmbeddableUIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\PhotoEditorExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIServiceProxy.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIServiceExtensionConnectCallback.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.sendableContextManager.d.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Configuration.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ConfigurationConstant.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Ability.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.EnvironmentCallback.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AbilityStageContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityStage.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ConfigurationConstant.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Configuration.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIExtensionContext.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.contextConstant.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.StartOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.StateManagement.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.dataUriUtils.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.dialogRequest.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.EnvironmentCallback.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Configuration.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationContext.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.errorManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ErrorObserver.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\LoopObserver.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ExtensionAbility.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Ability.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.PhotoEditorExtensionAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.insightIntent.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.InsightIntentExecutor.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.InsightIntentContext.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.InsightIntentExecutor.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.InsightIntentExecutor.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.insightIntent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.InsightIntentContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionContentSession.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.OpenLinkOptions.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIExtensionContext.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ShareExtensionAbility.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionAbility.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.StartOptions.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.contextConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AtomicServiceOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIExtensionContext.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIAbility.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Ability.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.rpc.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityLifecycleCallback.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.EmbeddableUIAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionAbility.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionContentSession.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ActionExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ShareExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.EmbeddedUIExtensionAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionContentSession.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\abilityResult.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AbilityStartCallback.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.uiExtension.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.InsightIntentExecutor.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.PhotoEditorExtensionAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.featureAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\startAbilityParameter.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\abilityResult.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.dialogRequest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.unifiedDataChannel.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.particleAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.wantAgent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\wantAgent\\wantAgentInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.wantAgent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\wantAgent\\triggerInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityStage.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.appRecovery.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.InsightIntentContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.PhotoEditorExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.FormMenu.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.pluginComponent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.account.appAccount.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.pasteboard.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.vpnExtension.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.VpnExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\embedded_component.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.wantConstant.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.wantAgent.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\wantAgent\\wantAgentInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\wantAgent\\triggerInfo.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\wantAgent\\wantAgentInfo.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.application.uriPermissionManager.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ApplicationInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\Metadata.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ElementName.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\AbilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\BundleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\HapModuleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ExtensionAbilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\Skill.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\AbilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ApplicationInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\BundleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\HapModuleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ExtensionAbilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ProcessInformation.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.StartOptions.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\applicationInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\abilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\bundleInfo.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\abilityInfo.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.defaultAppManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.launcherBundleManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\LauncherAbilityInfo.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.overlay.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\OverlayModuleInfo.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.continuation.continuationManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\continuation\\continuationResult.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\continuation\\continuationExtraParams.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\continuation\\continuationExtraParams.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.continueManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.package.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.privacyManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\permissions.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.EmbeddedUIExtensionAbility.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionAbility.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupConfig.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupListener.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupConfigEntry.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.startupManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupConfigEntry.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupConfig.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupListener.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupConfig.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupTask.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AbilityStageContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.startupManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupConfig.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.sendableContextManager.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\SendableContext.d.ets"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.screenLockFileManager.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AtomicServiceOptions.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.StartOptions.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.FullScreenLaunchComponent.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.HalfScreenLaunchComponent.d.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.EmbeddableUIAbility.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\EmbeddableUIAbilityContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.PhotoEditorExtensionAbility.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\PhotoEditorExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionContentSession.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.application.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityHelper.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\data\\rdb\\resultSet.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityOperation.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityResult.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.dataAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.rdb.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.ability.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.featureAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.particleAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.settings.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityOperation.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.featureAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.dataAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.rdb.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.ability.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityHelper.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityResult.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.ability.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityHelper.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\abilityResult.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.ability.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.featureAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AbilityStartCallback.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\PhotoEditorExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionContentSession.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\connectOptions.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ElementName.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.rpc.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.ability.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.featureAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.particleAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\startAbilityParameter.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.ability.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.featureAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.particleAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityHelper.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.featureAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.resourceManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.rpc.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.mediaquery.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.observer.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.appManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.promptAction.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.router.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.componentSnapshot.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.dragController.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.unifiedDataChannel.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimodalInput.pointer.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.rdb.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.particleAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notification.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notificationManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.wantAgent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.wantAgent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.abilityAccessCtrl.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionContentSession.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.uiExtension.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.childProcessManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.errorManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.InsightIntentContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.defaultAppManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.overlay.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.continuation.continuationManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.continueManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupListener.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.faultLogger.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hiviewdfx.hiAppEvent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.AtomicServiceNavigation.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.FormMenu.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.AtomicServiceWeb.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.display.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.PiPWindow.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.pluginComponent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.prompt.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.FoldSplitContainer.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.FullScreenLaunchComponent.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.MultiNavigation.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.HalfScreenLaunchComponent.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.distributedDataObject.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.distributedKVStore.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.preferences.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.relationalStore.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.sendablePreferences.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.cloudSync.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.fs.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.util.stream.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.events.emitter.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.hash.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.picker.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.securityLabel.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.statvfs.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.storageStatistics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.account.appAccount.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.account.distributedAccount.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.account.osAccount.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.pasteboard.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.power.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.print.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.request.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.runningLock.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.screenLock.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.settings.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.systemDateTime.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.systemTime.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.thermal.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.usbManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.wallpaper.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.zlib.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.commonEventManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\commonEvent\\commonEventSubscriber.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.resourceschedule.systemload.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.http.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.socket.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.security.cert.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.security.cryptoFramework.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.mdns.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.statistics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.webSocket.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneResources.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\embedded_component.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\nav_destination.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\navigation.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\pattern_lock.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.web.webview.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\data\\rdb\\resultSet.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityHelper.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.rdb.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.dataAbility.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.rdb.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityHelper.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityOperation.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.rdb.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\data\\rdb\\resultSet.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\BaseContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityHelper.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityOperation.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.dataAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\appVersionInfo.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.featureAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\context.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\context.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\applicationInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\processInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\elementName.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\BaseContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\hapModuleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\appVersionInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\abilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.featureAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\processInfo.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.featureAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\context.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\BaseContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ConfigurationConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.featureAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityLifecycleCallback.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.uiExtension.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.InsightIntentExecutor.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.FoldSplitContainer.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.picker.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\folder_stack.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\navigation.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\applicationInfo.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\moduleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\customizeData.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\abilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\bundleInfo.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\elementName.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\context.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\BaseContext.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.rdb.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.PiPWindow.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.distributedDataObject.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.distributedKVStore.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.preferences.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.relationalStore.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.sendablePreferences.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.request.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\hapModuleInfo.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\abilityInfo.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\bundleInfo.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\abilityInfo.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\applicationInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\customizeData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\app\\context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\hapModuleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\bundleInfo.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\moduleInfo.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\applicationInfo.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\customizeData.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\applicationInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\abilityInfo.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.colorSpaceManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.resourceManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.rpc.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.drawableDescriptor.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.StartOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.drawing.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.componentSnapshot.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.unifiedDataChannel.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\PhotoEditorExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimodalInput.pointer.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationRequest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.screenshot.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.uniformDataStruct.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.pasteboard.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.wallpaper.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.web.webview.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\bundleInfo.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\abilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\applicationInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundle\\hapModuleInfo.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.colorSpaceManager.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.display.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.resourceManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\global\\rawFileDescriptor.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\global\\resource.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.drawableDescriptor.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.rpc.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\connectOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.account.appAccount.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\global\\rawFileDescriptor.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.resourceManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\global\\resource.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.resourceManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ApplicationInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.drawing.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\units.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.drawableDescriptor.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.resourceManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\image.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ElementName.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\connectOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\LauncherAbilityInfo.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.font.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.mediaquery.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.inspector.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.observer.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.promptAction.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.router.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.componentUtils.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.animator.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.animator.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.measure.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.componentSnapshot.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.dragController.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimodalInput.pointer.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\BuilderNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\FrameNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ComponentContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NodeController.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\XComponentNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.autoFillManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.InterstitialDialogAction.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\BuilderNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\BuilderNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NodeController.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\FrameNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\FrameNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RenderNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\XComponentNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Content.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ComponentContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NodeContent.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ArcButton.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ProgressButtonV2.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ToolBar.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ToolBarV2.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SegmentButtonV2.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\content_slot.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\node_container.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.font.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.mediaquery.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.inspector.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.observer.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.promptAction.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\action_sheet.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\alert_dialog.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.router.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.componentUtils.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.animator.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.measure.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.componentSnapshot.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.dragController.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.unifiedDataChannel.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimodalInput.pointer.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\AbilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\abilityResult.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\connectOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\HapModuleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.StartOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.OpenLinkOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Configuration.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.dialogRequest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AbilityStartCallback.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AtomicServiceOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ConfigurationConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIServiceProxy.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIServiceExtensionConnectCallback.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.observer.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\EmbeddableUIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notificationManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.appRecovery.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.vpn.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\AbilityInfo.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ApplicationInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\Metadata.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\Skill.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\HapModuleInfo.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\HapModuleInfo.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\AbilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ExtensionAbilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\Metadata.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\BundleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AbilityStageContext.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ApplicationInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.resourceManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\BaseContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\EventHub.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.contextConstant.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AbilityStageContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.abilityAccessCtrl.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.application.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.picker.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.print.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.settings.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.mdns.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AbilityStartCallback.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\abilityResult.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionContentSession.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIServiceProxy.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIExtensionContext.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIServiceExtensionConnectCallback.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIExtensionContext.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ApplicationInfo.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\Metadata.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\global\\resource.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\AbilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\BundleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ExtensionAbilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\LauncherAbilityInfo.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\Metadata.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\AbilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ApplicationInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\HapModuleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ExtensionAbilityInfo.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\Skill.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\AbilityInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ExtensionAbilityInfo.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\BundleInfo.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ApplicationInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\HapModuleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ExtensionAbilityInfo.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ApplicationInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\Metadata.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\Skill.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\HapModuleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ExtensionContext.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\EventHub.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationContext.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityLifecycleCallback.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.EnvironmentCallback.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ApplicationStateChangeCallback.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ProcessInformation.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ConfigurationConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.application.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ProcessInformation.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.appManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.bundleManager.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.appManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationStateObserver.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AppStateData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AbilityStateData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ProcessData.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.appManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AppStateData.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationStateObserver.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AbilityStateData.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationStateObserver.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ProcessData.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ApplicationStateObserver.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\BuilderNode.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\FrameNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ComponentContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\XComponentNode.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NodeController.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\FrameNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.PiPWindow.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\FrameNode.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RenderNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ComponentContent.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\BuilderNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NodeController.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\XComponentNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NodeContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.PiPWindow.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\canvas.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.drawing.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.common2D.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\global\\resource.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\BuilderNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\FrameNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RenderNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NodeController.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\particle.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\units.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RenderNode.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\FrameNode.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\XComponentNode.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\BuilderNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\FrameNode.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Content.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ComponentContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NodeContent.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ComponentContent.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\BuilderNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Content.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\FrameNode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NodeContent.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Content.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\FrameNode.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.drawing.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.common2D.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\global\\resource.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\canvas.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.text.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.common2D.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.drawing.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.text.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.unifiedDataChannel.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.dragController.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.pasteboard.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIExtensionContext.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\abilityResult.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\connectOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.StartOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AtomicServiceOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.OpenLinkOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.ConfigurationConstant.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIServiceProxy.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIServiceExtensionConnectCallback.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\AbilityStageContext.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\HapModuleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Configuration.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AbilityStage.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.appstartup.StartupTask.d.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ExtensionContext.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\HapModuleInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Configuration.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ExtensionAbilityInfo.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\FormExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\VpnExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\PhotoEditorExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.BackupExtensionContext.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\FormExtensionContext.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ExtensionContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\VpnExtensionContext.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ExtensionContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.vpnExtension.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.VpnExtensionAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\EmbeddableUIAbilityContext.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.EmbeddableUIAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\PhotoEditorExtensionContext.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\abilityResult.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.PhotoEditorExtensionAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationRequest.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notification.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notificationManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.wantAgent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationActionButton.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationTemplate.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationFlags.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.ability.particleAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notification.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notificationManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notification.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationRequest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationSlot.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationRequest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationSlot.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notificationManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\NotificationCommonDef.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationActionButton.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationRequest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationRequest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationSlot.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationTemplate.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationUserInput.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationRequest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationSlot.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.wantAgent.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\wantAgent\\wantAgentInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\wantAgent\\triggerInfo.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationRequest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationActionButton.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\wantAgent\\wantAgentInfo.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationContent.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notification.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\global\\resource.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notificationManager.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationRequest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notificationManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationActionButton.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationUserInput.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.wantAgent.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationRequest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notificationManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationTemplate.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationRequest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notificationManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationFlags.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationRequest.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationSlot.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notification.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notificationManager.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notification.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notificationManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\NotificationCommonDef.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notificationManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationUserInput.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.notificationManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\notification\\notificationActionButton.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\wantAgent\\wantAgentInfo.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.wantAgent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.wantAgent.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.wantAgent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.wantAgent.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\wantAgent\\triggerInfo.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.wantAgent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.wantAgent.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\permissions.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.abilityAccessCtrl.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.privacyManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\security\\PermissionRequestResult.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.abilityAccessCtrl.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.uiExtension.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.UIExtensionContentSession.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ErrorObserver.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.errorManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\LoopObserver.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.errorManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\LauncherAbilityInfo.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ApplicationInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\ElementName.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.launcherBundleManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\bundleManager\\OverlayModuleInfo.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.bundle.overlay.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\continuation\\continuationResult.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.continuation.continuationManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\continuation\\continuationExtraParams.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.continuation.continuationManager.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.continuation.continuationManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\SendableContext.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\arkts\\@arkts.lang.d.ets"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.sendableContextManager.d.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\arkts\\@arkts.lang.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\SendableContext.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.sendableRelationalStore.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\arkts\\@arkts.collections.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.sendablePreferences.d.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.faultLogger.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hiviewdfx.hiAppEvent.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hichecker.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hidebug.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hilog.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hiTraceChain.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hiTraceMeter.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.hiviewdfx.jsLeakWatcher.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.AtomicServiceNavigation.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.AtomicServiceSearch.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SubHeader.d.ets"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ArcButton.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.Chip.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ChipGroup.d.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.NavPushPathHelper.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ChipGroup.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.Chip.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ComposeListItem.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ComposeTitleBar.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.Counter.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.Dialog.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.theme.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.DialogV2.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.EditableTitleBar.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ExceptionPrompt.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.Filter.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.FormMenu.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.form.formBindingData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.GridObjectSortComponent.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.Popup.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ProgressButton.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ProgressButtonV2.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SegmentButton.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SelectionMenu.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SelectTitleBar.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SplitLayout.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SubHeader.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.AtomicServiceSearch.d.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SwipeRefresher.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.TabTitleBar.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ToolBar.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ToolBarV2.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.TreeView.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.InterstitialDialogAction.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.StateManagement.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.contextConstant.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.shape.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.curves.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.AtomicServiceWeb.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.display.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.colorSpaceManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.hdrCapability.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.FoldSplitContainer.d.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.matrix4.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\image.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.PiPWindow.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\BaseContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NodeController.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\FrameNode.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.pluginComponent.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.prompt.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.screenshot.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.app.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.configuration.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.mediaquery.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.prompt.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.router.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.ArcList.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.ArcAlphabetIndexer.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.ArcScrollBar.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.theme.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.Dialog.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\with_theme.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.FoldSplitContainer.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.display.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.FullScreenLaunchComponent.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AtomicServiceOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.AtomicServiceTabs.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\CommonModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\AlphabetIndexerModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\BlankModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ButtonModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\CalendarPickerModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\CheckboxModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\CheckboxGroupModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ColumnModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ColumnSplitModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\CounterModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\DataPanelModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\DatePickerModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\DividerModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\GaugeModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\GridModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\GridColModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\GridItemModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\GridRowModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\HyperlinkModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ImageAnimatorModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ImageModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SymbolGlyphModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ImageSpanModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\LineModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ListModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ListItemModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ListItemGroupModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\LoadingProgressModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\MarqueeModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\MenuModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\MenuItemModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NavDestinationModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NavigationModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NavigatorModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NavRouterModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\PanelModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\PathModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\PatternLockModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\PolygonModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\PolylineModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ProgressModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\QRCodeModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RadioModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RatingModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RectModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RefreshModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RichEditorModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RowModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RowSplitModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ScrollModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SearchModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SelectModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ShapeModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SideBarContainerModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SliderModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SpanModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\StackModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\StepperItemModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SwiperModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TabsModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TextAreaModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TextModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TextClockModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TextInputModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TextPickerModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TextTimerModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TimePickerModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ToggleModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\VideoModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\WaterFlowModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\AttributeUpdater.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ContainerSpanModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SymbolSpanModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ParticleModifier.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SubHeader.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.Chip.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ChipGroup.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ToolBar.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ToolBarV2.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SubHeaderV2.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SegmentButtonV2.d.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.Prefetcher.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.DownloadFileButton.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.MultiNavigation.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.ArcSlider.d.ets": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.ArcSwiper.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SubHeaderV2.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.SegmentButtonV2.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.atomicservice.HalfScreenLaunchComponent.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.AtomicServiceOptions.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\CommonModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\tabs.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\AlphabetIndexerModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\BlankModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ButtonModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\CalendarPickerModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\CheckboxModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\CheckboxGroupModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ColumnModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ColumnSplitModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\CounterModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\DataPanelModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\DatePickerModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\DividerModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\GaugeModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\GridModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\GridColModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\GridItemModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\GridRowModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\HyperlinkModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ImageAnimatorModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ImageModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SymbolGlyphModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ImageSpanModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\LineModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ListModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ListItemModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ListItemGroupModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\LoadingProgressModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\MarqueeModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\MenuModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\MenuItemModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NavDestinationModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NavigationModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NavigatorModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\NavRouterModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\PanelModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\PathModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\PatternLockModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\PolygonModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\PolylineModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ProgressModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\QRCodeModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RadioModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RatingModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RectModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RefreshModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RichEditorModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RowModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\RowSplitModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ScrollModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SearchModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SelectModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ShapeModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SideBarContainerModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SliderModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SpanModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\StackModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\StepperItemModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SwiperModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TabsModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TextAreaModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TextModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TextClockModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TextInputModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TextPickerModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TextTimerModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\TimePickerModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ToggleModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\VideoModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\WaterFlowModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\AttributeUpdater.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ContainerSpanModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SymbolSpanModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ParticleModifier.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.modifier.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.form.formBindingData.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.advanced.FormMenu.d.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.hdrCapability.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.display.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.cloudData.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.commonType.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.cloudExtension.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.commonType.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.cloudData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.distributedDataObject.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.dataSharePredicates.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.ValuesBucket.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.distributedDataObject.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\BaseContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.commonType.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.distributedKVStore.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\BaseContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.preferences.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\BaseContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.relationalStore.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\BaseContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.sendableRelationalStore.d.ets"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.sendableRelationalStore.d.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.uniformTypeDescriptor.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.uniformDataStruct.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.ValuesBucket.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.dataSharePredicates.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.sendablePreferences.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\BaseContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\arkts\\@arkts.lang.d.ets"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.sendableRelationalStore.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\arkts\\@arkts.collections.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\arkts\\@arkts.lang.d.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.relationalStore.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.relationalStore.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.intelligence.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\arkts\\@arkts.collections.d.ets": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\arkts\\@arkts.lang.d.ets"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.sendableRelationalStore.d.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.application.BackupExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.BackupExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.cloudSync.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.cloudSyncManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.environment.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.fileAccess.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.fileuri.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.fs.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.hash.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.picker.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.securityLabel.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.statvfs.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.storageStatistics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.keyManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.fileshare.d.ts"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\entrybackupability\\EntryBackupAbility.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\entrybackupability\\EntryBackupAbility.ets": {"mtimeMs": 1749456513050.5852, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.application.BackupExtensionAbility.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.BackupExtensionContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.BackupExtensionContext.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\ExtensionContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.application.BackupExtensionAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.cloudSync.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.cloudSyncManager.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.environment.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.fileAccess.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.fileuri.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.uri.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.fs.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.util.stream.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.hash.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.util.stream.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.picker.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.securityLabel.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.statvfs.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.storageStatistics.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.keyManager.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.fileshare.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.CoreFileKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.uri.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.fileuri.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.util.stream.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.events.emitter.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.fs.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.file.hash.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.events.emitter.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.util.stream.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets": {"mtimeMs": 1750171108944.7676, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\App.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\Category.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\Banner.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\AppCard.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\SearchBar.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\NavigationBar.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\Index.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SearchPage.ets": {"mtimeMs": 1750171159016.0396, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\App.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\Category.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\AppCard.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\SearchBar.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\Index.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FeaturedPage.ets": {"mtimeMs": 1750171081269.7742, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\FeaturedCollection.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\Index.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryListPage.ets": {"mtimeMs": 1750169419239.9187, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\Category.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\SearchBar.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\Index.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\ProfilePage.ets": {"mtimeMs": 1750167601189.2737, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\User.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\Index.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\NavigationBar.ets": {"mtimeMs": 1750167601149.977, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\Index.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets": {"mtimeMs": 1750069431741.1272, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\Index.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\AppCard.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\SearchBar.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\NavigationBar.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SearchPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FeaturedPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\ProfilePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppDetailPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\LoginPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SettingsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyAppsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FavoritesPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyReviewsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HistoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HelpPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AboutPage.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\Index.ets": {"mtimeMs": 1750167601217.0225, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SearchPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FeaturedPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\ProfilePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\NavigationBar.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\App.ets": {"mtimeMs": 1750170899977.0383, "children": [], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\AppCard.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SearchPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppDetailPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyAppsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FavoritesPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HistoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\FeaturedCollection.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\Category.ets": {"mtimeMs": 1750164682152.885, "children": [], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SearchPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryPage.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets": {"mtimeMs": 1750171006400.8962, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\HttpClient.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\App.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\Category.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\Banner.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\User.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\FeaturedCollection.ets"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SearchPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FeaturedPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\ProfilePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppDetailPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\LoginPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationSettingsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyAppsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FavoritesPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyReviewsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HistoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SystemStatusPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\LocationPickerPage.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\AppCard.ets": {"mtimeMs": 1750167601208.1362, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\App.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SearchPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppListPage.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\SearchBar.ets": {"mtimeMs": 1750167601209.1367, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SearchPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryListPage.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets": {"mtimeMs": 1750167601209.1367, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SearchPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FeaturedPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\ProfilePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppDetailPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppListPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationSettingsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyAppsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FavoritesPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyReviewsPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HistoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SystemStatusPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HelpPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AboutPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\LocationPickerPage.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.account.appAccount.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.customization.customConfig.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.account.distributedAccount.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.account.osAccount.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.PrintExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.batteryInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.deviceAttest.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.deviceInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.pasteboard.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.power.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.print.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.request.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.request.cacheDownload.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.runningLock.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.screenLock.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.settings.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.systemDateTime.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.systemTime.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.thermal.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.usb.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.usbManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.wallpaper.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.zlib.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.commonEventManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.events.emitter.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.battery.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.brightness.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.device.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.request.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.resourceschedule.systemload.d.ts"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\HttpClient.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryPage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SettingsPage.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.account.appAccount.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.rpc.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.customization.customConfig.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.account.distributedAccount.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.account.osAccount.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.account.osAccount.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.account.distributedAccount.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.PrintExtensionAbility.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.application.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.batteryInfo.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.deviceAttest.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.deviceInfo.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.pasteboard.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.unifiedDataChannel.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.power.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.print.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.web.webview.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.request.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\BaseContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.request.cacheDownload.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.runningLock.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.screenLock.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.settings.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\ability\\dataAbilityHelper.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.systemDateTime.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.systemTime.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.thermal.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.usb.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.usbManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.wallpaper.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.zlib.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.commonEventManager.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\commonEvent\\commonEventData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\commonEvent\\commonEventSubscriber.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\commonEvent\\commonEventSubscribeInfo.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\commonEvent\\commonEventPublishData.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.battery.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.brightness.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.device.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@system.request.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.resourceschedule.systemload.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.application.Want.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.PrintExtensionAbility.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\commonEvent\\commonEventData.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.commonEventManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\commonEvent\\commonEventSubscriber.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\commonEvent\\commonEventSubscribeInfo.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.commonEventManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\commonEvent\\commonEventSubscribeInfo.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.commonEventManager.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\commonEvent\\commonEventSubscriber.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\commonEvent\\commonEventPublishData.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.commonEventManager.d.ts"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\HttpClient.ets": {"mtimeMs": 1750167601217.56, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryPage.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\User.ets": {"mtimeMs": 1750012515863.613, "children": [], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\ProfilePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\LoginPage.ets"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\FeaturedCollection.ets": {"mtimeMs": 1750170836764.5774, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\App.ets"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FeaturedPage.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.ethernet.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.http.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.mdns.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.policy.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.sharing.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.socket.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.statistics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.vpn.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.webSocket.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.vpnExtension.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.networkSecurity.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.VpnExtensionAbility.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.netFirewall.d.ts"], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\HttpClient.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryPage.ets"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.http.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.socket.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.http.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.socket.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.ethernet.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.mdns.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.policy.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.sharing.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.statistics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.vpn.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.webSocket.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.vpnExtension.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.ethernet.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.http.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.mdns.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.policy.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.sharing.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.socket.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.security.cert.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.statistics.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.vpn.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\UIAbilityContext.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.webSocket.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.vpnExtension.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.connection.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\VpnExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.networkSecurity.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.VpnExtensionAbility.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\VpnExtensionContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.netFirewall.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.NetworkKit.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.security.cert.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.security.cryptoFramework.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.net.socket.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.web.webview.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.security.cryptoFramework.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.security.cert.d.ts"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppDetailPage.ets": {"mtimeMs": 1750171442403.068, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\App.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\CategoryPage.ets": {"mtimeMs": 1750171137004.6377, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\App.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\Category.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\HttpClient.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\AppCard.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AppListPage.ets": {"mtimeMs": 1750171475395.791, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\AppCard.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\App.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\LoginPage.ets": {"mtimeMs": 1750139884129.3984, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\User.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationPage.ets": {"mtimeMs": 1750167601189.2737, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\App.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\NotificationSettingsPage.ets": {"mtimeMs": 1750139884130.3743, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SettingsPage.ets": {"mtimeMs": 1750140677693.548, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.BasicServicesKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.AbilityKit.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyAppsPage.ets": {"mtimeMs": 1750167601189.2737, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\App.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\FavoritesPage.ets": {"mtimeMs": 1750167601173.5078, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\App.ets"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\MyReviewsPage.ets": {"mtimeMs": 1750140695700.5967, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HistoryPage.ets": {"mtimeMs": 1750167601217.0225, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\App.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkData.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\SystemStatusPage.ets": {"mtimeMs": 1750140523177.2827, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HelpPage.ets": {"mtimeMs": 1750140576512.6572, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\AboutPage.ets": {"mtimeMs": 1750140595012.5608, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\DeviceUtils.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\LocationPickerPage.ets": {"mtimeMs": 1750167601217.56, "children": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\utils\\Constants.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\components\\LoadingView.ets", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.ArkUI.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\kits\\@kit.PerformanceAnalysisKit.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\action_sheet.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.promptAction.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.promptAction.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\alert_dialog.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.promptAction.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\canvas.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.drawing.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\FrameNode.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimodalInput.intentionCode.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.uiEffect.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\application\\Context.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimodalInput.pointer.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.unifiedDataChannel.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.unifiedDataChannel.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.uniformTypeDescriptor.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.data.unifiedDataChannel.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimodalInput.intentionCode.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ImageModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\SymbolGlyphModifier.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.shape.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.shape.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.shape.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.shape.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.observer.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.observer.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.observer.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.UIContext.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.uiEffect.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.uiEffect.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\ComponentContent.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.theme.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.promptAction.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.scene.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\ScenePostProcessSettings.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneTypes.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneResources.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneNodes.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\Scene.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\component3d.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\component3d.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.scene.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\ScenePostProcessSettings.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.scene.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneNodes.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneTypes.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.scene.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneResources.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneNodes.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\Scene.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneResources.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneTypes.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.scene.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneNodes.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\Scene.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneNodes.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneResources.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneTypes.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\ScenePostProcessSettings.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.scene.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\Scene.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\Scene.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneResources.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneNodes.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\graphics3d\\SceneTypes.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.scene.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\content_slot.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\embedded_component.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.app.ability.Want.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\folder_stack.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\image.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.drawableDescriptor.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.drawing.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.drawing.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.matrix4.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\nav_destination.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\navigation.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.window.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\node_container.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.node.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\particle.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\pattern_lock.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\tabs.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\CommonModifier.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.text.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.drawing.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.common2D.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\text_common.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\text_common.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.text.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.text.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.text.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.text.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.graphics.text.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.intl.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\time_picker.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\time_picker.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.intl.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\with_theme.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.arkui.theme.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\units.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\global\\resource.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\arkui\\Graphics.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.web.webview.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.base.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.security.cert.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.multimedia.image.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.print.d.ts", "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.web.netErrorList.d.ts"], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\web.d.ts"], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\build-tools\\ets-loader\\declarations\\web.d.ts": {"mtimeMs": ************, "children": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.web.webview.d.ts"], "parent": [], "error": false}, "C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.web.netErrorList.d.ts": {"mtimeMs": ************, "children": [], "parent": ["C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\ets\\api\\@ohos.web.webview.d.ts"], "error": false}, "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\models\\Banner.ets": {"mtimeMs": 1750168411657.3833, "children": [], "parent": ["C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\pages\\HomePage.ets", "C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets\\services\\ApiService.ets"], "error": false}}}