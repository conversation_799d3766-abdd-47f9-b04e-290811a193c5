/**
 * 应用数据模型
 */
export interface AppModel {
    id: number;
    created_at: string;
    updated_at: string;
    deleted_at?: string;
    name: string;
    package_name: string;
    description: string;
    short_description: string;
    icon: string;
    category_id: number;
    category_name: string;
    developer_id: number;
    developer_name: string;
    version: string;
    version_code: number;
    min_sdk_version: number;
    target_sdk_version: number;
    size: number;
    download_url: string;
    download_count: number;
    rating: number;
    review_count: number;
    screenshots: string[];
    permissions: string[];
    tags: string[];
    changelog: string;
    privacy_policy: string;
    support_email: string;
    website: string;
    status: string;
    is_featured: boolean;
    is_editor_choice: boolean;
    is_top: boolean;
    featured_at?: string;
    published_at: string;
    review_status: string;
    review_reason: string;
    reviewed_at: string;
    reviewer_id: number;
    // 客户端扩展属性
    isInstalled?: boolean;
    downloadTime?: string;
}
/**
 * 应用详情模型（包含更多详细信息）
 */
export interface AppDetailModel extends AppModel {
    versions: AppVersionModel[];
    // 后端实际返回的字段名（用于兼容性）
    average_rating?: number; // 平均评分
    current_version?: string; // 当前版本
    category?: string; // 分类名称
}
/**
 * 应用版本模型
 */
export interface AppVersionModel {
    id: number;
    application_id: number;
    version_name: string;
    version_code: number;
    change_log: string;
    package_url: string;
    size: number;
    status: string;
    min_open_harmony_os_ver: string;
    released_at: string;
    download_count: number;
    incremental_update: boolean;
    created_at: string;
    updated_at: string;
}
/**
 * 应用截图模型
 */
export interface AppScreenshotModel {
    id: number;
    application_id: number;
    image_url: string;
    sort_order: number;
    created_at: string;
}
/**
 * 应用列表数据模型（直接匹配后端API文档）
 */
export interface AppListData {
    list: AppModel[]; // 后端使用 list
    pagination: PaginationModel;
}
/**
 * 应用列表响应模型（直接匹配后端API文档）
 */
export interface AppListResponse {
    code: number;
    message: string;
    data: AppListData;
}
/**
 * 应用详情数据模型（直接匹配后端API返回结构）
 */
export interface AppDetailData {
    list: AppModel[]; // 后端返回的应用列表，通常只有一个元素
    screenshots: AppScreenshotModel[];
    versions: AppVersionModel[];
}
/**
 * 应用详情响应模型（直接匹配后端API返回结构）
 */
export interface AppDetailResponse {
    code: number;
    message: string;
    data: AppDetailData;
}
/**
 * 分页模型
 */
export interface PaginationModel {
    page: number;
    page_size: number;
    total: number;
    total_pages: number;
    hasNext?: boolean;
}
/**
 * 应用搜索参数
 */
export interface AppSearchParams {
    page?: number;
    page_size?: number;
    category?: string;
    sort?: string;
    keyword?: string;
}
/**
 * 应用下载状态
 */
export enum DownloadStatus {
    PENDING = "pending",
    DOWNLOADING = "downloading",
    PAUSED = "paused",
    COMPLETED = "completed",
    FAILED = "failed",
    INSTALLING = "installing",
    INSTALLED = "installed"
}
/**
 * 应用下载信息
 */
export interface AppDownloadInfo {
    appId: number;
    appName: string;
    appIcon: string;
    version: string;
    size: number;
    downloadUrl: string;
    status: DownloadStatus;
    progress: number;
    downloadedSize: number;
    speed: number;
    startTime: number;
    endTime?: number;
    error?: string;
}
/**
 * 应用安装信息
 */
export interface AppInstallInfo {
    appId: number;
    packageName: string;
    version: string;
    installTime: number;
    isSystemApp: boolean;
    canUninstall: boolean;
}
/**
 * 应用评论模型
 */
export interface AppReviewModel {
    id: number;
    application_id: number;
    user_id: number;
    username: string;
    user_avatar: string;
    rating: number;
    content: string;
    created_at: string;
    updated_at: string;
    is_helpful: boolean;
    helpful_count: number;
}
/**
 * 应用评论列表数据模型（直接匹配后端API文档）
 */
export interface AppReviewListData {
    list: AppReviewModel[]; // 后端使用 list
    pagination: PaginationModel;
}
/**
 * 应用评论列表响应（直接匹配后端API文档）
 */
export interface AppReviewListResponse {
    code: number;
    message: string;
    data: AppReviewListData;
}
