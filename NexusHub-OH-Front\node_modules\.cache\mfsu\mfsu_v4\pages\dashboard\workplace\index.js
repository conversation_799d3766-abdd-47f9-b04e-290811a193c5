"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { PageContainer } from "@ant-design/pro-components";
import { Avatar, Card, Col, List, Row, Skeleton, Statistic, Button, Modal, Form, Input, DatePicker, Tag } from "antd";
import { AppstoreOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useState, useEffect } from "react";
import EditableLinkGroup from "./components/EditableLinkGroup";
import { queryProjectNotice, queryActivities, queryTasks, createTask, updateTask, deleteTask } from "./service";
import useStyles from "./style.style";
dayjs.extend(relativeTime);
const links = [
  {
    title: "\u521B\u5EFA\u4EFB\u52A1",
    href: ""
  },
  {
    title: "\u67E5\u770B\u5E94\u7528",
    href: "/app/list"
  },
  {
    title: "\u67E5\u770B\u7EDF\u8BA1",
    href: "/statistics/app"
  },
  {
    title: "\u53D1\u5E03\u5E94\u7528",
    href: "/app/create"
  },
  {
    title: "\u7CFB\u7EDF\u76D1\u63A7",
    href: "/dashboard/monitor"
  }
];
const PageHeaderContent = ({ currentUser }) => {
  const { styles } = useStyles();
  const loading = currentUser && Object.keys(currentUser).length;
  if (!loading) {
    return /* @__PURE__ */ jsx(
      Skeleton,
      {
        avatar: true,
        paragraph: {
          rows: 1
        },
        active: true
      }
    );
  }
  return /* @__PURE__ */ jsxs("div", { className: styles.pageHeaderContent, children: [
    /* @__PURE__ */ jsx("div", { className: styles.avatar, children: /* @__PURE__ */ jsx(Avatar, { size: "large", src: currentUser.avatar }) }),
    /* @__PURE__ */ jsxs("div", { className: styles.content, children: [
      /* @__PURE__ */ jsxs("div", { className: styles.contentTitle, children: [
        "\u6B22\u8FCE\u56DE\u6765\uFF0C",
        currentUser.name,
        "\uFF0C\u795D\u4F60\u5F00\u5FC3\u6BCF\u4E00\u5929\uFF01"
      ] }),
      /* @__PURE__ */ jsxs("div", { children: [
        currentUser.title,
        " |",
        currentUser.group
      ] })
    ] })
  ] });
};
const ExtraContent = ({ appCount, downloadCount, rating }) => {
  const { styles } = useStyles();
  return /* @__PURE__ */ jsxs("div", { className: styles.extraContent, children: [
    /* @__PURE__ */ jsx("div", { className: styles.statItem, children: /* @__PURE__ */ jsx(Statistic, { title: "\u6211\u7684\u5E94\u7528", value: appCount }) }),
    /* @__PURE__ */ jsx("div", { className: styles.statItem, children: /* @__PURE__ */ jsx(Statistic, { title: "\u603B\u4E0B\u8F7D\u91CF", value: downloadCount }) }),
    /* @__PURE__ */ jsx("div", { className: styles.statItem, children: /* @__PURE__ */ jsx(Statistic, { title: "\u603B\u8BC4\u5206", value: rating, precision: 1, suffix: "/ 5" }) })
  ] });
};
const Workplace = () => {
  const { styles } = useStyles();
  const [summary, setSummary] = useState({});
  const [activities, setActivities] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [projectLoading, setProjectLoading] = useState(true);
  const [activitiesLoading, setActivitiesLoading] = useState(true);
  const [taskModalVisible, setTaskModalVisible] = useState(false);
  const [currentTask, setCurrentTask] = useState(null);
  const [form] = Form.useForm();
  useEffect(() => {
    const fetchData = async () => {
      setProjectLoading(true);
      setActivitiesLoading(true);
      try {
        const summaryResult = await queryProjectNotice();
        setSummary(summaryResult.data || {});
        const activitiesResult = await queryActivities();
        setActivities(activitiesResult.data || []);
        const tasksResult = await queryTasks();
        setTasks(tasksResult.data || []);
      } catch (error) {
        console.error("\u83B7\u53D6\u5DE5\u4F5C\u53F0\u6570\u636E\u5931\u8D25:", error);
      } finally {
        setProjectLoading(false);
        setActivitiesLoading(false);
      }
    };
    fetchData();
  }, []);
  const renderActivities = (item) => {
    const events = item.template.split(/@\{([^{}]*)\}/gi).map((key) => {
      if (item[key]) {
        const value = item[key];
        return /* @__PURE__ */ jsx("a", { href: value?.link, children: value.name }, value?.name);
      }
      return key;
    });
    return /* @__PURE__ */ jsx(List.Item, { children: /* @__PURE__ */ jsx(
      List.Item.Meta,
      {
        avatar: /* @__PURE__ */ jsx(Avatar, { src: item.user.avatar }),
        title: /* @__PURE__ */ jsxs("span", { children: [
          /* @__PURE__ */ jsx("a", { className: styles.username, children: item.user.name }),
          "\xA0",
          /* @__PURE__ */ jsx("span", { className: styles.event, children: events })
        ] }),
        description: /* @__PURE__ */ jsx("span", { className: styles.datetime, title: item.updatedAt, children: dayjs(item.updatedAt).fromNow() })
      }
    ) }, item.id);
  };
  const openTaskModal = (task) => {
    setCurrentTask(task || null);
    form.resetFields();
    if (task) {
      form.setFieldsValue({
        title: task.title,
        content: task.content,
        deadline: task.deadline ? dayjs(task.deadline) : null,
        priority: task.priority
      });
    }
    setTaskModalVisible(true);
  };
  const handleTaskSubmit = async () => {
    try {
      const values = await form.validateFields();
      const taskData = {
        ...values,
        deadline: values.deadline ? values.deadline.format("YYYY-MM-DD HH:mm:ss") : null
      };
      if (currentTask) {
        await updateTask(currentTask.id, taskData);
      } else {
        await createTask(taskData);
      }
      const tasksResult = await queryTasks();
      setTasks(tasksResult.data || []);
      setTaskModalVisible(false);
    } catch (error) {
      console.error("\u4FDD\u5B58\u4EFB\u52A1\u5931\u8D25:", error);
    }
  };
  const handleDeleteTask = async (id) => {
    try {
      await deleteTask(id);
      const tasksResult = await queryTasks();
      setTasks(tasksResult.data || []);
    } catch (error) {
      console.error("\u5220\u9664\u4EFB\u52A1\u5931\u8D25:", error);
    }
  };
  return /* @__PURE__ */ jsxs(
    PageContainer,
    {
      content: /* @__PURE__ */ jsx(
        PageHeaderContent,
        {
          currentUser: {
            avatar: summary.avatar || "https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png",
            name: summary.username || "\u5F00\u53D1\u8005",
            userid: summary.id,
            email: summary.email,
            signature: summary.signature || "\u5E94\u7528\u5F00\u53D1\u8005",
            title: summary.role || "\u5F00\u53D1\u8005",
            group: summary.organization || "NexusHub"
          }
        }
      ),
      extraContent: /* @__PURE__ */ jsx(
        ExtraContent,
        {
          appCount: summary.app_count,
          downloadCount: summary.download_count,
          rating: summary.avg_rating
        }
      ),
      children: [
        /* @__PURE__ */ jsxs(Row, { gutter: 24, children: [
          /* @__PURE__ */ jsxs(Col, { xl: 16, lg: 24, md: 24, sm: 24, xs: 24, children: [
            /* @__PURE__ */ jsx(
              Card,
              {
                className: styles.projectList,
                style: {
                  marginBottom: 24
                },
                title: "\u6211\u7684\u5E94\u7528",
                bordered: false,
                extra: /* @__PURE__ */ jsx("a", { href: "/app/list", children: "\u67E5\u770B\u5168\u90E8" }),
                loading: projectLoading,
                bodyStyle: {
                  padding: 0
                },
                children: (summary.apps || []).map((item) => /* @__PURE__ */ jsx(Card.Grid, { className: styles.projectGrid, children: /* @__PURE__ */ jsxs(
                  Card,
                  {
                    bodyStyle: {
                      padding: 0
                    },
                    bordered: false,
                    children: [
                      /* @__PURE__ */ jsx(
                        Card.Meta,
                        {
                          title: /* @__PURE__ */ jsxs("div", { className: styles.cardTitle, children: [
                            /* @__PURE__ */ jsx(
                              Avatar,
                              {
                                size: "small",
                                src: item.icon,
                                icon: /* @__PURE__ */ jsx(AppstoreOutlined, {}),
                                onError: () => {
                                  console.warn("\u5E94\u7528\u56FE\u6807\u52A0\u8F7D\u5931\u8D25:", item.icon);
                                  return false;
                                }
                              }
                            ),
                            /* @__PURE__ */ jsx("a", { href: `/app/detail/${item.id}`, children: item.name })
                          ] }),
                          description: item.description
                        }
                      ),
                      /* @__PURE__ */ jsxs("div", { className: styles.projectItemContent, children: [
                        /* @__PURE__ */ jsxs("div", { children: [
                          "\u4E0B\u8F7D\u91CF: ",
                          item.download_count
                        ] }),
                        item.updated_at && /* @__PURE__ */ jsx("span", { className: styles.datetime, title: item.updated_at, children: dayjs(item.updated_at).fromNow() })
                      ] })
                    ]
                  }
                ) }, item.id))
              }
            ),
            /* @__PURE__ */ jsx(
              Card,
              {
                bodyStyle: {
                  padding: 0
                },
                bordered: false,
                className: styles.activeCard,
                title: "\u6700\u8FD1\u6D3B\u52A8",
                loading: activitiesLoading,
                children: /* @__PURE__ */ jsx(
                  List,
                  {
                    loading: activitiesLoading,
                    renderItem: (item) => renderActivities(item),
                    dataSource: activities,
                    className: styles.activitiesList,
                    size: "large"
                  }
                )
              }
            )
          ] }),
          /* @__PURE__ */ jsxs(Col, { xl: 8, lg: 24, md: 24, sm: 24, xs: 24, children: [
            /* @__PURE__ */ jsx(
              Card,
              {
                style: {
                  marginBottom: 24
                },
                title: "\u5FEB\u901F\u5F00\u59CB / \u4FBF\u6377\u5BFC\u822A",
                bordered: false,
                bodyStyle: {
                  padding: 0
                },
                children: /* @__PURE__ */ jsx(EditableLinkGroup, { onAdd: () => {
                }, links, linkElement: "a" })
              }
            ),
            /* @__PURE__ */ jsx(
              Card,
              {
                style: {
                  marginBottom: 24
                },
                bordered: false,
                title: "\u4EFB\u52A1\u5217\u8868",
                extra: /* @__PURE__ */ jsx(Button, { type: "primary", size: "small", onClick: () => openTaskModal(), children: "\u65B0\u5EFA" }),
                children: /* @__PURE__ */ jsxs("div", { children: [
                  tasks.map((task) => /* @__PURE__ */ jsxs("div", { className: styles.taskItem, style: { marginBottom: 16, padding: 16, border: "1px solid #f0f0f0", borderRadius: 4 }, children: [
                    /* @__PURE__ */ jsxs("div", { style: { display: "flex", justifyContent: "space-between" }, children: [
                      /* @__PURE__ */ jsx("h3", { children: task.title }),
                      /* @__PURE__ */ jsxs("div", { children: [
                        task.priority === "high" && /* @__PURE__ */ jsx(Tag, { color: "red", children: "\u9AD8" }),
                        task.priority === "medium" && /* @__PURE__ */ jsx(Tag, { color: "orange", children: "\u4E2D" }),
                        task.priority === "low" && /* @__PURE__ */ jsx(Tag, { color: "green", children: "\u4F4E" })
                      ] })
                    ] }),
                    /* @__PURE__ */ jsx("p", { children: task.content }),
                    /* @__PURE__ */ jsxs("div", { style: { display: "flex", justifyContent: "space-between", marginTop: 8 }, children: [
                      /* @__PURE__ */ jsx("span", { children: task.deadline ? dayjs(task.deadline).format("YYYY-MM-DD") : "\u65E0\u622A\u6B62\u65E5\u671F" }),
                      /* @__PURE__ */ jsxs("div", { children: [
                        /* @__PURE__ */ jsx(Button, { type: "link", size: "small", onClick: () => openTaskModal(task), children: "\u7F16\u8F91" }),
                        /* @__PURE__ */ jsx(Button, { type: "link", danger: true, size: "small", onClick: () => handleDeleteTask(task.id), children: "\u5220\u9664" })
                      ] })
                    ] })
                  ] }, task.id)),
                  tasks.length === 0 && /* @__PURE__ */ jsx("div", { style: { textAlign: "center", padding: 16 }, children: "\u6682\u65E0\u4EFB\u52A1" })
                ] })
              }
            )
          ] })
        ] }),
        /* @__PURE__ */ jsx(
          Modal,
          {
            title: currentTask ? "\u7F16\u8F91\u4EFB\u52A1" : "\u65B0\u5EFA\u4EFB\u52A1",
            open: taskModalVisible,
            onOk: handleTaskSubmit,
            onCancel: () => setTaskModalVisible(false),
            children: /* @__PURE__ */ jsxs(Form, { form, layout: "vertical", children: [
              /* @__PURE__ */ jsx(Form.Item, { name: "title", label: "\u4EFB\u52A1\u6807\u9898", rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u4EFB\u52A1\u6807\u9898" }], children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u4EFB\u52A1\u6807\u9898" }) }),
              /* @__PURE__ */ jsx(Form.Item, { name: "content", label: "\u4EFB\u52A1\u5185\u5BB9", children: /* @__PURE__ */ jsx(Input.TextArea, { rows: 4, placeholder: "\u8BF7\u8F93\u5165\u4EFB\u52A1\u5185\u5BB9" }) }),
              /* @__PURE__ */ jsx(Form.Item, { name: "deadline", label: "\u622A\u6B62\u65E5\u671F", children: /* @__PURE__ */ jsx(DatePicker, { showTime: true, style: { width: "100%" } }) }),
              /* @__PURE__ */ jsx(Form.Item, { name: "priority", label: "\u4F18\u5148\u7EA7", rules: [{ required: true, message: "\u8BF7\u9009\u62E9\u4F18\u5148\u7EA7" }], initialValue: "medium", children: /* @__PURE__ */ jsx(Input.Group, { compact: true, children: /* @__PURE__ */ jsxs("div", { className: "ant-radio-group", children: [
                /* @__PURE__ */ jsx("span", { style: { marginRight: 16 }, children: /* @__PURE__ */ jsx(Tag, { color: "green", onClick: () => form.setFieldsValue({ priority: "low" }), style: { cursor: "pointer", padding: "5px 10px" }, children: "\u4F4E\u4F18\u5148\u7EA7" }) }),
                /* @__PURE__ */ jsx("span", { style: { marginRight: 16 }, children: /* @__PURE__ */ jsx(Tag, { color: "orange", onClick: () => form.setFieldsValue({ priority: "medium" }), style: { cursor: "pointer", padding: "5px 10px" }, children: "\u4E2D\u4F18\u5148\u7EA7" }) }),
                /* @__PURE__ */ jsx("span", { children: /* @__PURE__ */ jsx(Tag, { color: "red", onClick: () => form.setFieldsValue({ priority: "high" }), style: { cursor: "pointer", padding: "5px 10px" }, children: "\u9AD8\u4F18\u5148\u7EA7" }) })
              ] }) }) })
            ] })
          }
        )
      ]
    }
  );
};
export default Workplace;
