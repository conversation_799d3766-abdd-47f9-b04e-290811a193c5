import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, Select, Upload, InputNumber, Space, message, Alert, Spin, Divider } from 'antd';
import { InboxOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { history, useModel, useParams } from '@umijs/max';
import { PageContainer } from '@ant-design/pro-components';
// 移除不再需要的上传服务导入
import { getAppDetail, updateApp } from '@/services/app';

const { Option } = Select;
const { TextArea } = Input;

interface AppEditData {
  id: string;
  name: string;
  package: string;
  description: string;
  short_desc: string;
  icon: string;
  category: string;
  current_version: string;
  size: string;
  min_open_harmony_os_ver: string;
  tags: string[];
  website_url: string;
  privacy_url: string;
  developer: string;
  developer_name: string;
  price: number | string;
  package_url?: string;
  screenshots?: Array<{image_url: string} | string>;
}

const AppEdit: React.FC = () => {
  const [form] = Form.useForm();
  // 移除不再需要的上传相关状态
  const [loading, setLoading] = useState(true);
  const [appData, setAppData] = useState<AppEditData | null>(null);
  
  const { id } = useParams<{ id: string }>();
  
  // 获取用户信息
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;
  const isAdmin = currentUser?.role === 'admin';

  // 获取应用详情数据
  useEffect(() => {
    const fetchAppData = async () => {
      if (!id) {
        message.error('应用ID不存在');
        history.push('/app/list');
        return;
      }

      try {
        setLoading(true);
        const response = await getAppDetail(id);
        const data = response.data.list[0]; // 修正数据访问路径
        setAppData(data);
        console.log('API返回的完整数据:', data);
        
        // 只填充可编辑字段的表单数据
        form.setFieldsValue({
          category: data.category,
          description: data.description,
          short_desc: data.short_desc,
          tags: data.tags,
          website_url: data.website_url,
          privacy_url: data.privacy_url,
          price: data.price || 'free',
        });
      } catch (error) {
        message.error('获取应用信息失败');
        console.error('获取应用详情失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAppData();
  }, [id, form]);

  // 移除不再需要的上传相关函数

  // 提交表单
  const handleSubmit = async (values: any) => {
    if (!id) {
      message.error('应用ID不存在');
      return;
    }

    // 只提交可编辑的字段
    const formData = {
      category: values.category,
      price: values.price,
      price_amount: values.priceAmount,
      short_desc: values.short_desc,
      description: values.description,
      tags: Array.isArray(values.tags) ? values.tags.join(',') : values.tags,
      website_url: values.website_url,
      privacy_url: values.privacy_url,
    };
    
    console.log('提交的表单数据:', formData);
    console.log('应用ID:', id, '类型:', typeof id);
    try {
      await updateApp(id, formData);
      message.success('应用更新成功');
      history.push('/app/list');
    } catch (error) {
      message.error('更新失败，请重试');
      console.error('更新应用失败:', error);
      console.error('错误详情:', error.response?.data);
    }
  };

  // 返回按钮
  const handleBack = () => {
    history.push('/app/list');
  };

  if (loading) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </PageContainer>
    );
  }

  if (!appData) {
    return (
      <PageContainer>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <p>应用信息不存在</p>
          <Button onClick={handleBack}>返回应用列表</Button>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      header={{
        title: '编辑应用',
        subTitle: `编辑应用：${appData.name}`,
        extra: [
          <Button key="back" icon={<ArrowLeftOutlined />} onClick={handleBack}>
            返回
          </Button>,
        ],
      }}
    >
      {/* 角色提示信息 */}
      {isAdmin ? (
        <Alert
          message="管理员权限"
          description="您编辑的应用将直接生效，无需审核流程。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      ) : (
        <Alert
          message="开发者权限"
          description="您编辑的应用需要提交给管理员审核，审核通过后才能生效。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}
      
      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            price: 'free',
          }}
        >
          {/* 只读信息展示 */}
          <Form.Item label="应用名称">
            <Input value={appData?.name} disabled />
          </Form.Item>

          <Form.Item label="包名">
            <Input value={appData?.package} disabled />
          </Form.Item>

          <Form.Item label="应用图标">
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              {appData?.icon && (
                <img 
                  src={appData.icon} 
                  alt="应用图标" 
                  style={{ width: '64px', height: '64px', borderRadius: '8px' }} 
                />
              )}
              <span style={{ color: '#666' }}>当前版本图标（不可修改）</span>
            </div>
          </Form.Item>

          <Form.Item label="开发者">
            <Input value={appData?.developer} disabled />
          </Form.Item>

          <Form.Item label="版本号">
            <Input value={appData?.version} disabled />
          </Form.Item>

          <Form.Item label="应用大小">
            <Input value={appData?.size} disabled />
          </Form.Item>

          <Form.Item label="最低HarmonyOS版本">
            <Input value={appData?.min_open_harmony_os_ver} disabled />
          </Form.Item>

          {/* 可编辑字段 */}
          <Divider orientation="left">可编辑信息</Divider>
          
          <Form.Item
            name="category"
            label="应用分类"
            rules={[{ required: true, message: '请选择应用分类' }]}
          >
            <Select placeholder="请选择应用分类">
              <Option value="社交">社交</Option>
              <Option value="金融">金融</Option>
              <Option value="视频">视频</Option>
              <Option value="购物">购物</Option>
              <Option value="音乐">音乐</Option>
              <Option value="生活">生活</Option>
              <Option value="资讯">资讯</Option>
              <Option value="图像">图像</Option>
              <Option value="办公">办公</Option>
              <Option value="游戏">游戏</Option>
              <Option value="出行">出行</Option>
              <Option value="工具">工具</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="price"
            label="价格"
            rules={[{ required: true, message: '请设置价格' }]}
          >
            <Select 
              placeholder="请选择价格类型"
              onChange={(value) => {
                // 当选择免费时，清空价格金额
                if (value === 'free') {
                  form.setFieldsValue({ priceAmount: undefined });
                }
              }}
            >
              <Option value="free">免费</Option>
              <Option value="paid">付费</Option>
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.price !== currentValues.price}
          >
            {({ getFieldValue }) => 
              getFieldValue('price') === 'paid' ? (
                <Form.Item
                  name="priceAmount"
                  label="价格金额"
                  rules={[{ required: true, message: '请输入价格金额' }]}
                >
                  <InputNumber 
                    min={0.01} 
                    precision={2} 
                    placeholder="请输入价格" 
                    addonBefore="¥" 
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              ) : null
            }
          </Form.Item>

          <Form.Item
            name="short_desc"
            label="简短描述"
            rules={[
              { required: true, message: '请输入简短描述' },
              { max: 50, message: '简短描述不能超过50个字符' }
            ]}
          >
            <Input placeholder="请输入简短描述，最多50个字符" />
          </Form.Item>

          <Form.Item
            name="description"
            label="应用描述"
            rules={[{ required: true, message: '请输入应用描述' }]}
          >
            <TextArea rows={4} placeholder="请输入应用描述" />
          </Form.Item>

          <Form.Item
            name="tags"
            label="标签"
            rules={[{ required: true, message: '请选择至少一个标签' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择应用标签"
              maxTagCount={5}
            >
              <Option value="热门">热门</Option>
              <Option value="新品">新品</Option>
              <Option value="推荐">推荐</Option>
              <Option value="免费">免费</Option>
              <Option value="付费">付费</Option>
              <Option value="实用">实用</Option>
              <Option value="娱乐">娱乐</Option>
              <Option value="效率">效率</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="website_url"
            label="官方网站"
            rules={[
              { type: 'url', message: '请输入有效的网址' }
            ]}
          >
            <Input placeholder="请输入应用官方网站地址" />
          </Form.Item>

          <Form.Item
            name="privacy_url"
            label="隐私政策"
            rules={[
              { type: 'url', message: '请输入有效的网址' }
            ]}
          >
            <Input placeholder="请输入隐私政策页面地址" />
          </Form.Item>

          {/* 只读信息展示 - 安装包和截图 */}
          <Divider orientation="left">当前版本信息（只读）</Divider>
          
          <Form.Item label="应用安装包">
            <div style={{ padding: '12px', border: '1px dashed #d9d9d9', borderRadius: '6px', backgroundColor: '#fafafa' }}>
              {appData?.package_url ? (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <InboxOutlined style={{ color: '#666' }} />
                  <span>当前版本安装包已上传</span>
                  <a href={appData.package_url} target="_blank" rel="noopener noreferrer">查看</a>
                </div>
              ) : (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px', color: '#999' }}>
                  <InboxOutlined />
                  <span>暂无安装包</span>
                </div>
              )}
            </div>
            <div style={{ marginTop: '8px', color: '#666', fontSize: '12px' }}>
              如需更新安装包，请通过版本管理功能上传新版本
            </div>
          </Form.Item>

          <Form.Item label="应用截图">
            <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
              {appData?.screenshots && appData.screenshots.length > 0 ? (
                appData.screenshots.map((screenshot: any, index: number) => (
                  <div key={index} style={{ position: 'relative' }}>
                    <img 
                      src={screenshot.image_url || screenshot} 
                      alt={`截图 ${index + 1}`} 
                      style={{ 
                        width: '100px', 
                        height: '100px', 
                        objectFit: 'cover', 
                        borderRadius: '6px',
                        border: '1px solid #d9d9d9'
                      }} 
                    />
                  </div>
                ))
              ) : (
                <div style={{ 
                  width: '100px', 
                  height: '100px', 
                  border: '1px dashed #d9d9d9', 
                  borderRadius: '6px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#999'
                }}>
                  暂无截图
                </div>
              )}
            </div>
            <div style={{ marginTop: '8px', color: '#666', fontSize: '12px' }}>
              如需更新截图，请通过版本管理功能上传新版本
            </div>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" size="large">
                保存修改
              </Button>
              <Button size="large" onClick={handleBack}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </PageContainer>
  );
};

export default AppEdit;