"use strict";
import { createHashHistory, createM<PERSON>oryHistory, createBrowserHistory } from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/@umijs+renderer-react@4.4.1_5d37cdc93ae4c557f74cbc834d273583/node_modules/@umijs/renderer-react";
let history;
let basename = "/";
export function createHistory(opts) {
  let h;
  if (opts.type === "hash") {
    h = createHashHistory();
  } else if (opts.type === "memory") {
    h = createMemoryHistory(opts);
  } else {
    h = createBrowserHistory();
  }
  if (opts.basename) {
    basename = opts.basename;
  }
  history = {
    ...h,
    push(to, state) {
      h.push(patchTo(to, h), state);
    },
    replace(to, state) {
      h.replace(patchTo(to, h), state);
    },
    get location() {
      return h.location;
    },
    get action() {
      return h.action;
    }
  };
  return h;
}
export function setHistory(h) {
  if (h) {
    history = h;
  }
}
function patchTo(to, h) {
  if (typeof to === "string") {
    return `${stripLastSlash(basename)}${to}`;
  } else if (typeof to === "object") {
    const currentPathname = h.location.pathname;
    return {
      ...to,
      pathname: to.pathname ? `${stripLastSlash(basename)}${to.pathname}` : currentPathname
    };
  } else {
    throw new Error(`Unexpected to: ${to}`);
  }
}
function stripLastSlash(path) {
  return path.slice(-1) === "/" ? path.slice(0, -1) : path;
}
export { history };
