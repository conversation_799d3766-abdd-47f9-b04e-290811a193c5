import { preferences } from '@kit.ArkData';
import { Context } from '@kit.AbilityKit';
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 首次启动信息接口
 */
interface FirstLaunchInfo {
  isFirstLaunch: boolean;
  hasPreferences: boolean;
}

/**
 * 首次启动状态管理服务
 * 负责检测和管理应用的首次启动状态
 */
export class FirstLaunchService {
  private static instance: FirstLaunchService;
  private static readonly PREFERENCES_NAME = 'first_launch_prefs';
  private static readonly FIRST_LAUNCH_KEY = 'is_first_launch';
  private static readonly DOMAIN = 0x0000;
  private static readonly TAG = 'FirstLaunchService';

  private dataPreferences: preferences.Preferences | null = null;

  /**
   * 获取单例实例
   */
  public static getInstance(): FirstLaunchService {
    if (!FirstLaunchService.instance) {
      FirstLaunchService.instance = new FirstLaunchService();
    }
    return FirstLaunchService.instance;
  }

  /**
   * 初始化服务
   * @param context 应用上下文
   */
  public async initialize(context: Context): Promise<void> {
    try {
      const options: preferences.Options = { name: FirstLaunchService.PREFERENCES_NAME };
      this.dataPreferences = await preferences.getPreferences(context, options);
      hilog.info(FirstLaunchService.DOMAIN, FirstLaunchService.TAG, '首次启动服务初始化成功');
    } catch (error) {
      hilog.error(FirstLaunchService.DOMAIN, FirstLaunchService.TAG,
        '首次启动服务初始化失败: %{public}s', JSON.stringify(error));
      throw new Error('首次启动服务初始化失败');
    }
  }

  /**
   * 检查是否为首次启动
   * @returns Promise<boolean> true表示首次启动，false表示非首次启动
   */
  public async isFirstLaunch(): Promise<boolean> {
    try {
      if (!this.dataPreferences) {
        hilog.error(FirstLaunchService.DOMAIN, FirstLaunchService.TAG, 
          '首次启动服务未初始化');
        return true; // 默认为首次启动
      }

      // 获取首次启动标记，默认为true（首次启动）
      const isFirstLaunch = await this.dataPreferences.get(
        FirstLaunchService.FIRST_LAUNCH_KEY, 
        true
      ) as boolean;

      hilog.info(FirstLaunchService.DOMAIN, FirstLaunchService.TAG, 
        '检查首次启动状态: %{public}s', isFirstLaunch ? '是' : '否');
      
      return isFirstLaunch;
    } catch (error) {
      hilog.error(FirstLaunchService.DOMAIN, FirstLaunchService.TAG, 
        '检查首次启动状态失败: %{public}s', JSON.stringify(error));
      return true; // 出错时默认为首次启动
    }
  }

  /**
   * 标记已完成首次启动
   */
  public async markFirstLaunchCompleted(): Promise<void> {
    try {
      if (!this.dataPreferences) {
        hilog.error(FirstLaunchService.DOMAIN, FirstLaunchService.TAG, 
          '首次启动服务未初始化');
        return;
      }

      // 设置首次启动标记为false
      await this.dataPreferences.put(FirstLaunchService.FIRST_LAUNCH_KEY, false);
      
      // 立即持久化数据
      await this.dataPreferences.flush();

      hilog.info(FirstLaunchService.DOMAIN, FirstLaunchService.TAG, 
        '已标记首次启动完成');
    } catch (error) {
      hilog.error(FirstLaunchService.DOMAIN, FirstLaunchService.TAG,
        '标记首次启动完成失败: %{public}s', JSON.stringify(error));
      throw new Error('标记首次启动完成失败');
    }
  }

  /**
   * 重置首次启动状态（用于测试或重置功能）
   */
  public async resetFirstLaunchStatus(): Promise<void> {
    try {
      if (!this.dataPreferences) {
        hilog.error(FirstLaunchService.DOMAIN, FirstLaunchService.TAG, 
          '首次启动服务未初始化');
        return;
      }

      // 重置首次启动标记为true
      await this.dataPreferences.put(FirstLaunchService.FIRST_LAUNCH_KEY, true);
      
      // 立即持久化数据
      await this.dataPreferences.flush();

      hilog.info(FirstLaunchService.DOMAIN, FirstLaunchService.TAG, 
        '已重置首次启动状态');
    } catch (error) {
      hilog.error(FirstLaunchService.DOMAIN, FirstLaunchService.TAG,
        '重置首次启动状态失败: %{public}s', JSON.stringify(error));
      throw new Error('重置首次启动状态失败');
    }
  }

  /**
   * 获取首次启动相关的统计信息
   */
  public async getFirstLaunchInfo(): Promise<FirstLaunchInfo> {
    try {
      const isFirstLaunch = await this.isFirstLaunch();
      const hasPreferences = this.dataPreferences !== null;

      const info: FirstLaunchInfo = {
        isFirstLaunch: isFirstLaunch,
        hasPreferences: hasPreferences
      };
      return info;
    } catch (error) {
      hilog.error(FirstLaunchService.DOMAIN, FirstLaunchService.TAG,
        '获取首次启动信息失败: %{public}s', JSON.stringify(error));
      const fallbackInfo: FirstLaunchInfo = {
        isFirstLaunch: true,
        hasPreferences: false
      };
      return fallbackInfo;
    }
  }

  /**
   * 清理资源
   */
  public async cleanup(): Promise<void> {
    try {
      if (this.dataPreferences) {
        // 确保数据已持久化
        await this.dataPreferences.flush();
        this.dataPreferences = null;
      }
      hilog.info(FirstLaunchService.DOMAIN, FirstLaunchService.TAG, 
        '首次启动服务资源清理完成');
    } catch (error) {
      hilog.error(FirstLaunchService.DOMAIN, FirstLaunchService.TAG, 
        '清理首次启动服务资源失败: %{public}s', JSON.stringify(error));
    }
  }
}
