/*
 * @Author: xiaoxiaozhou <EMAIL>
 * @Date: 2025-06-07 11:53:23
 * @LastEditors: xiaoxiaozhou <EMAIL>
 * @LastEditTime: 2025-06-11 22:56:39
 * @FilePath: \NexusHub-OH\NexusHub-OH-Front\src\requestConfig.ts
 * @Description: 
 * 技术生的工作日常
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import { message } from 'antd';
import { history } from '@umijs/max';

export const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080/api/v1';

/**
 * 获取认证token
 */
const getAuthToken = (): string | null => {
  // 优先使用Logto token（如果启用了Logto认证）
  const useLogto = process.env.REACT_APP_USE_LOGTO === 'true';
  
  if (useLogto) {
    // 这里可以通过Logto Hook获取token，但在拦截器中无法使用Hook
    // 所以我们需要从其他地方获取token，比如全局状态或localStorage
    const logtoToken = localStorage.getItem('logto_access_token');
    if (logtoToken) {
      return logtoToken;
    }
  }
  
  // 回退到传统token
  return localStorage.getItem('token');
};

/**
 * @name 错误处理
 * pro 自带的错误处理， 可以在这里做自己的改动
 * @doc https://umijs.org/docs/max/request#配置
 */
export const errorConfig = {
  // 错误处理： umi@3 的错误处理方案。
  errorConfig: {
    // 错误抛出
    errorThrower: (res: any) => {
      const { success, data, errorCode, errorMessage, showType } = res;
      if (!success) {
        const error: any = new Error(errorMessage);
        error.name = 'BizError';
        error.info = { errorCode, errorMessage, showType, data };
        throw error; // 抛出自制的错误
      }
    },
    // 错误接收及处理
    errorHandler: (error: any, opts: any) => {
      if (opts?.skipErrorHandler) throw error;
      
      // 处理401未授权错误
      if (error.response?.status === 401) {
        message.error('登录已过期，请重新登录');
        localStorage.removeItem('token');
        localStorage.removeItem('logto_access_token');
        history.push('/user/login');
        return;
      }
      
      // 我们的 errorThrower 抛出的错误。
      if (error.name === 'BizError') {
        const errorInfo: any = error.info;
        if (errorInfo) {
          const { errorMessage, errorCode } = errorInfo;
          switch (errorInfo.showType) {
            case 'silent':
              // do nothing
              break;
            case 'warn':
              message.warning(errorMessage);
              break;
            case 'error':
              message.error(errorMessage);
              break;
            case 'notification':
              // notification.open({
              //   description: errorMessage,
              //   message: errorCode,
              // });
              break;
            case 'redirect':
              // TODO: redirect
              break;
            default:
              message.error(errorMessage);
          }
        }
      } else if (error.response) {
        // Axios 的错误
        // 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
        message.error(`Response status:${error.response.status}`);
      } else if (error.request) {
        // 请求已经成功发起，但没有收到响应
        // \`error.request\` 在浏览器中是 XMLHttpRequest 的实例，
        // 而在node.js中是 http.ClientRequest 的实例
        message.error('None response! Please retry.');
      } else {
        // 发送请求时出了点问题
        message.error('Request error, please retry.');
      }
    },
  },

  // 请求拦截器
  requestInterceptors: [
    (config: any) => {
      // 添加认证token
      const token = getAuthToken();
      if (token) {
        config.headers = {
          ...config.headers,
          Authorization: `Bearer ${token}`,
        };
      }
      
      return config;
    },
  ],

  // 响应拦截器
  responseInterceptors: [
    (response: any) => {
      // 拦截响应数据，进行个性化处理
      const { data } = response;
      if (data?.success === false) {
        message.error('请求失败！');
      }
      return response;
    },
  ],
};

// 请求配置
export const requestConfig = {
  // 错误处理
  errorConfig: {
    errorHandler: errorConfig.errorConfig.errorHandler,
  },
  
  // 请求拦截器
  requestInterceptors: errorConfig.requestInterceptors,
  
  // 响应拦截器
  responseInterceptors: errorConfig.responseInterceptors,
  
  // API前缀
  baseURL: API_BASE_URL,
};

export default requestConfig;