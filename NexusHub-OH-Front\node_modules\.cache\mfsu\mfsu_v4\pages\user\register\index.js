"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import {
  Button,
  Form,
  Input,
  Popover,
  Progress,
  Select,
  message,
  Steps,
  Typography,
  Divider,
  Space
} from "antd";
import { useEffect, useState } from "react";
import { Footer } from "@/components";
import { FormattedMessage, Helmet, useIntl, useRequest, history, Link } from "@umijs/max";
import { SelectLang } from "@/components/RightContent";
import { LockOutlined, MailOutlined, UserOutlined } from "@ant-design/icons";
import { createStyles } from "antd-style";
import Settings from "../../../../config/defaultSettings";
const FormItem = Form.Item;
const { Option } = Select;
const { Step } = Steps;
const { Title, Paragraph, Text } = Typography;
const useStyles = createStyles(({ token }) => {
  return {
    action: {
      marginLeft: "8px",
      color: "rgba(0, 0, 0, 0.2)",
      fontSize: "24px",
      verticalAlign: "middle",
      cursor: "pointer",
      transition: "color 0.3s",
      "&:hover": {
        color: token.colorPrimaryActive
      }
    },
    lang: {
      width: 42,
      height: 42,
      lineHeight: "42px",
      position: "fixed",
      right: 16,
      top: 16,
      borderRadius: token.borderRadius,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(255, 255, 255, 0.8)",
      boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
      zIndex: 1e3,
      cursor: "pointer",
      ":hover": {
        backgroundColor: token.colorBgTextHover
      }
    },
    container: {
      display: "flex",
      flexDirection: "column",
      height: "100vh",
      overflow: "auto",
      backgroundImage: "url('https://images.unsplash.com/photo-1579548122080-c35fd6820ecb?q=80&w=2070&auto=format&fit=crop')",
      backgroundSize: "cover",
      backgroundPosition: "center",
      position: "relative",
      "&::before": {
        content: '""',
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.4)",
        backdropFilter: "blur(2px)"
      }
    },
    content: {
      flex: "1",
      padding: "32px 0",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      position: "relative",
      zIndex: 1
    },
    form: {
      padding: "32px 24px",
      backgroundColor: "rgba(255, 255, 255, 0.95)",
      borderRadius: "12px",
      boxShadow: "0 8px 24px rgba(0, 0, 0, 0.2)",
      transition: "all 0.3s ease",
      maxWidth: "90%",
      width: "420px"
    },
    header: {
      display: "flex",
      alignItems: "center",
      marginBottom: "28px"
    },
    logo: {
      height: "44px",
      marginRight: "16px"
    },
    title: {
      fontSize: "33px",
      fontWeight: "bold",
      color: token.colorPrimary,
      marginBottom: "4px"
    },
    desc: {
      fontSize: "14px",
      color: token.colorTextSecondary,
      marginTop: "8px"
    },
    main: {
      width: "100%",
      margin: "0 auto"
    },
    password: {
      marginBottom: "24px",
      ".ant-form-item-explain": { display: "none" }
    },
    getCaptcha: {
      display: "block",
      width: "100%",
      borderRadius: "6px",
      height: "40px"
    },
    footer: {
      width: "100%",
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      marginTop: "16px"
    },
    submit: {
      width: "50%",
      height: "40px",
      borderRadius: "6px",
      fontSize: "16px",
      fontWeight: 500
    },
    success: {
      transition: "color 0.3s",
      color: token.colorSuccess
    },
    warning: {
      transition: "color 0.3s",
      color: token.colorWarning
    },
    error: {
      transition: "color 0.3s",
      color: token.colorError
    },
    "progress-pass > .progress": {
      ".ant-progress-bg": { backgroundColor: token.colorWarning }
    },
    stepContent: {
      padding: "24px 0",
      minHeight: "240px"
    },
    stepsContainer: {
      marginBottom: "32px",
      width: "100%",
      ".ant-steps-item-icon": {
        display: "flex",
        alignItems: "center",
        justifyContent: "center"
      },
      ".ant-steps-item-title": {
        fontSize: "14px",
        fontWeight: 500
      },
      ".ant-steps-item-description": {
        fontSize: "12px"
      }
    },
    formItem: {
      marginBottom: "24px"
    },
    loginLink: {
      color: token.colorPrimary,
      transition: "color 0.3s",
      "&:hover": {
        color: token.colorPrimaryActive,
        textDecoration: "underline"
      }
    },
    stepTitle: {
      fontSize: "18px",
      fontWeight: 500,
      marginBottom: "16px",
      color: token.colorTextHeading
    },
    confirmInfo: {
      backgroundColor: "rgba(240, 242, 245, 0.6)",
      padding: "16px",
      borderRadius: "8px",
      marginBottom: "16px"
    },
    infoItem: {
      display: "flex",
      marginBottom: "8px",
      "&:last-child": {
        marginBottom: 0
      }
    },
    infoLabel: {
      width: "80px",
      color: token.colorTextSecondary,
      flexShrink: 0
    },
    infoValue: {
      fontWeight: 500,
      color: token.colorText,
      flexGrow: 1
    }
  };
});
const Lang = () => {
  const { styles } = useStyles();
  return /* @__PURE__ */ jsx("div", { className: styles.lang, "data-lang": true, children: /* @__PURE__ */ jsx(SelectLang, { style: { height: "100%", width: "100%" } }) });
};
const passwordProgressMap = {
  ok: "success",
  pass: "normal",
  poor: "exception"
};
const Register = () => {
  const { styles } = useStyles();
  const [open, setVisible] = useState(false);
  const [popover, setPopover] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({});
  const confirmDirty = false;
  const intl = useIntl();
  const passwordStatusMap = {
    ok: /* @__PURE__ */ jsx("div", { className: styles.success, children: /* @__PURE__ */ jsx("span", { children: "\u5F3A\u5EA6\uFF1A\u5F3A" }) }),
    pass: /* @__PURE__ */ jsx("div", { className: styles.warning, children: /* @__PURE__ */ jsx("span", { children: "\u5F3A\u5EA6\uFF1A\u4E2D" }) }),
    poor: /* @__PURE__ */ jsx("div", { className: styles.error, children: /* @__PURE__ */ jsx("span", { children: "\u5F3A\u5EA6\uFF1A\u592A\u77ED" }) })
  };
  const [form] = Form.useForm();
  useEffect(() => {
    const savedData = localStorage.getItem("registerFormData");
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        form.setFieldsValue(parsedData);
        setFormData(parsedData);
      } catch (error) {
        console.error("\u89E3\u6790\u4FDD\u5B58\u7684\u8868\u5355\u6570\u636E\u5931\u8D25:", error);
      }
    }
    const autoSaveInterval = setInterval(() => {
      const currentValues = form.getFieldsValue();
      localStorage.setItem("registerFormData", JSON.stringify(currentValues));
      message.success("\u8868\u5355\u6570\u636E\u5DF2\u81EA\u52A8\u4FDD\u5B58", 1);
    }, 3e4);
    return () => {
      clearInterval(autoSaveInterval);
    };
  }, [form]);
  const getPasswordStatus = () => {
    const value = form.getFieldValue("password");
    if (value && value.length > 9) {
      return "ok";
    }
    if (value && value.length > 5) {
      return "pass";
    }
    return "poor";
  };
  const { loading: submitting, run: register } = useRequest(
    async (params) => {
      const response = await import("@/services/user").then((module) => module.register(params));
      if (response && (response.code === 200 || response.success)) {
        return { status: "ok", data: response };
      } else {
        throw new Error(response?.message || "\u6CE8\u518C\u5931\u8D25");
      }
    },
    {
      manual: true,
      onSuccess: (data, params) => {
        message.success("\u6CE8\u518C\u6210\u529F\uFF01");
        localStorage.removeItem("registerFormData");
        history.push({
          pathname: `/user/register-result?account=${params[0].email}`
        });
      },
      onError: (error) => {
        console.error("\u6CE8\u518C\u5931\u8D25:", error);
        const errorMessage = error?.message || error?.response?.data?.message || "\u6CE8\u518C\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5";
        message.error(errorMessage);
      }
    }
  );
  const checkUsernameUnique = async (username) => {
    try {
      return true;
    } catch (error) {
      console.error("\u68C0\u67E5\u7528\u6237\u540D\u5931\u8D25:", error);
      return false;
    }
  };
  const onFinish = async (values) => {
    const newFormData = { ...formData, ...values };
    setFormData(newFormData);
    localStorage.setItem("registerFormData", JSON.stringify(newFormData));
    if (currentStep < 1) {
      setCurrentStep(currentStep + 1);
      return;
    }
    const isUsernameUnique = await checkUsernameUnique(newFormData.username);
    if (!isUsernameUnique) {
      message.error("\u7528\u6237\u540D\u5DF2\u5B58\u5728\uFF0C\u8BF7\u9009\u62E9\u5176\u4ED6\u7528\u6237\u540D");
      setCurrentStep(0);
      return;
    }
    register({
      username: newFormData.username,
      password: newFormData.password,
      email: newFormData.email,
      nickname: newFormData.username
      // 使用用户名作为昵称
    });
  };
  const handlePrevStep = () => {
    setCurrentStep(currentStep - 1);
  };
  const handleSaveForm = () => {
    const currentValues = form.getFieldsValue();
    const newFormData = { ...formData, ...currentValues };
    setFormData(newFormData);
    localStorage.setItem("registerFormData", JSON.stringify(newFormData));
    message.success("\u8868\u5355\u6570\u636E\u5DF2\u4FDD\u5B58");
  };
  const checkConfirm = (_, value) => {
    const promise = Promise;
    if (value && value !== form.getFieldValue("password")) {
      return promise.reject("\u4E24\u6B21\u8F93\u5165\u7684\u5BC6\u7801\u4E0D\u5339\u914D!");
    }
    return promise.resolve();
  };
  const checkPassword = (_, value) => {
    const promise = Promise;
    if (!value) {
      setVisible(!!value);
      return promise.reject("\u8BF7\u8F93\u5165\u5BC6\u7801!");
    }
    if (!open) {
      setVisible(!!value);
    }
    setPopover(!popover);
    if (value.length < 6) {
      return promise.reject("");
    }
    if (value && confirmDirty) {
      form.validateFields(["confirm"]);
    }
    return promise.resolve();
  };
  const renderPasswordProgress = () => {
    const value = form.getFieldValue("password");
    const passwordStatus = getPasswordStatus();
    return value && value.length ? /* @__PURE__ */ jsx("div", { className: styles[`progress-${passwordStatus}`], children: /* @__PURE__ */ jsx(
      Progress,
      {
        status: passwordProgressMap[passwordStatus],
        strokeWidth: 6,
        percent: value.length * 10 > 100 ? 100 : value.length * 10,
        showInfo: false
      }
    ) }) : null;
  };
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return /* @__PURE__ */ jsxs("div", { className: styles.stepContent, children: [
          /* @__PURE__ */ jsx("div", { className: styles.stepTitle, children: "\u8BBE\u7F6E\u60A8\u7684\u8D26\u53F7\u4FE1\u606F" }),
          /* @__PURE__ */ jsx(
            FormItem,
            {
              name: "username",
              className: styles.formItem,
              rules: [
                {
                  required: true,
                  message: "\u8BF7\u8F93\u5165\u7528\u6237\u540D!"
                },
                {
                  min: 3,
                  message: "\u7528\u6237\u540D\u81F3\u5C113\u4E2A\u5B57\u7B26!"
                },
                {
                  max: 20,
                  message: "\u7528\u6237\u540D\u6700\u591A20\u4E2A\u5B57\u7B26!"
                },
                {
                  pattern: /^[a-zA-Z0-9_]+$/,
                  message: "\u7528\u6237\u540D\u53EA\u80FD\u5305\u542B\u5B57\u6BCD\u3001\u6570\u5B57\u548C\u4E0B\u5212\u7EBF!"
                }
              ],
              help: "\u7528\u6237\u540D\u5C06\u4F5C\u4E3A\u60A8\u7684\u552F\u4E00\u6807\u8BC6\uFF0C\u53EA\u80FD\u5305\u542B\u5B57\u6BCD\u3001\u6570\u5B57\u548C\u4E0B\u5212\u7EBF",
              children: /* @__PURE__ */ jsx(
                Input,
                {
                  size: "large",
                  prefix: /* @__PURE__ */ jsx(UserOutlined, {}),
                  placeholder: "\u7528\u6237\u540D",
                  autoComplete: "username"
                }
              )
            }
          ),
          /* @__PURE__ */ jsx(
            FormItem,
            {
              name: "email",
              className: styles.formItem,
              rules: [
                {
                  required: true,
                  message: "\u8BF7\u8F93\u5165\u90AE\u7BB1\u5730\u5740!"
                },
                {
                  type: "email",
                  message: "\u90AE\u7BB1\u5730\u5740\u683C\u5F0F\u9519\u8BEF!"
                }
              ],
              help: "\u8BF7\u8F93\u5165\u6709\u6548\u7684\u90AE\u7BB1\u5730\u5740\uFF0C\u7528\u4E8E\u63A5\u6536\u91CD\u8981\u901A\u77E5",
              children: /* @__PURE__ */ jsx(
                Input,
                {
                  size: "large",
                  prefix: /* @__PURE__ */ jsx(MailOutlined, {}),
                  placeholder: "\u90AE\u7BB1",
                  autoComplete: "email"
                }
              )
            }
          ),
          /* @__PURE__ */ jsx(
            Popover,
            {
              getPopupContainer: (node) => {
                if (node && node.parentNode) {
                  return node.parentNode;
                }
                return node;
              },
              content: open && /* @__PURE__ */ jsxs(
                "div",
                {
                  style: {
                    padding: "4px 0"
                  },
                  children: [
                    passwordStatusMap[getPasswordStatus()],
                    renderPasswordProgress(),
                    /* @__PURE__ */ jsx(
                      "div",
                      {
                        style: {
                          marginTop: 10
                        },
                        children: /* @__PURE__ */ jsx("span", { children: "\u8BF7\u81F3\u5C11\u8F93\u5165 6 \u4E2A\u5B57\u7B26\u3002\u8BF7\u4E0D\u8981\u4F7F\u7528\u5BB9\u6613\u88AB\u731C\u5230\u7684\u5BC6\u7801\u3002" })
                      }
                    )
                  ]
                }
              ),
              overlayStyle: {
                width: 240
              },
              placement: "right",
              open,
              children: /* @__PURE__ */ jsx(
                FormItem,
                {
                  name: "password",
                  className: `${styles.formItem} ${form.getFieldValue("password") && form.getFieldValue("password").length > 0 && styles.password}`,
                  rules: [
                    {
                      validator: checkPassword
                    }
                  ],
                  help: "\u5BC6\u7801\u957F\u5EA6\u81F3\u5C116\u4F4D\uFF0C\u5EFA\u8BAE\u4F7F\u7528\u5B57\u6BCD\u3001\u6570\u5B57\u548C\u7279\u6B8A\u5B57\u7B26\u7684\u7EC4\u5408",
                  children: /* @__PURE__ */ jsx(
                    Input,
                    {
                      size: "large",
                      type: "password",
                      prefix: /* @__PURE__ */ jsx(LockOutlined, {}),
                      placeholder: "\u81F3\u5C116\u4F4D\u5BC6\u7801\uFF0C\u533A\u5206\u5927\u5C0F\u5199",
                      autoComplete: "new-password"
                    }
                  )
                }
              )
            }
          ),
          /* @__PURE__ */ jsx(
            FormItem,
            {
              name: "confirm",
              className: styles.formItem,
              rules: [
                {
                  required: true,
                  message: "\u8BF7\u786E\u8BA4\u5BC6\u7801!"
                },
                {
                  validator: checkConfirm
                }
              ],
              help: "\u8BF7\u518D\u6B21\u8F93\u5165\u5BC6\u7801\u4EE5\u786E\u8BA4",
              children: /* @__PURE__ */ jsx(
                Input,
                {
                  size: "large",
                  type: "password",
                  prefix: /* @__PURE__ */ jsx(LockOutlined, {}),
                  placeholder: "\u786E\u8BA4\u5BC6\u7801",
                  autoComplete: "new-password"
                }
              )
            }
          )
        ] });
      case 1:
        return /* @__PURE__ */ jsxs("div", { className: styles.stepContent, children: [
          /* @__PURE__ */ jsx("div", { className: styles.stepTitle, children: "\u786E\u8BA4\u60A8\u7684\u6CE8\u518C\u4FE1\u606F" }),
          /* @__PURE__ */ jsxs("div", { className: styles.confirmInfo, children: [
            /* @__PURE__ */ jsxs("div", { className: styles.infoItem, children: [
              /* @__PURE__ */ jsx("span", { className: styles.infoLabel, children: "\u7528\u6237\u540D:" }),
              /* @__PURE__ */ jsx("span", { className: styles.infoValue, children: formData.username })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: styles.infoItem, children: [
              /* @__PURE__ */ jsx("span", { className: styles.infoLabel, children: "\u90AE\u7BB1:" }),
              /* @__PURE__ */ jsx("span", { className: styles.infoValue, children: formData.email })
            ] })
          ] }),
          /* @__PURE__ */ jsx(Paragraph, { children: /* @__PURE__ */ jsx(Text, { type: "secondary", children: '\u70B9\u51FB"\u6CE8\u518C"\u6309\u94AE\u5B8C\u6210\u8D26\u53F7\u521B\u5EFA\uFF0C\u6CE8\u518C\u6210\u529F\u540E\u60A8\u5C06\u53EF\u4EE5\u4F7F\u7528NexusHub\u7684\u6240\u6709\u529F\u80FD\u3002' }) }),
          /* @__PURE__ */ jsx(Paragraph, { children: /* @__PURE__ */ jsxs(Text, { type: "secondary", children: [
            "\u6CE8\u518C\u5373\u8868\u793A\u60A8\u540C\u610F\u6211\u4EEC\u7684",
            /* @__PURE__ */ jsx(Link, { to: "/terms", children: "\u670D\u52A1\u6761\u6B3E" }),
            "\u548C",
            /* @__PURE__ */ jsx(Link, { to: "/privacy", children: "\u9690\u79C1\u653F\u7B56" }),
            "\u3002"
          ] }) })
        ] });
      default:
        return null;
    }
  };
  const renderStepButtons = () => {
    if (currentStep === 0) {
      return /* @__PURE__ */ jsx(Button, { type: "primary", size: "large", htmlType: "submit", className: styles.submit, children: "\u4E0B\u4E00\u6B65" });
    }
    if (currentStep === 1) {
      return /* @__PURE__ */ jsxs(Space, { size: "middle", children: [
        /* @__PURE__ */ jsx(Button, { size: "large", onClick: () => setCurrentStep(currentStep - 1), children: "\u4E0A\u4E00\u6B65" }),
        /* @__PURE__ */ jsx(
          Button,
          {
            type: "primary",
            size: "large",
            htmlType: "submit",
            loading: submitting,
            className: styles.submit,
            children: "\u6CE8\u518C"
          }
        )
      ] });
    }
    return null;
  };
  return /* @__PURE__ */ jsxs("div", { className: styles.container, children: [
    /* @__PURE__ */ jsx(Helmet, { children: /* @__PURE__ */ jsxs("title", { children: [
      intl.formatMessage({
        id: "menu.register",
        defaultMessage: "\u6CE8\u518C\u9875"
      }),
      "- ",
      Settings.title
    ] }) }),
    /* @__PURE__ */ jsx(Lang, {}),
    /* @__PURE__ */ jsx("div", { className: styles.content, children: /* @__PURE__ */ jsxs("div", { className: styles.form, children: [
      /* @__PURE__ */ jsxs("div", { className: styles.header, children: [
        /* @__PURE__ */ jsx("img", { alt: "logo", className: styles.logo, src: "/logo.svg" }),
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("h1", { className: styles.title, children: "NexusHub" }),
          /* @__PURE__ */ jsx("p", { className: styles.desc, children: /* @__PURE__ */ jsx(FormattedMessage, { id: "pages.login.appDesc", defaultMessage: "\u4E00\u7AD9\u5F0F\u5E94\u7528\u7BA1\u7406\u4E0E\u5206\u53D1\u5E73\u53F0" }) })
        ] })
      ] }),
      /* @__PURE__ */ jsx(Divider, { style: { margin: "0 0 24px 0" } }),
      /* @__PURE__ */ jsxs("div", { className: styles.main, children: [
        /* @__PURE__ */ jsx("div", { className: styles.stepsContainer, children: /* @__PURE__ */ jsxs(Steps, { current: currentStep, className: styles.steps, children: [
          /* @__PURE__ */ jsx(Step, { title: "\u8D26\u53F7\u4FE1\u606F", description: "\u8BBE\u7F6E\u7528\u6237\u540D\u548C\u5BC6\u7801" }),
          /* @__PURE__ */ jsx(Step, { title: "\u786E\u8BA4\u6CE8\u518C", description: "\u786E\u8BA4\u4FE1\u606F\u5E76\u5B8C\u6210\u6CE8\u518C" })
        ] }) }),
        /* @__PURE__ */ jsxs(Form, { form, name: "UserRegister", onFinish, layout: "vertical", children: [
          renderStepContent(),
          /* @__PURE__ */ jsx(FormItem, { children: renderStepButtons() })
        ] })
      ] })
    ] }) }),
    /* @__PURE__ */ jsx(Footer, {})
  ] });
};
export default Register;
