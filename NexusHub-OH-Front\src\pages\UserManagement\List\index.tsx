import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  message,
  Avatar,
  Tag,
  Badge,
  Dropdown,
  Popconfirm,
  Grid,
  DatePicker,
} from 'antd';
import {
  SearchOutlined,
  UserOutlined,
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { history, useAccess } from '@umijs/max';
import type { ColumnsType } from 'antd/es/table';
import { getUserList } from '@/services/admin';
import { getSearchUsers } from '@/services/ant-design-pro/sousuo';
import {
  putAdminUsersIdRole,
  putAdminUsersIdStatus,
} from '@/services/ant-design-pro/guanliyuan';
import PermissionWrapper, { PermissionButton } from '@/components/PermissionWrapper';

const { useBreakpoint } = Grid;

const { RangePicker } = DatePicker;

interface UserItem {
  id: string;
  username: string;
  nickname: string;
  avatar: string;
  email: string;
  phone: string;
  role: 'admin' | 'user' | 'developer';
  status: 'active' | 'inactive' | 'banned';
  registerTime: string;
  lastLoginTime: string;
}

const UserList: React.FC = () => {
  const screens = useBreakpoint();
  const access = useAccess();
  const [searchParams, setSearchParams] = useState({
    page: 1,
    pageSize: 10,
    keyword: '',
    role: '',
    status: '',
  });
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState<UserItem | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [useSearchAPI, setUseSearchAPI] = useState(false);
  
  // 响应式配置
  const isMobile = !screens.md;
  const isTablet = screens.md && !screens.lg;
  
  // 根据是否有搜索条件决定使用哪个API
  const shouldUseSearchAPI = !!(searchParams.keyword?.trim() || searchParams.role || searchParams.status);
  

  const { data, loading, run } = useRequest(
    async () => {
      if (shouldUseSearchAPI) {

        const response = await getSearchUsers({
          keyword: searchParams.keyword,
          role: searchParams.role,
          status: searchParams.status,
          page: searchParams.page.toString(),
          page_size: searchParams.pageSize.toString(),
        });
        

        
        // 转换搜索API的数据格式以匹配原有格式
        if (response.code === 200 && response.data) {
          const convertedUsers = response.data.users?.map((user: any) => {
                return {
                  id: user.id?.toString() || '',
                  username: user.username || '',
                  nickname: user.developer_name || user.username || '',
                  avatar: '', // UserDocument中没有avatar字段
                  email: user.email || '',
                  phone: user.contact_phone || user.phone || '',
                  role: user.role || '',
                  status: user.status || '',
                  registerTime: user.created_at || '',
                  lastLoginTime: user.last_login_at || user.created_at || '',
                };
              }) || [];
          
          const convertedData = {
              data: convertedUsers,
              total: response.data.total || 0,
              code: response.code,
              message: response.message,
            };
          return convertedData;
        }
        return {
          data: [],
          total: 0,
          code: response.code || 500,
          message: response.message || '搜索失败',
        };
      } else {
        return getUserList({
          page: searchParams.page,
          pageSize: searchParams.pageSize,
        });
      }
    },
    {
      refreshDeps: [searchParams, shouldUseSearchAPI],
    }
  );

  const handleSearch = (values: any) => {
    setSearchParams({
      ...searchParams,
      page: 1, // 重置到第一页
      keyword: values.keyword || '',
      role: values.role || '',
      status: values.status || '',
    });
    // 手动触发数据重新加载
    setTimeout(() => {
      run();
    }, 100);
  };

  const handleReset = () => {
    searchForm.resetFields();
    setSearchParams({
      page: 1,
      pageSize: 10,
      keyword: '',
      role: '',
      status: '',
    });
    // 手动触发数据重新加载
    setTimeout(() => {
      run();
    }, 100);
  };

  const handleViewDetail = (id: string) => {
    history.push(`/user-management/detail/${id}`);
  };

  const handleEdit = (id: string) => {
    // 查找当前用户数据
    const user = data?.data?.find(item => item.id === id);
    if (user) {
      setCurrentUser(user);
      // 设置表单初始值
      form.setFieldsValue({
        username: user.username,
        nickname: user.nickname,
        email: user.email,
        phone: user.phone,
        role: user.role,
        status: user.status,
      });
      setEditModalVisible(true);
    }
  };
  
  // 处理编辑表单提交
  const handleEditSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (!currentUser) return;
      
      // 检查角色是否发生变化
      if (values.role !== currentUser.role) {
        await putAdminUsersIdRole(
          { id: currentUser.id },
          { role: values.role }
        );
      }
      
      // 检查状态是否发生变化
      if (values.status !== currentUser.status) {
        await putAdminUsersIdStatus(
          { id: currentUser.id },
          { status: values.status }
        );
      }
      
      message.success(`用户 ${currentUser?.nickname || currentUser?.username} 信息更新成功`);
      setEditModalVisible(false);
      // 刷新列表
      run();
    } catch (error) {
      console.error('更新用户信息失败:', error);
      message.error('更新用户信息失败，请重试');
    }
  };

  const handleLock = (id: string, isLocked: boolean) => {
    message.success(`${isLocked ? '解锁' : '锁定'}用户 ${id} 成功`);
    // 实际项目中应该调用API
  };

  const getRoleTag = (role: string) => {
    switch (role) {
      case 'admin':
        return <Tag color="red">管理员</Tag>;
      case 'developer':
        return <Tag color="blue">开发者</Tag>;
      case 'user':
        return <Tag color="green">普通用户</Tag>;
      default:
        return <Tag>{role}</Tag>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge status="success" text="活跃" />;
      case 'inactive':
        return <Badge status="default" text="非活跃" />;
      case 'banned':
        return <Badge status="error" text="已禁用" />;
      default:
        return <Badge status="processing" text={status} />;
    }
  };

  const columns: ColumnsType<UserItem> = [
    {
      title: '用户ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      hidden: isMobile, // 移动端隐藏ID列
    },
    {
      title: '用户信息',
      key: 'userInfo',
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
            src={record.avatar} 
            size={isMobile ? 'default' : 'large'} 
            style={{ marginRight: 8, flexShrink: 0 }} 
          />
          <div style={{ minWidth: 0, flex: 1 }}>
            <div style={{ fontWeight: 'bold', marginBottom: 2 }}>{record.nickname}</div>
            <div style={{ fontSize: 12, color: '#999', marginBottom: 2 }}>{record.username}</div>
            {isMobile && (
              <div style={{ fontSize: 11, color: '#666' }}>
                <div>{record.email}</div>
                {record.phone && <div>{record.phone}</div>}
              </div>
            )}
            {isMobile && (
              <Space size="small" style={{ marginTop: 4 }}>
                {getRoleTag(record.role)}
                {getStatusBadge(record.status)}
              </Space>
            )}
          </div>
        </div>
      ),
    },
    // 桌面端显示的详细列
    ...(!isMobile ? [
      {
        title: '联系方式',
        key: 'contact',
        render: (_, record) => (
          <div>
            <div>{record.email}</div>
            <div>{record.phone}</div>
          </div>
        ),
      },
      {
        title: '角色',
        dataIndex: 'role',
        key: 'role',
        render: (role) => getRoleTag(role),
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status) => getStatusBadge(status),
      },
      {
        title: '注册时间',
        dataIndex: 'registerTime',
        key: 'registerTime',
        sorter: (a, b) => a.registerTime.localeCompare(b.registerTime),
      },
      {
        title: '最后登录',
        dataIndex: 'lastLoginTime',
        key: 'lastLoginTime',
        sorter: (a, b) => a.lastLoginTime.localeCompare(b.lastLoginTime),
      },
    ] : []),
    {
      title: '操作',
      key: 'action',
      width: isMobile ? 80 : 200,
      fixed: isMobile ? 'right' : undefined,
      render: (_, record) => (
        <Space size="small" direction={isMobile ? 'vertical' : 'horizontal'}>
          <Button
            type="link"
            size="small"
            icon={<UserOutlined />}
            onClick={() => handleViewDetail(record.id)}
          >
            {isMobile ? '' : '详情'}
          </Button>
          
          <PermissionButton permission="canEditUser">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record.id)}
            >
              {isMobile ? '' : '编辑'}
            </Button>
          </PermissionButton>
          
          <PermissionButton permission="canEditUser">
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'lock',
                    label: record.status === 'banned' ? '解锁用户' : '锁定用户',
                    icon: record.status === 'banned' ? <UnlockOutlined /> : <LockOutlined />,
                    onClick: () => handleLock(record.id, record.status === 'banned'),
                  },
                  {
                    key: 'delete',
                    label: '删除用户',
                    danger: true,
                    disabled: !access.canDeleteUser,
                  },
                ],
              }}
            >
              <Button type="link" size="small" icon={<MoreOutlined />} />
            </Dropdown>
          </PermissionButton>
        </Space>
      ),
    },
  ];

  return (
    <PermissionWrapper permission="canViewUserList">
      <PageContainer>
        <Card>
          {/* 搜索表单 */}
          <Form
            form={searchForm}
            onFinish={handleSearch}
            layout={isMobile ? 'vertical' : 'inline'}
            style={{ marginBottom: 16 }}
          >
            <Form.Item name="keyword" style={{ marginBottom: isMobile ? 12 : 16 }}>
              <Input
                placeholder="搜索用户名、邮箱或开发者信息"
                prefix={<SearchOutlined />}
                style={{ width: isMobile ? '100%' : 250 }}
                allowClear
              />
            </Form.Item>
            <Form.Item name="role" style={{ marginBottom: isMobile ? 12 : 16 }}>
              <Select
                placeholder="选择角色"
                style={{ width: isMobile ? '100%' : 120 }}
                allowClear
              >
                <Select.Option value="admin">管理员</Select.Option>
                <Select.Option value="developer">开发者</Select.Option>
                <Select.Option value="user">普通用户</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item name="status" style={{ marginBottom: isMobile ? 12 : 16 }}>
              <Select
                placeholder="选择状态"
                style={{ width: isMobile ? '100%' : 120 }}
                allowClear
              >
                <Select.Option value="active">正常</Select.Option>
                <Select.Option value="inactive">未激活</Select.Option>
                <Select.Option value="banned">已封禁</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item style={{ marginBottom: isMobile ? 12 : 16 }}>
              <Space direction={isMobile ? 'horizontal' : 'horizontal'} style={{ width: isMobile ? '100%' : 'auto' }}>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  icon={<SearchOutlined />}
                  style={{ flex: isMobile ? 1 : 'none' }}
                >
                  搜索
                </Button>
                <Button 
                  onClick={handleReset}
                  style={{ flex: isMobile ? 1 : 'none' }}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>

          {/* 操作按钮 */}
          <div style={{ marginBottom: 16 }}>
            <Space 
              direction={isMobile ? 'vertical' : 'horizontal'} 
              style={{ width: isMobile ? '100%' : 'auto' }}
              size={isMobile ? 8 : 'small'}
            >
              <PermissionButton permission="canEditUser">
                <Button 
                  type="primary"
                  style={{ width: isMobile ? '100%' : 'auto' }}
                >
                  新增用户
                </Button>
              </PermissionButton>
              
              <PermissionButton permission="canEditUser">
                <Button style={{ width: isMobile ? '100%' : 'auto' }}>
                  批量操作
                </Button>
              </PermissionButton>
              
              <Button style={{ width: isMobile ? '100%' : 'auto' }}>
                导出数据
              </Button>
            </Space>
          </div>

          {/* 用户列表表格 */}
          <Table
            columns={columns}
            dataSource={shouldUseSearchAPI ? (data?.data || []) : (data?.data?.data || [])}
            loading={loading}
            rowKey="id"
            scroll={{
              x: isMobile ? 800 : undefined,
              y: isMobile ? 400 : undefined,
            }}
            size={isMobile ? 'small' : 'middle'}
            pagination={{
              current: searchParams.page,
              pageSize: searchParams.pageSize,
              total: shouldUseSearchAPI ? (data?.total || 0) : (data?.data?.total || 0),
              showSizeChanger: !isMobile,
              showQuickJumper: !isMobile,
              simple: isMobile,
              showTotal: !isMobile ? (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条` : undefined,
              onChange: (page, pageSize) => {
                setSearchParams({
                  ...searchParams,
                  page,
                  pageSize: pageSize || 10,
                });
              },
              position: [isMobile ? 'bottomCenter' : 'bottomRight'],
            }}
          />
        </Card>

        {/* 编辑用户模态框 */}
        <Modal
          title="编辑用户信息"
          open={editModalVisible}
          onOk={handleEditSubmit}
          onCancel={() => setEditModalVisible(false)}
          width={isMobile ? '95%' : 600}
          centered={isMobile}
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              status: 'active',
              role: 'user',
            }}
          >
            <Form.Item
              label="用户名"
              name="username"
              rules={[{ required: true, message: '请输入用户名' }]}
            >
              <Input placeholder="请输入用户名" />
            </Form.Item>
            
            <Form.Item
              label="昵称"
              name="nickname"
            >
              <Input placeholder="请输入昵称" />
            </Form.Item>
            
            <Form.Item
              label="邮箱"
              name="email"
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input placeholder="请输入邮箱" />
            </Form.Item>
            
            <Form.Item
              label="手机号"
              name="phone"
            >
              <Input placeholder="请输入手机号" />
            </Form.Item>
            
            <Form.Item
              label="角色"
              name="role"
              rules={[{ required: true, message: '请选择角色' }]}
            >
              <Select placeholder="请选择角色">
                <Select.Option value="user">普通用户</Select.Option>
                <Select.Option value="developer">开发者</Select.Option>
                {access.canManageRole && (
                  <Select.Option value="admin">管理员</Select.Option>
                )}
              </Select>
            </Form.Item>
            
            <Form.Item
              label="状态"
              name="status"
              rules={[{ required: true, message: '请选择状态' }]}
            >
              <Select placeholder="请选择状态">
                <Select.Option value="active">正常</Select.Option>
                <Select.Option value="inactive">未激活</Select.Option>
                <Select.Option value="banned">已封禁</Select.Option>
              </Select>
            </Form.Item>
          </Form>
        </Modal>
      </PageContainer>
    </PermissionWrapper>
  );
};

export default UserList;