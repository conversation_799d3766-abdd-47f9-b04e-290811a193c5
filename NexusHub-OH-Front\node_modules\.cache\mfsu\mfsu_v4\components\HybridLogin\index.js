"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { useState } from "react";
import { <PERSON><PERSON>, Card, Divider, Space, Typography } from "antd";
import { UserOutlined } from "@ant-design/icons";
import { useModel } from "@umijs/max";
import { useLogtoAuth } from "@/hooks/useLogtoAuth";
const { Title, Text } = Typography;
const HybridLogin = ({ onTraditionalLogin }) => {
  const { initialState } = useModel("@@initialState");
  const [loginType, setLoginType] = useState("traditional");
  if (!initialState?.useLogto) {
    return null;
  }
  const { signIn, isLoading } = useLogtoAuth();
  const handleSSOLogin = async () => {
    try {
      await signIn();
    } catch (error) {
      console.error("SSO\u767B\u5F55\u5931\u8D25:", error);
    }
  };
  return /* @__PURE__ */ jsxs("div", { style: { marginTop: 24 }, children: [
    /* @__PURE__ */ jsx(Divider, { children: /* @__PURE__ */ jsx(Text, { type: "secondary", children: "\u6216\u8005" }) }),
    /* @__PURE__ */ jsxs(Space, { direction: "vertical", style: { width: "100%" }, size: "middle", children: [
      /* @__PURE__ */ jsxs(
        Card,
        {
          size: "small",
          style: {
            textAlign: "center",
            background: "#f8f9fa",
            border: "1px dashed #d9d9d9"
          },
          children: [
            /* @__PURE__ */ jsx(Title, { level: 5, style: { margin: "8px 0" }, children: "\u4F01\u4E1A\u5355\u70B9\u767B\u5F55 (SSO)" }),
            /* @__PURE__ */ jsx(Text, { type: "secondary", style: { fontSize: "12px" }, children: "\u4F7F\u7528\u4F01\u4E1A\u8D26\u53F7\u5FEB\u901F\u767B\u5F55" })
          ]
        }
      ),
      /* @__PURE__ */ jsx(
        Button,
        {
          type: "primary",
          size: "large",
          icon: /* @__PURE__ */ jsx(UserOutlined, {}),
          loading: isLoading,
          onClick: handleSSOLogin,
          style: { width: "100%", height: "48px" },
          children: "\u4F01\u4E1A\u8D26\u53F7\u767B\u5F55"
        }
      ),
      /* @__PURE__ */ jsx("div", { style: { textAlign: "center", marginTop: 16 }, children: /* @__PURE__ */ jsx(Text, { type: "secondary", style: { fontSize: "12px" }, children: "\u9996\u6B21\u4F7F\u7528\u4F01\u4E1A\u8D26\u53F7\u767B\u5F55\u5C06\u81EA\u52A8\u521B\u5EFA\u8D26\u6237" }) })
    ] })
  ] });
};
export default HybridLogin;
