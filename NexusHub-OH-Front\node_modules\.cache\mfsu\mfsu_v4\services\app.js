"use strict";
import { request } from "@umijs/max";
export async function getAppList(params) {
  return request("/apps", {
    method: "GET",
    params
  });
}
export async function getAppDetail(id) {
  return request(`/apps/${id}`, {
    method: "GET"
  });
}
export async function searchApps(params) {
  return request("/app/search", {
    method: "GET",
    params
  });
}
export async function getAppCategories() {
  return request("/app/category", {
    method: "GET"
  });
}
export async function createApp(params) {
  return request("/apps", {
    method: "POST",
    data: params
  });
}
export async function uploadApp(params) {
  return request("/app/upload", {
    method: "POST",
    data: params
  });
}
export async function uploadAppIcon(appId, formData) {
  return request(`/app/icon/${appId}`, {
    method: "POST",
    data: formData
  });
}
export async function uploadAppScreenshot(appId, formData) {
  return request(`/app/screenshot/${appId}`, {
    method: "POST",
    data: formData
  });
}
export async function uploadAppPackage(appId, formData) {
  return request(`/app/package/${appId}`, {
    method: "POST",
    data: formData
  });
}
export async function updateApp(id, params) {
  return request(`/apps/${id}`, {
    method: "PUT",
    data: params
  });
}
export async function deleteApp(id) {
  return request(`/app/delete/${id}`, {
    method: "DELETE"
  });
}
export async function submitAppForReview(id) {
  return request(`/apps/${id}/submit`, {
    method: "POST"
  });
}
