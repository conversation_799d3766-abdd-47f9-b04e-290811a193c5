"use strict";
import { request } from "@umijs/max";
export async function getCategories(params, options) {
  return request("/categories", {
    method: "GET",
    params: {
      ...params
    },
    ...options || {}
  });
}
export async function postCategories(body, options) {
  return request("/categories", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    data: body,
    ...options || {}
  });
}
export async function getCategoriesId(params, options) {
  const { id: param0, ...queryParams } = params;
  return request(`/categories/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...options || {}
  });
}
export async function putCategoriesId(params, body, options) {
  const { id: param0, ...queryParams } = params;
  return request(`/categories/${param0}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json"
    },
    params: { ...queryParams },
    data: body,
    ...options || {}
  });
}
export async function deleteCategoriesId(params, options) {
  const { id: param0, ...queryParams } = params;
  return request(`/categories/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...options || {}
  });
}
export async function getCategoriesRoot(params, options) {
  return request("/categories/root", {
    method: "GET",
    params: {
      ...params
    },
    ...options || {}
  });
}
