"use strict";
export default {
  "pages.layouts.userLayout.title": "Ant Design \u662F\u897F\u6E56\u533A\u6700\u5177\u5F71\u54CD\u529B\u7684 Web \u8BBE\u8BA1\u89C4\u8303",
  "pages.login.accountLogin.tab": "\u8D26\u6237\u5BC6\u7801\u767B\u5F55",
  "pages.login.emailLogin.tab": "\u90AE\u7BB1\u767B\u5F55",
  "pages.login.accountLogin.errorMessage": "\u9519\u8BEF\u7684\u7528\u6237\u540D\u548C\u5BC6\u7801(admin/ant.design)",
  "pages.login.failure": "\u767B\u5F55\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5\uFF01",
  "pages.login.success": "\u767B\u5F55\u6210\u529F\uFF01",
  "pages.login.username.placeholder": "\u7528\u6237\u540D: admin or user",
  "pages.login.username.required": "\u7528\u6237\u540D\u662F\u5FC5\u586B\u9879\uFF01",
  "pages.login.password.placeholder": "\u5BC6\u7801: ant.design",
  "pages.login.password.required": "\u5BC6\u7801\u662F\u5FC5\u586B\u9879\uFF01",
  "pages.login.phoneLogin.tab": "\u624B\u673A\u53F7\u767B\u5F55",
  "pages.login.phoneLogin.errorMessage": "\u9A8C\u8BC1\u7801\u9519\u8BEF",
  "pages.login.phoneNumber.placeholder": "\u8BF7\u8F93\u5165\u624B\u673A\u53F7\uFF01",
  "pages.login.phoneNumber.required": "\u624B\u673A\u53F7\u662F\u5FC5\u586B\u9879\uFF01",
  "pages.login.phoneNumber.invalid": "\u4E0D\u5408\u6CD5\u7684\u624B\u673A\u53F7\uFF01",
  "pages.login.captcha.placeholder": "\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801\uFF01",
  "pages.login.captcha.required": "\u9A8C\u8BC1\u7801\u662F\u5FC5\u586B\u9879\uFF01",
  "pages.login.phoneLogin.getVerificationCode": "\u83B7\u53D6\u9A8C\u8BC1\u7801",
  "pages.getCaptchaSecondText": "\u79D2\u540E\u91CD\u65B0\u83B7\u53D6",
  "pages.login.rememberMe": "\u81EA\u52A8\u767B\u5F55",
  "pages.login.forgotPassword": "\u5FD8\u8BB0\u5BC6\u7801 ?",
  "pages.login.submit": "\u767B\u5F55",
  "pages.login.loginWith": "\u5176\u4ED6\u767B\u5F55\u65B9\u5F0F :",
  "pages.login.registerAccount": "\u6CE8\u518C\u8D26\u6237",
  "pages.login.appDesc": "\u4E00\u7AD9\u5F0F\u5E94\u7528\u7BA1\u7406\u4E0E\u5206\u53D1\u5E73\u53F0",
  "pages.welcome.link": "\u6B22\u8FCE\u4F7F\u7528",
  "pages.welcome.alertMessage": "\u66F4\u5FEB\u66F4\u5F3A\u7684\u91CD\u578B\u7EC4\u4EF6\uFF0C\u5DF2\u7ECF\u53D1\u5E03\u3002",
  "pages.404.subTitle": "\u62B1\u6B49\uFF0C\u60A8\u8BBF\u95EE\u7684\u9875\u9762\u4E0D\u5B58\u5728\u3002",
  "pages.404.buttonText": "\u8FD4\u56DE\u9996\u9875",
  "pages.admin.subPage.title": " \u8FD9\u4E2A\u9875\u9762\u53EA\u6709 admin \u6743\u9650\u624D\u80FD\u67E5\u770B",
  "pages.admin.subPage.alertMessage": "umi ui \u73B0\u5DF2\u53D1\u5E03\uFF0C\u6B22\u8FCE\u4F7F\u7528 npm run ui \u542F\u52A8\u4F53\u9A8C\u3002",
  "pages.searchTable.createForm.newRule": "\u65B0\u5EFA\u89C4\u5219",
  "pages.searchTable.updateForm.ruleConfig": "\u89C4\u5219\u914D\u7F6E",
  "pages.searchTable.updateForm.basicConfig": "\u57FA\u672C\u4FE1\u606F",
  "pages.searchTable.updateForm.ruleName.nameLabel": "\u89C4\u5219\u540D\u79F0",
  "pages.searchTable.updateForm.ruleName.nameRules": "\u8BF7\u8F93\u5165\u89C4\u5219\u540D\u79F0\uFF01",
  "pages.searchTable.updateForm.ruleDesc.descLabel": "\u89C4\u5219\u63CF\u8FF0",
  "pages.searchTable.updateForm.ruleDesc.descPlaceholder": "\u8BF7\u8F93\u5165\u81F3\u5C11\u4E94\u4E2A\u5B57\u7B26",
  "pages.searchTable.updateForm.ruleDesc.descRules": "\u8BF7\u8F93\u5165\u81F3\u5C11\u4E94\u4E2A\u5B57\u7B26\u7684\u89C4\u5219\u63CF\u8FF0\uFF01",
  "pages.searchTable.updateForm.ruleProps.title": "\u914D\u7F6E\u89C4\u5219\u5C5E\u6027",
  "pages.searchTable.updateForm.object": "\u76D1\u63A7\u5BF9\u8C61",
  "pages.searchTable.updateForm.ruleProps.templateLabel": "\u89C4\u5219\u6A21\u677F",
  "pages.searchTable.updateForm.ruleProps.typeLabel": "\u89C4\u5219\u7C7B\u578B",
  "pages.searchTable.updateForm.schedulingPeriod.title": "\u8BBE\u5B9A\u8C03\u5EA6\u5468\u671F",
  "pages.searchTable.updateForm.schedulingPeriod.timeLabel": "\u5F00\u59CB\u65F6\u95F4",
  "pages.searchTable.updateForm.schedulingPeriod.timeRules": "\u8BF7\u9009\u62E9\u5F00\u59CB\u65F6\u95F4\uFF01",
  "pages.searchTable.titleDesc": "\u63CF\u8FF0",
  "pages.searchTable.ruleName": "\u89C4\u5219\u540D\u79F0\u4E3A\u5FC5\u586B\u9879",
  "pages.searchTable.titleCallNo": "\u670D\u52A1\u8C03\u7528\u6B21\u6570",
  "pages.searchTable.titleStatus": "\u72B6\u6001",
  "pages.searchTable.nameStatus.default": "\u5173\u95ED",
  "pages.searchTable.nameStatus.running": "\u8FD0\u884C\u4E2D",
  "pages.searchTable.nameStatus.online": "\u5DF2\u4E0A\u7EBF",
  "pages.searchTable.nameStatus.abnormal": "\u5F02\u5E38",
  "pages.searchTable.titleUpdatedAt": "\u4E0A\u6B21\u8C03\u5EA6\u65F6\u95F4",
  "pages.searchTable.exception": "\u8BF7\u8F93\u5165\u5F02\u5E38\u539F\u56E0\uFF01",
  "pages.searchTable.titleOption": "\u64CD\u4F5C",
  "pages.searchTable.config": "\u914D\u7F6E",
  "pages.searchTable.subscribeAlert": "\u8BA2\u9605\u8B66\u62A5",
  "pages.searchTable.title": "\u67E5\u8BE2\u8868\u683C",
  "pages.searchTable.new": "\u65B0\u5EFA",
  "pages.searchTable.chosen": "\u5DF2\u9009\u62E9",
  "pages.searchTable.item": "\u9879",
  "pages.searchTable.totalServiceCalls": "\u670D\u52A1\u8C03\u7528\u6B21\u6570\u603B\u8BA1",
  "pages.searchTable.tenThousand": "\u4E07",
  "pages.searchTable.batchDeletion": "\u6279\u91CF\u5220\u9664",
  "pages.searchTable.batchApproval": "\u6279\u91CF\u5BA1\u6279"
};
