{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "d92500be-6745-45ad-a800-51c1636f2240", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255713782700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3d1e359-3d95-4966-8016-e533ceaffa1e", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255713928700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23e68f68-1297-4d70-9e84-52bab0b682b9", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255716079600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88367a56-7a46-4722-ac4f-9b6d3d4d62ae", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255716527500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "296dcb87-9851-4a47-b19f-5576896ab40b", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255718179700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "776cab53-0b90-46e6-9f81-2a49221192d9", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255719820600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68ef24da-5c04-40fd-ab51-77bd7f02c169", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255721049700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f1b656f-9c17-4c64-a6a9-808a4ff7902e", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153255762926100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7b562a0-89a7-467b-9225-4f1d6b8ce370", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373922143900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edde55e7-d414-40f1-8785-66af1dcb0016", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373927354800, "endTime": 153374107138100}, "additional": {"children": ["a563be09-4b47-4165-aa14-baa0c80366ee", "e340a97f-5c0d-45f3-851d-a2a5d82cfbab", "7f4fec86-5c2f-4c57-ab7a-dbd6f295692e", "88458693-f545-40bc-8174-b10c9fbe86b5", "9cd07309-cd96-4997-a7cf-51aea0d6982c", "ce3461fc-0d26-43e2-8526-d05d816603c1", "584b60fa-66e1-4715-a370-6dfe1adf2485"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "a4fa8dbc-5732-4718-a561-c47d63415fa4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a563be09-4b47-4165-aa14-baa0c80366ee", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373927356400, "endTime": 153373941053200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "edde55e7-d414-40f1-8785-66af1dcb0016", "logId": "9e9b04b6-3fb7-4daa-97c8-35d0042b9341"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e340a97f-5c0d-45f3-851d-a2a5d82cfbab", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373941066700, "endTime": 153374105891200}, "additional": {"children": ["6debcca5-27b8-4ca3-9f6a-1d478568d606", "0b620ffa-679d-4d02-abc3-dae566786feb", "d25c071b-5364-4945-aaca-15a83c212c55", "c4aeb05b-892f-4824-b761-55015d7bfe53", "79614560-f513-47ee-aa62-5151c4c4f4f7", "2396275a-4c43-4492-9749-958e0d189763", "346ea155-04dd-4e93-b113-fcef4aed2cd8", "24cdea13-7a0f-409b-8bdb-330e2c0ddec4", "357f6438-8eb3-4908-a79d-8a3bda9a85dc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "edde55e7-d414-40f1-8785-66af1dcb0016", "logId": "60bd9b58-6f74-4e40-83a5-9068200f8280"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f4fec86-5c2f-4c57-ab7a-dbd6f295692e", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374105919200, "endTime": 153374107112000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "edde55e7-d414-40f1-8785-66af1dcb0016", "logId": "096e87e8-9b98-4077-8203-1a2c115040e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88458693-f545-40bc-8174-b10c9fbe86b5", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374107116500, "endTime": 153374107134800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "edde55e7-d414-40f1-8785-66af1dcb0016", "logId": "ccb8f941-a81f-4837-94ee-4ee093af4fcf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9cd07309-cd96-4997-a7cf-51aea0d6982c", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373931833800, "endTime": 153373931878900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "edde55e7-d414-40f1-8785-66af1dcb0016", "logId": "c28d1658-ad25-4a6c-b36d-33155788be94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c28d1658-ad25-4a6c-b36d-33155788be94", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373931833800, "endTime": 153373931878900}, "additional": {"logType": "info", "children": [], "durationId": "9cd07309-cd96-4997-a7cf-51aea0d6982c", "parent": "a4fa8dbc-5732-4718-a561-c47d63415fa4"}}, {"head": {"id": "ce3461fc-0d26-43e2-8526-d05d816603c1", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373936235400, "endTime": 153373936255300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "edde55e7-d414-40f1-8785-66af1dcb0016", "logId": "6bbbf616-003d-438d-8d3a-26aacced1289"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bbbf616-003d-438d-8d3a-26aacced1289", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373936235400, "endTime": 153373936255300}, "additional": {"logType": "info", "children": [], "durationId": "ce3461fc-0d26-43e2-8526-d05d816603c1", "parent": "a4fa8dbc-5732-4718-a561-c47d63415fa4"}}, {"head": {"id": "ca1c2373-207c-4cdc-b062-0aed7fda3255", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373936303800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "986b3fbd-1aaa-435f-8c49-bd73cfc5886b", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373940938200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e9b04b6-3fb7-4daa-97c8-35d0042b9341", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373927356400, "endTime": 153373941053200}, "additional": {"logType": "info", "children": [], "durationId": "a563be09-4b47-4165-aa14-baa0c80366ee", "parent": "a4fa8dbc-5732-4718-a561-c47d63415fa4"}}, {"head": {"id": "6debcca5-27b8-4ca3-9f6a-1d478568d606", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373946462800, "endTime": 153373946474100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e340a97f-5c0d-45f3-851d-a2a5d82cfbab", "logId": "39ff74be-5782-4401-a113-46012542150e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b620ffa-679d-4d02-abc3-dae566786feb", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373946491600, "endTime": 153373951170100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e340a97f-5c0d-45f3-851d-a2a5d82cfbab", "logId": "f6942f03-1ab8-43b6-8177-4ba988df25f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d25c071b-5364-4945-aaca-15a83c212c55", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373951182900, "endTime": 153374020394700}, "additional": {"children": ["3e95a5a7-7e49-4307-9437-79575e88fca2", "774ed4c1-83fc-4c84-8d10-9bd6815ce10f", "159defe0-7801-4237-a71d-cbc22a0bf89e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e340a97f-5c0d-45f3-851d-a2a5d82cfbab", "logId": "0179540a-acdd-47d5-9108-62388135e0f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4aeb05b-892f-4824-b761-55015d7bfe53", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374020407500, "endTime": 153374038057800}, "additional": {"children": ["acf93ed4-0922-4ce9-a5d8-d23b95a7639b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e340a97f-5c0d-45f3-851d-a2a5d82cfbab", "logId": "848b08e8-9340-49dc-be8c-fae1349199ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79614560-f513-47ee-aa62-5151c4c4f4f7", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374038064900, "endTime": 153374079791600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e340a97f-5c0d-45f3-851d-a2a5d82cfbab", "logId": "6f502cee-6571-4b0d-b3e1-b046fed36567"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2396275a-4c43-4492-9749-958e0d189763", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374081355000, "endTime": 153374091141400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e340a97f-5c0d-45f3-851d-a2a5d82cfbab", "logId": "247fbc90-408d-454c-9d0c-8942a058fffe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "346ea155-04dd-4e93-b113-fcef4aed2cd8", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374091163700, "endTime": 153374105753100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e340a97f-5c0d-45f3-851d-a2a5d82cfbab", "logId": "7a2f315e-827d-4aba-ad25-4e6b08f36bba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24cdea13-7a0f-409b-8bdb-330e2c0ddec4", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374105770800, "endTime": 153374105879300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e340a97f-5c0d-45f3-851d-a2a5d82cfbab", "logId": "e3a528e3-bf91-40b0-946a-8488d8d21d90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39ff74be-5782-4401-a113-46012542150e", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373946462800, "endTime": 153373946474100}, "additional": {"logType": "info", "children": [], "durationId": "6debcca5-27b8-4ca3-9f6a-1d478568d606", "parent": "60bd9b58-6f74-4e40-83a5-9068200f8280"}}, {"head": {"id": "f6942f03-1ab8-43b6-8177-4ba988df25f2", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373946491600, "endTime": 153373951170100}, "additional": {"logType": "info", "children": [], "durationId": "0b620ffa-679d-4d02-abc3-dae566786feb", "parent": "60bd9b58-6f74-4e40-83a5-9068200f8280"}}, {"head": {"id": "3e95a5a7-7e49-4307-9437-79575e88fca2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373951712000, "endTime": 153373951737600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d25c071b-5364-4945-aaca-15a83c212c55", "logId": "a657be06-682b-43fc-b568-1d4d6febe2b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a657be06-682b-43fc-b568-1d4d6febe2b2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373951712000, "endTime": 153373951737600}, "additional": {"logType": "info", "children": [], "durationId": "3e95a5a7-7e49-4307-9437-79575e88fca2", "parent": "0179540a-acdd-47d5-9108-62388135e0f0"}}, {"head": {"id": "774ed4c1-83fc-4c84-8d10-9bd6815ce10f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373953376700, "endTime": 153374019701800}, "additional": {"children": ["12a66661-4fb3-44c6-acbd-6a9167cbabdf", "2b605b02-1523-4557-9e79-ec7837f88cd7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d25c071b-5364-4945-aaca-15a83c212c55", "logId": "980b8023-d651-4950-be98-d4d80ba2782f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12a66661-4fb3-44c6-acbd-6a9167cbabdf", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373953377700, "endTime": 153373958764300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "774ed4c1-83fc-4c84-8d10-9bd6815ce10f", "logId": "f524f7de-7251-42c8-97d6-44c3b44bd6e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b605b02-1523-4557-9e79-ec7837f88cd7", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373958781000, "endTime": 153374019691600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "774ed4c1-83fc-4c84-8d10-9bd6815ce10f", "logId": "115ef0ab-ad99-4f1c-bf2b-6fff21831831"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9807c2b-6705-4366-b16e-304d4569476b", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373953382300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "118e2830-3386-4a0e-8959-d67778e39850", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373958626100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f524f7de-7251-42c8-97d6-44c3b44bd6e3", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373953377700, "endTime": 153373958764300}, "additional": {"logType": "info", "children": [], "durationId": "12a66661-4fb3-44c6-acbd-6a9167cbabdf", "parent": "980b8023-d651-4950-be98-d4d80ba2782f"}}, {"head": {"id": "f86b0502-89b6-4ebe-8e9e-15633503069b", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373958793600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a059e8a-e351-4e4e-a0ff-0448a866ed56", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373966054200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6df64ee2-afa1-40bf-a446-0f2c83edd4d7", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373966204400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "020e849d-5c67-4685-bcef-ffc6ef38c451", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373966316600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be490a99-3430-427c-aac0-0ba9a0685452", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373966434100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c099e9b5-6349-4508-8e4c-652aec8d5ed7", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373967847500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5acce98a-a0a3-4b97-8c8f-82cb5a4fa2d9", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373978982600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d6e595b-120e-43a0-9c25-df9e8a0e5c77", "name": "Sdk init in 28 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374000027300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11de1ddd-c3a8-4975-99fc-7e08d11764c2", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374000222300}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 33, "second": 23}, "markType": "other"}}, {"head": {"id": "41f8d364-4bb0-4fab-be3a-3309dabf7f46", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374000239700}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 33, "second": 23}, "markType": "other"}}, {"head": {"id": "cece2965-1e1e-4fb3-9264-e8a615fa32a4", "name": "Project task initialization takes 18 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374019370200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dfefc4a-7903-4517-849f-61dbecf03b32", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374019558200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ad3c98f-c35a-49dd-982d-d89781320eaa", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374019624600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b23a2ad2-a7cc-4217-91f2-74b525df28e7", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374019659900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "115ef0ab-ad99-4f1c-bf2b-6fff21831831", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373958781000, "endTime": 153374019691600}, "additional": {"logType": "info", "children": [], "durationId": "2b605b02-1523-4557-9e79-ec7837f88cd7", "parent": "980b8023-d651-4950-be98-d4d80ba2782f"}}, {"head": {"id": "980b8023-d651-4950-be98-d4d80ba2782f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373953376700, "endTime": 153374019701800}, "additional": {"logType": "info", "children": ["f524f7de-7251-42c8-97d6-44c3b44bd6e3", "115ef0ab-ad99-4f1c-bf2b-6fff21831831"], "durationId": "774ed4c1-83fc-4c84-8d10-9bd6815ce10f", "parent": "0179540a-acdd-47d5-9108-62388135e0f0"}}, {"head": {"id": "159defe0-7801-4237-a71d-cbc22a0bf89e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374020244800, "endTime": 153374020380400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d25c071b-5364-4945-aaca-15a83c212c55", "logId": "a13a4dce-b66a-4fcf-96e2-dd803ed32824"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a13a4dce-b66a-4fcf-96e2-dd803ed32824", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374020244800, "endTime": 153374020380400}, "additional": {"logType": "info", "children": [], "durationId": "159defe0-7801-4237-a71d-cbc22a0bf89e", "parent": "0179540a-acdd-47d5-9108-62388135e0f0"}}, {"head": {"id": "0179540a-acdd-47d5-9108-62388135e0f0", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373951182900, "endTime": 153374020394700}, "additional": {"logType": "info", "children": ["a657be06-682b-43fc-b568-1d4d6febe2b2", "980b8023-d651-4950-be98-d4d80ba2782f", "a13a4dce-b66a-4fcf-96e2-dd803ed32824"], "durationId": "d25c071b-5364-4945-aaca-15a83c212c55", "parent": "60bd9b58-6f74-4e40-83a5-9068200f8280"}}, {"head": {"id": "acf93ed4-0922-4ce9-a5d8-d23b95a7639b", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374020936300, "endTime": 153374038046400}, "additional": {"children": ["1ec9b441-2e0f-488a-bdb2-913208111344", "b9d7f0dc-b1a3-4fc2-ab96-5a57924c9c3f", "afc1b11f-f12d-4377-a7df-06b71b7e6d53"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c4aeb05b-892f-4824-b761-55015d7bfe53", "logId": "725ac830-b42b-482b-af26-aba5c8c18632"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ec9b441-2e0f-488a-bdb2-913208111344", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374023191500, "endTime": 153374023205600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "acf93ed4-0922-4ce9-a5d8-d23b95a7639b", "logId": "2c4babdc-7028-4e9e-b41d-108712a5ff58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c4babdc-7028-4e9e-b41d-108712a5ff58", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374023191500, "endTime": 153374023205600}, "additional": {"logType": "info", "children": [], "durationId": "1ec9b441-2e0f-488a-bdb2-913208111344", "parent": "725ac830-b42b-482b-af26-aba5c8c18632"}}, {"head": {"id": "b9d7f0dc-b1a3-4fc2-ab96-5a57924c9c3f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374024643800, "endTime": 153374036667700}, "additional": {"children": ["78cd775e-cdd1-4fda-88b4-c6462f2e7bee", "42c20d7f-d5c5-432f-bd3f-23b72cf889ab"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "acf93ed4-0922-4ce9-a5d8-d23b95a7639b", "logId": "41413ab7-f3b5-41d6-980e-b7ad20bbdad7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78cd775e-cdd1-4fda-88b4-c6462f2e7bee", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374024644700, "endTime": 153374027180100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9d7f0dc-b1a3-4fc2-ab96-5a57924c9c3f", "logId": "a8da55c5-2352-4f82-8ff7-f8f8992342c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42c20d7f-d5c5-432f-bd3f-23b72cf889ab", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374027189100, "endTime": 153374036656600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9d7f0dc-b1a3-4fc2-ab96-5a57924c9c3f", "logId": "c2ae647d-abfd-4905-9dd4-8c8cb6a12b04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ec28764-35f8-4a14-bd15-6327a7374a7e", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374024648400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ade30654-0fcf-4cc5-b8cb-fc901d534281", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374027094600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8da55c5-2352-4f82-8ff7-f8f8992342c9", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374024644700, "endTime": 153374027180100}, "additional": {"logType": "info", "children": [], "durationId": "78cd775e-cdd1-4fda-88b4-c6462f2e7bee", "parent": "41413ab7-f3b5-41d6-980e-b7ad20bbdad7"}}, {"head": {"id": "a5d3e05b-3049-4f1b-9469-f4aa0af5e006", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374027197100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "013350a6-08d6-49cc-8df2-7f37512efd60", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374032624100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "672672f0-b67c-40f9-8932-2da6231a3407", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374032740800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f670aa96-8ae8-4e79-8258-252cead9ef61", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374032887200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffe5ed16-d2d1-4e45-89d7-e02465016c48", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374032971700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb3f8e67-9512-4c32-a94c-7df89a1fa1d8", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033006800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63a5c469-2787-48be-806f-5912f72646ae", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033041300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbc6795b-891b-493e-847f-9794cbaa50b6", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033093400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d169c694-78e3-4e14-adf2-446f29865984", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033124900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a94cddc-e772-4865-a06b-b963c92239b6", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033256400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad0799f9-8242-4995-8a5f-075a12a74e51", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033321700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "378ccadb-cb7e-4ef1-837e-d66e5ad657e5", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033356100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c86be635-ec61-4274-8cf1-0afacf302141", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033392700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0cd677e-b8a2-4b21-9802-91150c433f6b", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033434300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99dc1dcf-701c-4cc0-b231-d6809161b437", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033464000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f96fd3f-7214-4066-9680-5e046d92fcac", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033585800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88134638-6f95-4c71-b606-7a04efaa464e", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033651200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c95cd65-9e3a-47a6-82bc-5d96740f6c3e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033678400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35994414-49a4-4aa2-880d-ac42559fc06a", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033702900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9332f22b-d5d2-4aa9-bfa9-4b4708b95883", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374033735200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf2db3bc-d4db-425d-ae25-b4635ceaaeaa", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374036356900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee39ddba-5385-43c5-ae2c-72e29fbc4329", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374036502600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82ac7c55-c172-40da-b203-9f850264d82f", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374036554600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4afd5c99-d50b-4c5d-9572-c739dae59be0", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374036623100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2ae647d-abfd-4905-9dd4-8c8cb6a12b04", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374027189100, "endTime": 153374036656600}, "additional": {"logType": "info", "children": [], "durationId": "42c20d7f-d5c5-432f-bd3f-23b72cf889ab", "parent": "41413ab7-f3b5-41d6-980e-b7ad20bbdad7"}}, {"head": {"id": "41413ab7-f3b5-41d6-980e-b7ad20bbdad7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374024643800, "endTime": 153374036667700}, "additional": {"logType": "info", "children": ["a8da55c5-2352-4f82-8ff7-f8f8992342c9", "c2ae647d-abfd-4905-9dd4-8c8cb6a12b04"], "durationId": "b9d7f0dc-b1a3-4fc2-ab96-5a57924c9c3f", "parent": "725ac830-b42b-482b-af26-aba5c8c18632"}}, {"head": {"id": "afc1b11f-f12d-4377-a7df-06b71b7e6d53", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374037976800, "endTime": 153374037993100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "acf93ed4-0922-4ce9-a5d8-d23b95a7639b", "logId": "5c72b6aa-582b-4a04-8774-e30ed2099e6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c72b6aa-582b-4a04-8774-e30ed2099e6a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374037976800, "endTime": 153374037993100}, "additional": {"logType": "info", "children": [], "durationId": "afc1b11f-f12d-4377-a7df-06b71b7e6d53", "parent": "725ac830-b42b-482b-af26-aba5c8c18632"}}, {"head": {"id": "725ac830-b42b-482b-af26-aba5c8c18632", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374020936300, "endTime": 153374038046400}, "additional": {"logType": "info", "children": ["2c4babdc-7028-4e9e-b41d-108712a5ff58", "41413ab7-f3b5-41d6-980e-b7ad20bbdad7", "5c72b6aa-582b-4a04-8774-e30ed2099e6a"], "durationId": "acf93ed4-0922-4ce9-a5d8-d23b95a7639b", "parent": "848b08e8-9340-49dc-be8c-fae1349199ae"}}, {"head": {"id": "848b08e8-9340-49dc-be8c-fae1349199ae", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374020407500, "endTime": 153374038057800}, "additional": {"logType": "info", "children": ["725ac830-b42b-482b-af26-aba5c8c18632"], "durationId": "c4aeb05b-892f-4824-b761-55015d7bfe53", "parent": "60bd9b58-6f74-4e40-83a5-9068200f8280"}}, {"head": {"id": "73f65550-656e-46ac-a18f-f1d724fe9956", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374050228300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93493e0d-05a4-4570-a0ed-054d1e44b8b8", "name": "hvigorfile, resolve hvigorfile dependencies in 42 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374079628100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f502cee-6571-4b0d-b3e1-b046fed36567", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374038064900, "endTime": 153374079791600}, "additional": {"logType": "info", "children": [], "durationId": "79614560-f513-47ee-aa62-5151c4c4f4f7", "parent": "60bd9b58-6f74-4e40-83a5-9068200f8280"}}, {"head": {"id": "357f6438-8eb3-4908-a79d-8a3bda9a85dc", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374081130200, "endTime": 153374081338900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e340a97f-5c0d-45f3-851d-a2a5d82cfbab", "logId": "ac189cc2-ed20-4f67-b541-44e22e27ee9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b850bd7f-16a9-4b28-a970-9051b873cdf5", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374081168100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac189cc2-ed20-4f67-b541-44e22e27ee9b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374081130200, "endTime": 153374081338900}, "additional": {"logType": "info", "children": [], "durationId": "357f6438-8eb3-4908-a79d-8a3bda9a85dc", "parent": "60bd9b58-6f74-4e40-83a5-9068200f8280"}}, {"head": {"id": "60660d5a-310b-4ebe-bda9-c3640849a0ec", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374082827900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56c72219-7bbf-40f5-9238-fcc1bc815304", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374090235600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "247fbc90-408d-454c-9d0c-8942a058fffe", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374081355000, "endTime": 153374091141400}, "additional": {"logType": "info", "children": [], "durationId": "2396275a-4c43-4492-9749-958e0d189763", "parent": "60bd9b58-6f74-4e40-83a5-9068200f8280"}}, {"head": {"id": "8ac7c3e9-4416-4fc0-b291-1d092c0f2169", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374091183900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a9339de-b5f1-4c07-9476-bc305cc93318", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374098784700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e20ff671-a888-420a-a3cf-736ffd91631d", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374098992500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e2edbb4-894c-4f49-b2d0-dc860d41313a", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374099149600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "228de6de-eb40-4836-bd1e-2963f085802a", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374102351000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df7b5a69-f942-4436-a9fa-c1613531326d", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374102449500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a2f315e-827d-4aba-ad25-4e6b08f36bba", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374091163700, "endTime": 153374105753100}, "additional": {"logType": "info", "children": [], "durationId": "346ea155-04dd-4e93-b113-fcef4aed2cd8", "parent": "60bd9b58-6f74-4e40-83a5-9068200f8280"}}, {"head": {"id": "ec093c2b-fdf5-41bd-a7ac-566d86b2be1d", "name": "Configuration phase cost:160 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374105793600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3a528e3-bf91-40b0-946a-8488d8d21d90", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374105770800, "endTime": 153374105879300}, "additional": {"logType": "info", "children": [], "durationId": "24cdea13-7a0f-409b-8bdb-330e2c0ddec4", "parent": "60bd9b58-6f74-4e40-83a5-9068200f8280"}}, {"head": {"id": "60bd9b58-6f74-4e40-83a5-9068200f8280", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373941066700, "endTime": 153374105891200}, "additional": {"logType": "info", "children": ["39ff74be-5782-4401-a113-46012542150e", "f6942f03-1ab8-43b6-8177-4ba988df25f2", "0179540a-acdd-47d5-9108-62388135e0f0", "848b08e8-9340-49dc-be8c-fae1349199ae", "6f502cee-6571-4b0d-b3e1-b046fed36567", "247fbc90-408d-454c-9d0c-8942a058fffe", "7a2f315e-827d-4aba-ad25-4e6b08f36bba", "e3a528e3-bf91-40b0-946a-8488d8d21d90", "ac189cc2-ed20-4f67-b541-44e22e27ee9b"], "durationId": "e340a97f-5c0d-45f3-851d-a2a5d82cfbab", "parent": "a4fa8dbc-5732-4718-a561-c47d63415fa4"}}, {"head": {"id": "584b60fa-66e1-4715-a370-6dfe1adf2485", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374107084600, "endTime": 153374107100400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "edde55e7-d414-40f1-8785-66af1dcb0016", "logId": "a20afbd6-39b3-4ee8-9f71-e34e6cccd3b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a20afbd6-39b3-4ee8-9f71-e34e6cccd3b8", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374107084600, "endTime": 153374107100400}, "additional": {"logType": "info", "children": [], "durationId": "584b60fa-66e1-4715-a370-6dfe1adf2485", "parent": "a4fa8dbc-5732-4718-a561-c47d63415fa4"}}, {"head": {"id": "096e87e8-9b98-4077-8203-1a2c115040e1", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374105919200, "endTime": 153374107112000}, "additional": {"logType": "info", "children": [], "durationId": "7f4fec86-5c2f-4c57-ab7a-dbd6f295692e", "parent": "a4fa8dbc-5732-4718-a561-c47d63415fa4"}}, {"head": {"id": "ccb8f941-a81f-4837-94ee-4ee093af4fcf", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374107116500, "endTime": 153374107134800}, "additional": {"logType": "info", "children": [], "durationId": "88458693-f545-40bc-8174-b10c9fbe86b5", "parent": "a4fa8dbc-5732-4718-a561-c47d63415fa4"}}, {"head": {"id": "a4fa8dbc-5732-4718-a561-c47d63415fa4", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373927354800, "endTime": 153374107138100}, "additional": {"logType": "info", "children": ["9e9b04b6-3fb7-4daa-97c8-35d0042b9341", "60bd9b58-6f74-4e40-83a5-9068200f8280", "096e87e8-9b98-4077-8203-1a2c115040e1", "ccb8f941-a81f-4837-94ee-4ee093af4fcf", "c28d1658-ad25-4a6c-b36d-33155788be94", "6bbbf616-003d-438d-8d3a-26aacced1289", "a20afbd6-39b3-4ee8-9f71-e34e6cccd3b8"], "durationId": "edde55e7-d414-40f1-8785-66af1dcb0016"}}, {"head": {"id": "51176621-ca9a-4368-ad99-19054c085fcb", "name": "Configuration task cost before running: 183 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374107300300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31471788-b7cc-4a8b-915a-5ca11fec3bec", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374116017800, "endTime": 153374128467900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "72e2930e-54c8-4088-9416-46291b49d8d8", "logId": "6b6c693f-f022-4755-adb1-dd3cbfb8e431"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72e2930e-54c8-4088-9416-46291b49d8d8", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374108690100}, "additional": {"logType": "detail", "children": [], "durationId": "31471788-b7cc-4a8b-915a-5ca11fec3bec"}}, {"head": {"id": "3fc79bb2-a92a-48af-9ad4-93b020ea38fc", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374109398500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efb2c7cb-42aa-4355-94aa-8071c11339cb", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374109499500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b357388c-51c4-43cc-af1f-4d8a55152fba", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374110142300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b7b123c-307d-48d3-bd8d-f521639cf2be", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374111073500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76e23567-7ae6-4bde-ae73-ebbbfbc08519", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374112090400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "770eff29-6074-4ec8-90d7-d633d9045065", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374112169100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae516a18-a46e-498d-8ed1-73611f7e266b", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374116032800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9b00b46-5eca-4df1-ae1e-22f5758793dc", "name": "Incremental task entry:default@PreBuild pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374128178400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de7c7494-9029-4627-9e65-2df341034730", "name": "entry : default@PreBuild cost memory -3.51922607421875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374128361700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b6c693f-f022-4755-adb1-dd3cbfb8e431", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374116017800, "endTime": 153374128467900}, "additional": {"logType": "info", "children": [], "durationId": "31471788-b7cc-4a8b-915a-5ca11fec3bec"}}, {"head": {"id": "83cb11ba-0faf-40bf-8b24-bda9860e4448", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374135125600, "endTime": 153374136891100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9b074c7b-81d5-412b-85fd-a6cbe63bc795", "logId": "19d74ff1-93b5-4e58-a4e0-b083ec993658"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b074c7b-81d5-412b-85fd-a6cbe63bc795", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374133293300}, "additional": {"logType": "detail", "children": [], "durationId": "83cb11ba-0faf-40bf-8b24-bda9860e4448"}}, {"head": {"id": "d25cdafb-598b-460c-894b-a4e1bff111ef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374134302000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08c7d6e0-57c6-4565-a938-ca31685fb2d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374134407900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60ed3052-48c7-4dc6-b712-03ef5c8b9e13", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374135136700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94240cb1-d04c-4203-9b65-b25fbd6c9564", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374135810300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f68e95c4-efb8-4bd0-8695-346d91c6aeec", "name": "entry : default@CreateModuleInfo cost memory 0.0604248046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374136715000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31d9e5e1-c356-4716-9084-7ac724ebf25d", "name": "runTaskFromQueue task cost before running: 212 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374136836600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19d74ff1-93b5-4e58-a4e0-b083ec993658", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374135125600, "endTime": 153374136891100, "totalTime": 1689200}, "additional": {"logType": "info", "children": [], "durationId": "83cb11ba-0faf-40bf-8b24-bda9860e4448"}}, {"head": {"id": "90d7f3ea-9636-4c76-9f34-e7f10630dd20", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374144764100, "endTime": 153374147828800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "586ad9db-b2f7-491d-ad9f-737353694e91", "logId": "a0ad1b70-16a1-4fea-88f5-02d790b272c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "586ad9db-b2f7-491d-ad9f-737353694e91", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374139027200}, "additional": {"logType": "detail", "children": [], "durationId": "90d7f3ea-9636-4c76-9f34-e7f10630dd20"}}, {"head": {"id": "1b10f8b5-8a00-4871-b9fa-5729e89e0b18", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374140282200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f23a1b36-255f-4d4d-a94c-34dd54d0f3eb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374140382100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83474774-fe24-4b43-8c35-a2384dd1ac8d", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374144785000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf7829d3-2d30-4bfb-a2b0-20ff70d54643", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374146277600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "453778fa-7ca7-4e7f-9431-bd5c49ec5cca", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374147619900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6cb57a0-0720-4917-b4df-d09b3e36b750", "name": "entry : default@GenerateMetadata cost memory 0.1029205322265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374147756900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0ad1b70-16a1-4fea-88f5-02d790b272c4", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374144764100, "endTime": 153374147828800}, "additional": {"logType": "info", "children": [], "durationId": "90d7f3ea-9636-4c76-9f34-e7f10630dd20"}}, {"head": {"id": "16c19b6b-f54b-453c-92e1-61a8da3f790f", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374151603700, "endTime": 153374151938500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "275fcd96-fc6c-4bdb-8729-fdc3bba0957c", "logId": "e744988a-b14f-4386-85b7-68ad33187256"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "275fcd96-fc6c-4bdb-8729-fdc3bba0957c", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374150159800}, "additional": {"logType": "detail", "children": [], "durationId": "16c19b6b-f54b-453c-92e1-61a8da3f790f"}}, {"head": {"id": "322057cc-7aa9-451a-8488-fa222f0c94fa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374151353800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e195134e-8873-4ae2-be19-6a666cca6341", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374151473000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ba1ddaa-4461-40cb-ac94-c0b389f92fa8", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374151611500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "474d45e1-5fd2-45fc-be6e-6dd4385200f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374151693200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "316b98b0-a296-40ad-b6e7-3fb18f280988", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374151767100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acf184ad-6438-49cc-b76f-01d791ba78c5", "name": "entry : default@ConfigureCmake cost memory 0.0374755859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374151831500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "075ce775-bc69-40a3-9dd9-caa42a8b6a23", "name": "runTaskFromQueue task cost before running: 227 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374151899900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e744988a-b14f-4386-85b7-68ad33187256", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374151603700, "endTime": 153374151938500, "totalTime": 276900}, "additional": {"logType": "info", "children": [], "durationId": "16c19b6b-f54b-453c-92e1-61a8da3f790f"}}, {"head": {"id": "e21d23d7-ab1d-4802-b340-f310fd8c519b", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374155487200, "endTime": 153374157432800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "b9ec2b30-2dba-4904-85e1-8b13d6cb1200", "logId": "212fc734-100f-47e2-9160-1af126f9d761"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9ec2b30-2dba-4904-85e1-8b13d6cb1200", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374153665100}, "additional": {"logType": "detail", "children": [], "durationId": "e21d23d7-ab1d-4802-b340-f310fd8c519b"}}, {"head": {"id": "65ebe250-e31a-4c3a-9459-753b515ae47e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374154700100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f167a65-8565-4b31-8630-a74f03793325", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374154814600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad2ed319-263e-4657-b99e-8dfb4a457ffb", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374155498400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d27d1c0-7277-464b-8d9b-7970123bbd17", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374157251100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d04b28aa-6dd3-4bde-8d81-85b2587a32a5", "name": "entry : default@MergeProfile cost memory 0.11853790283203125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374157370700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "212fc734-100f-47e2-9160-1af126f9d761", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374155487200, "endTime": 153374157432800}, "additional": {"logType": "info", "children": [], "durationId": "e21d23d7-ab1d-4802-b340-f310fd8c519b"}}, {"head": {"id": "47256606-15af-4919-a56f-d8cc5cfdc3c4", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374161439100, "endTime": 153374165031300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "07d4eee9-f6d9-4096-b139-b08a2d693fb1", "logId": "46d9d51e-a376-4d57-8c07-3f9b27d1065a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07d4eee9-f6d9-4096-b139-b08a2d693fb1", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374159396000}, "additional": {"logType": "detail", "children": [], "durationId": "47256606-15af-4919-a56f-d8cc5cfdc3c4"}}, {"head": {"id": "867070a1-72c2-4cca-aadb-95d0e63f5ddb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374160500500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b13d948f-9b65-4a04-92bd-78dd46696efb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374160616000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd85137d-3b6b-4bb4-854f-96cdf5d2d139", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374161448800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b703a78e-04e1-4608-b06b-ad94557ec471", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374162368500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "821ba990-2ebc-4f4d-a6e4-cd1b40530cbf", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374164776900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b743776e-9d8e-487a-8cfa-37f12d2b8100", "name": "entry : default@CreateBuildProfile cost memory 0.10755157470703125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374164952900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46d9d51e-a376-4d57-8c07-3f9b27d1065a", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374161439100, "endTime": 153374165031300}, "additional": {"logType": "info", "children": [], "durationId": "47256606-15af-4919-a56f-d8cc5cfdc3c4"}}, {"head": {"id": "e8f2a8ee-3a68-401a-ab81-1cceb4b167d4", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374170831500, "endTime": 153374171486800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a2f07c79-5295-4c14-8118-1534ff52696f", "logId": "e97dd36e-167b-4de3-92b8-37c1c6fc13e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2f07c79-5295-4c14-8118-1534ff52696f", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374168316100}, "additional": {"logType": "detail", "children": [], "durationId": "e8f2a8ee-3a68-401a-ab81-1cceb4b167d4"}}, {"head": {"id": "962f94e0-9ab1-4e22-b3e7-d2e20934cd8e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374169936000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e2350f5-38ab-49da-b6e9-bf22c10acaf6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374170055300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51437ef2-219a-4b40-bd72-d292622690f8", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374170841300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9948129-faea-47f1-a733-cbe6fa237e0a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374170966700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b38859a2-7fb7-4acf-8478-e961af375c79", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374171029600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6ad397e-709a-4b87-a744-57194b5b2bf9", "name": "entry : default@PreCheckSyscap cost memory 0.041168212890625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374171321600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aba9e89d-0d7c-4022-b4e0-fbd1cb224579", "name": "runTaskFromQueue task cost before running: 247 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374171431600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e97dd36e-167b-4de3-92b8-37c1c6fc13e0", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374170831500, "endTime": 153374171486800, "totalTime": 577900}, "additional": {"logType": "info", "children": [], "durationId": "e8f2a8ee-3a68-401a-ab81-1cceb4b167d4"}}, {"head": {"id": "669f4332-4941-4736-8523-091bba6de407", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374175616700, "endTime": 153374180942200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c5179af6-c1d5-4412-a2bb-78bc415703e2", "logId": "257d6fcf-c91c-426e-ae75-5f6fc3b0e027"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5179af6-c1d5-4412-a2bb-78bc415703e2", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374173103200}, "additional": {"logType": "detail", "children": [], "durationId": "669f4332-4941-4736-8523-091bba6de407"}}, {"head": {"id": "130bdebc-be94-4ba9-a72b-ee7d90a497bf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374174132300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c85e8a8-769a-46fc-b7cc-91c31a207182", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374174230100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70ed3cf6-b44c-437d-9a2f-a7169774d820", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374175627300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d0442c2-c682-4f05-a737-059244a04494", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374179787800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f7c8beb-ed6f-44a2-8dc2-4a1d82e21094", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374180701300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c50e3318-686d-44fa-a9b4-be4c5e9e9d6d", "name": "entry : default@GeneratePkgContextInfo cost memory 0.24828338623046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374180864500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "257d6fcf-c91c-426e-ae75-5f6fc3b0e027", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374175616700, "endTime": 153374180942200}, "additional": {"logType": "info", "children": [], "durationId": "669f4332-4941-4736-8523-091bba6de407"}}, {"head": {"id": "66755f9a-3430-423e-b3aa-10edf8adb048", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374188822200, "endTime": 153374191282200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "282de7b6-3bb6-433f-9622-2c0188bc4b31", "logId": "ceea3700-85b4-434d-809f-38b59c652627"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "282de7b6-3bb6-433f-9622-2c0188bc4b31", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374182712700}, "additional": {"logType": "detail", "children": [], "durationId": "66755f9a-3430-423e-b3aa-10edf8adb048"}}, {"head": {"id": "4e0e2b5e-289a-418d-a20d-5704ae979b44", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374183905100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8759cd9-4a92-4e18-b082-7de52edf5b54", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374184031800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb6c9aa1-1916-4c82-8d30-27473df5de41", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374188838200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "012f1739-d63b-4aa2-a026-4417c3069aa7", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374190795600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a13356ca-2678-4645-8055-770289aff20c", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374190975500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6731fdb6-c21c-414a-a3fb-4207f8a83767", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374191066300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2ed1585-a9c7-4e94-9539-a9d9c46d0129", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374191108200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a63b2002-34b1-4492-ab29-90af72bd3632", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12047576904296875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374191175200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75682ab7-73dc-41e6-9846-7da517942c46", "name": "runTaskFromQueue task cost before running: 267 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374191244600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceea3700-85b4-434d-809f-38b59c652627", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374188822200, "endTime": 153374191282200, "totalTime": 2410600}, "additional": {"logType": "info", "children": [], "durationId": "66755f9a-3430-423e-b3aa-10edf8adb048"}}, {"head": {"id": "ab07eae2-f6b6-4187-94de-7dffb5b447f2", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374196356300, "endTime": 153374196832100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ce8f8081-25ec-4918-b831-d037405b1641", "logId": "b0196f22-fd66-4b9a-b9d6-93e5cb06bfda"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce8f8081-25ec-4918-b831-d037405b1641", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374193354800}, "additional": {"logType": "detail", "children": [], "durationId": "ab07eae2-f6b6-4187-94de-7dffb5b447f2"}}, {"head": {"id": "a13ead5c-d466-4d65-b13f-62453453b157", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374194628500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb7bc4c9-1f92-4e73-a88c-bd493c333b3d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374194803500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e9964ca-7f3e-4bd6-a9f5-0ce673130245", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374196372700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c44f54b-d351-414f-8087-a5af67c263fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374196582100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14a5eabc-a091-41a8-8452-9dbdd49cc2c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374196656000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efbfc66a-6091-47ff-a1f3-34aabb2af9a6", "name": "entry : default@BuildNativeWithCmake cost memory 0.03852081298828125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374196723400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "154c4eeb-89f3-433c-b474-fbcdf3c38f80", "name": "runTaskFromQueue task cost before running: 272 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374196794100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0196f22-fd66-4b9a-b9d6-93e5cb06bfda", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374196356300, "endTime": 153374196832100, "totalTime": 420500}, "additional": {"logType": "info", "children": [], "durationId": "ab07eae2-f6b6-4187-94de-7dffb5b447f2"}}, {"head": {"id": "91016b1f-ca36-4b63-a055-74ded06bd878", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374201033400, "endTime": 153374204754700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fddeed80-fce5-48b7-a042-dc1f2a94ed6e", "logId": "e9068883-320d-4268-a941-3576ba156f73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fddeed80-fce5-48b7-a042-dc1f2a94ed6e", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374198960500}, "additional": {"logType": "detail", "children": [], "durationId": "91016b1f-ca36-4b63-a055-74ded06bd878"}}, {"head": {"id": "bea30a52-0147-4ca2-bf12-8d15f5142e8d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374200092600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bb0b77e-4fb6-4eed-aa85-e20792d9d98a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374200273800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dfe92c9-d2c6-420a-9295-7c1c0acc13b1", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374201043300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14f65477-e072-4dce-aece-dbf38ce075f0", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374204544800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bdab7f8-6b0a-4525-9969-fcb8a27f969f", "name": "entry : default@MakePackInfo cost memory 0.1634063720703125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374204683600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9068883-320d-4268-a941-3576ba156f73", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374201033400, "endTime": 153374204754700}, "additional": {"logType": "info", "children": [], "durationId": "91016b1f-ca36-4b63-a055-74ded06bd878"}}, {"head": {"id": "0b9b77ff-525e-417e-93b4-e6f292eb9365", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374209363900, "endTime": 153374212846200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "02365b53-f845-4c56-b3d0-fceb9210ea94", "logId": "5ad7f74a-92fe-4780-8206-ff1c2346ebf9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02365b53-f845-4c56-b3d0-fceb9210ea94", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374206960300}, "additional": {"logType": "detail", "children": [], "durationId": "0b9b77ff-525e-417e-93b4-e6f292eb9365"}}, {"head": {"id": "8ac00181-5b1e-4e64-9121-a2696415d175", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374208026400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22ba7383-dc4a-4691-9694-230fa3fc4c58", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374208153400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24960f1a-b27b-478e-a889-ec4a09300bd1", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374209377200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "121bda6b-abb2-4d3b-a661-b621945c7a34", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374209591900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd4b099f-23d4-40d7-aa78-b51602253af2", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374210377400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0b60449-1c81-40b1-9b4a-1bb9c55efa7a", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374212631200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8e61019-3dfd-4671-957f-434a83900827", "name": "entry : default@SyscapTransform cost memory 0.1504669189453125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374212775300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ad7f74a-92fe-4780-8206-ff1c2346ebf9", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374209363900, "endTime": 153374212846200}, "additional": {"logType": "info", "children": [], "durationId": "0b9b77ff-525e-417e-93b4-e6f292eb9365"}}, {"head": {"id": "7b592cbd-645c-4a59-ac6b-76647faabdaa", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374217827400, "endTime": 153374221788600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "31650f22-bdd3-4b17-85a6-8a2e44fa1bad", "logId": "7bae74b9-e6ef-49c0-9410-ba14229c8c4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31650f22-bdd3-4b17-85a6-8a2e44fa1bad", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374215283800}, "additional": {"logType": "detail", "children": [], "durationId": "7b592cbd-645c-4a59-ac6b-76647faabdaa"}}, {"head": {"id": "95750693-dfab-4140-8d49-c7eb7870756d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374216594600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b6add14-1a44-40d8-8224-3323f54a423f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374216731700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eb6b50d-9329-452f-ac60-3b070aabd802", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374217839000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b896fcd5-adf8-4fa6-902c-0e2e52ac4220", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374219544800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2617038c-4b86-4699-9979-25c855fcb23b", "name": "entry : default@ProcessProfile cost memory -4.7335052490234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374221657900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bae74b9-e6ef-49c0-9410-ba14229c8c4c", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374217827400, "endTime": 153374221788600}, "additional": {"logType": "info", "children": [], "durationId": "7b592cbd-645c-4a59-ac6b-76647faabdaa"}}, {"head": {"id": "3c942bd1-1fdb-4ec2-878e-35d16c813820", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374226206600, "endTime": 153374232386900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2587aa2a-9039-41f8-a8cd-b2e17cde7001", "logId": "dceb8ce3-fb91-4689-b199-536c36e7dce6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2587aa2a-9039-41f8-a8cd-b2e17cde7001", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374223452900}, "additional": {"logType": "detail", "children": [], "durationId": "3c942bd1-1fdb-4ec2-878e-35d16c813820"}}, {"head": {"id": "8839f07b-b57d-4ee3-aa1a-82ed227a77e0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374224529800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "702f1ae3-a681-4c92-a8bb-864416df086a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374224637100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43feac4b-ee57-4571-93b3-3c05db595ba7", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374226217400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c760eab-6529-46b6-8f16-6d19d220639d", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374232092000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6572bbd4-d77f-412f-95a1-3b029ea6f37b", "name": "entry : default@ProcessRouterMap cost memory 0.232879638671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374232302700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dceb8ce3-fb91-4689-b199-536c36e7dce6", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374226206600, "endTime": 153374232386900}, "additional": {"logType": "info", "children": [], "durationId": "3c942bd1-1fdb-4ec2-878e-35d16c813820"}}, {"head": {"id": "88f7fe95-d56d-4722-bf95-f473c1faa72a", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374236363100, "endTime": 153374241656900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "070caf78-1e9a-486d-9290-5f03db13e1a6", "logId": "4098c736-9e41-40c7-b8c6-62ac2bf7e18b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "070caf78-1e9a-486d-9290-5f03db13e1a6", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374235187100}, "additional": {"logType": "detail", "children": [], "durationId": "88f7fe95-d56d-4722-bf95-f473c1faa72a"}}, {"head": {"id": "b32c3a54-b384-4117-815f-b8cdd74057fb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374236182900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33a065d7-f0e8-4b83-8b42-41474b05ac6f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374236279200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24068906-2ee3-4208-8250-22bc690ffeb7", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374236369800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65db0f29-a37a-40dc-8763-79bb3fb73dfe", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374236455400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aaeb507-5d26-4f6d-8a2e-c707a749af81", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374240190100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5889b685-ccee-4928-9ca1-ba6196b89e25", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374240350400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93600ba1-01d1-417b-b3f2-c52ce0d92512", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374240449100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2bd9f0a-8373-4f19-8260-bbe66d7ec399", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374240487000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "875ff7aa-a2a7-46f3-94f1-c84c79d34804", "name": "entry : default@ProcessStartupConfig cost memory 0.25811004638671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374241460800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f5f3260-dc77-4b79-a2be-6c2be9f8e978", "name": "runTaskFromQueue task cost before running: 317 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374241605500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4098c736-9e41-40c7-b8c6-62ac2bf7e18b", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374236363100, "endTime": 153374241656900, "totalTime": 5216100}, "additional": {"logType": "info", "children": [], "durationId": "88f7fe95-d56d-4722-bf95-f473c1faa72a"}}, {"head": {"id": "51865976-e50b-40e5-b892-9f7e6ff598a4", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374246107800, "endTime": 153374247776400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8781bc3d-71b1-43b9-959d-bdfcceeaba3a", "logId": "d29b2786-234e-4b9c-856d-3b244cceef5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8781bc3d-71b1-43b9-959d-bdfcceeaba3a", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374244263000}, "additional": {"logType": "detail", "children": [], "durationId": "51865976-e50b-40e5-b892-9f7e6ff598a4"}}, {"head": {"id": "e8f613f5-00bc-4cc8-be98-437a8e7bd9b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374245254400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e5cf4af-188b-4629-abed-8248a123b123", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374245364400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e542a6d-6de6-44e4-9045-33e06c746a0b", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374246143200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fcad431-8db5-4840-87fb-2a6ae1ee0abd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374246268300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4c78275-d9d6-425d-9e1d-50927056a396", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374246317400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da038674-cca4-45d5-91d1-c2f00d2881cc", "name": "entry : default@BuildNativeWithNinja cost memory 0.05817413330078125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374247480500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03965180-b170-42bf-8d91-2a4583cf1971", "name": "runTaskFromQueue task cost before running: 323 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374247708200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d29b2786-234e-4b9c-856d-3b244cceef5e", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374246107800, "endTime": 153374247776400, "totalTime": 1543500}, "additional": {"logType": "info", "children": [], "durationId": "51865976-e50b-40e5-b892-9f7e6ff598a4"}}, {"head": {"id": "9154c80a-f5e3-4eaa-acbf-dc80735a8b6d", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374253940400, "endTime": 153374259235200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "28765804-ba74-4e70-8992-d6420add9c06", "logId": "962ce85a-80a2-4a45-b1ee-766d5ced296c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28765804-ba74-4e70-8992-d6420add9c06", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374250367800}, "additional": {"logType": "detail", "children": [], "durationId": "9154c80a-f5e3-4eaa-acbf-dc80735a8b6d"}}, {"head": {"id": "1f89e44d-3299-4580-8fda-8058cb0c8e7e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374251362400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4536550-ba04-4563-bedf-77c276c4ab8d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374251472200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fc6805e-c5e0-423b-8263-8d7a753c197d", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374252501700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9dbf0c8-a034-450b-86f2-395549791fc0", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374255578800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2496752-4845-424f-aa7a-c88b88d6a80c", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374257624400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4914be30-7a27-4faa-8ef2-a230ec9cb73e", "name": "entry : default@ProcessResource cost memory 0.1613922119140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374257747200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "962ce85a-80a2-4a45-b1ee-766d5ced296c", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374253940400, "endTime": 153374259235200}, "additional": {"logType": "info", "children": [], "durationId": "9154c80a-f5e3-4eaa-acbf-dc80735a8b6d"}}, {"head": {"id": "1cd75765-c919-474e-ad83-27dc7b984932", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374266639400, "endTime": 153374288613600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ca99d350-034f-4fa1-8f76-ddc0d04ef2dc", "logId": "df53ca7d-44fe-4380-b9ab-d74e10d07b59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca99d350-034f-4fa1-8f76-ddc0d04ef2dc", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374262532700}, "additional": {"logType": "detail", "children": [], "durationId": "1cd75765-c919-474e-ad83-27dc7b984932"}}, {"head": {"id": "e95f2572-a4b5-4456-bec7-0319727c5b93", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374263432900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e686cdb3-6e29-4ffa-8dcc-493525a6e4f7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374263529000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4c2fcd6-ff67-4055-aa74-bff6de717c12", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374266651600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5923f995-487c-4375-acd6-c9f842a88e01", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374288396600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3ae12a1-8d74-48cf-ac2a-888691b53965", "name": "entry : default@GenerateLoaderJson cost memory 0.8775253295898438", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374288544300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df53ca7d-44fe-4380-b9ab-d74e10d07b59", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374266639400, "endTime": 153374288613600}, "additional": {"logType": "info", "children": [], "durationId": "1cd75765-c919-474e-ad83-27dc7b984932"}}, {"head": {"id": "f8e1ebb9-b251-45e0-8588-f10726a08c6d", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374299518900, "endTime": 153374304304900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "e0c0a993-ccde-4f59-bcda-0e58f7ded146", "logId": "1865e61a-4aa0-425a-b07e-262b839b8a60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0c0a993-ccde-4f59-bcda-0e58f7ded146", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374297193500}, "additional": {"logType": "detail", "children": [], "durationId": "f8e1ebb9-b251-45e0-8588-f10726a08c6d"}}, {"head": {"id": "f267cf9b-1355-4df6-aeb3-955ae08ccd49", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374298369500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff1eca48-3628-4b56-832a-25ecc125d3f4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374298559700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e68e2423-11d5-4efc-876d-99e11752b762", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374299530900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "079e16e1-d321-4e6a-bd97-20aaaccc63aa", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374304116100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b743f6b4-e955-480a-9805-bbba87e6e638", "name": "entry : default@ProcessLibs cost memory 0.14398956298828125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374304241500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1865e61a-4aa0-425a-b07e-262b839b8a60", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374299518900, "endTime": 153374304304900}, "additional": {"logType": "info", "children": [], "durationId": "f8e1ebb9-b251-45e0-8588-f10726a08c6d"}}, {"head": {"id": "cd7decf8-3c95-41ce-9934-4f128f899625", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374310778100, "endTime": 153374344627800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "9b493c76-8e21-4198-a4c0-db7d30002115", "logId": "c6d6403d-91e7-4573-814c-5c9b5659a449"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b493c76-8e21-4198-a4c0-db7d30002115", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374306175800}, "additional": {"logType": "detail", "children": [], "durationId": "cd7decf8-3c95-41ce-9934-4f128f899625"}}, {"head": {"id": "38af8202-1977-478c-9348-934a3dd10acc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374307103900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17bc76d4-cdb1-4a81-9076-bc9640481af7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374307192000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23f1c96b-96fd-444d-ac2c-80a0935a1178", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374308248500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b3d20d0-dbf3-42b4-bde5-34cb3e2796bf", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374310801300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d918e48a-007e-4dad-9f32-2d123184c013", "name": "Incremental task entry:default@CompileResource pre-execution cost: 33 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374344299700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6cba552-3702-4a5e-8b6d-a424d877138f", "name": "entry : default@CompileResource cost memory 1.4186019897460938", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374344509600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6d6403d-91e7-4573-814c-5c9b5659a449", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374310778100, "endTime": 153374344627800}, "additional": {"logType": "info", "children": [], "durationId": "cd7decf8-3c95-41ce-9934-4f128f899625"}}, {"head": {"id": "3e640e6a-14c1-43dd-9063-825bdef966a2", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374352044300, "endTime": 153374354236000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e60d05e3-d8b8-4cb4-ae24-121721e13738", "logId": "5a1e40bf-ed93-44aa-af28-0aad58025468"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e60d05e3-d8b8-4cb4-ae24-121721e13738", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374348292600}, "additional": {"logType": "detail", "children": [], "durationId": "3e640e6a-14c1-43dd-9063-825bdef966a2"}}, {"head": {"id": "2c11dac7-690b-484f-b19d-73b3cc402669", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374349440300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fb18c01-9bba-403b-9d3c-90917db584f6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374349570300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e99b76f5-b8f0-44a7-bc95-f10e3d06f631", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374352057100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6077259-bf2e-4c86-80d3-4a4fb553b383", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374352586400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f34e2e90-abf9-4c14-9216-2e86201cb037", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374354070400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbaf2e82-e631-4477-a7fe-4058d3bda4bb", "name": "entry : default@DoNativeStrip cost memory 0.08011627197265625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374354177600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a1e40bf-ed93-44aa-af28-0aad58025468", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374352044300, "endTime": 153374354236000}, "additional": {"logType": "info", "children": [], "durationId": "3e640e6a-14c1-43dd-9063-825bdef966a2"}}, {"head": {"id": "9b09528c-d636-4869-9b89-3317c4f838e9", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374360266200, "endTime": 153385109493900}, "additional": {"children": ["d9df0449-4713-42d9-96a1-2745f28fe91b", "1acf4caa-32b0-4752-8f72-4ad1a7f5cfca"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "ee94faa6-303b-471e-8c57-e05ce4061c67", "logId": "e72689f1-1a9c-4da5-9a2a-3473ff415cf1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee94faa6-303b-471e-8c57-e05ce4061c67", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374355560500}, "additional": {"logType": "detail", "children": [], "durationId": "9b09528c-d636-4869-9b89-3317c4f838e9"}}, {"head": {"id": "c5f94583-58d9-4a9a-936d-ffd9397138c9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374356443400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26b5088c-1de4-46e7-8752-f93f43e59abf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374356543500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e75429c-3c82-4fda-b339-983b1ee75c61", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374360277800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0350ada-3b06-420c-a913-c3a4dbb5fb60", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374360443400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ef1f730-97ce-40a7-85d5-7c6b4a06e5ed", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374391546300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95fa4e94-1241-4007-b65d-6dcff1e7029e", "name": "default@CompileArkTS work[10] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374393857500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9df0449-4713-42d9-96a1-2745f28fe91b", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 153377145607400, "endTime": 153385094386400}, "additional": {"children": ["e7bbf67e-cb83-4631-8fbe-88720109dfd7", "7d75ee1e-4f26-442d-8d0e-ce6314b887b0", "ce78440a-6e32-4582-b44f-6a3934d51168", "127aa328-2c3a-4f20-89ac-a46a953bf38b", "17fb52c0-8f29-4b74-a525-ab2420d9900d", "78b017ab-59e4-4e73-ac9e-30b01747c301", "1b8a78e6-407d-4465-8fbd-271cabcb92a4"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "9b09528c-d636-4869-9b89-3317c4f838e9", "logId": "dfaedb4e-aaa1-4407-a8e2-03f669917d60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a11d82b-7b91-4913-a514-7362e7a42522", "name": "default@CompileArkTS work[10] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374394886200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "618e4128-a47e-4397-af60-12d805b08cff", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374394968800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85078e97-a4a4-4d28-9792-5ec9ded3914d", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395005700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7359d034-3c1b-4929-bb9e-5fb27d9b12c0", "name": "A work dispatched to worker[17] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395031300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e763774-8d20-4065-9505-18f759e09d38", "name": "A work dispatched to worker[16] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395054600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5838f014-2cd3-4324-bfdf-757d3aeac77d", "name": "A work dispatched to worker[15] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395078500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cd32c90-b1d5-46c8-ba59-7e5b5df3b322", "name": "A work dispatched to worker[14] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395101900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "944fbb3f-fd8b-4b7c-b11d-3899068ba25b", "name": "A work dispatched to worker[13] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395126400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2dfb192-97d5-43bd-aa2c-69b0ec780be0", "name": "A work dispatched to worker[12] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395149600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd5d079f-ec00-442d-8656-ca90d84b8726", "name": "A work dispatched to worker[11] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395180100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ca2c757-de1a-4d37-9934-2e68131d632b", "name": "A work dispatched to worker[10] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395202800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e175e1ac-d962-4661-9e48-41f24e9d4c84", "name": "A work dispatched to worker[9] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395224600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "363502d7-9533-41c6-b524-731748f1479a", "name": "A work dispatched to worker[8] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395247000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5876afbd-6da7-4937-a620-c4d8b131f5fb", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395270600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3476fc2d-0676-4eed-a8ec-6795a49f47e0", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395293300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4c79b30-ca04-4739-a3cc-ef3fc3800048", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395316400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec52e0bf-d962-4cab-a96b-1f3131a77e58", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374395350500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48d81727-104f-4af3-8ad4-8d2ff6c7976b", "name": "default@CompileArkTS work[10] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374396051800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cd3f0c8-3c04-45e7-9e75-55f4d3143480", "name": "default@CompileArkTS work[10] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374396147900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d974c4b4-66f7-45ec-a5e8-04d8c48985b6", "name": "CopyResources startTime: 153374396192100", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374396194300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7d8c3ca-c2d0-4d0d-bf76-49111df873f1", "name": "default@CompileArkTS work[11] is submitted.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374396249800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1acf4caa-32b0-4752-8f72-4ad1a7f5cfca", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 153375489142500, "endTime": 153375503918800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "9b09528c-d636-4869-9b89-3317c4f838e9", "logId": "d9e05f2f-57d3-469c-8d84-db4433acb0c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee44b611-9030-4f91-ad50-b2410e1156d7", "name": "default@CompileArkTS work[11] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374398083200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32847ca4-a694-427f-9aa2-06f0bde99db2", "name": "A work dispatched to worker[19] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374398306900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee4e4583-fc97-40fd-bff8-4dab064fc5ec", "name": "Create  resident worker with id: 18.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374398497900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "914b9616-6a61-45fb-939f-e2c3d15114ee", "name": "default@CompileArkTS work[11] has been dispatched to worker[18].", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374399835200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54e276f9-a099-4c14-af57-5a735a71174d", "name": "default@CompileArkTS work[11] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374399954200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed36f8a4-0617-466d-a5aa-319f6682fe50", "name": "entry : default@CompileArkTS cost memory -5.397285461425781", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374400044700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75682ce0-39e3-4f24-ae6a-fcbb20609086", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374406286000, "endTime": 153374414990400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "94022128-be51-4db5-a3fc-e747b4506664", "logId": "21687d4c-d757-4a58-aaf4-f89d07279542"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94022128-be51-4db5-a3fc-e747b4506664", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374401554100}, "additional": {"logType": "detail", "children": [], "durationId": "75682ce0-39e3-4f24-ae6a-fcbb20609086"}}, {"head": {"id": "07014abd-fdb7-4c7e-8de5-d8cdb26ef02e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374402468300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39fea361-f90e-44f7-bc55-7dc651676772", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374402562400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82ec7c52-a2db-40dd-a940-1549a39060f2", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374406296900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "004fcf11-bf8d-41b7-8d10-cd1bfa4bd58e", "name": "entry : default@BuildJS cost memory 0.3447723388671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374414757600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85bf892d-6559-4e6a-b5b4-5e04f235eaa6", "name": "runTaskFromQueue task cost before running: 490 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374414923800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21687d4c-d757-4a58-aaf4-f89d07279542", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374406286000, "endTime": 153374414990400, "totalTime": 8605900}, "additional": {"logType": "info", "children": [], "durationId": "75682ce0-39e3-4f24-ae6a-fcbb20609086"}}, {"head": {"id": "77bc2fa9-5e2d-4128-a462-4c2c088c618b", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374420903300, "endTime": 153374424129500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "422040f8-019c-48b6-98fe-3976bd4325da", "logId": "3b6e8e47-780c-4aac-a7fc-5d3f870cadc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "422040f8-019c-48b6-98fe-3976bd4325da", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374416662400}, "additional": {"logType": "detail", "children": [], "durationId": "77bc2fa9-5e2d-4128-a462-4c2c088c618b"}}, {"head": {"id": "09938ff3-5465-484f-a6cc-b1ec5b72ae85", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374417902800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33536292-f3ce-4153-80d5-d1ab4491e2a2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374418061900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb5c7fef-f219-4fb7-bfd3-5353fa92345a", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374420914600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e0afc52-9976-4e76-b310-e7c03ed46387", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374421721000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b8fd5e7-ed2f-4623-a3d7-fee7f859dc3f", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374423943800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e12bbb18-593c-4315-b450-e46a4b4ddc8f", "name": "entry : default@CacheNativeLibs cost memory 0.09534454345703125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374424066500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b6e8e47-780c-4aac-a7fc-5d3f870cadc8", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374420903300, "endTime": 153374424129500}, "additional": {"logType": "info", "children": [], "durationId": "77bc2fa9-5e2d-4128-a462-4c2c088c618b"}}, {"head": {"id": "6faaff96-8989-40bf-8f08-4be262948b14", "name": "worker[18] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153375504334800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9ee04b3-2e38-41dd-b0e5-21f2e6116449", "name": "CopyResources is end, endTime: 153375504512400", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153375504518900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc240fdd-0de7-490d-b23d-7da50040d689", "name": "default@CompileArkTS work[11] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153375504635900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9e05f2f-57d3-469c-8d84-db4433acb0c8", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker18", "startTime": 153375489142500, "endTime": 153375503918800}, "additional": {"logType": "info", "children": [], "durationId": "1acf4caa-32b0-4752-8f72-4ad1a7f5cfca", "parent": "e72689f1-1a9c-4da5-9a2a-3473ff415cf1"}}, {"head": {"id": "4b9ee98e-d40f-4bf5-af3e-f2da658f0519", "name": "A work dispatched to worker[18] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153375504733200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a481e441-47bd-496d-8e20-7225ff76e8cd", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385095047200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7bbf67e-cb83-4631-8fbe-88720109dfd7", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 153377146723500, "endTime": 153378159978600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d9df0449-4713-42d9-96a1-2745f28fe91b", "logId": "8c354b86-e7d2-4b9b-b859-e038404a9093"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c354b86-e7d2-4b9b-b859-e038404a9093", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153377146723500, "endTime": 153378159978600}, "additional": {"logType": "info", "children": [], "durationId": "e7bbf67e-cb83-4631-8fbe-88720109dfd7", "parent": "dfaedb4e-aaa1-4407-a8e2-03f669917d60"}}, {"head": {"id": "7d75ee1e-4f26-442d-8d0e-ce6314b887b0", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 153378161481500, "endTime": 153378208842500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d9df0449-4713-42d9-96a1-2745f28fe91b", "logId": "eec6b390-75fc-4826-98f4-2eeb9362cd97"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eec6b390-75fc-4826-98f4-2eeb9362cd97", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153378161481500, "endTime": 153378208842500}, "additional": {"logType": "info", "children": [], "durationId": "7d75ee1e-4f26-442d-8d0e-ce6314b887b0", "parent": "dfaedb4e-aaa1-4407-a8e2-03f669917d60"}}, {"head": {"id": "ce78440a-6e32-4582-b44f-6a3934d51168", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 153378208949200, "endTime": 153378209049900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d9df0449-4713-42d9-96a1-2745f28fe91b", "logId": "87b4c155-c427-4958-9b62-5b337fa5e2e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87b4c155-c427-4958-9b62-5b337fa5e2e9", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153378208949200, "endTime": 153378209049900}, "additional": {"logType": "info", "children": [], "durationId": "ce78440a-6e32-4582-b44f-6a3934d51168", "parent": "dfaedb4e-aaa1-4407-a8e2-03f669917d60"}}, {"head": {"id": "127aa328-2c3a-4f20-89ac-a46a953bf38b", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 153378209103400, "endTime": 153384829089400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d9df0449-4713-42d9-96a1-2745f28fe91b", "logId": "6e88c0bd-85f3-4b08-b75e-352c6fd641f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e88c0bd-85f3-4b08-b75e-352c6fd641f1", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153378209103400, "endTime": 153384829089400}, "additional": {"logType": "info", "children": [], "durationId": "127aa328-2c3a-4f20-89ac-a46a953bf38b", "parent": "dfaedb4e-aaa1-4407-a8e2-03f669917d60"}}, {"head": {"id": "17fb52c0-8f29-4b74-a525-ab2420d9900d", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 153384829526700, "endTime": 153384845879000}, "additional": {"children": ["7426f1f6-d654-4bc7-bf81-b0434daa57ce", "5807495c-57a7-4af5-8f4b-7ac1bf15e826", "1fb646e0-40a6-440a-9aa7-c0bd3f7c7916"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d9df0449-4713-42d9-96a1-2745f28fe91b", "logId": "80ef73ee-3395-4813-ab0e-d5df64d6a819"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80ef73ee-3395-4813-ab0e-d5df64d6a819", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153384829526700, "endTime": 153384845879000}, "additional": {"logType": "info", "children": ["33e844c9-7c55-4f07-99c3-0062066aee52", "3d69e6e5-f5c5-45f5-af32-1584c842aca0", "392724ff-29b6-466d-890e-429dc0765ab8"], "durationId": "17fb52c0-8f29-4b74-a525-ab2420d9900d", "parent": "dfaedb4e-aaa1-4407-a8e2-03f669917d60"}}, {"head": {"id": "7426f1f6-d654-4bc7-bf81-b0434daa57ce", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 153384829713600, "endTime": 153384829737300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "17fb52c0-8f29-4b74-a525-ab2420d9900d", "logId": "33e844c9-7c55-4f07-99c3-0062066aee52"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33e844c9-7c55-4f07-99c3-0062066aee52", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153384829713600, "endTime": 153384829737300}, "additional": {"logType": "info", "children": [], "durationId": "7426f1f6-d654-4bc7-bf81-b0434daa57ce", "parent": "80ef73ee-3395-4813-ab0e-d5df64d6a819"}}, {"head": {"id": "5807495c-57a7-4af5-8f4b-7ac1bf15e826", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 153384829748000, "endTime": 153384839424000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "17fb52c0-8f29-4b74-a525-ab2420d9900d", "logId": "3d69e6e5-f5c5-45f5-af32-1584c842aca0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d69e6e5-f5c5-45f5-af32-1584c842aca0", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153384829748000, "endTime": 153384839424000}, "additional": {"logType": "info", "children": [], "durationId": "5807495c-57a7-4af5-8f4b-7ac1bf15e826", "parent": "80ef73ee-3395-4813-ab0e-d5df64d6a819"}}, {"head": {"id": "1fb646e0-40a6-440a-9aa7-c0bd3f7c7916", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 153384839429700, "endTime": 153384845835800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "17fb52c0-8f29-4b74-a525-ab2420d9900d", "logId": "392724ff-29b6-466d-890e-429dc0765ab8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "392724ff-29b6-466d-890e-429dc0765ab8", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153384839429700, "endTime": 153384845835800}, "additional": {"logType": "info", "children": [], "durationId": "1fb646e0-40a6-440a-9aa7-c0bd3f7c7916", "parent": "80ef73ee-3395-4813-ab0e-d5df64d6a819"}}, {"head": {"id": "78b017ab-59e4-4e73-ac9e-30b01747c301", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 153384845896800, "endTime": 153385088679900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d9df0449-4713-42d9-96a1-2745f28fe91b", "logId": "e90a59ce-892b-4d1e-8c42-038c712ecbe8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e90a59ce-892b-4d1e-8c42-038c712ecbe8", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153384845896800, "endTime": 153385088679900}, "additional": {"logType": "info", "children": [], "durationId": "78b017ab-59e4-4e73-ac9e-30b01747c301", "parent": "dfaedb4e-aaa1-4407-a8e2-03f669917d60"}}, {"head": {"id": "1b8a78e6-407d-4465-8fbd-271cabcb92a4", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 153375385048100, "endTime": 153377143874100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d9df0449-4713-42d9-96a1-2745f28fe91b", "logId": "b076c978-56f5-4ae6-8872-750000b8d44d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b076c978-56f5-4ae6-8872-750000b8d44d", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153375385048100, "endTime": 153377143874100}, "additional": {"logType": "info", "children": [], "durationId": "1b8a78e6-407d-4465-8fbd-271cabcb92a4", "parent": "dfaedb4e-aaa1-4407-a8e2-03f669917d60"}}, {"head": {"id": "d8b315ee-5830-4f6a-9640-d6649f3eb415", "name": "default@CompileArkTS work[10] done.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385108973000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfaedb4e-aaa1-4407-a8e2-03f669917d60", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Worker4", "startTime": 153377145607400, "endTime": 153385094386400}, "additional": {"logType": "info", "children": ["8c354b86-e7d2-4b9b-b859-e038404a9093", "eec6b390-75fc-4826-98f4-2eeb9362cd97", "87b4c155-c427-4958-9b62-5b337fa5e2e9", "6e88c0bd-85f3-4b08-b75e-352c6fd641f1", "80ef73ee-3395-4813-ab0e-d5df64d6a819", "e90a59ce-892b-4d1e-8c42-038c712ecbe8", "b076c978-56f5-4ae6-8872-750000b8d44d"], "durationId": "d9df0449-4713-42d9-96a1-2745f28fe91b", "parent": "e72689f1-1a9c-4da5-9a2a-3473ff415cf1"}}, {"head": {"id": "58b96c19-a083-4b2b-b54c-f9d4699ae16f", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385109253000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e72689f1-1a9c-4da5-9a2a-3473ff415cf1", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153374360266200, "endTime": 153385109493900, "totalTime": 8003382400}, "additional": {"logType": "info", "children": ["dfaedb4e-aaa1-4407-a8e2-03f669917d60", "d9e05f2f-57d3-469c-8d84-db4433acb0c8"], "durationId": "9b09528c-d636-4869-9b89-3317c4f838e9"}}, {"head": {"id": "5b0b76c5-04e4-481b-a507-4ec22072c9f1", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385120457200, "endTime": 153385121800000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "afe010a7-84e3-49b2-afce-df91b345c878", "logId": "7ad008bd-7308-4517-a36b-92b6f8c89a1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afe010a7-84e3-49b2-afce-df91b345c878", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385118471400}, "additional": {"logType": "detail", "children": [], "durationId": "5b0b76c5-04e4-481b-a507-4ec22072c9f1"}}, {"head": {"id": "56ac31ee-dc34-4551-a119-c38247a94bbf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385119361700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeba49f3-399d-49ba-9136-d90d05315f71", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385119460300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b88013a-4a17-4881-8800-294a5a0367f2", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385120467600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fce7629-5288-4e6b-80a1-53fb97c59437", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385120793500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "194f0ca9-941c-4101-8f54-1c97a4f1c2b9", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385121633800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65601928-ddfe-4c41-9429-6fcf55dfe49c", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07582855224609375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385121740000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ad008bd-7308-4517-a36b-92b6f8c89a1d", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385120457200, "endTime": 153385121800000}, "additional": {"logType": "info", "children": [], "durationId": "5b0b76c5-04e4-481b-a507-4ec22072c9f1"}}, {"head": {"id": "69bfaa62-a312-49be-ac75-a43b548ebae7", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385131700100, "endTime": 153385293424400}, "additional": {"children": ["f8a71be7-1157-4913-81e0-2139429751ca", "67870708-911a-47d2-be51-9c9e5c0e579d"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "8ac6e0a2-d6a0-4d94-8c78-b7d289242d3b", "logId": "d031466b-01f6-41c2-b879-076b22c78307"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ac6e0a2-d6a0-4d94-8c78-b7d289242d3b", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385123929700}, "additional": {"logType": "detail", "children": [], "durationId": "69bfaa62-a312-49be-ac75-a43b548ebae7"}}, {"head": {"id": "5410d980-0a75-4bec-9c33-2aaf4219e286", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385124805800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f338cf4-f6ac-416d-a124-5b30d288dbb9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385124899100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "834a5e30-65cd-4995-9a81-70daf8f71a7e", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385131716700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ae79af7-784c-4d0f-85ef-7fcf60995173", "name": "entry:default@PackageHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385147153900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02b4dffb-145d-45f8-8fca-9b22eac610bc", "name": "Incremental task entry:default@PackageHap pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385147357100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5440a64d-8b2d-48d2-a2bc-bbbd91005fb7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385147443400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c38d9053-65f6-460a-bd28-d2cd02f8e096", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385147482000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8a71be7-1157-4913-81e0-2139429751ca", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385149029100, "endTime": 153385151258600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "69bfaa62-a312-49be-ac75-a43b548ebae7", "logId": "9d344b51-942e-472d-8690-85fec094f02e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7fcd2004-083a-450c-a601-b17eef867b26", "name": "Use tool [C:\\Users\\<USER>\\AppData\\Local\\Huawei\\Sdk\\OpenHarmony\\Sdk\\18\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Huawei\\\\Sdk\\\\OpenHarmony\\\\Sdk\\\\18\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--rpcid-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\syscap\\\\default\\\\rpcid.sc',\n  '--ets-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets',\n  '--pkg-context-path',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader\\\\default\\\\pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385151095900}, "additional": {"logType": "debug", "children": [], "durationId": "69bfaa62-a312-49be-ac75-a43b548ebae7"}}, {"head": {"id": "9d344b51-942e-472d-8690-85fec094f02e", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385149029100, "endTime": 153385151258600}, "additional": {"logType": "info", "children": [], "durationId": "f8a71be7-1157-4913-81e0-2139429751ca", "parent": "d031466b-01f6-41c2-b879-076b22c78307"}}, {"head": {"id": "67870708-911a-47d2-be51-9c9e5c0e579d", "name": "submit HAP packaging task to java daemon server", "description": "Pack HAP in java daemon", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385151845300, "endTime": 153385288582300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "69bfaa62-a312-49be-ac75-a43b548ebae7", "logId": "3fea2b4d-eea3-4100-bbef-3d39b9e58253"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91352381-9652-405c-a94a-448c0a3915e4", "name": "java daemon socket received message:{\"code\":0,\"message\":\"pack success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385287837300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fea2b4d-eea3-4100-bbef-3d39b9e58253", "name": "submit HAP packaging task to java daemon server", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385151845300, "endTime": 153385288576700}, "additional": {"logType": "info", "children": [], "durationId": "67870708-911a-47d2-be51-9c9e5c0e579d", "parent": "d031466b-01f6-41c2-b879-076b22c78307"}}, {"head": {"id": "518b582b-d518-4a77-8d6c-76350a65abec", "name": "entry : default@PackageHap cost memory 1.5763015747070312", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385293234300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6541faa-e64e-4f41-aad0-69457d5c1414", "name": "runTaskFromQueue task cost before running: 11 s 369 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385293371400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d031466b-01f6-41c2-b879-076b22c78307", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385131700100, "endTime": 153385293424400, "totalTime": 161647100}, "additional": {"logType": "info", "children": ["9d344b51-942e-472d-8690-85fec094f02e", "3fea2b4d-eea3-4100-bbef-3d39b9e58253"], "durationId": "69bfaa62-a312-49be-ac75-a43b548ebae7"}}, {"head": {"id": "0c1d2fe9-1833-428d-9077-c01053fb7780", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385300056500, "endTime": 153385585442400}, "additional": {"children": ["c1770675-f2af-4700-b02c-4f0dbd52d990", "f8ecd60c-9011-4845-8676-f5cbec7859cf"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "c4555845-1420-43cf-a57f-91451f83ad87", "logId": "ba7655bd-2bb4-494d-9b59-b3e56eba1792"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4555845-1420-43cf-a57f-91451f83ad87", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385296470000}, "additional": {"logType": "detail", "children": [], "durationId": "0c1d2fe9-1833-428d-9077-c01053fb7780"}}, {"head": {"id": "25f6f94e-5bb0-4541-aaa6-db4ab1af162f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385297535700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8e91e2f-a743-4c1b-8c92-39400c4a40ca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385297650100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb38fcaa-a4fa-4b8a-abef-0ee697138231", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385300067500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8606696e-3218-4338-ab7e-9d89a5bcc97b", "name": "entry:default@SignHap is not up-to-date, since the input file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385301917000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d3b24ac-3006-4c7e-a9c1-e1d77cc3d8ca", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385302023700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eeca535-5d46-4e38-8c3c-6b7b8c0f8b96", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385302083700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4fc3179-7791-40eb-ab25-22e710f379a7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385302121800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1770675-f2af-4700-b02c-4f0dbd52d990", "name": "generate hap signing command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385303549900, "endTime": 153385387110500}, "additional": {"children": ["6424b331-8712-4574-981a-b724d3a8bd1b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c1d2fe9-1833-428d-9077-c01053fb7780", "logId": "258ead34-c5c5-4497-be58-3d96163bd08c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6424b331-8712-4574-981a-b724d3a8bd1b", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385320296100, "endTime": 153385385941400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1770675-f2af-4700-b02c-4f0dbd52d990", "logId": "b87557df-8f0e-41cb-80e7-492164e955f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1361ab2f-1d8c-4b82-a3b3-cdb61d60fbe1", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385323208300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "726b96ee-3a5d-4ac7-860b-7f0d0a07b4ea", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385385517900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b87557df-8f0e-41cb-80e7-492164e955f6", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385320296100, "endTime": 153385385941400}, "additional": {"logType": "info", "children": [], "durationId": "6424b331-8712-4574-981a-b724d3a8bd1b", "parent": "258ead34-c5c5-4497-be58-3d96163bd08c"}}, {"head": {"id": "258ead34-c5c5-4497-be58-3d96163bd08c", "name": "generate hap signing command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385303549900, "endTime": 153385387110500}, "additional": {"logType": "info", "children": ["b87557df-8f0e-41cb-80e7-492164e955f6"], "durationId": "c1770675-f2af-4700-b02c-4f0dbd52d990", "parent": "ba7655bd-2bb4-494d-9b59-b3e56eba1792"}}, {"head": {"id": "f8ecd60c-9011-4845-8676-f5cbec7859cf", "name": "execute hap signing command", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385387758900, "endTime": 153385584950200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c1d2fe9-1833-428d-9077-c01053fb7780", "logId": "6592c4f0-94d8-428e-b23f-304de26ede12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5dd48029-27c7-4462-9b1a-3d7260c745b8", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385389572800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9db6702c-02be-4324-8e26-541dca51f1ec", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385584512000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6592c4f0-94d8-428e-b23f-304de26ede12", "name": "execute hap signing command", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385387758900, "endTime": 153385584950200}, "additional": {"logType": "info", "children": [], "durationId": "f8ecd60c-9011-4845-8676-f5cbec7859cf", "parent": "ba7655bd-2bb4-494d-9b59-b3e56eba1792"}}, {"head": {"id": "be722aaa-202a-417e-93e9-20a27c7af500", "name": "entry : default@SignHap cost memory 0.9798202514648438", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385585233900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78330bc7-df06-4653-95bb-c175c6ec1a03", "name": "runTaskFromQueue task cost before running: 11 s 661 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385585378300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba7655bd-2bb4-494d-9b59-b3e56eba1792", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385300056500, "endTime": 153385585442400, "totalTime": 285288100}, "additional": {"logType": "info", "children": ["258ead34-c5c5-4497-be58-3d96163bd08c", "6592c4f0-94d8-428e-b23f-304de26ede12"], "durationId": "0c1d2fe9-1833-428d-9077-c01053fb7780"}}, {"head": {"id": "95b43ef9-c7b4-4f5e-9f15-61ddd554bc3c", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385589229100, "endTime": 153385596076000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9c7df402-7c10-4906-9bbe-4e485e5e78dd", "logId": "8c1a2817-d5de-4bce-9803-71866616ccb4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c7df402-7c10-4906-9bbe-4e485e5e78dd", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385587442200}, "additional": {"logType": "detail", "children": [], "durationId": "95b43ef9-c7b4-4f5e-9f15-61ddd554bc3c"}}, {"head": {"id": "0dddae90-8e61-42e7-83e2-8293dbc78c44", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385588397400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "017b976f-b0d2-4725-89ee-ccff5626ca22", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385588494500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c60adac-7b2c-40fb-a01d-10a7fcef6d61", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385589237800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7724b8c8-93bc-41b9-9981-38f084788dbf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385595576100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49dff466-55da-4648-9215-7fd3e3f50a05", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385595703100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07791a1c-9aae-4cbc-a123-98ff7a5f11cb", "name": "entry : default@CollectDebugSymbol cost memory 0.24394989013671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385595932000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43c361bc-5719-4052-b8ce-b44b88f0294a", "name": "runTaskFromQueue task cost before running: 11 s 672 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385596033600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c1a2817-d5de-4bce-9803-71866616ccb4", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385589229100, "endTime": 153385596076000, "totalTime": 6779000}, "additional": {"logType": "info", "children": [], "durationId": "95b43ef9-c7b4-4f5e-9f15-61ddd554bc3c"}}, {"head": {"id": "90063d53-2025-471a-b1b8-d08b9c877f51", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385598020400, "endTime": 153385598456700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "f51c49ce-3d5f-4e60-9c3a-f3688fe40bf7", "logId": "89c47271-dc46-4d09-90fc-b0f59988d0b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f51c49ce-3d5f-4e60-9c3a-f3688fe40bf7", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385597963300}, "additional": {"logType": "detail", "children": [], "durationId": "90063d53-2025-471a-b1b8-d08b9c877f51"}}, {"head": {"id": "6d9f25b9-4def-4716-95c8-5233d46c0ea3", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385598027200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c08a6dcf-da99-41c3-a3bc-3a5629461354", "name": "entry : assembleHap cost memory 0.011749267578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385598316400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b14abb9-e0c2-4b01-85ff-42c4bd592ab8", "name": "runTaskFromQueue task cost before running: 11 s 674 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385598414100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89c47271-dc46-4d09-90fc-b0f59988d0b7", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385598020400, "endTime": 153385598456700, "totalTime": 371100}, "additional": {"logType": "info", "children": [], "durationId": "90063d53-2025-471a-b1b8-d08b9c877f51"}}, {"head": {"id": "edfd3c92-b2e9-4f48-a58d-0e98f20ceee4", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385611349900, "endTime": 153385611380500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a536a17e-9c35-43b9-a5da-1fc97e44b421", "logId": "97479e3a-4929-480c-8317-ca3687e30cba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97479e3a-4929-480c-8317-ca3687e30cba", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385611349900, "endTime": 153385611380500}, "additional": {"logType": "info", "children": [], "durationId": "edfd3c92-b2e9-4f48-a58d-0e98f20ceee4"}}, {"head": {"id": "2bdf3e6c-479d-42a6-99d4-874f57bd7fbd", "name": "BUILD SUCCESSFUL in 11 s 687 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385611504300}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "2631da2e-0d8e-4d05-a2d0-6a6f20bc24f4", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153373924915400, "endTime": 153385611868600}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 33, "second": 35}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "b37d4088-9283-4a88-bdfc-38e9332127ea", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385611898400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aad3c47-5bc9-4af6-8e1e-e546b6df4d19", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385612003200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b6839c3-50f3-4e37-9f94-f229dc4a4d7d", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385612381500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3e04a6e-8e60-420f-8982-6ce388a6a123", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385612451600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6ce2317-9a9f-4bce-a211-1c05706b6c0d", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385612614800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fbf95f5-e4f6-469f-86f7-be1e0d80e122", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385612897800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1ab443e-bbe3-428f-a5d7-14060de6b54b", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385613002200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e57adedf-0f37-4af0-b32a-03d97531cf0b", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385613915500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96bb6a04-564c-4cf8-aa67-aae599c02bac", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385614255400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "434765db-692c-46b8-a2f9-1da94073dda9", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385614362000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6b7540f-8c48-4440-81a8-ec02bc523b47", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385614419400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e30c792-5973-48a8-9757-f369b462df44", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385614461700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a94d930-fe7a-4888-8598-511301ce642f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385614514800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6af2c755-e5ed-4aa4-87e9-4ebc017605b8", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385616345600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0f344d7-c604-419c-9501-45a6b1d3def6", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385616732300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b78cfeac-4fc5-47ea-b3b9-c33c7078f2ed", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385617046600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2098333-0720-4293-8d8b-ccd3d12b424d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385617128600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71eb2001-ce30-42e6-bab5-215a4c97db55", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385617181300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06b4d48a-de37-4e1d-a714-b89a640b52df", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385617233600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "835ebc46-157c-4142-a1c6-d9b4c4aeb98c", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385617269800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3413b63-d577-4850-b3ee-0575079d5dcd", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385617303900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f568349-092d-4208-95b7-65c3d8dc5434", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385621367200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0afea9b7-8d33-47ba-8df9-ea13ea4619ed", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385622493700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c07614ef-98f7-4f65-8f0f-2cbac6946578", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385623182200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0607d3ea-c35e-41f3-b252-61a2b3399ef4", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385623581300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4acf1f13-e595-4c41-b5ad-933a80fba076", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385623927300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7426abd2-3934-4ccd-81ab-d5bb045c7799", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385625071900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7c25916-eab0-47a1-b10b-231cdf42e607", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385633703900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d9d2814-c95b-4ecf-8de2-0c9aba066b11", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385634030500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "624ac2a2-8223-4775-8f35-bd0e3d3ce515", "name": "Update task entry:default@CompileArkTS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385634470400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8d25474-b8eb-45b0-af0d-e4397e05e2f4", "name": "Update task entry:default@CompileArkTS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385635520500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4993279b-a291-4296-8aa7-1f3cd97356b3", "name": "Incremental task entry:default@CompileArkTS post-execution cost:19 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385636296000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afde782a-831f-45b2-a57b-cba3cef49b4e", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385638593700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b21cd4af-ed20-4874-835f-867d5eeb7253", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385639258600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c259c550-2120-4f33-8987-72b6d0b95068", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385639689400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15b12f46-5b4b-4867-99c8-7990a3f8ff13", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385639952400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d7155b5-29ce-470a-8cd6-f849ba9f597b", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385640181600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c0c765d-221c-4e56-b98d-02482852d438", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385640947000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d9484b1-53bb-417e-8c64-0ff2269ff151", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385641816900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f971d82d-e36c-45b0-94d5-98fd08c7d148", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385642110500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80371389-4ba4-4107-8b60-b42256b701c4", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385642181500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44ea9a6e-fc2a-45e1-aa74-6ded1d30fa13", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385642224400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92975228-ba1d-4bd5-a8f8-bb6f92dad7a3", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385643864300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d813cc08-cc99-497b-904e-0f0431081f2a", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385644423400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef97f5e9-21a2-4bd6-816e-924f0d2452c1", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385644745400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29b4cc29-ed4a-4fb7-8cf3-3e1f73bf6b8a", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385653915200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13444acd-a809-40c6-a8a4-36633b6ccccd", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385654267600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b24f48ba-5e76-4622-b23f-22e8d95dad81", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385654518700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61ca39df-1de2-4003-bdbb-15a9ca4a69b9", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385654768300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa66f599-6661-4f0e-9749-de3a9aefdb12", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385654827900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "088aa9fb-ecc2-4d9f-b87c-3bd413025b0d", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385655032300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47690a0b-f58d-42f7-baa6-c1f7a47596f4", "name": "Update task entry:default@PackageHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385655262200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e89d3668-e9f0-484b-9da6-af75c96bf974", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385656217900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b9d3fd7-d25a-4bea-b92a-d3746891cb6f", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385656475400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b65924f7-5872-47c1-966a-1f5eb1d9e037", "name": "Update task entry:default@PackageHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385656711200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12eca2f0-4d4b-4186-87d0-e08aa475d360", "name": "Incremental task entry:default@PackageHap post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385656979800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24fcfe92-17ad-4e7e-be96-1c0a3ca0d595", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385657239100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7af338c5-6777-446c-aff8-311947ab3ec0", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385657477900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3455aad7-437d-4e74-a5eb-4943cee691ea", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\.ohos\\config\\default_NexusHub_ginAEvfmrRN3oKbJWtOYCN4hBWpfKa-kgueLA_zHxXQ=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385657686300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f76161d9-1891-4f2d-a643-92dccd7805ef", "name": "Update task entry:default@SignHap input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385657894600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26b6a33b-e2e6-41ce-958c-bbfe9a7e29ec", "name": "Update task entry:default@SignHap output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385657975300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5b20ccd-ae40-4271-af6b-7c86ca1039e4", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385658235600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e8e4b06-6e09-40d9-8932-80dd4a38c5c5", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385660781700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "874061fc-655d-496d-bc18-10de1ab5a8fe", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385661134800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5de90f4a-0541-4e02-8dfd-640901f58455", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385661692900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f95bc22f-d2b4-4728-bcf0-95f09620ac9c", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153385661952800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}