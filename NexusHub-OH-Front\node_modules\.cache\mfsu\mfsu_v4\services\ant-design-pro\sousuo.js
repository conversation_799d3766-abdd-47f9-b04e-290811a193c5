"use strict";
import { request } from "@umijs/max";
export async function getSearchApps(params, options) {
  return request("/search/apps", {
    method: "GET",
    params: {
      // page has a default value: 1
      page: "1",
      // page_size has a default value: 20
      page_size: "20",
      ...params
    },
    ...options || {}
  });
}
export async function postSearchInitialize(options) {
  return request("/search/initialize", {
    method: "POST",
    ...options || {}
  });
}
export async function postSearchInitializeAll(options) {
  return request("/search/initialize-all", {
    method: "POST",
    ...options || {}
  });
}
export async function getSearchReviews(params, options) {
  return request("/search/reviews", {
    method: "GET",
    params: {
      // page has a default value: 1
      page: "1",
      // page_size has a default value: 20
      page_size: "20",
      ...params
    },
    ...options || {}
  });
}
export async function getSearchSuggestions(params, options) {
  return request("/search/suggestions", {
    method: "GET",
    params: {
      // limit has a default value: 10
      limit: "10",
      ...params
    },
    ...options || {}
  });
}
export async function postSearchSync(options) {
  return request("/search/sync", {
    method: "POST",
    ...options || {}
  });
}
export async function getSearchTags(params, options) {
  return request("/search/tags", {
    method: "GET",
    params: {
      // page has a default value: 1
      page: "1",
      // page_size has a default value: 20
      page_size: "20",
      ...params
    },
    ...options || {}
  });
}
export async function getSearchTagsStats(options) {
  return request("/search/tags/stats", {
    method: "GET",
    ...options || {}
  });
}
export async function getSearchTagsSuggest(params, options) {
  return request("/search/tags/suggest", {
    method: "GET",
    params: {
      // limit has a default value: 10
      limit: "10",
      ...params
    },
    ...options || {}
  });
}
export async function getSearchUsers(params, options) {
  return request("/admin/search/users", {
    method: "GET",
    params: {
      // page has a default value: 1
      page: "1",
      // page_size has a default value: 20
      page_size: "20",
      ...params
    },
    ...options || {}
  });
}
