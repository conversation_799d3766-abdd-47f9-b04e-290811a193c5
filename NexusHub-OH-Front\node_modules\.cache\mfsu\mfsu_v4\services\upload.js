"use strict";
import { request } from "@umijs/max";
export async function getUploadToken(params) {
  return request("/upload/token", {
    method: "GET",
    params
  });
}
export async function uploadToSignedUrl(signedUrl, file) {
  try {
    const response = await fetch(signedUrl, {
      method: "PUT",
      headers: {
        "Content-Type": file.type
      },
      body: file
    });
    if (!response.ok) {
      throw new Error(`\u4E0A\u4F20\u5931\u8D25\uFF0C\u72B6\u6001\u7801: ${response.status}`);
    }
    const fileUrl = signedUrl.split("?")[0];
    return fileUrl;
  } catch (error) {
    console.error("\u6587\u4EF6\u4E0A\u4F20\u9519\u8BEF:", error);
    throw error;
  }
}
export async function uploadFile(fileType, file) {
  try {
    const response = await getUploadToken({
      file_type: fileType,
      file_name: file.name
    });
    if (response.code !== 200 || !response.data?.file_url) {
      throw new Error("\u83B7\u53D6\u4E0A\u4F20\u51ED\u8BC1\u5931\u8D25");
    }
    const fileUrl = await uploadToSignedUrl(response.data.file_url, file);
    return fileUrl;
  } catch (error) {
    console.error("\u6587\u4EF6\u4E0A\u4F20\u6D41\u7A0B\u9519\u8BEF:", error);
    throw error;
  }
}
