"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import useStyles from "./index.style";
const Field = ({ label, value, ...rest }) => {
  const { styles } = useStyles();
  return /* @__PURE__ */ jsxs("div", { className: styles.field, ...rest, children: [
    /* @__PURE__ */ jsx("span", { className: styles.label, children: label }),
    /* @__PURE__ */ jsx("span", { className: styles.number, children: value })
  ] });
};
export default Field;
