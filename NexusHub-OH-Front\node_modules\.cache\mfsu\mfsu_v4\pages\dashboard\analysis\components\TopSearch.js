"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { InfoCircleOutlined } from "@ant-design/icons";
import { Area } from "@ant-design/plots";
import { Card, Col, Row, Table, Tooltip, Avatar } from "antd";
import { AppstoreOutlined } from "@ant-design/icons";
import numeral from "numeral";
import useStyles from "../style.style";
import NumberInfo from "./NumberInfo";
const TopSearch = ({
  loading,
  visitData2,
  searchData,
  dropdownGroup
}) => {
  const { styles } = useStyles();
  const columns = [
    {
      title: "\u6392\u540D",
      key: "index",
      render: (_, __, index) => index + 1
    },
    {
      title: "\u5E94\u7528",
      key: "app",
      render: (text, record) => /* @__PURE__ */ jsxs("div", { style: { display: "flex", alignItems: "center" }, children: [
        /* @__PURE__ */ jsx(
          Avatar,
          {
            src: record.app_icon,
            size: "small",
            style: { marginRight: 8 },
            icon: /* @__PURE__ */ jsx(AppstoreOutlined, {}),
            onError: () => {
              console.warn("\u5E94\u7528\u56FE\u6807\u52A0\u8F7D\u5931\u8D25:", record.app_icon);
              return false;
            }
          }
        ),
        /* @__PURE__ */ jsx("a", { href: "/", children: record.app_name })
      ] })
    },
    {
      title: "\u5F00\u53D1\u8005",
      dataIndex: "developer_name",
      key: "developer_name"
    },
    {
      title: "\u5206\u7C7B",
      dataIndex: "category_name",
      key: "category_name"
    },
    {
      title: "\u4E0B\u8F7D\u91CF",
      dataIndex: "download_count",
      key: "download_count",
      sorter: (a, b) => a.download_count - b.download_count,
      render: (text) => numeral(text).format("0,0")
    },
    {
      title: "\u8BC4\u5206",
      dataIndex: "rating",
      key: "rating",
      sorter: (a, b) => a.rating - b.rating,
      render: (text) => text ? text.toFixed(1) : "-"
    }
  ];
  return /* @__PURE__ */ jsxs(
    Card,
    {
      loading,
      bordered: false,
      title: "\u70ED\u95E8\u5E94\u7528",
      extra: dropdownGroup,
      style: {
        height: "100%"
      },
      children: [
        /* @__PURE__ */ jsxs(Row, { gutter: 68, children: [
          /* @__PURE__ */ jsxs(
            Col,
            {
              sm: 12,
              xs: 24,
              style: {
                marginBottom: 24
              },
              children: [
                /* @__PURE__ */ jsx(
                  NumberInfo,
                  {
                    subTitle: /* @__PURE__ */ jsxs("span", { children: [
                      "\u603B\u4E0B\u8F7D\u91CF",
                      /* @__PURE__ */ jsx(Tooltip, { title: "\u6240\u6709\u70ED\u95E8\u5E94\u7528\u7684\u603B\u4E0B\u8F7D\u91CF", children: /* @__PURE__ */ jsx(
                        InfoCircleOutlined,
                        {
                          style: {
                            marginLeft: 8
                          }
                        }
                      ) })
                    ] }),
                    gap: 8,
                    total: numeral(searchData.reduce((sum, item) => sum + (item.download_count || 0), 0)).format("0,0"),
                    status: "up",
                    subTotal: 17.1
                  }
                ),
                /* @__PURE__ */ jsx(
                  Area,
                  {
                    xField: "x",
                    yField: "y",
                    shapeField: "smooth",
                    height: 45,
                    axis: false,
                    padding: -12,
                    style: { fill: "linear-gradient(-90deg, white 0%, #6294FA 100%)", fillOpacity: 0.4 },
                    data: visitData2
                  }
                )
              ]
            }
          ),
          /* @__PURE__ */ jsxs(
            Col,
            {
              sm: 12,
              xs: 24,
              style: {
                marginBottom: 24
              },
              children: [
                /* @__PURE__ */ jsx(
                  NumberInfo,
                  {
                    subTitle: /* @__PURE__ */ jsxs("span", { children: [
                      "\u5E73\u5747\u8BC4\u5206",
                      /* @__PURE__ */ jsx(Tooltip, { title: "\u6240\u6709\u70ED\u95E8\u5E94\u7528\u7684\u5E73\u5747\u8BC4\u5206", children: /* @__PURE__ */ jsx(
                        InfoCircleOutlined,
                        {
                          style: {
                            marginLeft: 8
                          }
                        }
                      ) })
                    ] }),
                    total: (searchData.reduce((sum, item) => sum + (item.rating || 0), 0) / (searchData.length || 1)).toFixed(1),
                    status: "up",
                    subTotal: 2.3,
                    gap: 8
                  }
                ),
                /* @__PURE__ */ jsx(
                  Area,
                  {
                    xField: "x",
                    yField: "y",
                    shapeField: "smooth",
                    height: 45,
                    padding: -12,
                    style: { fill: "linear-gradient(-90deg, white 0%, #6294FA 100%)", fillOpacity: 0.4 },
                    data: visitData2,
                    axis: false
                  }
                )
              ]
            }
          )
        ] }),
        /* @__PURE__ */ jsx(
          Table,
          {
            rowKey: (record) => record.app_id,
            size: "small",
            columns,
            dataSource: searchData,
            pagination: {
              style: {
                marginBottom: 0
              },
              pageSize: 5
            }
          }
        )
      ]
    }
  );
};
export default TopSearch;
