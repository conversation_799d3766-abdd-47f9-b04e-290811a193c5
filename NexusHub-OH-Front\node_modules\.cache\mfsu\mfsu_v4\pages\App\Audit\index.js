"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { PageContainer } from "@ant-design/pro-components";
import { Card, Table, Button, Space, Tag, message, Modal, Form, Input } from "antd";
import { CheckOutlined, CloseOutlined, EyeOutlined, ReloadOutlined } from "@ant-design/icons";
import { useState, useEffect } from "react";
import { getReviewerAppsPending, postReviewerAppsIdReview } from "@/services/ant-design-pro/shenheyuan";
const fetchPendingAppList = async (params) => {
  try {
    console.log("Fetching pending apps with params:", params);
    const response = await getReviewerAppsPending({
      page: params.page || 1,
      page_size: params.page_size || 20
    });
    console.log("API\u54CD\u5E94\u6570\u636E:", response);
    if (response && response.code === 200 && response.data) {
      const apps = response.data.data?.map((app) => ({
        id: app.id,
        name: app.name,
        developer_name: app.developer?.username || app.developer_name || "\u672A\u77E5\u5F00\u53D1\u8005",
        current_version: app.current_version,
        category: app.category,
        created_at: app.created_at,
        status: app.status,
        package: app.package,
        description: app.description,
        is_verified: app.is_verified,
        is_featured: app.is_featured,
        download_count: app.download_count || 0,
        average_rating: app.average_rating || 0
      })) || [];
      console.log("\u5904\u7406\u540E\u7684\u5E94\u7528\u6570\u636E:", apps);
      console.log("\u5E94\u7528\u6570\u636E\u8BE6\u60C5:", JSON.stringify(apps, null, 2));
      console.log("\u6570\u636E\u957F\u5EA6:", apps.length);
      return {
        data: apps,
        total: response.data.total || apps.length,
        page: response.data.page || 1,
        page_size: response.data.page_size || 20
      };
    }
    return { data: [], total: 0 };
  } catch (error) {
    console.error("\u83B7\u53D6\u5F85\u5BA1\u6838\u5E94\u7528\u5217\u8868\u5931\u8D25:", error);
    message.error("\u83B7\u53D6\u5F85\u5BA1\u6838\u5E94\u7528\u5217\u8868\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
    return { data: [], total: 0 };
  }
};
const AppAudit = () => {
  const [searchParams, setSearchParams] = useState({});
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await fetchPendingAppList(searchParams);
      console.log("fetchData result:", result);
      setData(result);
    } catch (err) {
      console.error("fetchData error:", err);
      setError(err instanceof Error ? err.message : "\u83B7\u53D6\u6570\u636E\u5931\u8D25");
    } finally {
      setLoading(false);
    }
  };
  const run = () => {
    fetchData();
  };
  useEffect(() => {
    fetchData();
  }, [searchParams]);
  console.log("\u624B\u52A8\u7BA1\u7406\u7684data:", data);
  console.log("Table\u7684dataSource:", data?.data);
  console.log("data\u662F\u5426\u4E3A\u6570\u7EC4:", Array.isArray(data?.data));
  console.log("data\u7684\u7C7B\u578B:", typeof data);
  console.log("total:", data?.total);
  const handleApprove = async (id) => {
    try {
      const response = await postReviewerAppsIdReview(
        { id: Number(id) },
        { status: "approved", reason: "\u5BA1\u6838\u901A\u8FC7" }
      );
      if (response && response.code === 200) {
        message.success("\u5E94\u7528\u5BA1\u6838\u901A\u8FC7");
        run();
      } else {
        message.error(response?.message || "\u5BA1\u6838\u5931\u8D25");
      }
    } catch (error2) {
      console.error("\u5BA1\u6838\u5931\u8D25:", error2);
      message.error("\u5BA1\u6838\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
    }
  };
  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [currentRejectApp, setCurrentRejectApp] = useState(null);
  const [rejectForm] = Form.useForm();
  const handleReject = (app) => {
    setCurrentRejectApp(app);
    setRejectModalVisible(true);
    rejectForm.resetFields();
  };
  const handleRejectSubmit = async () => {
    if (!currentRejectApp) return;
    try {
      const values = await rejectForm.validateFields();
      const response = await postReviewerAppsIdReview(
        { id: currentRejectApp.id },
        { status: "rejected", reason: values.reason }
      );
      if (response && response.code === 200) {
        message.success("\u5E94\u7528\u5DF2\u62D2\u7EDD");
        setRejectModalVisible(false);
        setCurrentRejectApp(null);
        rejectForm.resetFields();
        run();
      } else {
        message.error(response?.message || "\u64CD\u4F5C\u5931\u8D25");
      }
    } catch (error2) {
      console.error("\u64CD\u4F5C\u5931\u8D25:", error2);
      message.error("\u64CD\u4F5C\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
    }
  };
  const handleRejectCancel = () => {
    setRejectModalVisible(false);
    setCurrentRejectApp(null);
    rejectForm.resetFields();
  };
  const handleView = (id) => {
    window.open(`/app/detail/${id}`, "_blank");
  };
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80
    },
    {
      title: "\u5E94\u7528\u540D\u79F0",
      dataIndex: "name",
      key: "name",
      width: 150,
      ellipsis: true
    },
    {
      title: "\u5F00\u53D1\u8005",
      dataIndex: "developer_name",
      key: "developer_name",
      width: 120,
      ellipsis: true
    },
    {
      title: "\u7248\u672C",
      dataIndex: "current_version",
      key: "current_version",
      width: 100
    },
    {
      title: "\u5206\u7C7B",
      dataIndex: "category",
      key: "category",
      width: 100
    },
    {
      title: "\u5305\u540D",
      dataIndex: "package",
      key: "package",
      width: 150,
      ellipsis: true
    },
    {
      title: "\u4E0B\u8F7D\u91CF",
      dataIndex: "download_count",
      key: "download_count",
      width: 100,
      render: (count) => count?.toLocaleString() || 0
    },
    {
      title: "\u8BC4\u5206",
      dataIndex: "average_rating",
      key: "average_rating",
      width: 80,
      render: (rating) => rating ? rating.toFixed(1) : "\u6682\u65E0"
    },
    {
      title: "\u521B\u5EFA\u65F6\u95F4",
      dataIndex: "created_at",
      key: "created_at",
      width: 150,
      render: (time) => time ? new Date(time).toLocaleString() : "-",
      sorter: true
    },
    {
      title: "\u72B6\u6001",
      dataIndex: "status",
      key: "status",
      render: (status) => {
        let color = "blue";
        let text = "\u5F85\u5BA1\u6838";
        if (status === "approved") {
          color = "green";
          text = "\u5DF2\u901A\u8FC7";
        } else if (status === "rejected") {
          color = "red";
          text = "\u5DF2\u62D2\u7EDD";
        }
        return /* @__PURE__ */ jsx(Tag, { color, children: text });
      }
    },
    {
      title: "\u64CD\u4F5C",
      key: "action",
      render: (_, record) => /* @__PURE__ */ jsxs(Space, { size: "middle", children: [
        /* @__PURE__ */ jsx(
          Button,
          {
            type: "primary",
            icon: /* @__PURE__ */ jsx(EyeOutlined, {}),
            size: "small",
            onClick: () => handleView(record.id),
            children: "\u67E5\u770B"
          }
        ),
        record.status === "pending" && /* @__PURE__ */ jsxs(Fragment, { children: [
          /* @__PURE__ */ jsx(
            Button,
            {
              type: "primary",
              icon: /* @__PURE__ */ jsx(CheckOutlined, {}),
              size: "small",
              onClick: () => handleApprove(record.id),
              children: "\u901A\u8FC7"
            }
          ),
          /* @__PURE__ */ jsx(
            Button,
            {
              danger: true,
              icon: /* @__PURE__ */ jsx(CloseOutlined, {}),
              size: "small",
              onClick: () => handleReject(record),
              children: "\u62D2\u7EDD"
            }
          )
        ] })
      ] })
    }
  ];
  return /* @__PURE__ */ jsxs(PageContainer, { children: [
    /* @__PURE__ */ jsxs(Card, { bordered: false, children: [
      /* @__PURE__ */ jsxs("div", { style: { marginBottom: 16, display: "flex", justifyContent: "space-between", alignItems: "center" }, children: [
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("span", { children: "\u5E94\u7528\u5BA1\u6838\u7BA1\u7406" }),
          /* @__PURE__ */ jsxs(Tag, { color: "orange", style: { marginLeft: 8 }, children: [
            "\u5F85\u5BA1\u6838\u5E94\u7528: ",
            data?.total || 0
          ] })
        ] }),
        /* @__PURE__ */ jsx(
          Button,
          {
            icon: /* @__PURE__ */ jsx(ReloadOutlined, {}),
            onClick: () => run(),
            children: "\u5237\u65B0"
          }
        )
      ] }),
      /* @__PURE__ */ jsx(
        Table,
        {
          columns,
          dataSource: data?.data || [],
          rowKey: "id",
          loading,
          scroll: { x: 1200 },
          pagination: {
            current: data?.page || 1,
            pageSize: data?.page_size || 20,
            total: data?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `\u7B2C ${range[0]}-${range[1]} \u6761/\u5171 ${total} \u6761`,
            onChange: (page, pageSize) => {
              setSearchParams({
                ...searchParams,
                page,
                page_size: pageSize
              });
            }
          }
        }
      )
    ] }),
    /* @__PURE__ */ jsxs(
      Modal,
      {
        title: "\u62D2\u7EDD\u5E94\u7528\u5BA1\u6838",
        open: rejectModalVisible,
        onOk: handleRejectSubmit,
        onCancel: handleRejectCancel,
        okText: "\u786E\u8BA4\u62D2\u7EDD",
        cancelText: "\u53D6\u6D88",
        okButtonProps: { danger: true },
        children: [
          /* @__PURE__ */ jsxs("div", { style: { marginBottom: 16 }, children: [
            /* @__PURE__ */ jsxs("p", { children: [
              /* @__PURE__ */ jsx("strong", { children: "\u5E94\u7528\u540D\u79F0\uFF1A" }),
              currentRejectApp?.name
            ] }),
            /* @__PURE__ */ jsxs("p", { children: [
              /* @__PURE__ */ jsx("strong", { children: "\u5F00\u53D1\u8005\uFF1A" }),
              currentRejectApp?.developer_name
            ] }),
            /* @__PURE__ */ jsxs("p", { children: [
              /* @__PURE__ */ jsx("strong", { children: "\u7248\u672C\uFF1A" }),
              currentRejectApp?.current_version
            ] })
          ] }),
          /* @__PURE__ */ jsx(Form, { form: rejectForm, layout: "vertical", children: /* @__PURE__ */ jsx(
            Form.Item,
            {
              name: "reason",
              label: "\u62D2\u7EDD\u539F\u56E0",
              rules: [
                { required: true, message: "\u8BF7\u586B\u5199\u62D2\u7EDD\u539F\u56E0" },
                { min: 5, message: "\u62D2\u7EDD\u539F\u56E0\u81F3\u5C115\u4E2A\u5B57\u7B26" },
                { max: 200, message: "\u62D2\u7EDD\u539F\u56E0\u4E0D\u80FD\u8D85\u8FC7200\u4E2A\u5B57\u7B26" }
              ],
              children: /* @__PURE__ */ jsx(
                Input.TextArea,
                {
                  rows: 4,
                  placeholder: "\u8BF7\u8BE6\u7EC6\u8BF4\u660E\u62D2\u7EDD\u8BE5\u5E94\u7528\u7684\u539F\u56E0\uFF0C\u8FD9\u5C06\u5E2E\u52A9\u5F00\u53D1\u8005\u6539\u8FDB\u5E94\u7528...",
                  showCount: true,
                  maxLength: 200
                }
              )
            }
          ) })
        ]
      }
    )
  ] });
};
export default AppAudit;
