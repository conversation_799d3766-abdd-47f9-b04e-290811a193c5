"use strict";
import { request } from "@umijs/max";
export async function queryCurrent() {
  const response = await request("/users/profile", {
    method: "GET"
  });
  if (response.data) {
    const userResponse = response.data;
    const currentUser = {
      name: userResponse.username || "",
      avatar: userResponse.avatar || "https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png",
      userid: String(userResponse.id) || "",
      email: userResponse.email || "",
      signature: userResponse.description || "",
      title: userResponse.is_developer ? "\u5F00\u53D1\u8005" : "\u666E\u901A\u7528\u6237",
      group: userResponse.company_name || "",
      tags: [],
      notifyCount: 0,
      unreadCount: 0,
      country: userResponse.country || null,
      geographic: {
        province: userResponse.province ? { name: userResponse.province, id: "330000" } : null,
        city: userResponse.city ? { name: userResponse.city, id: "330100" } : null
      },
      address: userResponse.address || "",
      phone: userResponse.phone || ""
    };
    if (userResponse.is_developer) {
      currentUser.tags = [
        { key: "developer", label: "\u5F00\u53D1\u8005" }
      ];
      if (userResponse.verify_status) {
        switch (userResponse.verify_status) {
          case "approved":
            currentUser.tags.push({ key: "verified", label: "\u5DF2\u8BA4\u8BC1" });
            break;
          case "pending":
            currentUser.tags.push({ key: "pending", label: "\u8BA4\u8BC1\u4E2D" });
            break;
          case "rejected":
            currentUser.tags.push({ key: "rejected", label: "\u8BA4\u8BC1\u5931\u8D25" });
            break;
        }
      }
    }
    return { data: currentUser };
  }
  return {
    data: {
      name: "",
      avatar: "https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png",
      userid: "",
      notice: [],
      email: "",
      signature: "",
      title: "",
      group: "",
      tags: [],
      notifyCount: 0,
      unreadCount: 0,
      country: "China",
      geographic: {
        province: { name: "\u6D59\u6C5F\u7701", id: "330000" },
        city: { name: "\u676D\u5DDE\u5E02", id: "330100" }
      },
      address: "",
      phone: ""
    }
  };
}
export async function queryCountry() {
  return request("/geographic/country");
}
export async function queryProvince() {
  return request("/geographic/province");
}
export async function queryCity(province) {
  return request(`/geographic/city/${province}`);
}
export async function queryDistrict(city) {
  return request(`/geographic/district/${city}`);
}
export async function queryStreet(district) {
  return request(`/geographic/street/${district}`);
}
export async function query() {
  return request("/users");
}
