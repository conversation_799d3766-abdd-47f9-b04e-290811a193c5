"use strict";
import { jsx } from "react/jsx-runtime";
import React from "react";
import accessFactory from "@/access";
import { useModel } from "@@/plugin-model";
import { AccessContext } from "./context";
function Provider(props) {
  const { initialState } = useModel("@@initialState");
  const access = React.useMemo(() => accessFactory(initialState), [initialState]);
  return /* @__PURE__ */ jsx(AccessContext.Provider, { value: access, children: props.children });
}
export function accessProvider(container) {
  return /* @__PURE__ */ jsx(Provider, { children: container });
}
