"use strict";
import { request } from "@umijs/max";
export async function getFeaturedCollectionList(params) {
  return request("/public/featured-collections", {
    method: "GET",
    params
  });
}
export async function getFeaturedCollectionDetail(id) {
  return request(`/public/featured-collections/${id}`, {
    method: "GET"
  });
}
export async function createFeaturedCollection(data) {
  return request("/admin/featured-collections", {
    method: "POST",
    data
  });
}
export async function updateFeaturedCollection(id, data) {
  return request(`/admin/featured-collections/${id}`, {
    method: "PUT",
    data
  });
}
export async function deleteFeaturedCollection(id) {
  return request(`/admin/featured-collections/${id}`, {
    method: "DELETE"
  });
}
export async function batchDeleteFeaturedCollections(ids) {
  return request("/admin/featured-collections/batch-delete", {
    method: "POST",
    data: { ids }
  });
}
export async function updateFeaturedCollectionStatus(id, is_active) {
  return request(`/admin/featured-collections/${id}/status`, {
    method: "PUT",
    data: { is_active }
  });
}
export async function updateFeaturedCollectionOrder(id, display_order) {
  return request(`/admin/featured-collections/${id}/order`, {
    method: "PUT",
    data: { display_order }
  });
}
export async function getFeaturedCollectionApps(id, params) {
  return request(`/public/featured-collections/${id}/apps`, {
    method: "GET",
    params
  });
}
export async function addAppsToFeaturedCollection(id, app_ids) {
  return request(`/admin/featured-collections/${id}/apps`, {
    method: "POST",
    data: { app_ids }
  });
}
export async function removeAppsFromFeaturedCollection(id, app_ids) {
  return request(`/admin/featured-collections/${id}/apps`, {
    method: "DELETE",
    data: { app_ids }
  });
}
