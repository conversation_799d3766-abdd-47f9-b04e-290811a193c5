"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { useState, useEffect } from "react";
import { Card, Form, Input, Button, Select, InputNumber, Space, message, Alert, Spin, Divider } from "antd";
import { InboxOutlined, ArrowLeftOutlined } from "@ant-design/icons";
import { history, useModel, useParams } from "@umijs/max";
import { PageContainer } from "@ant-design/pro-components";
import { getAppDetail, updateApp } from "@/services/app";
const { Option } = Select;
const { TextArea } = Input;
const AppEdit = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(true);
  const [appData, setAppData] = useState(null);
  const { id } = useParams();
  const { initialState } = useModel("@@initialState");
  const currentUser = initialState?.currentUser;
  const isAdmin = currentUser?.role === "admin";
  useEffect(() => {
    const fetchAppData = async () => {
      if (!id) {
        message.error("\u5E94\u7528ID\u4E0D\u5B58\u5728");
        history.push("/app/list");
        return;
      }
      try {
        setLoading(true);
        const response = await getAppDetail(id);
        const data = response.data.list[0];
        setAppData(data);
        console.log("API\u8FD4\u56DE\u7684\u5B8C\u6574\u6570\u636E:", data);
        form.setFieldsValue({
          category: data.category,
          description: data.description,
          short_desc: data.short_desc,
          tags: data.tags,
          website_url: data.website_url,
          privacy_url: data.privacy_url,
          price: data.price || "free"
        });
      } catch (error) {
        message.error("\u83B7\u53D6\u5E94\u7528\u4FE1\u606F\u5931\u8D25");
        console.error("\u83B7\u53D6\u5E94\u7528\u8BE6\u60C5\u5931\u8D25:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchAppData();
  }, [id, form]);
  const handleSubmit = async (values) => {
    if (!id) {
      message.error("\u5E94\u7528ID\u4E0D\u5B58\u5728");
      return;
    }
    const formData = {
      category: values.category,
      price: values.price,
      price_amount: values.priceAmount,
      short_desc: values.short_desc,
      description: values.description,
      tags: Array.isArray(values.tags) ? values.tags.join(",") : values.tags,
      website_url: values.website_url,
      privacy_url: values.privacy_url
    };
    console.log("\u63D0\u4EA4\u7684\u8868\u5355\u6570\u636E:", formData);
    console.log("\u5E94\u7528ID:", id, "\u7C7B\u578B:", typeof id);
    try {
      await updateApp(id, formData);
      message.success("\u5E94\u7528\u66F4\u65B0\u6210\u529F");
      history.push("/app/list");
    } catch (error) {
      message.error("\u66F4\u65B0\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5");
      console.error("\u66F4\u65B0\u5E94\u7528\u5931\u8D25:", error);
      console.error("\u9519\u8BEF\u8BE6\u60C5:", error.response?.data);
    }
  };
  const handleBack = () => {
    history.push("/app/list");
  };
  if (loading) {
    return /* @__PURE__ */ jsx(PageContainer, { children: /* @__PURE__ */ jsx("div", { style: { textAlign: "center", padding: "50px" }, children: /* @__PURE__ */ jsx(Spin, { size: "large" }) }) });
  }
  if (!appData) {
    return /* @__PURE__ */ jsx(PageContainer, { children: /* @__PURE__ */ jsxs("div", { style: { textAlign: "center", padding: "50px" }, children: [
      /* @__PURE__ */ jsx("p", { children: "\u5E94\u7528\u4FE1\u606F\u4E0D\u5B58\u5728" }),
      /* @__PURE__ */ jsx(Button, { onClick: handleBack, children: "\u8FD4\u56DE\u5E94\u7528\u5217\u8868" })
    ] }) });
  }
  return /* @__PURE__ */ jsxs(
    PageContainer,
    {
      header: {
        title: "\u7F16\u8F91\u5E94\u7528",
        subTitle: `\u7F16\u8F91\u5E94\u7528\uFF1A${appData.name}`,
        extra: [
          /* @__PURE__ */ jsx(Button, { icon: /* @__PURE__ */ jsx(ArrowLeftOutlined, {}), onClick: handleBack, children: "\u8FD4\u56DE" }, "back")
        ]
      },
      children: [
        isAdmin ? /* @__PURE__ */ jsx(
          Alert,
          {
            message: "\u7BA1\u7406\u5458\u6743\u9650",
            description: "\u60A8\u7F16\u8F91\u7684\u5E94\u7528\u5C06\u76F4\u63A5\u751F\u6548\uFF0C\u65E0\u9700\u5BA1\u6838\u6D41\u7A0B\u3002",
            type: "info",
            showIcon: true,
            style: { marginBottom: 16 }
          }
        ) : /* @__PURE__ */ jsx(
          Alert,
          {
            message: "\u5F00\u53D1\u8005\u6743\u9650",
            description: "\u60A8\u7F16\u8F91\u7684\u5E94\u7528\u9700\u8981\u63D0\u4EA4\u7ED9\u7BA1\u7406\u5458\u5BA1\u6838\uFF0C\u5BA1\u6838\u901A\u8FC7\u540E\u624D\u80FD\u751F\u6548\u3002",
            type: "warning",
            showIcon: true,
            style: { marginBottom: 16 }
          }
        ),
        /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(
          Form,
          {
            form,
            layout: "vertical",
            onFinish: handleSubmit,
            initialValues: {
              price: "free"
            },
            children: [
              /* @__PURE__ */ jsx(Form.Item, { label: "\u5E94\u7528\u540D\u79F0", children: /* @__PURE__ */ jsx(Input, { value: appData?.name, disabled: true }) }),
              /* @__PURE__ */ jsx(Form.Item, { label: "\u5305\u540D", children: /* @__PURE__ */ jsx(Input, { value: appData?.package, disabled: true }) }),
              /* @__PURE__ */ jsx(Form.Item, { label: "\u5E94\u7528\u56FE\u6807", children: /* @__PURE__ */ jsxs("div", { style: { display: "flex", alignItems: "center", gap: "12px" }, children: [
                appData?.icon && /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: appData.icon,
                    alt: "\u5E94\u7528\u56FE\u6807",
                    style: { width: "64px", height: "64px", borderRadius: "8px" }
                  }
                ),
                /* @__PURE__ */ jsx("span", { style: { color: "#666" }, children: "\u5F53\u524D\u7248\u672C\u56FE\u6807\uFF08\u4E0D\u53EF\u4FEE\u6539\uFF09" })
              ] }) }),
              /* @__PURE__ */ jsx(Form.Item, { label: "\u5F00\u53D1\u8005", children: /* @__PURE__ */ jsx(Input, { value: appData?.developer, disabled: true }) }),
              /* @__PURE__ */ jsx(Form.Item, { label: "\u7248\u672C\u53F7", children: /* @__PURE__ */ jsx(Input, { value: appData?.version, disabled: true }) }),
              /* @__PURE__ */ jsx(Form.Item, { label: "\u5E94\u7528\u5927\u5C0F", children: /* @__PURE__ */ jsx(Input, { value: appData?.size, disabled: true }) }),
              /* @__PURE__ */ jsx(Form.Item, { label: "\u6700\u4F4EHarmonyOS\u7248\u672C", children: /* @__PURE__ */ jsx(Input, { value: appData?.min_open_harmony_os_ver, disabled: true }) }),
              /* @__PURE__ */ jsx(Divider, { orientation: "left", children: "\u53EF\u7F16\u8F91\u4FE1\u606F" }),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "category",
                  label: "\u5E94\u7528\u5206\u7C7B",
                  rules: [{ required: true, message: "\u8BF7\u9009\u62E9\u5E94\u7528\u5206\u7C7B" }],
                  children: /* @__PURE__ */ jsxs(Select, { placeholder: "\u8BF7\u9009\u62E9\u5E94\u7528\u5206\u7C7B", children: [
                    /* @__PURE__ */ jsx(Option, { value: "\u793E\u4EA4", children: "\u793E\u4EA4" }),
                    /* @__PURE__ */ jsx(Option, { value: "\u91D1\u878D", children: "\u91D1\u878D" }),
                    /* @__PURE__ */ jsx(Option, { value: "\u89C6\u9891", children: "\u89C6\u9891" }),
                    /* @__PURE__ */ jsx(Option, { value: "\u8D2D\u7269", children: "\u8D2D\u7269" }),
                    /* @__PURE__ */ jsx(Option, { value: "\u97F3\u4E50", children: "\u97F3\u4E50" }),
                    /* @__PURE__ */ jsx(Option, { value: "\u751F\u6D3B", children: "\u751F\u6D3B" }),
                    /* @__PURE__ */ jsx(Option, { value: "\u8D44\u8BAF", children: "\u8D44\u8BAF" }),
                    /* @__PURE__ */ jsx(Option, { value: "\u56FE\u50CF", children: "\u56FE\u50CF" }),
                    /* @__PURE__ */ jsx(Option, { value: "\u529E\u516C", children: "\u529E\u516C" }),
                    /* @__PURE__ */ jsx(Option, { value: "\u6E38\u620F", children: "\u6E38\u620F" }),
                    /* @__PURE__ */ jsx(Option, { value: "\u51FA\u884C", children: "\u51FA\u884C" }),
                    /* @__PURE__ */ jsx(Option, { value: "\u5DE5\u5177", children: "\u5DE5\u5177" })
                  ] })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "price",
                  label: "\u4EF7\u683C",
                  rules: [{ required: true, message: "\u8BF7\u8BBE\u7F6E\u4EF7\u683C" }],
                  children: /* @__PURE__ */ jsxs(
                    Select,
                    {
                      placeholder: "\u8BF7\u9009\u62E9\u4EF7\u683C\u7C7B\u578B",
                      onChange: (value) => {
                        if (value === "free") {
                          form.setFieldsValue({ priceAmount: void 0 });
                        }
                      },
                      children: [
                        /* @__PURE__ */ jsx(Option, { value: "free", children: "\u514D\u8D39" }),
                        /* @__PURE__ */ jsx(Option, { value: "paid", children: "\u4ED8\u8D39" })
                      ]
                    }
                  )
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  noStyle: true,
                  shouldUpdate: (prevValues, currentValues) => prevValues.price !== currentValues.price,
                  children: ({ getFieldValue }) => getFieldValue("price") === "paid" ? /* @__PURE__ */ jsx(
                    Form.Item,
                    {
                      name: "priceAmount",
                      label: "\u4EF7\u683C\u91D1\u989D",
                      rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u4EF7\u683C\u91D1\u989D" }],
                      children: /* @__PURE__ */ jsx(
                        InputNumber,
                        {
                          min: 0.01,
                          precision: 2,
                          placeholder: "\u8BF7\u8F93\u5165\u4EF7\u683C",
                          addonBefore: "\xA5",
                          style: { width: "100%" }
                        }
                      )
                    }
                  ) : null
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "short_desc",
                  label: "\u7B80\u77ED\u63CF\u8FF0",
                  rules: [
                    { required: true, message: "\u8BF7\u8F93\u5165\u7B80\u77ED\u63CF\u8FF0" },
                    { max: 50, message: "\u7B80\u77ED\u63CF\u8FF0\u4E0D\u80FD\u8D85\u8FC750\u4E2A\u5B57\u7B26" }
                  ],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u7B80\u77ED\u63CF\u8FF0\uFF0C\u6700\u591A50\u4E2A\u5B57\u7B26" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "description",
                  label: "\u5E94\u7528\u63CF\u8FF0",
                  rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u5E94\u7528\u63CF\u8FF0" }],
                  children: /* @__PURE__ */ jsx(TextArea, { rows: 4, placeholder: "\u8BF7\u8F93\u5165\u5E94\u7528\u63CF\u8FF0" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "tags",
                  label: "\u6807\u7B7E",
                  rules: [{ required: true, message: "\u8BF7\u9009\u62E9\u81F3\u5C11\u4E00\u4E2A\u6807\u7B7E" }],
                  children: /* @__PURE__ */ jsxs(
                    Select,
                    {
                      mode: "multiple",
                      placeholder: "\u8BF7\u9009\u62E9\u5E94\u7528\u6807\u7B7E",
                      maxTagCount: 5,
                      children: [
                        /* @__PURE__ */ jsx(Option, { value: "\u70ED\u95E8", children: "\u70ED\u95E8" }),
                        /* @__PURE__ */ jsx(Option, { value: "\u65B0\u54C1", children: "\u65B0\u54C1" }),
                        /* @__PURE__ */ jsx(Option, { value: "\u63A8\u8350", children: "\u63A8\u8350" }),
                        /* @__PURE__ */ jsx(Option, { value: "\u514D\u8D39", children: "\u514D\u8D39" }),
                        /* @__PURE__ */ jsx(Option, { value: "\u4ED8\u8D39", children: "\u4ED8\u8D39" }),
                        /* @__PURE__ */ jsx(Option, { value: "\u5B9E\u7528", children: "\u5B9E\u7528" }),
                        /* @__PURE__ */ jsx(Option, { value: "\u5A31\u4E50", children: "\u5A31\u4E50" }),
                        /* @__PURE__ */ jsx(Option, { value: "\u6548\u7387", children: "\u6548\u7387" })
                      ]
                    }
                  )
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "website_url",
                  label: "\u5B98\u65B9\u7F51\u7AD9",
                  rules: [
                    { type: "url", message: "\u8BF7\u8F93\u5165\u6709\u6548\u7684\u7F51\u5740" }
                  ],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u5E94\u7528\u5B98\u65B9\u7F51\u7AD9\u5730\u5740" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "privacy_url",
                  label: "\u9690\u79C1\u653F\u7B56",
                  rules: [
                    { type: "url", message: "\u8BF7\u8F93\u5165\u6709\u6548\u7684\u7F51\u5740" }
                  ],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u9690\u79C1\u653F\u7B56\u9875\u9762\u5730\u5740" })
                }
              ),
              /* @__PURE__ */ jsx(Divider, { orientation: "left", children: "\u5F53\u524D\u7248\u672C\u4FE1\u606F\uFF08\u53EA\u8BFB\uFF09" }),
              /* @__PURE__ */ jsxs(Form.Item, { label: "\u5E94\u7528\u5B89\u88C5\u5305", children: [
                /* @__PURE__ */ jsx("div", { style: { padding: "12px", border: "1px dashed #d9d9d9", borderRadius: "6px", backgroundColor: "#fafafa" }, children: appData?.package_url ? /* @__PURE__ */ jsxs("div", { style: { display: "flex", alignItems: "center", gap: "8px" }, children: [
                  /* @__PURE__ */ jsx(InboxOutlined, { style: { color: "#666" } }),
                  /* @__PURE__ */ jsx("span", { children: "\u5F53\u524D\u7248\u672C\u5B89\u88C5\u5305\u5DF2\u4E0A\u4F20" }),
                  /* @__PURE__ */ jsx("a", { href: appData.package_url, target: "_blank", rel: "noopener noreferrer", children: "\u67E5\u770B" })
                ] }) : /* @__PURE__ */ jsxs("div", { style: { display: "flex", alignItems: "center", gap: "8px", color: "#999" }, children: [
                  /* @__PURE__ */ jsx(InboxOutlined, {}),
                  /* @__PURE__ */ jsx("span", { children: "\u6682\u65E0\u5B89\u88C5\u5305" })
                ] }) }),
                /* @__PURE__ */ jsx("div", { style: { marginTop: "8px", color: "#666", fontSize: "12px" }, children: "\u5982\u9700\u66F4\u65B0\u5B89\u88C5\u5305\uFF0C\u8BF7\u901A\u8FC7\u7248\u672C\u7BA1\u7406\u529F\u80FD\u4E0A\u4F20\u65B0\u7248\u672C" })
              ] }),
              /* @__PURE__ */ jsxs(Form.Item, { label: "\u5E94\u7528\u622A\u56FE", children: [
                /* @__PURE__ */ jsx("div", { style: { display: "flex", gap: "8px", flexWrap: "wrap" }, children: appData?.screenshots && appData.screenshots.length > 0 ? appData.screenshots.map((screenshot, index) => /* @__PURE__ */ jsx("div", { style: { position: "relative" }, children: /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: screenshot.image_url || screenshot,
                    alt: `\u622A\u56FE ${index + 1}`,
                    style: {
                      width: "100px",
                      height: "100px",
                      objectFit: "cover",
                      borderRadius: "6px",
                      border: "1px solid #d9d9d9"
                    }
                  }
                ) }, index)) : /* @__PURE__ */ jsx("div", { style: {
                  width: "100px",
                  height: "100px",
                  border: "1px dashed #d9d9d9",
                  borderRadius: "6px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  color: "#999"
                }, children: "\u6682\u65E0\u622A\u56FE" }) }),
                /* @__PURE__ */ jsx("div", { style: { marginTop: "8px", color: "#666", fontSize: "12px" }, children: "\u5982\u9700\u66F4\u65B0\u622A\u56FE\uFF0C\u8BF7\u901A\u8FC7\u7248\u672C\u7BA1\u7406\u529F\u80FD\u4E0A\u4F20\u65B0\u7248\u672C" })
              ] }),
              /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsxs(Space, { children: [
                /* @__PURE__ */ jsx(Button, { type: "primary", htmlType: "submit", size: "large", children: "\u4FDD\u5B58\u4FEE\u6539" }),
                /* @__PURE__ */ jsx(Button, { size: "large", onClick: handleBack, children: "\u53D6\u6D88" })
              ] }) })
            ]
          }
        ) })
      ]
    }
  );
};
export default AppEdit;
