{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "8b188c5f-d802-4160-9728-8a13f1eccecd", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169728935300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4386fa8-86e0-444a-a102-f0749fdfe5d1", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169729117900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc4cb4a8-6224-4cf4-bdf7-175c55ffb658", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169729310700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d570b9c-5a33-4631-b81b-0543cf805810", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169731022100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6036680-3ee0-413d-901a-5d6a2547ab1e", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169731206300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b7f002d-fac1-4969-9963-491e2847a510", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169733187800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e63d10ad-a452-4ba2-93fe-c563feeb5e1e", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169733862000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e8ae642-078f-4e9a-969f-bf34e186d7f3", "name": "worker[18] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169735321900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f060e7b0-7992-4482-bd59-3850db12cdda", "name": "worker[4] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155169780043600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed117a83-d116-4d73-9d6e-407a0066feed", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180567541600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84890f8a-5589-4c95-b3a7-30b61238c055", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180584413200, "endTime": 155180894445100}, "additional": {"children": ["c4353326-b318-4252-b475-37c938655093", "85f46928-60a7-4d61-90e7-a6e89ceb99ad", "13a1c8c2-a170-4e43-af06-a3fbe8e7a182", "d700dd8b-227e-437f-bc9c-e0a00e177dcc", "d19d47e7-59ed-4701-88cd-e2b4a526867c", "da015c81-1380-4ff4-82af-5b7da9263f51", "ea728ca9-f0fa-4d0c-9d78-2dfb09a062ce"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "ed2912b1-89dd-4516-af33-244b13b14048"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4353326-b318-4252-b475-37c938655093", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180584415800, "endTime": 155180603126900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84890f8a-5589-4c95-b3a7-30b61238c055", "logId": "677228ac-3e5e-45d3-84e5-11cf4d9a7ab5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85f46928-60a7-4d61-90e7-a6e89ceb99ad", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180603154500, "endTime": 155180891773700}, "additional": {"children": ["5748a9f4-2a7c-40e1-ae06-68706e30f545", "583ef6f8-1537-4edf-8a5c-b6d791a6de13", "2d5b790d-065c-4253-a5b0-5593b1ae17df", "bc620789-7b5c-4d1d-b824-a4f1a8c8de5c", "f471cdeb-780b-42d3-9a61-7cba98367226", "8ab6dbdd-bfa3-40e6-b8cc-04717036d3b4", "65fa2082-c686-4917-b03f-d403d2e21720", "86cad0e2-e80e-41f5-ba6e-e8d22b3ed6da", "9fee5be8-42f0-4b80-a252-7c9cc32ca7fa"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84890f8a-5589-4c95-b3a7-30b61238c055", "logId": "d4e62abc-255c-4d11-93a3-cda74bb3ddf0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13a1c8c2-a170-4e43-af06-a3fbe8e7a182", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180891817400, "endTime": 155180894409800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84890f8a-5589-4c95-b3a7-30b61238c055", "logId": "3bef37b7-3f5b-4b7c-bb1b-4558b5be3570"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d700dd8b-227e-437f-bc9c-e0a00e177dcc", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180894419700, "endTime": 155180894437700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84890f8a-5589-4c95-b3a7-30b61238c055", "logId": "1562416f-e551-4687-9c2b-27a5d60a4c3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d19d47e7-59ed-4701-88cd-e2b4a526867c", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180590933900, "endTime": 155180591008900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84890f8a-5589-4c95-b3a7-30b61238c055", "logId": "5993d878-f40f-484a-bd17-aa41965103d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5993d878-f40f-484a-bd17-aa41965103d3", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180590933900, "endTime": 155180591008900}, "additional": {"logType": "info", "children": [], "durationId": "d19d47e7-59ed-4701-88cd-e2b4a526867c", "parent": "ed2912b1-89dd-4516-af33-244b13b14048"}}, {"head": {"id": "da015c81-1380-4ff4-82af-5b7da9263f51", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180597363500, "endTime": 155180597385000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84890f8a-5589-4c95-b3a7-30b61238c055", "logId": "71423553-52c2-4b0b-89d4-3733b96b43f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71423553-52c2-4b0b-89d4-3733b96b43f8", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180597363500, "endTime": 155180597385000}, "additional": {"logType": "info", "children": [], "durationId": "da015c81-1380-4ff4-82af-5b7da9263f51", "parent": "ed2912b1-89dd-4516-af33-244b13b14048"}}, {"head": {"id": "b739ae65-5a09-4fee-bce7-333073c8b5b1", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180597434600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efde0704-0be1-4d2c-aae9-602579fd9890", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180602923500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "677228ac-3e5e-45d3-84e5-11cf4d9a7ab5", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180584415800, "endTime": 155180603126900}, "additional": {"logType": "info", "children": [], "durationId": "c4353326-b318-4252-b475-37c938655093", "parent": "ed2912b1-89dd-4516-af33-244b13b14048"}}, {"head": {"id": "5748a9f4-2a7c-40e1-ae06-68706e30f545", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180608168200, "endTime": 155180608175500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85f46928-60a7-4d61-90e7-a6e89ceb99ad", "logId": "7ebc4741-9dd8-4d9b-84fe-c0465f5702de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "583ef6f8-1537-4edf-8a5c-b6d791a6de13", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180608188000, "endTime": 155180613335400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85f46928-60a7-4d61-90e7-a6e89ceb99ad", "logId": "a53df84f-ef1f-4e3b-b774-64325e284740"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d5b790d-065c-4253-a5b0-5593b1ae17df", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180613351800, "endTime": 155180721034900}, "additional": {"children": ["5dbfaf1a-8adb-4bea-8d7a-30397e9bc79f", "9925b9ae-aeec-4bea-a130-016a80e989da", "54d830e4-e0cd-4cc6-bce9-e547772f1e43"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85f46928-60a7-4d61-90e7-a6e89ceb99ad", "logId": "bed6ddd0-065c-410a-9208-ec55b1cc3ad6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc620789-7b5c-4d1d-b824-a4f1a8c8de5c", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180721045800, "endTime": 155180740729900}, "additional": {"children": ["045999ae-0759-473a-a537-cae3593dfaba"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85f46928-60a7-4d61-90e7-a6e89ceb99ad", "logId": "9f4a20a6-62eb-4028-be0d-8ca0a73d235a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f471cdeb-780b-42d3-9a61-7cba98367226", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180740735600, "endTime": 155180829756000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85f46928-60a7-4d61-90e7-a6e89ceb99ad", "logId": "ca201cfb-5df4-4714-858b-61a857f90c5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ab6dbdd-bfa3-40e6-b8cc-04717036d3b4", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180831968900, "endTime": 155180857079100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85f46928-60a7-4d61-90e7-a6e89ceb99ad", "logId": "6dc01b23-502f-4140-ad3a-d69a9dd921ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65fa2082-c686-4917-b03f-d403d2e21720", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180857109900, "endTime": 155180891454900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85f46928-60a7-4d61-90e7-a6e89ceb99ad", "logId": "95cdd88b-a651-43fb-884d-113a5177706a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86cad0e2-e80e-41f5-ba6e-e8d22b3ed6da", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180891489300, "endTime": 155180891751700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85f46928-60a7-4d61-90e7-a6e89ceb99ad", "logId": "76392f88-5d61-4caa-aa07-6543ee0fe9d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ebc4741-9dd8-4d9b-84fe-c0465f5702de", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180608168200, "endTime": 155180608175500}, "additional": {"logType": "info", "children": [], "durationId": "5748a9f4-2a7c-40e1-ae06-68706e30f545", "parent": "d4e62abc-255c-4d11-93a3-cda74bb3ddf0"}}, {"head": {"id": "a53df84f-ef1f-4e3b-b774-64325e284740", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180608188000, "endTime": 155180613335400}, "additional": {"logType": "info", "children": [], "durationId": "583ef6f8-1537-4edf-8a5c-b6d791a6de13", "parent": "d4e62abc-255c-4d11-93a3-cda74bb3ddf0"}}, {"head": {"id": "5dbfaf1a-8adb-4bea-8d7a-30397e9bc79f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180614184900, "endTime": 155180614208900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2d5b790d-065c-4253-a5b0-5593b1ae17df", "logId": "5809512e-6753-4b61-a5d7-4c2291bcae2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5809512e-6753-4b61-a5d7-4c2291bcae2d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180614184900, "endTime": 155180614208900}, "additional": {"logType": "info", "children": [], "durationId": "5dbfaf1a-8adb-4bea-8d7a-30397e9bc79f", "parent": "bed6ddd0-065c-410a-9208-ec55b1cc3ad6"}}, {"head": {"id": "9925b9ae-aeec-4bea-a130-016a80e989da", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180616964400, "endTime": 155180720435000}, "additional": {"children": ["4a7b9903-24c4-4d7b-a481-7dc6647fa5bf", "69da05ec-bad4-4e80-bd76-db243b4a9e10"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2d5b790d-065c-4253-a5b0-5593b1ae17df", "logId": "6fdce445-73e4-4fe7-80ed-a913db3c804f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a7b9903-24c4-4d7b-a481-7dc6647fa5bf", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180616966600, "endTime": 155180623714300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9925b9ae-aeec-4bea-a130-016a80e989da", "logId": "839e5398-5f1e-4351-a826-2f8efd748cb7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69da05ec-bad4-4e80-bd76-db243b4a9e10", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180623748600, "endTime": 155180720425000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9925b9ae-aeec-4bea-a130-016a80e989da", "logId": "a19f638c-53a3-42d5-98e8-f02e5261935b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b8f6093-7cd2-418a-ad95-ac0522ea0519", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180616972400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e61b5ad6-ceab-4d25-acef-0b94aca215ad", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180623383600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "839e5398-5f1e-4351-a826-2f8efd748cb7", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180616966600, "endTime": 155180623714300}, "additional": {"logType": "info", "children": [], "durationId": "4a7b9903-24c4-4d7b-a481-7dc6647fa5bf", "parent": "6fdce445-73e4-4fe7-80ed-a913db3c804f"}}, {"head": {"id": "abcbcf0b-cf1d-429f-b29d-387f03c5a013", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180623776600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75684dc7-9abc-42b0-a325-60501a85f802", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180635863100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbadedd4-d162-4d4f-9f59-bc7d140b16ad", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180636021500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e1ec5fe-1cbb-40a9-a968-4671c6025da2", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180636207200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "550ba65c-5e84-40d6-b630-3385c89fcb78", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180636342000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eeb6383b-4f2d-4dae-913d-084b3b25ded0", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180638902300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d3a0286-b8ac-4bc2-a5ca-87c010b09838", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180659590700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0960b913-2592-4984-9519-1504ad153172", "name": "Sdk init in 39 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180687045600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aabe3523-d784-4515-901e-a5d99f922752", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180687196900}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 3, "second": 30}, "markType": "other"}}, {"head": {"id": "83822476-e412-448c-bbd4-1918f9b2772d", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180687209200}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 3, "second": 30}, "markType": "other"}}, {"head": {"id": "3e2c2987-d189-48d9-ab36-608b82e01f8c", "name": "Project task initialization takes 32 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180720210000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7956d67-4fe5-4584-8a73-a7bcd668fcc5", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180720321000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1572b235-a7db-4194-9a5c-c83a0018b7a1", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180720363500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ef77ec7-51e3-4031-8a03-3e1b1d05b013", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180720395600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a19f638c-53a3-42d5-98e8-f02e5261935b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180623748600, "endTime": 155180720425000}, "additional": {"logType": "info", "children": [], "durationId": "69da05ec-bad4-4e80-bd76-db243b4a9e10", "parent": "6fdce445-73e4-4fe7-80ed-a913db3c804f"}}, {"head": {"id": "6fdce445-73e4-4fe7-80ed-a913db3c804f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180616964400, "endTime": 155180720435000}, "additional": {"logType": "info", "children": ["839e5398-5f1e-4351-a826-2f8efd748cb7", "a19f638c-53a3-42d5-98e8-f02e5261935b"], "durationId": "9925b9ae-aeec-4bea-a130-016a80e989da", "parent": "bed6ddd0-065c-410a-9208-ec55b1cc3ad6"}}, {"head": {"id": "54d830e4-e0cd-4cc6-bce9-e547772f1e43", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180720970700, "endTime": 155180720985000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2d5b790d-065c-4253-a5b0-5593b1ae17df", "logId": "3d44c9a1-219e-43a8-8832-6097feda84e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d44c9a1-219e-43a8-8832-6097feda84e6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180720970700, "endTime": 155180720985000}, "additional": {"logType": "info", "children": [], "durationId": "54d830e4-e0cd-4cc6-bce9-e547772f1e43", "parent": "bed6ddd0-065c-410a-9208-ec55b1cc3ad6"}}, {"head": {"id": "bed6ddd0-065c-410a-9208-ec55b1cc3ad6", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180613351800, "endTime": 155180721034900}, "additional": {"logType": "info", "children": ["5809512e-6753-4b61-a5d7-4c2291bcae2d", "6fdce445-73e4-4fe7-80ed-a913db3c804f", "3d44c9a1-219e-43a8-8832-6097feda84e6"], "durationId": "2d5b790d-065c-4253-a5b0-5593b1ae17df", "parent": "d4e62abc-255c-4d11-93a3-cda74bb3ddf0"}}, {"head": {"id": "045999ae-0759-473a-a537-cae3593dfaba", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180721524400, "endTime": 155180740720800}, "additional": {"children": ["d6412d26-25f3-413d-9b75-03d72a7df58b", "0ea5289b-4f37-48b1-ab4b-4a588a3d8ed9", "f849b8b5-695a-4b02-a5f6-e123154d3c91"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bc620789-7b5c-4d1d-b824-a4f1a8c8de5c", "logId": "455b5ef0-dc68-432e-9c9c-25c8d8a88707"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6412d26-25f3-413d-9b75-03d72a7df58b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180723776200, "endTime": 155180723787600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "045999ae-0759-473a-a537-cae3593dfaba", "logId": "0ed371d0-6599-403e-bb91-ab2dd7a0ef38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ed371d0-6599-403e-bb91-ab2dd7a0ef38", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180723776200, "endTime": 155180723787600}, "additional": {"logType": "info", "children": [], "durationId": "d6412d26-25f3-413d-9b75-03d72a7df58b", "parent": "455b5ef0-dc68-432e-9c9c-25c8d8a88707"}}, {"head": {"id": "0ea5289b-4f37-48b1-ab4b-4a588a3d8ed9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180725167300, "endTime": 155180738950000}, "additional": {"children": ["401e0064-7f33-42f4-b5c2-e46d1caeb558", "a7b601d6-36b3-4e26-9965-1442193d2070"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "045999ae-0759-473a-a537-cae3593dfaba", "logId": "6a6bad18-4287-4ba0-8455-7b94a2e5937f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "401e0064-7f33-42f4-b5c2-e46d1caeb558", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180725168300, "endTime": 155180727427900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0ea5289b-4f37-48b1-ab4b-4a588a3d8ed9", "logId": "22702d32-a2b2-4452-83c9-5efe7cf2f4c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7b601d6-36b3-4e26-9965-1442193d2070", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180727437200, "endTime": 155180738940500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0ea5289b-4f37-48b1-ab4b-4a588a3d8ed9", "logId": "68f2fa20-1284-4053-b111-123939fdc231"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8919164-2092-4ef9-a997-f5ec1f2a4da6", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180725170800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9597529d-6189-4d7c-bd97-095024efe890", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180727339900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22702d32-a2b2-4452-83c9-5efe7cf2f4c9", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180725168300, "endTime": 155180727427900}, "additional": {"logType": "info", "children": [], "durationId": "401e0064-7f33-42f4-b5c2-e46d1caeb558", "parent": "6a6bad18-4287-4ba0-8455-7b94a2e5937f"}}, {"head": {"id": "ba3ce4bd-3cf1-4c4c-a344-6cb63d0789b0", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180727443600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31be91d5-af09-42c3-8e9d-f07a502e347d", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180734672800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4982ffb8-114b-4075-8b5b-fc4825d35264", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180734803600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c58ce81-76d6-486b-8fc9-88c0ac9438f2", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180734959900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c764d733-585b-434c-8290-3e2cb9de0888", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735041800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7199395-c81c-4f05-8cfc-d0bf06e1f53d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735076100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b19249b-0b1d-488c-9b98-34a5c4b67a87", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735107100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaada02f-c500-4080-a67d-3846f7ba838f", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735143800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a3877c8-e549-4450-904e-8b87c1994958", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735176500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef9c57f4-b338-4055-92fa-3ab1c0e8d5c2", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735312700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5fa3450-af55-4e24-af4e-cf8551764e62", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735379400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b502b8c5-e3eb-43e5-9aff-230bd296f251", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735420600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4543dbe5-6bee-487d-803b-7b23ccba09d7", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735449500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77c9c434-e11a-4f95-867b-60f7e879b15e", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735485700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f56c132-6013-495b-b3df-586539c603b2", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735515000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adf487d2-0e7d-4829-b21a-9881367a0a05", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735582900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "823e49f6-9d25-4925-8f3b-aafcdafea0af", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735648400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c07161ec-b565-49ed-bac2-987357fb2968", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735680100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0edf471a-25f4-40db-8d4a-3201ec293668", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735709700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3298c9c9-58d5-4722-98ff-b2af2a3f8418", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180735754700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c7f5eb0-4cb1-4136-bafc-4a903ed70f2d", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180738514900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cb14614-3069-4070-8f17-f211f42335ee", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180738806400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4afbdb25-5a86-4965-ac6d-723e7159d11b", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180738870000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d43e1afc-1d1d-4644-bb15-52a02beaeab1", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180738904100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68f2fa20-1284-4053-b111-123939fdc231", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180727437200, "endTime": 155180738940500}, "additional": {"logType": "info", "children": [], "durationId": "a7b601d6-36b3-4e26-9965-1442193d2070", "parent": "6a6bad18-4287-4ba0-8455-7b94a2e5937f"}}, {"head": {"id": "6a6bad18-4287-4ba0-8455-7b94a2e5937f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180725167300, "endTime": 155180738950000}, "additional": {"logType": "info", "children": ["22702d32-a2b2-4452-83c9-5efe7cf2f4c9", "68f2fa20-1284-4053-b111-123939fdc231"], "durationId": "0ea5289b-4f37-48b1-ab4b-4a588a3d8ed9", "parent": "455b5ef0-dc68-432e-9c9c-25c8d8a88707"}}, {"head": {"id": "f849b8b5-695a-4b02-a5f6-e123154d3c91", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180740689600, "endTime": 155180740702300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "045999ae-0759-473a-a537-cae3593dfaba", "logId": "277687fe-7ccd-4fa8-a084-2debb8c887c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "277687fe-7ccd-4fa8-a084-2debb8c887c0", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180740689600, "endTime": 155180740702300}, "additional": {"logType": "info", "children": [], "durationId": "f849b8b5-695a-4b02-a5f6-e123154d3c91", "parent": "455b5ef0-dc68-432e-9c9c-25c8d8a88707"}}, {"head": {"id": "455b5ef0-dc68-432e-9c9c-25c8d8a88707", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180721524400, "endTime": 155180740720800}, "additional": {"logType": "info", "children": ["0ed371d0-6599-403e-bb91-ab2dd7a0ef38", "6a6bad18-4287-4ba0-8455-7b94a2e5937f", "277687fe-7ccd-4fa8-a084-2debb8c887c0"], "durationId": "045999ae-0759-473a-a537-cae3593dfaba", "parent": "9f4a20a6-62eb-4028-be0d-8ca0a73d235a"}}, {"head": {"id": "9f4a20a6-62eb-4028-be0d-8ca0a73d235a", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180721045800, "endTime": 155180740729900}, "additional": {"logType": "info", "children": ["455b5ef0-dc68-432e-9c9c-25c8d8a88707"], "durationId": "bc620789-7b5c-4d1d-b824-a4f1a8c8de5c", "parent": "d4e62abc-255c-4d11-93a3-cda74bb3ddf0"}}, {"head": {"id": "45190ea9-90e6-4f10-a6b1-2e31863823a9", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180766419500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "039314dc-c521-4e3c-8899-7a6e3ec4d69c", "name": "hvigorfile, resolve hvigorfile dependencies in 89 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180829527400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca201cfb-5df4-4714-858b-61a857f90c5b", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180740735600, "endTime": 155180829756000}, "additional": {"logType": "info", "children": [], "durationId": "f471cdeb-780b-42d3-9a61-7cba98367226", "parent": "d4e62abc-255c-4d11-93a3-cda74bb3ddf0"}}, {"head": {"id": "9fee5be8-42f0-4b80-a252-7c9cc32ca7fa", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180831438600, "endTime": 155180831937700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85f46928-60a7-4d61-90e7-a6e89ceb99ad", "logId": "1caae96b-dfdd-4e49-bc4c-83c42e03ba57"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb012515-fba9-4bdd-a6c8-df4998026fc9", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180831500700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1caae96b-dfdd-4e49-bc4c-83c42e03ba57", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180831438600, "endTime": 155180831937700}, "additional": {"logType": "info", "children": [], "durationId": "9fee5be8-42f0-4b80-a252-7c9cc32ca7fa", "parent": "d4e62abc-255c-4d11-93a3-cda74bb3ddf0"}}, {"head": {"id": "cc5fdc70-7bf5-48ba-a321-7403433f314b", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180835029400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "943c7b65-4855-454d-9ea0-ce951604acd4", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180855516100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dc01b23-502f-4140-ad3a-d69a9dd921ca", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180831968900, "endTime": 155180857079100}, "additional": {"logType": "info", "children": [], "durationId": "8ab6dbdd-bfa3-40e6-b8cc-04717036d3b4", "parent": "d4e62abc-255c-4d11-93a3-cda74bb3ddf0"}}, {"head": {"id": "8822ed8f-81b7-483b-8355-b597fde84b64", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180857134300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d183a992-c07f-47d9-8c3e-ceba42f72959", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180874400500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84300de4-3bb8-4079-97b6-e63d63ef9081", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180874633400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07f6c612-967a-4d8d-b4c7-d8e991c72e2c", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180874971100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15d6ce4f-6047-409e-8f33-3081c60bbaed", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180882439400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2017fe08-bcf2-4360-815f-a7f50a4688e5", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180882637500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95cdd88b-a651-43fb-884d-113a5177706a", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180857109900, "endTime": 155180891454900}, "additional": {"logType": "info", "children": [], "durationId": "65fa2082-c686-4917-b03f-d403d2e21720", "parent": "d4e62abc-255c-4d11-93a3-cda74bb3ddf0"}}, {"head": {"id": "94c34718-df9b-493b-9f23-800f251227c8", "name": "Configuration phase cost:284 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180891533200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76392f88-5d61-4caa-aa07-6543ee0fe9d6", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180891489300, "endTime": 155180891751700}, "additional": {"logType": "info", "children": [], "durationId": "86cad0e2-e80e-41f5-ba6e-e8d22b3ed6da", "parent": "d4e62abc-255c-4d11-93a3-cda74bb3ddf0"}}, {"head": {"id": "d4e62abc-255c-4d11-93a3-cda74bb3ddf0", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180603154500, "endTime": 155180891773700}, "additional": {"logType": "info", "children": ["7ebc4741-9dd8-4d9b-84fe-c0465f5702de", "a53df84f-ef1f-4e3b-b774-64325e284740", "bed6ddd0-065c-410a-9208-ec55b1cc3ad6", "9f4a20a6-62eb-4028-be0d-8ca0a73d235a", "ca201cfb-5df4-4714-858b-61a857f90c5b", "6dc01b23-502f-4140-ad3a-d69a9dd921ca", "95cdd88b-a651-43fb-884d-113a5177706a", "76392f88-5d61-4caa-aa07-6543ee0fe9d6", "1caae96b-dfdd-4e49-bc4c-83c42e03ba57"], "durationId": "85f46928-60a7-4d61-90e7-a6e89ceb99ad", "parent": "ed2912b1-89dd-4516-af33-244b13b14048"}}, {"head": {"id": "ea728ca9-f0fa-4d0c-9d78-2dfb09a062ce", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180894356800, "endTime": 155180894389500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84890f8a-5589-4c95-b3a7-30b61238c055", "logId": "e9b4fbe2-781f-4235-ae2a-69361c31b1cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9b4fbe2-781f-4235-ae2a-69361c31b1cd", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180894356800, "endTime": 155180894389500}, "additional": {"logType": "info", "children": [], "durationId": "ea728ca9-f0fa-4d0c-9d78-2dfb09a062ce", "parent": "ed2912b1-89dd-4516-af33-244b13b14048"}}, {"head": {"id": "3bef37b7-3f5b-4b7c-bb1b-4558b5be3570", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180891817400, "endTime": 155180894409800}, "additional": {"logType": "info", "children": [], "durationId": "13a1c8c2-a170-4e43-af06-a3fbe8e7a182", "parent": "ed2912b1-89dd-4516-af33-244b13b14048"}}, {"head": {"id": "1562416f-e551-4687-9c2b-27a5d60a4c3e", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180894419700, "endTime": 155180894437700}, "additional": {"logType": "info", "children": [], "durationId": "d700dd8b-227e-437f-bc9c-e0a00e177dcc", "parent": "ed2912b1-89dd-4516-af33-244b13b14048"}}, {"head": {"id": "ed2912b1-89dd-4516-af33-244b13b14048", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180584413200, "endTime": 155180894445100}, "additional": {"logType": "info", "children": ["677228ac-3e5e-45d3-84e5-11cf4d9a7ab5", "d4e62abc-255c-4d11-93a3-cda74bb3ddf0", "3bef37b7-3f5b-4b7c-bb1b-4558b5be3570", "1562416f-e551-4687-9c2b-27a5d60a4c3e", "5993d878-f40f-484a-bd17-aa41965103d3", "71423553-52c2-4b0b-89d4-3733b96b43f8", "e9b4fbe2-781f-4235-ae2a-69361c31b1cd"], "durationId": "84890f8a-5589-4c95-b3a7-30b61238c055"}}, {"head": {"id": "03d7b5a7-b9f7-44e7-8fa8-d8855850c31c", "name": "Configuration task cost before running: 318 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180894949600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "215438b7-7ec7-4a2d-becd-9b808446541a", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180916847500, "endTime": 155180941858900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "43332f41-065b-4893-b874-00900d4b21b6", "logId": "ff7bb60e-fd91-42b0-b525-2940a1a35e60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43332f41-065b-4893-b874-00900d4b21b6", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180898252700}, "additional": {"logType": "detail", "children": [], "durationId": "215438b7-7ec7-4a2d-becd-9b808446541a"}}, {"head": {"id": "764c57f1-bb1c-4438-b49d-7969bc58ffa0", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180899971300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df0831d0-3c50-4874-9f56-03da49f5a454", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180900254300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b20028b7-909e-454e-8e36-3791cee2a9b4", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180902277200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2df0caa6-aeb4-4a32-b150-c9c505a8e934", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180906422100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef4ee414-4647-48fb-8417-2d002519f6d0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180909312200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96ec9791-d95f-4129-baa7-1fbf40c571c1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180909723100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e320df6e-cf6c-4c8a-8959-82671d771b9e", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180916870400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7bb0471-fabb-4e11-a326-d646fe2fb417", "name": "Incremental task entry:default@PreBuild pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180941455600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7687cb3-50e9-49eb-844f-966393054961", "name": "entry : default@PreBuild cost memory 0.32102203369140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180941709400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff7bb60e-fd91-42b0-b525-2940a1a35e60", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180916847500, "endTime": 155180941858900}, "additional": {"logType": "info", "children": [], "durationId": "215438b7-7ec7-4a2d-becd-9b808446541a"}}, {"head": {"id": "d23458dc-e774-4627-9ff2-c3c426f21eca", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180960067700, "endTime": 155180966623900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e75a7e27-0cfa-4b1f-a365-d25e842888e8", "logId": "13a24f20-ee33-4b4f-bd0d-7251564fa891"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e75a7e27-0cfa-4b1f-a365-d25e842888e8", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180952426000}, "additional": {"logType": "detail", "children": [], "durationId": "d23458dc-e774-4627-9ff2-c3c426f21eca"}}, {"head": {"id": "aa01f74a-781d-4b3f-9360-cc13c644ccb7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180955911100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c961344d-9369-477c-a7da-da00a95c9c2a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180957564800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0338da0e-43b7-43f6-a18f-daf866da0539", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180960093400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63a4623b-ea24-4dc7-87c9-4572f1f94dc6", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180963025700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e62310a-66b0-4927-8b18-f08686662718", "name": "entry : default@CreateModuleInfo cost memory 0.06081390380859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180966198200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b583b9c-95d3-42ab-ae63-866b1b258eca", "name": "runTaskFromQueue task cost before running: 389 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180966490500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13a24f20-ee33-4b4f-bd0d-7251564fa891", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180960067700, "endTime": 155180966623900, "totalTime": 6369600}, "additional": {"logType": "info", "children": [], "durationId": "d23458dc-e774-4627-9ff2-c3c426f21eca"}}, {"head": {"id": "d5df2f43-d7ba-45b2-8556-8056c9139aca", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180983287600, "endTime": 155180988686000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "83a2b8b2-ab8c-4967-8474-f6a0315850fc", "logId": "8308e162-751b-4f58-8b1d-3f17b9249a39"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83a2b8b2-ab8c-4967-8474-f6a0315850fc", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180971266300}, "additional": {"logType": "detail", "children": [], "durationId": "d5df2f43-d7ba-45b2-8556-8056c9139aca"}}, {"head": {"id": "179d213c-fbd0-4707-a054-9d2102c091d7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180974144300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "933fe7a4-a23e-4695-b35a-6eeb9307a2f1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180974345200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e76082d-a369-40b6-8f31-d456535e242d", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180983321500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a7e60ed-252e-4371-b845-c2e3b3607fc2", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180985773700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90d7375a-79d8-497a-880f-ad435020d2c3", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180988314300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40b1d37c-7c7f-487f-9d74-e51b7cb74f03", "name": "entry : default@GenerateMetadata cost memory 0.102264404296875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180988533500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8308e162-751b-4f58-8b1d-3f17b9249a39", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180983287600, "endTime": 155180988686000}, "additional": {"logType": "info", "children": [], "durationId": "d5df2f43-d7ba-45b2-8556-8056c9139aca"}}, {"head": {"id": "11913ac5-c122-433b-8354-97316c9f9984", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180994942700, "endTime": 155180995820800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5eea79c9-9b23-40d3-bdc7-cfaafb4648ad", "logId": "896564a1-e7f6-4404-8135-c34b7449f54b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5eea79c9-9b23-40d3-bdc7-cfaafb4648ad", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180991883100}, "additional": {"logType": "detail", "children": [], "durationId": "11913ac5-c122-433b-8354-97316c9f9984"}}, {"head": {"id": "7e3bc423-9c09-429b-aa0d-979f27af0513", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180994474600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "170adf4d-ffd7-4e15-85b4-bd7e6b9ace31", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180994680800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acce13a3-dac0-4994-ac98-03d0635a9e1f", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180994958100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9598677a-5067-4c5d-8d34-c83106f3687e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180995270500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecf389c7-caa0-4905-8a6d-3865c9895236", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180995451900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5af858ad-5bcb-4eef-8062-e31a99f55a02", "name": "entry : default@ConfigureCmake cost memory 0.0372467041015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180995599200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98f4e92c-367b-4038-83b8-f10f1ad6634f", "name": "runTaskFromQueue task cost before running: 418 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180995733300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "896564a1-e7f6-4404-8135-c34b7449f54b", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180994942700, "endTime": 155180995820800, "totalTime": 762900}, "additional": {"logType": "info", "children": [], "durationId": "11913ac5-c122-433b-8354-97316c9f9984"}}, {"head": {"id": "8a5bacaf-fb35-47a9-be6f-d33476f58fbb", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181005615200, "endTime": 155181010993300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "5a714cfb-5c39-4aa6-b65c-eacbc680573e", "logId": "789a5fad-7419-440b-a4fe-40c76be08e41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a714cfb-5c39-4aa6-b65c-eacbc680573e", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181000673200}, "additional": {"logType": "detail", "children": [], "durationId": "8a5bacaf-fb35-47a9-be6f-d33476f58fbb"}}, {"head": {"id": "77535476-1bb8-4aee-8a42-4bd4dddcfc56", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181003756100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e2ebc4e-4aa6-4dcb-a9de-1e4886545148", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181003987900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "729d51ad-84d7-45e9-a93e-5dab4acd482d", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181005639600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caead8ca-3a1f-4ab0-8afd-271816ef6700", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181010464000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8659f494-9f55-4107-877c-55bc7f5d04e8", "name": "entry : default@MergeProfile cost memory 0.11811065673828125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181010807700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "789a5fad-7419-440b-a4fe-40c76be08e41", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181005615200, "endTime": 155181010993300}, "additional": {"logType": "info", "children": [], "durationId": "8a5bacaf-fb35-47a9-be6f-d33476f58fbb"}}, {"head": {"id": "25b3592d-9253-40e4-b7bb-28c46c33c94b", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181017703800, "endTime": 155181024499900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "152eac5b-1115-4b2a-a88c-d7bcd94fd0ed", "logId": "9c3d025e-8edd-4119-bb81-af4cbc183ee1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "152eac5b-1115-4b2a-a88c-d7bcd94fd0ed", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181014010500}, "additional": {"logType": "detail", "children": [], "durationId": "25b3592d-9253-40e4-b7bb-28c46c33c94b"}}, {"head": {"id": "8e85a651-2904-4d90-a543-7aa2d9460d1d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181015934000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "932a565d-fa17-45df-bb41-be873f95e98e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181016159700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59cbb607-c99a-4f63-9bac-469de9ce6f44", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181017725200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41df6069-d4d3-411c-b4ae-f496ab381764", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181019825800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "110c9442-95cf-49e7-a9d8-96765a769e10", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181024098000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59e93927-56c6-4cad-9f5e-9478a52e3489", "name": "entry : default@CreateBuildProfile cost memory 0.107574462890625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181024347000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c3d025e-8edd-4119-bb81-af4cbc183ee1", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181017703800, "endTime": 155181024499900}, "additional": {"logType": "info", "children": [], "durationId": "25b3592d-9253-40e4-b7bb-28c46c33c94b"}}, {"head": {"id": "3410f30f-540b-4e15-9072-925fd1f1067a", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181032825600, "endTime": 155181033937200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "ac715fac-fc25-4f9e-9dff-4baab62edc1d", "logId": "6ec79a5c-4ec8-48ac-a246-f52ecebae030"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac715fac-fc25-4f9e-9dff-4baab62edc1d", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181028207100}, "additional": {"logType": "detail", "children": [], "durationId": "3410f30f-540b-4e15-9072-925fd1f1067a"}}, {"head": {"id": "809582ec-d732-469d-8fc0-11abe2bb0d3e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181030921100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb0f0f78-32d7-469c-ad30-52f309d3e883", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181031151900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "817d0804-0510-4912-9147-5b663539a448", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181032845100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a6c3bbb-8aa1-4af6-a720-745932d1d330", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181033098700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4208593-d01d-4dc0-88c9-3b2d0153f598", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181033218800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb035f35-2a28-47c9-98aa-5ed2fa484fd2", "name": "entry : default@PreCheckSyscap cost memory 0.0408782958984375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181033625600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9900c69d-ce3e-4f4a-8cbb-08eee7c2750b", "name": "runTaskFromQueue task cost before running: 457 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181033818600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ec79a5c-4ec8-48ac-a246-f52ecebae030", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181032825600, "endTime": 155181033937200, "totalTime": 956700}, "additional": {"logType": "info", "children": [], "durationId": "3410f30f-540b-4e15-9072-925fd1f1067a"}}, {"head": {"id": "c828a0d3-d5fb-40f4-b391-725751e18132", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181042965800, "endTime": 155181054882100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2125d905-2c4c-4b96-b000-e72c3853a1e9", "logId": "64f05c1e-c00c-409b-9b65-0783ce44e3f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2125d905-2c4c-4b96-b000-e72c3853a1e9", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181037240400}, "additional": {"logType": "detail", "children": [], "durationId": "c828a0d3-d5fb-40f4-b391-725751e18132"}}, {"head": {"id": "4f50ff28-576f-44cc-8fab-8a37f8322a4d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181039696200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42b63abf-65d8-4520-af02-857eaae235ee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181039896800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47396c7f-1e08-4eb3-bf5e-89747b809cd8", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181042986800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adf5db87-3bc0-4db3-955e-2eb0434460e9", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181053012500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b5f9b2e-3c7e-4f57-bbd9-a8ef379c9fa2", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181054560300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b94aff58-96b6-4ca1-abda-8d5fc6613862", "name": "entry : default@GeneratePkgContextInfo cost memory 0.24942779541015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181054752300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64f05c1e-c00c-409b-9b65-0783ce44e3f0", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181042965800, "endTime": 155181054882100}, "additional": {"logType": "info", "children": [], "durationId": "c828a0d3-d5fb-40f4-b391-725751e18132"}}, {"head": {"id": "d1d9a529-177d-4c75-8ffd-d051b7657205", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181072153600, "endTime": 155181076952700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "7e8a499c-7996-4343-92cb-62044a622a5d", "logId": "a5e50b19-4fcf-48f5-97a2-ef88df010a8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e8a499c-7996-4343-92cb-62044a622a5d", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181057914100}, "additional": {"logType": "detail", "children": [], "durationId": "d1d9a529-177d-4c75-8ffd-d051b7657205"}}, {"head": {"id": "cefd3742-3347-4ef9-be96-5d26d7176259", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181060262200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76f16733-e07c-45cc-96ed-cd9467f31df0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181060470800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d58e6c8-ddbd-4735-95cb-383548872587", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181072184900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14a4988f-2f25-4de8-8674-5aa8930ac628", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181075992400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d30ecfaf-b3d8-4ce4-8b66-fffbfe092ad6", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181076219400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8395f8a1-9286-4b46-8145-cd301e122326", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181076381000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "781fefd2-bdbb-466b-a574-754403e8d7c4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181076502500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a65fb81-a730-4663-acaf-ff876bb79aed", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12033843994140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181076682200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02160089-4daa-4ac6-8a8f-6b610423c994", "name": "runTaskFromQueue task cost before running: 500 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181076840500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5e50b19-4fcf-48f5-97a2-ef88df010a8c", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181072153600, "endTime": 155181076952700, "totalTime": 4661600}, "additional": {"logType": "info", "children": [], "durationId": "d1d9a529-177d-4c75-8ffd-d051b7657205"}}, {"head": {"id": "7584a47c-7208-47cc-991f-a67cd111a5b8", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181085166900, "endTime": 155181085934200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ef016d56-f3bf-4000-9f3b-5a12849c1d1c", "logId": "03635c7f-a315-48d1-8559-7593b00e5a0e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef016d56-f3bf-4000-9f3b-5a12849c1d1c", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181081122200}, "additional": {"logType": "detail", "children": [], "durationId": "7584a47c-7208-47cc-991f-a67cd111a5b8"}}, {"head": {"id": "0b0157c4-58c5-419d-9338-73d19eec2236", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181083452400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7d74c32-8597-4917-b201-99bb3422d834", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181083636300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06a5e923-1955-44ff-93c3-a38772e48f45", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181085184300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33a2da00-dce7-4932-80a9-0eea034817ab", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181085402200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce95094f-8cde-4f35-a657-25131dd9a82c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181085527100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37aba172-853c-45d7-b3f1-9f972fa45aed", "name": "entry : default@BuildNativeWithCmake cost memory 0.03829193115234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181085673800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc498497-531f-4f93-9e2e-8e21e6eab2c3", "name": "runTaskFromQueue task cost before running: 509 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181085825600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03635c7f-a315-48d1-8559-7593b00e5a0e", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181085166900, "endTime": 155181085934200, "totalTime": 627800}, "additional": {"logType": "info", "children": [], "durationId": "7584a47c-7208-47cc-991f-a67cd111a5b8"}}, {"head": {"id": "5d239d7e-d2f3-4ee7-a2d7-22ab445265de", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181093394000, "endTime": 155181101812700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d84cac4e-3b69-44d8-824c-6a1b164e9a8b", "logId": "11534b16-8d95-4533-9ee0-d480700f089b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d84cac4e-3b69-44d8-824c-6a1b164e9a8b", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181089229100}, "additional": {"logType": "detail", "children": [], "durationId": "5d239d7e-d2f3-4ee7-a2d7-22ab445265de"}}, {"head": {"id": "432a07d8-6a44-4940-8c9f-c24b2da78ffd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181091622300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a222abb7-6e12-46d5-a297-10804999be69", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181091818700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72075d4c-70e2-43bb-a8c4-d675bba6d6af", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181093410200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6234fcd8-ca49-49b7-9118-cbf1a77edb7d", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181101443400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e88f0a9-984c-4b27-aa2a-ca8bf7d567d9", "name": "entry : default@MakePackInfo cost memory 0.16371917724609375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181101661600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11534b16-8d95-4533-9ee0-d480700f089b", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181093394000, "endTime": 155181101812700}, "additional": {"logType": "info", "children": [], "durationId": "5d239d7e-d2f3-4ee7-a2d7-22ab445265de"}}, {"head": {"id": "99393578-074f-4a81-bbb7-e9ce5fbfaac3", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181111312600, "endTime": 155181119501300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4e31f77a-d31a-4af6-9f53-186a9bf63867", "logId": "4a3234fe-e8f8-443d-b8db-83020dda769e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e31f77a-d31a-4af6-9f53-186a9bf63867", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181106295800}, "additional": {"logType": "detail", "children": [], "durationId": "99393578-074f-4a81-bbb7-e9ce5fbfaac3"}}, {"head": {"id": "ecd75eeb-f31d-4e36-a9f5-1fb81710074f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181108619200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ede371e8-c849-4391-aacc-350ffae51bb8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181108814000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdb03a79-71fc-4651-a65b-67f43dac78da", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181111329600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "860f9897-6677-4561-b476-8c683720a710", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181111724800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccc5e2d2-884e-4b40-9a5e-d1aff2a84f2c", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181113444700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0659d0c3-9395-4479-9658-daefd68b5fd3", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181119116200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2a5a085-2475-411c-9c2b-3a689f3a930c", "name": "entry : default@SyscapTransform cost memory 0.15030670166015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181119347900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a3234fe-e8f8-443d-b8db-83020dda769e", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181111312600, "endTime": 155181119501300}, "additional": {"logType": "info", "children": [], "durationId": "99393578-074f-4a81-bbb7-e9ce5fbfaac3"}}, {"head": {"id": "0055cc0e-934e-4604-a8a3-387959b2101f", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181128108600, "endTime": 155181132161600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "e0d12ede-e56c-4d1b-9f3e-ef54bc41e011", "logId": "b64c201d-f96d-46e8-9cf0-89eaa2949643"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0d12ede-e56c-4d1b-9f3e-ef54bc41e011", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181122861700}, "additional": {"logType": "detail", "children": [], "durationId": "0055cc0e-934e-4604-a8a3-387959b2101f"}}, {"head": {"id": "ee6c5713-66cb-4665-a428-37e6365189e6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181125375000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "690c0232-cfad-443f-89e7-dc8527e4dac7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181125592700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0e800b5-d0db-46d6-baca-df463d920bbf", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181128128600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8225a45-c646-4b4e-8acd-203483bf0b7f", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181131770000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af60a0cc-48ad-4211-b066-b568d141a1b1", "name": "entry : default@ProcessProfile cost memory 0.12457275390625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181132019900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b64c201d-f96d-46e8-9cf0-89eaa2949643", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181128108600, "endTime": 155181132161600}, "additional": {"logType": "info", "children": [], "durationId": "0055cc0e-934e-4604-a8a3-387959b2101f"}}, {"head": {"id": "e1196135-d6bb-4934-ac3b-fe46e031a664", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181141810400, "endTime": 155181154009000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6bba693c-75be-4c8f-83df-b875868bd2e6", "logId": "7d11ea4b-baec-43e2-b1b5-93a6474a19b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bba693c-75be-4c8f-83df-b875868bd2e6", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181135284400}, "additional": {"logType": "detail", "children": [], "durationId": "e1196135-d6bb-4934-ac3b-fe46e031a664"}}, {"head": {"id": "c9b8d5d4-fac2-46d8-99a5-294601babddc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181137654600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5465ee0-b8c2-477f-a593-88e01b40cb45", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181137848900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "004f2593-d1e8-4549-b79a-426c4ff57292", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181141831700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a7f0057-0d22-4f09-a3fc-0344b5802a7a", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181153781600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c201bf4-0f54-4568-8831-09a138904f6a", "name": "entry : default@ProcessRouterMap cost memory 0.2330780029296875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181153942000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d11ea4b-baec-43e2-b1b5-93a6474a19b6", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181141810400, "endTime": 155181154009000}, "additional": {"logType": "info", "children": [], "durationId": "e1196135-d6bb-4934-ac3b-fe46e031a664"}}, {"head": {"id": "22599415-af17-4052-bec6-5d10083d6df4", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181160783300, "endTime": 155181172382900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "25087bf5-059b-402f-9db0-72f711ac514e", "logId": "1fec0896-5aba-4270-b23a-18bd3567aaee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25087bf5-059b-402f-9db0-72f711ac514e", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181158149000}, "additional": {"logType": "detail", "children": [], "durationId": "22599415-af17-4052-bec6-5d10083d6df4"}}, {"head": {"id": "f22e7185-ba96-46b7-8fe3-196d9abe694f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181160406700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26b5e76c-e9fa-4b75-9856-bd830bfc64b2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181160602700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58c01dee-3b3d-4f6f-850b-610ced1d3d34", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181160797700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e45d5e8c-6b48-421e-855f-f84fa55f176c", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181161012500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddd85005-6196-4073-ad0b-2739bc27e6fb", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181169151400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d31bdc7-73a9-4776-9870-ec09075fe605", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181169401700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afb4ef56-242c-4364-a3fd-7f9ec06e69bb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181169563900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdcba6e7-a550-4113-a063-ac051f8e5363", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181169657400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38bb4f2f-48d5-405f-832e-3672380af5c6", "name": "entry : default@ProcessStartupConfig cost memory 0.25812530517578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181171953600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "666c5c30-faef-44d9-86e2-887cb7c813a8", "name": "runTaskFromQueue task cost before running: 595 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181172232400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fec0896-5aba-4270-b23a-18bd3567aaee", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181160783300, "endTime": 155181172382900, "totalTime": 11404200}, "additional": {"logType": "info", "children": [], "durationId": "22599415-af17-4052-bec6-5d10083d6df4"}}, {"head": {"id": "5959c375-a44e-4ba9-af95-06039a708cc1", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181182975700, "endTime": 155181185192900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ec0adc71-e9dd-4738-bad8-46fe1cb6082e", "logId": "724424a1-21e6-4da1-901b-8d76345e0e1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec0adc71-e9dd-4738-bad8-46fe1cb6082e", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181178175800}, "additional": {"logType": "detail", "children": [], "durationId": "5959c375-a44e-4ba9-af95-06039a708cc1"}}, {"head": {"id": "8853eca8-1eb4-47c9-898b-b9a98c24d966", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181180716800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43e00c60-9321-42ed-b86e-530a36440fdf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181180939100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6bbfac2-8686-476c-85cf-bfe478e2bec1", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181182995100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "863c1dd8-c3b7-4f23-aa8f-1e6a4321fe4e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181183254400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7ea65ed-e816-4f9b-b6cd-ce7b549534c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181183346900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "612fff87-a959-4b73-b682-b689accf583f", "name": "entry : default@BuildNativeWithNinja cost memory 0.058013916015625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181184869400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3590989c-6c2d-4b4a-84f4-0633e991c90a", "name": "runTaskFromQueue task cost before running: 608 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181185094600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "724424a1-21e6-4da1-901b-8d76345e0e1c", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181182975700, "endTime": 155181185192900, "totalTime": 2086200}, "additional": {"logType": "info", "children": [], "durationId": "5959c375-a44e-4ba9-af95-06039a708cc1"}}, {"head": {"id": "10b11fc7-f115-47ff-84c4-bc209de9872e", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181197553400, "endTime": 155181207963100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "a72841fe-661c-48cb-a518-654f340c7cd6", "logId": "5f5be6c7-83ea-4186-96f9-575ac88cb774"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a72841fe-661c-48cb-a518-654f340c7cd6", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181190352800}, "additional": {"logType": "detail", "children": [], "durationId": "10b11fc7-f115-47ff-84c4-bc209de9872e"}}, {"head": {"id": "f87ae75f-15f2-404f-81c8-e94a9968d2d6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181192773200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "000163ce-2d22-4a2c-9f8d-df17b8a3e91c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181192967400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e51e04b-0aab-4d0a-a32c-757709630a8b", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181195699400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c87b28f0-0257-4dbf-92fb-e701b2967873", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181199850800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50073cd0-34f3-4fbc-919a-218babeaf3ca", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181203820100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb4674d4-6f3b-4e32-b62b-1445b491056a", "name": "entry : default@ProcessResource cost memory 0.16167449951171875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181204054200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f5be6c7-83ea-4186-96f9-575ac88cb774", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181197553400, "endTime": 155181207963100}, "additional": {"logType": "info", "children": [], "durationId": "10b11fc7-f115-47ff-84c4-bc209de9872e"}}, {"head": {"id": "be2e3910-c5f5-461b-b3a6-1c60cc274fd6", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181222393600, "endTime": 155181261054600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e25bd447-b867-4922-b5ae-1b88267d7537", "logId": "14ed2a9c-b6ec-4e03-accf-3ea9d5605a8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e25bd447-b867-4922-b5ae-1b88267d7537", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181214110900}, "additional": {"logType": "detail", "children": [], "durationId": "be2e3910-c5f5-461b-b3a6-1c60cc274fd6"}}, {"head": {"id": "308076cd-4e7d-40fa-9baf-517b11cca8ac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181215163000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17153c7f-05ba-42f1-865c-a87b4d324308", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181215349200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c15cb69-2f8b-4a0b-874c-81bcdca25eed", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181222414500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97d9b857-c1b0-4f30-bd9a-92c141b6fed2", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181260634700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0dd2817-95dc-4b64-ac31-9986dc1b7370", "name": "entry : default@GenerateLoaderJson cost memory -8.279083251953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181260830400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14ed2a9c-b6ec-4e03-accf-3ea9d5605a8d", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181222393600, "endTime": 155181261054600}, "additional": {"logType": "info", "children": [], "durationId": "be2e3910-c5f5-461b-b3a6-1c60cc274fd6"}}, {"head": {"id": "99619354-9b12-45c2-8953-fdedd2bd9fc8", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181275510900, "endTime": 155181282300600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "35d1d506-5aa8-42be-a441-4ab49f63815a", "logId": "64f9e283-c5c7-43a3-bd6c-2d2f31ed6362"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35d1d506-5aa8-42be-a441-4ab49f63815a", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181272983900}, "additional": {"logType": "detail", "children": [], "durationId": "99619354-9b12-45c2-8953-fdedd2bd9fc8"}}, {"head": {"id": "c80ffea3-6cf0-4201-9508-def2a297b414", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181274026000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "813ed549-767f-4106-99f8-6ffbb1dc5154", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181274146800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e416ba6-afe7-45f2-a6e6-b2af7013b111", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181275528200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5cacac9-6ec7-4218-af31-6f60c4bddb38", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181281991500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27d44730-4e1d-445f-af0e-72761ab3c73d", "name": "entry : default@ProcessLibs cost memory 0.14189910888671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181282194900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64f9e283-c5c7-43a3-bd6c-2d2f31ed6362", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181275510900, "endTime": 155181282300600}, "additional": {"logType": "info", "children": [], "durationId": "99619354-9b12-45c2-8953-fdedd2bd9fc8"}}, {"head": {"id": "bb9da9b1-6186-4e5e-b719-03d34b044721", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181296312600, "endTime": 155181369392800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "a71352ca-9bb7-4e44-a292-57bfc43ae21c", "logId": "91ca09ae-3878-4fd8-8a65-34cc7d482461"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a71352ca-9bb7-4e44-a292-57bfc43ae21c", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181285801000}, "additional": {"logType": "detail", "children": [], "durationId": "bb9da9b1-6186-4e5e-b719-03d34b044721"}}, {"head": {"id": "0d3cba3e-2488-4fb6-b49c-5e990635aa5b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181287960700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f5cf553-f4d6-4b02-b533-f3e5eb2fd37f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181288147400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20e88168-3fb7-4a8b-b2e4-cf98c57eab1f", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181290837600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "885692b7-0f8a-43bc-a45b-7028f77ae7af", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181296361100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b14b0d5-2d88-4657-a571-03c8be4a92ba", "name": "Incremental task entry:default@CompileResource pre-execution cost: 70 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181368989100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e2fe4b7-b23e-44f7-9cfb-57b0b9c76b26", "name": "entry : default@CompileResource cost memory 1.3149185180664062", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181369240800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91ca09ae-3878-4fd8-8a65-34cc7d482461", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181296312600, "endTime": 155181369392800}, "additional": {"logType": "info", "children": [], "durationId": "bb9da9b1-6186-4e5e-b719-03d34b044721"}}, {"head": {"id": "bf4822c4-537e-456d-87ed-752b57953854", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181381499800, "endTime": 155181384727600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "9615d376-f904-461e-ab71-66c9694aaa0e", "logId": "2caadd50-c2ff-4243-b808-7139a0c93d7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9615d376-f904-461e-ab71-66c9694aaa0e", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181374227700}, "additional": {"logType": "detail", "children": [], "durationId": "bf4822c4-537e-456d-87ed-752b57953854"}}, {"head": {"id": "e1ec6e3e-0808-4928-ab1f-a3a06e807f7b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181376286100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f57d225a-81ee-4a89-80ed-48ce2467e3d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181376501600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7816584-9547-4f2c-9308-179f389d79df", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181381511000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a9ce04b-238b-4bfd-b1cb-b525849d3b96", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181382039700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b90b078-14ef-47b8-bb2f-d7b8936dedab", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181384449600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f8e9ed0-cdb6-43b8-8605-e8d537f014ee", "name": "entry : default@DoNativeStrip cost memory 0.07921600341796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181384623700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2caadd50-c2ff-4243-b808-7139a0c93d7a", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181381499800, "endTime": 155181384727600}, "additional": {"logType": "info", "children": [], "durationId": "bf4822c4-537e-456d-87ed-752b57953854"}}, {"head": {"id": "9cd9cba9-0ab5-48b1-a10d-f7210f29edbc", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181395314500, "endTime": 155181465218000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "068fa3a7-1272-4a03-a8e0-141b79d07059", "logId": "97e31666-34d4-4015-9ce4-4efbf46b246b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "068fa3a7-1272-4a03-a8e0-141b79d07059", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181386960000}, "additional": {"logType": "detail", "children": [], "durationId": "9cd9cba9-0ab5-48b1-a10d-f7210f29edbc"}}, {"head": {"id": "fe3b7215-eb29-4bbe-b4de-75605dd9144e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181388545200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fb2a3d5-966a-40dd-a200-e31c18492fcd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181388706400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93e63bcc-f6d6-48c4-a989-ea727ee23a74", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181395330900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "114fbab1-89fa-4f6a-93cd-fc9938b7d2d6", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181395571400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "255b82f9-eda2-4c70-bed9-ee277320b2d0", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 55 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181464777700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b89793c6-8337-4e1e-8f20-9f2ff549c303", "name": "entry : default@CompileArkTS cost memory 1.1677703857421875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181464980300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97e31666-34d4-4015-9ce4-4efbf46b246b", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181395314500, "endTime": 155181465218000}, "additional": {"logType": "info", "children": [], "durationId": "9cd9cba9-0ab5-48b1-a10d-f7210f29edbc"}}, {"head": {"id": "ea668ed5-2cf7-41d0-820a-f43fde714b17", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181488943200, "endTime": 155181504628100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "2f58b974-7cab-407d-9591-0237a8bd6714", "logId": "8886b25d-f3be-4b96-a6fa-b62e64329547"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f58b974-7cab-407d-9591-0237a8bd6714", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181478228200}, "additional": {"logType": "detail", "children": [], "durationId": "ea668ed5-2cf7-41d0-820a-f43fde714b17"}}, {"head": {"id": "098ea1a5-7b6e-4903-a820-a67e3c4d285a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181480256600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40f3b1ee-6d75-4beb-b5b2-155bb7c9ed37", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181480409900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c27d373a-0d1e-4cc4-9574-e9d3dad7344d", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181488960700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4aca07a-13a7-47c9-a4e5-dcd9c75e27f4", "name": "entry : default@BuildJS cost memory 0.33904266357421875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181504287900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df2cfbac-1d61-4b08-9999-46927fe2a9c0", "name": "runTaskFromQueue task cost before running: 927 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181504510900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8886b25d-f3be-4b96-a6fa-b62e64329547", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181488943200, "endTime": 155181504628100, "totalTime": 15534300}, "additional": {"logType": "info", "children": [], "durationId": "ea668ed5-2cf7-41d0-820a-f43fde714b17"}}, {"head": {"id": "20795804-0e2d-4e32-9f84-826c012e823f", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181514029400, "endTime": 155181521377600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "14c846e6-730b-4fa9-93d2-862e4ac71c89", "logId": "f037b58e-d71f-473e-9537-3bb5e2be6c55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14c846e6-730b-4fa9-93d2-862e4ac71c89", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181507125800}, "additional": {"logType": "detail", "children": [], "durationId": "20795804-0e2d-4e32-9f84-826c012e823f"}}, {"head": {"id": "e01e8b85-69b3-4a47-9ad0-c426d36f9ff1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181509114700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ece175d-2852-482f-b308-1d2fcc096d78", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181509295200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ac0d3f3-833f-4a20-8006-dbf9c1729b02", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181514044000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b12a53cf-d18f-4c7b-af47-d32e05749f23", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181515817700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38955d0b-feb8-414f-9d50-0ff3f89f4244", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181521036100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd2050a6-7ed9-4c8e-b788-b01eb2c27df3", "name": "entry : default@CacheNativeLibs cost memory 0.0951995849609375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181521244900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f037b58e-d71f-473e-9537-3bb5e2be6c55", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181514029400, "endTime": 155181521377600}, "additional": {"logType": "info", "children": [], "durationId": "20795804-0e2d-4e32-9f84-826c012e823f"}}, {"head": {"id": "7559f52d-8750-4c52-84eb-22d436908435", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181529518100, "endTime": 155181532925600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "12f01ec9-e737-4854-8000-242ce11deb8b", "logId": "4f3a03e8-3c3e-4fb5-8637-05ef9b4cf74b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12f01ec9-e737-4854-8000-242ce11deb8b", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181524916400}, "additional": {"logType": "detail", "children": [], "durationId": "7559f52d-8750-4c52-84eb-22d436908435"}}, {"head": {"id": "80e0bc2a-1342-4495-ba20-96c0b0668ef4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181527154100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd7fee49-7a69-4611-a60a-5a014edc8ee5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181527331600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7548d009-e2a3-458b-b5d5-add5b26763ad", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181529536000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bca540a-eaee-4227-a0ad-c1370cf0da37", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181530202100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75c7ae4e-7de4-4a7b-bc37-3fe41dc2f325", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181532608900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad4026e0-3d93-4418-b12e-e4fbc6f716ed", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07549285888671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181532809300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f3a03e8-3c3e-4fb5-8637-05ef9b4cf74b", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181529518100, "endTime": 155181532925600}, "additional": {"logType": "info", "children": [], "durationId": "7559f52d-8750-4c52-84eb-22d436908435"}}, {"head": {"id": "5aaefd4b-2c2e-48ce-affb-7d2a5e1968a5", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181555774500, "endTime": 155181599652400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "95ccf331-0947-4dc5-b145-dc4b2a81d52a", "logId": "c7d5fb4f-e8aa-43b0-b285-88513f584617"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95ccf331-0947-4dc5-b145-dc4b2a81d52a", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181537055400}, "additional": {"logType": "detail", "children": [], "durationId": "5aaefd4b-2c2e-48ce-affb-7d2a5e1968a5"}}, {"head": {"id": "6ce76cb5-8387-4200-84a0-40f1fc0f7710", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181539200400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d32b513-828c-4caf-a306-b26a59a9448e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181539382600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89480157-d0ae-4449-96c8-86ed6920b39b", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181555790600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "745ef975-f64d-45c0-9dbc-deed3b16d201", "name": "Incremental task entry:default@PackageHap pre-execution cost: 39 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181599255600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ed195f9-d033-44bd-8cea-9a09ff6bc864", "name": "entry : default@PackageHap cost memory 0.942626953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181599542100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7d5fb4f-e8aa-43b0-b285-88513f584617", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181555774500, "endTime": 155181599652400}, "additional": {"logType": "info", "children": [], "durationId": "5aaefd4b-2c2e-48ce-affb-7d2a5e1968a5"}}, {"head": {"id": "3be36480-43f0-4f01-beff-70f3b6263238", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181618559400, "endTime": 155181624397500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": [], "detailId": "e02e82e9-c1ec-4d23-9e37-c90574620092", "logId": "752fdc96-0ff9-48e2-b0d1-faccd45ee832"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e02e82e9-c1ec-4d23-9e37-c90574620092", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181608577200}, "additional": {"logType": "detail", "children": [], "durationId": "3be36480-43f0-4f01-beff-70f3b6263238"}}, {"head": {"id": "83ba32fa-5da2-4147-8aeb-c093fae9d2de", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181611846000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "843c6818-20ad-4876-bb78-9c7451a4414f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181612041800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e7bc721-53df-4a48-a2dc-6d99d7eef76c", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181618579800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7876f23c-a6ba-498c-b04a-dfc4164d0f86", "name": "Incremental task entry:default@SignHap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181624057900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fa6aa84-6285-4b8e-9944-d17de906f1c6", "name": "entry : default@SignHap cost memory 0.10527801513671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181624275300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "752fdc96-0ff9-48e2-b0d1-faccd45ee832", "name": "UP-TO-DATE :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181618559400, "endTime": 155181624397500}, "additional": {"logType": "info", "children": [], "durationId": "3be36480-43f0-4f01-beff-70f3b6263238"}}, {"head": {"id": "3e402a9f-741b-4cd2-8e65-81a3078ea13f", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181632350700, "endTime": 155181645747100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3ae625a6-0d35-4388-beb9-916117906ade", "logId": "51409b60-fc14-4a56-b3e8-476355e09ca4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ae625a6-0d35-4388-beb9-916117906ade", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181628440400}, "additional": {"logType": "detail", "children": [], "durationId": "3e402a9f-741b-4cd2-8e65-81a3078ea13f"}}, {"head": {"id": "fe9224ce-9c5f-492e-9bda-87f8d6677055", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181630663100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80dae441-3a95-4e4f-99e5-a7fbe042f89a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181630847900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6480430-03fc-462e-9d53-62a414fd6f0c", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181632364700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd6c5bd3-b5eb-4487-b3d9-cd96d6232482", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181645001600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "337a74fe-c660-4c05-a3cd-bdbc35d67298", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181645194900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d67fd340-74db-4dc8-a4d0-6b7c33a6a195", "name": "entry : default@CollectDebugSymbol cost memory 0.2418212890625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181645330200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db6a06cf-9fde-450b-a1d9-433a2452c881", "name": "runTaskFromQueue task cost before running: 1 s 68 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181645520200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51409b60-fc14-4a56-b3e8-476355e09ca4", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181632350700, "endTime": 155181645747100, "totalTime": 13086800}, "additional": {"logType": "info", "children": [], "durationId": "3e402a9f-741b-4cd2-8e65-81a3078ea13f"}}, {"head": {"id": "2e43ae59-1323-4b67-8e9b-73fa5771bb42", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181648027800, "endTime": 155181648890100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "29a5d1ce-40eb-47bc-9a4c-8689416c35ea", "logId": "64f1d31b-e39e-48c6-8b5d-57bb0679fa13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29a5d1ce-40eb-47bc-9a4c-8689416c35ea", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181647991000}, "additional": {"logType": "detail", "children": [], "durationId": "2e43ae59-1323-4b67-8e9b-73fa5771bb42"}}, {"head": {"id": "8efa361a-e9a2-437b-afd1-59677a256208", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181648031300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "833a78fe-7b2c-4688-afc6-b00dc6474725", "name": "entry : assembleHap cost memory 0.0113983154296875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181648258800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7e30285-d947-4af6-9687-44efa1fdc57e", "name": "runTaskFromQueue task cost before running: 1 s 71 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181648569100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64f1d31b-e39e-48c6-8b5d-57bb0679fa13", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181648027800, "endTime": 155181648890100, "totalTime": 505400}, "additional": {"logType": "info", "children": [], "durationId": "2e43ae59-1323-4b67-8e9b-73fa5771bb42"}}, {"head": {"id": "3a49d67f-fb18-419c-b6dc-b6185278b3fc", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181667274100, "endTime": 155181667321200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf3a0719-5b19-461d-813e-66aa5841970a", "logId": "f6e225ca-75c5-413c-bf6a-f8ab59af5977"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6e225ca-75c5-413c-bf6a-f8ab59af5977", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181667274100, "endTime": 155181667321200}, "additional": {"logType": "info", "children": [], "durationId": "3a49d67f-fb18-419c-b6dc-b6185278b3fc"}}, {"head": {"id": "f814029b-9276-4868-9ef7-98fabc231cbd", "name": "BUILD SUCCESSFUL in 1 s 90 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181667392800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "e1c4c9ca-ee66-4480-9623-c9006f53ca48", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155180577763400, "endTime": 155181667810400}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 1, "minute": 3, "second": 31}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "3422a2de-90dc-4534-b1b7-3cbea7abc9c1", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181667837300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13e7f456-bb03-4e66-b6a5-fbc30488cb52", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181667925200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cf9d691-54b4-46eb-8d81-d116cf63a009", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181668533500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ac4cecb-9e18-4937-89d9-eda5fea1e140", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181668655700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43bbe3d3-c0c3-4db7-880b-30cb4392aabf", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181668756100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3783ed42-4db7-429a-a3ac-e726fd8924a6", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181668850500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ad26898-a787-4206-8e6f-fa9a3b5a4a70", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181668937200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f660475-ab9d-4fe8-8f53-7bc77f09a16c", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181670139600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b33971fa-88d9-43ab-b8c8-8f009d964219", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181670502300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "324e8349-4977-4453-94e3-19c8f6734b35", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181670613200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49873298-7309-44f7-83a9-4c1e05c2d68d", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181670697200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "521e5f6c-05b7-4a15-a9f8-fbe43c6288df", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181670772300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b4606b6-6b65-4d10-98a9-4b9efc39a1dd", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181670848900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f92242f3-93d6-49f9-9386-eb558e4e9faa", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181673181100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1007f8b7-3daf-401e-9988-c8f3ed078103", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181673740300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d155a1c7-8c36-47a2-a7b7-8c04a1c8c17f", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181674091200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7f95f1b-3075-4574-be18-ffc618fc470c", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181674200300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a99f4379-27ae-4480-a3a4-3d77fe4ffbae", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181674282400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5011c6e0-2884-4ba3-9d5d-95e8ad1dcce5", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181674354400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f1a6a1e-2d9d-46de-ace8-e6e655a13c54", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181674426000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2f5807b-8427-4de4-b487-9f4041170691", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181674493700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6151c40b-8ffe-436a-ade3-336bb637e234", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181674555400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06942f0d-90cc-4161-b095-aeeb50b6be7c", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181679084200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f42405f6-c4ba-4723-97ac-5ccfc04e99f2", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181680534800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61246b52-a7bf-428a-97f9-3298ed9ea362", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181681190100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1a42b79-1176-4ba0-9244-756f1d84621a", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181681510900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf5f377d-9060-4531-ac56-8b5d9484cafa", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181681873000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e020ca4b-deb0-4d28-bd2e-7ccf29947ad1", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181683913700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee05f6ef-8b21-4ea8-92ec-b1ea79d0d336", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181685740600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20111dd0-a9d1-423e-a3a8-5de234600e6b", "name": "Incremental task entry:default@BuildJS post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181686192200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a4e354b-e291-499a-98b9-a924216afa4a", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181686333900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2da2f342-b573-4ea1-9c87-656fe4a7106f", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181686424500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8be11175-1c18-495f-b316-606b5160aaf6", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181686504000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1e2c0ee-7150-4209-bcf0-80ed0b6c6ce4", "name": "There is no need to refresh cache, since the incremental task entry:default@SignHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181686576500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70afbf84-65cc-4551-bafd-3f859c169cf1", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181692422000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "148a6984-0b01-483b-922d-f1d1804d13c2", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181693055800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04565f0e-27c0-4a0e-953f-c6958dcf6650", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181694185600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92ea4169-a85d-4a5a-b3f0-ce9314810f90", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 155181694629000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}