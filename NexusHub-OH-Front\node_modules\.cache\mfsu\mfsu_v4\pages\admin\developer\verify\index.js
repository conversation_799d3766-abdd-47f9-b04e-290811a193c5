"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { useState, useEffect } from "react";
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  message,
  Tag,
  Space,
  Image,
  Descriptions,
  Radio,
  Typography,
  Divider,
  Row,
  Col
} from "antd";
import { EyeOutlined, CheckOutlined, CloseOutlined, SearchOutlined } from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-components";
import { getAdminDevelopersVerify, postAdminDevelopersIdVerify } from "@/services/ant-design-pro/kaifazhe";
import { getSearchUsers } from "@/services/ant-design-pro/sousuo";
import dayjs from "dayjs";
import styles from "./index.less";
const { TextArea } = Input;
const { Title, Paragraph } = Typography;
const DeveloperVerifyAdmin = () => {
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [statusFilter, setStatusFilter] = useState("all");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [verifyModalVisible, setVerifyModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState(null);
  const fetchDeveloperList = async () => {
    console.log("fetchDeveloperList called with:", {
      currentPage,
      pageSize,
      statusFilter,
      searchKeyword
    });
    setLoading(true);
    try {
      let response;
      if (searchKeyword.trim()) {
        console.log("=== \u4F7F\u7528\u641C\u7D22API ===");
        console.log("\u641C\u7D22\u53C2\u6570:", {
          keyword: searchKeyword,
          is_developer: true,
          verify_status: statusFilter === "all" ? void 0 : statusFilter,
          page: currentPage.toString(),
          page_size: pageSize.toString()
        });
        response = await getSearchUsers({
          keyword: searchKeyword,
          is_developer: true,
          verify_status: statusFilter === "all" ? void 0 : statusFilter,
          page: currentPage.toString(),
          page_size: pageSize.toString()
        });
        console.log("\u641C\u7D22API\u54CD\u5E94:", response);
        if (response.code === 200) {
          const convertedData = response.data?.users?.map((user) => {
            console.log("\u8F6C\u6362\u7528\u6237\u6570\u636E:", user);
            return {
              id: user.id,
              username: user.username || "",
              developer_name: user.developer_name || user.username || "",
              company_name: user.company_name || "",
              contact_email: user.contact_email || user.email || "",
              contact_phone: user.contact_phone || user.phone || "",
              website: user.website || "",
              description: user.description || "",
              developer_address: user.developer_address || "",
              developer_avatar: user.developer_avatar || user.avatar || "",
              business_license: user.business_license || "",
              identity_card: user.identity_card || "",
              submitted_at: user.submitted_at || user.created_at || "",
              verified_at: user.verified_at || "",
              verify_reason: user.verify_reason || "",
              verify_status: user.verify_status || "pending"
            };
          }) || [];
          console.log("\u8F6C\u6362\u540E\u7684\u6570\u636E:", convertedData);
          setDataSource(convertedData);
          setTotal(response.data?.total || 0);
        } else {
          console.error("\u641C\u7D22\u5931\u8D25:", response.message);
          message.error(response.message || "\u641C\u7D22\u5931\u8D25");
          setDataSource([]);
          setTotal(0);
        }
      } else {
        console.log("=== \u4F7F\u7528\u5E38\u89C4API ===");
        console.log("\u5E38\u89C4API\u53C2\u6570:", {
          page: currentPage,
          page_size: pageSize,
          status: statusFilter === "all" ? void 0 : statusFilter
        });
        response = await getAdminDevelopersVerify({
          page: currentPage.toString(),
          page_size: pageSize.toString(),
          status: statusFilter === "all" ? void 0 : statusFilter
        });
        console.log("\u5E38\u89C4API\u54CD\u5E94:", response);
        if (response.code === 200) {
          console.log("API response data:", response.data);
          console.log("API response total:", response.total);
          const processedData = (response.data || []).map((item) => ({
            ...item,
            username: item.username || "",
            developer_name: item.developer_name || item.username || "",
            company_name: item.company_name || "",
            contact_email: item.contact_email || "",
            contact_phone: item.contact_phone || "",
            website: item.website || "",
            description: item.description || "",
            developer_address: item.developer_address || "",
            developer_avatar: item.developer_avatar || "",
            business_license: item.business_license || "",
            identity_card: item.identity_card || "",
            submitted_at: item.submitted_at || "",
            verified_at: item.verified_at || "",
            verify_reason: item.verify_reason || "",
            verify_status: item.verify_status || "pending"
          }));
          console.log("Processed data:", processedData);
          console.log("Processed data length:", processedData.length);
          setDataSource(processedData);
          setTotal(response.total || 0);
        } else {
          message.error(response.message || "\u83B7\u53D6\u6570\u636E\u5931\u8D25");
        }
      }
    } catch (error) {
      console.error("\u83B7\u53D6\u5F00\u53D1\u8005\u8BA4\u8BC1\u5217\u8868\u5931\u8D25:", error);
      message.error("\u83B7\u53D6\u6570\u636E\u5931\u8D25");
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    console.log("=== DEVELOPER VERIFY PAGE useEffect triggered ===");
    console.log("Dependencies:", { currentPage, pageSize, statusFilter, searchKeyword });
    fetchDeveloperList();
  }, [currentPage, pageSize, statusFilter, searchKeyword]);
  useEffect(() => {
    console.log("=== DataSource updated ===");
    console.log("Current dataSource:", dataSource);
    console.log("DataSource length:", dataSource.length);
  }, [dataSource]);
  const handleViewDetail = (record) => {
    setCurrentRecord(record);
    setDetailModalVisible(true);
  };
  const handleOpenVerify = (record) => {
    setCurrentRecord(record);
    form.resetFields();
    setVerifyModalVisible(true);
  };
  const handleSubmitVerify = async () => {
    if (!currentRecord) return;
    try {
      const values = await form.validateFields();
      const response = await postAdminDevelopersIdVerify(
        { id: currentRecord.id.toString() },
        {
          verify_status: values.verify_status,
          verify_reason: values.verify_reason || ""
        }
      );
      if (response.code === 200) {
        message.success("\u5BA1\u6838\u5B8C\u6210");
        setVerifyModalVisible(false);
        fetchDeveloperList();
      } else {
        message.error(response.message || "\u5BA1\u6838\u5931\u8D25");
      }
    } catch (error) {
      console.error("\u5BA1\u6838\u5931\u8D25:", error);
      message.error("\u5BA1\u6838\u5931\u8D25");
    }
  };
  const renderStatusTag = (status) => {
    const statusMap = {
      pending: { color: "orange", text: "\u5F85\u5BA1\u6838" },
      approved: { color: "green", text: "\u5DF2\u901A\u8FC7" },
      rejected: { color: "red", text: "\u5DF2\u62D2\u7EDD" }
    };
    const config = statusMap[status] || { color: "default", text: status };
    return /* @__PURE__ */ jsx(Tag, { color: config.color, children: config.text });
  };
  const columns = [
    {
      title: "\u7528\u6237\u540D",
      dataIndex: "username",
      key: "username",
      width: 120
    },
    {
      title: "\u5F00\u53D1\u8005\u59D3\u540D",
      dataIndex: "developer_name",
      key: "developer_name",
      width: 120
    },
    {
      title: "\u516C\u53F8\u540D\u79F0",
      dataIndex: "company_name",
      key: "company_name",
      width: 150,
      render: (text) => text || "-"
    },
    {
      title: "\u8054\u7CFB\u90AE\u7BB1",
      dataIndex: "contact_email",
      key: "contact_email",
      width: 200
    },
    {
      title: "\u8054\u7CFB\u7535\u8BDD",
      dataIndex: "contact_phone",
      key: "contact_phone",
      width: 120
    },
    {
      title: "\u72B6\u6001",
      dataIndex: "verify_status",
      key: "verify_status",
      width: 100,
      render: renderStatusTag
    },
    {
      title: "\u63D0\u4EA4\u65F6\u95F4",
      dataIndex: "submitted_at",
      key: "submitted_at",
      width: 150,
      render: (text) => dayjs(text).format("YYYY-MM-DD HH:mm")
    },
    {
      title: "\u64CD\u4F5C",
      key: "action",
      width: 200,
      fixed: "right",
      render: (_, record) => /* @__PURE__ */ jsxs(Space, { children: [
        /* @__PURE__ */ jsx(
          Button,
          {
            type: "link",
            icon: /* @__PURE__ */ jsx(EyeOutlined, {}),
            onClick: () => handleViewDetail(record),
            children: "\u67E5\u770B\u8BE6\u60C5"
          }
        ),
        record.verify_status === "pending" && /* @__PURE__ */ jsx(
          Button,
          {
            type: "primary",
            size: "small",
            onClick: () => handleOpenVerify(record),
            children: "\u5BA1\u6838"
          }
        )
      ] })
    }
  ];
  return /* @__PURE__ */ jsxs(
    PageContainer,
    {
      title: "\u5F00\u53D1\u8005\u8BA4\u8BC1\u5BA1\u6838",
      subTitle: "\u7BA1\u7406\u548C\u5BA1\u6838\u5F00\u53D1\u8005\u8BA4\u8BC1\u7533\u8BF7",
      children: [
        /* @__PURE__ */ jsxs(Card, { children: [
          /* @__PURE__ */ jsx("div", { className: styles.filterBar, children: /* @__PURE__ */ jsxs(Space, { direction: "vertical", style: { width: "100%" }, children: [
            /* @__PURE__ */ jsxs(
              Form,
              {
                form: searchForm,
                layout: "inline",
                onFinish: (values) => {
                  setSearchKeyword(values.keyword || "");
                  setCurrentPage(1);
                },
                children: [
                  /* @__PURE__ */ jsx(Form.Item, { name: "keyword", children: /* @__PURE__ */ jsx(
                    Input,
                    {
                      placeholder: "\u641C\u7D22\u5F00\u53D1\u8005\u59D3\u540D\u3001\u7528\u6237\u540D\u3001\u516C\u53F8\u540D\u79F0\u6216\u90AE\u7BB1",
                      prefix: /* @__PURE__ */ jsx(SearchOutlined, {}),
                      style: { width: 300 },
                      allowClear: true
                    }
                  ) }),
                  /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsxs(Space, { children: [
                    /* @__PURE__ */ jsx(Button, { type: "primary", htmlType: "submit", icon: /* @__PURE__ */ jsx(SearchOutlined, {}), children: "\u641C\u7D22" }),
                    /* @__PURE__ */ jsx(
                      Button,
                      {
                        onClick: () => {
                          searchForm.resetFields();
                          setSearchKeyword("");
                          setCurrentPage(1);
                        },
                        children: "\u91CD\u7F6E"
                      }
                    )
                  ] }) })
                ]
              }
            ),
            /* @__PURE__ */ jsxs(Space, { children: [
              /* @__PURE__ */ jsx("span", { children: "\u72B6\u6001\u7B5B\u9009\uFF1A" }),
              /* @__PURE__ */ jsxs(
                Radio.Group,
                {
                  value: statusFilter,
                  onChange: (e) => {
                    console.log("\u72B6\u6001\u7B5B\u9009\u53D8\u66F4:", e.target.value);
                    setStatusFilter(e.target.value);
                    setCurrentPage(1);
                  },
                  children: [
                    /* @__PURE__ */ jsx(Radio.Button, { value: "all", children: "\u5168\u90E8" }),
                    /* @__PURE__ */ jsx(Radio.Button, { value: "pending", children: "\u5F85\u5BA1\u6838" }),
                    /* @__PURE__ */ jsx(Radio.Button, { value: "approved", children: "\u5DF2\u901A\u8FC7" }),
                    /* @__PURE__ */ jsx(Radio.Button, { value: "rejected", children: "\u5DF2\u62D2\u7EDD" })
                  ]
                }
              )
            ] })
          ] }) }),
          /* @__PURE__ */ jsx(
            Table,
            {
              columns,
              dataSource,
              loading,
              rowKey: "id",
              scroll: { x: 1200 },
              pagination: {
                current: currentPage,
                pageSize,
                total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total2, range) => `\u7B2C ${range[0]}-${range[1]} \u6761/\u5171 ${total2} \u6761`,
                onChange: (page, size) => {
                  setCurrentPage(page);
                  setPageSize(size || 10);
                }
              }
            }
          )
        ] }),
        /* @__PURE__ */ jsx(
          Modal,
          {
            title: "\u5F00\u53D1\u8005\u8BA4\u8BC1\u8BE6\u60C5",
            open: detailModalVisible,
            onCancel: () => setDetailModalVisible(false),
            footer: [
              /* @__PURE__ */ jsx(Button, { onClick: () => setDetailModalVisible(false), children: "\u5173\u95ED" }, "close"),
              currentRecord?.verify_status === "pending" && /* @__PURE__ */ jsx(
                Button,
                {
                  type: "primary",
                  onClick: () => {
                    setDetailModalVisible(false);
                    handleOpenVerify(currentRecord);
                  },
                  children: "\u7ACB\u5373\u5BA1\u6838"
                },
                "verify"
              )
            ],
            width: 800,
            className: styles.detailModal,
            children: currentRecord && /* @__PURE__ */ jsxs("div", { children: [
              /* @__PURE__ */ jsxs(Descriptions, { title: "\u57FA\u672C\u4FE1\u606F", column: 2, bordered: true, children: [
                /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u7528\u6237\u540D", children: currentRecord.username }),
                /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u5F00\u53D1\u8005\u59D3\u540D", children: currentRecord.developer_name }),
                /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u516C\u53F8\u540D\u79F0", children: currentRecord.company_name || "-" }),
                /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u8054\u7CFB\u90AE\u7BB1", children: currentRecord.contact_email }),
                /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u8054\u7CFB\u7535\u8BDD", children: currentRecord.contact_phone }),
                /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u4E2A\u4EBA\u7F51\u7AD9", children: currentRecord.website ? /* @__PURE__ */ jsx("a", { href: currentRecord.website, target: "_blank", rel: "noopener noreferrer", children: currentRecord.website }) : "-" }),
                /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u8054\u7CFB\u5730\u5740", span: 2, children: currentRecord.developer_address }),
                /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u4E2A\u4EBA\u7B80\u4ECB", span: 2, children: currentRecord.description }),
                /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u8BA4\u8BC1\u72B6\u6001", children: renderStatusTag(currentRecord.verify_status) }),
                /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u63D0\u4EA4\u65F6\u95F4", children: dayjs(currentRecord.submitted_at).format("YYYY-MM-DD HH:mm:ss") }),
                currentRecord.verified_at && /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u5BA1\u6838\u65F6\u95F4", children: dayjs(currentRecord.verified_at).format("YYYY-MM-DD HH:mm:ss") }),
                currentRecord.verify_reason && /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u5BA1\u6838\u610F\u89C1", span: 2, children: currentRecord.verify_reason })
              ] }),
              /* @__PURE__ */ jsx(Divider, { children: "\u8BA4\u8BC1\u6750\u6599" }),
              /* @__PURE__ */ jsxs(Row, { gutter: 24, children: [
                currentRecord.developer_avatar && /* @__PURE__ */ jsx(Col, { span: 8, children: /* @__PURE__ */ jsxs("div", { className: styles.imageItem, children: [
                  /* @__PURE__ */ jsx(Title, { level: 5, children: "\u5F00\u53D1\u8005\u5934\u50CF" }),
                  /* @__PURE__ */ jsx(
                    Image,
                    {
                      src: currentRecord.developer_avatar,
                      alt: "\u5F00\u53D1\u8005\u5934\u50CF",
                      width: 150,
                      height: 150,
                      style: { objectFit: "cover" }
                    }
                  )
                ] }) }),
                currentRecord.business_license && /* @__PURE__ */ jsx(Col, { span: 8, children: /* @__PURE__ */ jsxs("div", { className: styles.imageItem, children: [
                  /* @__PURE__ */ jsx(Title, { level: 5, children: "\u8425\u4E1A\u6267\u7167" }),
                  /* @__PURE__ */ jsx(
                    Image,
                    {
                      src: currentRecord.business_license,
                      alt: "\u8425\u4E1A\u6267\u7167",
                      width: 150,
                      height: 150,
                      style: { objectFit: "cover" }
                    }
                  )
                ] }) }),
                /* @__PURE__ */ jsx(Col, { span: 8, children: /* @__PURE__ */ jsxs("div", { className: styles.imageItem, children: [
                  /* @__PURE__ */ jsx(Title, { level: 5, children: "\u8EAB\u4EFD\u8BC1\u660E" }),
                  /* @__PURE__ */ jsx(
                    Image,
                    {
                      src: currentRecord.identity_card,
                      alt: "\u8EAB\u4EFD\u8BC1\u660E",
                      width: 150,
                      height: 150,
                      style: { objectFit: "cover" }
                    }
                  )
                ] }) })
              ] })
            ] })
          }
        ),
        /* @__PURE__ */ jsx(
          Modal,
          {
            title: "\u5BA1\u6838\u5F00\u53D1\u8005\u8BA4\u8BC1",
            open: verifyModalVisible,
            onCancel: () => setVerifyModalVisible(false),
            onOk: handleSubmitVerify,
            okText: "\u63D0\u4EA4\u5BA1\u6838",
            cancelText: "\u53D6\u6D88",
            width: 600,
            children: currentRecord && /* @__PURE__ */ jsxs("div", { children: [
              /* @__PURE__ */ jsxs(Descriptions, { column: 1, bordered: true, size: "small", style: { marginBottom: 24 }, children: [
                /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u5F00\u53D1\u8005\u59D3\u540D", children: currentRecord.developer_name }),
                /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u8054\u7CFB\u90AE\u7BB1", children: currentRecord.contact_email }),
                /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u8054\u7CFB\u7535\u8BDD", children: currentRecord.contact_phone })
              ] }),
              /* @__PURE__ */ jsxs(Form, { form, layout: "vertical", children: [
                /* @__PURE__ */ jsx(
                  Form.Item,
                  {
                    name: "verify_status",
                    label: "\u5BA1\u6838\u7ED3\u679C",
                    rules: [{ required: true, message: "\u8BF7\u9009\u62E9\u5BA1\u6838\u7ED3\u679C" }],
                    children: /* @__PURE__ */ jsxs(Radio.Group, { children: [
                      /* @__PURE__ */ jsxs(Radio, { value: "approved", children: [
                        /* @__PURE__ */ jsx(CheckOutlined, { style: { color: "#52c41a" } }),
                        " \u901A\u8FC7\u8BA4\u8BC1"
                      ] }),
                      /* @__PURE__ */ jsxs(Radio, { value: "rejected", children: [
                        /* @__PURE__ */ jsx(CloseOutlined, { style: { color: "#ff4d4f" } }),
                        " \u62D2\u7EDD\u8BA4\u8BC1"
                      ] })
                    ] })
                  }
                ),
                /* @__PURE__ */ jsx(
                  Form.Item,
                  {
                    name: "verify_reason",
                    label: "\u5BA1\u6838\u610F\u89C1",
                    rules: [
                      {
                        validator: (_, value) => {
                          const status = form.getFieldValue("verify_status");
                          if (status === "rejected" && !value) {
                            return Promise.reject(new Error("\u62D2\u7EDD\u8BA4\u8BC1\u65F6\u5FC5\u987B\u586B\u5199\u62D2\u7EDD\u7406\u7531"));
                          }
                          return Promise.resolve();
                        }
                      }
                    ],
                    children: /* @__PURE__ */ jsx(
                      TextArea,
                      {
                        rows: 4,
                        placeholder: "\u8BF7\u586B\u5199\u5BA1\u6838\u610F\u89C1\uFF08\u62D2\u7EDD\u65F6\u5FC5\u586B\uFF09",
                        maxLength: 500,
                        showCount: true
                      }
                    )
                  }
                )
              ] })
            ] })
          }
        )
      ]
    }
  );
};
export default DeveloperVerifyAdmin;
