"use strict";
import component from "./fa-IR/component";
import globalHeader from "./fa-IR/globalHeader";
import menu from "./fa-IR/menu";
import pages from "./fa-IR/pages";
import pwa from "./fa-IR/pwa";
import settingDrawer from "./fa-IR/settingDrawer";
import settings from "./fa-IR/settings";
export default {
  "navBar.lang": "\u0632\u0628\u0627\u0646 \u0647\u0627  ",
  "layout.user.link.help": "\u06A9\u0645\u06A9",
  "layout.user.link.privacy": "\u062D\u0631\u06CC\u0645 \u062E\u0635\u0648\u0635\u06CC",
  "layout.user.link.terms": "\u0645\u0642\u0631\u0631\u0627\u062A",
  "app.preview.down.block": "\u0627\u06CC\u0646 \u0635\u0641\u062D\u0647 \u0631\u0627 \u062F\u0631 \u067E\u0631\u0648\u0698\u0647 \u0645\u062D\u0644\u06CC \u062E\u0648\u062F \u0628\u0627\u0631\u06AF\u06CC\u0631\u06CC \u06A9\u0646\u06CC\u062F",
  "app.welcome.link.fetch-blocks": "\u062F\u0631\u06CC\u0627\u0641\u062A \u062A\u0645\u0627\u0645 \u0628\u0644\u0648\u06A9",
  "app.welcome.link.block-list": '\u0628\u0647 \u0633\u0631\u0639\u062A \u0635\u0641\u062D\u0627\u062A \u0627\u0633\u062A\u0627\u0646\u062F\u0627\u0631\u062F \u0645\u0628\u062A\u0646\u06CC \u0628\u0631 \u062A\u0648\u0633\u0639\u0647 "\u0628\u0644\u0648\u06A9" \u0631\u0627 \u0628\u0633\u0627\u0632\u06CC\u062F',
  ...globalHeader,
  ...menu,
  ...settingDrawer,
  ...settings,
  ...pwa,
  ...component,
  ...pages
};
