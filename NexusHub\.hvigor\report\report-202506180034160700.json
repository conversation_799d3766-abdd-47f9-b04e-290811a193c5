{"version": "2.0", "ppid": 38564, "events": [{"head": {"id": "742c8a0f-6c95-4c95-9a96-8f22e5072607", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397909338300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6716c38b-50f1-42b8-8a05-f03fe2fe8584", "name": "Current worker pool is terminated.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397909460800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a09ad6d-e782-4f7c-be63-942144822040", "name": "worker[0] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397956608600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "243a9017-3248-4979-8ece-156336d8a004", "name": "worker[1] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397957030400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "710d14b8-e0d6-408d-a1bb-ff48840c34fd", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397959018500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c34aef30-30e2-4664-9221-d6695b02eaaa", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153397959292800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "474b3e60-11bc-46e8-8dcd-6a0cde77878b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426282588700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f2a99e8-2713-49b3-a894-a6306ea2bdb1", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426298973200, "endTime": 153426635242600}, "additional": {"children": ["b89842ad-ec9b-4270-8858-36f9475ce293", "4ca64753-f846-4bc5-97fc-468f6c96fba3", "90ccee04-3b96-49ac-ba71-a8ee6d5776a2", "c16a8d34-408d-44d8-9798-48351c4797f6", "0d931457-a120-437b-b668-12e2101f7c77", "d2e2937d-713f-47e2-867b-a00c8e2b1de5", "9fc5a5a0-0541-4a1a-b62d-48c867ace091"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "bb60de25-5913-463b-8610-8de90b45a0fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b89842ad-ec9b-4270-8858-36f9475ce293", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426298993200, "endTime": 153426331756400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f2a99e8-2713-49b3-a894-a6306ea2bdb1", "logId": "bf21a66c-777c-42ed-87ec-f05ce9648d7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ca64753-f846-4bc5-97fc-468f6c96fba3", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426331816300, "endTime": 153426633155300}, "additional": {"children": ["c7f4bcd6-2b42-469b-bd5f-2e2ccac1ea85", "40b4f528-462c-4370-bdb9-1bb269d2fcfa", "fe4d3876-13ba-4343-bfa5-63e4b4820880", "ec39ebf8-d09f-4324-8bf0-f34610880b0a", "b59561b6-1d76-4572-9b02-bdb8ad1acd6e", "d1def2a3-553d-4984-9355-db7e83c900dd", "47a3dbf6-e181-462f-bb85-8b9e105a98e9", "0e304d77-96ba-49ff-b224-1d48ca38e7e2", "bc65b017-2b02-48f5-ad2f-3c3e21115918"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f2a99e8-2713-49b3-a894-a6306ea2bdb1", "logId": "3578389c-617e-49b2-a384-7ef02022b59e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90ccee04-3b96-49ac-ba71-a8ee6d5776a2", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426633198900, "endTime": 153426635182000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f2a99e8-2713-49b3-a894-a6306ea2bdb1", "logId": "8e2251f6-b736-40a6-8600-2621cd40a36b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c16a8d34-408d-44d8-9798-48351c4797f6", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426635194500, "endTime": 153426635231800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f2a99e8-2713-49b3-a894-a6306ea2bdb1", "logId": "330f7ec1-8fcb-4f24-9a02-2393021c7741"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d931457-a120-437b-b668-12e2101f7c77", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426307808300, "endTime": 153426307885000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f2a99e8-2713-49b3-a894-a6306ea2bdb1", "logId": "2990e2be-ac9c-4036-9aab-2647ffaf5ba5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2990e2be-ac9c-4036-9aab-2647ffaf5ba5", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426307808300, "endTime": 153426307885000}, "additional": {"logType": "info", "children": [], "durationId": "0d931457-a120-437b-b668-12e2101f7c77", "parent": "bb60de25-5913-463b-8610-8de90b45a0fa"}}, {"head": {"id": "d2e2937d-713f-47e2-867b-a00c8e2b1de5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426319508100, "endTime": 153426319548900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f2a99e8-2713-49b3-a894-a6306ea2bdb1", "logId": "aebf3593-3b2a-4c0f-a943-500f0902561d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aebf3593-3b2a-4c0f-a943-500f0902561d", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426319508100, "endTime": 153426319548900}, "additional": {"logType": "info", "children": [], "durationId": "d2e2937d-713f-47e2-867b-a00c8e2b1de5", "parent": "bb60de25-5913-463b-8610-8de90b45a0fa"}}, {"head": {"id": "d6ab00fd-d48b-46be-a7ef-48df3fbb35a4", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426319655200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c59dc32-3b69-4b62-b9cc-cb2b210826cc", "name": "Cache service initialization finished in 12 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426331557100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf21a66c-777c-42ed-87ec-f05ce9648d7a", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426298993200, "endTime": 153426331756400}, "additional": {"logType": "info", "children": [], "durationId": "b89842ad-ec9b-4270-8858-36f9475ce293", "parent": "bb60de25-5913-463b-8610-8de90b45a0fa"}}, {"head": {"id": "c7f4bcd6-2b42-469b-bd5f-2e2ccac1ea85", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426344014900, "endTime": 153426344070200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ca64753-f846-4bc5-97fc-468f6c96fba3", "logId": "14e4cfae-14a2-40c0-9f1a-67369d0d1023"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40b4f528-462c-4370-bdb9-1bb269d2fcfa", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426344115700, "endTime": 153426354100300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ca64753-f846-4bc5-97fc-468f6c96fba3", "logId": "158e8e52-196e-400e-be1c-7be14d4dd9ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe4d3876-13ba-4343-bfa5-63e4b4820880", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426354120000, "endTime": 153426471839200}, "additional": {"children": ["fc35dcb0-8ba4-42eb-8334-db8c856ad4ae", "9e05883d-cdda-49f5-a3d1-e170cddf9576", "d37e34d4-d315-4536-9e32-8f1eb9638242"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ca64753-f846-4bc5-97fc-468f6c96fba3", "logId": "e8fc1ead-f762-4de2-917d-9dc1fcb3a347"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec39ebf8-d09f-4324-8bf0-f34610880b0a", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426471860100, "endTime": 153426504781400}, "additional": {"children": ["d55aa667-0881-4e7f-a7d6-dca180708982"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ca64753-f846-4bc5-97fc-468f6c96fba3", "logId": "c0eb8fa3-4bab-41a5-9b6c-72e510bdd78b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b59561b6-1d76-4572-9b02-bdb8ad1acd6e", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426504811500, "endTime": 153426584720600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ca64753-f846-4bc5-97fc-468f6c96fba3", "logId": "5a45e3d2-2242-4806-afee-9f4b69ecc79b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1def2a3-553d-4984-9355-db7e83c900dd", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426585666800, "endTime": 153426605824400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ca64753-f846-4bc5-97fc-468f6c96fba3", "logId": "2d4222a7-241b-4f27-a559-3294a5a5f7c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47a3dbf6-e181-462f-bb85-8b9e105a98e9", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426605852900, "endTime": 153426632866900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ca64753-f846-4bc5-97fc-468f6c96fba3", "logId": "7dcabd51-b4cb-460a-b90f-c3c9a2a55419"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e304d77-96ba-49ff-b224-1d48ca38e7e2", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426632898200, "endTime": 153426633105700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ca64753-f846-4bc5-97fc-468f6c96fba3", "logId": "b921d9a8-91ac-4389-ac2d-e1ef18ab1131"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14e4cfae-14a2-40c0-9f1a-67369d0d1023", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426344014900, "endTime": 153426344070200}, "additional": {"logType": "info", "children": [], "durationId": "c7f4bcd6-2b42-469b-bd5f-2e2ccac1ea85", "parent": "3578389c-617e-49b2-a384-7ef02022b59e"}}, {"head": {"id": "158e8e52-196e-400e-be1c-7be14d4dd9ac", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426344115700, "endTime": 153426354100300}, "additional": {"logType": "info", "children": [], "durationId": "40b4f528-462c-4370-bdb9-1bb269d2fcfa", "parent": "3578389c-617e-49b2-a384-7ef02022b59e"}}, {"head": {"id": "fc35dcb0-8ba4-42eb-8334-db8c856ad4ae", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426355279700, "endTime": 153426355303100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe4d3876-13ba-4343-bfa5-63e4b4820880", "logId": "f409b5e6-57d4-459d-904b-3f1947749d0a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f409b5e6-57d4-459d-904b-3f1947749d0a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426355279700, "endTime": 153426355303100}, "additional": {"logType": "info", "children": [], "durationId": "fc35dcb0-8ba4-42eb-8334-db8c856ad4ae", "parent": "e8fc1ead-f762-4de2-917d-9dc1fcb3a347"}}, {"head": {"id": "9e05883d-cdda-49f5-a3d1-e170cddf9576", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426360141900, "endTime": 153426470380800}, "additional": {"children": ["de93d121-356a-455c-bbea-eb61d36261ef", "a69d7cb3-7f12-4d4d-9aee-fed0c9d24a0d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe4d3876-13ba-4343-bfa5-63e4b4820880", "logId": "725240a5-5d5f-495b-8fec-ee82283682a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de93d121-356a-455c-bbea-eb61d36261ef", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426360143600, "endTime": 153426374295200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9e05883d-cdda-49f5-a3d1-e170cddf9576", "logId": "818ccb3a-81cb-470f-8ae9-d4ad3edb6b3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a69d7cb3-7f12-4d4d-9aee-fed0c9d24a0d", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426374322100, "endTime": 153426470363900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9e05883d-cdda-49f5-a3d1-e170cddf9576", "logId": "bb35cc40-46c1-4aa8-bab7-213d8e66546c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cff6a530-a10b-4d46-8235-a71ce7793060", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426360152100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55a4aed4-4b19-454d-b80e-6fb80b55a0da", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426374020300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "818ccb3a-81cb-470f-8ae9-d4ad3edb6b3a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426360143600, "endTime": 153426374295200}, "additional": {"logType": "info", "children": [], "durationId": "de93d121-356a-455c-bbea-eb61d36261ef", "parent": "725240a5-5d5f-495b-8fec-ee82283682a0"}}, {"head": {"id": "a1b00643-6719-4c7e-bd7a-39b463352c01", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426374353400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe6df8f2-c1d0-4fab-bbc6-edd73c1a7814", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426388794200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bd5699a-2d72-4ae7-876e-bff064b7bb72", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426388961900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "365c31da-903d-4551-b2de-fb32161ad5bb", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426389155200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90515ab8-71ae-4b85-a5e5-8354c47c759e", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426389312200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a422851d-958e-4858-bf75-0afdf951c700", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426392128500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0789a0d-e612-47e0-a9c5-2295d64892fd", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426415799000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05278ddd-d8e6-4ae5-b2e4-e551262fd2ec", "name": "Sdk init in 40 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426440798700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4eea7f6-2916-4373-8df2-98d41dfa17a1", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426440945200}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 34, "second": 16}, "markType": "other"}}, {"head": {"id": "3136fed8-df5b-4d10-909a-89417ed5b312", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426440981800}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 34, "second": 16}, "markType": "other"}}, {"head": {"id": "5815918b-7f87-4845-87c2-cca567663bf7", "name": "Project task initialization takes 27 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426469904900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2955cdf6-774e-4da8-9166-19eb6bed32a1", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426470109300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecd73e79-bade-4bc8-b250-9807cf065e2e", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426470208300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cc453b9-851f-4894-9a54-7bbf1676966a", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426470286500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb35cc40-46c1-4aa8-bab7-213d8e66546c", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426374322100, "endTime": 153426470363900}, "additional": {"logType": "info", "children": [], "durationId": "a69d7cb3-7f12-4d4d-9aee-fed0c9d24a0d", "parent": "725240a5-5d5f-495b-8fec-ee82283682a0"}}, {"head": {"id": "725240a5-5d5f-495b-8fec-ee82283682a0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426360141900, "endTime": 153426470380800}, "additional": {"logType": "info", "children": ["818ccb3a-81cb-470f-8ae9-d4ad3edb6b3a", "bb35cc40-46c1-4aa8-bab7-213d8e66546c"], "durationId": "9e05883d-cdda-49f5-a3d1-e170cddf9576", "parent": "e8fc1ead-f762-4de2-917d-9dc1fcb3a347"}}, {"head": {"id": "d37e34d4-d315-4536-9e32-8f1eb9638242", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426471796000, "endTime": 153426471817500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe4d3876-13ba-4343-bfa5-63e4b4820880", "logId": "e83d38ed-b6cd-467d-8cca-2e33036056dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e83d38ed-b6cd-467d-8cca-2e33036056dd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426471796000, "endTime": 153426471817500}, "additional": {"logType": "info", "children": [], "durationId": "d37e34d4-d315-4536-9e32-8f1eb9638242", "parent": "e8fc1ead-f762-4de2-917d-9dc1fcb3a347"}}, {"head": {"id": "e8fc1ead-f762-4de2-917d-9dc1fcb3a347", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426354120000, "endTime": 153426471839200}, "additional": {"logType": "info", "children": ["f409b5e6-57d4-459d-904b-3f1947749d0a", "725240a5-5d5f-495b-8fec-ee82283682a0", "e83d38ed-b6cd-467d-8cca-2e33036056dd"], "durationId": "fe4d3876-13ba-4343-bfa5-63e4b4820880", "parent": "3578389c-617e-49b2-a384-7ef02022b59e"}}, {"head": {"id": "d55aa667-0881-4e7f-a7d6-dca180708982", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426473615700, "endTime": 153426504760900}, "additional": {"children": ["d6ef8740-05db-4cae-99a9-5097a8707b7f", "d5e0cbef-32c2-4cd5-843e-c9daefd4777f", "c1f0bcd6-5e49-4c2f-aa28-7e38d638a477"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec39ebf8-d09f-4324-8bf0-f34610880b0a", "logId": "e9205923-ea9a-4f11-9f0b-64a011635926"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6ef8740-05db-4cae-99a9-5097a8707b7f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426476182700, "endTime": 153426476197400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d55aa667-0881-4e7f-a7d6-dca180708982", "logId": "4dab7f47-bbfd-4840-8100-d724bee022f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4dab7f47-bbfd-4840-8100-d724bee022f2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426476182700, "endTime": 153426476197400}, "additional": {"logType": "info", "children": [], "durationId": "d6ef8740-05db-4cae-99a9-5097a8707b7f", "parent": "e9205923-ea9a-4f11-9f0b-64a011635926"}}, {"head": {"id": "d5e0cbef-32c2-4cd5-843e-c9daefd4777f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426478301500, "endTime": 153426500576900}, "additional": {"children": ["873d0eaa-72f1-4840-a564-f193567b8e12", "738bb95f-5ffc-4c40-983a-771600b59779"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d55aa667-0881-4e7f-a7d6-dca180708982", "logId": "e1b7e7b5-c387-42aa-8c48-c4ce300b855d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "873d0eaa-72f1-4840-a564-f193567b8e12", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426478302800, "endTime": 153426484179500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5e0cbef-32c2-4cd5-843e-c9daefd4777f", "logId": "438a18aa-1e66-4e13-95b4-6938583281e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "738bb95f-5ffc-4c40-983a-771600b59779", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426484195200, "endTime": 153426500561900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d5e0cbef-32c2-4cd5-843e-c9daefd4777f", "logId": "2dbf1aa0-5d19-460d-8c1d-56152ebd7791"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a378d6c8-de80-4cae-925c-78c960a09e4f", "name": "hvigorfile, resolving C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426478307300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1370f18c-a5c1-4263-9006-f3f69bd5c404", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426483981600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "438a18aa-1e66-4e13-95b4-6938583281e2", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426478302800, "endTime": 153426484179500}, "additional": {"logType": "info", "children": [], "durationId": "873d0eaa-72f1-4840-a564-f193567b8e12", "parent": "e1b7e7b5-c387-42aa-8c48-c4ce300b855d"}}, {"head": {"id": "4e561f30-f3cf-4484-8455-6f00a7304199", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426484213500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b6ef149-b70d-4870-a880-2a2de3e41f4b", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426493864800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbb25074-a2e0-471e-b57c-35c4d06caf32", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426494001400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fed47dac-e180-45f2-8ce2-25bfe7eeec39", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426494207200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75172582-bca8-4aac-8643-c14f92c4bb74", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426494320800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8684243-b510-4dd6-95ba-3366500b0ac3", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426494359300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a14dc60c-a1cb-4be0-8a35-8179a7d13d46", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426494392900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9e56f91-916b-4ffd-88fe-e62cada1d93a", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=release", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426494432600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65438cf3-1162-4375-82cf-c3d67a68296f", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426494465000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "619fb25c-672b-4f86-8b2f-644cede416b0", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426494608400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "907643e9-b9f5-4d86-87ee-0ee017c4ff79", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": false,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"release\",\n  \"arkOptions\": {\n    \"obfuscation\": {\n      \"ruleOptions\": {\n        \"enable\": false,\n        \"files\": [\n          \"./obfuscation-rules.txt\"\n        ]\n      }\n    }\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426494698200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4e969ff-d5d9-4420-bb3f-fa4fa86016c9", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426494753200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d04a883-4896-4808-9fe0-0239314f7106", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426494792100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02d40508-bcb5-49b5-ac46-f9e2c49d7f85", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426494834000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d18bc779-9909-4cf9-be2a-2164d03a0016", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426494866500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faa45360-a221-4d83-a8c7-1f0ae945d983", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426494944300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "382ba036-c061-43a9-a877-1257af4efd63", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426495004900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70db9985-89f1-48ae-bcab-4de03d771fbf", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426495043600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e4fa377-aad9-4b01-b4ca-e1d53ebef37f", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426495073800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6df74227-1241-414d-9a69-8c5961dfb708", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426495115300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73f96dd5-2eb2-437e-9ced-88508c7e84ea", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426499605800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73332c5b-4994-48a4-a6ea-033c7521d06e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426500241600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79d0b75a-ec24-4e5b-8336-face79b91475", "name": "hvigorfile, no custom plugins were found in C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426500397900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f43a95e1-2b63-435d-b6cf-4fa3409dcbd5", "name": "hvigorfile, resolve finished C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426500483600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dbf1aa0-5d19-460d-8c1d-56152ebd7791", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426484195200, "endTime": 153426500561900}, "additional": {"logType": "info", "children": [], "durationId": "738bb95f-5ffc-4c40-983a-771600b59779", "parent": "e1b7e7b5-c387-42aa-8c48-c4ce300b855d"}}, {"head": {"id": "e1b7e7b5-c387-42aa-8c48-c4ce300b855d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426478301500, "endTime": 153426500576900}, "additional": {"logType": "info", "children": ["438a18aa-1e66-4e13-95b4-6938583281e2", "2dbf1aa0-5d19-460d-8c1d-56152ebd7791"], "durationId": "d5e0cbef-32c2-4cd5-843e-c9daefd4777f", "parent": "e9205923-ea9a-4f11-9f0b-64a011635926"}}, {"head": {"id": "c1f0bcd6-5e49-4c2f-aa28-7e38d638a477", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426504712600, "endTime": 153426504735600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d55aa667-0881-4e7f-a7d6-dca180708982", "logId": "31958b00-b784-4675-b8c9-2f41bdc74997"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31958b00-b784-4675-b8c9-2f41bdc74997", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426504712600, "endTime": 153426504735600}, "additional": {"logType": "info", "children": [], "durationId": "c1f0bcd6-5e49-4c2f-aa28-7e38d638a477", "parent": "e9205923-ea9a-4f11-9f0b-64a011635926"}}, {"head": {"id": "e9205923-ea9a-4f11-9f0b-64a011635926", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426473615700, "endTime": 153426504760900}, "additional": {"logType": "info", "children": ["4dab7f47-bbfd-4840-8100-d724bee022f2", "e1b7e7b5-c387-42aa-8c48-c4ce300b855d", "31958b00-b784-4675-b8c9-2f41bdc74997"], "durationId": "d55aa667-0881-4e7f-a7d6-dca180708982", "parent": "c0eb8fa3-4bab-41a5-9b6c-72e510bdd78b"}}, {"head": {"id": "c0eb8fa3-4bab-41a5-9b6c-72e510bdd78b", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426471860100, "endTime": 153426504781400}, "additional": {"logType": "info", "children": ["e9205923-ea9a-4f11-9f0b-64a011635926"], "durationId": "ec39ebf8-d09f-4324-8bf0-f34610880b0a", "parent": "3578389c-617e-49b2-a384-7ef02022b59e"}}, {"head": {"id": "54169a0c-6aad-4b09-a7fe-bdacd5da2bc0", "name": "watch files: [\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\hvigorfile.ts',\n  'C:\\\\Users\\\\<USER>\\\\Documents\\\\NexusHub-OH\\\\NexusHub\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\command-line-tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1951 more items\n]", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426538867500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d942fb3c-747d-4b3e-9b2f-60d3a7f79ff3", "name": "hvigorfile, resolve hvigorfile dependencies in 80 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426584567600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a45e3d2-2242-4806-afee-9f4b69ecc79b", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426504811500, "endTime": 153426584720600}, "additional": {"logType": "info", "children": [], "durationId": "b59561b6-1d76-4572-9b02-bdb8ad1acd6e", "parent": "3578389c-617e-49b2-a384-7ef02022b59e"}}, {"head": {"id": "bc65b017-2b02-48f5-ad2f-3c3e21115918", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426585478900, "endTime": 153426585654900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ca64753-f846-4bc5-97fc-468f6c96fba3", "logId": "b2238650-141b-4215-8de3-eed00ccffbb2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2583f494-a43d-4a59-b2a0-57399fad2852", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426585508200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2238650-141b-4215-8de3-eed00ccffbb2", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426585478900, "endTime": 153426585654900}, "additional": {"logType": "info", "children": [], "durationId": "bc65b017-2b02-48f5-ad2f-3c3e21115918", "parent": "3578389c-617e-49b2-a384-7ef02022b59e"}}, {"head": {"id": "255bf161-1859-42de-b06b-6857565efa7a", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426588089000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b4f288b-ea4e-45c9-85c0-c6eab8807553", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426604034200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d4222a7-241b-4f27-a559-3294a5a5f7c8", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426585666800, "endTime": 153426605824400}, "additional": {"logType": "info", "children": [], "durationId": "d1def2a3-553d-4984-9355-db7e83c900dd", "parent": "3578389c-617e-49b2-a384-7ef02022b59e"}}, {"head": {"id": "71333ef3-f341-4df4-b362-e9d2bc7ecc09", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426605885100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85ec0cb2-62a8-493b-b0ad-82cc07caeaa0", "name": "Mo<PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426619565100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fb1c03a-8aec-4bb8-86fc-83e6b711d2de", "name": "Module NexusHub's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426619747200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7238face-d26f-427a-b1be-5bee20d31372", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426620178100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e7625a2-19c6-4244-bf07-1d6e38b079bf", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426626332300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "279216f2-9758-4aa3-aae4-777149bb2cca", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426626490300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dcabd51-b4cb-460a-b90f-c3c9a2a55419", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426605852900, "endTime": 153426632866900}, "additional": {"logType": "info", "children": [], "durationId": "47a3dbf6-e181-462f-bb85-8b9e105a98e9", "parent": "3578389c-617e-49b2-a384-7ef02022b59e"}}, {"head": {"id": "6be54434-e90f-4e0a-9cb5-9dadf83ffe8c", "name": "Configuration phase cost:289 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426632933400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b921d9a8-91ac-4389-ac2d-e1ef18ab1131", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426632898200, "endTime": 153426633105700}, "additional": {"logType": "info", "children": [], "durationId": "0e304d77-96ba-49ff-b224-1d48ca38e7e2", "parent": "3578389c-617e-49b2-a384-7ef02022b59e"}}, {"head": {"id": "3578389c-617e-49b2-a384-7ef02022b59e", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426331816300, "endTime": 153426633155300}, "additional": {"logType": "info", "children": ["14e4cfae-14a2-40c0-9f1a-67369d0d1023", "158e8e52-196e-400e-be1c-7be14d4dd9ac", "e8fc1ead-f762-4de2-917d-9dc1fcb3a347", "c0eb8fa3-4bab-41a5-9b6c-72e510bdd78b", "5a45e3d2-2242-4806-afee-9f4b69ecc79b", "2d4222a7-241b-4f27-a559-3294a5a5f7c8", "7dcabd51-b4cb-460a-b90f-c3c9a2a55419", "b921d9a8-91ac-4389-ac2d-e1ef18ab1131", "b2238650-141b-4215-8de3-eed00ccffbb2"], "durationId": "4ca64753-f846-4bc5-97fc-468f6c96fba3", "parent": "bb60de25-5913-463b-8610-8de90b45a0fa"}}, {"head": {"id": "9fc5a5a0-0541-4a1a-b62d-48c867ace091", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426635144700, "endTime": 153426635166600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f2a99e8-2713-49b3-a894-a6306ea2bdb1", "logId": "65aa1470-f755-424d-adb2-fc879d812fac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65aa1470-f755-424d-adb2-fc879d812fac", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426635144700, "endTime": 153426635166600}, "additional": {"logType": "info", "children": [], "durationId": "9fc5a5a0-0541-4a1a-b62d-48c867ace091", "parent": "bb60de25-5913-463b-8610-8de90b45a0fa"}}, {"head": {"id": "8e2251f6-b736-40a6-8600-2621cd40a36b", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426633198900, "endTime": 153426635182000}, "additional": {"logType": "info", "children": [], "durationId": "90ccee04-3b96-49ac-ba71-a8ee6d5776a2", "parent": "bb60de25-5913-463b-8610-8de90b45a0fa"}}, {"head": {"id": "330f7ec1-8fcb-4f24-9a02-2393021c7741", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426635194500, "endTime": 153426635231800}, "additional": {"logType": "info", "children": [], "durationId": "c16a8d34-408d-44d8-9798-48351c4797f6", "parent": "bb60de25-5913-463b-8610-8de90b45a0fa"}}, {"head": {"id": "bb60de25-5913-463b-8610-8de90b45a0fa", "name": "init", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426298973200, "endTime": 153426635242600}, "additional": {"logType": "info", "children": ["bf21a66c-777c-42ed-87ec-f05ce9648d7a", "3578389c-617e-49b2-a384-7ef02022b59e", "8e2251f6-b736-40a6-8600-2621cd40a36b", "330f7ec1-8fcb-4f24-9a02-2393021c7741", "2990e2be-ac9c-4036-9aab-2647ffaf5ba5", "aebf3593-3b2a-4c0f-a943-500f0902561d", "65aa1470-f755-424d-adb2-fc879d812fac"], "durationId": "6f2a99e8-2713-49b3-a894-a6306ea2bdb1"}}, {"head": {"id": "10b2c738-c027-4b2b-87bb-c27f2e5f8c08", "name": "Configuration task cost before running: 345 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426635497500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54ada711-7d6c-4336-a506-321446bbbec6", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426653138400, "endTime": 153426678046600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "84c25a2b-81f9-40ec-aa96-e997d2cd97a8", "logId": "b17bb12e-a0fa-48dc-86e7-af0366a2ebbc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84c25a2b-81f9-40ec-aa96-e997d2cd97a8", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426637847800}, "additional": {"logType": "detail", "children": [], "durationId": "54ada711-7d6c-4336-a506-321446bbbec6"}}, {"head": {"id": "5fb9a596-4a47-4d33-aa74-42661418a87b", "name": "Since there is no instance or instance is terminated, create a new worker pool.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426639126900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "684a95c6-0186-46d3-b1bb-38eedb31118b", "name": "Worker pool is initialized with config:  {\n  minPoolNum: 2,\n  maxPoolNum: undefined,\n  maxCoreSize: undefined,\n  cacheCapacity: undefined,\n  cacheTtl: undefined\n}", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426639280900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "694f3029-b0a4-470a-b988-cfb8c0622476", "name": "Create  resident worker with id: 0.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426642502300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78da0c09-25d3-429a-bfc1-4c006b000e73", "name": "Create  resident worker with id: 1.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426643810600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a868ebce-efa5-4655-89a9-8b3dbf23fd46", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426645530400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43e0b19a-fea3-4ca4-8060-b504c97978ed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426645715400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8968e2b3-6b93-4577-9ca6-105320ddf92c", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426653158400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f75a03de-cdbd-466b-94f4-fb34293c292e", "name": "Incremental task entry:default@PreBuild pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426677693100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90c61b91-4a7f-4bc0-a613-1380da5c11ee", "name": "entry : default@PreBuild cost memory 0.32479095458984375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426677900000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b17bb12e-a0fa-48dc-86e7-af0366a2ebbc", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426653138400, "endTime": 153426678046600}, "additional": {"logType": "info", "children": [], "durationId": "54ada711-7d6c-4336-a506-321446bbbec6"}}, {"head": {"id": "c13dbef3-15c5-4cea-8563-42383cbc9f90", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426690002700, "endTime": 153426692278600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e0fd1428-c7b2-469d-ba10-d2ff4943da85", "logId": "7f78e016-3c74-4ea9-89e7-4717353b674f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0fd1428-c7b2-469d-ba10-d2ff4943da85", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426687031100}, "additional": {"logType": "detail", "children": [], "durationId": "c13dbef3-15c5-4cea-8563-42383cbc9f90"}}, {"head": {"id": "cac901a2-3c3d-471d-a86c-4828296f14a8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426689057700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40bb69f1-f49e-4e11-95d0-7863a953d902", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426689201300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbd0fdcb-62cb-4eb5-b094-dc66b1b5f231", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426690013800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "262949c1-c9b8-4082-af92-7457ea65d133", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426690885100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea55b0d3-5938-4d00-bd43-e8409e5abc63", "name": "entry : default@CreateModuleInfo cost memory 0.0605926513671875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426692000100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5269dd0c-324d-4276-b237-f721ca891286", "name": "runTaskFromQueue task cost before running: 402 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426692148100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f78e016-3c74-4ea9-89e7-4717353b674f", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426690002700, "endTime": 153426692278600, "totalTime": 2117600}, "additional": {"logType": "info", "children": [], "durationId": "c13dbef3-15c5-4cea-8563-42383cbc9f90"}}, {"head": {"id": "f54e90c6-1d42-415c-a331-de20a1c627f9", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426706944300, "endTime": 153426711234900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9b2ed743-8413-4914-b002-ea564ac8289a", "logId": "84d3bd7a-63cf-4660-99c1-ae7154be3c2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b2ed743-8413-4914-b002-ea564ac8289a", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426696311000}, "additional": {"logType": "detail", "children": [], "durationId": "f54e90c6-1d42-415c-a331-de20a1c627f9"}}, {"head": {"id": "4e9ee277-f733-4a22-9b83-837c7daba25b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426698348700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cea0c61-d606-4acc-a8f2-3dd896bd803c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426698534100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1556f44-4a5e-4f37-961b-034cb0250030", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426706966000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a18af5a2-91bd-4b50-8052-24d9454d2a00", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426708739900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa00be12-4f8c-4322-826c-5de50d10882a", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426710928400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a31409ed-a3a0-4f9a-956a-cfa84833b09e", "name": "entry : default@GenerateMetadata cost memory 0.1027069091796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426711115500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84d3bd7a-63cf-4660-99c1-ae7154be3c2b", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426706944300, "endTime": 153426711234900}, "additional": {"logType": "info", "children": [], "durationId": "f54e90c6-1d42-415c-a331-de20a1c627f9"}}, {"head": {"id": "34b3fdef-26ce-4309-be20-00ed9786ec47", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426716427700, "endTime": 153426717052500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "39dde4e5-8b17-4a39-91bd-5421724426b2", "logId": "19738371-2c83-4765-9195-3e71e99679be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39dde4e5-8b17-4a39-91bd-5421724426b2", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426714196900}, "additional": {"logType": "detail", "children": [], "durationId": "34b3fdef-26ce-4309-be20-00ed9786ec47"}}, {"head": {"id": "72c329a0-0e90-46e5-b63f-175acff0e1c9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426716055300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca89e7c5-ef9e-4529-aa10-9263530a7a50", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426716221100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e029c1c9-7da1-46f3-9884-f2d9764fd4a6", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426716439900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e60fe030-26b8-44a7-832e-08817436681c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426716611600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca037dcf-21b1-493e-9321-480c53445fb9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426716708900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58e71dab-d9d5-465d-9404-45cf41903513", "name": "entry : default@ConfigureCmake cost memory 0.0374755859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426716834000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8492671-31b5-4b32-a85d-9b40a64c5920", "name": "runTaskFromQueue task cost before running: 427 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426716962000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19738371-2c83-4765-9195-3e71e99679be", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426716427700, "endTime": 153426717052500, "totalTime": 506600}, "additional": {"logType": "info", "children": [], "durationId": "34b3fdef-26ce-4309-be20-00ed9786ec47"}}, {"head": {"id": "1deded39-dbf7-461d-a339-a918b94943ed", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426722963900, "endTime": 153426726797100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "95e62b1a-4600-4830-8879-010c1dad713d", "logId": "941dd7c5-8f52-4d33-99fc-c35c891fb41b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95e62b1a-4600-4830-8879-010c1dad713d", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426719787600}, "additional": {"logType": "detail", "children": [], "durationId": "1deded39-dbf7-461d-a339-a918b94943ed"}}, {"head": {"id": "3efd743c-b207-4f8d-aa96-561f085ef745", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426721668200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85d0c97c-7a99-42f4-ba02-0236f2198c34", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426721831200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26cb7a8a-b5e8-4ce9-b981-0c5e634f6731", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426722981600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5d6e150-25a7-45a3-91ed-9be582bb75ea", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426726511000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "712f8244-3175-4fc3-8aa3-792b95965ad4", "name": "entry : default@MergeProfile cost memory 0.1186981201171875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426726686400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "941dd7c5-8f52-4d33-99fc-c35c891fb41b", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426722963900, "endTime": 153426726797100}, "additional": {"logType": "info", "children": [], "durationId": "1deded39-dbf7-461d-a339-a918b94943ed"}}, {"head": {"id": "2cde34d3-c711-488e-a12d-f42d7ed78ad7", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426731648600, "endTime": 153426736338900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ae623057-fd69-4b62-aa9b-c89f2c6290ca", "logId": "80e0b1e5-518b-44cc-a44c-99d1732943af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae623057-fd69-4b62-aa9b-c89f2c6290ca", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426729192700}, "additional": {"logType": "detail", "children": [], "durationId": "2cde34d3-c711-488e-a12d-f42d7ed78ad7"}}, {"head": {"id": "81cd0727-96bc-4586-875d-c074e7b0d716", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426730322300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19d81d9e-076b-45fb-bf5c-47fd73b02c43", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426730488200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cd2511e-fb31-456d-baab-a381f1549eb9", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426731677100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5099b69-4548-4b69-af60-ae3074e44009", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426733222700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64b6edcc-2017-434a-805f-c26bbf23209e", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426736035100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cb8455f-4c47-43ef-bf0d-1828681124c5", "name": "entry : default@CreateBuildProfile cost memory 0.10787200927734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426736228100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80e0b1e5-518b-44cc-a44c-99d1732943af", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426731648600, "endTime": 153426736338900}, "additional": {"logType": "info", "children": [], "durationId": "2cde34d3-c711-488e-a12d-f42d7ed78ad7"}}, {"head": {"id": "5ba1317a-79e2-4f7d-bb06-576084762b35", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426739782500, "endTime": 153426740252600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "30f5d9ee-7049-488f-a48e-79804d5b856a", "logId": "053e362c-7394-4b5e-b358-9c5855eb9459"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30f5d9ee-7049-488f-a48e-79804d5b856a", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426738150300}, "additional": {"logType": "detail", "children": [], "durationId": "5ba1317a-79e2-4f7d-bb06-576084762b35"}}, {"head": {"id": "a3ebcbad-9ab7-4e7b-915c-949aef641d17", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426739062500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc18be6e-4a33-491c-9b92-f8cd05f95c3a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426739170800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de44bc2a-db7f-4f84-a61a-e72a3100f987", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426739791600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78370a69-b0ad-41e8-bbce-1bb8013c39b0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426739906400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8440f715-ded1-449a-8153-45904d38c72a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426739945900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f4d422b-e97e-49f2-ab99-8bc4e7ba6e41", "name": "entry : default@PreCheckSyscap cost memory 0.041107177734375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426740110900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78146a24-d609-430b-8c65-a45f5c904fbd", "name": "runTaskFromQueue task cost before running: 450 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426740207600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "053e362c-7394-4b5e-b358-9c5855eb9459", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426739782500, "endTime": 153426740252600, "totalTime": 404300}, "additional": {"logType": "info", "children": [], "durationId": "5ba1317a-79e2-4f7d-bb06-576084762b35"}}, {"head": {"id": "5a65f642-b333-4e2d-b8f8-8745a811790a", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426745032900, "endTime": 153426753735600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6eb53861-e9a8-4b49-9ddc-c485e1e927e6", "logId": "fdb952c6-4c76-44cd-ab13-1fc351684bfd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6eb53861-e9a8-4b49-9ddc-c485e1e927e6", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426741517400}, "additional": {"logType": "detail", "children": [], "durationId": "5a65f642-b333-4e2d-b8f8-8745a811790a"}}, {"head": {"id": "4c0a1696-cf86-4418-9916-a86eaafca723", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426742629300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d457a86d-9127-42ac-903d-14f7dcfea447", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426742769400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0586ba3d-64ec-40b2-92cc-d7f29378bc4b", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426745051200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7567a47e-50f9-4789-ba14-4be3aeb313b9", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426752441600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a1c827c-985a-4a22-a464-fc34fb01e3a1", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426753460200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31013795-351f-49d3-81f8-e3bfb5134b4c", "name": "entry : default@GeneratePkgContextInfo cost memory 0.24982452392578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426753626200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdb952c6-4c76-44cd-ab13-1fc351684bfd", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426745032900, "endTime": 153426753735600}, "additional": {"logType": "info", "children": [], "durationId": "5a65f642-b333-4e2d-b8f8-8745a811790a"}}, {"head": {"id": "b61ffd81-898a-4b1e-98a3-d37fbb680c83", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426766869700, "endTime": 153426770250600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "d6121d24-61a4-4203-886c-4f1b876e4ef1", "logId": "8036f48b-73b9-4564-a2be-bd24b9708c12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6121d24-61a4-4203-886c-4f1b876e4ef1", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426756170000}, "additional": {"logType": "detail", "children": [], "durationId": "b61ffd81-898a-4b1e-98a3-d37fbb680c83"}}, {"head": {"id": "862e4bd9-5841-40d8-ba2f-3f341086c6a6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426757865400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56b9d25e-83ea-4b19-935e-ff4becb6a8e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426758039400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a972dc48-e66f-446e-bf00-27c3132f2954", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426766894600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdafa0a1-7ab7-4d20-9016-7071c0637bdc", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426769519700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44125468-d868-4631-b411-65a3b0597b0d", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426769701800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dab05d3-b824-436d-8d6b-57b4e6b26a51", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426769833500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c8a8107-cdb3-42d2-ac91-c227c984e6d0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426769932600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f0c7909-c8c9-4197-b677-e75e6c90ab60", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1210784912109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426770054900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48331b0b-0b9c-4d29-b65d-9abd3133d69b", "name": "runTaskFromQueue task cost before running: 480 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426770171300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8036f48b-73b9-4564-a2be-bd24b9708c12", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426766869700, "endTime": 153426770250600, "totalTime": 3282800}, "additional": {"logType": "info", "children": [], "durationId": "b61ffd81-898a-4b1e-98a3-d37fbb680c83"}}, {"head": {"id": "a4d2b29c-981a-4efb-bbbf-e534dfe90055", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426776277800, "endTime": 153426776862200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c0b7a726-f5d5-4a42-8c5a-b32f3c3e2a09", "logId": "dbec9b3f-8958-46a4-858a-4cf1ef28be34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0b7a726-f5d5-4a42-8c5a-b32f3c3e2a09", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426773383100}, "additional": {"logType": "detail", "children": [], "durationId": "a4d2b29c-981a-4efb-bbbf-e534dfe90055"}}, {"head": {"id": "0de97d57-3884-4cbb-b413-5768a47a3482", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426774969100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8012d2f3-e247-4ebc-a2d5-5effe6b989be", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426775119100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40cf64ab-ae26-4aba-b419-7070cd167f37", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426776289900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60b2bcad-0aa2-4238-9282-ddf27675df4d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426776458900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79af873f-7d0d-4938-b27a-368d374a102c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426776552700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82ebb918-5e14-41fe-9c07-f6704c2a3963", "name": "entry : default@BuildNativeWithCmake cost memory 0.03852081298828125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426776664200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ca692b8-447b-4183-8a00-4e55a0e5204d", "name": "runTaskFromQueue task cost before running: 486 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426776774400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbec9b3f-8958-46a4-858a-4cf1ef28be34", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426776277800, "endTime": 153426776862200, "totalTime": 474500}, "additional": {"logType": "info", "children": [], "durationId": "a4d2b29c-981a-4efb-bbbf-e534dfe90055"}}, {"head": {"id": "2d5f3fbb-62bd-4961-a8e6-0f91d5eafc1d", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426784342100, "endTime": 153426789608200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f18c7086-8f9e-437e-8717-9fa18609cb20", "logId": "87377c3f-bf6e-4585-9094-5bbee7ba87b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f18c7086-8f9e-437e-8717-9fa18609cb20", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426779664600}, "additional": {"logType": "detail", "children": [], "durationId": "2d5f3fbb-62bd-4961-a8e6-0f91d5eafc1d"}}, {"head": {"id": "41a85765-8ece-4188-8d08-55f1aa5fbf11", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426781366600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb5b942f-7770-4704-8b0c-73948011a087", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426781536900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03f4994b-26a5-46fb-88d0-dcc2be298252", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426784354400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e46388f0-ba23-4686-aa6f-1af8398be8a4", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426789305600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6edacac5-dc51-496c-9316-2f406a7b8b64", "name": "entry : default@MakePackInfo cost memory 0.16375732421875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426789491400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87377c3f-bf6e-4585-9094-5bbee7ba87b8", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426784342100, "endTime": 153426789608200}, "additional": {"logType": "info", "children": [], "durationId": "2d5f3fbb-62bd-4961-a8e6-0f91d5eafc1d"}}, {"head": {"id": "6499849d-7b19-4620-8ea0-476e6ca5fa98", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426797631100, "endTime": 153426801829000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "314c719e-170b-4607-b8e1-71fde4f3e62c", "logId": "9d0179d0-7362-4b75-b16d-2a1ddfdcb807"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "314c719e-170b-4607-b8e1-71fde4f3e62c", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426792897300}, "additional": {"logType": "detail", "children": [], "durationId": "6499849d-7b19-4620-8ea0-476e6ca5fa98"}}, {"head": {"id": "55eaf17f-a2d0-43c1-821c-1cd942e3c02e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426795171400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8720ed8f-1631-46ab-9fdc-e7804f54490d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426795348100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a388de9-77e8-4710-bcca-419467313bd8", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426797651700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e23bc461-3165-459f-b2d4-994ae6fae8e8", "name": "File: 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426797990100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54688c14-005a-4507-b9e4-c875d3853432", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426799308500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de2b5484-c17e-4437-bf19-5bfdb200575d", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426801573500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b550d56e-16c4-4692-9679-d736514e7a4b", "name": "entry : default@SyscapTransform cost memory 0.1508026123046875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426801750400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d0179d0-7362-4b75-b16d-2a1ddfdcb807", "name": "UP-TO-DATE :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426797631100, "endTime": 153426801829000}, "additional": {"logType": "info", "children": [], "durationId": "6499849d-7b19-4620-8ea0-476e6ca5fa98"}}, {"head": {"id": "fc881260-e9a4-4993-b798-1212c11935d8", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426805160800, "endTime": 153426806871500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "b21f65b8-901b-46eb-8468-b6e614b150c6", "logId": "66520be7-20d9-4e9d-9ae6-7d31a882ed1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b21f65b8-901b-46eb-8468-b6e614b150c6", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426803254400}, "additional": {"logType": "detail", "children": [], "durationId": "fc881260-e9a4-4993-b798-1212c11935d8"}}, {"head": {"id": "21d10bac-1573-4dae-bf82-2de85b09bb29", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426804115500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39f12196-c535-4d03-8b1b-ab70574fb6fd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426804219700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c39b32e9-cb22-4206-93cf-24a5b91bd026", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426805193800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd4398b9-9342-46bf-9a89-8185af1c7791", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426806701200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42c22f66-4a80-45a2-9646-e1d11867b369", "name": "entry : default@ProcessProfile cost memory 0.12866973876953125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426806822500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66520be7-20d9-4e9d-9ae6-7d31a882ed1b", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426805160800, "endTime": 153426806871500}, "additional": {"logType": "info", "children": [], "durationId": "fc881260-e9a4-4993-b798-1212c11935d8"}}, {"head": {"id": "e4c91af5-8798-4e19-896c-0e66f33769d0", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426812161800, "endTime": 153426822690700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "19ed9093-8ae3-4632-ba87-243476e46fa0", "logId": "448c2684-66db-4acf-b519-430ae26549c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19ed9093-8ae3-4632-ba87-243476e46fa0", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426808826200}, "additional": {"logType": "detail", "children": [], "durationId": "e4c91af5-8798-4e19-896c-0e66f33769d0"}}, {"head": {"id": "2b4aa12c-daf3-45f2-8dc3-ab120e587444", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426810407200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca91cf74-ac59-4ca5-81a2-0a7c6966c740", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426810570300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7873f04e-e5e4-43cc-8ad2-100ceb5e000a", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426812226700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5730ed9d-9c94-4c58-ba09-fbe9b0b374eb", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426822400800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cac213af-5781-4e07-9858-516c1d0f49ea", "name": "entry : default@ProcessRouterMap cost memory 0.23352813720703125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426822586700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "448c2684-66db-4acf-b519-430ae26549c3", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426812161800, "endTime": 153426822690700}, "additional": {"logType": "info", "children": [], "durationId": "e4c91af5-8798-4e19-896c-0e66f33769d0"}}, {"head": {"id": "a18f82f5-f96f-468c-8dba-8b74b4ecf75a", "name": "entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426828590300, "endTime": 153426837920100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist."], "detailId": "cd14259c-8008-4fb6-b875-9f326bc1da78", "logId": "3248f5fa-015b-48de-a265-e75e9dafb62a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd14259c-8008-4fb6-b875-9f326bc1da78", "name": "create entry:default@ProcessStartupConfig task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426827291500}, "additional": {"logType": "detail", "children": [], "durationId": "a18f82f5-f96f-468c-8dba-8b74b4ecf75a"}}, {"head": {"id": "35825533-2f9f-4ad9-a5c9-3c7f9dfc2d7a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426828391100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01a839ac-8006-428b-adb6-a8b0de1ca4c2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426828504800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3a55fcd-13c5-4be3-aaa2-2e85cab1950e", "name": "Executing task :entry:default@ProcessStartupConfig", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426828596100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b78caac-1fa2-423b-a1aa-75277fbd81f9", "name": "Task 'entry:default@ProcessStartupConfig' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426828690800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85fd7079-23da-451d-abb0-9ce189eb8a41", "name": "entry:default@ProcessStartupConfig is not up-to-date, since the output file 'C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426835671500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fbbb0b3-7272-4aa4-98ca-3794b7bd9d6e", "name": "Incremental task entry:default@ProcessStartupConfig pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426835850300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "536d4344-fb38-4d84-8601-17fa2cc6fa1c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426835983500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "240bf2ad-35f8-4db6-a6e4-e6fd82c6f841", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426836055400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d989b65-99df-4e9d-959c-24bed0dea438", "name": "entry : default@ProcessStartupConfig cost memory 0.259429931640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426837641400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "050a8f65-a10d-4e7e-b698-de8811ccac55", "name": "runTaskFromQueue task cost before running: 547 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426837820800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3248f5fa-015b-48de-a265-e75e9dafb62a", "name": "Finished :entry:default@ProcessStartupConfig", "description": "Process startup configuration.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426828590300, "endTime": 153426837920100, "totalTime": 9195300}, "additional": {"logType": "info", "children": [], "durationId": "a18f82f5-f96f-468c-8dba-8b74b4ecf75a"}}, {"head": {"id": "13f6c6cd-285b-44e2-a76c-5f0b0a9c00e0", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426843051700, "endTime": 153426845317700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a050ac73-d2ae-41f0-989f-1eef9c05e346", "logId": "82a85874-5ab3-405d-9acc-dd9e05449637"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a050ac73-d2ae-41f0-989f-1eef9c05e346", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426841249900}, "additional": {"logType": "detail", "children": [], "durationId": "13f6c6cd-285b-44e2-a76c-5f0b0a9c00e0"}}, {"head": {"id": "e2b726bc-4ddf-4c04-8357-e9ebc65d8157", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426842036400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6a0ed55-3ce5-4d69-bad3-02616c9599f8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426842126400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33a0060b-a48c-4e74-9ecd-40915bbe6e3b", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426843065600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6119f0e3-0eda-4490-a1fb-9523f6bd7e79", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426843218500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ae975bc-3263-4827-aba1-9f6b4a596a2d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426843283900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9831a5d8-112e-4f08-aa0a-57ea3d01552f", "name": "entry : default@BuildNativeWithNinja cost memory 0.05831146240234375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426844930900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ed00615-22f5-4065-b9ab-1bee8d664ba4", "name": "runTaskFromQueue task cost before running: 555 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426845190900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82a85874-5ab3-405d-9acc-dd9e05449637", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426843051700, "endTime": 153426845317700, "totalTime": 2101400}, "additional": {"logType": "info", "children": [], "durationId": "13f6c6cd-285b-44e2-a76c-5f0b0a9c00e0"}}, {"head": {"id": "34aa194a-fa36-4856-8dd8-33fb6f03ee8c", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426855358800, "endTime": 153426866353300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "994499f7-04ee-48b1-b270-c1a4e4151d45", "logId": "b89e63c3-6d85-4344-93ea-7798064c6cf5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "994499f7-04ee-48b1-b270-c1a4e4151d45", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426848495800}, "additional": {"logType": "detail", "children": [], "durationId": "34aa194a-fa36-4856-8dd8-33fb6f03ee8c"}}, {"head": {"id": "2b29a06a-21b1-4b87-a6d1-c0e14e16463f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426850622800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fba88d6d-ab9a-4ab6-a9ee-aa534019a4ff", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426850799300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd8b7974-7d48-4230-8412-4c654e868c81", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426852442100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed9da400-11f2-4e40-b070-bb9941678cd7", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426858588700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a7e7c6d-9111-4396-bbf2-be19503c9ce9", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426862534700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1764a314-8090-453c-ba96-51d4845c6cdd", "name": "entry : default@ProcessResource cost memory 0.1621246337890625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426862768700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b89e63c3-6d85-4344-93ea-7798064c6cf5", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426855358800, "endTime": 153426866353300}, "additional": {"logType": "info", "children": [], "durationId": "34aa194a-fa36-4856-8dd8-33fb6f03ee8c"}}, {"head": {"id": "8aab0bad-fd6e-4668-9764-cc2c3df93e2e", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426883079900, "endTime": 153426924117200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c685ea79-4335-42f0-84d1-76fb7a2f873c", "logId": "f9061a60-9110-46b9-a074-7faad6eba3e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c685ea79-4335-42f0-84d1-76fb7a2f873c", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426873284000}, "additional": {"logType": "detail", "children": [], "durationId": "8aab0bad-fd6e-4668-9764-cc2c3df93e2e"}}, {"head": {"id": "6e573a34-5fc0-4023-b871-38a65e58967f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426875610600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b067c610-8b19-4dbb-b35b-404cb75df2fe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426875790700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8e85fbd-0ba5-433f-ad21-b63a0bf72d08", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426883103000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fad9809-7e7d-4366-b008-7284244bfb83", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 23 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426923796000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26a3d550-975e-4658-955f-57eb35975511", "name": "entry : default@GenerateLoaderJson cost memory 0.8825531005859375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426924012100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9061a60-9110-46b9-a074-7faad6eba3e1", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426883079900, "endTime": 153426924117200}, "additional": {"logType": "info", "children": [], "durationId": "8aab0bad-fd6e-4668-9764-cc2c3df93e2e"}}, {"head": {"id": "afaeb87b-1f4f-484d-a7af-e8c41a3f247e", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426938091700, "endTime": 153426943485600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "eb6a9350-9381-4596-83d3-a2f91fac50f7", "logId": "fa36c780-ee73-4afd-a2ba-b208f1c9f9c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb6a9350-9381-4596-83d3-a2f91fac50f7", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426935005500}, "additional": {"logType": "detail", "children": [], "durationId": "afaeb87b-1f4f-484d-a7af-e8c41a3f247e"}}, {"head": {"id": "bfc5d879-a6bc-4b18-b75f-f6f52d438968", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426937187300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79e7a2dd-f2a1-4f3b-8f4b-15e6bfa4300a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426937340700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54f886a7-7193-43ac-af0b-fe422e79d96a", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426938102100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0735d318-a4cc-4067-a296-7edb8169b1d2", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426943183500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e228752f-d9fe-491d-9a55-980526d29caf", "name": "entry : default@ProcessLibs cost memory 0.142303466796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426943362100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa36c780-ee73-4afd-a2ba-b208f1c9f9c8", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426938091700, "endTime": 153426943485600}, "additional": {"logType": "info", "children": [], "durationId": "afaeb87b-1f4f-484d-a7af-e8c41a3f247e"}}, {"head": {"id": "02070511-fff9-4fc4-82f3-1a3fbcc677bc", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426955879100, "endTime": 153427006123700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "069d64ae-4d4a-44be-970f-b41850624d86", "logId": "c7625ae5-f490-4eab-9d9c-d57a0c87caa2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "069d64ae-4d4a-44be-970f-b41850624d86", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426946348800}, "additional": {"logType": "detail", "children": [], "durationId": "02070511-fff9-4fc4-82f3-1a3fbcc677bc"}}, {"head": {"id": "d6187aed-caef-46ac-be62-92ff1e6f8600", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426948419400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1864760c-3c1b-4dae-9bd2-f409a30818cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426948590800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad33b6a-4ce8-439e-bbfd-2363af43efcb", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426950572900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e64e5d0-e6f7-4643-b800-f40cd4ae8780", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426955928000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94e65bdc-1865-44ce-931a-5aa0a9368813", "name": "Incremental task entry:default@CompileResource pre-execution cost: 48 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427005892000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71229880-6715-4b60-b761-2295f8e77de4", "name": "entry : default@CompileResource cost memory 1.3155746459960938", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427006034700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7625ae5-f490-4eab-9d9c-d57a0c87caa2", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426955879100, "endTime": 153427006123700}, "additional": {"logType": "info", "children": [], "durationId": "02070511-fff9-4fc4-82f3-1a3fbcc677bc"}}, {"head": {"id": "cbff9977-df6f-4448-94d5-268df75bf09f", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427012476300, "endTime": 153427015525200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6a04a0d0-9495-4c73-a048-9a270177ec70", "logId": "b8056213-1812-447f-b3c8-98771b25cfce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a04a0d0-9495-4c73-a048-9a270177ec70", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427009112000}, "additional": {"logType": "detail", "children": [], "durationId": "cbff9977-df6f-4448-94d5-268df75bf09f"}}, {"head": {"id": "d08687ec-492d-4fd7-b3bb-6e874b024bfc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427009911600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90a1d71f-c29b-49e9-8428-73dd43a5e6d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427010076400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d3b40db-4d05-48a4-8127-6efde7ab1094", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427012486700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d7ab83c-2d9a-4815-95ca-e6f899429aa0", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427012925100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "335cc8f3-8223-4b8d-b939-21dd0181dd66", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427015220700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "587d5b92-2cee-4545-8bd7-4e73ae3f547c", "name": "entry : default@DoNativeStrip cost memory 0.0803375244140625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427015391000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8056213-1812-447f-b3c8-98771b25cfce", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427012476300, "endTime": 153427015525200}, "additional": {"logType": "info", "children": [], "durationId": "cbff9977-df6f-4448-94d5-268df75bf09f"}}, {"head": {"id": "d223aea6-4b13-4adf-ae35-a0b61d85db0d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427028193000, "endTime": 153427073584300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "03208887-070a-4939-b9af-0187dec1dd35", "logId": "d3b48e42-826a-4799-8a01-90abb2c357db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03208887-070a-4939-b9af-0187dec1dd35", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427017557400}, "additional": {"logType": "detail", "children": [], "durationId": "d223aea6-4b13-4adf-ae35-a0b61d85db0d"}}, {"head": {"id": "adf96b38-2f3b-4b42-ade1-c0ce2a55621f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427018389100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72b74597-2ab4-4547-bbb7-e97fe70b8dc3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427020253300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c60f7c1-6736-4a68-9e77-754b95582304", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427028218000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adb3ee88-4afc-4b2c-ba4f-4f437aada76d", "name": "obfuscationOptions: undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427028477300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdb348e3-4115-4247-b7cb-e528720f2ef1", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 36 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427073270000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29b77160-ee5d-4f0b-a5e1-23289fae820c", "name": "entry : default@CompileArkTS cost memory 1.17633056640625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427073460500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3b48e42-826a-4799-8a01-90abb2c357db", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427028193000, "endTime": 153427073584300}, "additional": {"logType": "info", "children": [], "durationId": "d223aea6-4b13-4adf-ae35-a0b61d85db0d"}}, {"head": {"id": "a1e8c458-d39b-4f30-83fd-863ba44cdf33", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427094754700, "endTime": 153427107888600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "eae34402-c717-4278-a8e0-86cbee67f99c", "logId": "c416e62a-9932-4e00-a03e-646f6352592f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eae34402-c717-4278-a8e0-86cbee67f99c", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427084636400}, "additional": {"logType": "detail", "children": [], "durationId": "a1e8c458-d39b-4f30-83fd-863ba44cdf33"}}, {"head": {"id": "f366eff8-1506-4afa-a389-fb75942585ad", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427086587000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "974b47d3-73ba-4522-a179-19c647fddacd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427086750300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87db2045-242b-4990-b671-8bd114136ca0", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427094770800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a69fe23-6cce-4468-9dc8-cb61aea06f32", "name": "entry : default@BuildJS cost memory 0.3476715087890625", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427107583900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a73f962-0814-4098-822b-4340f0d1e041", "name": "runTaskFromQueue task cost before running: 817 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427107786000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c416e62a-9932-4e00-a03e-646f6352592f", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427094754700, "endTime": 153427107888600, "totalTime": 13000700}, "additional": {"logType": "info", "children": [], "durationId": "a1e8c458-d39b-4f30-83fd-863ba44cdf33"}}, {"head": {"id": "8c16ddc4-74db-412c-bc3c-fb30d2169333", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427117665200, "endTime": 153427123316800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d6f0f074-0783-47c3-a61d-1eec0a92d015", "logId": "98691a5e-1ab7-435c-9b7f-e3ab15930919"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6f0f074-0783-47c3-a61d-1eec0a92d015", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427110691500}, "additional": {"logType": "detail", "children": [], "durationId": "8c16ddc4-74db-412c-bc3c-fb30d2169333"}}, {"head": {"id": "f64fe36b-ac8a-4d78-84dd-763c85444af5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427112883100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f92afdb-e0ed-4f8e-87d2-29b4b2c90370", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427113052700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5b8d002-5e1b-4cf3-bb8d-142bead3dc89", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427117684300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "882bea9e-b0d5-41c6-9164-11fc46519fcb", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427118896500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c86bf91f-0749-457b-b624-cbf6fcff357b", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427122988700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b79b1c5-5a29-4ed2-aba6-38d91a50f1a0", "name": "entry : default@CacheNativeLibs cost memory 0.095428466796875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427123194900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98691a5e-1ab7-435c-9b7f-e3ab15930919", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427117665200, "endTime": 153427123316800}, "additional": {"logType": "info", "children": [], "durationId": "8c16ddc4-74db-412c-bc3c-fb30d2169333"}}, {"head": {"id": "778914f1-b038-43e6-838f-8d38690dba7d", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427129967800, "endTime": 153427132701900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "c9c01d01-f7b9-4a8a-8adb-86b3e50657da", "logId": "1ba14604-c39f-4eaf-a9f3-fae9476ef1c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9c01d01-f7b9-4a8a-8adb-86b3e50657da", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427125891800}, "additional": {"logType": "detail", "children": [], "durationId": "778914f1-b038-43e6-838f-8d38690dba7d"}}, {"head": {"id": "7bd9580c-f8ce-45a2-960c-f0e4795a37d0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427127911300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17d743da-6da9-4ff7-af88-68d9b4561ec6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427128068300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f3c2f27-aa0e-4a4e-9c38-187647ad0b49", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427129983800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e3441aa-9209-44ab-ae4c-25ea78bae399", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427130493800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ae652bf-ce4b-44eb-9744-ec975af16ab4", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427132475000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfd838ea-f1d3-46a1-8318-24b1cefbf34b", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07537078857421875", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427132614900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ba14604-c39f-4eaf-a9f3-fae9476ef1c0", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427129967800, "endTime": 153427132701900}, "additional": {"logType": "info", "children": [], "durationId": "778914f1-b038-43e6-838f-8d38690dba7d"}}, {"head": {"id": "41f2b4c4-7629-43c4-bf1d-c1070e9fd337", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427148746500, "endTime": 153427191942800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "36bcce76-1747-4013-94e1-4f6510ad9c61", "logId": "36b5834d-e3c7-4975-a879-5fb085343bbe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36bcce76-1747-4013-94e1-4f6510ad9c61", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427135869200}, "additional": {"logType": "detail", "children": [], "durationId": "41f2b4c4-7629-43c4-bf1d-c1070e9fd337"}}, {"head": {"id": "2fcfa9f2-2fbc-4e26-acd3-57b7d091ec5b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427137489000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c830a38-dbab-40e1-a285-d1d6be02f896", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427137629300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5803b94-b02c-4a73-b6f6-f3832d25aa38", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427148769400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4b07cb1-2971-4c9a-8438-5d5cdbb3cc84", "name": "Incremental task entry:default@PackageHap pre-execution cost: 39 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427191609700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ecbcffe-7e41-428d-81df-79b4cead9e7b", "name": "entry : default@PackageHap cost memory 0.9466171264648438", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427191826100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36b5834d-e3c7-4975-a879-5fb085343bbe", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427148746500, "endTime": 153427191942800}, "additional": {"logType": "info", "children": [], "durationId": "41f2b4c4-7629-43c4-bf1d-c1070e9fd337"}}, {"head": {"id": "d089ad72-0982-4775-977a-2b61a4d00d5a", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427205183300, "endTime": 153427211798200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": [], "detailId": "b8da08e3-a6d5-4653-8c43-6a01bb9b3c15", "logId": "08cf5510-d1eb-4ef6-bb19-8fc3f4126c2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8da08e3-a6d5-4653-8c43-6a01bb9b3c15", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427198883400}, "additional": {"logType": "detail", "children": [], "durationId": "d089ad72-0982-4775-977a-2b61a4d00d5a"}}, {"head": {"id": "9f7f82c8-0de8-4bfe-8586-2f068c1ef108", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427200335600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6a1f62f-437b-4a2c-90e6-58004c34c210", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427200462000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7a29cc7-b852-446a-a531-aa47f4a484d5", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427205204800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cdbe4a3-8a74-4ec8-89f9-d20ea4fb0a7b", "name": "Incremental task entry:default@SignHap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427211134000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "112cacda-437c-45fe-8d13-b5ebcb8daab2", "name": "entry : default@SignHap cost memory 0.1056671142578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427211439900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08cf5510-d1eb-4ef6-bb19-8fc3f4126c2b", "name": "UP-TO-DATE :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427205183300, "endTime": 153427211798200}, "additional": {"logType": "info", "children": [], "durationId": "d089ad72-0982-4775-977a-2b61a4d00d5a"}}, {"head": {"id": "ed8583e6-27d2-4f23-8635-9fbe30322229", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427222181300, "endTime": 153427235730100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8def30a2-21e5-437e-a2ff-c73f1c42db0d", "logId": "8d1ee32a-32da-4faf-a715-bb9552237b24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8def30a2-21e5-437e-a2ff-c73f1c42db0d", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427216668700}, "additional": {"logType": "detail", "children": [], "durationId": "ed8583e6-27d2-4f23-8635-9fbe30322229"}}, {"head": {"id": "88f916a1-865a-4a99-a084-5040d7e39629", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427218940100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58929e7d-b3ab-4477-bcfe-62ac05e1e453", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427219156100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a2c9472-393a-4256-a70a-0c232e6544b2", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427222197200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8787e6c-063c-409b-8541-573e403bc537", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427234705000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dfa7084-4e18-49fb-ae73-5f9fcc3dd89a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427234969700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3db270cb-a12d-4ded-a9ee-7d6328673906", "name": "entry : default@CollectDebugSymbol cost memory 0.24306488037109375", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427235138800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a344bf70-2638-42bc-87af-68ffa2dc2297", "name": "runTaskFromQueue task cost before running: 945 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427235609200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d1ee32a-32da-4faf-a715-bb9552237b24", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427222181300, "endTime": 153427235730100, "totalTime": 13390900}, "additional": {"logType": "info", "children": [], "durationId": "ed8583e6-27d2-4f23-8635-9fbe30322229"}}, {"head": {"id": "b386f90d-accf-45ba-a180-37841d8df782", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427239017800, "endTime": 153427239601000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "b888cde9-bc86-41cc-9237-6f9239905618", "logId": "50f7e50c-1ac3-4e9e-a3a6-7b05f54250b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b888cde9-bc86-41cc-9237-6f9239905618", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427238933900}, "additional": {"logType": "detail", "children": [], "durationId": "b386f90d-accf-45ba-a180-37841d8df782"}}, {"head": {"id": "3d27c1bf-2388-4da0-9c6d-75dfc5a26e77", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427239030200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeee8d8a-0e2f-453f-bb29-984ffbc79e22", "name": "entry : assembleHap cost memory 0.011749267578125", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427239239000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd074b43-4e5f-4014-935b-12ed78eab568", "name": "runTaskFromQueue task cost before running: 949 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427239494700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50f7e50c-1ac3-4e9e-a3a6-7b05f54250b7", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427239017800, "endTime": 153427239601000, "totalTime": 445600}, "additional": {"logType": "info", "children": [], "durationId": "b386f90d-accf-45ba-a180-37841d8df782"}}, {"head": {"id": "d02cde5e-c151-429e-a1d7-241529b8cf70", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427267050600, "endTime": 153427267112100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f4a80a11-7a52-4ab1-ae41-8902329501d5", "logId": "0ce2bd78-da70-4d28-9388-bd82533442ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ce2bd78-da70-4d28-9388-bd82533442ec", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427267050600, "endTime": 153427267112100}, "additional": {"logType": "info", "children": [], "durationId": "d02cde5e-c151-429e-a1d7-241529b8cf70"}}, {"head": {"id": "1460d5c8-a790-498b-a907-59305de3eb6f", "name": "BUILD SUCCESSFUL in 977 ms ", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427267262200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "42486531-6e65-459f-9dac-95a4b8455584", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153426290827900, "endTime": 153427268002400}, "additional": {"time": {"year": 2025, "month": 6, "day": 18, "hour": 0, "minute": 34, "second": 17}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.18.4", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "bab80397-304d-4319-93c6-6486ae9da003", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427268175400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4269232-c47e-4086-8eaa-ea628cdad080", "name": "Update task entry:default@CreateModuleInfo output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\generated\\profile\\default\\ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427268323000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c58d0f1a-9d26-4add-b2c5-ff364f1bc25e", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427269163200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc6a8205-8692-4563-8237-788d9e650074", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427269342200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "703a669a-cc32-41fd-9cf0-60bd46880389", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427269473400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bf9555e-5742-40e4-9616-b54ccc1fd104", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427269589200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52088ff1-196c-4551-a9fa-867029312140", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427269698400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6691b51-10a1-4105-b35f-754a4bc14dbe", "name": "Update task entry:default@ProcessIntegratedHsp output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427270953600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "727f3bc1-4809-4756-919c-052ec3dad3f0", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427271315900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f06b92aa-3b53-4353-acc4-e98e291760e4", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427271423700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "169e00a8-e6e3-4c54-aab6-5aa27642b713", "name": "There is no need to refresh cache, since the incremental task entry:default@SyscapTransform is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427271507900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b851a08-3352-4a37-a276-f6a94b6a5c12", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427271573800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d3e3b91-7bf9-42b1-8a34-cdbe89bbd8f7", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427271637400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e97aede0-1834-4304-96f6-eacbdc39de42", "name": "Update task entry:default@ProcessStartupConfig input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427273842800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b81be324-a151-4830-b534-e0bbc6d21990", "name": "Update task entry:default@ProcessStartupConfig output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\startup\\default\\startup_config.json cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427274487700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da88f774-56e1-4f28-adb6-9c9960b94275", "name": "Incremental task entry:default@ProcessStartupConfig post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427274852300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff2c50dd-b1ad-49cc-b8e4-4a186e1466c4", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427274953100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d32ab9c3-e084-4d0f-9c9d-0c28628e1b36", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427275037900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "862941ee-1cd2-40eb-900d-77ab1ac7fa89", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427275129000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9732304a-2ecb-4b62-9d91-602758001d75", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427275208700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cee70f9-1261-401c-936b-523afb63848b", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427275282400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe60bb0a-4429-4d6e-911c-69668fed1d98", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427275354600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69a3f719-1bf1-4211-8ed4-362253a6a957", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427280220800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0938bda-063b-41f1-856d-b28d634b2a4c", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427281533400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18a525f7-d109-43f9-8756-31c9d27aa3d7", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427282276100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6073d1f-9aef-4a28-bff5-f9f1f9397bed", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427282733700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bb6b8a4-0290-4071-b80e-1794df646247", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427283168700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3884e25-a58d-4d24-a77d-e238fc664ca4", "name": "Update task entry:default@BuildJS input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427284764500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13dc888f-ef77-4045-a4c0-5728eb91f9c7", "name": "Update task entry:default@BuildJS output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427286336200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fc9e6c7-10e5-4db6-b206-2c00e47fbdd5", "name": "Incremental task entry:default@BuildJS post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427286754600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99ff8ec5-0a35-41aa-9d4c-2d8060b9abf3", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427286868400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b6b3073-666a-400b-895a-cea86214b3eb", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427286943300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5a521b6-4ddd-4e4e-899e-1c0ca8e59257", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427287019500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebba3699-1260-4563-b7cb-5d86a86558fe", "name": "There is no need to refresh cache, since the incremental task entry:default@SignHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427287090400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7454d91e-956b-4c17-ae95-8182bf557d82", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427292058000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea8dec60-c013-4e09-8d54-c22439fe9540", "name": "Update task entry:default@CollectDebugSymbol input file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427292529300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9843fc1-7cde-4a9f-a51e-cfa172b9c380", "name": "Update task entry:default@CollectDebugSymbol output file:C:\\Users\\<USER>\\Documents\\NexusHub-OH\\NexusHub\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427293483600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6419fa0f-5f0b-47bd-a1c3-5f7822d86c5d", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 25612, "tid": "Main Thread", "startTime": 153427293929800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}