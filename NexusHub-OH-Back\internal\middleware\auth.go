package middleware

import (
	"net/http"
	"strings"

	"nexushub-oh-back/pkg/auth"
	"nexushub-oh-back/pkg/logger"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// JWTAuth JWT认证中间件
func JWTAuth(jwtService *auth.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "授权已过期或未授权",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			c.JSO<PERSON>(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "授权格式错误",
			})
			c.Abort()
			return
		}

		// 验证Token
		token := parts[1]
		claims, err := jwtService.ValidateToken(token)
		if err != nil {
			logger.Error("Token验证失败", zap.Error(err))
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "无效的令牌",
			})
			c.Abort()
			return
		}

		// 将用户信息存储在上下文中
		logger.Info("JWT验证成功", zap.Uint("user_id", claims.UserID), zap.String("username", claims.Username), zap.String("role", claims.Role))
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)

		c.Next()
	}
}

// RoleAuth 角色授权中间件
func RoleAuth(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{
				"code":    403,
				"message": "权限不足",
			})
			c.Abort()
			return
		}

		userRole := role.(string)
		// 检查用户角色是否在允许的角色列表中
		for _, r := range roles {
			if r == userRole {
				c.Next()
				return
			}
		}

		// 未找到匹配的角色
		c.JSON(http.StatusForbidden, gin.H{
			"code":    403,
			"message": "权限不足",
		})
		c.Abort()
	}

}
