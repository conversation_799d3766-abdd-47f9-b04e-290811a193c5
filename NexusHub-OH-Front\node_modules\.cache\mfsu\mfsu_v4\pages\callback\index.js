"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { useEffect } from "react";
import { useLogto } from "@logto/react";
import { Spin, Result } from "antd";
import { history } from "@umijs/max";
import { useModel } from "@umijs/max";
const CallbackPage = () => {
  const { handleSignInCallback, isLoading, error } = useLogto();
  const { setInitialState } = useModel("@@initialState");
  useEffect(() => {
    const handleCallback = async () => {
      try {
        await handleSignInCallback(window.location.href);
        setInitialState((s) => ({
          ...s,
          currentUser: null
          // 这里会在后续通过 fetchUserInfo 更新
        }));
        const urlParams = new URLSearchParams(window.location.search);
        const redirectTo = urlParams.get("redirect") || "/";
        history.replace(redirectTo);
      } catch (error2) {
        console.error("\u5904\u7406\u767B\u5F55\u56DE\u8C03\u5931\u8D25:", error2);
      }
    };
    handleCallback();
  }, [handleSignInCallback, setInitialState]);
  if (isLoading) {
    return /* @__PURE__ */ jsxs("div", { style: {
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100vh",
      flexDirection: "column"
    }, children: [
      /* @__PURE__ */ jsx(Spin, { size: "large" }),
      /* @__PURE__ */ jsx("div", { style: { marginTop: 16, fontSize: 16 }, children: "\u6B63\u5728\u5904\u7406\u767B\u5F55\u4FE1\u606F..." })
    ] });
  }
  if (error) {
    return /* @__PURE__ */ jsx(
      Result,
      {
        status: "error",
        title: "\u767B\u5F55\u5931\u8D25",
        subTitle: `\u8BA4\u8BC1\u8FC7\u7A0B\u4E2D\u53D1\u751F\u9519\u8BEF: ${error.message}`,
        extra: [
          /* @__PURE__ */ jsx("a", { href: "/user/login", children: "\u8FD4\u56DE\u767B\u5F55\u9875\u9762" }, "retry")
        ]
      }
    );
  }
  return /* @__PURE__ */ jsxs("div", { style: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100vh",
    flexDirection: "column"
  }, children: [
    /* @__PURE__ */ jsx(Spin, { size: "large" }),
    /* @__PURE__ */ jsx("div", { style: { marginTop: 16, fontSize: 16 }, children: "\u6B63\u5728\u8DF3\u8F6C..." })
  ] });
};
export default CallbackPage;
