import UIAbility from "@ohos:app.ability.UIAbility";
import ConfigurationConstant from "@ohos:app.ability.ConfigurationConstant";
import type Want from "@ohos:app.ability.Want";
import type AbilityConstant from "@ohos:app.ability.AbilityConstant";
import hilog from "@ohos:hilog";
import type window from "@ohos:window";
import preferences from "@ohos:data.preferences";
import { Constants } from "@normalized:N&&&entry/src/main/ets/utils/Constants&";
import { FirstLaunchService } from "@normalized:N&&&entry/src/main/ets/services/FirstLaunchService&";
const DOMAIN = 0x0000;
export default class EntryAbility extends UIAbility {
    private firstLaunchService = FirstLaunchService.getInstance();
    onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
        this.initializeColorMode();
        hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onCreate');
    }
    /**
     * 初始化颜色模式设置
     */
    private async initializeColorMode(): Promise<void> {
        try {
            const dataPreferences = await preferences.getPreferences(this.context, 'app_settings');
            // 获取深色模式跟随系统设置
            const followSystem = await dataPreferences.get(Constants.STORAGE_KEYS.DARK_MODE_FOLLOW_SYSTEM, true) as boolean;
            if (followSystem) {
                // 跟随系统设置
                this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
                hilog.info(DOMAIN, 'EntryAbility', '深色模式设置为跟随系统');
            }
            else {
                // 手动设置
                const manualDarkMode = await dataPreferences.get(Constants.STORAGE_KEYS.DARK_MODE_MANUAL, false) as boolean;
                if (manualDarkMode) {
                    this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_DARK);
                    hilog.info(DOMAIN, 'EntryAbility', '深色模式设置为手动开启');
                }
                else {
                    this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_LIGHT);
                    hilog.info(DOMAIN, 'EntryAbility', '深色模式设置为手动关闭');
                }
            }
        }
        catch (error) {
            hilog.error(DOMAIN, 'EntryAbility', '初始化颜色模式失败: %{public}s', JSON.stringify(error));
            // 默认跟随系统
            this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
        }
    }
    onDestroy(): void {
        hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onDestroy');
    }
    onWindowStageCreate(windowStage: window.WindowStage): void {
        // Main window is created, set main page for this ability
        hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onWindowStageCreate');
        this.loadInitialPage(windowStage);
    }
    /**
     * 根据首次启动状态加载初始页面
     */
    private async loadInitialPage(windowStage: window.WindowStage): Promise<void> {
        try {
            // 初始化首次启动服务
            await this.firstLaunchService.initialize(this.context);
            // 检查是否为首次启动
            const isFirstLaunch = await this.firstLaunchService.isFirstLaunch();
            // 根据首次启动状态决定加载的页面
            const targetPage = isFirstLaunch ? 'pages/WelcomePage' : 'pages/Index';
            hilog.info(DOMAIN, 'EntryAbility', '加载页面: %{public}s (首次启动: %{public}s)', targetPage, isFirstLaunch ? '是' : '否');
            windowStage.loadContent(targetPage, (err) => {
                if (err.code) {
                    hilog.error(DOMAIN, 'testTag', 'Failed to load the content. Cause: %{public}s', JSON.stringify(err));
                    // 如果加载失败，尝试加载默认页面
                    this.loadFallbackPage(windowStage);
                    return;
                }
                hilog.info(DOMAIN, 'testTag', 'Succeeded in loading the content: %{public}s', targetPage);
            });
        }
        catch (error) {
            hilog.error(DOMAIN, 'EntryAbility', '初始化首次启动检测失败: %{public}s', JSON.stringify(error));
            // 出错时加载默认页面
            this.loadFallbackPage(windowStage);
        }
    }
    /**
     * 加载备用页面（出错时使用）
     */
    private loadFallbackPage(windowStage: window.WindowStage): void {
        windowStage.loadContent('pages/Index', (err) => {
            if (err.code) {
                hilog.error(DOMAIN, 'testTag', 'Failed to load fallback page. Cause: %{public}s', JSON.stringify(err));
                return;
            }
            hilog.info(DOMAIN, 'testTag', 'Succeeded in loading fallback page.');
        });
    }
    onWindowStageDestroy(): void {
        // Main window is destroyed, release UI related resources
        hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onWindowStageDestroy');
    }
    onForeground(): void {
        // Ability has brought to foreground
        hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onForeground');
    }
    onBackground(): void {
        // Ability has back to background
        hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onBackground');
    }
}
