"use strict";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
import { PageContainer } from "@ant-design/pro-components";
import { Card, Descriptions, Button, Tabs, Table, Tag, Avatar, Badge, Space, message, Modal, Spin } from "antd";
import { UserOutlined, MailOutlined, PhoneOutlined, LockOutlined, UnlockOutlined, EditOutlined, RollbackOutlined } from "@ant-design/icons";
import React, { useState } from "react";
import { useRequest, history, useParams } from "@umijs/max";
import { getUserDetail, getUserLoginRecords, getUserAppRecords, toggleUserLockStatus } from "@/services/user";
const fetchUserDetail = async (id) => {
  if (!id) {
    throw new Error("\u7528\u6237ID\u4E0D\u80FD\u4E3A\u7A7A");
  }
  console.log("Fetching user detail with id:", id);
  try {
    return await getUserDetail(id);
  } catch (error) {
    console.error("\u83B7\u53D6\u7528\u6237\u8BE6\u60C5\u5931\u8D25:", error);
    throw error;
  }
};
const fetchLoginRecords = async (userId) => {
  if (!userId) {
    throw new Error("\u7528\u6237ID\u4E0D\u80FD\u4E3A\u7A7A");
  }
  console.log("Fetching login records for user:", userId);
  try {
    return await getUserLoginRecords(userId);
  } catch (error) {
    console.error("\u83B7\u53D6\u7528\u6237\u767B\u5F55\u8BB0\u5F55\u5931\u8D25:", error);
    throw error;
  }
};
const fetchAppRecords = async (userId) => {
  if (!userId) {
    throw new Error("\u7528\u6237ID\u4E0D\u80FD\u4E3A\u7A7A");
  }
  console.log("Fetching app records for user:", userId);
  try {
    return await getUserAppRecords(userId);
  } catch (error) {
    console.error("\u83B7\u53D6\u7528\u6237\u5E94\u7528\u8BB0\u5F55\u5931\u8D25:", error);
    throw error;
  }
};
const toggleUserStatus = async (userId, action, reason) => {
  if (!userId) {
    throw new Error("\u7528\u6237ID\u4E0D\u80FD\u4E3A\u7A7A");
  }
  try {
    return await toggleUserLockStatus(userId, action, reason);
  } catch (error) {
    console.error(`${action === "lock" ? "\u9501\u5B9A" : "\u89E3\u9501"}\u7528\u6237\u5931\u8D25:`, error);
    throw error;
  }
};
const UserDetail = () => {
  const { id } = useParams();
  const [lockModalVisible, setLockModalVisible] = useState(false);
  const [lockReason, setLockReason] = useState("");
  const [lockLoading, setLockLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  console.log("\u7528\u6237\u8BE6\u60C5\u9875\u9762\u63A5\u6536\u5230\u7684ID:", id);
  React.useEffect(() => {
    if (!id) {
      message.error("\u65E0\u6548\u7684\u7528\u6237ID");
      history.push("/user-management/list");
    }
  }, [id]);
  const { data: userData, loading: userLoading, error: userError, refresh: refreshUserData } = useRequest(
    () => fetchUserDetail(id || ""),
    {
      refreshDeps: [id],
      onError: (error) => {
        setErrorMessage(`\u83B7\u53D6\u7528\u6237\u8BE6\u60C5\u5931\u8D25: ${error.message}`);
        message.error("\u83B7\u53D6\u7528\u6237\u8BE6\u60C5\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
      },
      onSuccess: (data) => {
      }
    }
  );
  const { data: loginRecords, loading: loginLoading, error: loginError } = useRequest(
    () => fetchLoginRecords(id || ""),
    {
      refreshDeps: [id],
      onError: (error) => {
        console.log("\u83B7\u53D6\u767B\u5F55\u8BB0\u5F55\u9519\u8BEF:", error);
        message.error("\u83B7\u53D6\u767B\u5F55\u8BB0\u5F55\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
      }
    }
  );
  const { data: appRecords, loading: appLoading, error: appError } = useRequest(
    () => fetchAppRecords(id || ""),
    {
      refreshDeps: [id],
      onError: (error) => {
        console.log("\u83B7\u53D6\u5E94\u7528\u8BB0\u5F55\u9519\u8BEF:", error);
        message.error("\u83B7\u53D6\u5E94\u7528\u8BB0\u5F55\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
      }
    }
  );
  const handleBack = () => {
    history.push("/user-management/list");
  };
  const handleEdit = () => {
    message.info(`\u7F16\u8F91\u7528\u6237 ${id}`);
  };
  const handleLock = () => {
    setLockModalVisible(true);
  };
  const handleLockConfirm = async () => {
    if (!userData || !id) return;
    const action = userData.status === "banned" ? "unlock" : "lock";
    const actionText = action === "lock" ? "\u9501\u5B9A" : "\u89E3\u9501";
    try {
      setLockLoading(true);
      await toggleUserStatus(id, action, action === "lock" ? lockReason : void 0);
      message.success(`\u7528\u6237 ${userData.username} \u5DF2${actionText}`);
      setLockModalVisible(false);
      setLockReason("");
      refreshUserData();
    } catch (error) {
      if (error instanceof Error) {
        message.error(`${actionText}\u7528\u6237\u5931\u8D25: ${error.message}`);
      } else {
        message.error(`${actionText}\u7528\u6237\u5931\u8D25`);
      }
    } finally {
      setLockLoading(false);
    }
  };
  const getRoleTag = (role) => {
    switch (role) {
      case "admin":
        return /* @__PURE__ */ jsx(Tag, { color: "red", children: "\u7BA1\u7406\u5458" });
      case "developer":
        return /* @__PURE__ */ jsx(Tag, { color: "blue", children: "\u5F00\u53D1\u8005" });
      case "user":
        return /* @__PURE__ */ jsx(Tag, { color: "green", children: "\u666E\u901A\u7528\u6237" });
      default:
        return /* @__PURE__ */ jsx(Tag, { children: role });
    }
  };
  const getStatusBadge = (status) => {
    switch (status) {
      case "active":
        return /* @__PURE__ */ jsx(Badge, { status: "success", text: "\u6D3B\u8DC3" });
      case "inactive":
        return /* @__PURE__ */ jsx(Badge, { status: "default", text: "\u975E\u6D3B\u8DC3" });
      case "banned":
        return /* @__PURE__ */ jsx(Badge, { status: "error", text: "\u5DF2\u9501\u5B9A" });
      default:
        return /* @__PURE__ */ jsx(Badge, { status: "processing", text: status });
    }
  };
  const loginColumns = [
    {
      title: "\u767B\u5F55\u65F6\u95F4",
      dataIndex: "loginTime",
      key: "loginTime",
      sorter: (a, b) => (a.loginTime || "").localeCompare(b.loginTime || ""),
      defaultSortOrder: "descend"
    },
    {
      title: "IP\u5730\u5740",
      dataIndex: "ip",
      key: "ip"
    },
    {
      title: "\u8BBE\u5907",
      dataIndex: "device",
      key: "device"
    },
    {
      title: "\u4F4D\u7F6E",
      dataIndex: "location",
      key: "location"
    },
    {
      title: "\u72B6\u6001",
      dataIndex: "status",
      key: "status",
      render: (text) => {
        if (text === "success") {
          return /* @__PURE__ */ jsx(Badge, { status: "success", text: "\u6210\u529F" });
        }
        return /* @__PURE__ */ jsx(Badge, { status: "error", text: "\u5931\u8D25" });
      }
    }
  ];
  const appColumns = [
    {
      title: "\u5E94\u7528\u540D\u79F0",
      dataIndex: "appName",
      key: "appName"
    },
    {
      title: "\u4E0B\u8F7D\u65F6\u95F4",
      dataIndex: "downloadTime",
      key: "downloadTime",
      sorter: (a, b) => (a.downloadTime || "").localeCompare(b.downloadTime || ""),
      defaultSortOrder: "descend"
    },
    {
      title: "\u7248\u672C",
      dataIndex: "version",
      key: "version"
    },
    {
      title: "\u72B6\u6001",
      dataIndex: "status",
      key: "status",
      render: (text) => {
        switch (text) {
          case "installed":
            return /* @__PURE__ */ jsx(Badge, { status: "success", text: "\u5DF2\u5B89\u88C5" });
          case "uninstalled":
            return /* @__PURE__ */ jsx(Badge, { status: "default", text: "\u5DF2\u5378\u8F7D" });
          case "updated":
            return /* @__PURE__ */ jsx(Badge, { status: "processing", text: "\u5DF2\u66F4\u65B0" });
          default:
            return /* @__PURE__ */ jsx(Badge, { status: "default", text });
        }
      }
    }
  ];
  if (userLoading) {
    return /* @__PURE__ */ jsx(PageContainer, { children: /* @__PURE__ */ jsx(Card, { loading: true, variant: "borderless", children: /* @__PURE__ */ jsx("div", { style: { textAlign: "center", padding: "50px 0" }, children: /* @__PURE__ */ jsx(Spin, { size: "large", tip: "\u52A0\u8F7D\u7528\u6237\u6570\u636E\u4E2D..." }) }) }) });
  }
  if (errorMessage || userError) {
    return /* @__PURE__ */ jsx(
      PageContainer,
      {
        header: {
          title: "\u7528\u6237\u8BE6\u60C5",
          onBack: handleBack
        },
        children: /* @__PURE__ */ jsx(Card, { variant: "borderless", children: /* @__PURE__ */ jsxs("div", { style: { textAlign: "center", padding: "50px 0" }, children: [
          /* @__PURE__ */ jsx("h3", { style: { color: "#ff4d4f" }, children: errorMessage || "\u65E0\u6CD5\u52A0\u8F7D\u7528\u6237\u6570\u636E" }),
          /* @__PURE__ */ jsx("p", { children: "\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5\u6216\u7A0D\u540E\u91CD\u8BD5" }),
          /* @__PURE__ */ jsx(Button, { type: "primary", onClick: handleBack, children: "\u8FD4\u56DE\u7528\u6237\u5217\u8868" })
        ] }) })
      }
    );
  }
  const userDetailData = userData;
  if (!userDetailData) {
    console.log("DEBUG: \u8FDB\u5165 !userDetailData \u5206\u652F\uFF0C\u663E\u793A\u52A0\u8F7D\u72B6\u6001");
    return /* @__PURE__ */ jsx(PageContainer, { header: { title: "\u7528\u6237\u8BE6\u60C5", onBack: handleBack }, children: /* @__PURE__ */ jsx("div", { style: { textAlign: "center", padding: "50px" }, children: /* @__PURE__ */ jsx(Spin, { size: "large", spinning: true, tip: "\u52A0\u8F7D\u7528\u6237\u8BE6\u60C5\u4E2D...", children: /* @__PURE__ */ jsx("div", { style: { minHeight: "200px" } }) }) }) });
  }
  if (userLoading) {
    return /* @__PURE__ */ jsx(PageContainer, { header: { title: "\u7528\u6237\u8BE6\u60C5", onBack: handleBack }, children: /* @__PURE__ */ jsx("div", { style: { textAlign: "center", padding: "50px" }, children: /* @__PURE__ */ jsx(Spin, { size: "large", spinning: true, tip: "\u52A0\u8F7D\u7528\u6237\u8BE6\u60C5\u4E2D...", children: /* @__PURE__ */ jsx("div", { style: { minHeight: "200px" } }) }) }) });
  }
  if (userError && !userData) {
    return /* @__PURE__ */ jsx(PageContainer, { header: { title: "\u7528\u6237\u8BE6\u60C5", onBack: handleBack }, children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs("div", { style: { textAlign: "center", padding: "50px" }, children: [
      /* @__PURE__ */ jsx("h3", { style: { color: "#ff4d4f", marginBottom: "20px" }, children: "\u83B7\u53D6\u7528\u6237\u8BE6\u60C5\u5931\u8D25" }),
      /* @__PURE__ */ jsx("p", { children: "\u53EF\u80FD\u662F\u7F51\u7EDC\u95EE\u9898\u6216\u8BE5\u7528\u6237\u4E0D\u5B58\u5728" }),
      /* @__PURE__ */ jsx(Button, { type: "primary", onClick: () => window.location.reload(), style: { marginTop: "20px" }, children: "\u91CD\u8BD5" })
    ] }) }) });
  }
  return /* @__PURE__ */ jsxs(
    PageContainer,
    {
      header: {
        title: "\u7528\u6237\u8BE6\u60C5",
        onBack: handleBack,
        extra: [
          /* @__PURE__ */ jsx(Button, { type: "primary", icon: /* @__PURE__ */ jsx(EditOutlined, {}), onClick: handleEdit, children: "\u7F16\u8F91" }, "edit"),
          /* @__PURE__ */ jsx(
            Button,
            {
              danger: userDetailData.status !== "banned",
              icon: userDetailData.status === "banned" ? /* @__PURE__ */ jsx(UnlockOutlined, {}) : /* @__PURE__ */ jsx(LockOutlined, {}),
              onClick: handleLock,
              children: userDetailData.status === "banned" ? "\u89E3\u9501" : "\u9501\u5B9A"
            },
            "lock"
          ),
          /* @__PURE__ */ jsx(Button, { icon: /* @__PURE__ */ jsx(RollbackOutlined, {}), onClick: handleBack, children: "\u8FD4\u56DE" }, "back")
        ]
      },
      children: [
        /* @__PURE__ */ jsx(Card, { loading: userLoading, variant: "borderless", children: /* @__PURE__ */ jsxs(Space, { direction: "vertical", size: "large", style: { width: "100%" }, children: [
          /* @__PURE__ */ jsxs(Space, { align: "start", size: "large", style: { width: "100%" }, children: [
            /* @__PURE__ */ jsx(Card, { variant: "borderless", children: /* @__PURE__ */ jsxs(Space, { direction: "vertical", size: "large", style: { width: "100%", textAlign: "center" }, children: [
              /* @__PURE__ */ jsx(
                Avatar,
                {
                  size: 120,
                  src: userDetailData.avatar,
                  icon: /* @__PURE__ */ jsx(UserOutlined, {})
                }
              ),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("h2", { children: userDetailData.nickname }),
                /* @__PURE__ */ jsxs("p", { children: [
                  "@",
                  userDetailData.username
                ] }),
                /* @__PURE__ */ jsxs("div", { children: [
                  getRoleTag(userDetailData.role),
                  getStatusBadge(userDetailData.status)
                ] })
              ] }),
              /* @__PURE__ */ jsx("div", { children: /* @__PURE__ */ jsxs(Space, { children: [
                /* @__PURE__ */ jsx(Button, { type: "primary", shape: "circle", icon: /* @__PURE__ */ jsx(UserOutlined, {}) }),
                /* @__PURE__ */ jsx(Button, { type: "primary", shape: "circle", icon: /* @__PURE__ */ jsx(MailOutlined, {}) }),
                /* @__PURE__ */ jsx(Button, { type: "primary", shape: "circle", icon: /* @__PURE__ */ jsx(PhoneOutlined, {}) })
              ] }) })
            ] }) }),
            /* @__PURE__ */ jsx(Card, { style: { flex: 1 }, children: /* @__PURE__ */ jsxs(Descriptions, { bordered: true, column: 2, size: "small", children: [
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u7528\u6237ID", children: userDetailData.id }),
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u72B6\u6001", children: getStatusBadge(userDetailData.status) }),
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u7528\u6237\u540D", children: userDetailData.username }),
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u6635\u79F0", children: userDetailData.nickname || userDetailData.username }),
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u90AE\u7BB1", children: userDetailData.email }),
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u624B\u673A", children: userDetailData.phone || "\u672A\u8BBE\u7F6E" }),
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u6CE8\u518C\u65F6\u95F4", children: new Date(userDetailData.created_at).toLocaleString() }),
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u6700\u540E\u767B\u5F55", children: new Date(userDetailData.last_login_at).toLocaleString() }),
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u767B\u5F55\u6B21\u6570", children: userDetailData.login_count }),
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u662F\u5426\u5F00\u53D1\u8005", children: userDetailData.is_developer ? "\u662F" : "\u5426" }),
              /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u4E2A\u4EBA\u7B80\u4ECB", span: 2, children: userDetailData.description || "\u6682\u65E0\u7B80\u4ECB" })
            ] }) })
          ] }),
          userDetailData.is_developer && /* @__PURE__ */ jsx(Card, { title: "\u5F00\u53D1\u8005\u4FE1\u606F", variant: "borderless", children: /* @__PURE__ */ jsxs(Descriptions, { bordered: true, column: 2, children: [
            /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u5F00\u53D1\u8005\u540D\u79F0", children: userDetailData.developer_name || "\u672A\u8BBE\u7F6E" }),
            /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u8BA4\u8BC1\u72B6\u6001", children: userDetailData.verify_status === "approved" ? /* @__PURE__ */ jsx(Badge, { status: "success", text: "\u5DF2\u8BA4\u8BC1" }) : userDetailData.verify_status === "pending" ? /* @__PURE__ */ jsx(Badge, { status: "processing", text: "\u5BA1\u6838\u4E2D" }) : /* @__PURE__ */ jsx(Badge, { status: "error", text: "\u672A\u901A\u8FC7" }) }),
            /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u516C\u53F8\u540D\u79F0", children: userDetailData.company_name || "\u672A\u8BBE\u7F6E" }),
            /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u7F51\u7AD9", children: userDetailData.website || "\u672A\u8BBE\u7F6E" }),
            /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u8054\u7CFB\u90AE\u7BB1", children: userDetailData.contact_email || "\u672A\u8BBE\u7F6E" }),
            /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u8054\u7CFB\u7535\u8BDD", children: userDetailData.contact_phone || "\u672A\u8BBE\u7F6E" }),
            /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u5730\u5740", children: userDetailData.developer_address || "\u672A\u8BBE\u7F6E" }),
            userDetailData.submitted_at && /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u63D0\u4EA4\u65F6\u95F4", children: new Date(userDetailData.submitted_at).toLocaleString() }),
            userDetailData.verified_at && /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u8BA4\u8BC1\u65F6\u95F4", children: new Date(userDetailData.verified_at).toLocaleString() }),
            userDetailData.verify_reason && /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u5BA1\u6838\u610F\u89C1", span: 2, children: userDetailData.verify_reason })
          ] }) }),
          /* @__PURE__ */ jsx(Card, { variant: "borderless", children: /* @__PURE__ */ jsxs(Tabs, { defaultActiveKey: "login", children: [
            /* @__PURE__ */ jsx(Tabs.TabPane, { tab: "\u767B\u5F55\u8BB0\u5F55", children: /* @__PURE__ */ jsx(
              Table,
              {
                columns: loginColumns,
                dataSource: loginRecords?.data,
                rowKey: "id",
                loading: loginLoading
              }
            ) }, "login"),
            /* @__PURE__ */ jsx(Tabs.TabPane, { tab: "\u5E94\u7528\u8BB0\u5F55", children: /* @__PURE__ */ jsx(
              Table,
              {
                columns: appColumns,
                dataSource: appRecords?.data,
                rowKey: "id",
                loading: appLoading
              }
            ) }, "app")
          ] }) })
        ] }) }),
        /* @__PURE__ */ jsxs(
          Modal,
          {
            title: userDetailData.status === "banned" ? "\u89E3\u9501\u7528\u6237" : "\u9501\u5B9A\u7528\u6237",
            open: lockModalVisible,
            onOk: handleLockConfirm,
            onCancel: () => setLockModalVisible(false),
            confirmLoading: lockLoading,
            okText: lockLoading ? "\u5904\u7406\u4E2D..." : "\u786E\u8BA4",
            cancelButtonProps: { disabled: lockLoading },
            children: [
              userDetailData.status !== "banned" && /* @__PURE__ */ jsxs(Fragment, { children: [
                /* @__PURE__ */ jsxs("p", { children: [
                  "\u786E\u5B9A\u8981\u9501\u5B9A\u7528\u6237 ",
                  /* @__PURE__ */ jsx("strong", { children: userDetailData.username }),
                  " \u5417\uFF1F"
                ] }),
                /* @__PURE__ */ jsx("p", { children: "\u9501\u5B9A\u539F\u56E0\uFF1A" }),
                /* @__PURE__ */ jsx(
                  "textarea",
                  {
                    rows: 4,
                    style: { width: "100%" },
                    value: lockReason,
                    onChange: (e) => setLockReason(e.target.value),
                    disabled: lockLoading,
                    placeholder: "\u8BF7\u8F93\u5165\u9501\u5B9A\u7528\u6237\u7684\u539F\u56E0"
                  }
                )
              ] }),
              userDetailData.status === "banned" && /* @__PURE__ */ jsxs("p", { children: [
                "\u786E\u5B9A\u8981\u89E3\u9501\u7528\u6237 ",
                /* @__PURE__ */ jsx("strong", { children: userDetailData.username }),
                " \u5417\uFF1F"
              ] })
            ]
          }
        )
      ]
    }
  );
};
export default UserDetail;
