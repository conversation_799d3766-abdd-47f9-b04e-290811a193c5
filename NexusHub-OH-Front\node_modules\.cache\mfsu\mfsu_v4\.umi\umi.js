"use strict";
import "./core/polyfill";
import "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/src/global.less";
import "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/src/global.tsx";
import "antd/dist/reset.css";
import { renderClient } from "C:/Users/<USER>/Documents/NexusHub-OH/NexusHub-OH-Front/node_modules/.pnpm/@umijs+renderer-react@4.4.1_5d37cdc93ae4c557f74cbc834d273583/node_modules/@umijs/renderer-react";
import { getRoutes } from "./core/route";
import { createPluginManager } from "./core/plugin";
import { createHistory } from "./core/history";
import { ApplyPluginsType } from "umi";
const publicPath = "/";
const runtimePublicPath = false;
async function render() {
  const pluginManager = createPluginManager();
  const { routes, routeComponents } = await getRoutes(pluginManager);
  await pluginManager.applyPlugins({
    key: "patchRoutes",
    type: ApplyPluginsType.event,
    args: {
      routes,
      routeComponents
    }
  });
  const contextOpts = pluginManager.applyPlugins({
    key: "modifyContextOpts",
    type: ApplyPluginsType.modify,
    initialValue: {}
  });
  const basename = contextOpts.basename || "/";
  const historyType = contextOpts.historyType || "browser";
  const history = createHistory({
    type: historyType,
    basename,
    ...contextOpts.historyOpts
  });
  return pluginManager.applyPlugins({
    key: "render",
    type: ApplyPluginsType.compose,
    initialValue() {
      const context = {
        useStream: true,
        routes,
        routeComponents,
        pluginManager,
        mountElementId: "root",
        rootElement: contextOpts.rootElement || document.getElementById("root"),
        publicPath,
        runtimePublicPath,
        history,
        historyType,
        basename,
        __INTERNAL_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: { "pureApp": false, "pureHtml": false },
        callback: contextOpts.callback
      };
      const modifiedContext = pluginManager.applyPlugins({
        key: "modifyClientRenderOpts",
        type: ApplyPluginsType.modify,
        initialValue: context
      });
      return renderClient(modifiedContext);
    }
  })();
}
import "./plugin-moment2dayjs/runtime.tsx";
render();
if (typeof window !== "undefined") {
  window.g_umi = {
    version: "4.4.11"
  };
}
