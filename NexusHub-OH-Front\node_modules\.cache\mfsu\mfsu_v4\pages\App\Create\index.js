"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { PageContainer } from "@ant-design/pro-components";
import { Card, Form, Input, Button, Select, Upload, InputNumber, Space, message, Alert } from "antd";
import { InboxOutlined, PlusOutlined } from "@ant-design/icons";
import { history, useModel } from "@umijs/max";
import { useState, useEffect } from "react";
import { uploadFile } from "@/services/upload";
import { createApp, submitAppForReview } from "@/services/app";
import { getTags } from "@/services/ant-design-pro/biaoqianguanli";
import { getCategories } from "@/services/ant-design-pro/fenleiguanli";
const { Option } = Select;
const { TextArea } = Input;
const AppCreate = () => {
  const [form] = Form.useForm();
  const [uploadLoading, setUploadLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState("");
  const [iconUrl, setIconUrl] = useState("");
  const [packageUrl, setPackageUrl] = useState("");
  const [screenshotUrls, setScreenshotUrls] = useState([]);
  const [tagsList, setTagsList] = useState([]);
  const [tagsLoading, setTagsLoading] = useState(false);
  const [categoriesList, setCategoriesList] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(false);
  const { initialState } = useModel("@@initialState");
  const currentUser = initialState?.currentUser;
  const isAdmin = currentUser?.role === "admin";
  const fetchTags = async () => {
    try {
      setTagsLoading(true);
      const response = await getTags({ include_inactive: false });
      if (response && Array.isArray(response)) {
        setTagsList(response);
      }
    } catch (error) {
      console.error("\u83B7\u53D6\u6807\u7B7E\u5217\u8868\u5931\u8D25:", error);
      message.error("\u83B7\u53D6\u6807\u7B7E\u5217\u8868\u5931\u8D25");
    } finally {
      setTagsLoading(false);
    }
  };
  const fetchCategories = async () => {
    try {
      setCategoriesLoading(true);
      const response = await getCategories({ include_inactive: false });
      if (response && Array.isArray(response)) {
        setCategoriesList(response);
      }
    } catch (error) {
      console.error("\u83B7\u53D6\u5206\u7C7B\u5217\u8868\u5931\u8D25:", error);
      message.error("\u83B7\u53D6\u5206\u7C7B\u5217\u8868\u5931\u8D25");
    } finally {
      setCategoriesLoading(false);
    }
  };
  useEffect(() => {
    fetchTags();
    fetchCategories();
  }, []);
  const handleIconChange = async (info) => {
    if (info.file.status === "uploading") {
      setUploadLoading(true);
      return;
    }
    if (info.file.status === "done") {
      getBase64(info.file.originFileObj, (url) => {
        setPreviewUrl(url);
      });
    } else if (info.file.status === "error") {
      setUploadLoading(false);
      message.error(`${info.file.name} \u4E0A\u4F20\u5931\u8D25`);
    }
  };
  const getBase64 = (file, callback) => {
    const reader = new FileReader();
    reader.addEventListener("load", () => callback(reader.result));
    reader.readAsDataURL(file);
  };
  const customUploadIcon = async (options) => {
    const { file, onSuccess, onError } = options;
    try {
      setUploadLoading(true);
      const url = await uploadFile("avatar", file);
      setIconUrl(url);
      form.setFieldsValue({ icon: url });
      setUploadLoading(false);
      onSuccess(null, new Response(new Blob(), { status: 200 }));
      message.success(`${file.name} \u4E0A\u4F20\u6210\u529F`);
    } catch (error) {
      setUploadLoading(false);
      onError(error);
      message.error(`${file.name} \u4E0A\u4F20\u5931\u8D25`);
    }
  };
  const customUploadPackage = async (options) => {
    const { file, onSuccess, onError } = options;
    try {
      const url = await uploadFile("package", file);
      setPackageUrl(url);
      form.setFieldsValue({ appPackage: url });
      onSuccess(null, new Response(new Blob(), { status: 200 }));
      message.success(`${file.name} \u4E0A\u4F20\u6210\u529F`);
    } catch (error) {
      onError(error);
      message.error(`${file.name} \u4E0A\u4F20\u5931\u8D25`);
    }
  };
  const customUploadScreenshot = async (options) => {
    const { file, onSuccess, onError } = options;
    try {
      const url = await uploadFile("screenshot", file);
      const newScreenshotUrls = [...screenshotUrls, url];
      setScreenshotUrls(newScreenshotUrls);
      form.setFieldsValue({ screenshots: newScreenshotUrls });
      onSuccess(null, new Response(new Blob(), { status: 200 }));
      message.success(`${file.name} \u4E0A\u4F20\u6210\u529F`);
    } catch (error) {
      onError(error);
      message.error(`${file.name} \u4E0A\u4F20\u5931\u8D25`);
    }
  };
  const handleSaveDraft = async (values) => {
    const formData = {
      name: values.name,
      package: values.package,
      description: values.description,
      short_desc: values.short_desc,
      icon: iconUrl,
      category: String(values.category),
      // 确保category为字符串
      current_version: values.version,
      size: values.size,
      min_open_harmony_os_ver: values.min_open_harmony_os_ver || "1.0.0",
      // 提供默认值
      tags: Array.isArray(values.tags) ? values.tags.join(",") : values.tags,
      // 处理tags格式
      website_url: values.website_url,
      privacy_url: values.privacy_url
    };
    console.log("\u4FDD\u5B58\u8349\u7A3F\u7684\u8868\u5355\u6570\u636E:", formData);
    try {
      await createApp(formData);
      message.success("\u5E94\u7528\u5DF2\u4FDD\u5B58\u81F3\u8349\u7A3F");
      history.push("/app/list");
    } catch (error) {
      message.error("\u4FDD\u5B58\u8349\u7A3F\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5");
      console.error("\u4FDD\u5B58\u8349\u7A3F\u5931\u8D25:", error);
    }
  };
  const handleSubmit = async (values) => {
    const formData = {
      name: values.name,
      package: values.package,
      description: values.description,
      short_desc: values.short_desc,
      icon: iconUrl,
      category: String(values.category),
      // 确保category为字符串
      current_version: values.version,
      size: values.size,
      min_open_harmony_os_ver: values.min_open_harmony_os_ver || "1.0.0",
      // 提供默认值
      tags: Array.isArray(values.tags) ? values.tags.join(",") : values.tags,
      // 处理tags格式
      website_url: values.website_url,
      privacy_url: values.privacy_url
    };
    console.log("\u63D0\u4EA4\u7684\u8868\u5355\u6570\u636E:", formData);
    try {
      const response = await createApp(formData);
      const appId = response?.data?.id || response?.id;
      if (isAdmin) {
        message.success("\u5E94\u7528\u521B\u5EFA\u6210\u529F\uFF0C\u5DF2\u76F4\u63A5\u4E0A\u67B6");
      } else {
        if (appId) {
          try {
            await submitAppForReview(appId.toString());
            message.success("\u5E94\u7528\u521B\u5EFA\u6210\u529F\uFF0C\u5DF2\u63D0\u4EA4\u5BA1\u6838\uFF0C\u8BF7\u7B49\u5F85\u7BA1\u7406\u5458\u5BA1\u6838");
          } catch (submitError) {
            console.error("\u63D0\u4EA4\u5BA1\u6838\u5931\u8D25:", submitError);
            message.warning("\u5E94\u7528\u521B\u5EFA\u6210\u529F\uFF0C\u4F46\u63D0\u4EA4\u5BA1\u6838\u5931\u8D25\uFF0C\u8BF7\u5728\u5E94\u7528\u5217\u8868\u4E2D\u624B\u52A8\u63D0\u4EA4\u5BA1\u6838");
          }
        } else {
          message.success("\u5E94\u7528\u521B\u5EFA\u6210\u529F\uFF0C\u8BF7\u5728\u5E94\u7528\u5217\u8868\u4E2D\u63D0\u4EA4\u5BA1\u6838");
        }
      }
      history.push("/app/list");
    } catch (error) {
      message.error("\u521B\u5EFA\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5");
      console.error("\u521B\u5EFA\u5E94\u7528\u5931\u8D25:", error);
    }
  };
  return /* @__PURE__ */ jsxs(
    PageContainer,
    {
      header: {
        title: "\u521B\u5EFA\u5E94\u7528",
        subTitle: "\u53D1\u5E03\u65B0\u5E94\u7528\u5230\u5E94\u7528\u5546\u5E97"
      },
      children: [
        isAdmin ? /* @__PURE__ */ jsx(
          Alert,
          {
            message: "\u7BA1\u7406\u5458\u6743\u9650",
            description: "\u60A8\u521B\u5EFA\u7684\u5E94\u7528\u5C06\u76F4\u63A5\u4E0A\u67B6\uFF0C\u65E0\u9700\u5BA1\u6838\u6D41\u7A0B\u3002",
            type: "info",
            showIcon: true,
            style: { marginBottom: 16 }
          }
        ) : /* @__PURE__ */ jsx(
          Alert,
          {
            message: "\u5F00\u53D1\u8005\u6743\u9650",
            description: "\u60A8\u521B\u5EFA\u7684\u5E94\u7528\u9700\u8981\u63D0\u4EA4\u7ED9\u7BA1\u7406\u5458\u5BA1\u6838\uFF0C\u5BA1\u6838\u901A\u8FC7\u540E\u624D\u80FD\u4E0A\u67B6\u3002",
            type: "warning",
            showIcon: true,
            style: { marginBottom: 16 }
          }
        ),
        /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(
          Form,
          {
            form,
            layout: "vertical",
            onFinish: handleSubmit,
            initialValues: {
              // 移除price默认值，让占位符正常显示
            },
            children: [
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "name",
                  label: "\u5E94\u7528\u540D\u79F0",
                  rules: [
                    { required: true, message: "\u8BF7\u8F93\u5165\u5E94\u7528\u540D\u79F0" },
                    { min: 2, max: 100, message: "\u5E94\u7528\u540D\u79F0\u957F\u5EA6\u5FC5\u987B\u57282-100\u4E2A\u5B57\u7B26\u4E4B\u95F4" }
                  ],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u5E94\u7528\u540D\u79F0" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "package",
                  label: "\u5305\u540D",
                  rules: [
                    { required: true, message: "\u8BF7\u8F93\u5165\u5305\u540D" },
                    { pattern: /^[a-z][a-z0-9_]*(\.[a-z0-9_]+)+[0-9a-z_]$/i, message: "\u5305\u540D\u683C\u5F0F\u4E0D\u6B63\u786E" }
                  ],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u5305\u540D\uFF0C\u4F8B\u5982\uFF1Acom.example.app" })
                }
              ),
              /* @__PURE__ */ jsxs(
                Form.Item,
                {
                  name: "icon",
                  label: "\u5E94\u7528\u56FE\u6807",
                  rules: [{ required: true, message: "\u8BF7\u4E0A\u4F20\u5E94\u7528\u56FE\u6807" }],
                  children: [
                    /* @__PURE__ */ jsx(
                      Upload,
                      {
                        name: "icon",
                        listType: "picture-card",
                        showUploadList: false,
                        customRequest: customUploadIcon,
                        onChange: handleIconChange,
                        beforeUpload: (file) => {
                          const isImage = file.type.startsWith("image/");
                          if (!isImage) {
                            message.error("\u53EA\u80FD\u4E0A\u4F20\u56FE\u7247\u6587\u4EF6!");
                          }
                          const isLt2M = file.size / 1024 / 1024 < 2;
                          if (!isLt2M) {
                            message.error("\u56FE\u7247\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC72MB!");
                          }
                          return isImage && isLt2M;
                        },
                        children: previewUrl ? /* @__PURE__ */ jsx("img", { src: previewUrl, alt: "\u5E94\u7528\u56FE\u6807", style: { width: "100%" } }) : /* @__PURE__ */ jsxs("div", { children: [
                          /* @__PURE__ */ jsx(PlusOutlined, {}),
                          /* @__PURE__ */ jsx("div", { style: { marginTop: 8 }, children: "\u4E0A\u4F20" })
                        ] })
                      }
                    ),
                    /* @__PURE__ */ jsx("div", { style: { marginTop: 8 }, children: "\u5EFA\u8BAE\u5C3A\u5BF8: 512x512px, PNG\u683C\u5F0F" })
                  ]
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "developer",
                  label: "\u5F00\u53D1\u8005",
                  rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u5F00\u53D1\u8005\u540D\u79F0" }],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u5F00\u53D1\u8005\u540D\u79F0" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "category",
                  label: "\u5E94\u7528\u5206\u7C7B",
                  rules: [{ required: true, message: "\u8BF7\u9009\u62E9\u5E94\u7528\u5206\u7C7B" }],
                  children: /* @__PURE__ */ jsx(
                    Select,
                    {
                      placeholder: "\u8BF7\u9009\u62E9\u5E94\u7528\u5206\u7C7B",
                      loading: categoriesLoading,
                      showSearch: true,
                      filterOption: (input, option) => option?.children?.toLowerCase().includes(input.toLowerCase()),
                      children: categoriesList.map((category) => /* @__PURE__ */ jsx(Option, { value: category.name, children: category.name }, category.id))
                    }
                  )
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "tags",
                  label: "\u5E94\u7528\u6807\u7B7E",
                  rules: [{ required: true, message: "\u8BF7\u9009\u62E9\u81F3\u5C11\u4E00\u4E2A\u6807\u7B7E" }],
                  children: /* @__PURE__ */ jsx(
                    Select,
                    {
                      mode: "multiple",
                      placeholder: "\u8BF7\u9009\u62E9\u5E94\u7528\u6807\u7B7E",
                      maxTagCount: 5,
                      loading: tagsLoading,
                      showSearch: true,
                      filterOption: (input, option) => option?.children?.toLowerCase().includes(input.toLowerCase()),
                      children: tagsList.map((tag) => /* @__PURE__ */ jsx(Option, { value: tag.name, children: tag.name }, tag.id))
                    }
                  )
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "version",
                  label: "\u7248\u672C\u53F7",
                  rules: [
                    { required: true, message: "\u8BF7\u8F93\u5165\u7248\u672C\u53F7" },
                    { pattern: /^\d+\.\d+\.\d+$/, message: "\u7248\u672C\u53F7\u683C\u5F0F\u5E94\u4E3A X.Y.Z" }
                  ],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u7248\u672C\u53F7\uFF0C\u4F8B\u5982\uFF1A1.0.0" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "size",
                  label: "\u5E94\u7528\u5927\u5C0F",
                  rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u5E94\u7528\u5927\u5C0F" }],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u5E94\u7528\u5927\u5C0F\uFF0C\u4F8B\u5982\uFF1A10MB" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "min_open_harmony_os_ver",
                  label: "\u6700\u4F4EHarmonyOS\u7248\u672C",
                  rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u6700\u4F4EHarmonyOS\u7248\u672C" }],
                  children: /* @__PURE__ */ jsxs(Select, { placeholder: "\u8BF7\u9009\u62E9\u6700\u4F4EHarmonyOS\u7248\u672C", children: [
                    /* @__PURE__ */ jsx(Option, { value: "2.0", children: "HarmonyOS 2.0" }),
                    /* @__PURE__ */ jsx(Option, { value: "3.0", children: "HarmonyOS 3.0" }),
                    /* @__PURE__ */ jsx(Option, { value: "4.0", children: "HarmonyOS 4.0" }),
                    /* @__PURE__ */ jsx(Option, { value: "5.0", children: "HarmonyOS 5.0" })
                  ] })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "price",
                  label: "\u4EF7\u683C",
                  rules: [{ required: true, message: "\u8BF7\u8BBE\u7F6E\u4EF7\u683C" }],
                  children: /* @__PURE__ */ jsxs(
                    Select,
                    {
                      placeholder: "\u8BF7\u9009\u62E9\u4EF7\u683C\u7C7B\u578B",
                      onChange: (value) => {
                        if (value === "free") {
                          form.setFieldsValue({ priceAmount: void 0 });
                        }
                      },
                      children: [
                        /* @__PURE__ */ jsx(Option, { value: "free", children: "\u514D\u8D39" }),
                        /* @__PURE__ */ jsx(Option, { value: "paid", children: "\u4ED8\u8D39" })
                      ]
                    }
                  )
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  noStyle: true,
                  shouldUpdate: (prevValues, currentValues) => prevValues.price !== currentValues.price,
                  children: ({ getFieldValue }) => getFieldValue("price") === "paid" ? /* @__PURE__ */ jsx(
                    Form.Item,
                    {
                      name: "priceAmount",
                      label: "\u4EF7\u683C\u91D1\u989D",
                      rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u4EF7\u683C\u91D1\u989D" }],
                      children: /* @__PURE__ */ jsx(
                        InputNumber,
                        {
                          min: 0.01,
                          precision: 2,
                          placeholder: "\u8BF7\u8F93\u5165\u4EF7\u683C",
                          addonBefore: "\xA5",
                          style: { width: "100%" }
                        }
                      )
                    }
                  ) : null
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "description",
                  label: "\u5E94\u7528\u63CF\u8FF0",
                  rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u5E94\u7528\u63CF\u8FF0" }],
                  children: /* @__PURE__ */ jsx(TextArea, { rows: 4, placeholder: "\u8BF7\u8F93\u5165\u5E94\u7528\u63CF\u8FF0" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "short_desc",
                  label: "\u7B80\u77ED\u63CF\u8FF0",
                  rules: [
                    { required: true, message: "\u8BF7\u8F93\u5165\u7B80\u77ED\u63CF\u8FF0" },
                    { max: 50, message: "\u7B80\u77ED\u63CF\u8FF0\u4E0D\u80FD\u8D85\u8FC750\u4E2A\u5B57\u7B26" }
                  ],
                  children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u7B80\u77ED\u63CF\u8FF0\uFF0C\u6700\u591A50\u4E2A\u5B57\u7B26" })
                }
              ),
              /* @__PURE__ */ jsx(
                Form.Item,
                {
                  name: "appPackage",
                  label: "\u5E94\u7528\u5B89\u88C5\u5305",
                  rules: [{ required: true, message: "\u8BF7\u4E0A\u4F20\u5E94\u7528\u5B89\u88C5\u5305" }],
                  children: /* @__PURE__ */ jsxs(
                    Upload.Dragger,
                    {
                      name: "file",
                      maxCount: 1,
                      customRequest: customUploadPackage,
                      accept: ".hap,.hsp,.app",
                      onChange: (info) => {
                        if (info.file.status === "done") {
                          message.success(`${info.file.name} \u4E0A\u4F20\u6210\u529F`);
                        } else if (info.file.status === "error") {
                          message.error(`${info.file.name} \u4E0A\u4F20\u5931\u8D25`);
                        }
                      },
                      children: [
                        /* @__PURE__ */ jsx("p", { className: "ant-upload-drag-icon", children: /* @__PURE__ */ jsx(InboxOutlined, {}) }),
                        /* @__PURE__ */ jsx("p", { className: "ant-upload-text", children: "\u70B9\u51FB\u6216\u62D6\u62FD\u6587\u4EF6\u5230\u6B64\u533A\u57DF\u4E0A\u4F20" }),
                        /* @__PURE__ */ jsx("p", { className: "ant-upload-hint", children: "\u652F\u6301 HAP\u3001HSP\u3001APP \u683C\u5F0F\u7684\u5E94\u7528\u5B89\u88C5\u5305" })
                      ]
                    }
                  )
                }
              ),
              /* @__PURE__ */ jsxs(
                Form.Item,
                {
                  name: "screenshots",
                  label: "\u5E94\u7528\u622A\u56FE",
                  rules: [{ required: true, message: "\u8BF7\u4E0A\u4F20\u81F3\u5C11\u4E00\u5F20\u5E94\u7528\u622A\u56FE" }],
                  children: [
                    /* @__PURE__ */ jsx(
                      Upload,
                      {
                        name: "screenshots",
                        listType: "picture-card",
                        customRequest: customUploadScreenshot,
                        accept: "image/*",
                        multiple: true,
                        onRemove: (file) => {
                          const index = screenshotUrls.findIndex((url, idx) => idx === file.uid || url === file.url);
                          if (index > -1) {
                            const newScreenshotUrls = screenshotUrls.filter((_, idx) => idx !== index);
                            setScreenshotUrls(newScreenshotUrls);
                            form.setFieldsValue({ screenshots: newScreenshotUrls });
                          }
                        },
                        onChange: (info) => {
                          if (info.file.status === "done") {
                            message.success(`${info.file.name} \u4E0A\u4F20\u6210\u529F`);
                          } else if (info.file.status === "error") {
                            message.error(`${info.file.name} \u4E0A\u4F20\u5931\u8D25`);
                          }
                        },
                        children: /* @__PURE__ */ jsxs("div", { children: [
                          /* @__PURE__ */ jsx(PlusOutlined, {}),
                          /* @__PURE__ */ jsx("div", { style: { marginTop: 8 }, children: "\u4E0A\u4F20" })
                        ] })
                      }
                    ),
                    /* @__PURE__ */ jsx("div", { children: "\u5EFA\u8BAE\u4E0A\u4F203-5\u5F20\u622A\u56FE\uFF0C\u5C55\u793A\u5E94\u7528\u4E3B\u8981\u529F\u80FD" })
                  ]
                }
              ),
              /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsxs(Space, { children: [
                /* @__PURE__ */ jsx(Button, { type: "primary", htmlType: "submit", children: "\u521B\u5EFA\u5E94\u7528" }),
                /* @__PURE__ */ jsx(
                  Button,
                  {
                    type: "default",
                    onClick: () => {
                      form.validateFields().then((values) => {
                        handleSaveDraft(values);
                      }).catch((errorInfo) => {
                        console.log("\u8868\u5355\u9A8C\u8BC1\u5931\u8D25:", errorInfo);
                        message.warning("\u8BF7\u5B8C\u5584\u5FC5\u586B\u4FE1\u606F\u540E\u518D\u4FDD\u5B58\u8349\u7A3F");
                      });
                    },
                    children: "\u4FDD\u5B58\u81F3\u8349\u7A3F"
                  }
                ),
                /* @__PURE__ */ jsx(Button, { onClick: () => history.back(), children: "\u53D6\u6D88" })
              ] }) })
            ]
          }
        ) })
      ]
    }
  );
};
export default AppCreate;
