"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { useEffect, useState } from "react";
import { SwaggerUIBundle } from "swagger-ui-dist";
import "swagger-ui-dist/swagger-ui.css";
const App = () => {
  const [value, setValue] = useState("openapi");
  useEffect(() => {
    SwaggerUIBundle({
      url: `/umi-plugins_${value}.json`,
      dom_id: "#swagger-ui"
    });
  }, [value]);
  return /* @__PURE__ */ jsxs(
    "div",
    {
      style: {
        padding: 24
      },
      children: [
        /* @__PURE__ */ jsx(
          "select",
          {
            style: {
              position: "fixed",
              right: "16px",
              top: "8px"
            },
            onChange: (e) => setValue(e.target.value),
            children: /* @__PURE__ */ jsx("option", { value: "openapi", children: "openapi" })
          }
        ),
        /* @__PURE__ */ jsx("div", { id: "swagger-ui" })
      ]
    }
  );
};
export default App;
