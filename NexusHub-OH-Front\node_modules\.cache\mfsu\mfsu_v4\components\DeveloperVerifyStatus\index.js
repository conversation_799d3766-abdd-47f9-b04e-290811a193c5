"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { Card, Steps, Tag, Typography, Space, Alert, Descriptions, Button } from "antd";
import { CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import styles from "./index.less";
const { Title, Paragraph, Text } = Typography;
const { Step } = Steps;
const DeveloperVerifyStatus = ({
  data,
  onResubmit,
  showResubmitButton = true
}) => {
  if (!data || !data.verify_status || data.verify_status === "") {
    return /* @__PURE__ */ jsx(Card, { className: styles.statusCard, children: /* @__PURE__ */ jsxs("div", { className: styles.emptyStatus, children: [
      /* @__PURE__ */ jsx(ExclamationCircleOutlined, { className: styles.emptyIcon }),
      /* @__PURE__ */ jsx(Title, { level: 4, children: "\u5C1A\u672A\u63D0\u4EA4\u8BA4\u8BC1\u7533\u8BF7" }),
      /* @__PURE__ */ jsx(Paragraph, { type: "secondary", children: "\u60A8\u8FD8\u6CA1\u6709\u63D0\u4EA4\u5F00\u53D1\u8005\u8BA4\u8BC1\u7533\u8BF7\uFF0C\u8BF7\u5148\u9009\u62E9\u5F00\u53D1\u8005\u7C7B\u578B\u5E76\u586B\u5199\u8BA4\u8BC1\u4FE1\u606F\u3002" })
    ] }) });
  }
  const { verify_status, verify_reason, submitted_at, verified_at } = data;
  const getCurrentStep = () => {
    switch (verify_status) {
      case "pending":
        return 1;
      case "approved":
        return 2;
      case "rejected":
        return 1;
      default:
        return 0;
    }
  };
  const getStepStatus = () => {
    switch (verify_status) {
      case "pending":
        return "process";
      case "approved":
        return "finish";
      case "rejected":
        return "error";
      default:
        return "wait";
    }
  };
  const renderStatusIcon = () => {
    switch (verify_status) {
      case "pending":
        return /* @__PURE__ */ jsx(ClockCircleOutlined, { className: styles.pendingIcon });
      case "approved":
        return /* @__PURE__ */ jsx(CheckCircleOutlined, { className: styles.approvedIcon });
      case "rejected":
        return /* @__PURE__ */ jsx(CloseCircleOutlined, { className: styles.rejectedIcon });
      default:
        return /* @__PURE__ */ jsx(ExclamationCircleOutlined, { className: styles.defaultIcon });
    }
  };
  const renderStatusTag = () => {
    const statusMap = {
      pending: { color: "orange", text: "\u5BA1\u6838\u4E2D" },
      approved: { color: "green", text: "\u5DF2\u901A\u8FC7" },
      rejected: { color: "red", text: "\u5DF2\u62D2\u7EDD" }
    };
    const config = statusMap[verify_status];
    return config ? /* @__PURE__ */ jsx(Tag, { color: config.color, children: config.text }) : null;
  };
  const renderStatusDescription = () => {
    switch (verify_status) {
      case "pending":
        return /* @__PURE__ */ jsx(
          Alert,
          {
            message: "\u8BA4\u8BC1\u7533\u8BF7\u5BA1\u6838\u4E2D",
            description: "\u60A8\u7684\u5F00\u53D1\u8005\u8BA4\u8BC1\u7533\u8BF7\u5DF2\u63D0\u4EA4\uFF0C\u6211\u4EEC\u5C06\u57281-3\u4E2A\u5DE5\u4F5C\u65E5\u5185\u5B8C\u6210\u5BA1\u6838\uFF0C\u8BF7\u8010\u5FC3\u7B49\u5F85\u3002",
            type: "info",
            showIcon: true,
            className: styles.statusAlert
          }
        );
      case "approved":
        return /* @__PURE__ */ jsx(
          Alert,
          {
            message: "\u8BA4\u8BC1\u7533\u8BF7\u5DF2\u901A\u8FC7",
            description: "\u606D\u559C\uFF01\u60A8\u7684\u5F00\u53D1\u8005\u8BA4\u8BC1\u7533\u8BF7\u5DF2\u901A\u8FC7\u5BA1\u6838\uFF0C\u73B0\u5728\u60A8\u53EF\u4EE5\u4E0A\u4F20\u548C\u7BA1\u7406\u5E94\u7528\u4E86\u3002",
            type: "success",
            showIcon: true,
            className: styles.statusAlert
          }
        );
      case "rejected":
        return /* @__PURE__ */ jsx(
          Alert,
          {
            message: "\u8BA4\u8BC1\u7533\u8BF7\u88AB\u62D2\u7EDD",
            description: `\u5F88\u62B1\u6B49\uFF0C\u60A8\u7684\u5F00\u53D1\u8005\u8BA4\u8BC1\u7533\u8BF7\u672A\u901A\u8FC7\u5BA1\u6838\u3002${verify_reason ? `\u62D2\u7EDD\u539F\u56E0\uFF1A${verify_reason}` : ""}\u60A8\u53EF\u4EE5\u4FEE\u6539\u4FE1\u606F\u540E\u91CD\u65B0\u63D0\u4EA4\u7533\u8BF7\u3002`,
            type: "error",
            showIcon: true,
            className: styles.statusAlert,
            action: showResubmitButton && onResubmit ? /* @__PURE__ */ jsx(Button, { size: "small", type: "primary", onClick: onResubmit, children: "\u91CD\u65B0\u7533\u8BF7" }) : null
          }
        );
      default:
        return null;
    }
  };
  return /* @__PURE__ */ jsxs(Card, { className: styles.statusCard, children: [
    /* @__PURE__ */ jsx("div", { className: styles.statusHeader, children: /* @__PURE__ */ jsxs(Space, { align: "center", children: [
      renderStatusIcon(),
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsxs(Title, { level: 4, className: styles.statusTitle, children: [
          "\u5F00\u53D1\u8005\u8BA4\u8BC1\u72B6\u6001 ",
          renderStatusTag()
        ] }),
        /* @__PURE__ */ jsxs(Text, { type: "secondary", children: [
          submitted_at && `\u7533\u8BF7\u65F6\u95F4\uFF1A${dayjs(submitted_at).format("YYYY-MM-DD HH:mm")}`,
          verified_at && ` | \u5BA1\u6838\u65F6\u95F4\uFF1A${dayjs(verified_at).format("YYYY-MM-DD HH:mm")}`
        ] })
      ] })
    ] }) }),
    /* @__PURE__ */ jsxs("div", { className: styles.statusContent, children: [
      renderStatusDescription(),
      /* @__PURE__ */ jsx("div", { className: styles.stepsContainer, children: /* @__PURE__ */ jsxs(Steps, { current: getCurrentStep(), status: getStepStatus(), children: [
        /* @__PURE__ */ jsx(Step, { title: "\u63D0\u4EA4\u7533\u8BF7", description: "\u586B\u5199\u8BA4\u8BC1\u4FE1\u606F\u5E76\u63D0\u4EA4" }),
        /* @__PURE__ */ jsx(Step, { title: "\u5BA1\u6838\u4E2D", description: "\u7BA1\u7406\u5458\u5BA1\u6838\u8BA4\u8BC1\u6750\u6599" }),
        /* @__PURE__ */ jsx(Step, { title: "\u5BA1\u6838\u5B8C\u6210", description: "\u83B7\u5F97\u5F00\u53D1\u8005\u6743\u9650" })
      ] }) }),
      data.developer_name && /* @__PURE__ */ jsxs("div", { className: styles.infoContainer, children: [
        /* @__PURE__ */ jsx(Title, { level: 5, children: "\u7533\u8BF7\u4FE1\u606F" }),
        /* @__PURE__ */ jsxs(Descriptions, { column: 2, size: "small", children: [
          /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u5F00\u53D1\u8005\u59D3\u540D", children: data.developer_name }),
          /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u516C\u53F8\u540D\u79F0", children: data.company_name || "-" }),
          /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u8054\u7CFB\u90AE\u7BB1", children: data.contact_email }),
          /* @__PURE__ */ jsx(Descriptions.Item, { label: "\u8054\u7CFB\u7535\u8BDD", children: data.contact_phone })
        ] })
      ] })
    ] })
  ] });
};
export default DeveloperVerifyStatus;
