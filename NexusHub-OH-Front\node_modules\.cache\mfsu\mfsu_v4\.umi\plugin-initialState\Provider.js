"use strict";
import { jsx } from "react/jsx-runtime";
import React from "react";
import { useModel } from "@@/plugin-model";
function Loading() {
  return /* @__PURE__ */ jsx("div", {});
}
export default function InitialStateProvider(props) {
  const appLoaded = React.useRef(false);
  const { loading = false } = useModel("@@initialState") || {};
  React.useEffect(() => {
    if (!loading) {
      appLoaded.current = true;
    }
  }, [loading]);
  if (loading && !appLoaded.current && typeof window !== "undefined") {
    return /* @__PURE__ */ jsx(Loading, {});
  }
  return props.children;
}
