"use strict";
import { jsx, jsxs } from "react/jsx-runtime";
import { useState, useEffect } from "react";
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  message,
  Popconfirm,
  Tooltip,
  Tag as AntdTag,
  Grid
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined
} from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-components";
import {
  getTags,
  postTags as createTag,
  putTagsId as updateTag,
  deleteTagsId as deleteTag
} from "@/services/ant-design-pro/biaoqianguanli";
const { useBreakpoint } = Grid;
const { Option } = Select;
const TagsManagement = () => {
  console.log("\u{1F50D} [TagsManagement] \u7EC4\u4EF6\u521D\u59CB\u5316");
  const screens = useBreakpoint();
  const [searchParams, setSearchParams] = useState({
    include_inactive: true
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTag, setEditingTag] = useState(null);
  const [form] = Form.useForm();
  const isMobile = !screens.md;
  const isTablet = screens.md && !screens.lg;
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const fetchTags = async () => {
    setLoading(true);
    try {
      const response = await getTags(searchParams);
      if (Array.isArray(response)) {
        setData(response);
      } else if (response && response.data) {
        setData(response.data);
      } else {
        setData([]);
      }
    } catch (error) {
      console.error("\u6807\u7B7E\u6570\u636E\u8BF7\u6C42\u5931\u8D25:", error);
      setData([]);
    } finally {
      setLoading(false);
    }
  };
  const refresh = fetchTags;
  useEffect(() => {
    fetchTags();
  }, [searchParams]);
  const handleSearch = (values) => {
    setSearchParams({
      ...searchParams,
      ...values
    });
  };
  const showAddModal = () => {
    setEditingTag(null);
    form.resetFields();
    setModalVisible(true);
  };
  const showEditModal = (record) => {
    setEditingTag(record);
    form.setFieldsValue({
      name: record.name,
      color: record.color,
      description: record.description,
      is_active: record.is_active
    });
    setModalVisible(true);
  };
  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      if (editingTag) {
        await updateTag(
          { id: editingTag.id },
          values
        );
        message.success(`\u6807\u7B7E "${values.name}" \u5DF2\u66F4\u65B0`);
      } else {
        try {
          await createTag(values);
          message.success(`\u6807\u7B7E "${values.name}" \u5DF2\u521B\u5EFA`);
          setModalVisible(false);
          refresh();
        } catch (error) {
          if (error.response && error.response.status === 400) {
            const errorData = error.response.data;
            if (errorData && errorData.message) {
              message.error(errorData.message);
            } else if (errorData && errorData.code === 400) {
              message.error("\u6807\u7B7E\u540D\u79F0\u5DF2\u5B58\u5728");
            } else {
              message.error("\u521B\u5EFA\u6807\u7B7E\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u8F93\u5165");
            }
          } else {
            message.error("\u521B\u5EFA\u6807\u7B7E\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
          }
          return;
        }
      }
      setModalVisible(false);
      refresh();
    } catch (error) {
      console.error("\u8868\u5355\u9A8C\u8BC1\u6216\u63D0\u4EA4\u9519\u8BEF:", error);
    }
  };
  const handleDelete = async (id, name) => {
    try {
      await deleteTag({ id });
      message.success(`\u6807\u7B7E "${name}" \u5DF2\u5220\u9664`);
      refresh();
    } catch (error) {
      message.error(`\u5220\u9664\u5931\u8D25: ${error}`);
    }
  };
  const handleBatchDelete = async () => {
    try {
      for (const id of selectedRowKeys) {
        await deleteTag({ id });
      }
      message.success(`\u5DF2\u5220\u9664 ${selectedRowKeys.length} \u4E2A\u6807\u7B7E`);
      setSelectedRowKeys([]);
      refresh();
    } catch (error) {
      message.error(`\u6279\u91CF\u5220\u9664\u5931\u8D25: ${error}`);
    }
  };
  const colorOptions = [
    { label: "\u7EA2\u8272", value: "#f5222d" },
    { label: "\u6A59\u8272", value: "#fa8c16" },
    { label: "\u9EC4\u8272", value: "#fadb14" },
    { label: "\u7EFF\u8272", value: "#52c41a" },
    { label: "\u9752\u8272", value: "#13c2c2" },
    { label: "\u84DD\u8272", value: "#1890ff" },
    { label: "\u7D2B\u8272", value: "#722ed1" },
    { label: "\u7C89\u8272", value: "#eb2f96" }
  ];
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80,
      sorter: (a, b) => a.id - b.id,
      hidden: isMobile
      // 移动端隐藏ID列
    },
    {
      title: "\u6807\u7B7E\u4FE1\u606F",
      key: "tagInfo",
      render: (_, record) => /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx("div", { style: { fontWeight: "bold", marginBottom: 4 }, children: record.name }),
        /* @__PURE__ */ jsxs(Space, { size: "small", children: [
          /* @__PURE__ */ jsx(
            "div",
            {
              style: {
                width: 16,
                height: 16,
                backgroundColor: record.color,
                borderRadius: "50%",
                border: "1px solid #d9d9d9"
              }
            }
          ),
          /* @__PURE__ */ jsx(AntdTag, { color: record.color, size: "small", children: record.name }),
          /* @__PURE__ */ jsx(AntdTag, { color: record.is_active ? "green" : "red", size: "small", children: record.is_active ? "\u542F\u7528" : "\u7981\u7528" })
        ] }),
        !isMobile && record.description && /* @__PURE__ */ jsx("div", { style: { fontSize: 12, color: "#666", marginTop: 4 }, children: record.description })
      ] })
    },
    // 桌面端显示的详细列
    ...!isMobile ? [
      {
        title: "\u989C\u8272",
        dataIndex: "color",
        key: "color",
        width: 120,
        render: (color, record) => /* @__PURE__ */ jsxs(Space, { children: [
          /* @__PURE__ */ jsx(
            "div",
            {
              style: {
                width: 20,
                height: 20,
                backgroundColor: color,
                borderRadius: "50%",
                border: "1px solid #d9d9d9"
              }
            }
          ),
          /* @__PURE__ */ jsx(AntdTag, { color, children: record.name })
        ] })
      },
      {
        title: "\u63CF\u8FF0",
        dataIndex: "description",
        key: "description",
        ellipsis: {
          showTitle: false
        },
        render: (description) => /* @__PURE__ */ jsx(Tooltip, { placement: "topLeft", title: description, children: description || "-" })
      },
      {
        title: "\u72B6\u6001",
        dataIndex: "is_active",
        key: "is_active",
        width: 100,
        render: (isActive) => /* @__PURE__ */ jsx(AntdTag, { color: isActive ? "green" : "red", children: isActive ? "\u542F\u7528" : "\u7981\u7528" })
      },
      {
        title: "\u521B\u5EFA\u65F6\u95F4",
        dataIndex: "created_at",
        key: "created_at",
        width: 180,
        sorter: (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
        render: (date) => new Date(date).toLocaleString()
      },
      {
        title: "\u66F4\u65B0\u65F6\u95F4",
        dataIndex: "updated_at",
        key: "updated_at",
        width: 180,
        sorter: (a, b) => new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime(),
        render: (date) => new Date(date).toLocaleString()
      }
    ] : [],
    {
      title: "\u64CD\u4F5C",
      key: "action",
      width: isMobile ? 80 : 150,
      fixed: isMobile ? "right" : void 0,
      render: (_, record) => /* @__PURE__ */ jsxs(Space, { size: "small", direction: isMobile ? "vertical" : "horizontal", children: [
        /* @__PURE__ */ jsx(Tooltip, { title: "\u7F16\u8F91", children: /* @__PURE__ */ jsx(
          Button,
          {
            type: "link",
            icon: /* @__PURE__ */ jsx(EditOutlined, {}),
            onClick: () => showEditModal(record),
            size: "small",
            children: isMobile ? "" : "\u7F16\u8F91"
          }
        ) }),
        /* @__PURE__ */ jsx(Tooltip, { title: "\u5220\u9664", children: /* @__PURE__ */ jsx(
          Popconfirm,
          {
            title: "\u786E\u5B9A\u8981\u5220\u9664\u8FD9\u4E2A\u6807\u7B7E\u5417\uFF1F",
            onConfirm: () => handleDelete(record.id, record.name),
            okText: "\u786E\u5B9A",
            cancelText: "\u53D6\u6D88",
            children: /* @__PURE__ */ jsx(
              Button,
              {
                type: "link",
                icon: /* @__PURE__ */ jsx(DeleteOutlined, {}),
                danger: true,
                size: "small",
                children: isMobile ? "" : "\u5220\u9664"
              }
            )
          }
        ) })
      ] })
    }
  ];
  const dataLength = data?.length || 0;
  const hasData = dataLength > 0;
  return /* @__PURE__ */ jsxs(
    PageContainer,
    {
      header: {
        title: "\u6807\u7B7E\u7BA1\u7406",
        subTitle: "\u7BA1\u7406\u7CFB\u7EDF\u4E2D\u7684\u6240\u6709\u6807\u7B7E"
      },
      children: [
        /* @__PURE__ */ jsxs(Card, { children: [
          /* @__PURE__ */ jsxs(
            Form,
            {
              layout: isMobile ? "vertical" : "inline",
              onFinish: handleSearch,
              style: { marginBottom: 16 },
              children: [
                /* @__PURE__ */ jsx(Form.Item, { name: "keyword", label: "\u5173\u952E\u8BCD", style: { marginBottom: isMobile ? 12 : 16 }, children: /* @__PURE__ */ jsx(
                  Input,
                  {
                    placeholder: "\u641C\u7D22\u6807\u7B7E\u540D\u79F0",
                    prefix: /* @__PURE__ */ jsx(SearchOutlined, {}),
                    style: { width: isMobile ? "100%" : 200 },
                    allowClear: true
                  }
                ) }),
                /* @__PURE__ */ jsx(Form.Item, { name: "include_inactive", label: "\u663E\u793A\u7981\u7528\u6807\u7B7E", style: { marginBottom: isMobile ? 12 : 16 }, children: /* @__PURE__ */ jsxs(
                  Select,
                  {
                    placeholder: "\u662F\u5426\u663E\u793A\u7981\u7528\u6807\u7B7E",
                    style: { width: isMobile ? "100%" : 150 },
                    defaultValue: true,
                    allowClear: true,
                    children: [
                      /* @__PURE__ */ jsx(Option, { value: true, children: "\u663E\u793A\u6240\u6709" }),
                      /* @__PURE__ */ jsx(Option, { value: false, children: "\u4EC5\u663E\u793A\u542F\u7528" })
                    ]
                  }
                ) }),
                /* @__PURE__ */ jsx(Form.Item, { style: { marginBottom: isMobile ? 12 : 16 }, children: /* @__PURE__ */ jsxs(Space, { direction: isMobile ? "horizontal" : "horizontal", style: { width: isMobile ? "100%" : "auto" }, children: [
                  /* @__PURE__ */ jsx(
                    Button,
                    {
                      type: "primary",
                      htmlType: "submit",
                      icon: /* @__PURE__ */ jsx(SearchOutlined, {}),
                      style: { flex: isMobile ? 1 : "none" },
                      children: "\u641C\u7D22"
                    }
                  ),
                  /* @__PURE__ */ jsx(
                    Button,
                    {
                      onClick: () => setSearchParams({ include_inactive: true }),
                      style: { flex: isMobile ? 1 : "none" },
                      children: "\u91CD\u7F6E"
                    }
                  )
                ] }) })
              ]
            }
          ),
          /* @__PURE__ */ jsx("div", { style: { marginBottom: 16 }, children: /* @__PURE__ */ jsxs(
            Space,
            {
              direction: isMobile ? "vertical" : "horizontal",
              style: { width: isMobile ? "100%" : "auto" },
              size: isMobile ? 8 : "small",
              children: [
                /* @__PURE__ */ jsx(
                  Button,
                  {
                    type: "primary",
                    icon: /* @__PURE__ */ jsx(PlusOutlined, {}),
                    onClick: showAddModal,
                    style: { width: isMobile ? "100%" : "auto" },
                    children: "\u65B0\u5EFA\u6807\u7B7E"
                  }
                ),
                /* @__PURE__ */ jsx(
                  Popconfirm,
                  {
                    title: "\u786E\u5B9A\u8981\u5220\u9664\u9009\u4E2D\u7684\u6807\u7B7E\u5417\uFF1F",
                    onConfirm: handleBatchDelete,
                    okText: "\u786E\u5B9A",
                    cancelText: "\u53D6\u6D88",
                    disabled: selectedRowKeys.length === 0,
                    children: /* @__PURE__ */ jsxs(
                      Button,
                      {
                        danger: true,
                        disabled: selectedRowKeys.length === 0,
                        style: { width: isMobile ? "100%" : "auto" },
                        children: [
                          "\u6279\u91CF\u5220\u9664 (",
                          selectedRowKeys.length,
                          ")"
                        ]
                      }
                    )
                  }
                ),
                /* @__PURE__ */ jsx(
                  Button,
                  {
                    icon: /* @__PURE__ */ jsx(ReloadOutlined, {}),
                    onClick: refresh,
                    style: { width: isMobile ? "100%" : "auto" },
                    children: "\u5237\u65B0"
                  }
                )
              ]
            }
          ) }),
          /* @__PURE__ */ jsx(
            Table,
            {
              rowSelection: {
                selectedRowKeys,
                onChange: setSelectedRowKeys
              },
              columns,
              dataSource: data || [],
              rowKey: "id",
              loading,
              scroll: {
                x: isMobile ? 800 : void 0,
                y: isMobile ? 400 : void 0
              },
              size: isMobile ? "small" : "middle",
              pagination: {
                current: searchParams.page,
                pageSize: searchParams.pageSize,
                total: data?.length || 0,
                showSizeChanger: !isMobile,
                showQuickJumper: !isMobile,
                simple: isMobile,
                showTotal: !isMobile ? (total, range) => `\u7B2C ${range[0]}-${range[1]} \u6761/\u603B\u5171 ${total} \u6761` : void 0,
                onChange: (page, pageSize) => {
                  setSearchParams({
                    ...searchParams,
                    page,
                    pageSize: pageSize || 10
                  });
                },
                position: [isMobile ? "bottomCenter" : "bottomRight"]
              }
            }
          )
        ] }),
        /* @__PURE__ */ jsx(
          Modal,
          {
            title: editingTag ? "\u7F16\u8F91\u6807\u7B7E" : "\u65B0\u5EFA\u6807\u7B7E",
            open: modalVisible,
            onOk: handleModalOk,
            onCancel: () => {
              setModalVisible(false);
              setEditingTag(null);
              form.resetFields();
            },
            width: isMobile ? "95%" : 600,
            centered: isMobile,
            destroyOnHidden: true,
            children: /* @__PURE__ */ jsxs(
              Form,
              {
                form,
                layout: "vertical",
                children: [
                  /* @__PURE__ */ jsx(
                    Form.Item,
                    {
                      name: "name",
                      label: "\u6807\u7B7E\u540D\u79F0",
                      rules: [{ required: true, message: "\u8BF7\u8F93\u5165\u6807\u7B7E\u540D\u79F0" }],
                      children: /* @__PURE__ */ jsx(Input, { placeholder: "\u8BF7\u8F93\u5165\u6807\u7B7E\u540D\u79F0" })
                    }
                  ),
                  /* @__PURE__ */ jsx(
                    Form.Item,
                    {
                      name: "color",
                      label: "\u6807\u7B7E\u989C\u8272",
                      children: /* @__PURE__ */ jsx(Select, { placeholder: "\u8BF7\u9009\u62E9\u6807\u7B7E\u989C\u8272", children: colorOptions.map((option) => /* @__PURE__ */ jsx(Option, { value: option.value, children: /* @__PURE__ */ jsxs("div", { style: { display: "flex", alignItems: "center" }, children: [
                        /* @__PURE__ */ jsx("div", { style: {
                          width: 16,
                          height: 16,
                          backgroundColor: option.value,
                          marginRight: 8,
                          borderRadius: "50%"
                        } }),
                        option.label
                      ] }) }, option.value)) })
                    }
                  ),
                  /* @__PURE__ */ jsx(
                    Form.Item,
                    {
                      name: "description",
                      label: "\u6807\u7B7E\u63CF\u8FF0",
                      children: /* @__PURE__ */ jsx(Input.TextArea, { rows: 3, placeholder: "\u8BF7\u8F93\u5165\u6807\u7B7E\u63CF\u8FF0" })
                    }
                  ),
                  /* @__PURE__ */ jsx(
                    Form.Item,
                    {
                      name: "is_active",
                      label: "\u72B6\u6001",
                      valuePropName: "checked",
                      initialValue: true,
                      children: /* @__PURE__ */ jsxs(Select, { defaultValue: true, children: [
                        /* @__PURE__ */ jsx(Option, { value: true, children: "\u542F\u7528" }),
                        /* @__PURE__ */ jsx(Option, { value: false, children: "\u7981\u7528" })
                      ] })
                    }
                  )
                ]
              }
            )
          }
        )
      ]
    }
  );
};
export default TagsManagement;
