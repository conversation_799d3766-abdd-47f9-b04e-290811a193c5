<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地理位置API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .api-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .api-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        select {
            padding: 5px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .input-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>地理位置API测试页面</h1>
    <p>测试新构建的地理位置API接口</p>

    <!-- 国家列表 -->
    <div class="api-section">
        <h3>1. 获取国家列表</h3>
        <button onclick="testCountries()">测试 GET /api/v1/geographic/country</button>
        <div id="countries-result" class="result"></div>
    </div>

    <!-- 省份列表 -->
    <div class="api-section">
        <h3>2. 获取省份列表</h3>
        <button onclick="testProvinces()">测试 GET /api/v1/geographic/province</button>
        <div id="provinces-result" class="result"></div>
    </div>

    <!-- 城市列表 -->
    <div class="api-section">
        <h3>3. 获取城市列表</h3>
        <div class="input-group">
            <label>省份ID:</label>
            <select id="province-select">
                <option value="11">北京 (11)</option>
                <option value="12">天津 (12)</option>
                <option value="13">河北 (13)</option>
            </select>
            <button onclick="testCities()">测试 GET /api/v1/geographic/city/{province}</button>
        </div>
        <div id="cities-result" class="result"></div>
    </div>

    <!-- 区/镇列表 -->
    <div class="api-section">
        <h3>4. 获取区/镇列表</h3>
        <div class="input-group">
            <label>城市ID:</label>
            <select id="city-select">
                <option value="1101">北京市 (1101)</option>
            </select>
            <button onclick="testDistricts()">测试 GET /api/v1/geographic/district/{city}</button>
        </div>
        <div id="districts-result" class="result"></div>
    </div>

    <!-- 街道列表 -->
    <div class="api-section">
        <h3>5. 获取街道列表</h3>
        <div class="input-group">
            <label>区/镇ID:</label>
            <select id="district-select">
                <option value="110101">东城区 (110101)</option>
                <option value="110102">西城区 (110102)</option>
                <option value="110105">朝阳区 (110105)</option>
            </select>
            <button onclick="testStreets()">测试 GET /api/v1/geographic/street/{district}</button>
        </div>
        <div id="streets-result" class="result"></div>
    </div>

    <!-- 按级别获取 -->
    <div class="api-section">
        <h3>6. 按级别获取地理位置数据</h3>
        <div class="input-group">
            <label>级别:</label>
            <select id="level-select">
                <option value="0">省份 (0)</option>
                <option value="1">城市 (1)</option>
                <option value="2">区/镇 (2)</option>
                <option value="3">街道 (3)</option>
            </select>
        </div>
        <div class="input-group">
            <label>父级ID:</label>
            <input type="number" id="parent-id-input" placeholder="可选，获取省份时不需要">
        </div>
        <button onclick="testByLevel()">测试 GET /api/v1/geographic/level</button>
        <div id="level-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api/v1/geographic';

        async function makeRequest(url, resultElementId) {
            const resultElement = document.getElementById(resultElementId);
            resultElement.textContent = '请求中...';
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.textContent = `状态: ${response.status}\n响应数据:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultElement.textContent = `错误: ${response.status}\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultElement.textContent = `网络错误: ${error.message}`;
            }
        }

        function testCountries() {
            makeRequest(`${API_BASE}/country`, 'countries-result');
        }

        function testProvinces() {
            makeRequest(`${API_BASE}/province`, 'provinces-result');
        }

        function testCities() {
            const provinceId = document.getElementById('province-select').value;
            makeRequest(`${API_BASE}/city/${provinceId}`, 'cities-result');
        }

        function testDistricts() {
            const cityId = document.getElementById('city-select').value;
            makeRequest(`${API_BASE}/district/${cityId}`, 'districts-result');
        }

        function testStreets() {
            const districtId = document.getElementById('district-select').value;
            makeRequest(`${API_BASE}/street/${districtId}`, 'streets-result');
        }

        function testByLevel() {
            const level = document.getElementById('level-select').value;
            const parentId = document.getElementById('parent-id-input').value;
            
            let url = `${API_BASE}/level?level=${level}`;
            if (parentId) {
                url += `&parent_id=${parentId}`;
            }
            
            makeRequest(url, 'level-result');
        }

        // 页面加载时自动测试国家接口
        window.onload = function() {
            testCountries();
        };
    </script>
</body>
</html>