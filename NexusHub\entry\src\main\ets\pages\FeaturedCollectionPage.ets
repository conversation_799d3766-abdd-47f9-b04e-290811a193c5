/**
 * 精选集页面
 * 展示精选集列表和详情
 */
import { FeaturedCollectionModel, FeaturedCollectionListResponse } from '../models/FeaturedCollection';
import { Constants } from '../utils/Constants';
import { DeviceUtils } from '../utils/DeviceUtils';
import { ApiService } from '../services/ApiService';
import { LoadingView, LoadMoreView } from '../components/LoadingView';
import { router } from '@kit.ArkUI';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { promptAction } from '@kit.ArkUI';

/**
 * 精选集页面组件
 */
@Entry
@Component
struct FeaturedCollectionPage {
  @State collections: FeaturedCollectionModel[] = [];
  @State isLoading: boolean = false;
  @State isLoadingMore: boolean = false;
  @State hasMore: boolean = true;
  @State currentPage: number = 1;
  @State pageSize: number = 20;
  @State errorMessage: string = '';
  @State showError: boolean = false;
  
  private apiService: ApiService = ApiService.getInstance();
  private deviceUtils: DeviceUtils = DeviceUtils.getInstance();
  
  /**
   * 页面即将出现时的回调
   */
  aboutToAppear() {
    hilog.info(0x0000, 'FeaturedCollectionPage', 'FeaturedCollectionPage aboutToAppear');
    this.testApiDirectly();
    this.loadFeaturedCollections();
  }
  
  /**
   * 直接测试API调用
   */
  private async testApiDirectly() {
    try {
      hilog.info(0x0000, 'FeaturedCollectionPage', '=== 开始直接测试精选集API ===');
      hilog.info(0x0000, 'FeaturedCollectionPage', 'API Base URL: %{public}s', Constants.API_BASE_URL);
      
      // 测试基础连接
      const testUrl = `${Constants.BASE_URL}/api/v1/public/featured-collections?page=1&page_size=20&status=active`;
      hilog.info(0x0000, 'FeaturedCollectionPage', '完整测试URL: %{public}s', testUrl);
      
      // 直接调用API
      const response = await this.apiService.getFeaturedCollections(1, 20, 'active');
      hilog.info(0x0000, 'FeaturedCollectionPage', '=== API测试结果 ===');
      hilog.info(0x0000, 'FeaturedCollectionPage', 'Response: %{public}s', JSON.stringify(response, null, 2));
      
    } catch (error) {
      hilog.error(0x0000, 'FeaturedCollectionPage', '=== API测试失败 ===');
      hilog.error(0x0000, 'FeaturedCollectionPage', 'Error: %{public}s', JSON.stringify(error));
      hilog.error(0x0000, 'FeaturedCollectionPage', 'Error message: %{public}s', error instanceof Error ? error.message : 'Unknown error');
    }
  }
  
  /**
   * 加载精选集列表
   */
  private async loadFeaturedCollections() {
    if (this.isLoading) return;
    
    this.isLoading = true;
    this.showError = false;
    this.currentPage = 1;
    this.hasMore = true;
    
    try {
      hilog.info(0x0000, 'FeaturedCollectionPage', '开始加载精选集列表...');
      const response: FeaturedCollectionListResponse = await this.apiService.getFeaturedCollections(
        this.currentPage, 
        this.pageSize, 
        'active'
      );
      
      hilog.info(0x0000, 'FeaturedCollectionPage', '精选集API响应: %{public}s', JSON.stringify(response));
      
      if (response && response.code === 200 && response.data) {
        this.collections = response.data.list || [];
        hilog.info(0x0000, 'FeaturedCollectionPage', '加载到 %{public}d 个精选集', this.collections.length);
        this.hasMore = response.data.pagination ?
          response.data.pagination.page < response.data.pagination.total_pages : false;
      } else {
        hilog.error(0x0000, 'FeaturedCollectionPage', '精选集API响应异常: %{public}s', JSON.stringify(response));
        this.showError = true;
        this.errorMessage = response?.message || '加载失败';
      }
    } catch (error) {
      hilog.error(0x0000, 'FeaturedCollectionPage', '加载精选集失败: %{public}s', JSON.stringify(error));
      this.showError = true;
      this.errorMessage = '网络连接失败，请检查网络设置';
    } finally {
      this.isLoading = false;
    }
  }
  
  /**
   * 加载更多精选集
   */
  private async loadMoreCollections() {
    if (this.isLoadingMore || !this.hasMore) return;
    
    this.isLoadingMore = true;
    
    try {
      const nextPage = this.currentPage + 1;
      const response: FeaturedCollectionListResponse = await this.apiService.getFeaturedCollections(
        nextPage, 
        this.pageSize, 
        'active'
      );
      
      if (response && response.code === 200 && response.data) {
        const newCollections: FeaturedCollectionModel[] = response.data.list || [];
        this.collections = this.collections.concat(newCollections);
        this.currentPage = nextPage;
        this.hasMore = response.data.pagination ?
          response.data.pagination.page < response.data.pagination.total_pages : false;
      } else {
        this.hasMore = false;
        this.getUIContext().getPromptAction().showToast({
          message: '加载失败',
          duration: 2000
        });
      }
    } catch (error) {
      hilog.error(0x0000, 'FeaturedCollectionPage', '加载更多精选集失败: %{public}s', JSON.stringify(error));
      this.hasMore = false;
      this.getUIContext().getPromptAction().showToast({
        message: '网络连接失败',
        duration: 2000
      });
    } finally {
      this.isLoadingMore = false;
    }
  }
  
  /**
   * 处理精选集卡片点击
   */
  private handleCollectionClick(collection: FeaturedCollectionModel) {
    this.getUIContext().getRouter().pushUrl({
      url: 'pages/FeaturedCollectionDetailPage',
      params: {
        collectionId: collection.id,
        collection: collection
      }
    }).catch((error: Error) => {
      hilog.error(0x0000, 'FeaturedCollectionPage', '导航到精选集详情页失败: %{public}s', JSON.stringify(error));
      this.getUIContext().getPromptAction().showToast({
        message: '页面跳转失败',
        duration: 2000
      });
    });
  }
  
  /**
   * 返回上一页
   */
  private goBack() {
    this.getUIContext().getRouter().back();
  }
  
  /**
   * 构建精选集卡片
   */
  @Builder
  private buildCollectionCard(collection: FeaturedCollectionModel) {
    Column() {
      // 封面图片
      Image(collection.cover_image || Constants.PLACEHOLDER_IMAGE)
        .width('100%')
        .height(120)
        .objectFit(ImageFit.Cover)
        .borderRadius({ topLeft: Constants.BORDER_RADIUS.MEDIUM, topRight: Constants.BORDER_RADIUS.MEDIUM })

      // 内容区域
      Column({ space: 8 }) {
        Text(collection.name)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.MEDIUM))
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.COLORS.TEXT_PRIMARY)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })

        Text(collection.description)
          .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
          .fontColor(Constants.COLORS.TEXT_SECONDARY)
          .maxLines(2)
          .textOverflow({ overflow: TextOverflow.Ellipsis })

        Row() {
          Text(`${collection.app_count}个应用`)
            .fontSize(this.deviceUtils.getAdaptiveFontSize(Constants.FONT_SIZE.SMALL))
            .fontColor(Constants.COLORS.TEXT_TERTIARY)
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
      }
      .alignItems(HorizontalAlign.Start)
      .padding(12)
    }
    .width('100%')
    .backgroundColor(Constants.COLORS.WHITE)
    .borderRadius(Constants.BORDER_RADIUS.MEDIUM)
    .shadow({
      radius: 4,
      color: Constants.COLORS.SHADOW,
      offsetX: 0,
      offsetY: 2
    })
    .onClick(() => this.handleCollectionClick(collection))
  }
  
  /**
   * 构建页面UI
   */
  build() {
    Stack() {
      Column() {
        // 顶部标题栏
        Row() {
          Button() {
            Image($r('app.media.ic_back'))
              .width(24)
              .height(24)
              .fillColor(Constants.COLORS.TEXT_PRIMARY)
          }
          .width(40)
          .height(40)
          .backgroundColor(Color.Transparent)
          .onClick(() => this.goBack())
          
          Text('精选集')
            .fontSize(Constants.FONT_SIZE.LARGE)
            .fontColor(Constants.COLORS.TEXT_PRIMARY)
            .fontWeight(FontWeight.Medium)
            .layoutWeight(1)
            .textAlign(TextAlign.Center)
          
          // 占位，保持标题居中
          Row().width(40).height(40)
        }
        .width('100%')
        .height(56)
        .padding({ left: 16, right: 16 })
        .backgroundColor(Constants.COLORS.WHITE)
        
        // 内容区域
        if (this.isLoading) {
          LoadingView()
            .layoutWeight(1)
        } else if (this.showError) {
          Column() {
            Image($r('app.media.ic_error'))
              .width(64)
              .height(64)
              .fillColor(Constants.COLORS.TEXT_SECONDARY)
              .margin({ bottom: 16 })
            
            Text(this.errorMessage)
              .fontSize(Constants.FONT_SIZE.NORMAL)
              .fontColor(Constants.COLORS.TEXT_SECONDARY)
              .textAlign(TextAlign.Center)
              .margin({ bottom: 24 })
            
            Button('重试')
              .fontSize(Constants.FONT_SIZE.NORMAL)
              .fontColor(Constants.COLORS.PRIMARY)
              .backgroundColor(Constants.COLORS.BACKGROUND_LIGHT)
              .borderRadius(8)
              .padding({ left: 24, right: 24, top: 8, bottom: 8 })
              .onClick(() => this.loadFeaturedCollections())
          }
          .layoutWeight(1)
          .justifyContent(FlexAlign.Center)
          .alignItems(HorizontalAlign.Center)
        } else {
          // 精选集列表
          List() {
            ForEach(this.collections, (collection: FeaturedCollectionModel, index: number) => {
              ListItem() {
                this.buildCollectionCard(collection)
                  .margin({ left: 16, right: 16, bottom: 16 })
              }
            }, (collection: FeaturedCollectionModel) => collection.id.toString())
            
            // 加载更多组件
            if (this.hasMore || this.isLoadingMore) {
              ListItem() {
                LoadMoreView({
                  isLoading: this.isLoadingMore,
                  hasMore: this.hasMore,
                  onLoadMore: () => {
                    if (!this.isLoadingMore && this.hasMore) {
                      this.loadMoreCollections();
                    }
                  }
                })
              }
              .padding({ top: 12, bottom: 12 })
            }
          }
          .layoutWeight(1)
          .scrollBar(BarState.Auto)
          .edgeEffect(EdgeEffect.Spring)
          .padding({ top: 16 })
          .onReachEnd(() => {
            if (this.hasMore && !this.isLoadingMore) {
              this.loadMoreCollections();
            }
          })
        }
      }
      .width('100%')
      .height('100%')
      .backgroundColor(Constants.COLORS.BACKGROUND)
    }
    .width('100%')
    .height('100%')
  }
}